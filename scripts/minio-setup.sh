#!/bin/bash

# MinIO Local Development Setup Script
# This script helps manage MinIO container for local S3-compatible storage

set -e

CONTAINER_NAME="kvc-s3"
MINIO_PORT=9000
CONSOLE_PORT=9001
BUCKET_NAME="kvcare-dev"
DATA_DIR="$HOME/minio-data/kv-care/data"
CONFIG_DIR="$HOME/minio-data/kv-care/config"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 MinIO Setup Script${NC}"
echo ""

# Function to check if container exists
container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to start MinIO
start_minio() {
    if container_exists; then
        if container_running; then
            echo -e "${YELLOW}⚠️  MinIO container is already running${NC}"
        else
            echo -e "${GREEN}▶️  Starting existing MinIO container...${NC}"
            docker start $CONTAINER_NAME
        fi
    else
        echo -e "${GREEN}📦 Creating MinIO container...${NC}"
        
        # Create directories if they don't exist
        mkdir -p "$DATA_DIR" "$CONFIG_DIR"
        
        docker run -d --name $CONTAINER_NAME \
            -p $MINIO_PORT:9000 -p $CONSOLE_PORT:9001 \
            -e MINIO_ROOT_USER=minioadmin \
            -e MINIO_ROOT_PASSWORD=minioadmin \
            -v "$DATA_DIR:/data" \
            -v "$CONFIG_DIR:/root/.minio" \
            minio/minio server /data --console-address ":9001"
        
        echo -e "${GREEN}✅ MinIO container created and started${NC}"
        
        # Wait for MinIO to be ready
        echo -e "${YELLOW}⏳ Waiting for MinIO to be ready...${NC}"
        sleep 5
        
        # Create bucket
        echo -e "${GREEN}📁 Creating bucket: $BUCKET_NAME${NC}"
        docker exec $CONTAINER_NAME mc alias set local http://localhost:9000 minioadmin minioadmin
        docker exec $CONTAINER_NAME mc mb local/$BUCKET_NAME 2>/dev/null || echo -e "${YELLOW}Bucket already exists${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}✅ MinIO is running!${NC}"
    echo -e "   API:     http://localhost:$MINIO_PORT"
    echo -e "   Console: http://localhost:$CONSOLE_PORT"
    echo -e "   Login:   minioadmin / minioadmin"
    echo ""
}

# Function to stop MinIO
stop_minio() {
    if container_running; then
        echo -e "${YELLOW}⏹️  Stopping MinIO container...${NC}"
        docker stop $CONTAINER_NAME
        echo -e "${GREEN}✅ MinIO stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  MinIO container is not running${NC}"
    fi
}

# Function to remove MinIO container
remove_minio() {
    if container_exists; then
        if container_running; then
            echo -e "${YELLOW}⏹️  Stopping MinIO container...${NC}"
            docker stop $CONTAINER_NAME
        fi
        echo -e "${RED}🗑️  Removing MinIO container...${NC}"
        docker rm $CONTAINER_NAME
        echo -e "${GREEN}✅ MinIO container removed${NC}"
        echo -e "${YELLOW}⚠️  Data is preserved in: $DATA_DIR${NC}"
    else
        echo -e "${YELLOW}⚠️  MinIO container does not exist${NC}"
    fi
}

# Function to show status
show_status() {
    echo -e "${GREEN}📊 MinIO Status${NC}"
    echo ""
    
    if container_exists; then
        if container_running; then
            echo -e "   Status: ${GREEN}Running ✅${NC}"
            echo -e "   API:    http://localhost:$MINIO_PORT"
            echo -e "   Console: http://localhost:$CONSOLE_PORT"
            echo ""
            echo -e "${GREEN}📁 Buckets:${NC}"
            docker exec $CONTAINER_NAME mc ls local
        else
            echo -e "   Status: ${YELLOW}Stopped ⏹️${NC}"
        fi
    else
        echo -e "   Status: ${RED}Not created ❌${NC}"
    fi
    echo ""
}

# Function to show logs
show_logs() {
    if container_exists; then
        echo -e "${GREEN}📋 MinIO Logs (last 50 lines)${NC}"
        echo ""
        docker logs --tail 50 $CONTAINER_NAME
    else
        echo -e "${RED}❌ MinIO container does not exist${NC}"
    fi
}

# Function to list buckets
list_buckets() {
    if container_running; then
        echo -e "${GREEN}📁 Buckets:${NC}"
        docker exec $CONTAINER_NAME mc ls local
    else
        echo -e "${RED}❌ MinIO is not running${NC}"
    fi
}

# Function to create bucket
create_bucket() {
    if [ -z "$1" ]; then
        echo -e "${RED}❌ Please provide bucket name${NC}"
        echo "   Usage: $0 create-bucket <bucket-name>"
        exit 1
    fi
    
    if container_running; then
        echo -e "${GREEN}📁 Creating bucket: $1${NC}"
        docker exec $CONTAINER_NAME mc mb local/$1
        echo -e "${GREEN}✅ Bucket created${NC}"
    else
        echo -e "${RED}❌ MinIO is not running${NC}"
    fi
}

# Function to open console
open_console() {
    if container_running; then
        echo -e "${GREEN}🌐 Opening MinIO Console...${NC}"
        open http://localhost:$CONSOLE_PORT
    else
        echo -e "${RED}❌ MinIO is not running${NC}"
    fi
}

# Main script
case "${1:-}" in
    start)
        start_minio
        ;;
    stop)
        stop_minio
        ;;
    restart)
        stop_minio
        sleep 2
        start_minio
        ;;
    remove)
        remove_minio
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    buckets)
        list_buckets
        ;;
    create-bucket)
        create_bucket "$2"
        ;;
    console)
        open_console
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|remove|status|logs|buckets|create-bucket|console}"
        echo ""
        echo "Commands:"
        echo "  start          - Start MinIO container"
        echo "  stop           - Stop MinIO container"
        echo "  restart        - Restart MinIO container"
        echo "  remove         - Remove MinIO container (data is preserved)"
        echo "  status         - Show MinIO status"
        echo "  logs           - Show MinIO logs"
        echo "  buckets        - List all buckets"
        echo "  create-bucket  - Create a new bucket"
        echo "  console        - Open MinIO Console in browser"
        echo ""
        exit 1
        ;;
esac

