# MinIO Local Development Setup

This guide explains how to use MinIO as a local S3-compatible storage solution for development.

## 🎯 Quick Start

MinIO is already configured and running! Here's what was set up:

### ✅ What's Already Done

1. **MinIO Container** - Running on Docker
2. **Environment Variables** - Added to `.env` file
3. **Bucket Created** - `kvcare-dev` bucket is ready
4. **Helper Script** - `scripts/minio-setup.sh` for easy management

### 📋 Current Configuration

```env
IONOS_ENDPOINT_URL=http://localhost:9000
IONOS_ACCESS_KEY_ID=minioadmin
IONOS_SECRET_ACCESS_KEY=minioadmin
IONOS_BUCKET_NAME=kvcare-dev
```

### 🌐 Access Points

- **S3 API**: http://localhost:9000
- **Web Console**: http://localhost:9001
- **Login Credentials**: `minioadmin` / `minioadmin`

---

## 🛠️ Using the Helper Script

The `scripts/minio-setup.sh` script provides easy management of MinIO:

```bash
# Show status
./scripts/minio-setup.sh status

# Start MinIO (if stopped)
./scripts/minio-setup.sh start

# Stop MinIO
./scripts/minio-setup.sh stop

# Restart MinIO
./scripts/minio-setup.sh restart

# View logs
./scripts/minio-setup.sh logs

# List buckets
./scripts/minio-setup.sh buckets

# Create a new bucket
./scripts/minio-setup.sh create-bucket my-new-bucket

# Open MinIO Console in browser
./scripts/minio-setup.sh console
```

---

## 📦 Manual Docker Commands

If you prefer to use Docker commands directly:

```bash
# Check if container is running
docker ps | grep kvc-s3

# Start container
docker start kvc-s3

# Stop container
docker stop kvc-s3

# View logs
docker logs kvc-s3

# Remove container (data is preserved)
docker rm -f kvc-s3

# Recreate container
docker run -d --name kvc-s3 \
  -p 9000:9000 -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v ~/minio-data/kv-care/data:/data \
  -v ~/minio-data/kv-care/config:/root/.minio \
  minio/minio server /data --console-address ":9001"
```

---

## 🧪 Testing Upload Functionality

### 1. Restart Next.js Dev Server

After setting up MinIO, restart your Next.js development server to load the new environment variables:

```bash
# Stop current dev server (Ctrl+C)
# Then start again
npm run dev
```

### 2. Test Upload Through Application

1. Open the application in browser: http://localhost:3000
2. Navigate to a Case that has upload functionality
3. Click "Dokument hochladen" button
4. Select a PDF file and upload
5. Check MinIO Console to verify the file was uploaded

### 3. Verify in MinIO Console

1. Open http://localhost:9001
2. Login with `minioadmin` / `minioadmin`
3. Navigate to **Buckets** → **kvcare-dev** → **Object Browser**
4. You should see the uploaded file in the structure:
   ```
   kvcare-dev/
     └── cases/
         └── {caseId}/
             └── {userId}/
                 └── {fileName}.pdf
   ```

---

## 🔧 Troubleshooting

### Problem: "Connection refused" error

```bash
# Check if MinIO is running
./scripts/minio-setup.sh status

# If not running, start it
./scripts/minio-setup.sh start
```

### Problem: "Bucket does not exist"

```bash
# List buckets
./scripts/minio-setup.sh buckets

# Create bucket if missing
./scripts/minio-setup.sh create-bucket kvcare-dev
```

### Problem: Upload works but file is not visible

```bash
# Check MinIO logs
./scripts/minio-setup.sh logs

# Verify file exists in container
docker exec kvc-s3 ls -la /data/kvcare-dev/
```

### Problem: "Access Denied" error

1. Check that credentials in `.env` match MinIO credentials:

   ```env
   IONOS_ACCESS_KEY_ID=minioadmin
   IONOS_SECRET_ACCESS_KEY=minioadmin
   ```

2. Restart Next.js dev server after changing `.env`

---

## 📁 Data Persistence

MinIO data is stored in:

```
~/minio-data/kv-care/data/
```

Even if you remove the Docker container, your data will be preserved in this directory.

To completely reset MinIO:

```bash
# Remove container
./scripts/minio-setup.sh remove

# Delete data (⚠️ WARNING: This deletes all files!)
rm -rf ~/minio-data/kv-care/data/*

# Recreate container
./scripts/minio-setup.sh start
```

---

## 🔐 Security Notes

**For Development Only:**

- Default credentials (`minioadmin`/`minioadmin`) are used
- MinIO is accessible without authentication on localhost
- **DO NOT** use this setup in production
- **DO NOT** expose ports 9000/9001 to the internet

---

## 🚀 Advanced Usage

### Using MinIO Client (mc)

If you want command-line access to MinIO:

```bash
# Install MinIO Client
brew install minio/stable/mc

# Configure alias
mc alias set local http://localhost:9000 minioadmin minioadmin

# List buckets
mc ls local

# List files in bucket
mc ls local/kvcare-dev

# Copy file to bucket
mc cp test.pdf local/kvcare-dev/test.pdf

# Download file
mc cp local/kvcare-dev/test.pdf ./downloaded.pdf

# Remove file
mc rm local/kvcare-dev/test.pdf
```

### Creating Additional Buckets

```bash
# Using helper script
./scripts/minio-setup.sh create-bucket my-new-bucket

# Using Docker exec
docker exec kvc-s3 mc mb local/my-new-bucket

# Using MinIO Client (if installed)
mc mb local/my-new-bucket
```

---

## 📊 Monitoring

### View Real-time Logs

```bash
# Follow logs
docker logs -f kvc-s3

# Last 100 lines
docker logs --tail 100 kvc-s3
```

### Check Container Stats

```bash
docker stats kvc-s3
```

---

## 🔄 Switching Between MinIO and Production S3

To switch back to production S3 (IONOS), simply update `.env`:

```env
# Comment out MinIO config
# IONOS_ENDPOINT_URL=http://localhost:9000
# IONOS_ACCESS_KEY_ID=minioadmin
# IONOS_SECRET_ACCESS_KEY=minioadmin

# Add production credentials
IONOS_ENDPOINT_URL=https://s3.eu-central-3.ionoscloud.com
IONOS_ACCESS_KEY_ID=your-production-key
IONOS_SECRET_ACCESS_KEY=your-production-secret
IONOS_BUCKET_NAME=kvcare-prod
```

Then restart your Next.js dev server.

---

## 📚 Additional Resources

- [MinIO Documentation](https://docs.min.io)
- [MinIO Client Guide](https://min.io/docs/minio/linux/reference/minio-mc.html)
- [AWS SDK for JavaScript v3](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/)

---

## ✅ Setup Checklist

- [x] MinIO container created and running
- [x] Environment variables added to `.env`
- [x] Bucket `kvcare-dev` created
- [x] Helper script created (`scripts/minio-setup.sh`)
- [x] MinIO Console accessible at http://localhost:9001
- [ ] Next.js dev server restarted
- [ ] Upload functionality tested
- [ ] File verified in MinIO Console

---

**Need Help?**

Run the helper script to see available commands:

```bash
./scripts/minio-setup.sh
```
