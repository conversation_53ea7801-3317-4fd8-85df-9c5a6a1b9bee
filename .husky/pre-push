#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🛡️ Checking branch protection rules..."

# Protected branches - no direct push allowed
protected_branches="main master develop staging"
current_branch=$(git symbolic-ref HEAD | sed -e 's,.*/\(.*\),\1,')

# Check if pushing to a protected branch
for branch in $protected_branches; do
  if [ "$current_branch" = "$branch" ]; then
    echo "🚫 Direct push to '$branch' branch is not allowed!"
    echo "📋 Please create a Pull Request instead."
    echo "💡 Steps to follow:"
    echo "   1. Create a feature branch: git checkout -b feature/your-feature-name"
    echo "   2. Push your feature branch: git push origin feature/your-feature-name"
    echo "   3. Create a Pull Request through the web interface"
    exit 1
  fi
done

# Check if trying to push to protected branches from another branch
while read local_ref local_sha remote_ref remote_sha; do
  remote_branch=$(echo "$remote_ref" | sed -e 's,.*/\(.*\),\1,')
  for branch in $protected_branches; do
    if [ "$remote_branch" = "$branch" ]; then
      echo "🚫 Push to protected branch '$branch' is not allowed!"
      echo "📋 Please create a Pull Request instead."
      exit 1
    fi
  done
done

echo "✅ Branch protection check passed! Push allowed."
