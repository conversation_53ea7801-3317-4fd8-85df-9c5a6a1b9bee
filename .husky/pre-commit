# echo "🔍 Running pre-commit checks..."

# # Run TypeScript type checking
# echo "1/3 Running TypeScript type checking..."
# npm run ts:check
# if [ $? -ne 0 ]; then
#   echo "❌ TypeScript type checking failed. Commit aborted."
#   exit 1
# fi

# # Run lint and format
# echo "2/3 Running lint and format..."
# npm run lint:format
# if [ $? -ne 0 ]; then
#   echo "❌ Lint and format failed. Commit aborted."
#   exit 1
# fi

# # Run build
# echo "3/3 Running build..."
# npm run build
# if [ $? -ne 0 ]; then
#   echo "❌ Build failed. Commit aborted."
#   exit 1
# fi

# echo "✅ All pre-commit checks passed! Ready to commit."
