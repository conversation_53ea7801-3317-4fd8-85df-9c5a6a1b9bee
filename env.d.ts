namespace NodeJS {
    interface ProcessEnv {
        STRAPI_BASE_URL: string;
        STRAPI_API_TOKEN: string;
        IONOS_ACCESS_KEY_ID: string;
        IONOS_SECRET_ACCESS_KEY: string;
        ION<PERSON>_ENDPOINT_URL: string;
        ION<PERSON>_BUCKET_NAME: string;
        SMTP_SERVER: string;
        SMTP_PORT: number;
        SMTP_PASSWORD: string;
        SMTP_USER: string;
        DEV_MAIL_TO: string;
        DEV_MODE: string;
        MAIL_SEND_TO_SUPPORT: string;
        NEXT_PUBLIC_MAIL_SERIVCE: string;
        NUMBER_GENERATOR_SALT: string;
        NEXT_PUBLIC_ENVIRONMENT: string;
        MONGODB_URI: string;
        POSTGRES_URI: string;
        CORONJOB_API_KEY: string;
        THEME_ID: string;
        AUTH_URL: string;
        AUTH_SECRET: string;
        AUTH_KEYCLOAK_ID: string;
        ATUH_KEYCLOAK_SECRET: string;
        ATUH_KEYCLOAK_ISSUER: string;
        NEXTAUTH_SECRET: string;
        NEXTAUTH_URL: string;
    }
}
