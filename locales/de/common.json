{"errorMessage": {"validation": {"email": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein"}}, "remainingDaysChip": {"expired": "Abgelaufen", "daysLeft": "{daysLeft} Tage verbleibend"}, "tipStatus": {"invited": "Eingeladen", "pending": "<PERSON><PERSON><PERSON><PERSON>", "open": "<PERSON>en", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "accepted": "<PERSON><PERSON><PERSON><PERSON>"}, "header": {"title": "refeo"}, "menu": {"form": "Tippgeber Formular", "list": "Empfehlungsverlauf", "logout": "Ausloggen"}, "footer": {"copyright": "refeo by mv Services GmbH"}, "404": {"title": "404", "message": "Wir konnten diese Seite leider nicht finden.", "button": "Zur Startsei<PERSON>"}, "customerDashboard": {"greeting": "<PERSON><PERSON>", "overview": "Hier ist Ihr Angebotsüberblick", "loadingFailed": "Die Angebote konnten nicht geladen werden", "noOffers": "<PERSON><PERSON> gefunden", "offer": {"extensions": "Erweitertungen:", "button": {"accept": "Angebot akzeptieren", "decline": "<PERSON><PERSON><PERSON>", "downloadOffer": "<PERSON><PERSON><PERSON>", "downloadPolicy": "Police", "getOffer": "<PERSON><PERSON><PERSON> e<PERSON>holen", "actualizeOffer": "Aktuelles Ang<PERSON> e<PERSON>holen"}}, "acceptOfferModal": {"title": "Angebot akzeptieren", "question": "Möchten Sie das Angebot für <strong></strong> wirklich akzeptieren?", "primaryButton": "Ja, ak<PERSON><PERSON><PERSON>en", "secondaryButton": "Abbrechen"}, "rejectOfferModal": {"title": "<PERSON><PERSON><PERSON>", "questions": "Möchten Sie das Angebot für <strong></strong> wirklich ablehnen?", "primaryButton": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>", "secondaryButton": "Abbrechen"}, "infoCard": {"title": "So <PERSON><PERSON><PERSON><PERSON> dieser Bereich", "paragraph1": "Hier sehen <PERSON> alle Ihre aktuellen Angebote. <PERSON><PERSON> können diese direkt akzeptieren oder ablehnen. So<PERSON><PERSON> ein Angebot angenommen wird, erhalten Sie alle relevanten Dokumente wie die Police zum Download.", "paragraph2": "Vertrauen Sie auf unsere geprüften Versicherungsangebote. Wir stehen Ihnen jederzeit für Fragen zur Verfügung.", "footerTitle": "Kontakt & Hilfe", "phoneNumber": "+49 123 456 789", "email": "<EMAIL>"}}, "referralsHistory": {"title": "Empfehlungsverlauf", "searchHint": "Suche nach Kunde oder Email", "statusFilter": {"hint": "Status filtern", "showAll": "Alle"}, "downloadCsvTooltip": "Als CSV exportieren", "noReferrals": "<PERSON><PERSON> Empfehlungsverlauf ve<PERSON>", "loadingFailed": "Die Empfehlungen konnten nicht geladen werden", "notification": {"downloadCsvFailed": "Beim Exportieren ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "downloadAgreementFailed": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "invitationResent": "Gesendet!", "invitationResendingFailed": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut später."}, "referrerAgreementFileName": "Tippgebervereibarung"}, "adminLogin": {"title": "Zugang zum Admin-Bereich", "message": "<PERSON>te Ihre Administrator-E-Mail-<PERSON><PERSON><PERSON>.", "inputLabel": "E-Mail-Adresse", "submitSuccessMessage": "Bitte überprüfen Sie Ihr Postfach. Wir haben Ihnen einen Zugangslink gesendet.", "submitErrorMessage": {"generic": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut."}, "button": "<PERSON><PERSON><PERSON>"}, "referrerRegister": {"title": "Tippgeber Zugang anfordern", "message": "Geben Sie Ihre geschäftliche E-Mail-Adresse ein, um Zugang zum Tippgeber-Bereich zu erhalten.", "inputLabel": "E-Mail-Adresse", "submitSuccessMessage": "Bitte überprüfen Sie Ihr Postfach. Wir haben Ihnen einen Zugangslink gesendet.", "submitErrorMessage": {"generic": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut."}, "button": "<PERSON><PERSON><PERSON>"}, "customerRequestAccess": {"title": "Zugang zum Kunden-Dashboard anfordern", "message": "Geben Sie Ihre E-Mail-Adresse ein, um <PERSON>ng zum Kunden-Dashboard zu erhalten.", "inputLabel": "E-Mail-Adresse", "submitSuccessMessage": "Bitte überprüfen Sie Ihr Postfach. Wir haben Ihnen einen Zugangslink gesendet.", "submitErrorMessage": {"generic": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.", "customerNotFound": "Wir haben Sie in unserem System nicht gefunden. Bitte überprüfen Sie die E-Mail-Adresse oder wenden Si<PERSON> sich an den Administrator."}, "button": "<PERSON><PERSON><PERSON>"}, "email": {"invitation": {"preview": "Einladung zur Angebotserstellung", "subject": "Einladung zur Angebotserstellung Ihrer {{productName}}", "salutation": "<PERSON><PERSON> geehrte/r {{recipientName}},", "intro": "vielen Dank für Ihr Interesse an einem Angebot für Ihre {{productName}}. Sie erhalten diese E-Mail aufgrund der Empfehlung von {{referrerName}}.", "promo": "{{promoText}}", "cta": "Mit nur wenigen Angaben können Sie in unserem Tarifrechner Ihren persönlichen Beitrag berechnen & direkt online abschließen!", "button": "<PERSON>um Tarifre<PERSON>ner"}, "referrerLogin": {"preview": "Ihr Zugang ist jetzt aktiv", "subject": "Willkommen im Vertriebspartnerportal Privat – Ihr Zugang ist jetzt aktiv!!", "salutation": "<PERSON><PERSON> {{referrerEmail}},", "intro": "<PERSON>z<PERSON> willkommen beim ADVANIA Vertriebspartnerportal Privat! Wir freuen un<PERSON>, Sie als neuen Nutzer begrüßen zu dürfen.", "cta": "Ihr Zugang wurde erfolgreich eingerichtet und Si<PERSON> können sich ab sofort mit Ihrer E-Mail-Adresse unter folgendem Link anmelden:", "button": "Zum Vertriebspartnerportal Privat", "outro": "Wir wünschen Ihnen einen erfolgreichen Start und viel Freude mit unserem Online-Portal!"}, "customerLogin": {"preview": "<PERSON>hr Zugang zum Kunden-Dashboard", "title": "Willkommen im Kunden-Dashboard!", "message": "Sie können jetzt auf Ihr Kunden-Dashboard zugreifen. Klicken Sie auf den Button unten, um fortzufahren und Ihre Angebote, Policen und Informationen einzusehen:", "button": "<PERSON><PERSON>-Dashboard"}, "customerOfferNotification": {"preview": "<PERSON><PERSON> wurde erstellt", "title": "Vielen Dank für Ihr Interesse!", "message": "Sie haben ein Angebot erhalten. Sie können nun den Status Ihres Angebots und weitere Details jederzeit im Dashboard einsehen und annehmen:", "button": "<PERSON><PERSON><PERSON> e<PERSON>ehen"}, "customerPoliceNotification": {"preview": "Ihre Police ist jetzt verfügbar", "title": "<PERSON>hr Angebot wurde angenommen – Police erstellt!", "message": "Herzlichen Glückwunsch! Ihr Angebot wurde angenommen und die Police wurde erfolgreich erstellt. Sie können die fertige Police und alle Details jetzt in Ihrem Dashboard einsehen:", "button": "Police im Dashboard ansehen"}, "referrerOfferNotification": {"preview": "Ihr Advania Tippgeber-Update", "title": "Neues Angebot erstellt!", "message": "Ein Kunde, den Si<PERSON> e<PERSON>laden haben, hat gerade ein Ang<PERSON>ot erstellt. Sie können jetzt die Details und den Verlauf im Empfehlungsverlauf einsehen:", "button": "Empfehlungsver<PERSON><PERSON>"}, "referrerPoliceNotification": {"preview": "Ihr Advania Tippgeber-Update", "title": "Ein Angebot wurde angenommen!", "message": "Ein Kunde, den Si<PERSON> e<PERSON>laden haben, hat das Angebot angenommen. Sie können jetzt die Details und den Verlauf im Empfehlungsverlauf einsehen:", "button": "Empfehlungsverlauf prüfen"}, "referrerAgreement": {"preview": "Ihre Tippgebervereinbarung", "title": "Ihre Tippgebervereinbarung", "message": "Ihre Tippgebervereinbarung finden Sie im Anhang"}, "footer": {"signature": "Ihre\nADVANIA GmbH\nVersicherungsmakler", "legal": "<PERSON>ten Si<PERSON> bitte unsere rechtlichen Hinweise:", "legalLinks": {"privacy": {"text": "Informationen zur Datenverarbeitung finden Sie hier", "url": "https://advania.de/datenschutz"}, "firstInfo": {"text": "Die Erstinformation ADVANIA GmbH gemäß §15 Versicherungsvermittlungsverordnung finden Sie hier", "url": "https://advania.de/erstinformation"}, "unsubscribe": {"text": "Falls Sie dieses Ang<PERSON>ot nicht in Anspru<PERSON> nehmen möchten und Ihre Daten gelöscht werden sollen, klicken <PERSON>e bitte hier", "url": "https://advania.de/unsubscribe"}}, "companyInfo": "ADVANIA GmbH\nVersicherungsmakler\nBörsenbrücke 6 • 20457 Hamburg\nMail <EMAIL>\nWeb www.advania.de\n\nSitz und Registergericht: Hamburg / HRB 156554\nVersicherungsvermittlerregister Nr.: D-PO8M-CW8L5-00\nG<PERSON>chäftsführer: <PERSON>, <PERSON> zu unserem Unternehmen gem. EU-Vermittlerrichtlinie", "disclaimerDe": "Diese E-Mail und alle Anhänge enthalten vertrauliche und/oder rechtlich geschützte Informationen. Wenn Sie nicht der richtige Adressat sind oder diese E-Mail irrtümlich erhalten haben, informieren Sie bitte sofort den Absender und vernichten Sie diese E-Mail. Das unerlaubte Kopieren sowie die unbefugte Weitergabe dieser E-Mail sind nicht gestattet.", "disclaimerEn": "This e-mail and any attached files may contain confidential and/or privileged information. If you are not the intended recipient (or have received this e-mail in error) please notify the sender immediately and destroy this e-mail. Any unauthorised copying, disclosure or distribution of the material in this e-mail is strictly forbidden.", "preview": "Einladung zur Angebotserstellung"}}, "cookieConsent": {"title": "Wir verwenden Cookies", "description": "Wir verwenden Cookies, um die Website zu betreiben und das Erlebnis zu verbessern. Sie können alle Cookies akzeptieren, nicht-essentielle ablehnen oder Präferenzen konfigurieren.", "acceptAll": "Alle akzeptieren", "reject": "<PERSON><PERSON> <PERSON><PERSON>", "configure": "Einstellungen", "configureTitle": "Cookie-Einstellungen", "configureDescription": "<PERSON><PERSON><PERSON><PERSON> aus, welche Cookies du zulassen möchtest. Du kannst deine Entscheidung jederzeit ändern.", "save": "Speichern", "categories": {"essential": "Notwendige Cookies", "essentialDesc": "Diese Cookies sind für die Basisfunktionen der Website erforderlich (z. B<PERSON>gin). Sie werden immer gesetzt.", "functional": "Funktionale Cookies", "functionalDesc": "Ermöglichen zusätzliche Funktionen (z. B. Einstellungen, Formularverhalten).", "analytics": "Analytics", "analyticsDesc": "<PERSON><PERSON><PERSON> uns, die Nutzung der Website zu verstehen und zu verbessern.", "marketing": "Marketing", "marketingDesc": "Werbe- und Tracking-Cookies, die personalisierte Werbung ermöglichen."}}, "unauthorized": {"title": "<PERSON><PERSON><PERSON> verweigert", "message": "Sie haben keinen gültigen Zugang zu diesem Bereich. Bitte wenden Si<PERSON> sich an den Administrator oder fordern Sie einen Zugangslink an.", "alert": "Zugriff nicht autorisiert", "button": "<PERSON><PERSON>"}, "referralFlow": {"form": {"salutation": {"masculine": "<PERSON>", "feminine": "<PERSON><PERSON>", "nonBinary": "Divers"}, "hint": {"fieldIsRequired": "Bitte füllen Sie das Feld ein", "invalidContractNumber": "Bitte geben Si<PERSON> eine gültige Vertragsnummer ein (z. B. AS-**********)", "invalidEmail": "<PERSON>te geben Si<PERSON> eine gültige Emailadresse ein"}, "title": {"referrerInfo": "Tippgeber-Daten", "customerInfo": "Kunden-Daten", "products": "Produktwahl", "insuranceStart": "Versicherungsbeginn"}, "subtitle": {"name": "Name", "address": "Addresse", "contact": "Kontakt", "agency": "Agentur", "bankAccount": "Bankverbindung", "contractNumber": "Bisherige Vertragsnummber"}, "fieldLabel": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "street": "Straße", "houseNumber": "Hausnummer", "email": "Emailad<PERSON><PERSON>", "phoneNumber": "Telefonnummer", "agencyNumber": "Agenturnummer", "salesDirection": "Vertriebsdirektion", "iban": "IBAN", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "city": "Ort", "contractNumber": "Vertragsnummer", "termsAndConditions": "<PERSON><PERSON><PERSON> bestätige ich, dass", "salutation": "<PERSON><PERSON><PERSON>"}, "residentialBuildingInsurance": "Wohngebäude-Versicherung", "button": {"next": "<PERSON><PERSON>"}, "termsAndConditions": {"1": "der Kunde mit diesem Risiko von mir betreut wird", "2": "die seitens der Allianz definierten und kommunizierten Voraussetzungen für eine Ventilierung in diesem konkreten Vorgang vorliegen", "3": "die oben eingetragene Vertragsnummer stammt aus der VWG Re-Underwriting-Aktion XXX", "4": "der Kunde hat sich damit einverstanden erklärt, dass seine Kontaktdaten (Name, E-Mailadresse) an die Advania GmbH, Börsenbrücke 6, 20457 Hamburg, übermittelt werden dürfen und sich die Advania GmbH mit ihm zwecks Angebotserstellung für eine Wohngebäudeversicherung per E-Mail in Kontakt setzt"}, "salesDirection": {"Hamburg": "Hamburg", "Köln": "Köln", "Berlin": "Berlin", "Leipzig": "Leipzig", "Frankfurt": "Frankfurt", "Stuttgart": "Stuttgart", "Nürnberg": "Nürnberg", "München": "München", "Sonstige": "Sonstige"}}, "summary": {"title": {"summary": "Zusammenfassung", "agreement": "Vereinbarung"}, "button": {"backToForm": "DATEN ANPASSEN", "loadAgreement": "VEREINBARUNGSVORSCHAU", "sendOffer": "ANFRAGE AN KUNDE SENDEN"}, "modal": {"successTitle": "Anfrage erfolgreich versendet!", "successMessage": "Der Kunde erhält nun seinen persönlichen Link per E-Mail, um sein Ang<PERSON>ot zu rechnen und abzuschließen. Wir informieren Sie über das Zustandekommen oder die Ablehnung der Überleitung per E-Mail."}, "agreementAcceptance": "Mit Versendung des Angebotlinks an den Kunden akzeptiere ich die Tippgebervereinbarung für das Privatgeschäft.", "agreementName": "\"Vereinbarung über eine Tätigkeit als Tippgeber Privatgeschäft\"", "genericErrorMessage": "Ein Fehler ist beim Senden aufgetreten. Bitte versuchen Sie es später erneut."}}, "insuranceForm": {"form": {"mainTitle": "Angebot Wohngebäudeversicherung", "title": {"need": "<PERSON><PERSON><PERSON>", "buildingData": "Gebäudedaten", "construction": "Bauweise und Anlagen", "offer": "<PERSON><PERSON><PERSON>"}, "subtitle": {"personalData": "Name", "personalAddress": "Anschrift", "propertyAddress": "Adresse der Immobilie", "renovations": "Welche Gewerke wurden in den letzten 30 Jahren vollständig modernisiert?", "riskLocation": "Risikoort", "previousClaims": "<PERSON><PERSON><PERSON><PERSON>", "damageDetails": "Was ist passiert und wie hoch war der Schaden?", "construction": "Bauweise", "floors": "Stockwerke", "livingArea": "Wohnfläche", "specialFeatures": "Sonderausstattungen", "outbuilding": "<PERSON>ebengeb<PERSON>ude", "renewableEnergy": "Anlagen für erneuerbare Energien", "buildingFeatures": "Bauausführungen / Ausstattungen des Gebäudes", "deductible": "Selbstbeteiligung", "coverage": "Versicherungsschutz", "additionalOptions": "Weitere Optionen", "policyholder": "Versicherungsnehmer", "policyStart": "Versicherungsbeginn"}, "actions": {"next": "<PERSON><PERSON>", "back": "Zurück", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltip": {"livingAreaBasement": "Nur die zu Wohn- <PERSON><PERSON> genutzten Wohnfläche sind zu berücksichtigen", "specialFeaturesList": "<PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, nachträglich verbaute Wärmedämmung, <PERSON>h<PERSON><PERSON> becken im Gebäude"}, "fieldLabel": {"buildingType": "Bitte wählen Sie Ihren Gebäudetyp", "street": "Straße", "houseNumber": "Hausnummer", "postalCode": "<PERSON><PERSON><PERSON><PERSON>", "city": "Ort", "stormZone": "Tarifzone Sturm", "waterZone": "Tarifzone Leitungswasser", "zuersZone": "ZÜRS-Zone", "constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "numUnits": "<PERSON><PERSON><PERSON> der Wohneinheiten", "newBuilding": "Neubau", "renovations": "Welche Gewerke wurden in den letzten 30 Jahren saniert?", "waterPipes": "Leitungswasserrohre", "heating": "Heizung und Heizungsrohre", "roof": "<PERSON><PERSON>", "electricity": "<PERSON><PERSON>", "partialUse": "Befindet sich das Gebäude ganz oder teilweise in gewerblicher Nutzung?", "nearbyBusiness": "Sind in einem Umkreis von 10 Metern Gewerbebetriebe?", "businessType": "Art der Gewerbebetriebe", "nearbyForests": "Befinden sich in einem Umkreis von 10 Metern Wälder?", "readyForOccupancy": "Ist das Gebäude bei Versicherungsbeginn bezugsfertig?", "permanentOccupancy": "Ist das Gebäude bei Versicherungsbeginn ständig bewohnt?", "vacancy": "Steht das Gebäude bei Versicherungsbeginn leer?", "vacancyPercent": "Anteil des Leerstands in Prozent", "prevDamageFree": "Waren Sie in den letzten 5 Jahren in der Wohngebäudeversicherung schadenfrei?", "prevDamageCount": "Wie viele Wohngebäude-Schäden waren es in den letzten 5 Jahren?", "damageCause": "<PERSON><PERSON><PERSON>", "damageYear": "<PERSON><PERSON><PERSON>", "damageAmount": "Schadenhöhe", "houseConstruction": "Wie ist Ihr Ha<PERSON> g<PERSON>?", "softRoof": "Weiche Dachung?", "roofType": "Welche Dachform hat Ihr <PERSON>?", "atticFinished": "Ist Ihr Dachgeschoss ausgebaut?", "numFloors": "Wie viele Obergeschosse hat Ihr Haus?", "hasBasement": "Hat Ihr <PERSON>us ein <PERSON>- / Untergeschoss?", "livingAreaMain": "Wohnfläche im Erd-, Ober- und Dachgeschoss (m²)", "livingAreaBasement": "Wohnfläche im Keller (m²)", "garageParkingSpacesNumber": "Anzahl der Stellplätze in der Garage", "carportParkingSpacesNumber": "Anzahl der Stellplätze im Carport", "undergroundParkingSpacesNumber": "<PERSON><PERSON><PERSON>garagenstellplätze", "specialFeatures": {"list": "Sonderausstattungen", "rebuildingPrice": "Neubauwert der Sonderausstattungen"}, "outbuilding": {"howUsed": "Art der Nutzung des Nebengebäudes", "squareFootage": "Größe (m²)", "wallType": "Bauart der Außenwände", "roofType": "Dachung", "rebuildingPrice": "Neubauwert"}, "renewableEnergyInstallations": "Sind Anlagen für erneuerbare Energien vorhanden?", "maxPowerKWp": "Maximale Leistung (kWp)", "solarThermie": "<PERSON><PERSON><PERSON>", "geothermie": "Geother<PERSON>", "heatPumpAC": "Wärmepumpe / Klimaanlage", "naturalStoneCopperRoof": "Naturstein, Kupferdach", "stoneCeramicCladding": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>kle<PERSON>, Handstrichklinker", "stuccoWood": "St<PERSON><PERSON>beiten, Edelholzverkleidung", "premiumFlooring": "<PERSON><PERSON><PERSON>b<PERSON><PERSON>, Parkett- oder Teppichböden in hochwertiger Qualität", "metalWoodWindows": "Leichtmetall- oder Holzsprossenfenster", "premiumDoors": "Edelholztüren", "premiumSanitary": "Hochwertige Sanitäreinrichtungen", "heatingRenewableSystems": "W<PERSON>rmepumpen, Solaranlagen, Fußboden- und Deckenheizungen", "premiumKitchen": "Hochwertige Einbauküchen", "deductible": "Selbstbeteiligung wählen", "coverageFire": "Feuer absichern", "coverageWater": "Wasser absichern", "coverageStorm": "Sturm absichern", "coverageBestAdvice": "Gewünschten Versicherungsschutz (Best Advice / Tarif) wählen", "paymentInterval": "Zahlungsweise wählen", "glassInsurance": "Glasversicherung", "unnamedRisks": "Unbenannte Gefahren absichern", "extendedPipeCoverage": "Erweiterter Versicherungsschutz für Rohre", "salutation": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Nachname", "policyStartDate": "Versicherungsbeginn"}}, "infoCard": {"title": "Wichtige Hinweise", "steps": {"need": {"disclaimer": "<PERSON>er erfassen Sie die Grunddaten zu Ihrem Objekt.", "tipsTitle": "<PERSON><PERSON><PERSON>", "tips": [{"title": "Was möchten Si<PERSON> versichern?", "text": "Schützen Sie sich als Hauseigentümer vor den finanziellen Folgen von Sachschäden an Ihrem Wohngebäude. Unsere Wohngebäudeversicherung erstattet Kosten für Reparaturarbeiten bis hin zum Wiederaufbau Ihres Gebäudes. Wir bieten Ihnen Versicherungsschutz für Ihr Ein-, Zwei- oder Mehrfamilienhaus (bis 4 Wohneinheiten) und sichern dieses gegen Schäden wie Feuer, Leitungswasser, Sturm/Hagel, sowie weitere Naturgefahren (z.B. Überschwemmung oder Erdrutsch) ab."}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "text": "Ihre Daten werden sicher und verschlüsselt übertragen."}]}, "buildingData": {"disclaimer": "<PERSON>te machen Sie Angaben zum Gebäudezustand.", "tipsTitle": "Gebäudedaten", "tips": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "text": "Als Baujahr tragen Si<PERSON> bitte das Jahr e<PERSON>, in dem Ihr Gebäude erstmals bezugsfertig war/wird. (<PERSON><PERSON><PERSON>, die vor über 70 Jahren erbaut wurden, benötigen wir weitere Angaben zur Modernisierung. ) "}, {"title": "Bisherige W<PERSON>ngebäude-Schäden", "text": "Bitte geben Sie hier an, ob in den letzten 3 Jahren Wohngebäude- oder Glasschäden aufgetreten sind. Beantworten Sie die Frage unabhängig davon, ob Sie die Schäden einem anderen Versicherer gemeldet haben. Treffen Ihre Angaben zu Vorschäden nicht zu, drohen Ihnen Nachteile im Schadenfall."}]}, "basicData": {"disclaimer": "Bauweise und Ausstattung helfen bei der Risiko­einschätzung.", "tipsTitle": "Bauweise und Anlagen", "tips": [{"title": "Bauweise", "text": "Bitte geben Si<PERSON> an, aus welchem Material die Außenwände und der Dachbelag Ihres Hauses bestehen. <br /> Massiv (Mauerwerk, Beton) <br /> Stahl- oder Holzfachwerk mit Stein- oder Glasfüllung, Stahl- oder Stahlbeton-Konstruktion mit Wandplattenverkleidung aus nichtbrennbarem Material (z.B. Profilblech, Asbestzement, kein Kunststoff) <br /> <PERSON><PERSON><PERSON>, Holzfachwerk mit Lehmfüllung, Holzkonstruktion mit Verkleidung jeglicher Art, Stahl- oder Stahlbetonkonstruktion mit Wandplattenverkleidung aus Holz oder Kunststoff, Gebäude mit einer oder mehreren offenen Seiten <br /> Fertighaus? <br /> Weiches Dach: <br /> Dachbelag aus vollständig oder teilweise mit Holz, Ried, <PERSON><PERSON><PERSON>, Stroh u.ä."}, {"title": "Wohnfläche", "text": "Anzugeben ist die Grundfläche aller Wohnräume in allen Geschossen einschließlich Dachgeschoss mit Hobbyräumen und Wintergärten. Nicht zu berücksichtigen sind Treppen, <PERSON><PERSON><PERSON>, Loggien und Terrassen sowie Keller-/ Speicher-/ Bodenräume, die nicht zu Wohn- oder Ho<PERSON>zwecken genutzt werden."}, {"title": "Anzahl der Kfz-Stellplätze", "text": "Bitte geben Sie hier die Anzahl der Stellplätze in vorhandenen Carports, Garagen oder einer Tiefgarage an. Entscheidend ist die Anzahl der Stellplätze. <br /> Mit Tiefgaragenstellplätzen ist die Anzahl der Stellplätze in einer Tiefgarage im Mehrfamilienhaus gemeint. Eine in einem Ein- oder Zweifamilienhaus integrierte Garage muss bei der Wertermittlung nicht berücksichtigt werden. <br /> Carports müssen nur dann angegeben werden, wenn die Grundfläche je Carport mehr als 30m² beträgt."}, {"title": "<PERSON>ebengeb<PERSON>ude", "text": "Gewächs- und Gartenhäuser bis zu einer Grundfläche von insgesamt 25 qm sind bedingungsgemäß mitversichert. Die Entschädigungsgrenzen für dieses „Sonstiges Zubehör und sonstige Grundstücksbestandteile“ entnehmen Sie bitte dem Bedingungswerk. Bitte beachten Sie, dass die hier genannten Entschädigungsgrenzen nicht nur Gewächs- und Gartenhäuser berücksichtigen."}, {"title": "Photovoltaikanlage", "text": "Eine Photovoltaikversicherung zur Elektronik- inkl. Ertragsausfallversicherung kann separat beantragt werden - siehe'Baustein Photovoltaik'"}]}, "offer": {"disclaimer": "Wählen Sie Ihren gewünschten Versicherungsschutz.", "tipsTitle": "<PERSON><PERSON><PERSON>", "tips": [{"title": "<PERSON>ust<PERSON>", "text": "SB für Elementar 10 % des Schadens, min. 500.- €, max. 5.000,- €. ACHTUNG: 10 Jahre vorschadenfrei ?"}, {"title": "Baustein Ableitungsrohre", "text": "Mitversichert sind ohne Einschluss dieses Bausteins die Ableitungsrohre innerhalb des zu versichernden Gebäudes. Durch den Baustein werden zusätzlich die Ableitungsrohre auf und außerhalb des Grundstückes mitversichert. Den Versicherungsumfang und Entschädigungsgrenzen für 'Ableitungsrohre auf und außerhalb des Versicherungsgrundstücks' entnehmen Sie bitte dem zugrunde liegenden Bedingungswerk."}, {"title": "<PERSON><PERSON><PERSON>", "text": "Versichert ist die Gebäude- und Mobiliarverglasung des im Versicherungsschein genannten Gebäudes gegen Bruchschäden (Zerbrechen). In Zwei- oder Mehrfamilienhäusern (auch Einfamilienhäuser mit Einliegerwohnung) beschränkt sich der Versicherungsschutz für Mobiliarverglasung auf die vom Versicherungsnehmer bewohnte Wohnung."}, {"title": "Baustein Photovoltaik", "text": "Sofern in der Wertermittlung berücksichtigt ist die Photovoltaikanlage gegen die Gefahren Feuer, Leitungswasser, Sturm und Hagel versichert. Durch den Einschluss des Bausteins wird der Versicherungssschutz um eine Elektronik- inkl. Ertragsausfallversicherung erweitert. Die Leistung der Photovoltaikanlage darf 15 kWp nicht übersteigen. Den Versicherungsumfang und die Entschädigungsgrenzen entnehmen Sie bitten den „Besondere Bedingungen für die Versicherung von Photovoltaikanlagen (BPV 2013)“"}, {"title": "Unbenannte Gefahren?", "text": " "}]}}}}, "customerLanding": {"partners": {"sectionTitle": "Wer ist beteiligt?", "primary": {"title": "ADVANIA", "desc": "Stellt das Kundenportal bereit, in dem Sie Ihr Angebot erstellen."}, "secondary": {"title": "WECOYA", "desc": "Erstellt das Wohngebäudeversicherungs-Angebot."}}, "product": {"sleepEasyTitle": "Unser Sleep-Easy-Versprechen für Sie", "sectionIntro": "WECOYA Wohngebäude best advice – mit Risikoträger Gothaer Versicherung. Verlassen Sie sich auf umfassenden Schutz und hohe Leistungsstandards.", "highlightsTitle": "Leistungshighlights", "detailedTitle": "Weitere wichtige Leistungen", "optionalTitle": "Optionale Zusatzbausteine", "benefit1": "Unterversicherungsverzicht", "benefit1Desc": "Absicherung zum Neuwert für gleichwertigen Wiederaufbau.", "benefit2": "Grob fahrlässige Verstöße", "benefit2Desc": "Verzicht auf Leistungskürzung bei Herbeiführung des Versicherungsfalles.", "benefit3": "Haustechnische Anlagen (inkl. Smart Home)", "benefit3Desc": "Leistung bis zu 25.000 € für ergänzende technische Gefahren.", "benefit4": "Nässeschäden", "benefit4Desc": "Schutz bei Schäden durch undichte Fugen und ebenerdige Duschen.", "benefit5": "Böswillige Beschädigungen", "benefit5Desc": "Absicherung bei Einbruch, Graffiti und böswilligen Handlungen bis 25.000 €.", "benefit6": "Summen- und Bedingungsdifferenzdeckung", "benefit6Desc": "Bei laufendem Vorvertrag gilt der umfangreichere Versicherungsschutz bis zu 18 Monate.", "elementar": {"label": "Elementar (optional)"}, "glas": {"label": "Glasversicherung (optional)"}, "updateGuarantee": {"label": "Update-<PERSON><PERSON><PERSON>"}, "footnote": "Die genannten Informationen sind ein Auszug aus den Versicherungsbedingungen. Maßgeblich ist der vollständige Wortlaut der Bedingungen.", "cta": "Jetzt Angebot berechnen"}, "dashboard": {"sectionTitle": "<PERSON>hre Vorteile auf einen Blick", "sectionDesc": "Mit WECOYA Wohngebäude best advice sind Sie von der Beratung bis zur Schadenregulierung gut aufgehoben.", "feature": {"differenz": "Summen- und Bedingungsdifferenzdeckung – bis zu 18 Monate", "update": "Update-Garantie – künftige prämienneutrale Leistungsverbesserungen gelten automatisch", "bestleistung": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – Best-Leistungs-Garantie", "besserstellung": "Besserstellungsklausel gegenüber Vorverträgen", "transparenz": "Transparente Bedingungen und faire Leistungsvoraussetzungen"}, "cta": "Jetzt absichern", "ctaSecondary": "Details herunterladen"}}}