generator client {
  provider = "prisma-client-js"
  output   = "../src/lib/mongo-db-client"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URI")
}

model CustomerHistory {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  customerId    Int
  userId        Int
  timestamp     DateTime      @default(now())
  operation     AuditOperation
  action        String?
  dataBefore    Json?
  dataAfter     Json?
  changedFields String[]

  @@index([customerId, timestamp])
  @@index([operation, timestamp])
  @@index([userId, timestamp])
  @@map("customer_history")
}

model UserHistory {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  userId        Int        // Changed from Int to String for consistency
  timestamp     DateTime      @default(now())
  operation     AuditOperation
  action        String?
  dataBefore    Json?
  dataAfter     Json?
  changedFields String[]

  @@index([userId, timestamp])
  @@index([operation, timestamp])
  @@map("user_history")
}

model CaseHistory {
  id            String        @id @default(auto()) @map("_id") @db.ObjectId
  caseId        Int
  userId        Int
  timestamp     DateTime      @default(now())
  operation     AuditOperation
  action        String?
  dataBefore    Json?
  dataAfter     Json?
  changedFields String[]

  @@index([caseId, timestamp])
  @@index([operation, timestamp])
  @@index([userId, timestamp])
  @@map("case_history")
}

enum AuditOperation {
  CREATE
  UPDATE
  DELETE
}
