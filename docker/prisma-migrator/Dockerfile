# syntax=docker/dockerfile:1
ARG BASE_REF=asevo-global.cr.de-fra.ionos.com/refeo-prisma-base:latest
FROM ${BASE_REF}

USER appuser
WORKDIR /app

# Copy the migration assets the scripts need (no app deps)
COPY --chown=appuser:appuser prisma/ ./prisma/
# If you have any mongo config files, copy them too, e.g.:
# COPY --chown=appuser:appuser prisma-mongo-db/migrate-mongo-config.js ./prisma-mongo-db/

# No ENTRYPOINT/CMD here — drive the command from Helm values:
# e.g., command: ["npm","run","db:deploy"]
