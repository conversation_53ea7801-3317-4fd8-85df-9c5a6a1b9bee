# Stage 1: Install deps and build the Next.js app
FROM node:22-alpine AS build
WORKDIR /app

# Copy only the necessary files first to leverage Docker cache
COPY package.json package-lock.json ./
RUN npm install

# Copy the rest of the source files
COPY . .

# Run the build with build-time environment variables
RUN npm run prisma:generate
RUN npm run build

# Stage 2: Run the app using the standalone output
FROM node:22-alpine AS runner
WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

# Copy necessary artifacts from the build stage
COPY --from=build --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=build --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=build --chown=nextjs:nodejs /app/public ./public

USER nextjs

EXPOSE 3000
ENV PORT=3000

CMD ["node", "server.js"]
