# Stage 1: Install deps and build the Next.js app
FROM node:20 AS build
WORKDIR /app

# Accept build-time environment variables
# ARG NEXT_PUBLIC_MAIL_SERIVCE
# ARG NEXT_PUBLIC_ENVIRONMENT

# Set them as ENV so they're accessible during the build (e.g., by Next.js)
ENV NEXT_PUBLIC_MAIL_SERIVCE=true
ENV NEXT_PUBLIC_ENVIRONMENT=dev
ENV NEXT_TELEMETRY_DISABLED 1

# Copy only the necessary files first to leverage Docker cache
COPY package.json package-lock.json ./
RUN npm install

# Copy the rest of the source files
COPY . .

# Run the build with build-time environment variables
RUN npm run prisma:generate
RUN npm run build

# Stage 2: Run the app using the standalone output
FROM node:20 AS runner
WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED 1

# Create non-root user
RUN addgroup --system --gid 999 nodejs \
    && adduser --system --uid 999 nextjs

# Copy necessary artifacts from the build stage
COPY --from=build --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=build --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=build --chown=nextjs:nodejs /app/public ./public

USER nextjs

EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
