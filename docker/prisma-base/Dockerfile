# syntax=docker/dockerfile:1
FROM node:22-slim

# System deps Prisma needs
RUN apt-get update && apt-get install -y --no-install-recommends \
    openssl ca-certificates \
 && rm -rf /var/lib/apt/lists/*

# Non-root runtime user
RUN useradd -m -u 10001 appuser
WORKDIR /app

# Install ONLY tooling deps (from prisma-tooling/)
# Copy lockfiles first for better caching
COPY prisma-tooling/package.json prisma-tooling/package-lock.json* ./
# If you use a private registry, also copy an .npmrc:
# COPY prisma-tooling/.npmrc ./
RUN chown -R appuser:appuser /app

USER appuser
ENV PATH="/app/node_modules/.bin:${PATH}"

# Deterministic install; prisma CLI available in node_modules/.bin
RUN npm ci

# Warm Prisma engines now (avoids runtime downloads/permissions issues)
RUN npx prisma --version
