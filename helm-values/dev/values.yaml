app:
  name: asevo
  namespace: dev

replicaCount: 1

image:
  repository: asevo-global.cr.de-fra.ionos.com/asevo
  tag: not-set
  pullPolicy: Always
  pullSecrets:
    - name: asevo-regcred

imagePullSecret:
  create: true
  name: asevo-regcred
  registry: asevo-global.cr.de-fra.ionos.com
  username: <your-username>
  password: <your-password>

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

ingressRoute:
  enabled: true
  host: app.dev.asevo.ai
  entryPoints:
    - websecure
  certResolver: letsencrypt
  servicePort: 80 # Target port inside the pod

resources:
  limits:
    cpu: '500m'
    memory: '256Mi'
  requests:
    cpu: '250m'
    memory: '128Mi'

secretEnvFromFiles:
  - secretName: tf-generated-secrets-asevo
    keys:
      - MONGODB_URI
      - POSTGRES_URI
      - IONOS_ACCESS_KEY_ID
      - IONOS_SECRET_ACCESS_KEY

externalSecrets:
  enabled: true
  mounts:
    - name: asevo-generated-secret
      secretName: tf-generated-secrets-asevo
      mountPath: /secrets/generated

configMap:
  enabled: true
  name: asevo-config
  data:
    STRAPI_BASE_URL: http://***********:1337/api
    NEXT_PUBLIC_ENVIRONMENT: dev
    PORT: '3000'
    LOG_LEVEL: debug
    IONOS_ENDPOINT_URL: https://s3.eu-central-3.ionoscloud.com
    IONOS_BUCKET_NAME: alpha-object-storage
    SMTP_SERVER: smtp.office365.com
    SMTP_PORT: 587
    SMTP_USER: <EMAIL>
    DEV_MAIL_TO: <EMAIL>
    DEV_MODE: true
    NEXT_PUBLIC_MAIL_SERVICE: true
    THEME_ID: ykghwlnfyrmifzczcyb13yg9
    AUTH_URL: https://dev.asevo.de
    AUTH_KEYCLOAK_ID: asevo-web-dev
    AUTH_KEYCLOAK_ISSUER: https://login.dev.asevo.de/realms/dev

secret:
  enabled: true
  name: asevo-secret

migrator:
  # Hooks
  hooks: [pre-install, pre-upgrade]
  hookDeletePolicy: 'before-hook-creation,hook-succeeded'

  image:
    repository: asevo-global.cr.de-fra.ionos.com/asevo-migrator
    tag: not-set
    pullPolicy: IfNotPresent
    pullSecrets:
      - name: asevo-regcred

  command: ['npm', 'run', 'db:deploy']
  args: []

  # Fine-grained inheritance (all default OFF for minimal output)
  inherit:
    configMapEnvFrom: true
    secretEnvFrom: true
    secretEnvFromFiles: true
    externalSecretMounts: true
    resourcesFromApp: true

  backoffLimit: 3
  ttlSecondsAfterFinished: 600
