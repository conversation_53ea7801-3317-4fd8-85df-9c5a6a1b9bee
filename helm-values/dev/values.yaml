app:
  name: kv-care
  namespace: dev

replicaCount: 1

image:
  repository: kvcare-global.cr.de-fra.ionos.com/kv-care
  tag: not-set
  pullPolicy: Always
  pullSecrets:
    - name: kv-care-regcred

imagePullSecret:
  create: true
  name: kv-care-regcred
  registry: kvcare-global.cr.de-fra.ionos.com
  username: <your-username>
  password: <your-password>

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

ingressRoute:
  enabled: true
  host: dev.kvcare.de
  entryPoints:
    - websecure
  certResolver: letsencrypt
  servicePort: 80 # Target port inside the pod

resources:
  limits:
    cpu: '500m'
    memory: '1024Mi'
  requests:
    cpu: '250m'
    memory: '128Mi'

secretEnvFromFiles:
  - secretName: tf-generated-secrets-kvcare
    keys:
      - IONOS_ACCESS_KEY_ID
      - IONOS_SECRET_ACCESS_KEY
      - MONGODB_URI
      - DATABASE_URL
      - AUTH_KEYCLOAK_ID
      - AUTH_KEYCLOAK_SECRET
      - NEXT_PUBLIC_AUTH_KEYCLOAK_ID
      - NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER
      - ENV
      - DOMAIN

externalSecrets:
  enabled: true
  mounts:
    - name: kvcare-generated-secret
      secretName: tf-generated-secrets-kvcare
      mountPath: /secrets/generated

configMap:
  enabled: true
  name: kv-care-config
  data:
    NEXT_PUBLIC_ENVIRONMENT: dev
    PORT: '3000'
    LOG_LEVEL: debug
    IONOS_ENDPOINT_URL: https://s3.eu-central-3.ionoscloud.com
    IONOS_BUCKET_NAME: kvcare-dev-test-dev
    SITE_URL: https://dev.kvcare.de
    AUTH_URL: https://dev.kvcare.de
    AUTH_KEYCLOAK_ISSUER: https://login.dev.kvcare.de/realms/dev
    NEXT_PUBLIC_AUTH_URL: https://dev.kvcare.de

secret:
  enabled: true
  name: kv-care-secret

migrator:
  # Hooks
  hooks: [pre-install, pre-upgrade]
  hookDeletePolicy: "before-hook-creation,hook-succeeded"

  image:
    repository: kvcare-global.cr.de-fra.ionos.com/kv-care-migrator
    tag: not-set
    pullPolicy: IfNotPresent
    pullSecrets:
      - name: kv-care-regcred

  command: ["npm","run","db:deploy"]
  args: []

  # Fine-grained inheritance (all default OFF for minimal output)
  inherit:
    configMapEnvFrom: true
    secretEnvFrom: true
    secretEnvFromFiles: true
    externalSecretMounts: true
    resourcesFromApp: true

  backoffLimit: 3
  ttlSecondsAfterFinished: 600
