app:
  name: refeo
  namespace: refeo

replicaCount: 1

image:
  repository: asevo-global.cr.de-fra.ionos.com/refeo
  tag: not-set
  pullPolicy: Always
  pullSecrets:
    - name: refeo-regcred

imagePullSecret:
  create: true
  name: refeo-regcred
  registry: asevo-global.cr.de-fra.ionos.com
  username: <your-username>
  password: <your-password>

service:
  type: ClusterIP
  port: 80
  targetPort: 3000

ingressRoute:
  enabled: true
  host: refeo.dev.asevo.ai
  entryPoints:
    - websecure
  certResolver: letsencrypt
  servicePort: 80 # Target port inside the pod

resources:
  limits:
    cpu: '500m'
    memory: '1024Mi'
  requests:
    cpu: '250m'
    memory: '128Mi'

secretEnvFromFiles:
  - secretName: tf-generated-secrets-asevo
    keys:
      - POSTGRES_URI
      - IONOS_ACCESS_KEY_ID
      - IONOS_SECRET_ACCESS_KEY

externalSecrets:
  enabled: true
  mounts:
    - name: asevo-generated-secret
      secretName: tf-generated-secrets-asevo
      mountPath: /secrets/generated

configMap:
  enabled: true
  name: refeo-config
  data:
    NEXT_PUBLIC_ENVIRONMENT: dev
    PORT: '3000'
    LOG_LEVEL: debug
    IONOS_ENDPOINT_URL: https://s3.eu-central-3.ionoscloud.com
    IONOS_BUCKET_NAME: asevo-dev-test-refeo
    SMTP_HOST: smtp.office365.com
    SMTP_PORT: 587
    SMTP_USER: <EMAIL>
    MAIL_FROM: <EMAIL>
    CONTACT_EMAIL: <EMAIL>
    ADMIN_EMAIL: <EMAIL>
    ALLOWED_DOMAINS: "munichgeneral.com,mv-services.tech,allianz.de,advania.de"
    APP_URL: https://refeo.dev.asevo.ai

secret:
  enabled: true
  name: refeo-secret

migrator:
  # Hooks
  hooks: [pre-install, pre-upgrade]
  hookDeletePolicy: 'before-hook-creation,hook-succeeded'

  image:
    repository: asevo-global.cr.de-fra.ionos.com/refeo-migrator
    tag: not-set
    pullPolicy: IfNotPresent
    pullSecrets:
      - name: refeo-regcred

  command: ['npm', 'run', 'db:deploy']
  args: []

  # Fine-grained inheritance (all default OFF for minimal output)
  inherit:
    configMapEnvFrom: true
    secretEnvFrom: true
    secretEnvFromFiles: true
    externalSecretMounts: true
    resourcesFromApp: true

  backoffLimit: 3
  ttlSecondsAfterFinished: 600
