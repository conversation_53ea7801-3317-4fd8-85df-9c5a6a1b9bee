{"globals": {"save": "Save"}, "greeting": "hello", "common": {"greeting": "hello2", "header_text": "Header Text", "cancel_button_label": "Cancel", "back_button_label": "Back", "yes": "Yes", "no": "No", "close": "Close"}, "header": {"title": "Private Health Insurance", "view_switch_label": "Client View", "language_switcher_tooltip": "Change language"}, "menu": {"button_logout_label": "Logout", "language": "Language", "cases": "Cases", "customers": "Customers", "create_new_case": "Create New Case"}, "words": {"welcome": "Welcome", "case": "Case", "insurance_plans": "Insurance Plans", "data_input": "Data Input", "kilogramm_short": "kg", "centimeter_short": "cm", "currency_sign_euro": "€"}, "footer": {}, "start-cards": {"schnellcheck": {"title": "Quick Check", "body": "Here you can <b>quickly find out</b> whether your client can be insured.", "button": "Start", "button-disabled-placeholder": "Available soon"}, "risiko-anfrage": {"title": "Risk Pre-inquiry", "body": "Here quickly <b>obtain offers, compare</b> and <b>conclude.</b>", "button": "Start", "button-disabled-placeholder": "Available soon"}, "antrag": {"title": "Application", "body": "Everything ready, everything clear? Then <b>directly to the application.</b>", "button": "Start", "button-disabled-placeholder": "Available soon"}}, "components": {"data_input_frame": {"add-new-person": "Add another person", "case": "Case"}, "case_details_view": {"selected_insurance_plan_mix_is": "Selected KV-Care Insurance Plan Mix:", "none_selected": "none", "documents_in_case": "Case Documents", "risk_pre_requests_in_progress": "Ongoing Risk Pre-Inquiries", "you_dont_have_risk_pre_requests_in_progress": "You don't have any ongoing risk pre-inquiries yet", "data_input_in_progress_before_name": "Data input for ", "data_input_in_progress_after_name": " still incomplete", "button_go_to_data_input": "Go to Data Input", "data_input_in_progress": "Data input for this case has not been completed yet"}}, "insurance_plan_mix": {"basic": "Basic", "comfort": "Comfort", "premium": "Premium"}, "form_names": {"previousInsurance": "Previous Insurance", "citizenshipAndProfession": "Origin & Profession"}, "welcome": {"form": {"title": "Confirm Your Data", "labels": {"salutation": "Salutation*", "title": "Academic Title", "firstName": "First Name*", "lastName": "Last Name*", "email": "Email Address*", "phone": "Phone Number*", "street": "Street*", "houseNumber": "House No.*", "postalCode": "ZIP Code*", "city": "City*", "agencyName": "Agency Name*"}, "options": {"mr": "Mr.", "ms": "Ms.", "diverse": "Diverse", "noTitle": "No Title", "dr": "Dr.", "prof": "Prof.", "profDr": "Prof. Dr."}, "termsCheckbox": "Confirm our Terms and Conditions", "submit_button": "Confirm and Continue", "messages": {"success": "Profile updated successfully!", "error": "Error updating profile. Please try again.", "saving": "Saving..."}}, "info": {"title_prefix": "With ", "title_brand": "KV", "title_suffix": "care...", "items": {"item1": {"title": "...ut labore et Dolore", "description": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna erat, sed diam voluptua."}, "item2": {"title": "...magna Aliquam", "description": "Stet clita kasd gube<PERSON>, no sea takimata sanctus est Lorem ipsum dolor sit amet."}, "item3": {"title": "...eirmod tempor invidunt ut labore", "description": "Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. <PERSON><PERSON><PERSON> leo ligula, porttitor eu, consequat vitae, eleifend ac, enim."}}}}, "statistik": {"headline": "Statistics", "card": {"offene-vorgaenge": {"line1": "open cases", "line2": "Premium contribution", "currency-sign": "€", "currency-prefix": "= approx."}, "policierte-vorgaenge": {"line1": "policied cases", "line2": "Premium contribution", "currency-sign": "€", "currency-prefix": "="}}}, "dashboard": {"title": "Dashboard", "statistics": {"open_cases": "Open Cases", "completed_cases": "Completed Cases", "action_required": "Action Required", "total_premium": "Total Premium: €{amount}", "breakdown": {"data_entry": "Data Entry: {count}", "processing": "Processing: {count}", "applications_ready": "Applications Ready: {count}"}}, "recent_cases": {"title": "Recent Cases", "table": {"customer": "Customer", "case_type": "Case Type", "insurances": "Insurances", "premium": "Premium", "status": "Status", "created": "Created", "insurance_count": "{count, plural, one {# Insurance} other {# Insurances}}", "no_cases_found": "No cases found"}}}, "cases": {"overview": {"title": "Customer Cases Overview", "cases_in_progress": "Customer Cases in Progress", "applied_cases": "Applied Customer Cases", "unknown_customer": "Unknown Customer", "case_count": "{count, plural, one {# case} other {# cases}} found", "you_have_no_cases_yet_click_new_case_to_create_one": "You haven't created any cases yet."}, "header": {"overview_button": "Overview"}}, "pages": {"overview": {"navbar-headline": "Overview"}, "cases": {"navbar_headline": "Customer Cases Overview"}}, "modal": {"warning_no_offers": {"headline": "Attention!", "text": "There is a very high probability that no positive insurance offer can be obtained with this information.", "iconId": "SentimentNeutralIcon", "iconColor": "var(--modal-warning-icon-color)"}, "delete_customer": {"headline": "Do you really want to delete this customer?", "dateOfBirth": "Born", "yes_button_label": "Yes", "no_button_label": "Cancel"}}, "communicationModal": {"messageFromAdminPrefix": "Message from KVcare:", "title": {"contractsToSign": "Pending Applications", "followUpRequests": "Current Follow-ups", "allDocuments": "Submitted & Processed"}, "threadType": {"contract": "Application", "followUpRequest": "Follow-up", "upload": "Uploaded"}, "uploadLinkedAttachment": {"contract": "Submit Signed", "followUpRequest": "Upload Completed"}, "downloadButtonTooltip": "Download", "deleteButtonTooltip": "Delete", "button": {"uploadDocument": "Upload Document"}, "error": {"loadingThreads": "Error loading communication threads. Please try again."}}, "widget": {"new-case": {"headline": "Create new case"}}, "data_input_configuration": {"steps": [{"step": "basic_data", "substep": "formArray", "substeps": [{"id": "previousInsurance"}, {"id": "citizenshipAndProfession"}]}, {"step": "insurancePlanSelection"}, {"step": "healthQuestions", "substep": "formSegmentation", "substepform": "healthChecklist"}, {"step": "questionnaires", "substep": "additionalQuestionnaires"}]}, "data_input_configuration2": {"steps": [{"step": "basic_data", "substep": "formArray", "substeps": [{"id": "previousInsurance"}, {"id": "citizenshipAndProfession"}]}, {"step": "insurancePlanSelection"}, {"step": "healthChecklist", "substep": "formSegmentation", "substepform": "healthChecklist"}, {"step": "questionnaires", "substep": "additionalQuestionnaires"}]}, "data_input_complete": {"headline": "All data successfully entered!", "body": "You have entered all the data we need to start a risk pre-inquiry.<br></br><b>Further questions and follow-ups may arise</b>, we will keep you informed.<br></br><br></br>", "warning": "The data cannot be edited afterwards!", "button_start_text": "Start Risk Pre-Inquiry", "or_return_to_data_input": "<b>You can also continue editing the data or add more persons to this risk pre-inquiry.</b>", "button_back_to_data_input_text": "Back to Data Input"}, "form": {"group": "Group", "data_input_complete": "All required data has been successfully entered, you can now complete the data input.", "data_input_blocked": "Data input for this case has been completed, changes are no longer possible.", "data_input_blocked_admin": "All required data has been successfully entered, you can now complete the data input.", "proceed_to_next_CUSTOMER": "Continue to", "proceed_to_next_STEP": "Continue to", "proceed_to_data_input": "Continue to Data Input", "who-will-be-insured": "Who will be insured?", "versicherungsnehmer-ist-zuversichernde-person": "Is the policyholder the same person as the insured person?", "insured-person": "Policyholder", "number_of_days": "Number of Days", "doctor_or_therapist": "Doctor, Therapist", "treatment_type": "Type of Treatment", "basisdaten": {"headline": "Basic Data", "personliche_daten": {"headline": "Personal Data", "stammdaten": {"headline": "Master Data", "anrede": "Salutation", "titel": "Title", "first_name": "First Name", "last_name": "Last Name", "birthdate": "Date of Birth", "birthdate_DD_MM_YYYY": "Date of Birth (DD/MM/YYYY)", "familienstand": "Marital Status", "staatsangehoerigkeit": "Citizenship", "email": "Email", "customerType": "Person is...", "phone_number": "Phone"}, "address": {"city": "City", "house-number": "House Number", "zip-code": "ZIP Code", "street": "Street"}, "steps": {"basic_data": "Basic Data", "personalData": "Personal Data", "previousInsurance": "Previous Insurance", "citizenshipAndProfession": "Origin & Profession", "questionnaires": "Additional Questions", "healthQuestions": "Health Questions", "healthChecklist": "Health Questions", "insurancePlanSelection": "Plan Selection", "test": "☢️☢️☢️ TEST ☢️☢️☢️"}}, "vorversicherungen": {"headline": "Previous Insurance"}, "herkunft": {"headline": "Origin", "staatsangehoerigkeit": "Citizenship"}}, "herkunft": {"country": "Country", "inGermanySinceYear": "In Germany since (YYYY)", "aufenthaltstitel": "Residence Permit", "befristetBis": "Valid until (DD/MM/YYYY)"}, "beruf": {"headline": "Profession", "beruf": "Profession", "sinceDate": "Since (DD/MM/YYYY)", "berufstand": "Professional Status", "numberOfChildren": "Number of Children", "zahnzusatz": "Dental Supplement", "ambulant": "Outpatient", "stationaer": "Inpatient", "bundesland": "Federal State", "seit": "Since (DD/MM/YYYY)"}, "vorversicherung": {"krankenversichherung": {"label": "Health Insurance", "name_provider": "Name PHI / SHI / EEA Service Provider", "begin_year": "Insurance Start (YYYY)", "end_year": "Insurance End (YYYY)", "outstanding_contributions": "Are there outstanding contributions?", "add_new": "Add Health Insurance", "array_element": "Health Insurance"}, "pflegeversicherung": {"label": "Long-term Care Insurance", "name_provider": "Name Long-term Care Insurance", "begin_year": "Insurance Start (YYYY)", "end_year": "Insurance End (YYYY)", "outstanding_contributions": "Are there outstanding contributions?", "add_new": "Add Long-term Care Insurance", "array_element": "Long-term Care Insurance"}, "zusatzversicherung": {"label": "Supplementary Insurance", "insurance_type": "Insurance Type", "name_provider": "Company", "insurance_plan": "Plan", "add_new": "Add Supplementary Insurance", "array_element": "Supplementary Insurance"}, "insurance_types": {"values": {"Krankenzusatzversicherung": "Health Supplementary Insurance", "Krankenhauszusatzversicherung": "Hospital Supplementary Insurance", "Heilpraktiker-Zusatzversicherung": "Alternative Medicine Supplementary Insurance", "Zahnzusatzversicherung": "Dental Supplementary Insurance"}}}, "delete_customer": "Remove person", "previous_insurance": "Previous Insurance", "add_krankenversicherung": "Add Health Insurance", "add_pflegeversicherung": "Add Long-term Care Insurance", "add_zusatzversicherung": "Add Supplementary Insurance", "new_customer": {"headline": "New Customer", "customer_number": "Customer Number - Management Program", "create_new_customer_button": "Create New Customer", "cancel_create_new_customer_button": "Cancel"}, "edit_customer": {"headline": "Update Customer", "customer_number": "Customer Number - Management Program", "edit_customer_button": "Update", "cancel_edit_new_customer_button": "Cancel", "delete_customer_button": "Delete Customer", "do_you_want_to_delete_this_customer": "Do you really want to delete this customer?", "confirm_delete_customer_button": "Delete", "cancel_delete_customer_button": "Cancel"}, "existing_customer": {"headline": "Existing Customer", "proceed_with_this_customer": "Continue with this Customer", "customer_number": "Customer Number"}, "form_array": {"remove_item_button_label_suffix": "remove", "element_delete_warning": "Do you really want to delete this element?", "confirm_delete_item": "Delete", "cancel_delete_item": "Cancel"}, "health_checklist": {"condition": "Condition", "conditions": "Conditions", "add_condition": "Add Another Condition", "medication": "Medication", "medications": "Were medication(s) prescribed?", "medication_name": "Medication", "medication_add": "Add Medication", "medication_use_timespan": "Duration of Use", "dosage": "Dosage", "treatmentType": "Type of Treatment", "provider": "Place of Treatment", "fromDate_label": "Start (DD/MM/YYYY)", "toDate_label": "End (DD/MM/YYYY)", "daysUnableToWork": "How many days were you unable to work?", "recoveredWithoutConsequences": "Recovered without consequences?", "teeth": "<PERSON><PERSON>", "outpatientTreatmentLast5Years": {"headline": "", "question": "Have there been outpatient examinations, operations, medical check-ups and follow-up examinations or treatments by doctors or other healthcare providers in the last 5 years, or are such recommended or intended?"}, "visualImpairment": {"headline": "", "question": "Is there a visual impairment", "leftEyeDiopter": "Diopters Right Eye", "rightEyeDiopter": "Diopters Left Eye", "isLeftEyeImapired": "Right Eye", "isRightEyeImapired": "Left Eye"}, "inpatientTreatmentLast10Years": {"headline": "", "question": "Have there been inpatient examinations, operations, treatments or spa or rehabilitation measures in a hospital, infirmary, sanatorium or nursing home in the last 10 years, or is such recommended or intended?"}, "psychologicalTreatmentLast10Years": {"headline": "", "question": "Have there been psychological, psychotherapeutic or psychiatric treatments, examinations, consultations in the last 10 years, or have such been recommended or carried out, or are such intended?"}, "currentDentalTreatmentOrConsultation": {"headline": "", "question": "Are you currently receiving dental consultation or treatment, or is one recommended?"}, "diagnosedPeriodontitisOrJawMisalignment": {"headline": "", "question": "Has periodontitis or a tooth or jaw misalignment ever been diagnosed?"}, "infertilityOrConsultationLast5Years": {"headline": "", "question": "Is there sterility, infertility, or have consultations or examinations been carried out in the last 5 years due to an unfulfilled desire to have children?"}, "disabilityOrOccupationalIncapacitySymptomsAnomalies": {"headline": "Disability/Symptoms/Anomalies etc."}, "disabilityOrOccupationalIncapacity": {"headline": "", "question": "Is there or has there been a reduction in earning capacity and/or occupational disability, recognized disability, need for care, or has this been applied for or is an application intended?"}, "SymptomsAnomaliesLast3Years": {"question": "Have there been or are there symptoms, anomalies, diseases, misalignments, malformations, functional impairments in the last 3 years, or do you wear body implants (except dental implants) and/or prostheses - even if they were not or are not being treated?"}, "addictionMedicationHiv": {"headline": "Addiction/Medication/HIV"}, "hivInfectionOrTestPending": {"headline": "Addiction/Medication/HIV", "question": "Has an HIV infection ever been detected, is an HIV test recommended, or is a test result pending?"}, "physicalConditionsLast3Years": {"headline": "", "question": "Have there been or are there symptoms, anomalies, diseases, misalignments, malformations, functional impairments in the last 3 years, or do you wear body implants (except dental implants) and/or prostheses - even if they were not or are not being treated?"}, "substanceAbuseLast10Years": {"headline": "", "question": "Is there or has there been alcohol, drug or medication dependency in the last 10 years?"}, "regularMedicationOrDrugUseLast10Years": "", "body_measurements_and_eyesight": "Body Measurements/Visual Impairment", "body_measurements": "Body Measurements", "eyesight": "Is there a visual impairment?", "examinations_operations_treatments": "Examinations/Operations/Treatments", "examinationsOperationsTreatments": "Examinations/Operations/Treatments", "weight": "Weight", "height": "Height", "treatment": {"illness_diagnosis": "Description of the condition (diagnoses or type of symptoms, medications, dosage, type of treatments)", "illness_diagnosis_placeholder": "Enter description etc.", "illness_duration": "How long were you in treatment?", "illness_duration_placeholder": ""}}, "insurancePlanSelection": {"kvcare_insurance_plan_mix": {"headline": "KVcare Insurance Plan Mix", "body": "KVcare compiles <b>the best plans for you based on your preference</b>. <b>You can select a group of plans or specific plans.</b> You are also welcome to combine these.", "cards": {"basic": {"headline": "Basic"}, "comfort": {"headline": "Comfort"}, "premium": {"headline": "Premium"}}, "checkbox_dont_suggest_insurance_plan_group": "Don't suggest plan group"}, "insurance_plan_selection": {"headline": "Select Plans", "body": "You can <b>additionally add specific plans</b> to the KVcare plan mix <b>(Max. 5)</b>.", "insurance_plan_array_item_name": "Plan", "insurance_plan_array_item_add": "Additional Plan", "insurance_company": "Insurance Company", "insurance_plan": "Plan", "insurance_plan_add_button_label": "Add Plan", "hint_please_add_at_least_1_insurance_plan_or_allow_suggestions": "Please add at least one plan, or select at least one plan group above.", "max_insurance_plan_count": 5}, "insurance_plan_delete_modal": {"body": "Delete this plan?", "confirm_delete_button_label": "Yes", "cancel_delete_button_label": "Cancel"}, "sick_pay_and_hostpital_fee": {"headline": "How much should the daily sickness benefit and hospital daily allowance be?", "body": "<b>DIN 77230</b> suggests <b>80% of net income</b> from work for daily sickness benefit to maintain the standard of living.", "input_sick_pay_euro": "Daily Sickness Benefit in €", "input_hospital_fee_euro": "Hospital Daily Allowance in €"}, "deductable": {"headline": "How high should the deductible be?", "input_max_deductable_euro": "Maximum Deductible in €"}}}, "raw": {"geschlecht": ["Mr.", "Mrs.", "Person"], "titel": ["Dr.", "Prof."], "familienstand": ["single", "married", "registered partnership", "widowed", "divorced"], "berufsstand": ["employed", "self-employed", "in training", "not employed/child", "freelance"], "staatsangehoerigkeit": ["German", "Other"]}, "rawKv": {"geschlecht": {"values": {"Herr": "Mr.", "Frau": "Mrs.", "Person": "Person"}, "defaultValue": "Mr."}, "titel": {"values": {"Dr": "Dr.", "Prof": "Prof."}}, "familienstand": {"label": "Marital Status", "values": {"ledig": "single", "verheiratet": "married", "eingetragene Lebensgemeinschft": "registered partnership", "verwitwet": "widowed", "geschieden:": "divorced"}}, "berufsstand": {"label": "Professional Status", "values": {"angestellt": "employed", "selbständig": "self-employed", "in Ausbildung": "in training", "nicht berufstätig/Kind": "not employed/child", "freiberuflich": "freelance", "Beamter": "civil servant", "Student": "student", "Beamtenanwärter": "civil service trainee"}}, "staatsangehoerigkeit": {"values": {"deutsch": "German", "andere:": "Other"}, "defaultValue": "German"}, "aufenthaltstitel": {"values": {"befristet": "Limited", "unbefristet:": "Unlimited"}}, "boolean": {"true": "Yes", "false": "No"}, "bundesland": {"values": {"Baden-Württemberg": "Baden-Württemberg", "Bayern": "Bavaria", "Berlin": "Berlin", "Brandenburg": "Brandenburg", "Bremen": "Bremen", "Hamburg": "Hamburg", "Hessen": "Hesse", "Mecklenburg-Vorpommern": "Mecklenburg-Western Pomerania", "Niedersachsen": "Lower Saxony", "Nordrhein-Westfalen": "North Rhine-Westphalia", "Rheinland-Pfalz": "Rhineland-Palatinate", "Saarland": "Saarland", "Sachsen": "Saxony", "Sachsen-Anhalt": "Saxony-Anhalt", "Schleswig-Holstein": "Schleswig-Holstein", "Thüringen": "Thuringia"}}}, "errors": {"prisma": {"P2002": {"email": "This email address is already in use.", "id": "This ID is already in use."}, "validation": "The entered data is not valid or a system error has occurred, please contact support."}}, "autocomplete": {"profession": {"values": ["(Voluntary) Military Service", "Demolition Worker", "<PERSON><PERSON><PERSON>", "Members of Parliament and Politicians", "Department, Area and Division Managers", "Agricultural Engineers", "Agricultural Technicians", "Performance Artists", "Geriatric Nurses", "Ambulant Traders", "Anatomy Assistants", "Entertainers", "Plant Mechanics", "Antique Dealers", "Lawyers and Notaries", "Pharmacy Assistants", "Pharmacists and Pharmaceutical Scientists", "Unemployed", "Archaeologists", "Architects", "Archivists and Librarians", "Artists", "Doctors (Human Medicine)", "Doctors (Veterinary Medicine)", "Doctors (Dental Medicine)", "Astrologers", "Astronomers", "Opticians", "Auctioneers", "Bone Removers", "Trainees", "Vending Machine Operators", "Automotive Sales and Salespeople", "<PERSON><PERSON>", "Bakery and Confectionery Sales Staff", "Pool Attendants and Assistants", "Banking Professionals", "Basketball Players (Professional)", "Construction Workers and Construction Assistants", "Building Society Professionals", "Architectural Draftsmen and Cartographers", "Civil Servants", "Employees", "Clothing Accessories Manufacturers", "Advisors in Agriculture, Animal Husbandry and Horticulture", "Mining Craftsmen", "Mountain Guides", "Miners and Hewers", "Profession unknown from child conversion", "Career, Educational Advisors and Employment Mediators", "Undertakers", "Concrete Workers and Masons", "Business and Economics Professionals", "Business Organization and Control Specialists", "Operations Technicians", "Sculptors and Carvers", "Biologists", "Biological-Technical Specialists", "Sheet Metal Pressers, Pullers and Punchers", "Lightning Protection Installers", "Floor Layers", "Oil Rig Craftsmen", "Stock and Currency Traders", "Ambassadors", "Fire Protection Specialists", "Brick, Shaped Brick and Concrete Manufacturers", "Mail Carriers and Sorters", "Bookbinders", "Stage, Lighting, Image and Sound Technicians", "Stage Designers and Props Masters", "Federal Volunteer Service", "Office Clerks", "Office Communication Specialists and Secretaries", "Bus Drivers", "Caisson Workers", "Chemical Plant Workers", "Chemical Laboratory Technicians", "Chemical Technicians", "Chemists", "Choir Directors and Conductors", "Controllers", "Croupiers", "Roofers and Scaffolders", "Data Protection Officers", "Data Processing and IT Specialists", "Decorators", "Designers (Textile) and Couturiers", "Disinfectors and Pest Controllers", "Detectives", "Dietitians and Health Advisors", "Diplomats", "Management and Executive Assistants", "Dock Machinists", "Interpreters and Translators", "Wire Formers and Embossers", "Dramatists", "Druggists", "Printers", "Printer Assistants", "Print Template Manufacturers", "E-Sports Players", "Gemstone Processors", "EDP and Software Consultants", "EDP Operators", "Buyers and Dispatchers", "Shooters", "Retail Sales Clerks and Sellers (Clothing, Textiles, Shoes)", "Retail Sales Clerks and Sellers", "Retail Sales Clerks and Sellers (Electronics)", "Retail Sales Clerks and Sellers (Automotive and Motorcycle Accessories)", "Retail Sales Clerks and Sellers (Kiosk and Snack Bar)", "Retail Sales Clerks and Sellers (Food and Beverages)", "Retail Sales Clerks and Sellers (Market)", "Retail Sales Clerks and Sellers (Pets and Pet Supplies)", "Retail Sales Clerks and Sellers (Weapons)", "Railway Operating Assistants (Couplers, Shunters and Track Runners)", "Railway Operating Managers", "Electricians and Electrical Installers", "Electric Machine and Equipment Builders", "Electrical Fitters", "Electronics Technicians", "Parental Leave", "Reception Staff", "Energy and Environmental Consultants", "Energy Machinists", "Waste Disposal and Waste Management Specialists", "Earthmoving and Construction Machine Operators", "Earthmoving Workers", "Earth Drilling Equipment Operators", "Earth, Gravel and Sand Extractors", "Oil and Gas Extractors", "Inventors", "Occupational Therapists", "Nutritional Scientists", "Educators (Institution, Home)", "Screed and Terrazzo Layers", "Event Managers and Event Clerks", "Traffic Controllers and Dispatchers", "Bicycle Messengers", "Driving Instructors and Driving Safety Instructors", "Vehicle Cleaners and Maintainers", "Precision Mechanics", "Furnace, Fireplace and Chimney Builders", "Firefighters", "Branch and Operations Managers", "Finance and Accounting Specialists", "Financial and Asset Advisors (Field Service)", "Financial and Asset Advisors (Internal Service)", "Fishermen", "Fishery Workers", "Butchery Sales Staff", "Tile, Plate and Mosaic Layers", "Florists", "Loggers", "Air Traffic Controllers and Air Traffic Control Staff", "Aircraft Pilots", "Aircraft Mechanics", "Conveyor Machinists", "Forest Administrators", "Foresters", "Photo, Film and Video Laboratory Technicians", "Photographers", "Voluntary Social Year", "Cemetery Workers", "Hairdressers", "Haulage and Taxi Entrepreneurs", "Fleet Managers", "Radio and Television Presenters", "Radio Operators", "Football Players (Professional)", "Galvanizers", "Cloakroom Attendants", "Garden and Landscape Architects", "Gardeners and Tree Care Specialists", "Gas and Water Installers", "Restaurateurs and Innkeepers", "Building Cleaners", "Industrial Designers", "Clergy and Pastoral Workers", "Geologists and Cartographers", "Bailiffs and Enforcement Officers", "Managing Directors and Owners", "Historians", "Health Clerks", "Beverage Manufacturers", "Trade Supervisors and Safety Controllers", "Founders and Molders", "Glass, Ceramic and Porcelain Painters", "Glassblowers (non-mechanical)", "Glaziers", "Glass Manufacturers, Processors and Refiners", "Glass Matting Specialists", "Glass Opticians", "Track Builders", "Gold and Precious Metal Smiths", "Graphic Designers, Communication and Media Designers", "Engravers", "Border Guard Officers", "Border Guard Officers (Flight Service)", "Border Guard Officers (GSG9)", "Border Guard Officers (Dog Handlers)", "Border Guard Officers (Sea Service)", "Border Guard Officers (Explosives Experts)", "Border Guard Officers (Divers)", "Wholesale and Foreign Trade Clerks", "Rubber Manufacturers and Processors", "Casting Cleaners", "Appraisers", "Port Workers", "Harbor Supervisors", "Hand, Foot Care Specialists and Nail Designers", "Handball Players (Professional)", "Sales Representatives", "Housewives and Househusbands", "Caretakers", "Housekeepers and Householders", "Midwives and Birth Assistants", "Special Education Care Workers", "Alternative Practitioners", "Home Workers", "Stokers", "Unskilled Workers", "Wood and Plastic Construction Builders", "Wood Processors", "Wood Processors", "Wood Surface Finishers", "<PERSON> Loggers", "Wood Product Manufacturers", "Hearing Aid Acousticians", "Beekeepers", "Real Estate Specialists and Property Managers", "Industrial and Facade Climbers", "Industrial and Machine Workers", "Industrial Clerks", "Industrial Mechanics", "Influencers", "Computer Scientists", "Engineers (Oil and Gas)", "Engineers", "Engineers (Construction)", "Engineers (Mining, Metallurgy and Foundry)", "Engineers (Electrical)", "Engineers (Manufacturing)", "Engineers (Aerospace)", "Engineers (Mechanical and Vehicle Engineering)", "Engineers (Physics and Chemistry)", "Engineers (Shipbuilding)", "Engineers (Surveying and Cartography)", "Debt Collection Agents (Field Service)", "Debt Collection Agents (Internal Service)", "Interior Architects", "Interior Finishing Workers", "Directors", "Internet Traders", "Insulators and Sealers", "Hunters and Animal Catchers", "Journalists and Editors", "Legal Professionals", "Legal Assistants", "Refrigeration System Builders and Installers", "Camera Operators", "Captains and Ship Masters", "Body and Vehicle Builders", "Ticket Sellers and Controllers", "Cashiers", "Commercial Employees", "No Profession Specified", "Waiters", "Ceramicists and Potters", "Kindergarten Teachers and Child Care Workers", "Church Painters, Monument Conservators and Conservators", "Plumbers and Tinsmiths", "<PERSON><PERSON>", "Confectioners", "Canned and Ready Meal Manufacturers", "Designers", "Construction Mechanics", "Head Slaughterers", "Basket and Wickerwork Manufacturers", "Cosmetics and Makeup Artists", "Motor Vehicle Drivers", "Motor Vehicle and Motorcycle Mechanics", "Crane Operators", "Nurses and Nursing Assistants", "Criminal Officers (Investigation)", "Kitchen Staff", "Cultural Scientists and Ethnologists", "Art Photographers", "Art Dealers and Art Experts", "Plastic Processors", "Art Historians", "Coachmen", "Laboratory Technicians", "Painters", "Warehouse Workers", "Warehouse Managers and Magazine Workers", "Agricultural Workers", "Agricultural Machinery Mechanics", "Landscape Gardeners", "Farmers", "Food and Luxury Food Tasters", "Food Designers", "Food Inspectors", "Leather Manufacturers, Leather and Fur Processors", "Teachers (Vocational School)", "Teachers (Elementary, Secondary, Main, Comprehensive and Special Schools)", "Teachers (High School)", "Teachers (Music, Singing and Art School)", "Teachers (Language School)", "Teachers (Adult Education Center)", "Literature Scholars", "Speech Therapists", "Solderers and Riveters", "Aviation Professionals (Ground Activities)", "Brokers", "Painters", "Marketing Specialists", "Knitwear Manufacturers", "Machine and Container Cleaners", "Mechanical Engineering and Maintenance Professionals", "Machine Setters and Operators", "Machine Operators and Machinists", "Masseurs, Physical Therapists and Physiotherapists", "Mathematicians", "Mechanics", "Mechatronics Engineers", "Medical and Pharmaceutical Technical Assistants and Medical Laboratory Technicians", "Flour and Nutritional Product Manufacturers", "Metal Builders", "Metal Turners, Millers, Grinders, Filers and Planers", "Precision Metal Workers", "Metal Gluers", "Metal Surface Finishers and Enamelers", "Metal Polishers", "Metal Heat Treaters", "Meteorologists", "Butchers, Meat Processors and Slaughterers", "Milk and Fat Processors", "Military, Police and Customs Administration", "Mineral Processors", "Mineralogists", "Miners, Blast Masters and Assistants", "Mission Clergy", "Furniture Movers and Stevedores", "Model Builders (Precision Work)", "Models", "Assemblers and Assembly Workers", "Garbage Collectors and Street Cleaners", "Musicians", "Musical Instrument Makers", "Musicologists", "Natural Scientists", "Non-Working Population", "Emergency Doctors", "Public Order Office Staff", "Orthopedic and Surgery Mechanics", "Educators (Scientists)", "Package Delivery Staff", "Paper Makers and Processors", "Perfume Developers", "Retirees", "Personnel Consultants", "Human Resources Specialists", "Personal Trainers (Business Coaching)", "Bodyguards", "Wig Makers", "Horse Breeders", "Pavers", "Care, Social and Health Economists", "Doormen and Porters", "Pharmaceutical Consultants", "Pharmaceutical Representatives", "Physician Assistants", "Physical-Technical Specialists", "Physicists", "<PERSON>", "<PERSON><PERSON><PERSON>, Construction Managers and Supervisors", "Political Scientists", "Police Officers (Flight Service)", "Police Officers (Dog Handlers)", "Police Officers (Internal Service)", "Police Officers (Sea Service)", "Police Officers (SEK and MEK)", "Police Officers (Explosives Experts)", "Police Officers (Divers)", "Police Executive Officers", "Upholsterers", "Postal Service Specialists", "Press and Advertising Photographers", "Press Officers and Spokespersons", "Product Developers and Managers", "Production and Assembly Line Workers", "Professors, University Teachers and Lecturers", "Professional Athletes", "Programmers", "Project Managers", "Propagandists", "Prostitutes", "Psychologists and Psychotherapists", "Pyrotechnicians", "Quality Inspectors", "Radio and Television Technicians", "Refinery Workers", "Interior Decorators", "Room Cleaners", "Legal Assistants and Notary Clerks", "Legal Officers and Advisors", "Directors and Producers", "Tour Guides", "Travel Specialists", "Religious Studies Scholars", "Retirees", "Reprographers", "Restaurant and Hotel Specialists", "Restorers", "Judges, Prosecutors and District Attorneys", "Pipe, Sewer and Industrial Service Specialists", "Pipeline Builders and Installers", "Shutter and Sunshade Builders and Installers", "Radio and Television Announcers", "Case Workers and Customer Advisors", "Salt Extractors and Processors", "Singers", "Sanitary, Heating, Ventilation Builders and Installers", "Paramedics and Rescue Assistants", "Conductors and Controllers", "Actors", "Show People", "Shift Supervisors", "Rail Vehicle Operators", "Shipbuilding Assistants and Dock Workers", "Ship Deck Crew and Maritime Assistants", "Ship Pilots", "Ship Engineers", "Ship Navigators", "Ship Officers", "Sign and Illuminated Advertising Manufacturers", "Lock Keepers", "Locksmiths", "Blacksmiths", "Jewelry Designers and Modelers", "Tailors, Seamstresses and Embroiderers", "Editors, Retouchers and Retouchers", "<PERSON><PERSON><PERSON> Sweeps", "<PERSON><PERSON>", "Typesetters", "Writers and Publishers", "Scrap and Old Iron Dealers", "Shoemakers", "Students", "School Principals", "School Sports Teachers", "Protection and Security Specialists", "Welders", "Pastoral Assistants and Pastoral Workers", "Hotel and Restaurant Service Staff", "Service Technicians", "Software and Systems Specialists", "Soldiers and Officers", "Social Workers", "Social Educators", "Sociologists", "Shipping and Traffic Specialists", "Speleologists (Cave Researchers)", "Spinners, Winders and Rope Makers", "Sports and Fitness Clerks", "Sports Trainers", "Sports Scientists", "Linguists", "Medical and Doctor Assistants", "Statisticians and Market Researchers", "Stone Processors and Stone Masons", "Stone Extractors and Crushers", "Tax Advisors", "<PERSON><PERSON><PERSON> (Aviation)", "Stewards (Shipping)", "Prison and Correctional Staff", "Road Builders", "Road Patrol Drivers", "Road Maintenance Workers", "Plasterers, Plasterers and Renderers", "Students", "Stuntmen", "Tobacco Product Makers", "Day Care Providers", "Gas Station Staff", "Dancers", "Dancers (Hotel and Restaurant Industry)", "Dancers (Nightclub or Bar)", "Tattoo Artists and Piercers", "Divers", "Dive Supervisors", "Taxi Drivers", "Technicians", "Technicians (Surveying and Cartography)", "Technicians (Construction)", "Technicians (Mining, Metallurgy and Foundry)", "Tech<PERSON><PERSON> (Electrical)", "Technicians (Oil and Gas)", "Technicians (Manufacturing)", "Technicians (Aerospace)", "Technicians (Mechanical and Vehicle Engineering)", "Technicians (Physics and Chemistry)", "Technicians (Shipbuilding)", "Technical Draftsmen", "Telephone Operators", "Test Drivers", "Test Pilots (Hang Gliders, Paragliders, Hang Gliders, Advertising Pilots)", "Textile Cleaners, Maintainers and Processors", "Textile Processors and Finishers", "Bar Staff", "Bar Staff (Nightclub)", "Civil Engineers", "Veterinary Assistants", "Animal Caretakers", "Taxidermists", "Animal Psychologists", "Animal Husbandry and Breeding Specialists", "<PERSON><PERSON>", "Trainees", "Transport Equipment Operators", "Drywall Installers", "Tunnel and Gallery Builders", "Bouncers", "Watchmakers", "Management Consultants", "Event Riggers", "Association Leaders", "Consumer Advisors", "Process Mechanics (Metal Production and Forming)", "Sales Drivers", "Publishing Clerks", "Landlords and Lessors", "Packaging Material Manufacturers", "Shipping Workers and Packers", "Insurance Specialists (Field Service)", "Insurance Specialists (Internal Service)", "Sales Assistants", "Sales Specialists (Field Service)", "Sales Specialists (Internal Service)", "Administrators in Agriculture, Animal Husbandry and Horticulture", "Administrative Specialists", "Administrative Specialists in Higher, Senior and Middle Service", "Executive Boards", "Executive Boards (Corporation)", "Vulcanizers", "Wax Pullers and Modelers", "Guards", "Forest Workers", "Goods and Newspaper Deliverers", "Goods Painters", "Goods Inspectors, Packagers and Sorters", "Water Engineers and Cultural Construction Workers", "Water Supply and Disposal Specialists", "Weavers, Felt and Tufting Product Makers", "Advertising Specialists", "Foremen", "Tool Makers and Mechanics", "Vintners", "Business Consultants and Tax Assistants", "Industrial Engineers", "Auditors", "Scientific Staff", "Dental Assistants", "Dental Technicians", "Draftsmen and Artists", "Newspaper Advertisers", "Tent Masters", "Machining Mechanics and Metal Drillers", "<PERSON><PERSON>", "Room Service Staff", "Civilian Service Workers", "Customs Inspectors", "Zoologists", "Sugar and Confectionery Manufacturers"]}, "private_health_insurance": {"GKV": {"values": ["AOK - The Health Insurance for Lower Saxony", "AOK - The Health Insurance in Hesse", "AOK Baden-Württemberg", "AOK Bavaria - The Health Insurance", "AOK Bremen / Bremerhaven", "AOK Northeast - The Health Insurance", "AOK Northwest - The Health Insurance", "AOK PLUS - The Health Insurance for Saxony and Thuringia", "AOK Rhineland/Hamburg - The Health Insurance", "AOK Rhineland-Palatinate/Saarland - The Health Insurance", "AOK Saxony-Anhalt - The Health Insurance", "Audi BKK", "BAHN-BKK", "BARMER", "BERGISCHE KRANKENKASSE", "Bertelsmann BKK", "Company Health Insurance of G.M.Pfaff AG", "Company Health Insurance EWE", "Company Health Insurance Miele", "Company Health Insurance Mobil", "Company Health Insurance PricewaterhouseCoopers", "Company Health Insurance Technoform", "BIG direkt gesund", "BKK Akzo Nobel Bavaria", "BKK B<PERSON>", "BKK Deutsche Bank AG", "BKK Diakonie", "BKK EUREGIO", "BKK evm", "BKK exklusiv", "BKK Faber-Castell & Partner", "BKK firmus", "BKK <PERSON>", "BKK GILDEMEISTER SEIDENSTICKER", "BKK Groz-Beckert", "BKK Herkules", "BKK Linde", "BKK MAHLE", "bkk melitta hmr", "BKK mkk - my health insurance", "BKK MTU", "BKK Pfalz", "BKK ProVita", "BKK Public", "BKK Rieker.RICOSTA.Weisser", "BKK Salzgitter", "BKK Scheufelen", "BKK Schwarzwald-Baar-Heuberg", "BKK VDN", "BKK VerbundPlus", "BKK Voralb", "BKK HELLER*INDEX*LEUZE", "BKK WERRA-MEISSNER", "<PERSON><PERSON>K BUSINESS AND FINANCE", "BKK_DürkoppAdler", "BKK24", "BKK-Würth", "BMW BKK", "Bosch BKK", "Continentale Company Health Insurance", "DAK-Gesundheit", "Debeka BKK", "energie-Company Health Insurance", "EY Company Health Insurance", "Commercial Health Insurance (hkk)", "Heimat Health Insurance", "HEK - Hanseatic Health Insurance", "IKK - The Innovation Health Insurance", "IKK classic", "IKK gesund plus", "IKK Southwest", "GUILD HEALTH INSURANCE BRANDENBURG AND BERLIN", "KARL MAYER BKK", "Commercial Health Insurance", "Health Insurance Name", "KKH", "KNAPPSCHAFT", "Koenig & Bauer BKK", "Krones Company Health Insurance", "Mercedes-Benz BKK", "Merck BKK", "mhplus Company Health Insurance", "Novitas BKK", "Pronova BKK", "R+V Company Health Insurance", "Salus BKK", "SECURVITA BKK", "Siemens Company Health Insurance (SBK)", "SKD BKK", "Social Insurance for Agriculture, Forestry and Horticulture (SVLFG)", "Südzucker BKK", "Techniker Health Insurance", "TUI BKK", "VIACTIV Health Insurance", "vivida bkk", "WMF BKK", "ZF BKK"]}, "PKV": {"values": ["<PERSON><PERSON><PERSON>", "DKV (Victoria)", "Allianz Private Health Insurance", "Axa (Colonia, DBV-Winterthur)", "Signal Iduna (with Deutscher Ring)", "Central Health Insurance", "Barmenia", "Insurance Chamber Bavaria", "Continentale", "HUK-Coburg", "<PERSON><PERSON>", "HanseMerkur", "<PERSON><PERSON><PERSON>", "State Health Aid LKH", "South German Health Insurance", "Union Health Insurance", "Deutscher Ring", "Inter", "Universa", "Munich Association", "R+V", "Ergo Direct", "ARAG", "LVM", "Old Oldenburg Health Insurance", "Nuremberg", "Württemberg Health Insurance", "PAX Family Care Health Insurance", "HanseMerkur Insurance Group S", "Mannheim Insurance", "Envivas Health Insurance", "DEVK", "Provincial", "Concordia", "Free Medical Fund", "Vigo Health Insurance (Düsseldorf Insurance)", "Mecklenburg", "LIGA Health Insurance for Catholic Priests", "Optician Compensation Fund", "Praenatura", "St. Martinus Health and Death Fund", "Health Support Fund of Hannover Professional Fire Department", "Old Oldenburg", "Others"]}}}, "modal_configurations": {"warning_no_offers": {"headline": "Attention!", "text": "There is a very high probability that no positive insurance offer can be obtained with this information.", "iconId": "SentimentNeutralIcon", "iconColor": "var(--modal-warning-icon-color)"}, "problem": {"headline": "Problem!", "text": "We have a problem!", "iconId": "SentimentVeryDissatisfiedIcon", "iconColor": "var(--error-red)"}, "success": {"headline": "Success!", "text": "Everything successful!", "iconId": "SentimentSatisfiedAltIcon", "iconColor": "var(--teal)"}}, "customer_types": {"policy_holder_only": "Policyholder Only", "policy_holder_and_insured_person": "Policyholder and Insured Person", "insured_primary": "Insured Person", "spouse": "Spouse", "child": "Child"}, "case_relation_types": {"paying": "Policyholder Only", "paying_and_insured_primary": "Policyholder and Insured Person", "insured_primary": "Insured Person", "child": "Spouse", "spouse": "Child"}, "document_list": {"documents_single_or_plural": "{count, plural, one {# Document} other {# Documents}}", "buttons": {"download": "Download", "open": "Open"}}, "document_upload": {"headline_upload_documents": "Upload Documents", "pick_document_button_label": "Select PDF Document", "dropdown_document_type": {"label": "Document Type", "placeholder": "Please select a document type", "options": {"examination_booklet": "Examination Booklet", "medical_report": "Medical Report / Findings Report", "lab_report": "Laboratory Report", "hospital_report": "Hospital Report", "vaccination_card": "Vaccination Card", "application_form": "Application Form", "authorization_consent": "Power of Attorney / Confidentiality Waiver", "rehab_report": "Rehabilitation or Spa Report", "medication_plan": "Medication Plan", "medical_certificate": "Medical Certificate / Medical Statement", "psychological_report": "Psychological Report", "surgery_report": "Surgery Report", "followup_report": "Follow-up Report", "termination_confirmation": "Previous Insurance Cancellation Confirmation", "advisory_protocol": "Advisory Protocol / Pre-contractual Information", "health_self_disclosure": "Health Self-disclosure", "risk_pre_request": "Risk Pre-inquiry", "insurance_policy": "Insurance Policy", "modified_document": "Modified Document", "additional_information": "Additional Information", "other_document": "Other Document"}}, "optional_message": "Optional Message", "optional_message_input_placeholder": "Text", "button_upload_document": "Upload Document", "limit_uploaded_file_types_with_html_accept": ".pdf", "error_upload_failed": "The document could not be uploaded. Please try again.", "error_file_too_large": "The file is too large. Maximum file size is {maxSize}.", "error_invalid_file_type": "Invalid file type. Please select a PDF file."}, "case_status": {"application_ready": "Application Ready", "risk_inquiry_running": "Risk Pre-inquiry Running", "processing": "Processing", "rejected": "Rejected", "data_entry": "Data Entry", "inquiry_running": "Inquiry Running", "applied": "Applied", "processing_requests": "{count, plural, one {# Processing} other {# Processing}}", "applications_ready": "{count, plural, one {# Application Ready} other {# Applications Ready}}"}, "not_found_404_page": {"sorry": "We're sorry!", "page_not_found": "The page was not found", "back_to_start": "Back to Start"}, "form_configurations": {"personalData": [{"id": "customerType", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.customerType", "optionsPath": "customer_types", "disabled": true}, {"element": "subtitle", "subtitleTranslationKey": "form.insured-person"}, {"id": "firstName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.first_name"}, {"id": "lastName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.last_name"}, {"id": "salutation", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.anrede", "optionsPath": "rawKv.geschlecht.values"}, {"id": "academicTitle", "element": "dropdown", "label": "form.basisdaten.personliche_daten.stammdaten.titel", "optionsPath": "rawKv.titel.values"}, {"id": "dateOfBirth", "element": "date", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.birthdate_DD_MM_YYYY"}, {"id": "familienstand", "element": "dropdown", "required": true, "label": "rawKv.familienstand.label", "optionsPath": "rawKv.familienstand.values", "disabled": true}, {"id": "email", "element": "email", "required": false, "label": "form.basisdaten.personliche_daten.stammdaten.email"}], "citizenshipAndProfession": [{"element": "subtitle", "subtitleTranslationKey": "form.basisdaten.herkunft.headline"}, {"id": "citizenship", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.staatsangehoerigkeit", "optionsPath": "rawKv.staatsangehoerigkeit.values", "defaultValue": "rawKv.staatsangehoerigkeit.defaultValue"}, {"id": "country", "showIf": {"citizenship": "andere"}, "element": "country", "required": true, "label": "form.herkunft.country"}, {"id": "inCountrySince", "showIf": {"citizenship": "andere"}, "element": "year", "required": true, "label": "form.herkunft.inGermanySinceYear"}, {"id": "aufenthaltstitel", "showIf": {"citizenship": "andere"}, "element": "dropdown", "required": true, "label": "form.herkunft.aufenthaltstitel", "optionsPath": "rawKv.aufenthaltstitel.values"}, {"id": "aufenthaltstitelBefristetBis", "showIf": {"aufenthaltstitel": "befristet"}, "element": "datefuture", "required": true, "label": "form.herkunft.befristetBis"}], "previousInsurance": [{"element": "subtitle", "label": "form.previous_insurance.headline", "useFullWidthOfParent": true}, {"element": "typography", "label": "form.previous_insurance.if_no_previous_insurance_set_to_none", "useFullWidthOfParent": true}, {"id": "vorversicherungKrankenversicherung", "element": "arrayof", "required": true, "arrayOfForm": "vorversicherungKrankenversicherung", "arrayItemLabel": "form.vorversicherung.krankenversichherung.array_element", "arrayAddLabel": "form.vorversicherung.krankenversichherung.add_new"}, {"element": "divider"}, {"id": "vorversicherungPflegeversicherung", "element": "arrayof", "arrayOfForm": "vorversicherungPflegeversicherung", "arrayItemLabel": "form.vorversicherung.pflegeversicherung.array_element", "arrayAddLabel": "form.vorversicherung.pflegeversicherung.add_new"}, {"element": "divider"}, {"id": "vorversicherungZusatzversicherung", "element": "arrayof", "arrayOfForm": "vorversicherungZusatzversicherung", "arrayItemLabel": "form.vorversicherung.zusatzversicherung.array_element", "arrayAddLabel": "form.vorversicherung.zusatzversicherung.add_new"}], "vorversicherungKrankenversicherung": [{"id": "nameProvider", "element": "autocomplete", "required": true, "label": "form.vorversicherung.krankenversichherung.name_provider", "valuesArraysPaths": ["autocomplete.private_health_insurance.GKV.values", "autocomplete.private_health_insurance.PKV.values"], "autocompleteFreeSolo": true, "useFullWidthOfParent": true}, {"id": "beginYear", "element": "year", "required": true, "label": "form.vorversicherung.krankenversichherung.begin_year"}, {"id": "endYear", "element": "year", "required": true, "label": "form.vorversicherung.krankenversichherung.end_year"}, {"id": "outstanding_contributions", "element": "radio", "required": true, "label": "form.vorversicherung.krankenversichherung.outstanding_contributions", "showModalOnValueClicked": {"value": true, "modalId": "warning_no_offers"}}], "vorversicherungPflegeversicherung": [{"id": "nameProvider", "element": "text", "required": true, "label": "form.vorversicherung.pflegeversicherung.name_provider"}, {"id": "beginYear", "element": "year", "required": true, "label": "form.vorversicherung.pflegeversicherung.begin_year"}, {"id": "endYear", "element": "year", "required": true, "label": "form.vorversicherung.pflegeversicherung.end_year"}, {"id": "outstanding_contributions", "element": "radio", "required": true, "label": "form.vorversicherung.pflegeversicherung.outstanding_contributions", "showModalOnValueClicked": {"value": true, "modalId": "warning_no_offers"}}], "vorversicherungZusatzversicherung": [{"id": "insuranceType", "element": "dropdown", "required": true, "label": "form.vorversicherung.zusatzversicherung.insurance_type", "optionsPath": "form.vorversicherung.insurance_types.values"}, {"id": "providerName", "element": "text", "required": true, "label": "form.vorversicherung.zusatzversicherung.name_provider"}, {"id": "insurancePlan", "element": "text", "required": true, "label": "form.vorversicherung.zusatzversicherung.insurance_plan"}], "medication": [{"id": "name", "element": "text", "required": true, "label": "form.health_checklist.medication_name"}, {"id": "dosage", "element": "text", "required": true, "label": "form.health_checklist.dosage"}], "bodyMassAndEyesight": [{"id": "weight", "element": "text", "required": true, "label": "form.health_checklist.weight"}, {"id": "height", "element": "text", "required": true, "label": "form.health_checklist.height"}, {"id": "hasVisualImpairment", "element": "radio", "required": true, "useFullWidthOfParent": true, "label": "form.health_checklist.visualImpairment.question"}, {"id": "leftEyeImpaired", "element": "radio", "required": true, "showIf": {"hasVisualImpairment": true}, "label": "form.health_checklist.visualImpairment.isLeftEyeImapired"}, {"id": "rightEyeImpaired", "element": "radio", "required": true, "showIf": {"hasVisualImpairment": true}, "label": "form.health_checklist.visualImpairment.isRightEyeImapired"}], "outpatientTreatmentLast5Years": [], "inpatientTreatmentLast10Years": [], "psychologicalTreatmentLast10Years": [], "examinationsOperationsTreatments": [{"id": "outpatientTreatmentLast5Years", "element": "nested", "required": true, "label": "form.health_checklist.outpatientTreatmentLast5Years.question", "nestedForm": "treatment"}, {"id": "inpatientTreatmentLast10Years", "element": "nested", "required": true, "label": "form.health_checklist.inpatientTreatmentLast10Years.question", "nestedForm": "treatment"}, {"id": "psychologicalTreatmentLast10Years", "element": "nested", "required": true, "label": "form.health_checklist.psychologicalTreatmentLast10Years.question", "nestedForm": "treatment"}], "teeth": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.missingTeethCount"}, {"id": "missingTeethCount", "element": "number", "required": true, "label": "form.health_checklist.teeth.missingTeethCount_input_placeholder"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.replacedOrCrownedTeethCount"}, {"id": "replacedOrCrownedTeethCount", "element": "number", "required": true, "label": "form.health_checklist.teeth.replacedOrCrownedTeethCount_input_placeholder"}, {"element": "space"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.oldest_dental_restoration_date"}, {"id": "oldestDentalRestorationDate", "element": "year", "required": true, "label": "form.health_checklist.teeth.oldest_dental_restoration_date_input_placeholder_yyyy"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.currentDentalTreatmentOrConsultation"}, {"id": "currentDentalTreatmentOrConsultation", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.diagnosedPeriodontitisOrJawMisalignment"}, {"id": "diagnosedPeriodontitisOrJawMisalignment", "element": "nested", "required": true, "nestedForm": "treatment"}], "disabilityOrOccupationalIncapacitySymptomsAnomalies": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.disabilityOrOccupationalIncapacity.question"}, {"id": "disabilityOrOccupationalIncapacity", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.physicalConditionsLast3Years.question"}, {"id": "physicalConditionsLast3Years", "element": "nested", "required": true, "nestedForm": "treatment"}], "infertilityOrConsultationLast5Years": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.infertilityOrConsultationLast5Years.question"}, {"id": "infertilityOrConsultationLast5Years", "element": "nested", "required": true, "nestedForm": "treatment"}], "addictionMedicationHiv": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.substanceAbuseLast10Years.question"}, {"id": "substanceAbuseLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.regularMedicationOrDrugUseLast10Years.question"}, {"id": "regularMedicationOrDrugUseLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.hivInfectionOrTestPending.question"}, {"id": "hivInfectionOrTestPending", "element": "nested", "required": true, "nestedForm": "treatment"}], "healthChecklist": [{"id": "bodyMassAndEyesight", "element": "nested", "required": true, "label": "form.health_checklist.body_measurements_and_eyesight", "nestedForm": "bodyMassAndEyesight"}, {"id": "examinationsOperationsTreatments", "element": "nested", "required": true, "label": "form.health_checklist.examinationsOperationsTreatments", "nestedForm": "examinationsOperationsTreatments"}], "treatmentDetails": [{"id": "condition", "element": "text", "required": true, "label": "form.health_checklist.condition"}, {"id": "medication", "element": "arrayof", "label": "form.health_checklist.medication_name", "arrayOfForm": "medication", "arrayAddLabel": "form.health_checklist.medication_add"}, {"id": "fromDate", "element": "date", "required": true, "label": "form.health_checklist.fromDate_label"}, {"id": "toDate", "element": "date", "label": "form.health_checklist.toDate_label"}, {"id": "daysUnableToWork", "element": "number", "required": true, "useFullWidthOfParent": true, "label": "form.health_checklist.daysUnableToWork"}, {"id": "recoveredWithoutConsequences", "element": "radio", "required": true, "label": "form.health_checklist.recoveredWithoutConsequences"}], "treatment": [{"id": "hadTreatment", "element": "radio", "required": true, "useFullWidthOfParent": true}, {"id": "details", "element": "arrayof", "required": true, "label": "form.health_checklist.condition", "arrayOfForm": "treatmentDetails", "showIf": {"hadTreatment": true}, "arrayAddLabel": "form.health_checklist.add_condition"}], "testform": [{"id": "academicTitle", "element": "dropdown", "label": "form.basisdaten.personliche_daten.stammdaten.titel", "optionsPath": "rawKv.titel.values"}], "insurancePlanSelection": [{"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.insurance_plan_selection.headline"}, {"id": "insurancePlanMix", "element": "cardselector", "optionsPath": "card_selector_configutaions.kvcare_insurance_plan_mix"}, {"id": "selectedPolicies", "element": "database", "required": true, "label": "form.insurancePlanSelection.insurance_plan_selection.body", "datasetConditions": [{"dataset": "potentialInsurance", "dataType": "array", "condition": "hasObjectWithKv", "key": "notes", "value": "consultant"}], "requiredIfKv": {"insurancePlanMix": null}, "arrayAddLabel": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan_array_item_add", "arrayItemLabel": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan_array_item_name"}, {"element": "divider"}, {"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.headline"}, {"element": "typography", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.body"}, {"id": "sickPayEuro", "element": "number", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.input_sick_pay_euro"}, {"id": "hospitalFeeEuro", "element": "number", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.input_hospital_fee_euro"}, {"element": "divider"}, {"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.deductable.headline"}, {"id": "deductableEuro", "element": "number", "required": true, "label": "form.insurancePlanSelection.deductable.input_max_deductable_euro"}], "insuranceCompanyAndPlan": [{"id": "insuranceCompany", "element": "autocomplete", "label": "form.insurancePlanSelection.insurance_plan_selection.insurance_company", "valuesArraysPaths": ["autocomplete.private_health_insurance.PKV.values"], "autocompleteFreeSolo": false, "useFullWidthOfParent": false}, {"id": "insurancePolicy", "element": "text", "required": true, "label": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan"}], "basic_data": {"personalData": [{"id": "customerType", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.customerType", "optionsPath": "customer_types"}, {"element": "subtitle", "subtitleTranslationKey": "form.insured-person"}, {"id": "firstName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.first_name"}, {"id": "lastName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.last_name"}, {"id": "salutation", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.anrede", "optionsPath": "rawKv.geschlecht.values"}, {"id": "academicTitle", "element": "dropdown", "label": "form.basisdaten.personliche_daten.stammdaten.titel", "optionsPath": "rawKv.titel.values"}, {"id": "dateOfBirth", "element": "date", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.birthdate_DD_MM_YYYY"}, {"id": "familienstand", "element": "dropdown", "required": true, "label": "rawKv.familienstand.label", "optionsPath": "rawKv.familienstand.values"}, {"id": "email", "element": "email", "required": false, "label": "form.basisdaten.personliche_daten.stammdaten.email"}], "citizenshipAndProfession": [{"element": "subtitle", "subtitleTranslationKey": "form.basisdaten.herkunft.headline"}, {"id": "citizenship", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.staatsangehoerigkeit", "optionsPath": "rawKv.staatsangehoerigkeit.values", "defaultValue": "rawKv.staatsangehoerigkeit.defaultValue"}, {"id": "country", "showIf": {"citizenship": "andere"}, "element": "country", "required": true, "label": "form.herkunft.country"}, {"id": "inCountrySince", "showIf": {"citizenship": "andere"}, "element": "year", "required": true, "label": "form.herkunft.inGermanySinceYear"}, {"id": "aufenthaltstitel", "showIf": {"citizenship": "andere"}, "element": "dropdown", "required": true, "label": "form.herkunft.aufenthaltstitel", "optionsPath": "rawKv.aufenthaltstitel.values"}, {"id": "aufenthaltstitelBefristetBis", "showIf": {"aufenthaltstitel": "befristet"}, "element": "date", "required": true, "label": "form.herkunft.befristetBis"}], "previousInsurance": [{"id": "firstName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.first_name"}, {"id": "lastName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.last_name"}, {"id": "arr", "element": "arrayof", "required": true, "label": "Array Of", "arrayOfForm": "testform"}]}}, "kvcare_private_insurance_provider_list": {"values": ["Allianz", "Alte Oldenburger", "ARAG", "Axa", "Barmenia", "BBKK", "Concordia", "Continentale", "DBV", "DKV", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "HanseMerkur", "Inter", "LKH", "Münchener Verein", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "R+V", "SDK", "Signal Iduna", "Universa", "Württembergische"]}, "card_selector_configutaions": {"kvcare_insurance_plan_mix": {"options": [{"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.basic.headline", "value": "basic", "icon": "SellIcon"}, {"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.comfort.headline", "value": "comfort", "icon": "RecommendIcon"}, {"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.premium.headline", "value": "premium", "icon": "LocalPoliceIcon"}], "none_option_label": "Don't suggest plan group"}}, "imprint": {"title": "Imprint", "subtitle": "Information according to § 5 TMG", "sections": [{"title": "Operator of the Online Service", "paragraphs": ["KVcare GmbH", "Beethovenstraße 43", "60325 Frankfurt am Main"]}, {"title": "Register Entry", "paragraphs": ["Commercial Register: HRB 139613", "Register Court: Frankfurt am Main", "VAT Identification Number: DE456451491"]}, {"title": "Authorized Managing Directors", "paragraphs": ["Dip<PERSON>. <PERSON><PERSON>", "Dipl<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Contact", "paragraphs": ["E-Mail: <EMAIL>"]}, {"title": "Responsible for Content", "paragraphs": ["Dip<PERSON>. <PERSON><PERSON>", "Dipl<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"]}]}, "privacy": {"title": "Privacy Policy", "subtitle": "Information according to GDPR", "sections": [{"title": "I. General", "paragraphs": ["(1) This privacy policy contains information about the processing of your personal data.", "(2) Personal data according to Art. 4 No. 1 GDPR includes all information that can be related to you, such as your first and last name, your email address, your telephone or mobile number, your IP address or other contact details.", "(3) Processing of your personal data according to Art. 4 No. 2 GDPR includes any operation in connection with your personal data, such as collection, recording, storage, disclosure, transmission or deletion of personal data.", "(4) The controller responsible for data processing according to Art. 4 No. 7 GDPR is any natural or legal person or other entity that alone or jointly with others determines the purposes and means of processing personal data."]}, {"title": "II. Information about the Responsible Entity", "paragraphs": ["(1) The responsible entity for the processing of your personal data in this portal according to Art. 4 No. 7 GDPR is", "KVcare GmbH\nBeethovenstr. 43\n60325 Frankfurt", "(2) You can reach our data protection officer by post at the above address with the addition – Data Protection Officer – or by email at: <EMAIL>"]}, {"title": "III. Processing of Your Data When Accessing the Website", "paragraphs": ["(1) When accessing the website, we collect the personal data that your internet browser transmits to our server and which is temporarily stored by us in log files (server log files). The following information is recorded by us: ...", "(2) Processing this data serves the purpose of enabling you to establish an error-free connection to this website...", "(3) The legal basis for the data processing mentioned here results from Art. 6 Para. 1 f) GDPR..."]}, {"title": "IV. <PERSON><PERSON>", "paragraphs": ["(1) During your visit to this website and when using our internet portal, so-called cookies are stored on your computer...", "(2) Text files are stored in your browser and saved in a way that can be assigned to us...", "(3) The processing of this data takes place within the framework of our legitimate interest according to Art. 6 Para. 1 f) GDPR..."]}, {"title": "V. Data Security", "paragraphs": ["(1) We only process personal data to the extent that this is possible in accordance with data protection regulations...", "(2) Our security procedures are regularly reviewed and adapted to technological progress..."]}, {"title": "VI. Your Rights under the General Data Protection Regulation", "paragraphs": ["(1) Regarding the processing of your personal data, you have the following rights according to the GDPR regulations: ...", "(2) Your right to revoke: You have the right to revoke consent once given ...", "(3) Your right to object: We process certain data within the framework of a balancing of interests ...", "(4) You have the option to file a complaint with our data protection officer or with a data protection supervisory authority."]}]}, "terms": {"title": "Terms of Use", "subtitle": "Please read the following terms carefully", "content": ["By using this website, you agree to the following terms.", "1. Use of content for private purposes only.", "2. No distribution without written consent.", "3. Changes to the terms are possible at any time."]}, "cookieConsent": {"title": "We Use Cookies", "description": "We use cookies to operate the website and improve the experience. You can accept all cookies, reject non-essential ones, or configure preferences.", "acceptAll": "Accept All", "reject": "Only Necessary", "configure": "Settings", "configureTitle": "<PERSON><PERSON>", "configureDescription": "Choose which cookies you want to allow. You can change your decision at any time.", "save": "Save", "categories": {"essential": "Essential Cookies", "essentialDesc": "These cookies are required for the basic functions of the website (e.g. login). They are always set.", "functional": "Functional Cookies", "functionalDesc": "Enable additional functions (e.g. settings, form behavior).", "analytics": "Analytics", "analyticsDesc": "Helps us understand and improve the use of the website.", "marketing": "Marketing", "marketingDesc": "Advertising and tracking cookies that enable personalized advertising."}}, "quickCheck": {"title": "Risk Check for Comprehensive Health Insurance", "description": "The risk check provides you with a quick and uncomplicated rough assessment and orientation regarding the acceptability of the risk. For this purpose, individual information is sufficient; it is not necessary to fill out all fields.", "submitButton": "Show Result", "successMessage": "Your request has been successfully submitted", "result": {"title": "Your Risk Check Results", "subtitle": "Here is a summary of your information", "bmiSection": "Body Mass Index (BMI)", "dentalSection": "Dental Health", "heightWeight": "Height: {height} cm, Weight: {weight} kg", "footerNote": "These results are for informational purposes only. For a binding risk assessment, please contact our customer service."}, "bmi": {"underweight": "Underweight", "normal": "Normal Weight", "overweight": "Overweight", "obese": "Obesity"}, "sections": {"previousInsurance": {"question": "Does the change take place directly following the previous insurance?"}, "profession": {"question": "Select the current occupation of the person to be insured from the list below:", "label": "Profession"}, "bmi": {"description": "From an entry age of 16 years, the BMI of the person to be insured can be checked below:", "heightLabel": "Height", "weightLabel": "Weight"}, "diagnosis": {"addButton": "Add diagnosis ({current}/{max})"}, "outpatientDiagnosis": {"question": "Has there been an outpatient diagnosis in the last 3 years?", "diagnosisLabel": "{number}. Enter diagnosis"}, "inpatientDiagnosis": {"question": "Has there been an inpatient diagnosis in the last 10 years (up to entry age 32 the last 5 years)?", "diagnosisLabel": "{number}. Enter diagnosis"}, "missingTeeth": {"question": "Are there any missing teeth (except wisdom teeth or in case of gap closure) that have not yet been replaced?", "label": "Number of missing teeth"}, "dentalReplacements": {"question": "Dental replacements (replaced or crowned teeth, including implants, bridges, crowns, partial crowns and dentures) present?", "label": "Number of replaced teeth", "helperText": "For bridges, all affected teeth must be counted individually, including anchor or abutment teeth."}}, "validation": {"errorSummaryTitle": "Please correct the following errors", "selectOption": "Please select an option", "diagnosisRequired": "Diagnosis must not be empty", "maxDiagnoses": "Maximum 5 diagnoses allowed", "professionRequired": "Please select a profession", "heightMustBeNumber": "Height must be a number", "heightMustBeInteger": "Height must be an integer", "heightMin": "Height must be at least 50 cm", "heightMax": "Height may be at most 250 cm", "weightMustBeNumber": "Weight must be a number", "weightMustBeInteger": "Weight must be an integer", "weightMin": "Weight must be at least 20 kg", "weightMax": "Weight may be at most 300 kg", "teethMustBeNumber": "Number must be a number", "teethMustBeInteger": "Number must be an integer", "teethMin": "Number cannot be negative", "teethMax": "Maximum 32 teeth possible", "dentalMustBeNumber": "Number must be a number", "dentalMustBeInteger": "Number must be an integer", "dentalMin": "Number cannot be negative", "dentalMax": "Maximum 32 teeth possible", "outpatientDiagnosisRequired": "Please provide at least one outpatient diagnosis or select \"No\"", "inpatientDiagnosisRequired": "Please provide at least one inpatient diagnosis or select \"No\""}, "fields": {"previousInsurance": "Previous Insurance", "profession": "Profession", "height": "Height", "weight": "Weight", "outpatientDiagnosis": "Outpatient Diagnosis", "outpatientDiagnoses": "Outpatient Diagnoses", "inpatientDiagnosis": "Inpatient Diagnosis", "inpatientDiagnoses": "Inpatient Diagnoses", "missingTeeth": "Missing <PERSON><PERSON>", "dentalReplacements": "Dental Replacements"}, "professions": {"employee": "Employee", "civil_servant": "Civil Servant", "self_employed": "Self-employed", "student": "Student", "retiree": "<PERSON><PERSON><PERSON>", "unemployed": "Unemployed", "homemaker": "Homemaker", "craftsman": "Craftsman", "construction_worker": "Construction Worker", "doctor": "Doctor", "teacher": "Teacher", "engineer": "Engineer", "merchant": "Merchant", "other": "Other"}}}