{"globals": {"save": "Speichern"}, "greeting": "hellow", "common": {"greeting": "hellow2", "header_text": "Header Text", "cancel_button_label": "Abbrechen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "close": "Schließen", "back_button_label": "Zurück", "euro_per_day": "€ pro Tag"}, "header": {"title": "Private Krankenversicherung", "view_switch_label": "Klientenansicht", "language_switcher_tooltip": "Sprache ändern"}, "menu": {"button_logout_label": "Abmelden", "language": "<PERSON><PERSON><PERSON>", "cases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customers": "<PERSON><PERSON>", "create_new_case": "Neuen Vorgang erstellen"}, "words": {"welcome": "<PERSON><PERSON><PERSON><PERSON>", "case": "Vorgang", "insurance_plans": "<PERSON><PERSON><PERSON>", "data_input": "<PERSON><PERSON><PERSON><PERSON>", "kilogramm_short": "kg", "centimeter_short": "cm", "currency_sign_euro": "€", "percent": "%"}, "values": {"zero": 0}, "footer": {}, "start-cards": {"schnellcheck": {"title": "Schnellcheck", "body": "Hier können Si<PERSON> eine bestimmte Erkrankung auf Versicherbarkeit überprüfen.", "button": "Starten", "button-disabled-placeholder": "<PERSON>ld verfügbar"}, "risiko-anfrage": {"title": "Risiko-Voranfrage", "body": "Hier können Sie bei mehreren Versicherern gleichzeit prüfen, ob Ihr Kunde versicherbar ist.", "button": "Starten", "button-disabled-placeholder": "<PERSON>ld verfügbar"}, "antrag": {"title": "<PERSON><PERSON><PERSON>", "body": "Hier können Sie einen verbindlichen Antrag für eine Krankenvollversicherung stellen.-", "button": "Starten", "button-disabled-placeholder": "<PERSON>ld verfügbar"}}, "components": {"data_input_frame": {"add-new-person": "Weitere Person hinzufügen", "case": "Vorgang"}, "case_details_view": {"selected_insurance_plan_mix_is": "Gewählter KV-Care Tarif-Mix:", "none_selected": "keins", "documents_in_case": "Vorgangsdokumente", "risk_pre_requests_in_progress": "Laufende Risikovoranfragen", "you_dont_have_risk_pre_requests_in_progress": "Sie haben noch keine Laufende Risikovoranfragen", "data_input_in_progress_before_name": "Dateneingabe für ", "data_input_in_progress_after_name": " noch unvollständig", "button_go_to_data_input": "<PERSON><PERSON>"}}, "insurance_plan_mix": {"basic": "<PERSON><PERSON>", "standard": "Standard", "premium": "Premium"}, "form_names": {"previousInsurance": "Vorversicherung", "citizenshipAndProfession": "Staatsangehoerigkeit & Beruf"}, "welcome": {"form": {"title": "Bestätigen Sie Ihre Daten", "labels": {"salutation": "Anred<PERSON>*", "title": "Akademischer Titel", "firstName": "Vorname*", "lastName": "Nachname*", "email": "Email-<PERSON><PERSON><PERSON>*", "phone": "Telefonnummer", "street": "Straße", "houseNumber": "Hausnr.", "postalCode": "PLZ", "city": "Ort", "agencyName": "<PERSON><PERSON><PERSON>"}, "options": {"mr": "<PERSON>", "ms": "<PERSON><PERSON>", "diverse": "Divers", "noTitle": "<PERSON><PERSON>", "dr": "Dr.", "prof": "Prof.", "profDr": "Prof. Dr."}, "termsCheckbox": "Hiermit akzeptiere ich die Nutzungsbedingungen von KVcare", "submit_button": "Bestätigen und Weiter", "messages": {"success": "Profil wurde erfolgreich aktualisiert!", "error": "Fehler beim Aktualisieren des Profils. Bitte versuchen Sie es erneut.", "saving": "Wird gespeichert..."}}, "info": {"title_prefix": "<PERSON> bi<PERSON>t ", "title_brand": "KV", "title_suffix": "care:", "items": {"item1": {"title": "Einfache Risikivoranfrageprozesse", "description": "Einmal ausfüllen und verschiedene Versicherungen anfragen. Und natürlich prüfen wir Ihre Risikovoranfrage auch auf Vollständigkeit und Plausibilität."}, "item2": {"title": "Starke KI und echte PKV-Experten", "description": "Schlaue Technologie ersetzt unübersichtliche PDFs mit viel zu wenig Platz. Wir reduzieren Nachbearbeitungen auf ein Minimum und unsere Experten steuern die gesamte Kommunikation mit den Versicherern."}, "item3": {"title": "Statusupdates ohne viel E-Mail-Ping-Pong", "description": "Unser maklerfreundliches Portal liefert Ihnen täglich Statusupdates – an einem Ort, übersichtlich und strukturiert."}}}}, "statistik": {"headline": "Statistik", "card": {"offene-vorgaenge": {"line1": "<PERSON><PERSON>", "line2": "Beitragsprämie", "currency-sign": "€", "currency-prefix": "= ca."}, "policierte-vorgaenge": {"line1": "policierte Vorgänge", "line2": "Beitragsprämie", "currency-sign": "€", "currency-prefix": "="}}}, "dashboard": {"title": "Dashboard", "statistics": {"open_cases": "<PERSON><PERSON> Vorgänge", "completed_cases": "Abgeschlossene Vorgänge", "action_required": "Aktion erford<PERSON>lich", "total_premium": "Gesamtprämie: €{amount}", "breakdown": {"data_entry": "Dateneingabe: {count}", "processing": "Nachbearbeitungen: {count}", "applications_ready": "Anträge bereit: {count}"}}, "recent_cases": {"title": "Aktuelle Vorgänge", "table": {"customer": "Kunde", "case_type": "Vorgangstyp", "insurances": "Versicherungen", "premium": "Prämie", "status": "Status", "created": "<PERSON><PERSON><PERSON><PERSON>", "insurance_count": "{count, plural, one {# Versicherung} other {# Versicherungen}}", "no_cases_found": "<PERSON><PERSON>änge gefunden"}}}, "cases": {"overview": {"title": "Übersicht Kundenvorgänge", "cases_in_progress": "Kundenvorgänge in Arbeit", "applied_cases": "beantragte Kundenvorgänge", "unknown_customer": "Unbekannter Kunde", "case_count": "{count, plural, one {# Vorgang} other {# Vorgänge}} gefunden", "you_have_no_cases_yet_click_new_case_to_create_one": "Sie haben noch keine Vorgänge erstellt."}, "header": {"overview_button": "Übersicht"}}, "pages": {"overview": {"navbar-headline": "Übersicht"}, "cases": {"navbar_headline": "Übersicht Kundenvorgänge"}}, "modal": {"warning_no_offers": {"headline": "Achtung!", "text": "Es besteht eine sehr hohe Wahrscheinlichkeit, dass mit dieser Angabe kein positives Versicherungsangebot eingeholt werden kann.", "iconId": "SentimentNeutralIcon", "iconColor": "var(--modal-warning-icon-color)"}, "delete_customer": {"headline": "Möchten Sie diesen Kunden wirklich löschen?", "dateOfBirth": "<PERSON><PERSON><PERSON><PERSON>", "yes_button_label": "<PERSON>a", "no_button_label": "Abbrechen"}}, "communicationModal": {"messageFromAdminPrefix": "<PERSON><PERSON><PERSON><PERSON> von K<PERSON>care:", "title": {"contractsToSign": "Bereitstehende Anträge", "followUpRequests": "Aktuelle Nachbearbeitungen", "allDocuments": "Eingereicht & bearbeitet"}, "threadType": {"contract": "<PERSON><PERSON><PERSON>", "followUpRequest": "Nachbearbeitung", "upload": "Hochgeladen"}, "uploadLinkedAttachment": {"contract": "Unterschrieben einreichen", "followUpRequest": "Ausgefüllt hochladen"}, "downloadButtonTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteButtonTooltip": "Löschen", "button": {"uploadDocument": "Dokument hochladen"}, "error": {"loadingThreads": "Fehler beim Laden der Kommunikationsthreads. Bitte versuchen Sie es erneut."}}, "widget": {"new-case": {"headline": "Neuen Vorgang anlegen"}}, "data_input_configuration": {"steps": [{"step": "basic_data", "substep": "formArray", "substeps": [{"id": "previousInsurance"}, {"id": "citizenshipAndProfession"}]}, {"step": "insurancePlanSelection"}, {"step": "healthChecklist", "substep": "formSegmentation", "substepform": "healthChecklist"}]}, "data_input_configuration2": {"steps": [{"step": "basic_data", "substep": "formArray", "substeps": [{"id": "previousInsurance"}, {"id": "citizenshipAndProfession"}]}, {"step": "insurancePlanSelection"}, {"step": "healthChecklist", "substep": "formSegmentation", "substepform": "healthChecklist"}, {"step": "questionnaires", "substep": "additionalQuestionnaires"}]}, "data_input_complete": {"headline": "Alle Daten erfolgreich eigegeben!", "body": "Sie haben alle Daten eingegeben, die wir benötigen um eine Risiko-Voranfrage zu starten.<br></br><b>Es können noch Nachfragen und Nachbearbeitungen entstehen</b>, über die halten wir sie auf dem Laufenden.<br></br><br></br>", "warning": "Die Daten können danach nicht mehr bearbeitet werden!", "button_start_text": "Risiko-Voranfrage starten", "or_return_to_data_input": "<b><PERSON><PERSON> können aber auch die Daten weiter bearbeiten, oder weitere Personen zu dieser Risiko-Voranfrage hinzufügen.</b>", "button_back_to_data_input_text": "Zurück zur Dateneingabe"}, "form": {"group": "Gruppe", "data_input_complete": "<PERSON>e erforderichen Daten wurden erfolgreich eingegeben, <PERSON><PERSON> können die Dateneingabe nun abschließen.", "data_input_blocked": "Dateneingebe für diesen Vorgang wurde abgeschlossen, Änderungen sind nicht mehr möglich.", "data_input_blocked_admin": "<PERSON>e erforderichen Daten wurden erfolgreich eingegeben, <PERSON><PERSON> können die Dateneingabe nun abschließen.", "proceed_to_next_CUSTOMER": "<PERSON><PERSON> zu", "proceed_to_next_STEP": "<PERSON><PERSON> zu", "proceed_to_data_input": "<PERSON><PERSON>inga<PERSON>", "who-will-be-insured": "Wer wird versichert?", "versicherungsnehmer-ist-zuversichernde-person": "Ist der Versicherungsnehmer*in gleizeitig die zu versichernde Person?", "insured-person": "Versicherungsnehmer*in", "number_of_days": "Anzahl der Tage", "doctor_or_therapist": "Arzt, Therapeut", "treatment_type": "Art der Behandlung", "basisdaten": {"headline": "Basisdaten", "personliche_daten": {"headline": "Persönliche Daten", "stammdaten": {"headline": "Stammdaten", "anrede": "<PERSON><PERSON><PERSON>", "titel": "Titel", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "birthdate": "Geburtsdatum", "birthdate_DD_MM_YYYY": "Geburtsdatum (TT/MM/JJJJ)", "familienstand": "Familienstand", "staatsangehoerigkeit": "Staatsangehoerigkeit", "email": "Email", "customerType": "Person ist...", "phone_number": "Telefon"}, "address": {"city": "Stadt", "house-number": "Hausnummer", "zip-code": "PLZ", "street": "Strasse"}, "steps": {"basic_data": "Basisdaten", "personalData": "Persönliche Daten", "previousInsurance": "Vorversicherung", "citizenshipAndProfession": "Staatsangehoerigkeit & Beruf", "questionnaires": "Zusatzfragen", "healthQuestions": "Gesundheitsfragen", "healthChecklist": "Gesundheitsfragen", "insurancePlanSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test": "☢️☢️☢️ TEST ☢️☢️☢️"}}, "vorversicherungen": {"headline": "Vorversicherungen"}, "herkunft": {"headline": "Herkunft", "staatsangehoerigkeit": "Staatsangehoerigkeit"}}, "herkunft": {"country": "Land", "inGermanySinceYear": "In Deutschland seit (JJJJ)", "aufenthaltstitel": "Aufenthaltstitel", "befristetBis": "Befristet bis (TT/MM/JJJJ)"}, "beruf": {"headline": "<PERSON><PERSON><PERSON>", "beruf": "<PERSON><PERSON><PERSON>", "sinceDate": "Seit (TT/MM/JJJJ)", "berufstand": "Berufstand", "numberOfChildren": "<PERSON><PERSON><PERSON>", "employerContributonDentalPercent": "Zahnzusatz", "employerContributonOutpatientPercent": "Ambulant", "employerContributonInpatientPercent": "Stationär", "bundesland": "Bundesland", "seit": "Seit (TT/MM/JJJJ)", "governmentEmployerType": "<PERSON><PERSON><PERSON><PERSON>"}, "vorversicherung": {"krankenversichherung": {"label": "Krankenversicherung", "name_provider": "Name PKV / GKV / EWR-Dienstleister", "begin_year": "Versicherungsbeginn (Monat Jahr)", "end_year": "Versicherungsende (Monat Jahr)", "outstanding_contributions": "Bestehen Beitragsrückstände?", "add_new": "Krankenversicherung hinzufügen", "array_element": "Krankenversicherung"}, "pflegeversicherung": {"label": "Pflegeversicherung", "name_provider": "Name Pflegeversicherung", "begin_year": "Versicherungsbeginn (Monat Jahr)", "end_year": "Versicherungsende (Monat Jahr)", "outstanding_contributions": "Bestehen Beitragsrückstände?", "add_new": "Pflegeversicherung hinzufügen", "array_element": "Pflegeversicherung"}, "zusatzversicherung": {"label": "Zusatzversicherung", "insurance_type": "Versicherungsart", "name_provider": "Gesellschaft", "insurance_plan": "<PERSON><PERSON><PERSON>", "add_new": "Zusatzversicherung hinzufügen", "array_element": "Zusatzversicherung"}, "insurance_types": {"values": {"Krankenzusatzversicherung": "Krankenzusatzversicherung", "Krankenhauszusatzversicherung": "Krankenhauszusatzversicherung", "Heilpraktiker-Zusatzversicherung": "Heilpraktiker-Zusatzversicherung", "Zahnzusatzversicherung": "Zahnzusatzversicherung"}}}, "delete_customer": "Person entfernen", "previous_insurance": {"headline": "Vorversicherung", "if_no_previous_insurance_set_to_none": "<b><PERSON> keine Vorversicherung</b>, bitte tragen sie <b>\"<PERSON><PERSON>\"</b> ein.", "array_element": "Krankenversicherung"}, "add_krankenversicherung": "Weitere Krankenversicherung", "add_pflegeversicherung": "Weitere Pflegeversicherung", "add_zusatzversicherung": "Weitere Zusatzversicherung", "new_customer": {"headline": "Neuer Kunde", "customer_number": "Kundennummer - Verwaltungsprogramm", "create_new_customer_button": "Neuen Kunden anlegen", "cancel_create_new_customer_button": "Abbrechen"}, "edit_customer": {"headline": "Kunden aktualisieren", "customer_number": "Kundennummer - Verwaltungsprogramm", "edit_customer_button": "Aktualisieren", "cancel_edit_new_customer_button": "Abbrechen", "delete_customer_button": "Kunden löschen", "do_you_want_to_delete_this_customer": "Diesen Kunden wirklich löschen?", "confirm_delete_customer_button": "Löschen", "cancel_delete_customer_button": "Abbrechen"}, "existing_customer": {"headline": "Vorhandener Kunde", "proceed_with_this_customer": "<PERSON><PERSON> mit diesem <PERSON>", "customer_number": "Kundennummer"}, "form_array": {"remove_item_button_label_suffix": "entfernen", "element_delete_warning": "Möchten Sie dieses Element wirklich löschen?", "confirm_delete_item": "Löschen", "cancel_delete_item": "Abbrechen"}, "health_checklist": {"condition": "Erkrankung", "conditions": "Erkrankungen", "add_condition": "Weitere Erkrankung hinzufügen", "medication": "Medikament", "medications": "Wurden Medikament(e) verschieben?", "medication_name": "Medikament", "medication_add": "Medikament hinzufügen", "medication_use_timespan": "<PERSON>uer der Einnahme", "dosage": "<PERSON><PERSON><PERSON>", "treatmentType": "Art der Behandlung", "provider": "Ort der Behandlung", "fromDate_label": "Beginn (TT/MM/JJJJ)", "toDate_label": "Ende (TT/MM/JJJJ)", "daysUnableToWork": "Wie viele Tage waren Sie arbeitsunfähig?", "recoveredWithoutConsequences": "Folgenfrei ausgeheilt?", "teeth": {"headline": "<PERSON><PERSON><PERSON><PERSON>", "missingTeethCount": "Wie viele Zähne - außer fehlenden Weisheitszähnen und vollständigem Lückenschluss - fehlen und sind noch nicht ersetzt worden?", "missingTeethCount_input_placeholder": "<PERSON><PERSON><PERSON>", "replacedOrCrownedTeethCount": "Wieviel Zähne wurden ersetzt oder überkront?", "replacedOrCrownedTeethCount_input_placeholder": "<PERSON><PERSON><PERSON>", "oldest_dental_restoration_date": "Wann wurde der äteste Zahnersatz vorgenommen (inkl. Überkronung)?", "oldest_dental_restoration_date_input_placeholder_yyyy": "JJJJ", "currentDentalTreatmentOrConsultation": "Befinden Sie sich in einer zahnärztlichen Beratung oder Behandlung bzw. ist eine angeraten?", "diagnosedPeriodontitisOrJawMisalignment": "<PERSON>rde jemals eine Paradontose oder eine Zahn- bzw. Kieferfehlstellung diagnostiziert?"}, "outpatientTreatmentLast5Years": {"headline": "", "question": "Fanden in den letzten 5 Jahren ambulante Untersuchungen, Operationen, medizinische Kontroll- und Nachsorgeuntersuchungen oder Behandlungen von Ärzten oder anderen Leistungserbringern im Gesundheitswesen statt oder sind solche angeraten oder beabsichtigt?"}, "visualImpairment": {"headline": "", "question": "Besteht eine Fehlsichtigkeit", "leftEyeDiopter": "Diop<PERSON><PERSON> rechtes Auge", "rightEyeDiopter": "Dioptrien linkes Auge"}, "inpatientTreatmentLast10Years": {"headline": "", "question": "<PERSON>den in den letzten 10 Jahren stationäre Untersuchungen, Operationen, Behandlungen oder Kur- oder Rehamaßnahmen in einem Krankenhaus, Lazarett, Sanatorium oder einer Heilanstalt statt oder ist eine solche angeraten oder beabsichtigt?"}, "psychologicalTreatmentLast10Years": {"headline": "", "question": "<PERSON>den in den letzten 10 Jahren psychologische, psychotherapeutische oder psychiatrische Behandlungen, Untersuchungen, Beratungen  statt oder sind solche  angeraten oder durchgeführt worden, bzw. sind solche beabsichtigt?"}, "currentDentalTreatmentOrConsultation": {"headline": "", "question": "Befinden Sie sich in einer zahnärztlichen Beratung oder Behandlung bzw. ist eine angeraten?"}, "diagnosedPeriodontitisOrJawMisalignment": {"headline": "", "question": "<PERSON>rde jemals eine Paradontose oder eine Zahn- bzw. Kieferfehlstellung diagnostiziert?"}, "infertilityOrConsultationLast5Years": {"headline": "Sterilität, Infertilität", "question": "Besteht eine Sterilität, Infertilität oder wurden in den letzten 5 Jahren Beratungen, Untersuchungen aufgrund eines unerfüllten Kinderwunsches durchgeführt?"}, "disabilityOrOccupationalIncapacitySymptomsAnomalies": {"headline": "Erwerbsminderung/Beschwerden/Anomalien etc."}, "disabilityOrOccupationalIncapacity": {"headline": "", "question": "Besteht oder bestand eine Erwerbsminderung und/oder eine Erwerbs-/Berufsunfähigkeit, anerkannte Behinderung, Pflegebedürftigkeit oder wurde diese beantragt oder ist eine Beantragung beabsichtigt?"}, "SymptomsAnomaliesLast3Years": {"question": "Bestanden in den letzten 3 Jahren oder bestehen Beschwerden, Anomalien, Krankheiten, Fehlstellungen, Fehlbildungen, Funktionsbeeinträchtigungen oder tragen sie Körperimplantate (außer Zahnimplantate) und/ oder Prothesen - auch wenn sie nicht behandelt wurden bzw. werden?"}, "addictionMedicationHiv": {"headline": "Sucht/Medikamente/HIV"}, "hivInfectionOrTestPending": {"headline": "Sucht/Medikamente/HIV", "question": "<PERSON><PERSON> jemals eine HIV-Infektion festgestellt, ist ein HIV-Test angeraten oder steht ein Testergebnis aus?"}, "physicalConditionsLast3Years": {"headline": "", "question": "Bestanden in den letzten 3 Jahren oder bestehen Beschwerden, Anomalien, Krankheiten, Fehlstellungen, Fehlbildungen, Funktionsbeeinträchtigungen oder tragen sie Körperimplantate (außer Zahnimplantate) und/ oder Prothesen - auch wenn sie nicht behandelt wurden bzw. werden?"}, "substanceAbuseLast10Years": {"headline": "", "question": "<PERSON><PERSON>t oder bestand in den letzten 10 Jahren eine Alkohol-, Drogen- oder Medikamentenabhängigkeit?"}, "regularMedicationOrDrugUseLast10Years": {"headline": "", "question": "<PERSON><PERSON> in den letzten 10 Jahren regelmäßig Medikamente oder Betäubungsmittel/Drogen eingenommen?"}, "body_measurements_and_eyesight": "Körpermaße/Fehlsichtigkeit", "body_measurements": "Körpermaße", "eyesight": "<PERSON>eht eine Fehlsichtigkeit?", "examinations_operations_treatments": "Untersuchungen/Operationen/Behandlungen", "examinationsOperationsTreatments": "Untersuchungen/Operationen/Behandlungen", "weight": "Gewicht", "height": "Größe", "treatment": {"illness_diagnosis": "Bezeichnung der Erkrankung (Diagnosen bzw. Art der Beschwerden, Medikamente, Dosierung, Art der Behandlungen)", "illness_diagnosis_placeholder": "Bezeichnung usw. eingeben", "illness_duration": "Wie lange waren Sie in Behandlung?", "illness_duration_placeholder": ""}}, "insurancePlanSelection": {"kvcare_insurance_plan_mix": {"headline": "KVcare-Tarif-Mix", "body": "<PERSON><PERSON><PERSON> stellt, <b>b<PERSON>ere<PERSON> auf Ihrer Präferenz, für Sie die besten Tarife</b> zu<PERSON>mme<PERSON>. <b><PERSON>e können eine Gruppe von Tarifen oder bestimmte Tarife auswählen.</b> Gerne können Sie dies auch kombinieren.", "cards": {"basic": {"headline": "<PERSON><PERSON>"}, "standard": {"headline": "Standard"}, "premium": {"headline": "Premium"}}, "checkbox_dont_suggest_insurance_plan_group": "Keine Tarifgruppe vorschlagen"}, "insurance_plan_selection": {"headline": "<PERSON><PERSON><PERSON> au<PERSON>", "body": "<PERSON><PERSON> <b>zusätzlich noch spezifische Tarife</b> zum KVcare-Tarif-<PERSON> hinzuf<PERSON> <b>(Max. 5)</b>.", "insurance_plan_array_item_name": "<PERSON><PERSON><PERSON>", "insurance_plan_array_item_add": "<PERSON><PERSON><PERSON>", "insurance_company": "Versicherungsunternehmen", "insurance_plan": "<PERSON><PERSON><PERSON>", "insurance_plan_add_button_label": "<PERSON><PERSON><PERSON>", "hint_please_add_at_least_1_insurance_plan_or_allow_suggestions": "Bitte fügen Sie mindestens einen Tarif <PERSON>, oder wählen Sie mindestens eine Tarifgruppe oben.", "max_insurance_plan_count": 5, "add_insurance_plan_from_other_customer_in_group": "<PERSON>e können Tarife von anderen Personen in dieser Gruppe kopieren. Die Tarife werden für jede Person separat gespeichert.", "insurance_plans_for_customer_in_group": "Tarife für"}, "insurance_plan_delete_modal": {"body": "<PERSON><PERSON>?", "confirm_delete_button_label": "<PERSON>a", "cancel_delete_button_label": "Abbrechen"}, "sick_pay_and_hostpital_fee": {"headline": "Wie hoch soll das Krankentagegeld und das Krankenhaustagegeld sein?", "body": "<b>DIN 77230</b> schl<PERSON><PERSON> für Krankentagegeld <b>80 % des Nettoeinkommens</b> aus Arbeit zur Aufrechterhaltung des Lebensstandards vor.", "input_sick_pay_euro": "Krankentagegeld", "input_hospital_fee_euro": "Krankenhaustagegeld"}, "deductable": {"headline": "Wie hoch soll die Selbstbeteiligung sein?", "input_max_deductable_euro": "Maximale Selbstbeteiligung"}}}, "raw": {"geschlecht": ["<PERSON>", "<PERSON><PERSON>", "Person"], "titel": ["Dr.", "Prof."], "familienstand": ["ledig", "verheiratet", "eingetragene Lebensgemeinschft", "ver<PERSON><PERSON><PERSON>t", "geschieden"], "berufsstand": ["angestellt", "selbständig", "in Ausbildung", "Student", "nicht berufstätig/Kind", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Beamtenanwärter"], "government_employer": ["Land", "Bund"], "staatsangehoerigkeit": ["deutsch", "andere"]}, "rawKv": {"geschlecht": {"values": {"Herr": "<PERSON>", "Frau": "<PERSON><PERSON>", "Person": "Person"}, "defaultValue": "<PERSON>"}, "titel": {"values": {"Dr": "Dr.", "Prof": "Prof."}}, "familienstand": {"label": "Familienstand", "values": {"ledig": "ledig", "verheiratet": "verheiratet", "eingetragene Lebensgemeinschft": "eingetragene Lebensgemeinschft", "verwitwet": "ver<PERSON><PERSON><PERSON>t", "geschieden:": "geschieden"}}, "berufsstand": {"label": "Berufsstand", "values": {"angestellt": "angestellt", "selbständig": "selbständig", "in Ausbildung": "in Ausbildung", "nicht berufstätig/Kind": "nicht berufstätig/Kind", "freiberuflich": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Beamter": "<PERSON><PERSON><PERSON>", "Student": "Student", "Beamtenanwärter": "Beamtenanwärter"}}, "staatsangehoerigkeit": {"values": {"deutsch": "deutsch", "andere:": "andere"}, "defaultValue": "deutsch"}, "aufenthaltstitel": {"values": {"befristet": "befristet", "unbefristet:": "unbefristet"}}, "boolean": {"true": "<PERSON>a", "false": "<PERSON><PERSON>"}, "bundesland": {"values": {"Baden-Württemberg": "Baden-Württemberg", "Bayern": "Bayern", "Berlin": "Berlin", "Brandenburg": "Brandenburg", "Bremen": "Bremen", "Hamburg": "Hamburg", "Hessen": "Hessen", "Mecklenburg-Vorpommern": "Mecklenburg-Vorpommern", "Niedersachsen": "Niedersachsen", "Nordrhein-Westfalen": "Nordrhein-Westfalen", "Rheinland-Pfalz": "Rheinland-Pfalz", "Saarland": "Saarland", "Sachsen": "Sachsen", "Sachsen-Anhalt": "Sachsen-Anhalt", "Schleswig-Holstein": "Schleswig-Holstein", "Thüringen": "T<PERSON><PERSON><PERSON>en"}}}, "validation": {"errorSummaryTitle": "Bitte korrigieren Sie folgende Fehler"}, "errors": {"prisma": {"P2002": {"email": "<PERSON><PERSON> Email <PERSON> wird bereits verwendet.", "id": "Diese ID wird bereits verwendet."}, "validation": "Die eingegeben Daten sind nicht valide oder ein Systemfehler ist aufgetretten, bitte wenden Sie sich an Support."}}, "autocomplete": {"profession": {"values": ["(<PERSON><PERSON><PERSON><PERSON>) Wehrdienst", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Abgeordnete und Politiker", "Abteilungs-, Bereichsleiter und Referatsleiter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Aktionskünstler", "<PERSON><PERSON>pf<PERSON><PERSON>", "ambulante <PERSON>", "Anatomiegehilfen", "Animateur", "Anlagenmechaniker", "Antiquität<PERSON>h<PERSON><PERSON><PERSON>", "Anwälte und Notare", "Apothekenhelfer", "Apotheker und Pharmazeuten", "Arbeitslose", "Archäologen", "Architekten", "Archivare und Bibliothekare", "Artisten", "Ärzte (Humanmedizin)", "Ärzte (Tiermedizin)", "Ärzte (Zahnmedizin)", "Astrologen", "Astronomen", "Aug<PERSON>pt<PERSON><PERSON>", "Auktionatoren", "<PERSON>sbeiner", "Auszubildende", "Automatenaufsteller", "Automobilkaufleute und -verkäufer", "<PERSON><PERSON><PERSON>", "Bäckerei- und Konditoreifachverkäufer", "Bademeister und -gehilfen", "Bankfachleute", "<PERSON><PERSON><PERSON><PERSON> (Profi), <PERSON><PERSON><PERSON><PERSON><PERSON> (Profi)", "Bauarbeit<PERSON> und Bauhelfer", "Bausparkassenfachleute", "Bauzeichner und Kartographen", "Beam<PERSON>", "Bedienstete", "Bekleidungszubehörfertiger", "Berater in Land-, Tierwirtschaft und Gartenbau", "Bergbauhandwerker", "<PERSON><PERSON><PERSON>", "Bergleute und Hauer", "<PERSON><PERSON><PERSON> unbekannt aus Kinderumstellung", "Berufs-, Bildungsberater und Arbeitsvermittler", "<PERSON><PERSON><PERSON>", "Betonbauer und Maurer", "Betriebs- und Volkswirte", "Betriebsorganisations- und Steuerungsfachleute", "Betriebstechniker", "Bildhauer und Schnitzer", "Biologen", "Biologisch-technische Fachkräfte", "<PERSON><PERSON><PERSON><PERSON><PERSON>, -zie<PERSON> und -stanzer", "Blitzschutzmonteur", "<PERSON><PERSON><PERSON><PERSON>", "Bohrinselhandwerker", "<PERSON><PERSON><PERSON><PERSON> <PERSON> Devi<PERSON>ä<PERSON>ler", "Bots<PERSON><PERSON>", "Brandschutzfachleute", "B<PERSON><PERSON>tein-, Formstein und Betonhersteller", "Briefträger und -sortierer", "<PERSON><PERSON><PERSON><PERSON>", "Bühnen-, Licht-, Bild- und Tontechniker", "Bühnenbildner und Requisiteure", "Bundesfreiwilligendienst", "Bürokaufleute", "Bürokommunikationsfachleute und Sekretäre", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chemiebetriebswerker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chemikanten", "<PERSON><PERSON><PERSON><PERSON>", "Chorleiter und Dirigenten", "Controller", "Croupiers", "Dachdecker und Gerüstbauer", "Datenschutzbeauftragte", "Datenverarbeitungs- und IT-Fachleute", "Dekorateure", "Designer (Textil) und Couturiere", "Desinfektoren und Schädlingsbekämpfer", "Detektive", "Diätassistenten und Gesundheitsberater", "<PERSON><PERSON>", "Direktions- und Managementassistenten", "Dockmaschinisten", "Dolmetscher und Übersetzer", "Drahtverformer und -präger", "Dramaturgen", "Drogist", "<PERSON>ucker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Druckvorlagenhersteller", "E-Sportler", "Edelsteinbearbeiter", "EDV- und Software-Berater", "EDV-Operator", "Einkäufer und Disponenten", "Einschießer", "Einzelhandelskaufleute u. Verkäufer (Kleidung, Textilien, Schuhe)", "Einzelhandelskaufleute und Verkäufer", "Einzelhandelskaufleute und Verkäufer (Elektro)", "Einzelhandelskaufleute und Verkäufer (Kfz- und Zweiradzubehör)", "Einzelhandelskaufleute und Verkäufer (Kiosk und Imbiss)", "Einzelhandelskaufleute und Verkäufer (Lebensmittel und Getränke)", "Einzelhandelskaufleute und Verkäufer (Markt)", "Einzelhandelskaufleute und Verkäufer (Tiere und Zoobedarf)", "Einzelhandelskaufleute und Verkäufer (Waffen)", "Eisenbahnbetriebshelfer (Ku<PERSON>ler, Rangierer und Streckenläufer)", "Eisenbahnbetriebsleiter", "Elektriker und Elektroinstallateure", "Elektromaschinen- und -g<PERSON><PERSON><PERSON><PERSON>", "Elektromonteure", "Elektroniker", "Elternzeit", "Empfangspersonal", "Energie- und Umweltberater", "Energiemaschinisten", "Entsorgungs- und Abfallbeseitigungsfachkräfte", "Erdbewegungs- und Baumaschinenführer", "Erdbewegungsarbeiter", "<PERSON>rdbohrgerät<PERSON><PERSON>", "Erden-, Kies- und Sandgewinner", "Erdöl- und Erdgasgewinner", "<PERSON><PERSON><PERSON>", "Ergotherapeuten", "Ernährungswissenschaftler", "<PERSON><PERSON><PERSON><PERSON> (Anstalt, Heim)", "<PERSON><PERSON><PERSON><PERSON> <PERSON>leger", "Eventmanager und Veranstaltungskaufleute", "Fahrbetriebsregler und Dispatcher", "Fahrradkuriere", "Fahrschullehrer und Fahrsicherheitsinstruktoren", "Fahrzeugreiniger und -pfleger", "Feinmechaniker", "Feuerungs-, <PERSON><PERSON><PERSON> <PERSON> <PERSON>uer", "Feuerwehrleute", "Filial- und Betriebsleiter", "Finanz- und Rechnungswesenfachleute", "Finanz- und Vermögensberater (Außendienst)", "Finanz- und Vermögensberater (Innendienst)", "<PERSON>", "Fischereifacha<PERSON><PERSON><PERSON>", "Fleischereifachverkäufer", "Fliesen-, Platten- und Mosaikleger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>r", "Fluglotsen und Flugsicherungsbedienstete", "Flugzeugführer und Piloten", "Flugzeugmechaniker", "Fördermaschinisten", "Forstverwalter", "Forstwirte", "Foto-, Film und Videolaboranten", "Fotografen", "Freiwilliges Soziales Jahr", "Friedhofsarbeiter", "Friseure", "Fuhr- und Taxiunternehmer", "Fuhrparkleiter", "Funk- und Fernsehmoderatoren", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Profi), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Profi)", "Galvaniseure", "Garderobiere", "Garten- und Landschaftsarchitekten", "<PERSON><PERSON><PERSON><PERSON> und Baumpfleger", "Gas- und Wasserinstallateure", "Gastronome und Gastwirte", "Gebäudereiniger", "Gebrauch<PERSON>design<PERSON>", "Geistliche und Seelsorger", "Geologen und Kartographen", "Gerichtsvollzieher und Vollstreckungsbedienstete", "Geschäftsführer und Inhaber", "Geschichtswissenschaftler", "Gesundheitskaufleute", "Getränkehersteller", "Gewerbeaufseher und Sicherheitskontrolleure", "Gießer und Former", "Glas-, Keram- und Porzellanmaler", "Glasbläser (nicht maschinell)", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>, -bear<PERSON><PERSON> und -veredler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Gold- und Edelmetallschmiede", "Grafiker, Kommunikations- und Mediendesigner", "<PERSON><PERSON>", "Grenzschutzbeamte", "Grenzschutzbeamte (Flugdienst)", "Grenzschutzbeamte (GSG9)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Hundeführer)", "Grenzschutzbeamte (Seedienst)", "Grenzschutzbeamte (Sprengstoffexperten)", "Grenzschutzbeamte (Taucher)", "Groß- und Außenhandelskaufleute", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> und -verarbeiter", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hafenarbeitskräfte", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hand-, <PERSON><PERSON><PERSON>leger und Nageldesigner", "Handballspieler (Profi), Handballspielerin (Profi)", "Handelsvertreter", "Hausfrauen und Hausmänner", "<PERSON><PERSON><PERSON>", "Hauswirtschafter und Haushälter", "Hebammen und Entbindungshelfer", "He<PERSON><PERSON>hungspfleg<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Heimarb<PERSON><PERSON>", "He<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Holz- und Kunststoffkonstruktionsbauer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Holzoberflächenveredler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ho<PERSON>zwarenhersteller", "Hörgeräteakustiker", "<PERSON><PERSON><PERSON>", "Immobilienfachleute und Hausverwalter", "Industrie- und Fassadenkletterer", "Industrie- und Maschinenarbeiter", "Industriekaufleute", "Industriemechaniker", "Influencer, Influencerin", "Informatiker", "Ingenieur (Erdöl und Erdgas)", "Ingenieure", "Ingenieure (Bau)", "Ingenieure (Bergbau-, Hütten- und Gießereigewerbe)", "Ingenieure (Elektro)", "Ingenieure (Fertigung)", "Ingenieure (Luft- und Raumfahrttechnik)", "Ingenieure (Maschinenbau und Fahrzeugtechnik)", "Ingenieure (Physik und Chemie)", "Ingenieure (Schiffsbau)", "Ingenieure (Vermessungswesen und Kartographie)", "Inkassobeauftragte (Außendienst)", "Inkassobeauftragte (Innendienst)", "Innenarchitekten", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Intendanten", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Isolierer und Abdichter", "<PERSON><PERSON><PERSON> und Tierfänger", "Journalisten und Redakteure", "<PERSON><PERSON><PERSON>", "Justizfachangestellte", "Kälteanlagenbauer und -installateure", "Kameraleute", "Kapitäne und Schiffsführer", "Karosserie- und Fahrzeugbauer", "Kartenverkäufer und -kontrolleure", "<PERSON><PERSON><PERSON>", "Kaufmännische Angestellte", "kein Beruf angegeben", "<PERSON><PERSON><PERSON>", "Keramiker und Töpfer", "Kindergärt<PERSON> und -pfleger", "<PERSON><PERSON><PERSON><PERSON>, Denkmalpfleger und Konservatoren", "<PERSON><PERSON><PERSON><PERSON> und Flas<PERSON>ner", "<PERSON><PERSON><PERSON>", "Konditoren", "Konserven- und Fertiggerichtehersteller", "Konstrukteure", "Konstruktionsmechaniker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Korb- und Flechtwarenhersteller", "Kosmetiker und Visagisten", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Kraftfahrzeug- und Zweiradmechaniker", "<PERSON><PERSON><PERSON><PERSON>", "Krankenpfleger und -schwestern, Krankenpflegehelfer", "Kriminalbeamte (Ermittlungstätigkeit)", "Küchenpersonal", "Kulturwissenschaftler und Völkerkundler", "Kunstfotografen", "Kunsthändler und Kunstsachverständige", "Kunststoffverarbeiter", "Kunstwissenschaftler", "<PERSON><PERSON><PERSON>", "Laborant", "<PERSON><PERSON><PERSON>", "Lagerarbeitskräfte", "Lagerverwalter und Magaziner", "Landarbeitskräfte", "Landmaschinenmechaniker", "Landschaftsgärtner", "Landwirte", "Lebens- und Genussmittelkoster", "<PERSON><PERSON><PERSON><PERSON>lde<PERSON><PERSON>", "Lebensmittelkontrolleure", "<PERSON><PERSON><PERSON><PERSON><PERSON>, Leder- und Fellverarbeiter", "<PERSON><PERSON><PERSON> (Berufsschule)", "<PERSON><PERSON><PERSON> (Grund-, Real-, Haupt-, Gesamt- und Sonderschule)", "<PERSON><PERSON><PERSON> (Gymnasium)", "<PERSON><PERSON><PERSON> (Musik-, Gesang- und Kunstschule)", "<PERSON><PERSON><PERSON> (Sprachschule)", "<PERSON><PERSON><PERSON> (Volkshochschule)", "Literaturwissenschaftler", "Logopäden", "<PERSON><PERSON><PERSON> und <PERSON>", "Luftverkehrsberufe (Bodentätigkeiten)", "Makler", "Maler", "Marketingfachleute", "Maschenwarenfertiger", "Maschinen- und Behälterreiniger", "Maschinenbau- und -wartungsberufe", "Maschineneinrichter und -wärter", "Maschinenführer und Maschinisten", "<PERSON><PERSON><PERSON>, Krankengymnasten und Physiotherapeuten", "Mathematiker", "Mechaniker", "Mechatroniker", "Medizinisch- und Pharmazeutisch-techn. Assist. und Medizinallab.", "Mehl- und Nährmittelhersteller", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>, -fr<PERSON><PERSON>, -sch<PERSON><PERSON>, -feiler und -hobler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Metallkleber", "Metalloberflächenveredler und Emaillierer", "Metallpolierer", "Metallvergüter", "Meteorologen", "<PERSON><PERSON>, Fleischer und Schlächter", "Milch- und Fettverarbeiter", "Militär-, Polizei- und Zollverwaltung", "<PERSON><PERSON>au<PERSON><PERSON><PERSON><PERSON>", "Mineralogen", "<PERSON><PERSON>, Sprengmeister und -gehilfen", "Missionsgeistliche", "Möbelpacker und Stauer", "<PERSON><PERSON><PERSON><PERSON> (Feinwerk)", "Models", "<PERSON><PERSON> und Montagearbeiter", "Müllmänner, -frauen und Straßenreiniger", "<PERSON><PERSON><PERSON>", "Musikinstrumentenbauer", "Musikwissenschaftler", "Naturwissenschaftler", "Nicht-Erwerbstätige", "Notärzte", "Ordnungsamtsmitarbeiter", "Orthopädie- und Chirurgiemechaniker", "Pädagoge (Wissenschaftler)", "Paketzusteller", "Papierma<PERSON> und -verarbeiter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pensionäre", "Personalberater", "Personalfachleute", "<PERSON>trainer (Business Coaching)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pferdewirte", "<PERSON><PERSON><PERSON><PERSON>", "Pflege-, Sozial-, und Gesundheitswirte", "Pförtner und Portiers", "Pharmaberater", "Pharmareferent", "Physician Assistant", "Physikalisch-technische Fachkräfte", "<PERSON>ys<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>, Bauleiter und -führer", "Politologe", "Polizeibeamte (Flugdienst)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (Hundeführer)", "Polizeibeamte (Innendienst)", "Polizeibeamte (Seedienst)", "Polizeibeamte (SEK und MEK)", "Polizeibeamte (Sprengstoffexperten)", "Polizeibeamte (Taucher)", "Polizeivollzugsbeamte", "<PERSON><PERSON><PERSON>", "Postdienstleistungsfachleute", "Presse- und Werbefotografen", "Pressereferenten und -sprecher", "Produkt<PERSON><PERSON><PERSON> und -manager", "Produktions- und Fließbandarbeiter", "<PERSON><PERSON>, Hochschullehrer und -dozenten", "Profisportler", "<PERSON><PERSON><PERSON>", "Projektmanager", "Propagandisten", "Prostituierte", "Psychologen und Psychotherapeuten", "Pyrotechniker", "Qualitätsprüfer", "Radio- und Fernsehtechniker", "Raffineriebetriebsarbeitskräfte", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Rechtsanwalts- und Notarfachangestellte", "Rechtspfleger und -berater", "Regisseure und Produzenten", "<PERSON><PERSON><PERSON><PERSON>", "Reiseverkehrsfachleute", "Religionswissenschaftler", "<PERSON><PERSON><PERSON>", "Reprografen", "Restaurant- und Hotelfachleute", "Restauratoren", "Richter, Staats- und Amtsanwälte", "<PERSON><PERSON>r-, Kanal- und Industrieservicefachkräfte", "Rohrleitungsbauer und -installateure", "Rolladen- und Sonnenschutzbauer und -installateure", "Rundfunk- und Fernsehsprecher", "Sachbearbeiter und Kundenberater", "Salzgewinner und -aufbereiter", "<PERSON><PERSON><PERSON>", "Sanitär-, Heizungs-, Lüftungsbauer und -installateure", "Sanitäter und Rettungsassistenten", "<PERSON><PERSON><PERSON><PERSON> und Kontrolleure", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Schic<PERSON>leiter", "Sc<PERSON>enenfahrzeugführer", "Schiffsbauhelfer und Dockarbeiter", "Schiffsdecksleute und Schifffahrtsgehilfen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Schiffsmaschinisten", "Schiffsnautiker", "Schiffsoffiziere", "Schilder- und Lichtreklamehersteller", "Schleusenwärter", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Schmuckdesigner und -modelleure", "<PERSON>, <PERSON><PERSON><PERSON> und Sticker", "<PERSON><PERSON><PERSON><PERSON>, Kasche<PERSON> und Retuscheure", "Schornsteinfeger und Kaminkehrer", "<PERSON><PERSON><PERSON><PERSON>", "Schriftsetzer", "Schriftsteller und Publizisten", "Schrott- und Alteisenhändler", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sc<PERSON><PERSON>iter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Schutz- und Sicherheitsfachleute", "Sch<PERSON><PERSON>r", "Seelsorgehelfer und Pastoralarbeiter", "Servicekräfte Hotel- und Gaststättengewerbe", "Servicetechniker", "Software- und Systemfachleute", "Soldaten und Offiziere", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sozialpädagogen", "Soziologe", "Speditions- und Verkehrsfachleute", "Speläologen (Höhlenforscher)", "<PERSON><PERSON>, <PERSON><PERSON><PERSON> und Seiler", "Sport- und Fitnesskaufleute", "Sporttrainer", "Sportwissenschaftler", "Sprachwissenschaftler", "Sprechstunden- und Arzthelfer", "Statistiker und Marktforscher", "Steinbearbeiter und -metze", "Stein<PERSON><PERSON><PERSON> und -brecher", "Steuerberater", "Stewards (Luftfahrt)", "Stewards (Schifffahrt)", "Strafvollstreckungs- und Vollzugsbedienstete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Straßenwachtfahrer", "Straßenwärter", "Stuckateure, Gipser und Verputzer", "<PERSON><PERSON>", "Stuntmen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tagesmütter und -väter", "Tankstellenpersonal", "<PERSON><PERSON><PERSON><PERSON>", "Tänzer (Hotel- und Gaststättengewerbe)", "Tänzer (Nachtlokal oder Bar)", "Tätowierer und Piercer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Techniker", "Techniker  (Vermessungswesen und Kartographie)", "Techniker (Bau)", "Techniker (Bergbau-, Hütten- und Gießereigewerbe)", "Techniker (Elektro)", "Techniker (Erdöl und Erdgas)", "Techniker (Fertigung)", "Techniker (Luft- und Raumfahrttechnik)", "Techniker (Maschinenbau und Fahrzeugtechnik)", "Techniker (Physik und Chemie)", "Techniker (Schiffsbau)", "Technische Zeichner", "Telefonisten", "<PERSON><PERSON><PERSON><PERSON>", "Testpiloten (Drachen-, <PERSON><PERSON><PERSON><PERSON>, Hängegleiter, Reklameflieger)", "Textilreiniger, -p<PERSON>ger und -bearbeiter", "Textilverarbeiter und -ausrüster", "Thekenpersonal", "Thekenpersonal (Nachtlokal)", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tierpräparatoren", "Tierpsychologen", "Tierwirte und -züchter", "<PERSON><PERSON><PERSON>", "Trainee", "Transport<PERSON><PERSON><PERSON><PERSON>", "Trockenbaumonteure", "Tunnel- und Stollenbauer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Unternehmensberater", "Veranstaltungsrigger", "Verbandsleiter", "Verbraucherberater", "Verfahrensmechaniker (Metallerzeugung und -umformung)", "Verkaufsfahrer", "Verlagskaufleute", "Vermieter und Verleiher", "Verpackungsmittelhersteller", "Versandarbeiter und Verpacker", "Versicherungsfachleute (Außendienst)", "Versicherungsfachleute (Innendienst)", "Vertriebsassistenten", "Vertriebsfachleute (Außendienst)", "Vertriebsfachleute (Innendienst)", "Verwalter in Land-, Tierwirtschaft und Gartenbau", "Verwaltungsfachleute", "Verwaltungsfachleute im höheren, gehobenen und mittleren Dienst", "Vorstände", "Vorstände (Kapitalgesellschaft)", "Vulkaniseure", "<PERSON><PERSON><PERSON><PERSON><PERSON> und -bildner", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Waren- und Zeitungsausträger", "Warenmal<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -auf<PERSON><PERSON> und -sortierer", "Wasserbauer und Kulturbauarbeiter", "Wasserver- und -entsorgungsfachkräfte", "<PERSON>, Filz- und Tuftingwarenmacher", "Werbefachleute", "Werkmeister", "Werkzeugmacher und -mechaniker", "<PERSON><PERSON>", "Wirtschaftsberater- und Steuerfachgehilfen", "Wirtschaftsingenieure", "Wirtschaftsprüfer", "Wissenschaftliche Mitarbeiter", "Zahnarzthelfer", "Zahntechniker", "Z<PERSON><PERSON><PERSON> und Kunstmaler", "Zeitungswerber", "<PERSON><PERSON><PERSON><PERSON>", "Zerspanungsmechaniker und Metallbohrer", "<PERSON><PERSON><PERSON>", "Zimmerservicekräfte", "Zivildienstleistende", "Zollinspektoren", "Zoologe", "Zucker- und Süßwarenhersteller"]}, "private_health_insurance": {"none": {"values": ["<PERSON><PERSON>"]}, "GKV": {"values": ["AOK - Die Gesundheitskasse für Niedersachsen", "AOK - Die Gesundheitskasse in Hessen", "AOK Baden-Württemberg", "AOK Bayern - Die Gesundheitskasse", "AOK Bremen / Bremerhaven", "AOK Nordost - Die Gesundheitskasse", "AOK NordWest - Die Gesundheitskasse", "AOK PLUS - Die Gesundheitskasse für Sachsen und Thüringen", "AOK Rheinland/Hamburg - Die Gesundheitskasse", "AOK Rheinland-Pfalz/Saarland - Die Gesundheitskasse", "AOK Sachsen-Anhalt - Die Gesundheitskasse", "Audi BKK", "BAHN-BKK", "BARMER", "BERGISCHE KRANKENKASSE", "Bertelsmann BKK", "Betriebskrankenkasse der G.M.Pfaff AG", "Betriebskrankenkasse EWE", "Betriebskrankenkasse Miele", "Betriebskrankenkasse Mobil", "Betriebskrankenkasse PricewaterhouseCoopers", "Betriebskrankenkasse Technoform", "BIG direkt gesund", "BKK Akzo Nobel Bayern", "BKK B<PERSON>", "BKK Deutsche Bank AG", "BKK Diakonie", "BKK EUREGIO", "BKK evm", "BKK exklusiv", "BKK Faber-Castell & Partner", "BKK firmus", "BKK <PERSON>", "BKK GILDEMEISTER SEIDENSTICKER", "BKK Groz-Beckert", "BKK Herkules", "BKK Linde", "BKK MAHLE", "bkk melitta hmr", "BKK mkk - meine krankenkasse", "BKK MTU", "BKK Pfalz", "BKK ProVita", "BKK Public          ", "BKK Rieker.RICOSTA.Weisser", "BKK Salzgitter", "BKK Scheufelen", "BKK Schwarzwald-BaarHeuberg", "BKK VDN", "BKK VerbundPlus", "BKK Voralb ", "HELLER*INDEX*LEUZE", "BKK WERRA-MEISSNER", "BKK WIRTSCHAFT UND FINANZEN", "BKK_DürkoppAdler", "BKK24", "BKK-Würth", "BMW BKK", "Bosch BKK", "Continentale ", "Betriebskrankenkasse", "DAK-Gesundheit", "Debeka BKK", "energie-Betriebskrankenkasse", "EY Betriebskrankenkasse", "Handelskrankenkasse (hkk)", "He<PERSON>t K<PERSON>enkas<PERSON>", "HEK - Hanseatische Krankenkasse", "IKK - Die Innovationskasse", "IKK classic", "IKK gesund plus", "IKK Südwest", "INNUNGSKRANKENKASSE ", "BRANDENBURG UND BERLIN", "KARL MAYER BKK", "Kaufmännische Krankenkasse - ", "Krankenkassenname", "KKH", "KNAPPSCHAFT", "Koenig & Bauer BKK", "Krones Betriebskrankenkasse", "Mercedes-Benz BKK", "Merck BKK", "mhplus Betriebskrankenkasse", "Novitas BKK", "Pronova BKK", "R+V Betriebskrankenkasse", "Salus BKK", "SECURVITA BKK", "Siemens-Betriebskrankenkasse ", "(SBK)", "SKD BKK", "Sozialversicherung für ", "Landwirtschaft, Forsten und ", "Gartenbau (SVLFG)", "Südzucker BKK", "Techniker <PERSON>kas<PERSON>", "TUI BKK", "VIACTIV Krankenkasse", "vivida bkk", "WMF BKK", "ZF BKK"]}, "PKV": {"values": ["<PERSON><PERSON><PERSON>", "DKV (Victoria)", "Allianz Private Krankenversicherung", "Axa (Colonia, DBV-Winterthur)", "Signal Iduna (mit Deutscher Ring)", "Central Krankenversicherung", "Barmenia", "Versicherungskammer Bayern", "Continentale", "HUK-Coburg", "<PERSON><PERSON>", "HanseMerkur", "<PERSON><PERSON><PERSON>", "Landeskrankenhilfe LKH", "Süddeutsche Krankenversicherung", "Union Krankenversicherung", "Deutscher Ring", "Inter", "Universa", "Münchener Verein", "R+V", "<PERSON><PERSON>", "ARAG", "LVM", "Alte Oldenburger Krankenversicherung", "<PERSON><PERSON><PERSON><PERSON>", "Württembergische Krankenversicherung", "PAX-Familienfürsorge Krankenversicherung", "HanseMerkur Versicherungsgruppe S", "Mannheimer Versicherungen", "<PERSON><PERSON><PERSON>", "DEVK", "Provinzial", "Concordia", "Freie Arztkasse", "Vigo Krankenversicherung (Düsseldorfer Versicherung)", "Mecklenburgische", "LIGA Krankenversicherung katholischer Priester", "Augenoptiker Ausgleichskasse", "Praenatura", "<PERSON><PERSON>- und Sterbekasse", "Krankenunterstützungskasse der Berufsfeuerwehr Hannover", "Alte Oldenburger", "Sonstige"]}}}, "modal_configurations": {"warning_no_offers": {"headline": "Achtung!", "text": "Es besteht eine sehr hohe Wahrscheinlichkeit, dass mit dieser Angabe kein positives Versicherungsangebot eingeholt werden kann.", "iconId": "SentimentNeutralIcon", "iconColor": "var(--modal-warning-icon-color)"}, "problem": {"headline": "Problem!", "text": "Wir haben ein Problem!", "iconId": "SentimentVeryDissatisfiedIcon", "iconColor": "var(--error-red)"}, "success": {"headline": "Erfolg!", "text": "Alles erfolgreich!", "iconId": "SentimentSatisfiedAltIcon", "iconColor": "var(--teal)"}}, "customer_types": {"policy_holder_only": "<PERSON><PERSON> Versicherungsnehmer", "policy_holder_and_insured_person": "Versicherungsnehmer und die zu versichernde Person", "insured_primary": "<PERSON><PERSON> ve<PERSON><PERSON>", "spouse": "<PERSON><PERSON><PERSON><PERSON>", "child": "Kind"}, "case_relation_types": {"paying": "<PERSON><PERSON> Versicherungsnehmer", "paying_and_insured_primary": "Versicherungsnehmer und die zu versichernde Person", "insured_primary": "<PERSON><PERSON> ve<PERSON><PERSON>", "child": "<PERSON><PERSON><PERSON><PERSON>", "spouse": "Kind"}, "document_list": {"documents_single_or_plural": "{count, plural, one {# Dokument} other {# Dokumente}}", "buttons": {"download": "Download", "open": "<PERSON><PERSON><PERSON>"}}, "document_upload": {"headline_upload_documents": "Dokumente hochladen", "pick_document_button_label": "PDF Dokument auswählen", "dropdown_document_type": {"label": "Dokumententyp", "placeholder": "Bitte wählen Sie einen Dokumententyp", "options": {"examination_booklet": "Untersuchungsheft", "medical_report": "Arztbericht / Befundbericht", "lab_report": "Laborbefund", "hospital_report": "Krankenhausbericht", "vaccination_card": "Impfpass", "application_form": "Antragsformular", "authorization_consent": "Vollmacht / Schweigepflichtentbindung", "rehab_report": "Reha<PERSON> <PERSON><PERSON>", "medication_plan": "Medikamentenplan", "medical_certificate": "Attest / ärztliche Stellungnahme", "psychological_report": "Psychologisches Gutachten", "surgery_report": "Operationsbericht", "followup_report": "Nachsorgebericht", "termination_confirmation": "Kündigungsbestätigung Vorversicherung", "advisory_protocol": "Beratungsprotokoll / vorvertragliche Information", "health_self_disclosure": "Selbstauskunft Gesundheitsfragen", "risk_pre_request": "Risikovoranfrage", "insurance_policy": "Versicherungsschein / Police", "modified_document": "Geändertes Dokument", "additional_information": "Zusätzliche Informationen", "other_document": "Sonstiges Dokument"}}, "optional_message": "Optionale Nachricht", "optional_message_input_placeholder": "Text", "button_upload_document": "Dokument hochladen", "limit_uploaded_file_types_with_html_accept": ".pdf", "error_upload_failed": "Das Dokument konnte nicht hoch<PERSON>aden werden. Bitte versuchen Sie es erneut.", "error_file_too_large": "Die Datei ist zu groß. Maximale Dateigröße ist {maxSize}.", "error_invalid_file_type": "Ungültiger Dateityp. Bitte wählen Sie eine PDF-Datei."}, "case_status": {"application_ready": "<PERSON><PERSON><PERSON> bereit", "risk_inquiry_running": "Risiko-Voranfrage läuft", "processing": "Nachbearbeitung", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_entry": "<PERSON><PERSON><PERSON><PERSON>", "inquiry_running": "<PERSON><PERSON><PERSON> l<PERSON>", "applied": "<PERSON><PERSON><PERSON>", "processing_requests": "{count, plural, one {# Nachbearbeitung} other {# Nachbearbeitungen}}", "applications_ready": "{count, plural, one {# <PERSON>trag bereit} other {# Anträge bereit}}"}, "not_found_404_page": {"sorry": "Tut uns leid!", "page_not_found": "Wir konnten diese Seite leider nicht finden.", "back_to_start": "Zurück zur Startseite"}, "form_configurations": {"personalData": [{"id": "customerType", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.customerType", "optionsPath": "customer_types", "disabled": true}, {"element": "subtitle", "subtitleTranslationKey": "form.insured-person", "disabled": true}, {"id": "firstName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.first_name"}, {"id": "lastName", "element": "text", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.last_name"}, {"id": "salutation", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.anrede", "optionsPath": "rawKv.geschlecht.values"}, {"id": "academicTitle", "element": "dropdown", "label": "form.basisdaten.personliche_daten.stammdaten.titel", "optionsPath": "rawKv.titel.values"}, {"id": "dateOfBirth", "element": "date", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.birthdate_DD_MM_YYYY"}, {"id": "familienstand", "element": "dropdown", "required": true, "label": "rawKv.familienstand.label", "optionsPath": "rawKv.familienstand.values", "disabled": true}, {"id": "email", "element": "email", "required": false, "label": "form.basisdaten.personliche_daten.stammdaten.email"}], "citizenshipAndProfession": [{"element": "subtitle", "subtitleTranslationKey": "form.basisdaten.herkunft.headline"}, {"id": "citizenship", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.staatsangehoerigkeit", "optionsPath": "rawKv.staatsangehoerigkeit.values", "defaultValue": "rawKv.staatsangehoerigkeit.defaultValue"}, {"id": "country", "showIf": {"citizenship": "andere"}, "element": "country", "required": true, "label": "form.herkunft.country"}, {"id": "inCountrySince", "showIf": {"citizenship": "andere"}, "element": "year", "required": true, "label": "form.herkunft.inGermanySinceYear"}, {"id": "aufenthaltstitel", "showIf": {"citizenship": "andere"}, "element": "dropdown", "required": true, "label": "form.herkunft.aufenthaltstitel", "optionsPath": "rawKv.aufenthaltstitel.values"}, {"id": "aufenthaltstitelBefristetBis", "showIf": {"aufenthaltstitel": "befristet"}, "element": "date", "required": true, "dateDisablePast": true, "label": "form.herkunft.befristetBis"}, {"element": "divider"}, {"element": "subtitle", "subtitleTranslationKey": "form.beruf.headline"}, {"id": "professionStatus", "element": "dropdown", "required": true, "label": "form.beruf.berufstand", "optionsPath": "raw.berufsstand"}, {"id": "profession", "element": "autocomplete", "required": true, "label": "form.beruf.beruf", "showIfNot": {"professionStatus": "nicht berufstätig/Kind"}, "optionsPath": "autocomplete.profession.values"}, {"id": "professionSince", "element": "date", "required": true, "dateDisableFuture": true, "showIfNot": {"professionStatus": "nicht berufstätig/Kind"}, "label": "form.beruf.sinceDate"}, {"id": "governmentEmployerType", "element": "dropdown", "required": true, "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>"}, "label": "form.beruf.governmentEmployerType", "optionsPath": "raw.government_employer"}, {"id": "governmentEmployerState", "element": "dropdown", "required": true, "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>", "governmentEmployerType": "Land"}, "label": "form.beruf.bundesland", "optionsPath": "rawKv.bundesland.values"}, {"id": "numberOfChildren", "element": "number", "required": true, "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>"}, "label": "form.beruf.numberOfChildren"}, {"id": "employerContributonDentalPercent", "element": "number", "required": true, "numberMinValue": 0, "inputFieldSuffix": "words.percent", "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>"}, "label": "form.beruf.employerContributonDentalPercent"}, {"id": "employerContributonOutpatientPercent", "element": "number", "required": true, "numberMinValue": 0, "inputFieldSuffix": "words.percent", "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>"}, "label": "form.beruf.employerContributonOutpatientPercent"}, {"id": "employerContributonInpatientPercent", "element": "number", "required": true, "numberMinValue": 0, "inputFieldSuffix": "words.percent", "showIf": {"professionStatus": "<PERSON><PERSON><PERSON>"}, "label": "form.beruf.employerContributonInpatientPercent"}], "previousInsurance": [{"element": "subtitle", "label": "form.previous_insurance.headline", "useFullWidthOfParent": true}, {"element": "typography", "label": "form.previous_insurance.if_no_previous_insurance_set_to_none", "useFullWidthOfParent": true}, {"id": "vorversicherungKrankenversicherung", "element": "arrayof", "required": true, "arrayOfForm": "vorversicherungKrankenversicherung", "arrayItemLabel": "form.vorversicherung.krankenversichherung.array_element", "arrayAddLabel": "form.vorversicherung.krankenversichherung.add_new"}, {"element": "divider"}, {"id": "vorversicherungPflegeversicherung", "element": "arrayof", "arrayOfForm": "vorversicherungPflegeversicherung", "arrayItemLabel": "form.vorversicherung.pflegeversicherung.array_element", "arrayAddLabel": "form.vorversicherung.pflegeversicherung.add_new"}, {"element": "divider"}, {"id": "vorversicherungZusatzversicherung", "element": "arrayof", "arrayOfForm": "vorversicherungZusatzversicherung", "arrayItemLabel": "form.vorversicherung.zusatzversicherung.array_element", "arrayAddLabel": "form.vorversicherung.zusatzversicherung.add_new"}], "vorversicherungKrankenversicherung": [{"id": "nameProvider", "element": "autocomplete", "required": true, "label": "form.vorversicherung.krankenversichherung.name_provider", "valuesArraysPaths": ["autocomplete.private_health_insurance.GKV.values", "autocomplete.private_health_insurance.PKV.values", "autocomplete.private_health_insurance.none.values"], "autocompleteFreeSolo": true, "useFullWidthOfParent": true}, {"id": "beginYearMonth", "element": "monthyear", "required": true, "showIfNot": {"nameProvider": "<PERSON><PERSON>"}, "label": "form.vorversicherung.krankenversichherung.begin_year"}, {"id": "endYearMonth", "element": "monthyear", "required": true, "showIfNot": {"nameProvider": "<PERSON><PERSON>"}, "label": "form.vorversicherung.krankenversichherung.end_year"}, {"id": "outstanding_contributions", "element": "radio", "required": true, "showIfNot": {"nameProvider": "<PERSON><PERSON>"}, "label": "form.vorversicherung.krankenversichherung.outstanding_contributions", "showModalOnValueClicked": {"value": true, "modalId": "warning_no_offers"}}], "vorversicherungPflegeversicherung": [{"id": "nameProvider", "element": "text", "required": true, "label": "form.vorversicherung.pflegeversicherung.name_provider"}, {"id": "beginYearMonth", "element": "monthyear", "required": true, "dateDisableFuture": true, "label": "form.vorversicherung.pflegeversicherung.begin_year"}, {"id": "endYearMonth", "element": "monthyear", "required": true, "label": "form.vorversicherung.pflegeversicherung.end_year"}, {"id": "outstanding_contributions", "element": "radio", "required": true, "label": "form.vorversicherung.pflegeversicherung.outstanding_contributions", "showModalOnValueClicked": {"value": true, "modalId": "warning_no_offers"}}], "vorversicherungZusatzversicherung": [{"id": "insuranceType", "element": "dropdown", "required": true, "label": "form.vorversicherung.zusatzversicherung.insurance_type", "optionsPath": "form.vorversicherung.insurance_types.values"}, {"id": "providerName", "element": "text", "required": true, "label": "form.vorversicherung.zusatzversicherung.name_provider"}, {"id": "insurancePlan", "element": "text", "required": true, "label": "form.vorversicherung.zusatzversicherung.insurance_plan"}], "medication": [{"id": "name", "element": "text", "required": true, "label": "form.health_checklist.medication_name"}, {"id": "dosage", "element": "text", "required": true, "label": "form.health_checklist.dosage"}, {"element": "space"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.medication_use_timespan"}, {"id": "medicationTreatmentStart", "element": "date", "required": true, "label": "form.health_checklist.fromDate_label"}, {"id": "medicationTreatmentEnd", "element": "date", "dateDisablePast": true, "label": "form.health_checklist.toDate_label"}], "bodyMassAndEyesight": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.body_measurements"}, {"id": "weight", "element": "number", "required": true, "inputFieldSuffix": "words.kilogramm_short", "label": "form.health_checklist.weight"}, {"id": "height", "element": "number", "required": true, "inputFieldSuffix": "words.centimeter_short", "label": "form.health_checklist.height"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.visualImpairment.question"}, {"id": "hasVisualImpairment", "element": "radio", "required": true, "useFullWidthOfParent": true}, {"id": "leftEyeDiopter", "element": "text", "required": true, "showIf": {"hasVisualImpairment": true}, "label": "form.health_checklist.visualImpairment.leftEyeDiopter"}, {"id": "rightEyeDiopter", "element": "text", "required": true, "showIf": {"hasVisualImpairment": true}, "label": "form.health_checklist.visualImpairment.rightEyeDiopter"}], "outpatientTreatmentLast5Years": [], "inpatientTreatmentLast10Years": [], "psychologicalTreatmentLast10Years": [], "examinationsOperationsTreatments": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.outpatientTreatmentLast5Years.question"}, {"id": "outpatientTreatmentLast5Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.inpatientTreatmentLast10Years.question"}, {"id": "inpatientTreatmentLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.psychologicalTreatmentLast10Years.question"}, {"id": "psychologicalTreatmentLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}], "teeth": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.missingTeethCount"}, {"id": "missingTeethCount", "element": "number", "required": true, "numberMaxValue": 28, "numberMinValue": 0, "label": "form.health_checklist.teeth.missingTeethCount_input_placeholder"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.replacedOrCrownedTeethCount"}, {"id": "replacedOrCrownedTeethCount", "element": "number", "required": true, "numberMaxValue": 32, "numberMinValue": 0, "label": "form.health_checklist.teeth.replacedOrCrownedTeethCount_input_placeholder"}, {"element": "space"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.oldest_dental_restoration_date"}, {"id": "oldestDentalRestorationDate", "element": "year", "required": true, "dateDisableFuture": true, "label": "form.health_checklist.teeth.oldest_dental_restoration_date_input_placeholder_yyyy"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.currentDentalTreatmentOrConsultation"}, {"id": "currentDentalTreatmentOrConsultation", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.teeth.diagnosedPeriodontitisOrJawMisalignment"}, {"id": "diagnosedPeriodontitisOrJawMisalignment", "element": "nested", "required": true, "nestedForm": "treatment"}], "disabilityOrOccupationalIncapacitySymptomsAnomalies": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.disabilityOrOccupationalIncapacity.question"}, {"id": "disabilityOrOccupationalIncapacity", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.physicalConditionsLast3Years.question"}, {"id": "physicalConditionsLast3Years", "element": "nested", "required": true, "nestedForm": "treatment"}], "infertilityOrConsultationLast5Years": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.infertilityOrConsultationLast5Years.question"}, {"id": "infertilityOrConsultationLast5Years", "element": "nested", "required": true, "nestedForm": "treatment"}], "addictionMedicationHiv": [{"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.substanceAbuseLast10Years.question"}, {"id": "substanceAbuseLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.regularMedicationOrDrugUseLast10Years.question"}, {"id": "regularMedicationOrDrugUseLast10Years", "element": "nested", "required": true, "nestedForm": "treatment"}, {"element": "divider"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.hivInfectionOrTestPending.question"}, {"id": "hivInfectionOrTestPending", "element": "nested", "required": true, "nestedForm": "treatment"}], "healthChecklist": [{"id": "bodyMassAndEyesight", "element": "nested", "required": true, "label": "form.health_checklist.body_measurements_and_eyesight", "nestedForm": "bodyMassAndEyesight"}, {"id": "examinationsOperationsTreatments", "element": "nested", "required": true, "label": "form.health_checklist.examinationsOperationsTreatments", "nestedForm": "examinationsOperationsTreatments"}, {"id": "teeth", "element": "nested", "required": true, "label": "form.health_checklist.teeth.headline", "nestedForm": "teeth"}, {"id": "disabilityOrOccupationalIncapacitySymptomsAnomalies", "element": "nested", "required": true, "label": "form.health_checklist.disabilityOrOccupationalIncapacitySymptomsAnomalies.headline", "nestedForm": "disabilityOrOccupationalIncapacitySymptomsAnomalies"}, {"id": "infertilityOrConsultationLast5Years", "element": "nested", "required": true, "label": "form.health_checklist.infertilityOrConsultationLast5Years.headline", "nestedForm": "infertilityOrConsultationLast5Years"}, {"id": "addictionMedicationHiv", "element": "nested", "required": true, "label": "form.health_checklist.addictionMedicationHiv.headline", "nestedForm": "addictionMedicationHiv"}], "treatmentDetails": [{"id": "condition", "element": "textarea", "required": true, "useFullWidthOfParent": true, "label": "form.health_checklist.treatment.illness_diagnosis_placeholder"}, {"element": "h6", "label": "form.health_checklist.medications", "useFullWidthOfParent": true}, {"id": "medication", "element": "arrayof", "arrayOfForm": "medication", "arrayAddLabel": "form.health_checklist.medication_add", "arrayItemLabel": "form.health_checklist.medication"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.treatment.illness_duration"}, {"id": "fromDate", "element": "date", "required": true, "label": "form.health_checklist.fromDate_label"}, {"id": "toDate", "element": "date", "label": "form.health_checklist.toDate_label"}, {"element": "space"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.daysUnableToWork"}, {"id": "daysUnableToWork", "element": "number", "required": true, "label": "form.number_of_days"}, {"element": "space"}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "label": "form.health_checklist.recoveredWithoutConsequences"}, {"id": "recoveredWithoutConsequences", "element": "radio", "required": true}], "treatment": [{"id": "hadTreatment", "element": "radio", "required": true, "useFullWidthOfParent": true}, {"element": "divider", "showIf": {"hadTreatment": true}, "disabled": true}, {"element": "space", "showIf": {"hadTreatment": true}}, {"element": "plainsubheadline", "useFullWidthOfParent": true, "showIf": {"hadTreatment": true}, "label": "form.health_checklist.treatment.illness_diagnosis"}, {"id": "details", "element": "arrayof", "required": true, "arrayOfForm": "treatmentDetails", "showIf": {"hadTreatment": true}, "arrayAddLabel": "form.health_checklist.add_condition", "arrayItemLabel": "form.health_checklist.condition"}], "testform": [{"id": "lastName", "element": "text", "required": true, "label": "not_found_404_page.page_not_found"}, {"id": "academicTitle", "element": "dropdown", "label": "form.basisdaten.personliche_daten.stammdaten.titel", "optionsPath": "rawKv.titel.values"}, {"id": "citizenship", "element": "dropdown", "required": true, "label": "form.basisdaten.personliche_daten.stammdaten.staatsangehoerigkeit", "optionsPath": "rawKv.staatsangehoerigkeit.values", "defaultValue": "rawKv.staatsangehoerigkeit.defaultValue"}], "insurancePlanSelection": [{"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.insurance_plan_selection.headline"}, {"id": "insurancePlanMix", "element": "cardselector", "optionsPath": "card_selector_configutaions.kvcare_insurance_plan_mix"}, {"id": "selectedPolicies", "element": "database", "required": true, "label": "form.insurancePlanSelection.insurance_plan_selection.body", "datasetConditions": [{"dataset": "potentialInsurance", "dataType": "array", "condition": "hasObjectWithKv", "key": "notes", "value": "consultant"}], "requiredIfKv": {"insurancePlanMix": null}, "arrayAddLabel": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan_array_item_add", "arrayItemLabel": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan_array_item_name"}, {"element": "divider"}, {"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.headline"}, {"element": "typography", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.body"}, {"id": "sickPayEuro", "element": "number", "numberMinValue": 0, "required": true, "inputFieldSuffix": "common.euro_per_day", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.input_sick_pay_euro"}, {"id": "hospitalFeeEuro", "element": "number", "numberMinValue": 0, "required": true, "inputFieldSuffix": "common.euro_per_day", "label": "form.insurancePlanSelection.sick_pay_and_hostpital_fee.input_hospital_fee_euro"}, {"element": "divider"}, {"element": "subtitle", "subtitleTranslationKey": "form.insurancePlanSelection.deductable.headline"}, {"id": "deductableEuro", "element": "number", "numberMinValue": 0, "required": true, "inputFieldSuffix": "words.currency_sign_euro", "label": "form.insurancePlanSelection.deductable.input_max_deductable_euro"}], "insuranceCompanyAndPlan": [{"id": "insuranceCompany", "element": "autocomplete", "label": "form.insurancePlanSelection.insurance_plan_selection.insurance_company", "valuesArraysPaths": ["autocomplete.private_health_insurance.PKV.values"], "autocompleteFreeSolo": false, "useFullWidthOfParent": false}, {"id": "insurancePolicy", "element": "text", "required": true, "label": "form.insurancePlanSelection.insurance_plan_selection.insurance_plan"}]}, "kvcare_private_insurance_provider_list": {"values": ["Allianz", "Alte Oldenburger", "ARAG", "Axa", "Barmenia", "BBKK", "Concordia", "Continentale", "DBV", "DKV", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "HanseMerkur", "Inter", "LKH", "Münchener Verein", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "R+V", "SDK", "Signal Iduna", "Universa", "Württembergische"]}, "card_selector_configutaions": {"kvcare_insurance_plan_mix": {"options": [{"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.basic.headline", "value": "basic", "icon": "SellIcon"}, {"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.standard.headline", "value": "standard", "icon": "RecommendIcon"}, {"name": "form.insurancePlanSelection.kvcare_insurance_plan_mix.cards.premium.headline", "value": "premium", "icon": "LocalPoliceIcon"}], "none_option_label": "Tarifgruppe deaktivieren / nur eigene Auswahl treffen"}}, "imprint": {"title": "Impressum", "subtitle": "<PERSON><PERSON><PERSON> gemäß § 5 TMG", "sections": [{"title": "Betreiber des Online-Angebots", "paragraphs": ["KVcare GmbH", "Beethovenstraße 43", "60325 Frankfurt am Main"]}, {"title": "Registereintrag", "paragraphs": ["Handelsregister: HRB 139613", "Registergericht: Frankfurt am Main", "Umsatzsteuer-Identifikationsnummer: DE456451491"]}, {"title": "Vertretungsberechtigte Geschäftsführer", "paragraphs": ["Dip<PERSON>. <PERSON><PERSON>", "Dipl<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Kontakt", "paragraphs": ["E-Mail: <EMAIL>"]}, {"title": "Inhaltlich verantwortlich", "paragraphs": ["Dip<PERSON>. <PERSON><PERSON>", "Dipl<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>"]}]}, "privacy": {"title": "Datenschutzhinweise", "subtitle": "Informationen gemäß DSGVO", "sections": [{"title": "I. Allgemeines", "paragraphs": ["(1) Diese Datens<PERSON>tzhinweise beinhalten Informationen über die Verarbeitung Ihrer personenbezogenen Daten.", "(2) Personenbezogene Daten gemäß Art. 4 Nr. 1 DSGVO umfassen sämtliche Informationen, welche sich auf Ihre Person beziehen lassen, wie beispielsweise Ihr Vor- und Nachname, Ihre E-Mail-Adresse, Ihre Telefon- bzw. Mobilnummer, Ihre IP-Adresse oder sonstige Kontaktdaten.", "(3) Eine Verarbeitung Ihrer personenbezogenen Daten nach Art. 4 Nr. 2 DSGVO beinhaltet jeden Vorgang im Zusammenhang mit Ihren personenbezogenen Daten, wie beispielsweise das Erheben, das Erfassen, die Speicherung, die Offenlegung, Übermittlung oder das Löschen personenbezogener Daten.", "(4) Die für die Datenverarbeitung verantwortliche Stelle gemäß Art. 4 Nr. 7 DSGVO ist jede natürliche oder juristische Person oder sonstige Stelle, die allein oder gemeinsam mit anderen Verantwortlichen über die Zwecke und Mittel der Verarbeitung personenbezogener Daten entscheidet."]}, {"title": "II. Hinweise zur Verantwortlichen Stelle", "paragraphs": ["(1) Verantwortliche Stelle für die Verarbeitung Ihrer personenbezogenen Daten in diesem Portal gemäß Art. 4 Nr. 7 DSGVO ist die", "KVcare GmbH\nBeethovenstr. 43\n60325 Frankfurt", "(2) Unseren Datenschutzbeauftragten erreichen Sie per Post unter der o.g. Adresse mit dem Zusatz – Datenschutzbeauftragter – oder per E-Mail unter: <EMAIL>"]}, {"title": "III. Verarbeitung Ihrer Daten bei Aufruf der Webseite", "paragraphs": ["(1) <PERSON><PERSON> der Webseite erheben wir diejenigen personenbezogenen Daten, welche Ihr Internet-Browser an unseren Server übermittelt und welche temporär von uns in Protokolldaten (Server-Logfiles) gespeichert werden. Hierbei werden folgende Informationen durch uns erfasst: ...", "(2) Eine Verarbeitung dieser Daten dient dem Zweck, <PERSON>hnen einen fehlerfreien Verbindungsaufbau dieser Website zu ermöglichen...", "(3) Die Rechtsgrundlage für die hier genannte Datenverarbeitung ergibt sich aus Art. 6 Abs. 1 f) DSGVO..."]}, {"title": "IV. <PERSON><PERSON>", "paragraphs": ["(1) Für den Zeitraum des Besuchs auf dieser Website sowie bei Verwendung unseres Internetportals werden sogenannte Cookies auf Ihrem Rechner gespeichert...", "(2) Dabei werden in Ihrem Browser Textdateien abgelegt und in einer für uns zuordenbaren Weise gespeichert...", "(3) Die Verarbeitung dieser Daten erfolgt im Rahmen unseres berechtigten Interesses gemäß Art. 6 Abs. 1 f) DSGVO..."]}, {"title": "<PERSON><PERSON>", "paragraphs": ["(1) Wir verarbeiten personenbezogenen Daten nur, soweit dies im Einklang mit datenschutzrechtlichen Bestimmungen möglich ist...", "(2) Unsere Sicherheitsverfahren werden regelmäßig überprüft und dem technologischen Fortschritt angepasst..."]}, {"title": "VI. Ihre Rechte nach der Datenschutz-Grundverordnung", "paragraphs": ["(1) Bezüglich der Verarbeitung Ihrer personenbezogenen Daten stehen Ihnen nach den Regelungen der DSGVO die nachfolgenden Rechte zu: ...", "(2) Ihr Widerrufsrecht: <PERSON><PERSON> haben das Recht, eine einmal erteilte Einwilligung ...", "(3) Ihr Widerspruchsrecht: Bestimmte Daten verarbeiten wir im Rahmen einer Interessensabwägung ...", "(4) Sie haben die Möglichkeit, sich mit einer Beschwerde an unseren Datenschutzbeauftragten oder an eine Datenschutzaufsichtsbehörde zu wenden."]}]}, "terms": {"title": "Nutzungsbedingungen", "subtitle": "Bitte lesen Sie die folgenden Bedingungen sorgfältig durch", "content": ["Mit der Nutzung dieser Website erklären Sie sich mit den folgenden Bedingungen einverstanden.", "1. Nutzung der Inhalte nur für private Zwecke.", "2. <PERSON><PERSON>gabe ohne schriftliche Zustimmung.", "3. Änderungen der Bedingungen sind jederzeit möglich."]}, "cookieConsent": {"title": "Wir verwenden Cookies", "description": "Wir verwenden Cookies, um die Website zu betreiben und das Erlebnis zu verbessern. Sie können alle Cookies akzeptieren, nicht-essentielle ablehnen oder Präferenzen konfigurieren.", "acceptAll": "Alle akzeptieren", "reject": "<PERSON><PERSON> <PERSON><PERSON>", "configure": "Einstellungen", "configureTitle": "Cookie-Einstellungen", "configureDescription": "<PERSON><PERSON><PERSON><PERSON> aus, welche Cookies du zulassen möchtest. Du kannst deine Entscheidung jederzeit ändern.", "save": "Speichern", "categories": {"essential": "Notwendige Cookies", "essentialDesc": "Diese Cookies sind für die Basisfunktionen der Website erforderlich (z. B<PERSON>gin). Sie werden immer gesetzt.", "functional": "Funktionale Cookies", "functionalDesc": "Ermöglichen zusätzliche Funktionen (z. B. Einstellungen, Formularverhalten).", "analytics": "Analytics", "analyticsDesc": "<PERSON><PERSON><PERSON> uns, die Nutzung der Website zu verstehen und zu verbessern.", "marketing": "Marketing", "marketingDesc": "Werbe- und Tracking-Cookies, die personalisierte Werbung ermöglichen."}}, "quickCheck": {"title": "Risikocheck für die Krankenvollversicherung", "description": "Der Risikocheck gibt ihnen schnell und unkompliziert eine grobe Einschätzung und Orientierung zur Annahmefähigkeit des Risikos. Da<PERSON>ür reichen bereits einzelne Angaben aus, es ist nicht erforderlich alle Felder auszufüllen.", "submitButton": "Ergebnis anzeigen", "successMessage": "Ihre Anfrage wurde erfolgreich übermittelt", "result": {"title": "Ihre Risi<PERSON>ck-Ergebnisse", "subtitle": "Hier ist eine Zusammenfassung Ihrer Angaben", "bmiSection": "Body-Mass-Index (BMI)", "dentalSection": "Zahngesundheit", "heightWeight": "Größe: {height} cm, G<PERSON><PERSON>t: {weight} kg", "footerNote": "Diese Ergebnisse dienen nur zur Information. Für eine verbindliche Risikoeinschätzung kontaktieren Si<PERSON> bitte unseren Kundenservice."}, "bmi": {"underweight": "Untergewicht", "normal": "Normalgewicht", "overweight": "Übergewicht", "obese": "Adipositas"}, "sections": {"previousInsurance": {"question": "Der Wechsel erfolgt im direkten Anschluss zur Vorversicherung?"}, "profession": {"question": "Wählen Sie die derzeitige Tätigkeit der zu versichernden Person aus der untenstehenden Liste:", "label": "<PERSON><PERSON><PERSON>"}, "bmi": {"description": "Ab einem Eintrittsalter von 16 Jahren kann nachfolgend der BMI der zu versichernden Person geprüft werden:", "heightLabel": "Größe", "weightLabel": "Gewicht"}, "diagnosis": {"addButton": "Diagnose hinzufügen ({current}/{max})"}, "outpatientDiagnosis": {"question": "Gab es in den letzten 3 Jahren eine ambulante Diagnose?", "diagnosisLabel": "{number}. Diagnose angeben"}, "inpatientDiagnosis": {"question": "Gab es in den letzten 10 Jahren eine stationäre Diagnose (bis Eintrittsalter 32 die letzten 5 Jahre)?", "diagnosisLabel": "{number}. Diagnose angeben"}, "missingTeeth": {"question": "Gibt es fehlende Zähne (außer den Weisheitszähnen oder bei Lückenschluss), die noch nicht ersetzt sind?", "label": "Anzahl der fehlenden Zähne"}, "dentalReplacements": {"question": "Zahnersatz (ersetzte oder überkronte Zähne, einschließlich Implantate, Brücken, Kronen, Teilkronen und Prothesen) vorhanden?", "label": "Anzahl der ersetzten Zähne", "helperText": "Bei Brücken sind alle betroffenen Zähne einzeln zu zählen, inklusive Anker- bzw. Pfeilerzähnen."}}, "validation": {"errorSummaryTitle": "Bitte korrigieren Sie folgende Fehler", "selectOption": "Bitte wählen Sie eine Option", "diagnosisRequired": "Diagnose darf nicht leer sein", "maxDiagnoses": "Maximal 5 Diagnosen erlaubt", "professionRequired": "Bitte wählen Si<PERSON> einen Beruf aus", "heightMustBeNumber": "<PERSON><PERSON><PERSON><PERSON> muss eine Zahl sein", "heightMustBeInteger": "<PERSON><PERSON><PERSON><PERSON> muss eine ganze <PERSON> sein", "heightMin": "<PERSON><PERSON><PERSON><PERSON> muss mindestens 50 cm sein", "heightMax": "G<PERSON><PERSON><PERSON> darf maximal 250 cm sein", "weightMustBeNumber": "<PERSON><PERSON><PERSON>t muss eine Zahl sein", "weightMustBeInteger": "G<PERSON><PERSON>t muss eine ganze <PERSON> sein", "weightMin": "<PERSON><PERSON><PERSON><PERSON> muss <PERSON>estens 20 kg sein", "weightMax": "G<PERSON>icht darf maximal 300 kg sein", "teethMustBeNumber": "<PERSON><PERSON><PERSON> muss eine Zahl sein", "teethMustBeInteger": "<PERSON><PERSON><PERSON> muss eine ganze <PERSON> sein", "teethMin": "<PERSON><PERSON><PERSON> kann nicht negativ sein", "teethMax": "Maximal 32 Zähne möglich", "dentalMustBeNumber": "<PERSON><PERSON><PERSON> muss eine Zahl sein", "dentalMustBeInteger": "<PERSON><PERSON><PERSON> muss eine ganze <PERSON> sein", "dentalMin": "<PERSON><PERSON><PERSON> kann nicht negativ sein", "dentalMax": "Maximal 32 Zähne möglich", "outpatientDiagnosisRequired": "<PERSON>te geben Sie mindestens eine ambulante Diagnose an oder wählen Sie \"Nein\"", "inpatientDiagnosisRequired": "Bitte geben Sie mindestens eine stationäre Diagnose an oder wählen Sie \"Nein\""}, "fields": {"previousInsurance": "Vorversicherung", "profession": "<PERSON><PERSON><PERSON>", "height": "Größe", "weight": "Gewicht", "outpatientDiagnosis": "Ambulante Diagnose", "outpatientDiagnoses": "Ambulante Diagnosen", "inpatientDiagnosis": "Station<PERSON>re Diagnose", "inpatientDiagnoses": "Stationäre Diagnosen", "missingTeeth": "Fehlende Zähne", "dentalReplacements": "Zahnersatz"}, "professions": {"employee": "<PERSON><PERSON><PERSON><PERSON>", "civil_servant": "<PERSON><PERSON><PERSON>", "self_employed": "Selbstständig", "student": "Student", "retiree": "<PERSON><PERSON><PERSON>", "unemployed": "Arbeitslos", "homemaker": "<PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "craftsman": "Handwerker", "construction_worker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doctor": "Arzt/Ärztin", "teacher": "Lehrer/in", "engineer": "Ingenieur/in", "merchant": "Kaufmann/Ka<PERSON>u", "other": "Sonstiges"}}}