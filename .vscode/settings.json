{
  // Code formatting and linting
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.removeUnusedImports": "never",
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // TypeScript specific settings
  "typescript.preferences.importModuleSpecifier": "non-relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.includePackageJsonAutoImports": "auto",

  // Editor behavior
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,

  // File associations
  "files.associations": {
    "*.css": "tailwindcss",
    "*.scss": "scss"
  },

  // Tailwind CSS IntelliSense
  "tailwindCSS.includeLanguages": {
    "typescript": "typescript",
    "typescriptreact": "typescriptreact"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\\\(([^)]*)\\\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\\\(([^)]*)\\\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],

  // ESLint settings
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": false,
  "eslint.useFlatConfig": true,

  // Search and file explorer
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.next": true,
    "**/coverage": true,
    "**/.nyc_output": true,
    "**/src/generated": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/.next": true
  },

  // Git settings
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,

  // GitHub Copilot
  "github.copilot.chat.commitMessageGeneration.instructions": [
    {
      "text": "<type>(<scope>): <short summary>\\n\\n- <bullet point describing key change 1>\\n- <bullet point describing key change 2>\\n- <bullet point describing key change 3>\\n\\n# Optional: Add extra context, migration notes, or links if needed.\\n# BREAKING CHANGE: <describe breaking change and migration steps>"
    }
  ],

  // Emmet settings for React/JSX
  "emmet.includeLanguages": {
    "typescript": "typescriptreact",
    "javascript": "javascriptreact"
  },

  // Auto-save
  "files.autoSave": "onFocusChange"
}
