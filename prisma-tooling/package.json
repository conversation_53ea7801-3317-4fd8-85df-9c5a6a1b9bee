{"name": "prisma-tooling", "private": true, "scripts": {"db:generate": "prisma generate --schema=./prisma/schema.mongo.prisma && prisma generate --schema=./prisma/schema.postgres.prisma", "db:migrate:mongo": "prisma db push --schema ./prisma/schema.mongo.prisma", "seed": "ts-node prisma/seed.ts", "db:deploy": "npm run db:generate && prisma migrate deploy --schema=./prisma/schema.postgres.prisma && npm run db:migrate:mongo && npm run seed"}, "devDependencies": {"prisma": "6.16.2"}}