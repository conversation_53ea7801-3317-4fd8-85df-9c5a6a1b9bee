{"name": "prisma-tooling", "private": true, "scripts": {"db:generate": "prisma generate --schema=./prisma/schema.prisma && prisma generate --schema=./prisma-mongo-db/schema.prisma", "db:migrate:mongo": "prisma db push --schema ./prisma-mongo-db/schema.prisma", "db:deploy": "npm run db:generate && prisma migrate deploy --schema=./prisma/schema.prisma && npm run db:migrate:mongo"}, "devDependencies": {"prisma": "6.16.2"}}