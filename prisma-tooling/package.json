{"name": "prisma-tooling", "private": true, "scripts": {"seed": "ts-node prisma/seed.ts", "db:deploy": "npm run prisma:generate && prisma migrate deploy --schema=./prisma/schema.prisma && npm run seed", "prisma:generate": "prisma generate --schema=prisma/schema.prisma"}, "devDependencies": {"prisma": "6.16.2", "ts-node": "10.9.2", "@mainamiru/prisma-types-generator": "^1.1.14", "@types/chance": "^1.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "chance": "^1.1.13", "typescript": "^5"}}