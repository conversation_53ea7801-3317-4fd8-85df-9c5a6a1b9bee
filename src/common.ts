export const getExpectedDocumentTypes = () => {
    return expectedDocumentTypesDe;
};

const expectedDocumentTypesDe = [
    'Schadensmeldung',
    'Unfallbericht',
    'Polizeibericht',
    'Kostenvoranschlag',
    'Reparaturrechnung',
    'Reparaturbestätigung',
    'Fotos vom Schaden',
    'Gutachten',
    'Arztbericht',
    'Krankenhausrechnung',
    'Rezept / Arzneimittelbeleg',
    'Totenschein',
    'Versicherungsvertrag',
    'Zahlungsnachweis',
    'Korrespondenz mit Versicherung',
    'Zeugenaussage',
    'Abschlepprechnung',
    'Mietwagenrechnung',
    'Kontoauszug',
    'Verlustmeldung',
    'Diebstahlanzeige',
    'Bestätigung über Eigenleistung',
    'Arbeitsunfähigkeitsbescheinigung',
    'Schadensformular der Versicherung',
    'Zahlungsverpflichtung',
    'Abtretungserklärung',
    'Reparaturfreigabe',
    'Kündigungsbestätigung',
];

// const expectedDocumentTypesEn = [
//   'Claim Report / Loss Report',
//   'Accident Report',
//   'Police Report',
//   'Cost Estimate / Quotation',
//   'Repair Invoice',
//   'Repair Confirmation',
//   'Photos of the Damage',
//   'Expert Report / Appraisal',
//   'Medical Report',
//   'Hospital Invoice',
//   'Prescription / Pharmacy Receipt',
//   'Death Certificate',
//   'Insurance Policy',
//   'Proof of Payment',
//   'Correspondence with Insurance',
//   'Witness Statement',
//   'Towing Invoice',
//   'Rental Car Invoice',
//   'Bank Statement',
//   'Loss Report',
//   'Theft Report',
//   'Confirmation of Own Work / DIY Proof',
//   'Sick Note / Medical Certificate',
//   'Insurance Claim Form',
//   'Payment Obligation Document',
//   'Assignment of Claim',
//   'Repair Authorization',
//   'Cancellation Confirmation',
// ];
