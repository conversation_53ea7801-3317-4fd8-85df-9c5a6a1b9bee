// src/auth.ts
import { decodeJwt } from 'jose';
import NextAuth from 'next-auth';
import type { JWT } from 'next-auth/jwt';
import Keycloak from 'next-auth/providers/keycloak';

function normalizeIssuer(issuer?: string) {
    if (!issuer) return '';
    return issuer.endsWith('/') ? issuer.slice(0, -1) : issuer;
}

export const { handlers, auth, signIn, signOut } = NextAuth({
    providers: [
        Keycloak({
            issuer: process.env.AUTH_KEYCLOAK_ISSUER,
            clientId: process.env.AUTH_KEYCLOAK_ID!,
            clientSecret: process.env.AUTH_KEYCLOAK_SECRET!,
            authorization: { params: { scope: 'openid profile email offline_access' } },
        }),
    ],
    session: { strategy: 'jwt' },

    // ⛔ THIS decides who can pass the middleware
    callbacks: {
        authorized({ auth, request }) {
            const { pathname, origin, href } = request.nextUrl;

            // Public routes: exact '/' OR any path under these prefixes.
            const publicPrefixes = ['/api/auth', '/login', '/public']; // add more if needed
            if (pathname === '/') return true;
            if (publicPrefixes.some((p) => pathname === p || pathname.startsWith(`${p}/`))) {
                return true;
            }

            // If authed, allow
            if (auth?.user) return true;

            // Not authed: redirect to /login with callbackUrl
            const url = new URL('/login', origin);
            url.searchParams.set('callbackUrl', href);
            return Response.redirect(url);
        },

        // --- your existing jwt/session callbacks (unchanged) ---
        async jwt({ token, account }) {
            type Ext = {
                access_token?: string;
                refresh_token?: string;
                expires_at?: number;
                roles?: string[];
                token_error?: string;
                agentNumber?: string;
                agencyNumber?: string;
            };
            const t = token as typeof token & Ext;

            const applyFromAccessToken = (accessToken: string) => {
                try {
                    const decoded = decodeJwt(accessToken) as Record<string, any>;
                    const realmRoles: string[] = decoded?.realm_access?.roles ?? [];
                    const clientRoles: string[] = Object
                        .values(decoded?.resource_access ?? {})
                        .flatMap((v: any) => v?.roles ?? []);
                    t.roles = Array.from(new Set([...realmRoles, ...clientRoles]));
                    if (!t.expires_at && typeof decoded?.exp === 'number') {
                        t.expires_at = decoded.exp;
                    }
                    if (decoded.agentNumber) t.agentNumber = decoded.agentNumber;
                    if (decoded.agencyNumber) t.agencyNumber = decoded.agencyNumber;
                } catch { /* empty */ }
            };

            if (account) {
                t.access_token = account.access_token as any;
                t.refresh_token = (account.refresh_token as any) ?? t.refresh_token;
                t.expires_at = (account.expires_at as number | undefined) ?? t.expires_at;
                if (t.expires_at && t.expires_at > 10_000_000_000) {
                    t.expires_at = Math.floor(t.expires_at / 1000);
                }
                if (t.access_token) applyFromAccessToken(t.access_token);
                if ((!t.agentNumber || !t.agencyNumber) && account.id_token) {
                    try {
                        const idDecoded = decodeJwt(account.id_token) as Record<string, any>;
                        if (idDecoded.agentNumber) t.agentNumber = idDecoded.agentNumber;
                        if (idDecoded.agencyNumber) t.agencyNumber = idDecoded.agencyNumber;
                    } catch { /* empty */ }
                }
            }

            const now = Math.floor(Date.now() / 1000);
            const skew = 60;
            if (t.access_token && !t.expires_at) applyFromAccessToken(t.access_token);
            if (t.expires_at && t.expires_at > 10_000_000_000) {
                t.expires_at = Math.floor(t.expires_at / 1000);
            }
            const closeToExpiry = !!t.expires_at && t.expires_at - skew <= now;
            const missingAccess = !t.access_token && !!t.refresh_token;
            const shouldRefresh = !!t.refresh_token && (closeToExpiry || missingAccess);

            if (shouldRefresh) {
                try {
                    const issuer = normalizeIssuer(process.env.AUTH_KEYCLOAK_ISSUER);
                    const body = new URLSearchParams({
                        grant_type: 'refresh_token',
                        client_id: process.env.AUTH_KEYCLOAK_ID!,
                        client_secret: process.env.AUTH_KEYCLOAK_SECRET!,
                        refresh_token: String(t.refresh_token),
                    });
                    const res = await fetch(`${issuer}/protocol/openid-connect/token`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body,
                    });
                    const text = await res.text();
                    if (!res.ok) throw new Error(text);
                    const data = JSON.parse(text);
                    t.access_token = data.access_token ?? t.access_token;
                    t.refresh_token = data.refresh_token ?? t.refresh_token;
                    t.expires_at = typeof data.expires_in === 'number' ? now + data.expires_in : undefined;
                    if (t.access_token) applyFromAccessToken(t.access_token);
                    delete t.token_error;
                } catch (e) {
                    console.error('[Auth] Refresh exception', e);
                    delete t.access_token;
                    delete t.expires_at;
                    t.roles = [];
                    t.token_error = 'RefreshAccessTokenError';
                }
            }

            return t;
        },

        async session({ session, token }) {
            const t = token as JWT & {
                access_token?: string;
                roles?: string[];
                token_error?: string;
                agentNumber?: string;
                agencyNumber?: string;
            };
            session.accessToken = t.access_token;
            session.roles = t.roles ?? [];
            (session as any).tokenError = (t as any).token_error;
            session.agentNumber = t.agentNumber;
            session.agencyNumber = t.agencyNumber;
            return session;
        },
    },
});
