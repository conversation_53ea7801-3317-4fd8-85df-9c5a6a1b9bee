import {createTranslator, Messages, NamespaceKeys, NestedKeyOf} from "use-intl"

export type Translator<NestedKey extends NamespaceKeys<Messages, NestedKeyOf<Messages>> = never> =
    ReturnType<typeof createTranslator<Messages, NestedKey>>;

export type TipStatus = "invited" | "pending" | "open" | "accepted" | "rejected";

export const tipStatuses: TipStatus[] = ['invited', 'pending', 'open', 'accepted', 'rejected']

export const tipStatusOrder: Record<TipStatus, number> = {
    invited: 0,
    pending: 1,
    open: 2,
    accepted: 3,
    rejected: 4,
};

export type Order = "asc" | "desc";

export type CustomerOffer = {
    id: string | number
    productName: string
    premium?: {
        amount: number,
        span: string
    }
    status: TipStatus
    offerPdf?: string
    policyPdf?: string
    expiresAt?: string
    risk?: string
    productExtensions?: string[]
}

export type Referral = {
    id: string;
    tipId: number
    status: TipStatus
    createdAt: string
    customerName: string;
    customerEmail: string;
    productName: string
    expiresAt?: string
    previousContractNumber?: string
    commission?: number
    premium?: number
    productExtensions?: string[]
};
