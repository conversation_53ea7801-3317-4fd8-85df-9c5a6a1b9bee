:root {
  /* This SHOULD be set to 14 because it's the default base size
    for MUI. This way whatever value is set in custom MUI theme
    it will be calculated correctly. Maybe setting it to 16 also
    works, but not setting anything results in very weird behavior.
  */
  font-size: 14px;

  --gray-background: #f4f4f4;
  --gray-light: #9e9e9e;

  --background: #ffffff;
  --foreground: #171717;

  --teal: #2faa97;
  --teal-dark: #34746a;
  --teal-light: #c6f3e4;

  --toggle-on: var(--teal-dark);
  --toggle-off: #a1a1a1;
  --toggle-border: #b2b2b2;
  --toggle-fill: #ffffff;

  --footer-background: var(--gray-background);
  --footer-text-color: #212121;

  --header-background: white;

  --navbar-background: var(--teal);

  --avatar-circle-color: var(--teal);
  --avatar-size: 50px;
  --avatar-font-size: 22px;

  --card-background: var(--gray-background);

  --startcard-background: var(--gray-background);
  --startcard-icon-color: var(--teal);
  --startcard-title-color: var(--teal-dark);
  --startcard-body-color: #212121;

  --dark-button-hover: #80b6b0;

  --statistic-widget-background: var(--teal-light);
  --statistic-widget-counter-color: var(--teal);
  --statistic-widget-headline-color: var(--teal);

  --case-status-indicator-background-offen: #fff0a4;
  --case-status-indicator-background-abgelehnt: #ffd3d3;
  --case-status-indicator-background-fertig: #a2f6e9;
  --case-status-indicator-background-policiert: #b3f4af;

  --modal-headline-color: var(--teal);
  --modal-warning-icon-color: #ffe55f;

  --progressbar-background: #d8d8d8;
  --progressbar-fill: var(--teal-dark);

  --error-red: #ff7979;

  --data-input-step-incomplete: #dc9811;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
  min-height: 100vh;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@keyframes blinkRed {
  0%,
  100% {
    background-color: inherit;
  }
  50% {
    background-color: red;
    color: white;
  }
}

.blink-red {
  animation: blinkRed 0.5s ease-in-out 3;
}

.array-delete-start ~ *:has(~ .array-delete-end):not(.array-delete-ui) {
  /* background: red; */
  /* animation: blinkRed 2s ease-in-out infinite; */
  background-color: rgba(255, 0, 0, 0.3);
  border: solid 2px red;
}
