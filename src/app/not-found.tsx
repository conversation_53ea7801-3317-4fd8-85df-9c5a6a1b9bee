'use client';

import { Box, Button, Container, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import Footer from '@/components/layout/footer';
import Header from '@/components/layout/header';

export default function NotFoundPage() {
  const theme = useTheme();
  const t = useTranslations();

  return (
    <>
      <Header />

      <Container
        maxWidth={false}
        disableGutters
        sx={{
          backgroundColor: theme.palette.primary.main,
          minHeight: '80vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
        }}
      >
        <Typography
          variant="h1"
          component="h1"
          sx={{
            fontSize: { xs: '6rem', md: '10rem' },
            fontWeight: 800,
            color: '#fff',
          }}
        >
          404
        </Typography>
        <Box
          sx={{
            display: 'flex',
            mb: 4,
            mt: 2,
          }}
        >
          <Typography
            variant="body1"
            component="p"
            sx={{ fontWeight: 600, color: '#fff' }}
          >
            {t('not_found_404_page.sorry')}
          </Typography>
          <Typography variant="body1" sx={{ pl: 1, color: '#fff' }}>
            {t('not_found_404_page.page_not_found')}
          </Typography>
        </Box>

        <Button
          component={Link}
          href="/start"
          variant="contained"
          sx={{
            backgroundColor: theme.palette.primary.dark,
            color: '#fff',
            px: 4,
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
            borderRadius: 2,
            textTransform: 'none',
            '&:hover': {
              backgroundColor: theme.palette.primary.light,
            },
          }}
        >
          {t('not_found_404_page.back_to_start')}
        </Button>
      </Container>
      <Footer />
    </>
  );
}
