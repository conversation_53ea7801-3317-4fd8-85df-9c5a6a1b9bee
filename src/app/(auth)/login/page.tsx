import { redirect } from 'next/navigation';

import { LoginForm } from '@/modules/auth/components/LoginForm';
import { auth } from '@/modules/auth/server/auth';

export default async function LoginPage() {
  // Check if user is already authenticated
  const session = await auth();

  if (session) {
    // If user has accepted terms, redirect to overview
    if (session.user?.termsAndConditionsAccepted === true) {
      redirect('/start');
    }
    // If user hasn't accepted terms, redirect to welcome
    else if (session.user?.termsAndConditionsAccepted === false) {
      redirect('/welcome');
    }
    // Default redirect to overview if terms status is undefined
    else {
      redirect('/start');
    }
  }

  return <LoginForm />;
}
