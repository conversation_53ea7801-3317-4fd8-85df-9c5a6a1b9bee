// import { getUserLocale } from '@/i18n/i18n-actions'
import '@/styles/globals.css';

import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from 'next/font/google';
import { SessionProvider } from 'next-auth/react';
import { NextIntlClientProvider } from 'next-intl';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import CookieConsent from '@/components/CookieConsent';
import MUIThemeProvider from '@/components/providers/ThemeProvider';
import { TRPCReactProvider } from '@/trpc/client';
import { fetchPublicVariables } from '@/utils/publicVariables';

const geistSans = Geist({ variable: '--font-geist-sans', subsets: ['latin'] });

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.APP_URL || 'http://localhost:3000'),
  title: 'KVCare',
  description: 'KVCare',
  openGraph: { title: 'KVCare', description: 'KVCare' },
  other: {
    language: 'de',
    'apple-mobile-web-app-title': 'KVcare',
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/icon0.svg', type: 'image/svg+xml' },
      { url: '/icon1.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [{ url: '/apple-icon.png', sizes: '180x180', type: 'image/png' }],
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    title: 'KVcare',
    statusBarStyle: 'default',
  },
};

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const publicVariables = await fetchPublicVariables();
  return (
    <html lang={'de'}>
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <SessionProvider>
          <TRPCReactProvider envPublic={publicVariables}>
            <NuqsAdapter>
              <MUIThemeProvider>
                <NextIntlClientProvider>
                  {children}
                  <CookieConsent />
                </NextIntlClientProvider>
              </MUIThemeProvider>
            </NuqsAdapter>
          </TRPCReactProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
