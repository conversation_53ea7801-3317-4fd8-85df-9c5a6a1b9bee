import { authGuard } from '@/modules/auth/lib/auth-libs';
import { ChatView } from '@/modules/chat/ui/view/chat-view';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

interface ChatPageProps {
  params: Promise<{ caseId: string }>;
}

export default async function ChatPage({ params }: ChatPageProps) {
  await authGuard(`/chat/${(await params).caseId}`);

  const { caseId } = await params;
  const caseIdNum = parseInt(caseId, 10);

  // Prefetch data
  prefetch(trpc.chat.getStreamToken.queryOptions());
  prefetch(trpc.chat.getCasesForChat.queryOptions());

  return (
    <HydrateClient>
      <ChatView selectedCaseId={isNaN(caseIdNum) ? undefined : caseIdNum} />
    </HydrateClient>
  );
}
