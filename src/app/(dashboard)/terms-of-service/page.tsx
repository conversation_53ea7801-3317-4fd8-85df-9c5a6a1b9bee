import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

export default function TermsOfServicePage() {
  const t = useTranslations();
  const content: string[] = t.raw('terms.content');

  return (
    <Box
      sx={{
        p: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 4,
        maxWidth: '800px',
        mx: 'auto',
        mt: 6,
      }}
    >
      {/* Header */}
      <Typography
        variant="h4"
        component="h1"
        sx={{ fontWeight: 'bold', mb: 1 }}
      >
        {t('terms.title')}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {t('terms.subtitle')}
      </Typography>

      {/* Content */}
      <Box>
        {content.map((paragraph, i) => (
          <Typography key={i} variant="body1" paragraph>
            {paragraph}
          </Typography>
        ))}
      </Box>
    </Box>
  );
}
