import { Box } from '@mui/material';
import { type SearchParams } from 'nuqs';

import { authGuard } from '@/modules/auth/lib/auth-libs';
import { loadCustomerSearchParams } from '@/modules/customer/types/customer-params';
import { CustomerSearchFilters } from '@/modules/customer/ui/components/CustomerSearchFilters';
import { AdminCustomersView } from '@/modules/customer/ui/views/admin-customers-view';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

interface AdminCustomersPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function AdminCustomersPage({
  searchParams,
}: AdminCustomersPageProps) {
  await authGuard('/admin/customers');

  // Check if user is admin - redirects if not
  //   await requireAdmin();

  const filters = await loadCustomerSearchParams(searchParams);
  prefetch(trpc.customers.getMany.queryOptions({ ...filters }));

  return (
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Search Filters - Server Rendered */}
      <CustomerSearchFilters />

      {/* Customer Data - Client Rendered */}
      <HydrateClient>
        <AdminCustomersView />
      </HydrateClient>
    </Box>
  );
}
