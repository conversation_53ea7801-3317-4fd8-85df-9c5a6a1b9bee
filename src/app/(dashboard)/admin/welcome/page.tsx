import { authGuard } from '@/modules/auth/lib/auth-libs';
import { WelcomeView } from '@/modules/welcome/ui/views/welcome-view';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

export default async function WelcomePage() {
  await authGuard('/admin/welcome');

  prefetch(trpc.user.getCurrentUser.queryOptions());

  return (
    <HydrateClient>
      <WelcomeView />
    </HydrateClient>
  );
}
