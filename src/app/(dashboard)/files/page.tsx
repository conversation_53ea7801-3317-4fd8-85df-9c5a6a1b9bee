'use client';
import {
  Button,
  <PERSON>ack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import Navbar from '@/components/layout/navbar';
import { KvModal } from '@/components/modal/modal';
import KvcProgressBar from '@/components/progressbar/progressBar';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';
import { useTRPC } from '@/trpc/client';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export default function DateneingabePerson() {
  const params = useParams();
  useAuthGuard(`/files}`);

  const [formProgressPercent, setFormProgressPercent] = useState(0);
  const [blobDownloadUrl, setBlobDownloadUrl] = useState<string | null>(null);
  const [blobFileName, setBlobFileName] = useState<string | null>(null);
  const t = useTranslations();
  const trpc = useTRPC();

  const {
    data: files,
    refetch: refetchCaseGroupData,
    isRefetching: caseGroupDataIsLoading,
    isLoadingError: caseGroupDataLoadingError,
  } = useSuspenseQuery(trpc.files.getFilesList.queryOptions());

  const { mutateAsync: downloadFile } = useMutation(
    trpc.files.downloadFile.mutationOptions({
      onSuccess: (result) => {
        if (result !== null && result.blob.length > 0) {
          const uint8Array = new Uint8Array(result.blob);
          const blob = new Blob([uint8Array], { type: 'application/pdf' });
          const url = URL.createObjectURL(blob);
          setBlobDownloadUrl(url);
          setBlobFileName(result.fileName);

          //             const a = document.createElement("a");
          //   a.href = url;
          //   a.download = result.fileName;
          //   a.click();

          //   URL.revokeObjectURL(url);
        }
      },
      onError: (error) => {
        alert(`download error: ${JSON.stringify(error)}`);
        console.log('error', error);
      },
    })
  );

  const { mutateAsync: getDownloadFileUrl } = useMutation(
    trpc.files.getDownloadFileUrl.mutationOptions({
      onSuccess: (result) => {
        if (
          result !== null &&
          typeof result.url === 'string' &&
          typeof result.fileName === 'string'
        ) {
          setBlobDownloadUrl(result.url);
          setBlobFileName(result.fileName);
        }
      },
      onError: (error) => {
        alert(`download error: ${JSON.stringify(error)}`);
        console.log('error', error);
      },
    })
  );

  return (
    <>
      <Navbar
        contentBelow={
          <KvcProgressBar fillPercent={formProgressPercent}></KvcProgressBar>
        }
      >
        <Stack>
          <Typography
            variant="body1"
            component="h1"
            color="white"
            // fontWeight={700}
            justifyContent="center"
            textAlign="center"
          >
            {t.rich(
              'components.data_input_frame.case',
              intlTranslationRichHelper
            )}
            : {params.id}
          </Typography>
        </Stack>
      </Navbar>

      <Stack
        direction="column"
        sx={{
          margin: 10,
        }}
      >
        <Table
          sx={
            {
              // '& td': { color: 'white' }
            }
          }
        >
          <TableHead>
            <TableRow>
              <TableCell>id</TableCell>
              <TableCell>case</TableCell>
              <TableCell>name</TableCell>
              <TableCell>user</TableCell>
              <TableCell>date</TableCell>
              <TableCell>backend</TableCell>
              <TableCell>s3</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {files.map((file) => {
              return (
                <TableRow
                  key={file.attachmentId}
                  sx={{
                    backgroundColor: 'var(--gray-background)',
                    borderRadius: '50%',
                    marginBottom: 10,
                  }}
                >
                  <TableCell>{file.attachmentId}</TableCell>
                  <TableCell>{file.caseId}</TableCell>
                  <TableCell>{file.fileName}</TableCell>
                  <TableCell>{file.uploadedBy}</TableCell>
                  <TableCell>
                    {file.uploadedAt?.toLocaleString('de-de', {
                      timeStyle: 'short', // e.g. 8:24 PM
                      dateStyle: 'short', // e.g. 9/29/25
                    })}
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => {
                        downloadFile({ attachmentId: file.attachmentId });
                      }}
                    >
                      Download from backend
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => {
                        getDownloadFileUrl({ attachmentId: file.attachmentId });
                      }}
                    >
                      Download from s3
                    </Button>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </Stack>

      {blobDownloadUrl !== null && blobFileName !== null && (
        <KvModal
          onClose={() => {
            setBlobDownloadUrl(null);
            setBlobFileName(null);
          }}
        >
          <Button
            component="a"
            href={blobDownloadUrl}
            download={blobFileName}
            variant="contained"
            color="primary"
            sx={{ fontWeight: 700 }}
            onClick={() => {
              setBlobDownloadUrl(null);
              setBlobFileName(null);
            }}
          >
            {blobFileName} speichern
          </Button>
        </KvModal>
      )}
    </>
  );
}
