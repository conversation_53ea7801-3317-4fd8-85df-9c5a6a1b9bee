'use client';
import { Stack, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';

import { CustomerCreateForm } from '@/components/forms/CustomerCreateForm';
import Navbar from '@/components/layout/navbar';
import { KvModal } from '@/components/modal/modal';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';

export default function NewCase() {
  useAuthGuard('/newcase');
  const router = useRouter();

  return (
    <>
      <Navbar>
        <Typography>New Customer</Typography>
      </Navbar>

      <KvModal
        sx={{ padding: 0 }}
        onClose={() => {
          router.push('/start');
        }}
      >
        <Stack
          direction="row"
          display="flex"
          justifyContent="center"
          sx={{ width: '100%' }}
          flexWrap="wrap"
        >
          <Stack
            sx={{
              width: { xs: '100%', md: '50%' },
              padding: 10,
              borderRight: { xs: 0, md: 1 },
              borderBottom: { xs: 1, md: 0 },
              borderColor: (theme) => theme.palette.separator.light,
            }}
          >
            <Typography
              variant="h5"
              sx={{
                width: '100%',
                textAlign: 'center',
                // color: 'var(--teal)',
                fontWeight: 700,
                marginTop: 'auto',
                marginBottom: 'auto',
              }}
            >
              Kundenimport bald verfügbar
            </Typography>
          </Stack>
          {/* <CustomerImportForm
            sx={{
              paddingLeft: 5,
              paddingTop: 10,
              paddingBottom: 10,
              minWidth: '350px',
              flexGrow: 1,
            }}
            customer={importedCustomer}
          ></CustomerImportForm> */}
          <CustomerCreateForm
            sx={{
              paddingRight: 5,
              paddingTop: 10,
              paddingBottom: 10,
              minWidth: '350px',
              flexGrow: 1,
            }}
          />
        </Stack>
      </KvModal>
    </>
  );
}
