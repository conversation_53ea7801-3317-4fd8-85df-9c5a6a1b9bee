import { Stack } from '@mui/material';

import Navbar from '@/components/layout/navbar';
import { authGuard } from '@/modules/auth/lib/auth-libs';
import { CaseNavbarTitle } from '@/modules/case/ui/components/CaseNavbarTitle';
import { CaseDetailsView } from '@/modules/case/ui/views/CaseDetailsView';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

export default async function CaseDetailsPage({
  params,
}: {
  params: Promise<{ caseNumber: string }>;
}) {
  await authGuard('/cases');
  const { caseNumber } = await params;

  prefetch(trpc.cases.getWithAllRelations.queryOptions({ number: caseNumber }));

  return (
    <HydrateClient>
      <Navbar showNewCaseButton={true} showOverviewButton={true}>
        <CaseNavbarTitle caseNumber={caseNumber} />
      </Navbar>
      <Stack
        direction="column"
        display="flex"
        justifyContent="center"
        width="100%"
        paddingLeft={10}
        paddingRight={10}
      >
        <Stack
          direction="column"
          maxWidth="100%"
          width={1200}
          marginLeft="auto"
          marginRight="auto"
        >
          {/* <div>case number: {caseNumber}</div> */}

          <CaseDetailsView caseNumber={caseNumber} />
        </Stack>
      </Stack>
    </HydrateClient>
  );
}
