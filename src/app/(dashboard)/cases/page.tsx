import WindowIcon from '@mui/icons-material/Window';
import { type SearchParams } from 'nuqs';

import Navbar from '@/components/layout/navbar';
import { authGuard } from '@/modules/auth/lib/auth-libs';
import { loadCaseSearchParams } from '@/modules/case/types/case-params';
import { CasesGroupView } from '@/modules/case/ui/views/CasesGroupView';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

interface AgentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function CasesPage({ searchParams }: AgentsPageProps) {
  await authGuard('/cases');

  const filters = await loadCaseSearchParams(searchParams);

  prefetch(trpc.cases.getManyWithGroup.queryOptions({ ...filters }));

  return (
    <>
      <Navbar
        elementBeforeText={
          <WindowIcon sx={{ color: 'white', marginRight: 1 }} />
        }
        textKey="pages.cases.navbar_headline"
        textSX={{
          color: 'white',
          fontWeight: 700,
        }}
        showNewCaseButton={true}
      />
      <HydrateClient>
        <CasesGroupView />
      </HydrateClient>
    </>
  );
}
