'use client';
import { But<PERSON>, Stack, Typography } from '@mui/material';
import { useState } from 'react';

import { DocumentsListTest } from '@/components/documents/documentsList';
import Navbar from '@/components/layout/navbar';
import { KvModal } from '@/components/modal/modal';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';

export default function DocumentsListTestPage() {
  useAuthGuard('/documents');

  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <Navbar>
        <Typography>Documents list test page</Typography>
      </Navbar>
      <Stack direction="column" sx={{ padding: 5 }} gap={1}>
        <Button
          onClick={() => {
            setModalOpen(true);
          }}
        >
          Open modal
        </Button>
        <DocumentsListTest />

        {modalOpen && (
          <KvModal
            onClose={() => {
              setModalOpen(false);
            }}
          >
            <DocumentsListTest />
          </KvModal>
        )}
      </Stack>
    </>
  );
}
