import AppLayout from '@/components/layout/layout';

export default async function DashboardLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  //   const session = await auth();
  //   const publicVariables = await fetchPublicVariables();

  //   const streamToken = await chatRouterCaller.getStreamToken();

  return (
    <AppLayout>
      {/* <ChatProvider envPublic={publicVariables} streamToken={streamToken}> */}
      {children}
      {/* </ChatProvider> */}
    </AppLayout>
  );
}
