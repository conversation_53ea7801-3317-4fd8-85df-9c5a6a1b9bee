'use client';
import { Stack, Typography } from '@mui/material';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import Navbar from '@/components/layout/navbar';
import { KvModal } from '@/components/modal/modal';
import { DocumentUpload } from '@/components/upload/DocumentUpload';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';

export default function DocumentsListTestPage() {
  const params = useParams();
  useAuthGuard(`/upload/case/${params.id}`);

  const t = useTranslations();
  //   const router = useRouter();
  const id =
    typeof params.id === 'string' &&
    params.id.trim().length > 0 &&
    params.id !== 'undefined'
      ? params.id
      : undefined;

  const caseId =
    typeof id === 'string' && !isNaN(parseInt(id)) ? parseInt(id) : null;

  const [modalOpen, setModalOpen] = useState(true);

  return (
    <>
      <Navbar>
        <Typography color="white">Documents upload test page</Typography>
      </Navbar>
      <Stack direction="column" sx={{ padding: 5 }} gap={1}>
        {typeof caseId === 'number' && (
          <KvModal
            onClose={() => {
              setModalOpen(false);
            }}
          >
            <DocumentUpload caseId={caseId} />
          </KvModal>
        )}
      </Stack>
    </>
  );
}
