'use client';
import { But<PERSON>, Stack, Typography } from '@mui/material';
import { useState } from 'react';

import Navbar from '@/components/layout/navbar';
import { KvModal } from '@/components/modal/modal';
import { DocumentUpload } from '@/components/upload/DocumentUpload';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';

export default function DocumentsListTestPage() {
  useAuthGuard('/upload');

  const [modalOpen, setModalOpen] = useState(false);

  return (
    <>
      <Navbar>
        <Typography color="white">Documents upload test page</Typography>
      </Navbar>
      <Stack direction="column" sx={{ padding: 5 }} gap={1}>
        <Button
          onClick={() => {
            setModalOpen(true);
          }}
        >
          Open modal
        </Button>
        <DocumentUpload caseId={-1} />

        {modalOpen && (
          <KvModal
            onClose={() => {
              setModalOpen(false);
            }}
          >
            <DocumentUpload caseId={-1} />
          </KvModal>
        )}
      </Stack>
    </>
  );
}
