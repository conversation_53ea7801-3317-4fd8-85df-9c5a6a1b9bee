import { Box, CircularProgress, Stack, Typography } from '@mui/material';
import { getTranslations } from 'next-intl/server';

import Navbar from '@/components/layout/navbar';
import { VorgangListItem } from '@/components/lists/vorgangListItem';
import StatistikWidget from '@/components/statistik/statistikWidget';
import { authGuard } from '@/modules/auth/lib/auth-libs';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export default async function Overview() {
  await authGuard('/overview');

  const t = await getTranslations();
  const cases: any[] = [];

  return (
    <>
      <Navbar showNewCaseButton={true} showOverviewButton={true}>
        <Typography
          variant="body1"
          color="white"
          fontWeight={700}
          justifyContent="center"
          textAlign="center"
        >
          {t.rich('pages.overview.navbar-headline', intlTranslationRichHelper)}
        </Typography>
      </Navbar>
      <StatistikWidget />

      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          flexWrap: 'wrap',
          gap: 3,
          marginTop: '20px',
          marginBottom: '20px',
        }}
      >
        <Stack
          direction="column"
          display="flex"
          justifyContent="center"
          spacing={1}
        >
          {cases.map((c, index) => (
            <VorgangListItem case={c} key={`${c.caseId}-${index}`} />
          ))}
        </Stack>
      </Box>
      <Stack padding={'12px'}>
        <CircularProgress
          size={75}
          sx={{
            marginLeft: 'auto',
            marginRight: 'auto',
            // padding: '20px',
          }}
        />
      </Stack>
    </>
  );
}
