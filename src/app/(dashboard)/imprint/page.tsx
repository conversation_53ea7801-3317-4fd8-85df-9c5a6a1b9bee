import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

export default function ImprintPage() {
  const t = useTranslations('imprint');

  const sections: {
    title: string;
    paragraphs: string[];
  }[] = t.raw('sections');

  return (
    <Box
      sx={{
        p: 4,
        display: 'flex',
        flexDirection: 'column',
        gap: 4,
        maxWidth: '800px',
        mx: 'auto',
        mt: 6,
      }}
    >
      {/* Header */}
      <Typography
        variant="h4"
        component="h1"
        sx={{ fontWeight: 'bold', mb: 1 }}
      >
        {t('title')}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {t('subtitle')}
      </Typography>

      {/* Sections */}
      {sections.map((section, index) => (
        <Box key={index} sx={{ mt: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            {section.title}
          </Typography>
          {section.paragraphs.map((p, i) => (
            <Typography key={i} variant="body1" paragraph>
              {p}
            </Typography>
          ))}
        </Box>
      ))}
    </Box>
  );
}
