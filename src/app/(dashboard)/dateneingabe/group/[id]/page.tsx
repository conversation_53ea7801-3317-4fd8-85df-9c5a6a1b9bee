'use client';
import { Stack, Typography } from '@mui/material';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { DataInputFrame } from '@/components/forms/DataInputFrame';
import Navbar from '@/components/layout/navbar';
import KvcProgressBar from '@/components/progressbar/progressBar';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export default function DateneingabePerson() {
  const params = useParams();
  useAuthGuard(`/dateneingabe/case/${params.id}`);

  const formProgressPercent = 0;
  const t = useTranslations();

  return (
    <>
      <Navbar
        contentBelow={
          <KvcProgressBar fillPercent={formProgressPercent}></KvcProgressBar>
        }
      >
        <Stack>
          <Typography
            variant="body1"
            component="h1"
            color="white"
            // fontWeight={700}
            justifyContent="center"
            textAlign="center"
          >
            {t.rich(
              'components.data_input_frame.case',
              intlTranslationRichHelper
            )}
            : {params.id}
          </Typography>
        </Stack>
      </Navbar>

      <DataInputFrame caseGroupNumber={String(params.id)} />
    </>
  );
}
