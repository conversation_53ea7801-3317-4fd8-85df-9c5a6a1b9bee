'use client';
import {
  <PERSON><PERSON>,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { type Prisma } from '@prisma/client';
import dayjs, { type Dayjs } from 'dayjs';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import Navbar from '@/components/layout/navbar';
import FormStack from '@/components/ui/FormStack';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';

function MakeNewEmptyCustomer(): Prisma.CustomerCreateInput {
  const now = new Date().toISOString();
  return {
    // customerId: 0,
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: null,
    email: 'email',
    phoneNumber: null,
    street: null,
    houseNumber: null,
    postalCode: null,
    city: null,
    country: '',
    // createdAt: now,
  };
}

function MakeNewCustomer(): Prisma.CustomerCreateInput {
  const now = new Date().toISOString();
  return {
    // customerId: 0,
    firstName: 'asdas',
    lastName: 'asdasd',
    dateOfBirth: now,
    gender: 'Herr',
    email: 'email',
    phoneNumber: null,
    street: null,
    houseNumber: null,
    postalCode: null,
    city: null,
    country: '',
    // createdAt: now,
  };
}

export default function Dateneingabe() {
  useAuthGuard('/dateneingabe');

  const t = useTranslations();
  const [newCustomer, setNewCustomer] = useState<Prisma.CustomerCreateInput>(
    MakeNewEmptyCustomer()
  );
  const [newCustomers, setNewCustomers] = useState<
    Prisma.CustomerCreateInput[]
  >([]);

  const handleSubmitForm = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    alert('submit!');

    await backendCreateCustomer([newCustomer], false);
  };

  const backendCreateCustomer = async (
    customers: any[],
    asArray: boolean,
    method: string = 'POST'
  ) => {
    const payload = asArray ? customers : customers[0];

    return;
  };

  return (
    <>
      <Navbar showNewCaseButton={true} showOverviewButton={true}>
        <Typography
          variant="body1"
          component="h1"
          color="white"
          fontWeight={700}
          justifyContent="center"
          textAlign="center"
        >
          Willkommen Herr Frank Assekurichter
        </Typography>
      </Navbar>
      <div>Dateneingabe</div>

      <Stack direction="row" display="flex" flexWrap="wrap">
        <Stack direction="column" display="flex" flexWrap="wrap">
          <Typography variant="h6" fontWeight={700}>
            {t.rich('form.basisdaten.headline')}
          </Typography>
          <Typography variant="h6" fontWeight={700}>
            {t.rich('form.basisdaten.personliche_daten.headline')}
          </Typography>

          <Typography variant="h6" fontWeight={700}>
            {t.rich('form.basisdaten.vorversicherungen.headline')}
          </Typography>
        </Stack>

        <Stack direction="column" display="flex" flexWrap="wrap" flexGrow={1}>
          <form onSubmit={handleSubmitForm}>
            <Typography variant="h6" fontWeight={700}>
              {t.rich('form.basisdaten.personliche_daten.headline')}
            </Typography>

            <Typography fontWeight={700}>
              {t.rich('form.basisdaten.personliche_daten.stammdaten.headline')}
            </Typography>

            {/* <Stack direction="row" display="flex" flexWrap="wrap" flexGrow={1}> */}
            <FormStack>
              <FormControl>
                <InputLabel>
                  {t.rich(
                    'form.basisdaten.personliche_daten.stammdaten.anrede'
                  )}
                </InputLabel>
                <Select
                  name="salutation"
                  onChange={(e) => {
                    const newValue = e.target.value;
                    if (typeof newValue === 'string')
                      setNewCustomer((prev) => ({
                        ...prev,
                        gender: newValue,
                      }));
                  }}
                >
                  {(t.raw('raw.geschlecht') as string[]).map((s) => (
                    <MenuItem value={s} key={s}>
                      {s}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl>
                <InputLabel>
                  {t.rich('form.basisdaten.personliche_daten.stammdaten.titel')}
                </InputLabel>
                <Select name="titel">
                  {(t.raw('raw.titel') as string[]).map((s) => (
                    <MenuItem value={s} key={s}>
                      {s}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </FormStack>

            <FormStack>
              <TextField
                value={newCustomer.firstName}
                onChange={(e) => {
                  setNewCustomer((prev) => ({
                    ...prev,
                    firstName: e.target.value,
                  }));
                }}
                label={t.rich(
                  'form.basisdaten.personliche_daten.stammdaten.first_name'
                )}
                required
              ></TextField>
              <TextField
                value={newCustomer.lastName}
                onChange={(e) => {
                  setNewCustomer((prev) => ({
                    ...prev,
                    lastName: e.target.value,
                  }));
                }}
                label={t.rich(
                  'form.basisdaten.personliche_daten.stammdaten.last_name'
                )}
                required
              ></TextField>
            </FormStack>

            <FormStack>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                  label={t.rich(
                    'form.basisdaten.personliche_daten.stammdaten.birthdate'
                  )}
                  value={
                    newCustomer.dateOfBirth
                      ? dayjs(newCustomer.dateOfBirth)
                      : null
                  }
                  onChange={(newValue: Dayjs | null) =>
                    setNewCustomer((prev) => ({
                      ...prev,
                      dateOfBirth: newValue
                        ? newValue.toDate().toISOString()
                        : '',
                    }))
                  }
                  slotProps={{
                    textField: {
                      required: true,
                    },
                  }}
                ></DatePicker>
              </LocalizationProvider>

              <FormControl>
                <InputLabel
                // required
                >
                  {t.rich(
                    'form.basisdaten.personliche_daten.stammdaten.familienstand'
                  )}
                </InputLabel>
                <Select
                  name="titel"
                  // required
                >
                  {(t.raw('raw.familienstand') as string[]).map((s) => (
                    <MenuItem value={s} key={s}>
                      {s}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </FormStack>

            <FormStack>
              <Button type="submit">Speichern</Button>
            </FormStack>

            <FormStack>
              <Button
                onClick={() => {
                  setNewCustomer(MakeNewEmptyCustomer());
                }}
              >
                Clear
              </Button>

              <Button
                onClick={() => {
                  setNewCustomer(MakeNewCustomer());
                }}
              >
                Fill Customer
              </Button>

              <Button
                onClick={async () => {
                  backendCreateCustomer([newCustomer], false);
                }}
              >
                Send Good Customer
              </Button>

              <Button
                onClick={async () => {
                  backendCreateCustomer([newCustomer], false, 'PUT');
                }}
              >
                Update Good Customer
              </Button>

              <Button
                onClick={async () => {
                  backendCreateCustomer([{ custom: 'er' }], false);
                }}
              >
                Send Broken Customer
              </Button>

              <Button
                onClick={async () => {
                  backendCreateCustomer([MakeNewEmptyCustomer()], false);
                }}
              >
                Send Empty Customer
              </Button>

              <Button
                onClick={() => {
                  setNewCustomers((prev) => [...prev, newCustomer]);
                }}
              >
                Add To Array
              </Button>
            </FormStack>
          </form>
        </Stack>
      </Stack>
      <div>
        <div>Customers: {newCustomers.length}</div>

        <Button>Create {newCustomers.length} Customers</Button>
      </div>
    </>
  );
}
