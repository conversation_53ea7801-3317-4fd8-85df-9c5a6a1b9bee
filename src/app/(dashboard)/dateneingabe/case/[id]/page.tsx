import { authGuard } from '@/modules/auth/lib/auth-libs';
import DateneingabePersonView, {
  NotFoundDateneingabePersonView,
} from '@/modules/dateneingabe/views/dateneingabe-person-view';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

export default async function DateneingabePerson({
  params,
}: PageProps<'/dateneingabe/case/[id]'>) {
  const { id: caseId } = await params;
  await authGuard(`/dateneingabe/case/${caseId}`);

  prefetch(
    trpc.cases.getCaseGroupWithAllRelations.queryOptions({
      caseNumber: caseId,
    })
  );
  prefetch(
    trpc.cases.getWithAllRelations.queryOptions({
      number: caseId,
    })
  );
  prefetch(trpc.administration.userIsAdmin.queryOptions());

  return (
    <HydrateClient errorFallback={NotFoundDateneingabePersonView}>
      <DateneingabePersonView caseNumber={caseId} />
    </HydrateClient>
  );
}
