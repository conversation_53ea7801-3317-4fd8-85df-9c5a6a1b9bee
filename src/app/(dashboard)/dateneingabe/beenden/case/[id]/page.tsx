'use client';
import { Typography } from '@mui/material';
import equal from 'fast-deep-equal';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { CompleteDataInput } from '@/components/forms/CompleteDataInput';
import Navbar from '@/components/layout/navbar';
import { useAuthGuard } from '@/modules/auth/hooks/useAuthGuard';
import { CaseNavbarTitle } from '@/modules/case/ui/components/CaseNavbarTitle';
import { type CaseNavbarInfo } from '@/utils/helperTypes';

export default function DateneingabePerson() {
  const params = useParams();
  useAuthGuard(`/dateneingabe/case/beenden/${params.id}`);

  const t = useTranslations();
  const router = useRouter();
  const id =
    typeof params.id === 'string' &&
    params.id.trim().length > 0 &&
    params.id !== 'undefined'
      ? params.id
      : undefined;
  const [caseInfo, setCaseInfo] = useState<CaseNavbarInfo | null>(null);

  return (
    <>
      <Navbar>
        {caseInfo != null ? <CaseNavbarTitle caseInfo={caseInfo} /> : <></>}
      </Navbar>

      {id ? (
        // <DataInputFrame
        //   caseNumber={id}
        //   onProgressChange={(progress) => {
        //     setFormProgressPercent(progress / 3);
        //   }}
        // />
        <CompleteDataInput
          caseNumber={id}
          onBackToDataInputClick={() => {
            router.push(`/dateneingabe/case/${id}`);
          }}
          onSuccess={() => {
            router.push(`/cases`);
          }}
          onCaseInfoChanged={(newInfo) => {
            if (caseInfo === null || !equal(newInfo, caseInfo)) {
              setCaseInfo(newInfo);
            }
          }}
          onGoToCaseDetails={() => {
            router.push(`/cases/${id}`);
          }}
        ></CompleteDataInput>
      ) : (
        <Typography>Vorgang nicht gefunden</Typography>
      )}
    </>
  );
}
