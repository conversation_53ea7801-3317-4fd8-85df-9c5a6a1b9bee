import { Typography } from '@mui/material';
import { type Metadata } from 'next';

import Navbar from '@/components/layout/navbar';
import { authGuard } from '@/modules/auth/lib/auth-libs';

export const metadata: Metadata = {
  title: 'person',
  description: 'person',
};

export default async function DateneingabePerson() {
  await authGuard('/dateneingabe/basisdaten/person');

  return (
    <>
      <Navbar>
        <Typography
          variant="body1"
          component="h1"
          color="white"
          fontWeight={700}
          justifyContent="center"
          textAlign="center"
        >
          Dateneingabe Person
        </Typography>
      </Navbar>

      {/* <PersonForm></PersonForm> */}
    </>
  );
}
