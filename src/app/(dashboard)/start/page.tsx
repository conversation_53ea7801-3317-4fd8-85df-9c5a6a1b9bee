import { Typography } from '@mui/material';

import Navbar from '@/components/layout/navbar';
import { StartWidget } from '@/components/widgets/startWidget';
import { UserNameWidget } from '@/components/widgets/UserNameWidget';
import { authGuard } from '@/modules/auth/lib/auth-libs';
import { auth } from '@/modules/auth/server/auth';

export default async function StartPage() {
  await authGuard();
  const session = await auth();

  return (
    <>
      <Navbar showNewCaseButton={false} showOverviewButton={true}>
        <Typography
          variant="body1"
          component="h1"
          color="white"
          fontWeight={700}
          justifyContent="center"
          textAlign="center"
        >
          <UserNameWidget prefixKey="words.welcome"></UserNameWidget>
        </Typography>
      </Navbar>
      <StartWidget
        sx={{
          marginTop: 'auto',
          marginBottom: 'auto',
        }}
        schnellCheckDisabled={session?.user?.role !== 'admin'}
        antragDisabled={true}
      />
    </>
  );
}
