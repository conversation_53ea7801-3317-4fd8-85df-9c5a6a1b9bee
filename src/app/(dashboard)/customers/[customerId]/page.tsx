import { authGuard } from '@/modules/auth/lib/auth-libs';
import { CustomerDetails } from '@/modules/customer/ui/components/CustomerDetails';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

interface DetailsPageProps {
  params: Promise<{ customerId: string }>;
}

export default async function CustomerDetailsPage({
  params,
}: DetailsPageProps) {
  await authGuard(`/customers/${(await params).customerId}`);

  const { customerId } = await params;
  const customerIdNum = parseInt(customerId, 10);

  if (isNaN(customerIdNum)) {
    return <div>Invalid customer ID</div>;
  }

  // Prefetch the customer data
  prefetch(trpc.customers.getOne.queryOptions({ customerId: customerIdNum }));

  return (
    <HydrateClient>
      <CustomerDetails customerId={customerIdNum} />
    </HydrateClient>
  );
}
