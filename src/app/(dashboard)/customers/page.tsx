import { Box, Typography } from '@mui/material';
import { type SearchParams } from 'nuqs';

import { authGuard } from '@/modules/auth/lib/auth-libs';
import { loadCustomerSearchParams } from '@/modules/customer/types/customer-params';
import { CustomerSearchFilters } from '@/modules/customer/ui/components/CustomerSearchFilters';
import { CustomersView } from '@/modules/customer/ui/views/customers-view';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

interface CustomersPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function CustomersPage({
  searchParams,
}: CustomersPageProps) {
  await authGuard('/customers');

  const filters = await loadCustomerSearchParams(searchParams);
  prefetch(trpc.customers.getMany.queryOptions({ ...filters }));

  return (
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Page Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="h1"
            sx={{ fontWeight: 'bold', mb: 1 }}
          >
            Customers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage and search customer records
          </Typography>
        </Box>
      </Box>

      {/* Search Filters - Server Rendered */}
      <CustomerSearchFilters />

      {/* Customer Data - Client Rendered */}
      <HydrateClient>
        <CustomersView />
      </HydrateClient>
    </Box>
  );
}
