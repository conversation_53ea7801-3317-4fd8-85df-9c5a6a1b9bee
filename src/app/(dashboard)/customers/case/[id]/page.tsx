import { Stack, Typography } from '@mui/material';
import { getTranslations } from 'next-intl/server';

import { DataInputFrame } from '@/components/forms/DataInputFrame';
import Navbar from '@/components/layout/navbar';
import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export default async function DateneingabePerson({
  params,
}: PageProps<'/customers/case/[id]'>) {
  const { id: caseId } = await params;
  //   const formProgressPercent = 0;
  const t = await getTranslations();

  prefetch(
    trpc.cases.getCaseGroupWithAllRelations.queryOptions({
      caseGroupNumber: String(caseId),
    })
  );
  prefetch(trpc.administration.userIsAdmin.queryOptions());

  return (
    <HydrateClient>
      <Navbar
      // contentBelow={
      //   <KvcProgressBar fillPercent={formProgressPercent}></KvcProgressBar>
      // }
      >
        <Stack>
          <Typography
            variant="body1"
            component="h1"
            color="white"
            // fontWeight={700}
            justifyContent="center"
            textAlign="center"
          >
            {t.rich(
              'components.data_input_frame.case',
              intlTranslationRichHelper
            )}
            : {caseId}
          </Typography>
        </Stack>
      </Navbar>

      <DataInputFrame
        caseGroupNumber={String(caseId)}
        createCustomersOnly={true}
      />
    </HydrateClient>
  );
}
