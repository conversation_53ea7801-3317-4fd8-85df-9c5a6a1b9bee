import { Lock as LockIcon } from '@mui/icons-material';
import { Box, Button, Container, Typography } from '@mui/material';
import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '60vh',
          textAlign: 'center',
          gap: 3,
        }}
      >
        <LockIcon sx={{ fontSize: 80, color: 'error.main' }} />

        <Typography variant="h3" component="h1" gutterBottom>
          Zugriff verweigert
        </Typography>

        <Typography variant="h6" color="text.secondary" gutterBottom>
          Sie haben keine Berechtigung, auf diese Seite zuzugreifen.
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Diese Seite ist nur für Administratoren zugänglich. Wenn <PERSON> glau<PERSON>,
          dass dies ein <PERSON>hler ist, wenden <PERSON> sich an Ihren Administrator.
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            component={Link}
            href="/cases"
            variant="contained"
            color="primary"
          >
            Zur Übersicht
          </Button>

          <Button
            component={Link}
            href="/customers"
            variant="outlined"
            color="primary"
          >
            Zu Kunden
          </Button>
        </Box>
      </Box>
    </Container>
  );
}
