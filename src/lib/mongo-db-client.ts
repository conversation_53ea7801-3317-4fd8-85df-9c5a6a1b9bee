import {
  AuditOperation,
  PrismaClient as MongoDBPrismaClient,
} from './mongo-db-client/index';

const getMongoDBPrisma = () => new MongoDBPrismaClient();

const globalForMongoDBPrisma = global as unknown as {
  mongoDBPrisma: ReturnType<typeof getMongoDBPrisma>;
};

export const mongoDBPrisma =
  globalForMongoDBPrisma.mongoDBPrisma || getMongoDBPrisma();

if (process.env.NODE_ENV !== 'production') {
  globalForMongoDBPrisma.mongoDBPrisma = mongoDBPrisma;
}

export type MongoDBPrismaClientType = typeof mongoDBPrisma;

// Export types and enums for use in other files
export { AuditOperation };
