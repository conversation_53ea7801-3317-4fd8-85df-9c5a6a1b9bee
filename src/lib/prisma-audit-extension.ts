import { Prisma, type PrismaClient } from '@prisma/client';

import { mongoDBPrisma } from './mongo-db-client';

/**
 * Creates a Prisma Client extension that automatically sets user context
 * for audit logging. This extension sets the PostgreSQL runtime parameter
 * 'app.current_user_id' which can be used by database triggers or
 * application-level audit logging.
 *
 * @param userId - The ID of the user performing the operation
 * @returns Prisma Client extension
 */
export function createAuditExtension(userId: string) {
  return Prisma.defineExtension((client) =>
    client.$extends({
      query: {
        $allModels: {
          async $allOperations({ model, operation, args, query }) {
            // Set user context for the current database session
            // This makes the userId available to database triggers or queries
            await client.$executeRaw`SELECT set_config('app.current_user_id', ${userId}, TRUE)`;

            // Execute the original query
            const result = await query(args);

            return result;
          },
        },
      },
    })
  );
}

/**
 * Creates an audit-enabled Prisma client instance with user context
 *
 * @param prisma - Base Prisma client instance
 * @param userId - The ID of the user performing operations
 * @returns Extended Prisma client with audit context
 */
export function createAuditClient(prisma: PrismaClient, userId: string) {
  return prisma.$extends(createAuditExtension(userId));
}

/**
 * MongoDB doesn't need session-level user context like PostgreSQL,
 * but we can create a wrapper for consistency
 */
export function createMongoAuditContext(userId: string) {
  return {
    userId,
    mongoClient: mongoDBPrisma,
  };
}

/**
 * Type helper for audit-enabled Prisma client
 */
export type AuditPrismaClient = ReturnType<typeof createAuditClient>;
