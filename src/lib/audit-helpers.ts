import { type PrismaClient } from '@prisma/client';

import { AuditOperation, mongoDBPrisma } from './mongo-db-client';

/**
 * Base parameters for creating an audit record
 */
interface BaseAuditRecordParams<T = any> {
  userId: number;
  operation: AuditOperation;
  dataBefore?: T | null;
  dataAfter?: T | null;
  action?: string;
  changedFields?: string[];
}

/**
 * Parameters for creating customer/case audit records (numeric IDs)
 */
interface CreateAuditRecordParams<T = any> extends BaseAuditRecordParams<T> {
  entityId: number;
}

/**
 * Parameters for creating user audit records (string IDs)
 */
interface CreateUserAuditRecordParams<T = any>
  extends BaseAuditRecordParams<T> {
  entityId: number;
}

/**
 * Extracts only the changed fields from before/after objects for optimized storage
 */
function extractChangedFieldsData(
  dataBefore: any,
  dataAfter: any,
  changedFields: string[]
): { optimizedBefore: any; optimizedAfter: any } {
  // Handle CREATE operation (dataBefore is null)
  if (!dataBefore && dataAfter) {
    return {
      optimizedBefore: null,
      optimizedAfter:
        changedFields.length > 0
          ? changedFields.reduce((acc, field) => {
              if (field in dataAfter) {
                acc[field] = dataAfter[field];
              }
              return acc;
            }, {} as any)
          : dataAfter, // Fallback to full object if no changedFields provided
    };
  }

  // Handle DELETE operation (dataAfter is null)
  if (dataBefore && !dataAfter) {
    return {
      optimizedBefore:
        changedFields.length > 0
          ? changedFields.reduce((acc, field) => {
              if (field in dataBefore) {
                acc[field] = dataBefore[field];
              }
              return acc;
            }, {} as any)
          : dataBefore, // Fallback to full object if no changedFields provided
      optimizedAfter: null,
    };
  }

  // Handle UPDATE operation (both objects exist)
  if (dataBefore && dataAfter && changedFields.length > 0) {
    const optimizedBefore = changedFields.reduce((acc, field) => {
      if (field in dataBefore) {
        acc[field] = dataBefore[field];
      }
      return acc;
    }, {} as any);

    const optimizedAfter = changedFields.reduce((acc, field) => {
      if (field in dataAfter) {
        acc[field] = dataAfter[field];
      }
      return acc;
    }, {} as any);

    return { optimizedBefore, optimizedAfter };
  }

  // Fallback: return original data if no optimization possible
  return { optimizedBefore: dataBefore, optimizedAfter: dataAfter };
}

/**
 * Creates a customer audit record in MongoDB with optimized storage
 */
export async function createCustomerAuditRecord(
  params: CreateAuditRecordParams
) {
  // Extract only changed fields for optimized storage
  const { optimizedBefore, optimizedAfter } = extractChangedFieldsData(
    params.dataBefore,
    params.dataAfter,
    params.changedFields || []
  );

  return await mongoDBPrisma.customerHistory.create({
    data: {
      customerId: params.entityId,
      userId: params.userId,
      operation: params.operation,
      action: params.action,
      dataBefore: optimizedBefore,
      dataAfter: optimizedAfter,
      changedFields: params.changedFields || [],
    },
  });
}

/**
 * Creates a case audit record in MongoDB with optimized storage
 */
export async function createCaseAuditRecord(params: CreateAuditRecordParams) {
  // Extract only changed fields for optimized storage
  const { optimizedBefore, optimizedAfter } = extractChangedFieldsData(
    params.dataBefore,
    params.dataAfter,
    params.changedFields || []
  );

  return await mongoDBPrisma.caseHistory.create({
    data: {
      caseId: params.entityId,
      userId: params.userId,
      operation: params.operation,
      action: params.action,
      dataBefore: optimizedBefore,
      dataAfter: optimizedAfter,
      changedFields: params.changedFields || [],
    },
  });
}

/**
 * Creates a user audit record in MongoDB with optimized storage
 */
export async function createUserAuditRecord(
  params: CreateUserAuditRecordParams
) {
  // Extract only changed fields for optimized storage
  const { optimizedBefore, optimizedAfter } = extractChangedFieldsData(
    params.dataBefore,
    params.dataAfter,
    params.changedFields || []
  );

  return await mongoDBPrisma.userHistory.create({
    data: {
      userId: params.entityId, // The user being audited
      operation: params.operation,
      action: params.action,
      dataBefore: optimizedBefore,
      dataAfter: optimizedAfter,
      changedFields: params.changedFields || [],
    },
  });
}

/**
 * Captures a complete snapshot of a customer record
 */
export async function captureCustomerSnapshot(
  prisma: PrismaClient,
  customerId: number
) {
  return await prisma.customer.findUnique({
    where: { customerId },
    include: {
      cases: {
        select: {
          caseId: true,
          caseNumber: true,
          caseType: true,
          status: true,
        },
      },
    },
  });
}

/**
 * Captures a complete snapshot of a case record
 */
export async function captureCaseSnapshot(
  prisma: PrismaClient,
  caseId: number
) {
  return await prisma.case.findUnique({
    where: { caseId },
    include: {
      customer: {
        select: {
          customerId: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      assignedUser: {
        select: { userId: true, firstName: true, lastName: true, email: true },
      },
    },
  });
}

/**
 * Compares two objects and returns the list of changed fields
 */
export function getChangedFields<T extends Record<string, any>>(
  before: T | null,
  after: T | null
): string[] {
  if (!before || !after) return [];

  const changedFields: string[] = [];

  // Get all unique keys from both objects
  const allKeys = new Set([...Object.keys(before), ...Object.keys(after)]);

  for (const key of allKeys) {
    // Skip nested objects and arrays for now (can be enhanced later)
    if (typeof before[key] === 'object' || typeof after[key] === 'object') {
      continue;
    }

    if (before[key] !== after[key]) {
      changedFields.push(key);
    }
  }

  return changedFields;
}

/**
 * Generates a human-readable description of the changes made
 */
export function generateChangeDescription(
  operation: AuditOperation,
  entityType: 'customer' | 'case',
  changedFields?: string[]
): string {
  switch (operation) {
    case AuditOperation.CREATE:
      return `New ${entityType} created`;

    case AuditOperation.DELETE:
      return `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deleted`;

    case AuditOperation.UPDATE:
      if (changedFields && changedFields.length > 0) {
        return `Updated ${changedFields.join(', ')}`;
      }
      return `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} updated`;

    default:
      return `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} modified`;
  }
}

/**
 * High-level helper for customer audit logging
 */
export async function auditCustomerChange(params: {
  customerId: number;
  userId: number;
  operation: AuditOperation;
  dataBefore?: any;
  dataAfter?: any;
  action?: string;
}) {
  const changedFields = getChangedFields(params.dataBefore, params.dataAfter);

  return await createCustomerAuditRecord({
    entityId: params.customerId,
    userId: params.userId,
    operation: params.operation,
    dataBefore: params.dataBefore,
    dataAfter: params.dataAfter,
    action: params.action,
    changedFields,
  });
}

/**
 * High-level helper for case audit logging
 */
export async function auditCaseChange(params: {
  caseId: number;
  userId: number;
  operation: AuditOperation;
  dataBefore?: any;
  dataAfter?: any;
  action?: string;
}) {
  const changedFields = getChangedFields(params.dataBefore, params.dataAfter);

  return await createCaseAuditRecord({
    entityId: params.caseId,
    userId: params.userId,
    operation: params.operation,
    dataBefore: params.dataBefore,
    dataAfter: params.dataAfter,
    action: params.action,
    changedFields,
  });
}

/**
 * High-level helper for user audit logging
 */
export async function auditUserChange(params: {
  targetUserId: number; // The user being audited
  performedByUserId: number; // The user performing the action
  operation: AuditOperation;
  dataBefore?: any;
  dataAfter?: any;
  action?: string;
}) {
  const changedFields = getChangedFields(params.dataBefore, params.dataAfter);

  return await createUserAuditRecord({
    entityId: params.targetUserId, // Keep as string for MongoDB
    userId: params.performedByUserId,
    operation: params.operation,
    dataBefore: params.dataBefore,
    dataAfter: params.dataAfter,
    action: params.action,
    changedFields,
  });
}
