import { redirect } from 'next/navigation';

import { auth } from '@/modules/auth/server/auth';

/**
 * Check if the current user is an admin
 * Redirects to unauthorized page if not admin
 */
export async function requireAdmin() {
  const session = await auth();

  if (!session) {
    redirect('/login');
  }

  if (session.user?.role !== 'admin') {
    redirect('/unauthorized');
  }

  return session;
}

/**
 * Check if the current user is authenticated
 * Redirects to login if not authenticated
 */
export async function requireAuth() {
  const session = await auth();

  if (!session) {
    redirect('/login');
  }

  return session;
}

/**
 * Get current user role
 */
export async function getCurrentUserRole() {
  const session = await auth();
  return session?.user?.role || null;
}
