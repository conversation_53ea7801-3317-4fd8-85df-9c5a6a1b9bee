import { render } from "@react-email/render";
import ReferrerLoginEmail, {ReferrerLoginEmailProps} from "./ReferrerLoginEmail";
import { getEmailMessages } from "@/utils/getEmailMessages";

type Params = Omit<ReferrerLoginEmailProps, 'messages'> & { locale?: string }

export async function renderReferrerLoginEmail({
  locale = 'de',
  ...params
}: Params) {
  const messages = getEmailMessages(locale, "referrerLogin");

  return render(
    <ReferrerLoginEmail {...params} messages={messages} />
  );
}
