import {
  Html,
  Head,
  Preview,
  Body,
  Container,
  Heading,
  Text,
  Button,
  Img,
  Link,
} from "@react-email/components";

const PRIMARY = "#374883";
const BORDER_RADIUS = 12;

export default function EmailTemplate({
  ctaUrl,
  messages,
}: {
  ctaUrl?: string;
  messages: Record<string, any>;
}) {
  return (
    <Html>
      <Head />
      <Preview>{messages.preview}</Preview>
      <Body
        style={{
          fontFamily: "Inter, sans-serif",
          backgroundColor: "#f7f8fc",
          padding: "24px",
        }}
      >
        <Container
          style={{
            maxWidth: "600px",
            margin: "auto",
            backgroundColor: "#fff",
            borderRadius: BORDER_RADIUS,
            padding: "32px",
            boxShadow:
              "0 1px 2px rgba(16,24,40,0.06), 0 1px 3px rgba(16,24,40,0.1)",
          }}
        >
          <Container style={{ marginBottom: "32px" }}>
            <table
              role="presentation"
              width="100%"
              cellPadding="0"
              cellSpacing="0"
              style={{ textAlign: "center", margin: "0 auto" }}
            >
              <tbody>
                <tr>
                  <td align="center">
                    <Img
                      src="/email/header.png"
                      alt="Logo 1"
                      width="150"
                      style={{ display: "inline-block", margin: "0 8px" }}
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </Container>
          <Heading
            style={{
              color: PRIMARY,
              fontSize: "2rem",
              fontWeight: 700,
              marginBottom: "16px",
            }}
          >
            {messages.title}
          </Heading>

          <Text
            style={{
              color: "#374151",
              fontSize: "1rem",
              marginBottom: "24px",
            }}
          >
            {messages.message}
          </Text>

            {ctaUrl && (
                <Button
                    href={ctaUrl}
                    style={{
                      display: "inline-block",
                      textDecoration: "none",
                      backgroundColor: PRIMARY,
                      color: "#fff",
                      padding: "12px 24px",
                      borderRadius: "999px",
                      fontWeight: 600,
                      textAlign: "center",
                    }}
                  >
                    {messages.button}
                  </Button>
            )}

           {/* Signature */}
          <Text style={{ marginTop: "32px", whiteSpace: "pre-line" }}>
            {messages.signature}
          </Text>
          {/* Legal Section */}
          <Text
            style={{
              marginTop: "32px",
              fontSize: "0.875rem",
              color: "#6b7280",
            }}
          >
            {messages.legal}
          </Text>
          <ul
            style={{
              paddingLeft: "20px",
              fontSize: "0.875rem",
              color: "#6b7280",
            }}
          >
            <li>
              <Link href={messages.legalLinks.privacy.url}>
                {messages.legalLinks.privacy.text}
              </Link>
            </li>
            <li>
              <Link href={messages.legalLinks.firstInfo.url}>
                {messages.legalLinks.firstInfo.text}
              </Link>
            </li>
            <li>
              <Link href={messages.legalLinks.unsubscribe.url}>
                {messages.legalLinks.unsubscribe.text}
              </Link>
            </li>
          </ul>

          {/* Company Info */}
          <Text
            style={{
              marginTop: "24px",
              fontSize: "0.875rem",
              whiteSpace: "pre-line",
              color: "#6b7280",
            }}
          >
            {messages.companyInfo}
          </Text>
          {/* Disclaimers */}
          <Text
            style={{ marginTop: "24px", fontSize: "0.75rem", color: "#9ca3af" }}
          >
            {messages.disclaimerDe}
          </Text>
        </Container>
      </Body>
    </Html>
  );
}
