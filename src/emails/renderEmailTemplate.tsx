import { render } from "@react-email/render";
import EmailTemplate from "./EmailTemplate";
import { getEmailMessages } from "@/utils/getEmailMessages";

type Params = {
  ctaUrl?: string
  locale?: string
  group: string
}
export async function renderEmailTemplate({ ctaUrl, locale = 'de', group }: Params) {
  const messages = getEmailMessages(locale, group);
  return render(<EmailTemplate ctaUrl={ctaUrl} messages={messages} />);
}
