import { render } from "@react-email/render";
import CustomerInvitationEmail, {CustomerInvitationEmailProps} from "./CustomerInvitationEmail";
import { getEmailMessages } from "@/utils/getEmailMessages";

type Params = Omit<CustomerInvitationEmailProps, 'messages'> & { locale?: string }

export async function renderCustomerInvitationEmail({
  locale = 'de',
  ...params
}: Params) {
  const messages = getEmailMessages(locale, "invitation");

  return render(
    <CustomerInvitationEmail {...params} messages={messages} />
  );
}
