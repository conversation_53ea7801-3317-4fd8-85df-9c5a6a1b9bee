import * as z from "zod";
import {NextApiRequest, NextApiResponse} from "next";
import {AccessTokenRepository} from "@/server/AccessTokenRepository";
import {EmailService} from "@/server/EmailService";

type ValidateParams = {
    req: NextApiRequest;
}
type ExecuteParams = {
    req: NextApiRequest;
    res: NextApiResponse;
    logger: Logger;
    accessTokenRepository: AccessTokenRepository
    emailService: EmailService
}

export type EndpointHandler = {
    schemas?: {
        body?: z.ZodObject
        query?: z.ZodObject
    }
    validate?: (params: ValidateParams) => string | void | Promise<string | void>
    execute: (params: ExecuteParams) => any
}

export interface Logger {
    log(...args: unknown[]): Promise<void>
    error(...args: unknown[]): Promise<void>
}

export type ReferrerIdentity = {
    referrerId: number
    isAdmin?: boolean
}
export type CustomerIdentity = {
    customerId: number
}