import { type PrismaClient } from '@prisma/client';

import { type Prisma } from '@/generated/prisma-postgres';

import { AgencyRepository } from './AgencyRepositroy';

export class AgencyService {
  private repo: AgencyRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new AgencyRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single agency by arbitrary filter */
  findOne(
    where: Prisma.AgencyWhereInput,
    options?: {
      select?: Prisma.AgencySelect;
      include?: Prisma.AgencyInclude;
      orderBy?: Prisma.AgencyOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  /** Optional helper if you prefer throwing semantics */
  async findOneOrThrow(
    where: Prisma.AgencyWhereInput,
    options?: {
      select?: Prisma.AgencySelect;
      include?: Prisma.AgencyInclude;
      orderBy?: Prisma.AgencyOrderByWithRelationInput;
    }
  ) {
    const row = await this.findOne(where, options);
    if (!row) throw new Error('Agency not found');
    return row;
  }

  async createWithAudit(data: any) {
    return this.db.$transaction(async (tx: PrismaClient) => {
      const agency = await tx.agency.create({ data });
      await tx.auditLog.create({
        data: { model: 'Agency', action: 'CREATE', refId: agency.id },
      });
      return agency;
    });
  }
}
