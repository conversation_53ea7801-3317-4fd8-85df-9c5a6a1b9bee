import { type PrismaClient } from '@prisma/client';

import { type Prisma } from '@/generated/prisma-postgres';

import { ThemeRepository } from './ThemeRepository';

export class ThemeService {
  private repo: ThemeRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ThemeRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single agency by arbitrary filter */
  findOne(
    where: Prisma.ThemeWhereInput,
    options?: {
      select?: Prisma.ThemeSelect;
      orderBy?: Prisma.ThemeOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }
}
