import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { AgentRepository } from './AgentRepository';

export class AgentService {
  private repo: AgentRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new AgentRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.db.agent.findMany({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.db.agent.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single agency by arbitrary filter */
  findOne(
    where: Prisma.AgentWhereInput,
    options?: {
      select?: Prisma.AgentSelect;
      include?: Prisma.AgentInclude;
      orderBy?: Prisma.AgentOrderByWithRelationInput;
    }
  ) {
    return this.db.agent.findFirst({ where, ...options });
  }

  /** Optional helper if you prefer throwing semantics */
  async findOneOrThrow(
    where: Prisma.AgentWhereInput,
    options?: {
      select?: Prisma.AgentSelect;
      include?: Prisma.AgentInclude;
      orderBy?: Prisma.AgentOrderByWithRelationInput;
    }
  ) {
    const row = await this.findOne(where, options);
    if (!row) throw new Error('Agent not found');
    return row;
  }

  async update(
    where: Prisma.CustomerWhereUniqueInput, // { id } | { documentId } | { customerNumber }
    data: Prisma.CustomerUpdateInput,
    options?: {
      select?: Prisma.CustomerSelect;
      include?: Prisma.CustomerInclude;
    }
  ) {
    return this.db.customer.update({ where, data, ...options });
  }
}
