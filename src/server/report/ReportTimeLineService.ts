import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportTimelineEntryRepository } from './ReportTimelineEntryRepository';

export class ReportTimelineEntryService {
  private repo: ReportTimelineEntryRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportTimelineEntryRepository(db);
  }

  async list(params: {
    where: Prisma.ReportTimelineEntryWhereInput;
    orderBy:
      | Prisma.ReportTimelineEntryOrderByWithRelationInput
      | Prisma.ReportTimelineEntryOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportTimelineEntry(
    where: Prisma.ReportTimelineEntryWhereInput,
    options?: {
      select?: Prisma.ReportTimelineEntrySelect;
      include?: Prisma.ReportTimelineEntryInclude;
      orderBy?: Prisma.ReportTimelineEntryOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportTimelineEntryCreateInput,
    options?: {
      select?: Prisma.ReportTimelineEntrySelect;
      include?: Prisma.ReportTimelineEntryInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
