import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportRepository } from './ReportRepository';

export class ReportService {
    private repo: ReportRepository;

    constructor(private readonly db: PrismaClient) {
        this.repo = new ReportRepository(db);
    }

    async list(params: {
        where: Prisma.ReportWhereInput;
        orderBy:
        | Prisma.ReportOrderByWithRelationInput
        | Prisma.ReportOrderByWithRelationInput[];
        skip: number;
        take: number;
    }) {
        const [items, total] = await this.db.$transaction([
            this.repo.findAll({
                where: params.where,
                orderBy: params.orderBy,
                skip: params.skip,
                take: params.take,
            }),
            this.repo.count({ where: params.where }),
        ]);

        return { items, total };
    }

    getReport(
        where: Prisma.ReportWhereInput,
        options?: {
            select?: Prisma.ReportSelect;
            include?: Prisma.ReportInclude;
            orderBy?: Prisma.ReportOrderByWithRelationInput;
        }
    ) {
        return this.repo.findOne({ where, ...options });
    }

    async create(
        data: Prisma.ReportCreateInput,
        options?: {
            select?: Prisma.ReportSelect;
            include?: Prisma.ReportInclude;
        }
    ) {
        return this.repo.create({ data, ...options });
    }

    async update(
        where: Prisma.ReportWhereUniqueInput,
        data: Prisma.ReportUpdateInput,
        options?: {
            select?: Prisma.ReportSelect;
            include?: Prisma.ReportInclude;
        }
    ) {
        return this.repo.update({ where, data, ...options });
    }
}
