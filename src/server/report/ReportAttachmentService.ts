import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportAttachmentRepository } from './ReportAttachmentRepository';

export class ReportAttachmentService {
  private repo: ReportAttachmentRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportAttachmentRepository(db);
  }

  async list(params: {
    where: Prisma.ReportAttachmentWhereInput;
    orderBy:
      | Prisma.ReportAttachmentOrderByWithRelationInput
      | Prisma.ReportAttachmentOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportAttachment(
    where: Prisma.ReportAttachmentWhereInput,
    options?: {
      select?: Prisma.ReportAttachmentSelect;
      include?: Prisma.ReportAttachmentInclude;
      orderBy?: Prisma.ReportAttachmentOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportAttachmentCreateInput,
    options?: {
      select?: Prisma.ReportAttachmentSelect;
      include?: Prisma.ReportAttachmentInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
