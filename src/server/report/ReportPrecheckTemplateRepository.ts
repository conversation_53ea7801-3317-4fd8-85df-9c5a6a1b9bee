import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class ReportPrecheckTemplateRepository extends BaseRepository<
    (
        c: PrismaClient | Prisma.TransactionClient
    ) => Prisma.ReportPrecheckTemplateDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.reportPrecheckTemplate);
    }
}
