import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportSummaryRepository } from './ReportSummaryRepository';

export class ReportSummaryService {
  private repo: ReportSummaryRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportSummaryRepository(db);
  }

  async list(params: {
    where: Prisma.ReportSummaryWhereInput;
    orderBy:
      | Prisma.ReportSummaryOrderByWithRelationInput
      | Prisma.ReportSummaryOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportSummary(
    where: Prisma.ReportSummaryWhereInput,
    options?: {
      select?: Prisma.ReportSummarySelect;
      include?: Prisma.ReportSummaryInclude;
      orderBy?: Prisma.ReportSummaryOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportSummaryCreateInput,
    options?: {
      select?: Prisma.ReportSummarySelect;
      include?: Prisma.ReportSummaryInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
