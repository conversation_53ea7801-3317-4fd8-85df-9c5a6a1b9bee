import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportPrecheckRepository } from './ReportPrecheckRepository';

export class ReportPrecheckService {
  private repo: ReportPrecheckRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportPrecheckRepository(db);
  }

  async list(params: {
    where: Prisma.ReportPrecheckWhereInput;
    orderBy:
      | Prisma.ReportPrecheckOrderByWithRelationInput
      | Prisma.ReportPrecheckOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportPrecheck(
    where: Prisma.ReportPrecheckWhereInput,
    options?: {
      select?: Prisma.ReportPrecheckSelect;
      include?: Prisma.ReportPrecheckInclude;
      orderBy?: Prisma.ReportPrecheckOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportPrecheckCreateInput,
    options?: {
      select?: Prisma.ReportPrecheckSelect;
      include?: Prisma.ReportPrecheckInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }

  async update(
    where: Prisma.ReportPrecheckWhereUniqueInput,
    data: Prisma.ReportPrecheckUpdateInput,
    options?: {
      select?: Prisma.ReportPrecheckSelect;
      include?: Prisma.ReportPrecheckInclude;
    }
  ) {
    return this.repo.update({ where, data, ...options });
  }
}
