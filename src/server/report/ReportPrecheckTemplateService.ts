import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportPrecheckTemplateRepository } from './ReportPrecheckTemplateRepository';

export class ReportPrecheckTemplateService {
  private repo: ReportPrecheckTemplateRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportPrecheckTemplateRepository(db);
  }

  async list(params: {
    where: Prisma.ReportPrecheckTemplateWhereInput;
    orderBy:
      | Prisma.ReportPrecheckTemplateOrderByWithRelationInput
      | Prisma.ReportPrecheckTemplateOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportPrecheckTemplate(
    where: Prisma.ReportPrecheckTemplateWhereInput,
    options?: {
      select?: Prisma.ReportPrecheckTemplateSelect;
      orderBy?: Prisma.ReportPrecheckTemplateOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportPrecheckTemplateCreateInput,
    options?: {
      select?: Prisma.ReportPrecheckTemplateSelect;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
