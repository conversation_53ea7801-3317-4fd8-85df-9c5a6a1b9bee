import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class ReportCoverageAssessmentRepository extends BaseRepository<
    (
        c: PrismaClient | Prisma.TransactionClient
    ) => Prisma.ReportCoverageAssessmentDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.reportCoverageAssessment);
    }
}
