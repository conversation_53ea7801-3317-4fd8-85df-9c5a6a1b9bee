import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class ReportTimelineEntryRepository extends BaseRepository<
    (
        c: PrismaClient | Prisma.TransactionClient
    ) => Prisma.ReportTimelineEntryDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.reportTimelineEntry);
    }
}
