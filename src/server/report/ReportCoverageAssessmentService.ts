import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ReportCoverageAssessmentRepository } from './ReportCoverageAssessmentRepository';

export class ReportCoverageAssessmentService {
  private repo: ReportCoverageAssessmentRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new ReportCoverageAssessmentRepository(db);
  }

  async list(params: {
    where: Prisma.ReportCoverageAssessmentWhereInput;
    orderBy:
      | Prisma.ReportCoverageAssessmentOrderByWithRelationInput
      | Prisma.ReportCoverageAssessmentOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getReportCoverageAssessment(
    where: Prisma.ReportCoverageAssessmentWhereInput,
    options?: {
      select?: Prisma.ReportCoverageAssessmentSelect;
      include?: Prisma.ReportCoverageAssessmentInclude;
      orderBy?: Prisma.ReportCoverageAssessmentOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.ReportCoverageAssessmentCreateInput,
    options?: {
      select?: Prisma.ReportCoverageAssessmentSelect;
      include?: Prisma.ReportCoverageAssessmentInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
