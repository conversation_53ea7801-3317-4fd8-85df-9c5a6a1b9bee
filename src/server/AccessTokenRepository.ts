import {generateToken} from "@/utils/generateToken";
import {prisma} from "@/server/prisma";

const MONTH = 1000 * 60 * 60 * 24 * 30

type CreateParams = { customerId: number } | { referrerId: number }

export class AccessTokenRepository {
    async create(params: CreateParams) {
        const token = generateToken()
        const expiresAt = new Date(Date.now() + MONTH)

        if ('customerId' in params) {
            return prisma.customerAccessToken.create({
                data: {
                    customerId: params.customerId,
                    token,
                    expiresAt,
                },
            });
        }

        return prisma.referrerAccessToken.create({
            data: {
                referrerId: params.referrerId,
                token,
                expiresAt,
            },
        });
    }
}
