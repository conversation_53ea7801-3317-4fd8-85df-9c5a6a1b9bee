// src/server/policy/policies.ts

import { type PolicyMap } from './infra/policy';
import { ForbiddenError } from './infra/securePrisma';

export const policies: PolicyMap = {
    Agency: {
        // Row-level read scope
        scope: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) return undefined;
            // no agent info -> deny by default
            throw new ForbiddenError('Missing admin role');
        },
        // Check before creating
        canCreate: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) {
                return true;
            }
            return false;
        },
        // Check before updating
        canUpdate: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) return true;
            return false;
        },
        // Check before deleting
        canDelete: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) return true;
            return false;
        },
        // Field-level enforcement for writes
        filterWrite: (ctx, data, action) => {
            const copy = { ...data } as any;
            if (!ctx.roles.includes('asevo-admin') && ctx.userId) {
                copy.agentNumber = ctx.userId; // force ownership
            }

            //TODO: only an usage example, delete later since non-admin are not allowed to update
            // Ensure non-admins cannot change ownership
            if (action === 'update' && !ctx.roles.includes('asevo-admin')) {
                delete copy.agentNumber;
            }

            delete copy.id;
            return copy;
        },
    },
    Agent: {
        // Row-level read scope
        scope: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) return undefined;
            // no agent info -> deny by default
            throw new ForbiddenError('Missing admin role');
        },
        // Check before creating
        canCreate: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) {
                return true;
            }
            return false;
        },
        // Check before updating
        canUpdate: (ctx) => {
            if (ctx.roles.includes('asevo-admin')) return true;
            return false;
        },
        // Check before deleting
        canDelete: () => {
            return false;
        },
        // Field-level enforcement for writes
        filterWrite: (ctx, data, action) => {
            const copy = { ...data } as any;
            if (!ctx.roles.includes('asevo-admin') && ctx.userId) {
                copy.agentNumber = ctx.userId; // force ownership
            }

            // Ensure field protection
            if (action === 'update') {
                delete copy.id;
                delete copy.documentId;
                delete copy.agentNumber;
                delete copy.agencyNumber;
                delete copy.updatedAt;
                delete copy.createddAt;
            }

            delete copy.id;
            return copy;
        },
    },
    Contract: {
        filterWrite: (ctx, data, action) => {
            const copy = { ...data } as any;
            if (!ctx.roles.includes('asevo-admin') && ctx.userId) {
                copy.agentNumber = ctx.userId; // force ownership
            }
            if (action === 'create') {
                delete copy.id;
                delete copy.documentId;
                delete copy.updatedAt;
                delete copy.createddAt;
            }
            return copy;
        },
    },
};
