// src/server/policy/policies.ts

import { type PolicyMap } from './infra/policy';
import { ForbiddenError } from './infra/securePrisma';

export const policies: PolicyMap = {
  Agency: {
    // Row-level read scope
    scope: (ctx) => {
      if (ctx.roles.includes('admin')) return undefined;
      // no agent info -> deny by default
      throw new ForbiddenError('Missing admin role');
    },
    // Check before creating
    canCreate: (ctx, data) => {
      if (ctx.roles.includes('admin') || ctx.roles.includes('manager')) {
        return true;
      }
      return false;
    },
    // Check before updating
    canUpdate: (ctx, where, data) => {
      if (ctx.roles.includes('admin')) return true;
      return false;
    },
    // Check before deleting
    canDelete: (ctx, where) => {
      if (ctx.roles.includes('admin')) return true;
      return false;
    },
    // Field-level enforcement for writes
    filterWrite: (ctx, data, action) => {
      const copy = { ...data } as any;
      if (!ctx.roles.includes('admin') && ctx.userId) {
        copy.agentNumber = ctx.userId; // force ownership
      }

      //TODO: only an usage example, delete later since non-admin are not allowed to update
      // Ensure non-admins cannot change ownership
      if (action === 'update' && !ctx.roles.includes('admin')) {
        delete copy.agentNumber;
      }

      delete copy.id;
      return copy;
    },
  },
  Agent: {
    // Row-level read scope
    scope: (ctx) => {
      if (ctx.roles.includes('admin')) return undefined;
      // no agent info -> deny by default
      throw new ForbiddenError('Missing admin role');
    },
    // Check before creating
    canCreate: (ctx, data) => {
      if (ctx.roles.includes('admin')) {
        return true;
      }
      return false;
    },
    // Check before updating
    canUpdate: (ctx, where, data) => {
      if (ctx.roles.includes('admin')) return true;
      return false;
    },
    // Check before deleting
    canDelete: (ctx, where) => {
      return false;
    },
    // Field-level enforcement for writes
    filterWrite: (ctx, data, action) => {
      const copy = { ...data } as any;
      if (!ctx.roles.includes('admin') && ctx.userId) {
        copy.agentNumber = ctx.userId; // force ownership
      }

      // Ensure field protection
      if (action === 'update') {
        delete copy.id;
        delete copy.documentId;
        delete copy.agentNumber;
        delete copy.agencyNumber;
        delete copy.updatedAt;
        delete copy.createddAt;
      }

      delete copy.id;
      return copy;
    },
  },
};
