import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { InvoiceSequenceRepository } from './InvoiceSequenceRepository';

export class InvoiceSequenceService {
  private repo: InvoiceSequenceRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new InvoiceSequenceRepository(db);
  }

  async list(params: {
    where: Prisma.InvoiceSequenceWhereInput;
    orderBy:
      | Prisma.InvoiceSequenceOrderByWithRelationInput
      | Prisma.InvoiceSequenceOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [contracts, total] = await this.db.$transaction([
      this.db.invoiceSequence.findMany({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.db.invoiceSequence.count({ where: params.where }),
    ]);

    return { contracts, total };
  }

  getSequence(
    where: Prisma.InvoiceSequenceWhereInput,
    options?: {
      select?: Prisma.InvoiceSequenceSelect;
      include?: Prisma.InvoiceSequenceInclude;
      orderBy?: Prisma.InvoiceSequenceOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async update(
    where: Prisma.InvoiceSequenceWhereUniqueInput,
    data: Prisma.InvoiceSequenceUpdateInput,
    options?: {
      select?: Prisma.InvoiceSequenceSelect;
      include?: Prisma.InvoiceSequenceInclude;
    }
  ) {
    return this.repo.update({ where, data, ...options });
  }

  async create(
    data: Prisma.InvoiceSequenceCreateInput,
    options?: {
      select?: Prisma.InvoiceSequenceSelect;
      include?: Prisma.InvoiceSequenceInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
