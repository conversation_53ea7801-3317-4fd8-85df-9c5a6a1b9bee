// src/server/sequence/InvoiceSequenceRepository.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class InvoiceSequenceRepository extends BaseRepository<
    (c: PrismaClient | Prisma.TransactionClient) => Prisma.InvoiceSequenceDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.invoiceSequence);
    }
}
