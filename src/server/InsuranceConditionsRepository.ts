import {InsuranceCondition} from "@/types";
import {insuranceConditionMapper} from "@/utils/mappers";
import {HttpClient} from "@/utils/HttpClient";
import {BaseRepository} from "@/server/BaseRepository";

export class InsuranceConditionsRepository extends BaseRepository<InsuranceCondition> {
    protected readonly endpoint = '/insurance-conditions';
    protected readonly mapper = insuranceConditionMapper;

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }
}