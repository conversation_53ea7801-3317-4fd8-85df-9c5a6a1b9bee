import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { CalculationParameterRepository } from './CalculationParameterRepository';

export class CalculationParameterService {
  private repo: CalculationParameterRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new CalculationParameterRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single agency by arbitrary filter */
  findOne(
    where: Prisma.CalculationParameterWhereInput,
    options?: {
      select?: Prisma.CalculationParameterSelect;
      orderBy?: Prisma.CalculationParameterOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async getCalculationParameters(startDate: string) {
    const param = await this.repo.findOne({
      where: { validFrom: { lte: startDate } },
      orderBy: { validFrom: 'desc' },
    });
    return param ?? null;
  }
}
