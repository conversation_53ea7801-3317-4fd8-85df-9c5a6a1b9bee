// src/server/contract/CalculationParameterRepository.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class CalculationParameterRepository extends BaseRepository<
    (
        c: PrismaClient | Prisma.TransactionClient
    ) => Prisma.CalculationParameterDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.calculationParameter);
    }
}
