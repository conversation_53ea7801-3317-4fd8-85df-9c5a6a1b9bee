import {jwtVerify, SignJWT} from "jose";
import {CustomerIdentity, ReferrerIdentity} from "@/server/domain";
import {NextApiRequest} from "next";
import {UnauthenticatedError} from "@/server/errors";
import {tryCatchAsync} from "@/utils/tryCatchAsync";

class IdentityManager {
    private readonly secret = new TextEncoder().encode(process.env.JWT_SECRET!)

    async createIdentity(identity: CustomerIdentity | ReferrerIdentity) {
        return new SignJWT(identity)
            .setProtectedHeader({ alg: "HS256" })
            .setExpirationTime("24h")
            .sign(this.secret);
    }

    async getReferrerOrThrow(reqOrSession: NextApiRequest | string): Promise<ReferrerIdentity> {
        const session = typeof reqOrSession === 'string' ? reqOrSession : reqOrSession.cookies.referrerSession;
        if (!session) {
            throw new UnauthenticatedError('referrerSession cookie is not found')
        }

        const { data: verified, error } = await tryCatchAsync(jwtVerify(session, this.secret));
        if (error) {
            throw new UnauthenticatedError('Failed to verify JWT')
        }

        const { referrerId, isAdmin } = verified.payload
        if (typeof referrerId !== 'number') {
            throw new UnauthenticatedError('No valid referrerId is present in the JWT')
        }

        return {
            referrerId,
            isAdmin: Boolean(isAdmin)
        }
    }

    async getCustomerOrThrow(reqOrSession: NextApiRequest | string): Promise<CustomerIdentity> {
        const session = typeof reqOrSession === 'string' ? reqOrSession : reqOrSession.cookies.customerSession;
        if (!session) {
            throw new UnauthenticatedError('customerSession cookie is not found')
        }

        const { data: verified, error } = await tryCatchAsync(jwtVerify(session, this.secret));
        if (error) {
            throw new UnauthenticatedError('Failed to verify JWT')
        }

        const { customerId } = verified.payload
        if (typeof customerId !== 'number') {
            throw new UnauthenticatedError('No valid customerId is present in the JWT')
        }

        return {
            customerId
        }
    }
}

export const identityManager = new IdentityManager()