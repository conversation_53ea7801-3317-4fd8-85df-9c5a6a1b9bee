// src/server/contract/ContractRepository.ts
import {type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class ContractRepository extends BaseRepository<
    (c: PrismaClient | Prisma.TransactionClient) => Prisma.ContractDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.contract);
    }

    findOneOrThrow<T extends Prisma.ContractFindUniqueArgs>(
        args: Prisma.SelectSubset<T, Prisma.ContractFindUniqueArgs>
    ) {
        return this.db.contract.findUniqueOrThrow(args);
    }
}
