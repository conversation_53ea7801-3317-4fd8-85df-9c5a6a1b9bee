import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { ContractRepository } from './ContractRepository';

export class ContractService {
    private repo: ContractRepository;

    constructor(private readonly db: PrismaClient) {
        this.repo = new ContractRepository(db);
    }

    async list(params: {
        where: Prisma.ContractWhereInput;
        orderBy:
        | Prisma.ContractOrderByWithRelationInput
        | Prisma.ContractOrderByWithRelationInput[];
        skip: number;
        take: number;
    }) {
        const [items, total] = await this.db.$transaction([
            this.db.contract.findMany({
                where: params.where,
                orderBy: params.orderBy,
                skip: params.skip,
                take: params.take,
            }),
            this.db.contract.count({ where: params.where }),
        ]);

        return { items, total };
    }

    getContract(
        where: Prisma.ContractWhereInput,
        options?: {
            select?: Prisma.ContractSelect;
            include?: Prisma.ContractInclude;
            orderBy?: Prisma.ContractOrderByWithRelationInput;
        }
    ) {
        return this.repo.findOne({ where, ...options });
    }

    async update(
        where: Prisma.ContractWhereUniqueInput,
        data: Prisma.ContractUpdateInput,
        options?: {
            select?: Prisma.ContractSelect;
            include?: Prisma.ContractInclude;
        }
    ) {
        return this.repo.update({ where, data, ...options });
    }

    async create(
        data: Prisma.ContractCreateInput,
        options?: {
            select?: Prisma.ContractSelect;
            include?: Prisma.ContractInclude;
        }
    ) {
        return this.repo.create({ data, ...options });
    }

    getContractOrThrow<T extends Prisma.ContractFindUniqueArgs>(
        args: Prisma.SelectSubset<T, Prisma.ContractFindUniqueArgs>
    ) {
        return this.repo.findOneOrThrow(args);
    }
}
