// src/server/infra/createEndpoint.ts
import { ConsoleLogger } from "@/server/ConsoleLogger";
import type { EndpointHandler } from "@/server/domain";
import type { NextApiRequest, NextApiResponse, NextApiHandler } from "next";

import {badRequest, internalServerError, notAllowed, unauthenticated} from "./responses";
import {Logger} from "@/server/domain";
import {AccessTokenRepository} from "@/server/AccessTokenRepository";
import * as z from "zod";
import {EmailService} from "@/server/EmailService";
import {UnauthenticatedError} from "@/server/errors";

type SupportedMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
type Handlers = Partial<Record<SupportedMethod, EndpointHandler>>;

export function createEndpoint(handlers: Handlers): NextApiHandler {
    const logger = new ConsoleLogger();

    return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
            const method = (req.method || "GET").toUpperCase() as SupportedMethod;
            const handler = handlers[method];

            if (!handler) {
                const supported = Object.keys(handlers) as SupportedMethod[];
                return notAllowed(res, req, supported);
            }

            return await executeHandler({ handler, req, res, logger });
        } catch (err: any) {
            logger.error(err);

            if (err instanceof UnauthenticatedError) {
                return unauthenticated(res)
            }
            return internalServerError(res);
        }
    };
}

type ExecuteHandlerParams = {
    handler: EndpointHandler;
    req: NextApiRequest;
    res: NextApiResponse;
    logger: Logger;
};

async function executeHandler(params: ExecuteHandlerParams) {
    const { handler, logger, res, req } = params

    const validationError = await validateRequest(params);
    if (validationError) {
        return badRequest(res, validationError);
    }

    return await handler.execute({
        req,
        res,
        logger,
        accessTokenRepository: new AccessTokenRepository(),
        emailService: new EmailService(),
    });
}

const validateRequest = async ({ handler, req }: ExecuteHandlerParams) => {
    const { schemas, validate } = handler;

    if (schemas?.body) {
        const result = schemas.body.safeParse(req.body)
        if (result.error) {
            return z.prettifyError(result.error)
        }
    }
    if (schemas?.query) {
        const result = schemas.query.safeParse(req.query)
        if (result.error) {
            return z.prettifyError(result.error)
        }
    }
    if (validate) {
        return validate({req});
    }
}
