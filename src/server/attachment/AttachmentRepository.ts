// src/server/contract/ContractRepository.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class AttachmentRepository extends BaseRepository<
    (c: PrismaClient | Prisma.TransactionClient) => Prisma.AttachmentDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.attachment);
    }
}
