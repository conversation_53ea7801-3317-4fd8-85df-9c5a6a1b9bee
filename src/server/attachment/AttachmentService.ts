import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { AttachmentRepository } from './AttachmentRepository';

export class AttachmentService {
  private repo: AttachmentRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new AttachmentRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.db.attachment.findMany({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.db.attachment.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single agency by arbitrary filter */
  findOne(
    where: Prisma.AttachmentWhereInput,
    options?: {
      select?: Prisma.AttachmentSelect;
      include?: Prisma.AttachmentInclude;
      orderBy?: Prisma.AttachmentOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.AttachmentCreateInput,
    options?: {
      select?: Prisma.AttachmentSelect;
      include?: Prisma.AttachmentInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
