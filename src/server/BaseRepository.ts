import { HttpClient } from "@/utils/HttpClient";
import {EntityMapper, OffsetPagination} from "@/types";
import {NotFoundError} from "@/server/errors";
import qs from 'qs'
import {isObject} from "@/utils/isObject";

export abstract class BaseRepository<
    TEntity,
    TFilters extends Record<string, unknown> = { [key in keyof TEntity]?: any }
> {
    protected abstract readonly endpoint: string;
    protected readonly mapper?: EntityMapper<TEntity>;

    protected constructor(protected readonly httpClient: HttpClient) {}

    async findById(id: string): Promise<TEntity> {
        const entity = await this.httpClient.strapiRequest(`${this.endpoint}/${id}`);
        return this.maybeToDomain(entity);
    }

    async findOne(
        filters: TFilters = {} as TFilters,
        other?: { sort?: string[], pagination?: OffsetPagination }
    ): Promise<TEntity> {
        const entities = await this.findAll(filters, other);
        if (entities.length === 0) {
            throw new NotFoundError()
        }

        return entities[0];
    }

    async findAll(
        filters: TFilters = {} as TFilters,
        other?: { sort?: string[], pagination?: OffsetPagination }
    ): Promise<TEntity[]> {
        const { sort, pagination } = other ?? {}
        const searchParams = this.buildSearchParams(filters, sort, pagination);
        const url = searchParams ? `${this.endpoint}?${searchParams}` : this.endpoint;
        
        const entities = await this.httpClient.strapiRequest(url);
        return entities.map((entity: any) => this.maybeToDomain(entity));
    }

    async create(entity: TEntity): Promise<TEntity> {
        const createdEntity = await this.httpClient.strapiRequest(this.endpoint, {
            method: 'POST',
            body: this.maybeToStorage(entity)
        });

        return this.maybeToDomain(createdEntity);
    }

    async set(id: string, entity: TEntity): Promise<TEntity> {
        const url = `${this.endpoint}/${id}`;
        const updatedEntity = await this.httpClient.strapiRequest(url, {
            method: 'PUT',
            body: this.maybeToStorage(entity)
        });

        return this.maybeToDomain(updatedEntity);
    }

    protected buildSearchParams(filters: TFilters, sort?: string[], pagination?: OffsetPagination): string {
        const unsimplifiedFilters = Object.entries(filters).reduce((acc, [key, value]) => {
            const unsimplifiedValue = isObject(value) ? value : { $eq: value };
            return {
                ...acc,
                [key]: unsimplifiedValue
            }
        }, {} as TFilters);

        return qs.stringify({
            filters: unsimplifiedFilters,
            sort,
            pagination
        });
    }

    protected maybeToDomain(storageEntity: any) {
        return this.mapper?.toDomain(storageEntity) ?? storageEntity
    }

    protected maybeToStorage(domainEntity: any) {
        return this.mapper?.toStorage(domainEntity) ?? domainEntity
    }
}
