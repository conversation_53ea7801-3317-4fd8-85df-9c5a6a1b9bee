import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { InsuranceConditionRepository } from './IsuranceConditionRepository';

export class InsuranceConditionService {
  private repo: InsuranceConditionRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new InsuranceConditionRepository(db);
  }

  async list(params: {
    where: Prisma.InsuranceConditionWhereInput;
    orderBy:
      | Prisma.InsuranceConditionOrderByWithRelationInput
      | Prisma.InsuranceConditionOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction([
      this.repo.findAll({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.repo.count({ where: params.where }),
    ]);

    return { items, total };
  }

  getInsuranceCondition(
    where: Prisma.InsuranceConditionWhereInput,
    options?: {
      select?: Prisma.InsuranceConditionSelect;
      orderBy?: Prisma.InsuranceConditionOrderByWithRelationInput;
    }
  ) {
    return this.repo.findOne({ where, ...options });
  }

  async create(
    data: Prisma.InsuranceConditionCreateInput,
    options?: {
      select?: Prisma.InsuranceConditionSelect;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
