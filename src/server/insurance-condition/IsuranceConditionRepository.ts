import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

export class InsuranceConditionRepository extends BaseRepository<
    (
        c: PrismaClient | Prisma.TransactionClient
    ) => Prisma.InsuranceConditionDelegate
> {
    constructor(db: PrismaClient) {
        super(db, (c) => c.insuranceCondition);
    }
}
