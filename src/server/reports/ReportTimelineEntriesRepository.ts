import {TimelineEntry} from "@/types";
import {timelineEntryMapper} from "@/utils/mappers";
import {HttpClient} from "@/utils/HttpClient";
import {BaseRepository} from "@/server/BaseRepository";

export class ReportTimelineEntriesRepository extends BaseRepository<TimelineEntry> {
    protected readonly endpoint = '/report-timeline-entries';
    protected readonly mapper = timelineEntryMapper;

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }
}