import {ReportPrecheckTemplate, } from "@/types";
import {reportPrecheckTemplateMapper} from "@/utils/mappers";

export class ReportPrecheckTemplatesRepository {
    constructor(private readonly token: string) {}

    async findAllByInsuranceType(insuranceType: string): Promise<ReportPrecheckTemplate[]> {
        try {
            const response = await fetch(`${process.env.STRAPI_BASE_URL}/report-precheck-templates?filters[insurance_type][$eq]=${insuranceType}`, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            })
            if (!response.ok) {
                throw new Error(`Failed to fetch: ${await response.text()}`)
            }
            const strapiResponse = await response.json()

            return strapiResponse.data.map(reportPrecheckTemplateMapper.toDomain)
        } catch (error) {
            throw new Error(`ReportPrecheckTemplatesRepository.findAllByInsuranceType(${insuranceType}): ${error}`)
        }
    }
}