import {ReportPrecheck} from "@/types";
import {reportPrecheckMapper} from "@/utils/mappers";
import {HttpClient} from "@/utils/HttpClient";
import {BaseRepository} from "@/server/BaseRepository";

export class ReportPrechecksRepository extends BaseRepository<ReportPrecheck> {
    protected readonly endpoint = '/report-prechecks';
    protected readonly mapper = reportPrecheckMapper;

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }
}
