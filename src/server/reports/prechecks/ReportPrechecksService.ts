import {AuthorType, ReportData, ReportPrecheckStatus, ReportPrecheckTemplate, User} from "@/types";
import {ContractsRepository} from "@/server/ContractsRepository";
import {ReportPrecheckTemplatesRepository} from "@/server/reports/prechecks/ReportPrecheckTemplatesRepository";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";

export class ReportPrechecksService {
    constructor(
        private readonly contractsRepository: ContractsRepository,
        private readonly precheckTemplatesRepository: ReportPrecheckTemplatesRepository,
        private readonly prechecksRepository: ReportPrechecksRepository,
        private readonly authUser: User = {},
    ) {}

    async createForReport({ contract_number, report_number }: ReportData) {
        const { agent_number } = this.authUser;

        const { contract_type } = await this.contractsRepository.findByContractNumber(contract_number);
        const templates = await this.precheckTemplatesRepository.findAllByInsuranceType(contract_type);

        return Promise.all(templates.map(
            template => this.createByTemplate({ template, agent_number, report_number })
        ))
    }

    private async createByTemplate({ template, agent_number, report_number }: CreateByTemplateParams) {
        return this.prechecksRepository.create({
            id: '',
            author_type: AuthorType.AI,
            status: ReportPrecheckStatus.EMPTY,
            timestamp: new Date().toISOString(),
            agent_number,
            report_number,
            index: template.index,
            title: template.title,
            tooltip: template.tooltip,
        })
    }
}


type CreateByTemplateParams = {
    template: ReportPrecheckTemplate
    report_number: string,
    agent_number: string,
}
