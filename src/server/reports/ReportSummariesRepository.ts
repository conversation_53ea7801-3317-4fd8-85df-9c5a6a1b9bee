import {ReportAttachment, ReportSummary, TimelineEntry} from "@/types";
import {reportAttachmentMapper, reportSummaryMapper, timelineEntryMapper} from "@/utils/mappers";
import {HttpClient} from "@/utils/HttpClient";
import {BaseRepository} from "@/server/BaseRepository";

export class ReportSummariesRepository extends BaseRepository<ReportSummary> {
    protected readonly endpoint = '/report-summaries';
    protected readonly mapper = reportSummaryMapper;

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }
}