import {ReportAttachment, TimelineEntry} from "@/types";
import {reportAttachmentMapper, timelineEntryMapper} from "@/utils/mappers";
import {HttpClient} from "@/utils/HttpClient";
import {BaseRepository} from "@/server/BaseRepository";

export class ReportAttachmentsRepository extends BaseRepository<ReportAttachment> {
    protected readonly endpoint = '/report-attachments';
    protected readonly mapper = reportAttachmentMapper;

    constructor(httpClient: HttpClient) {
        super(httpClient);
    }
}