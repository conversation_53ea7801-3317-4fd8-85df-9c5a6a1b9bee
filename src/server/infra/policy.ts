// policy.ts
export type Action = 'read' | 'create' | 'update' | 'delete';
export type Role = 'admin' | 'manager' | 'user';

export type PolicyContext = {
  userId: string;
  roles: Role[];
  tenantId?: string;
  agentNumber?: string;
  // add anything from Keycloak later: groups, realm roles, scopes, orgs, etc.
};

type MergeWhere<T> = (a?: T, b?: T) => T | undefined;

/**
 * Minimal policy contract per model.
 * - scope: row-level constraint to AND with the user query
 * - canCreate/canUpdate/canDelete: throw or return false if forbidden
 * - filterWrite: strips/augments write data (e.g., enforce tenantId)
 */
export type ModelPolicy<WhereInput, CreateInput, UpdateInput> = {
  scope?(ctx: PolicyContext): Partial<WhereInput> | undefined;
  canCreate?(ctx: PolicyContext, data: CreateInput): boolean | string;
  canUpdate?(
    ctx: PolicyContext,
    where: WhereInput,
    data: UpdateInput
  ): boolean | string;
  canDelete?(ctx: PolicyContext, where: WhereInput): boolean | string;

  // Field-level write constraints (optional)
  filterWrite?(
    ctx: PolicyContext,
    data: CreateInput | UpdateInput,
    action: 'create' | 'update'
  ): Partial<CreateInput & UpdateInput>;
};

export type PolicyMap = {
  // Keyed by Prisma model name ("User", "Post", ...)
  [modelName: string]: ModelPolicy<any, any, any>;
};
