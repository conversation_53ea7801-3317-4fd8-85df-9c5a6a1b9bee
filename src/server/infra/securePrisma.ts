// src/server/securePrisma.ts
import { type PrismaClient } from '@prisma/client';

import {
  type RevisionModel,
  writeRevision,
} from '@/utils/mongoDB/mongoDBFunctions';

import { type PolicyContext } from './policy';

/** ===== Types ===== */
export type ModelPolicy<
  WhereInput = any,
  CreateInput = any,
  UpdateInput = any,
> = {
  scope?(ctx: PolicyContext): Partial<WhereInput> | undefined;
  canCreate?(ctx: PolicyContext, data: CreateInput): boolean | string;
  canUpdate?(
    ctx: PolicyContext,
    where: WhereInput,
    data: UpdateInput
  ): boolean | string;
  canDelete?(ctx: PolicyContext, where: WhereInput): boolean | string;
  filterWrite?(
    ctx: PolicyContext,
    data: CreateInput | UpdateInput,
    action: 'create' | 'update'
  ): Partial<CreateInput & UpdateInput>;
};

export type PolicyMap = {
  [modelName: string]: ModelPolicy<any, any, any> | undefined;
};

export class ForbiddenError extends Error {
  constructor(message = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

/** ===== Helpers ===== */
function andWhere<T extends object | undefined>(a?: T, b?: T): T | undefined {
  if (!a) return b as T;
  if (!b) return a as T;
  return { AND: [a, b] } as T;
}
function ensureOk(ok: boolean | string | undefined, fallback: string) {
  if (ok === true || ok === undefined) return;
  throw new ForbiddenError(typeof ok === 'string' ? ok : fallback);
}

function isRevisionModel(model: string): model is RevisionModel {
  model = model.toLowerCase();
  return (
    model === 'agent' ||
    model === 'contract' ||
    model === 'customer' ||
    model === 'invoice'
  );
}

/** ===== Factory ===== */
export function createPolicyPrisma<T extends PrismaClient>(
  prisma: T,
  policies: PolicyMap
) {
  const hasExtends = typeof (prisma as any).$extends === 'function';
  if (!hasExtends) {
    throw new Error(
      "PrismaClient at runtime has no $extends(). Make sure you're using the Node client (@prisma/client, not /edge) and Prisma v5+."
    );
  }

  // Root extension that adds `withPolicy`
  const root = (prisma as any).$extends({
    name: 'policy-root',
    client: {
      withPolicy(ctx: PolicyContext) {
        const base = prisma as any;

        const instance = base.$extends({
          name: 'policy-instance',
          query: {
            $allModels: {
              findMany({ model, args, query }: any) {
                const p = policies[model];
                if (p?.scope) args.where = andWhere(args.where, p.scope(ctx));
                return query(args);
              },
              findFirst({ model, args, query }: any) {
                const p = policies[model];
                if (p?.scope) args.where = andWhere(args.where, p.scope(ctx));
                return query(args);
              },
              findUnique({ model, args, query }: any) {
                const p = policies[model];
                if (!p?.scope) return query(args);
                const scopedWhere = andWhere<any>(
                  p.scope(ctx),
                  (args as any).where
                );
                return (base as any)[model].findFirst({ where: scopedWhere });
              },
              count({ model, args, query }: any) {
                const p = policies[model];
                if (p?.scope) args.where = andWhere(args.where, p.scope(ctx));
                return query(args);
              },
              create({ model, args, query }: any) {
                const p = policies[model];
                if (p?.filterWrite)
                  args.data = p.filterWrite(ctx, args.data, 'create');
                if (p?.canCreate)
                  ensureOk(
                    p.canCreate(ctx, args.data),
                    `${model}: create not allowed`
                  );
                return query(args);
              },
              createMany({ model, args, query }: any) {
                const p = policies[model];
                if (p?.filterWrite) {
                  args.data = Array.isArray(args.data)
                    ? args.data.map((d: any) =>
                        p.filterWrite!(ctx, d, 'create')
                      )
                    : p.filterWrite(ctx, args.data, 'create');
                }
                if (p?.canCreate) {
                  const list = Array.isArray(args.data)
                    ? args.data
                    : [args.data];
                  for (const d of list)
                    ensureOk(
                      p.canCreate(ctx, d),
                      `${model}: create not allowed`
                    );
                }
                return query(args);
              },
              // ----- UPDATE (single) -----
              async update({ model, args, query }: any) {
                const p = policies[model];
                const originalWhere = args.where;
                if (p?.filterWrite)
                  args.data = p.filterWrite(ctx, args.data, 'update');

                if (p?.scope) {
                  const scopedWhere = andWhere(originalWhere, p.scope(ctx));
                  ensureOk(
                    p?.canUpdate?.(ctx, scopedWhere, args.data),
                    `${model}: update not allowed`
                  );

                  // [REV] fetch BEFORE
                  const before = isRevisionModel(model)
                    ? await (base as any)[model].findFirst({
                        where: scopedWhere,
                      })
                    : undefined;

                  const updated = await (base as any)[model].updateMany({
                    where: scopedWhere,
                    data: args.data,
                  });
                  if (updated.count === 0)
                    throw new ForbiddenError(
                      `${model}: update affected 0 rows`
                    );

                  // return single row if originalWhere targeted one; otherwise updated
                  const after = originalWhere
                    ? await (base as any)[model].findFirst({
                        where: scopedWhere,
                      })
                    : undefined;

                  // [REV] save delta
                  if (isRevisionModel(model)) {
                    await writeRevision({ model, before, after, ctx });
                  }

                  return originalWhere ? after : updated;
                }

                ensureOk(
                  p?.canUpdate?.(ctx, originalWhere, args.data),
                  `${model}: update not allowed`
                );

                // [REV] fetch BEFORE (no scope path)
                const before = isRevisionModel(model)
                  ? await (base as any)[model].findFirst({
                      where: originalWhere,
                    })
                  : undefined;

                const result = await query(args);

                // [REV] fetch AFTER (single update)
                const after = isRevisionModel(model)
                  ? await (base as any)[model].findFirst({
                      where: originalWhere,
                    })
                  : undefined;

                if (isRevisionModel(model)) {
                  await writeRevision({ model, before, after, ctx });
                }

                return result;
              },
              // ----- UPDATE MANY -----
              async updateMany({ model, args, query }: any) {
                const p = policies[model];
                if (p?.filterWrite)
                  args.data = p.filterWrite(ctx, args.data, 'update');
                if (p?.scope) args.where = andWhere(args.where, p.scope(ctx));
                ensureOk(
                  p?.canUpdate?.(ctx, args.where, args.data),
                  `${model}: update not allowed`
                );

                // [REV] fetch BEFORE set (cap to avoid huge loads)
                let befores: any[] = [];
                if (isRevisionModel(model)) {
                  befores = await (base as any)[model].findMany({
                    where: args.where,
                    take: 50, // safety cap; adjust as needed
                  });
                }

                const result = await query(args);

                // [REV] fetch AFTER set and write per-row
                if (isRevisionModel(model) && befores.length > 0) {
                  const ids = befores.map((r: any) => r.id).filter(Boolean);
                  const afters = await (base as any)[model].findMany({
                    where: { id: { in: ids } },
                  });
                  const afterById = new Map(afters.map((r: any) => [r.id, r]));
                  await Promise.all(
                    befores.map((before) =>
                      writeRevision({
                        model,
                        before,
                        after: afterById.get(before.id) ?? null,
                        ctx,
                      })
                    )
                  );
                }

                return result;
              },
              // ----- DELETE (single) -----
              async delete({ model, args, query }: any) {
                const p = policies[model];
                const originalWhere = args.where;
                if (p?.scope) {
                  const scopedWhere = andWhere(originalWhere, p.scope(ctx));
                  ensureOk(
                    p?.canDelete?.(ctx, scopedWhere),
                    `${model}: delete not allowed`
                  );

                  // [REV] fetch BEFORE
                  const before = isRevisionModel(model)
                    ? await (base as any)[model].findFirst({
                        where: scopedWhere,
                      })
                    : undefined;

                  const result = await (base as any)[model].deleteMany({
                    where: scopedWhere,
                  });

                  // [REV] save with after=null
                  if (isRevisionModel(model) && before) {
                    await writeRevision({ model, before, after: null, ctx });
                  }
                  return result;
                }

                ensureOk(
                  p?.canDelete?.(ctx, originalWhere),
                  `${model}: delete not allowed`
                );

                // [REV] fetch BEFORE
                const before = isRevisionModel(model)
                  ? await (base as any)[model].findFirst({
                      where: originalWhere,
                    })
                  : undefined;

                const result = await query(args);

                if (isRevisionModel(model) && before) {
                  await writeRevision({ model, before, after: null, ctx });
                }
                return result;
              },

              // ----- DELETE MANY -----
              async deleteMany({ model, args, query }: any) {
                const p = policies[model];
                if (p?.scope) args.where = andWhere(args.where, p.scope(ctx));
                ensureOk(
                  p?.canDelete?.(ctx, args.where),
                  `${model}: delete not allowed`
                );

                // [REV] fetch BEFORE set (cap)
                let befores: any[] = [];
                if (isRevisionModel(model)) {
                  befores = await (base as any)[model].findMany({
                    where: args.where,
                    take: 50, // safety cap
                  });
                }

                const result = await query(args);

                // [REV] save each with after=null
                if (isRevisionModel(model) && befores.length > 0) {
                  await Promise.all(
                    befores.map((before) =>
                      writeRevision({ model, before, after: null, ctx })
                    )
                  );
                }
                return result;
              },
            },
          },
        });

        return instance;
      },
    },
  });

  return root as T & {
    withPolicy(ctx: PolicyContext): T;
  };
}
