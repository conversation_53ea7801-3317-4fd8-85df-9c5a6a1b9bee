// src/server/db.ts
import { policies } from '@/server/policies';

import { prisma } from './prismaPostgres'; // <-- use the singleton
import { createPolicyPrisma } from './securePrisma';

// Create the policy-aware wrapper ONCE (no per-request middleware registration)
const policyDb = createPolicyPrisma(prisma, policies);

// Export a helper that only injects the user context (cheap), no new clients
export function dbWithUser(ctx: {
  userId: string;
  roles: string[];
  agentNumber: string;
  agencyNumber: string;
}) {
  return policyDb.withPolicy(ctx);
}

// (optional) export the base db if you need it without user context
export { policyDb as db };
