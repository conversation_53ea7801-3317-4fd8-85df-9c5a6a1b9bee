// src/repo/BaseRepository.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

type AnyClient = PrismaClient | Prisma.TransactionClient;

// A function that, given a Prisma client (root or tx), returns the model delegate
export type DelegateSelector<TDelegate> = (c: AnyClient) => TDelegate;

// Helper types to extract the 1st parameter and return type of a delegate method
type Arg<T, K extends keyof T> = T[K] extends (
  arg: infer A,
  ...rest: any[]
) => any
  ? A
  : never;
type Res<T, K extends keyof T> = T[K] extends (...a: any[]) => infer R
  ? R
  : never;

/**
 * BaseRepository with *no* model-specific Args in its generics.
 * It infers method types from the actual delegate you pass via `selectDelegate`.
 */
export abstract class BaseRepository<S extends DelegateSelector<any>> {
  protected constructor(
    protected readonly db: PrismaClient,
    /** e.g. (c) => c.customer, (c) => c.contract, (c) => c.agent, ... */
    protected readonly selectDelegate: S
  ) {}

  /** resolve delegate from tx if provided, otherwise from base db */
  protected d(tx?: Prisma.TransactionClient): ReturnType<S> {
    return this.selectDelegate((tx ?? this.db) as AnyClient);
  }

  // ------- CRUD methods with inferred types from the delegate -------

  findAll(
    args?: Arg<ReturnType<S>, 'findMany'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'findMany'> {
    return this.d(tx).findMany(args as any);
  }

  findOne(
    args?: Arg<ReturnType<S>, 'findFirst'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'findFirst'> {
    return this.d(tx).findFirst(args as any);
  }

  findById(
    args: Arg<ReturnType<S>, 'findUnique'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'findUnique'> {
    return this.d(tx).findUnique(args as any);
  }

  count(
    args?: Arg<ReturnType<S>, 'count'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'count'> {
    return this.d(tx).count(args as any);
  }

  create(
    args: Arg<ReturnType<S>, 'create'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'create'> {
    return this.d(tx).create(args as any);
  }

  update(
    args: Arg<ReturnType<S>, 'update'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'update'> {
    return this.d(tx).update(args as any);
  }

  updateMany(
    args: Arg<ReturnType<S>, 'updateMany'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'updateMany'> {
    return this.d(tx).updateMany(args as any);
  }

  delete(
    args: Arg<ReturnType<S>, 'delete'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'delete'> {
    return this.d(tx).delete(args as any);
  }

  deleteMany(
    args: Arg<ReturnType<S>, 'deleteMany'>,
    tx?: Prisma.TransactionClient
  ): Res<ReturnType<S>, 'deleteMany'> {
    return this.d(tx).deleteMany(args as any);
  }
}
