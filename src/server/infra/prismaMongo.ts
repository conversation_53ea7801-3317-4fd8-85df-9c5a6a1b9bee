// src/server/infra/prismaMongo.ts
// import { PrismaClient } from "@/generated/prisma-mongo";

import { PrismaClient } from '@prisma/client';

const g = globalThis as unknown as { prismaMongo?: PrismaClient };

export const prismaMongo =
  g.prismaMongo ??
  new PrismaClient({
    // log: ["warn", "error"], // optional
  });

if (process.env.NODE_ENV !== 'production') {
  g.prismaMongo = prismaMongo;
}
