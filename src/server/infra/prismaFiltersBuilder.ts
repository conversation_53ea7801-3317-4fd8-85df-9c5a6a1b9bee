// src/server/queryToWhere.ts
import { Prisma } from '@prisma/client';
import { type NextApiRequest } from 'next';

import { type FieldConfig } from '@/components/filters/types';

const SKIP = new Set(['limit', 'offset', 'sort', 'sortField', 'sortDirection']);

type FieldKind = 'string' | 'number' | 'boolean' | 'date' | 'json' | 'unknown';
type ParseOpts = {
    rootModel?: string;
    /** Optional: bring your filter config so we can coerce like the client */
    fields?: Record<string, FieldConfig>;
    /** Optional: quick overrides if you don't want to import FieldConfig */
    coerceOverrides?: Record<string, FieldKind>; // e.g. { type: "number" }
};

// Build a lightweight model → fields map from Prisma DMMF (once per process)
type DmmfField = {
    name: string;
    kind: 'scalar' | 'object' | 'enum';
    type: string;
};
type DmmfModel = { name: string; fields: DmmfField[] };

const DMMF_MODELS: Map<string, DmmfModel> = (() => {
    const models: DmmfModel[] = (Prisma as any)?.dmmf?.datamodel?.models ?? [];
    const map = new Map<string, DmmfModel>();
    for (const m of models) {
        map.set(m.name, {
            name: m.name,
            fields: m.fields.map((f: any) => ({
                name: f.name,
                kind: f.kind,
                type: f.type,
            })),
        });
    }
    return map;
})();

const scalarToKind: Record<string, FieldKind> = {
    String: 'string',
    Int: 'number',
    BigInt: 'number',
    Float: 'number',
    Decimal: 'number',
    Boolean: 'boolean',
    DateTime: 'date',
    Json: 'json',
    Bytes: 'unknown',
};

// Resolve the field kind by walking `path` from `rootModel` across relations.
function resolvePathKind(
    rootModel: string | undefined,
    path: string
): FieldKind | undefined {
    if (!rootModel || !DMMF_MODELS.size) return undefined;
    const segments = path.split('.');
    let model = DMMF_MODELS.get(rootModel);
    for (let i = 0; i < segments.length; i++) {
        if (!model) return undefined;
        const seg = segments[i];
        const f = model.fields.find((x) => x.name === seg);
        if (!f) return undefined;
        if (f.kind === 'object') {
            // relation: descend to related model
            model = DMMF_MODELS.get(f.type);
            continue;
        }
        if (f.kind === 'enum') return 'string';
        if (f.kind === 'scalar') return scalarToKind[f.type] ?? 'unknown';
    }
    return undefined;
}

// Coerce one scalar by resolved kind and/or op (string ops force string)
function coerceByKind(v: any, kind?: FieldKind, op?: string) {
    if (v === 'null') return null;

    const baseOp = (op ?? '').replace(/_i$/, '');
    const stringOps = new Set(['contains', 'startsWith', 'endsWith']);
    if (stringOps.has(baseOp)) return String(v);

    switch (kind) {
        case 'string':
            return String(v);
        case 'number': {
            const n = Number(v);
            if (Number.isFinite(n)) return n;
            // as a fallback, keep it string so Prisma doesn't crash on type mismatch
            return String(v);
        }
        case 'boolean': {
            const s = String(v).toLowerCase();
            if (s === 'true') return true;
            if (s === 'false') return false;
            return Boolean(v);
        }
        case 'date':
            return new Date(v);
        case 'json':
            // try JSON.parse, otherwise keep as string
            try {
                return typeof v === 'string' ? JSON.parse(v) : v;
            } catch {
                return String(v);
            }
        default: {
            // Heuristics like your original code
            if (/^\d{4}-\d{2}-\d{2}(T.*)?$/.test(String(v))) return new Date(v);
            const numericOps = new Set(['gt', 'gte', 'lt', 'lte', 'between']);
            if (numericOps.has(baseOp)) {
                const n = Number(v);
                if (Number.isFinite(n)) return n;
            }
            return String(v);
        }
    }
}

export function parseQuery(
    query: NextApiRequest['query'],
    opts: ParseOpts = {}
) {
    const rootAnd: any[] = [];

    for (const [key, raw] of Object.entries(query)) {
        if (SKIP.has(key)) continue;
        if (raw == null || (Array.isArray(raw) && raw.length === 0)) continue;

        const [path, op = 'eq'] = key.split('__');
        const rawValue: any = Array.isArray(raw) ? raw[0] : raw;

        // Auto-detect kind from Prisma model metadata
        const fieldKind = effectiveKind(path, opts);

        let value: any;
        if (op === 'in') {
            value = String(rawValue)
                .split(',')
                .map((v) => coerceByKind(v.trim(), fieldKind, op));
        } else if (op === 'between') {
            const [a, b] = String(rawValue).split(',');
            const v1 = coerceByKind(a?.trim(), fieldKind, op);
            const v2 = coerceByKind(b?.trim(), fieldKind, op);
            value = [v1, v2];
        } else if (op === 'null' || op === 'notNull') {
            value = String(rawValue).toLowerCase() === 'true';
        } else {
            value = coerceByKind(rawValue, fieldKind, op);
        }

        const cond = leaf(op, value);
        rootAnd.push(pathToNestedWhere(path, cond));
    }

    return rootAnd.length ? { AND: rootAnd } : {};
}

function leaf(op: string, value: any) {
    const ci = op.endsWith('_i');
    const base = ci ? op.slice(0, -2) : op;
    switch (base) {
        case 'eq':
            return { equals: value };
        case 'not':
            return { not: value };
        case 'gt':
            return { gt: value };
        case 'gte':
            return { gte: value };
        case 'lt':
            return { lt: value };
        case 'lte':
            return { lte: value };
        case 'in':
            return { in: value };
        case 'between':
            return { gte: value?.[0], lte: value?.[1] };
        case 'contains':
            return { contains: value, ...(ci && { mode: 'insensitive' }) };
        case 'startsWith':
            return { startsWith: value, ...(ci && { mode: 'insensitive' }) };
        case 'endsWith':
            return { endsWith: value, ...(ci && { mode: 'insensitive' }) };
        case 'null':
            return value ? { equals: null } : {};
        case 'notNull':
            return value ? { not: null } : {};
        default:
            return { equals: value };
    }
}

function pathToNestedWhere(path: string, cond: any) {
    return path
        .split('.')
        .reverse()
        .reduce((acc, key, i) => {
            return { [key]: i === 0 ? cond : acc };
        }, {} as any);
}

function effectiveKind(path: string, opts: ParseOpts): FieldKind | undefined {
    // explicit overrides take precedence
    if (opts.coerceOverrides?.[path]) return opts.coerceOverrides[path];

    const cfg = opts.fields?.[path];
    if (cfg) {
        // if the UI says it's a number, trust it
        if (cfg.valueKind === 'number') return 'number';

        if (cfg.valueKind === 'select') {
            const mode = cfg.coerceValueAs ?? 'auto';
            const allNumeric =
                (cfg.options ?? []).length > 0 &&
                (cfg.options ?? []).every((o) => typeof o.value === 'number');

            if (mode === 'boolean') return 'boolean';
            if (mode === 'number' || (mode === 'auto' && allNumeric)) return 'number';
            if (mode === 'string') return 'string';
            // fall through: if neither numeric nor string enforced, keep undefined
            // so Prisma model inference can still apply.
        }

        if (cfg.valueKind === 'date') return 'date';
        if (cfg.valueKind === 'boolean') return 'boolean';
    }

    // fall back to Prisma model inference
    return resolvePathKind(opts.rootModel, path);
}
