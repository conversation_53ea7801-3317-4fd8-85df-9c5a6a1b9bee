// src/server/infra/createEndpoint.ts
import type { NextApiHandler, NextApiRequest, NextApiResponse } from 'next';

import { Prisma } from '@/generated/prisma-postgres';
import { verifyAccessToken } from '@/server/auth/verifyAccessToken';
import type { EndpointHandler } from '@/server/domain/endpoints';
import type { Logger } from '@/server/domain/logging';
import { ConsoleLogger } from '@/server/infra/ConsoleLogger';
import {
  badRequest,
  internalServerError,
  notAllowed,
  unauthorized,
} from '@/server/responses';

import { dbWithUser } from './postgresDb';
import { ForbiddenError } from './securePrisma';

type SupportedMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
type Handlers = Partial<Record<SupportedMethod, EndpointHandler>>;

export function createEndpoint(handlers: Handlers): NextApiHandler {
  const logger = new ConsoleLogger();

  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const method = (req.method || 'GET').toUpperCase() as SupportedMethod;
      const handler = handlers[method];
      if (!handler) {
        const supported = Object.keys(handlers) as SupportedMethod[];
        return notAllowed(res, req, supported);
      }

      // ---- Extract Bearer token from Authorization header ----
      const authHeader = req.headers.authorization;
      const bearer = authHeader?.startsWith('Bearer ')
        ? authHeader.slice(7)
        : undefined;

      // Verify token (or null if none)
      const verified = bearer ? await verifyAccessToken(bearer) : null;

      const auth = {
        isAuthenticated: !!verified,
        sub: verified?.sub ?? '',
        accessToken: verified?.token ?? '',
        roles: verified?.roles ?? [],
        agencyNumber: verified?.agencyNumber ?? '',
        agentNumber: verified?.agentNumber ?? '',
      };

      return await executeHandler({ handler, req, res, logger, auth });
    } catch (err: any) {
      if (err instanceof ForbiddenError) {
        logger.log('error', `[${err.name}]`, err.message);
        return res.status(403).json({ error: err.message });
      }
      if (err instanceof Prisma.PrismaClientKnownRequestError) {
        logger.log('error', err);
        return res.status(400).json({ error: err.message, code: err.code });
      }
      if (err instanceof Prisma.PrismaClientValidationError) {
        logger.log('error', err);
        return res
          .status(422)
          .json({ error: 'Validation error', detail: String(err.message) });
      }
      logger.log('error', err);
      return internalServerError(res);
    }
  };
}

type ExecuteHandlerParams = {
  handler: EndpointHandler;
  req: NextApiRequest;
  res: NextApiResponse;
  logger: Logger;
  auth: {
    isAuthenticated: boolean;
    sub: string;
    accessToken: string;
    roles: string[];
    agencyNumber: string;
    agentNumber: string;
  };
};

async function executeHandler({
  handler,
  logger,
  res,
  req,
  auth,
}: ExecuteHandlerParams) {
  const {
    validate,
    checkPermissions,
    execute,
    auth: authMode = 'required',
  } = handler;

  if (authMode === 'required' && !auth.isAuthenticated) {
    return unauthorized(res);
  }

  if (validate) {
    const validationError = await validate({ req, auth });
    if (validationError) return badRequest(res, validationError);
  }

  if (checkPermissions) {
    const isPermitted = await checkPermissions({ req, auth });
    if (!isPermitted) return unauthorized(res);
  }

  // Create Prisma with user context
  const db = dbWithUser({
    userId: String(auth.sub ?? ''),
    roles: auth.roles,
    agentNumber: auth.agentNumber,
    agencyNumber: auth.agencyNumber,
  });

  return await execute({ req, res, logger, auth, db });
}
