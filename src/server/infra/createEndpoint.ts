import {ConsoleLogger} from "@/server/infra/ConsoleLogger";
import {Endpoint<PERSON>and<PERSON>} from "@/server/domain/endpoints";
import {NextApiRequest, NextApiResponse} from "next";
import {badRequest, internalServerError, notAllowed, unauthorized} from "@/server/responses";
import {Logger} from "@/server/domain/logging";

type SupportedMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

type Handlers = {
    [key in SupportedMethod]?: EndpointHandler
}

export const createEndpoint = async (handlers: Handlers) => {
    const logger = new ConsoleLogger()

    return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
            for (const [method, handler] of Object.entries(handlers)) {
                if (req.method === method) {
                    return await executeHandler({ handler, req, res, logger });
                }
            }

            const supportedMethods = Object.keys(handlers)
            return notAllowed(res, req, supportedMethods);
        } catch (error) {
            await logger.log('error', error)
            return internalServerError(res)
        }
    }

}

type ExecuteHandlerParams = {
    handler: EndpointHandler,
    req: NextApiRequest,
    res: NextApiResponse
    logger: Logger
}
const executeHandler = async ({ handler, logger, res, req }: ExecuteHandlerParams) => {
    const { validate, checkPermissions, execute } = handler

    if (validate) {
        const validationError = await validate({ req })
        if (validationError) {
            return badRequest(res, validationError)
        }
    }
    // check if user is logged in
    if (checkPermissions) {
        const isPermitted = await checkPermissions({ req })
        if (!isPermitted) {
            return unauthorized(res)
        }
    }

    return execute({ req, res, logger })
}