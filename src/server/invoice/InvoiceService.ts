// src/server/invoice/InvoiceService.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { InvoiceCustomerStatusType } from '@/utils/invoice/types';

import { InvoiceRepository } from './InvoiceRepository';

export class InvoiceService {
  private repo: InvoiceRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new InvoiceRepository(db);
  }

  async list(params: {
    where: Prisma.InvoiceWhereInput;
    orderBy:
      | Prisma.InvoiceOrderByWithRelationInput
      | Prisma.InvoiceOrderByWithRelationInput[];
    skip: number;
    take: number;
  }) {
    const [items, total] = await this.db.$transaction(async (tx) => {
      const [rows, cnt] = await Promise.all([
        this.repo.findAll(
          {
            where: params.where,
            orderBy: params.orderBy,
            skip: params.skip,
            take: params.take,
          },
          tx
        ),
        this.repo.count({ where: params.where }, tx),
      ]);
      return [rows, cnt] as const;
    });

    return { items, total };
  }

  /** Find a single invoice by arbitrary filter */
  findOne(
    where: Prisma.InvoiceWhereInput,
    options?: Omit<Prisma.InvoiceFindFirstArgs, 'where'>
  ) {
    return this.repo.findOne({ where, ...options });
  }

  /** Throwing variant */
  async findOneOrThrow(
    where: Prisma.InvoiceWhereInput,
    options?: Omit<Prisma.InvoiceFindFirstArgs, 'where'>
  ) {
    const row = await this.findOne(where, options);
    if (!row) throw new Error('Invoice not found');
    return row;
  }

  /** Aggregated stats per agency (OPEN/PAID) */
  async getAgencyStatistics(agencyNumber: string) {
    const grouped = await this.repo.groupByStatusForAgencyRaw(agencyNumber);

    if (!grouped || grouped.length === 0) {
      return {
        openInvoices: 0,
        paiedInvoices: 0,
        openAmount: 0,
        paiedAmount: 0,
      };
    }

    let openInvoices = 0;
    let openAmount = 0;
    let paiedInvoices = 0; // keep legacy key spelling
    let paiedAmount = 0;

    for (const row of grouped) {
      const status = row.customerStatus;
      const count = row._count?._all ?? 0;
      const sum = row._sum?.totalGross ?? 0;

      if (status === InvoiceCustomerStatusType.OPEN) {
        openInvoices += count;
        openAmount += sum;
      } else if (status === InvoiceCustomerStatusType.PAID) {
        paiedInvoices += count;
        paiedAmount += sum;
      }
    }

    return { openInvoices, paiedInvoices, openAmount, paiedAmount };
  }

  async create(
    data: Prisma.InvoiceCreateInput,
    options?: {
      select?: Prisma.InvoiceSelect;
      include?: Prisma.InvoiceInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }

  async update(
    where: Prisma.InvoiceWhereUniqueInput,
    data: Prisma.InvoiceUpdateInput,
    options?: {
      select?: Prisma.InvoiceSelect;
      include?: Prisma.InvoiceInclude;
    }
  ) {
    return this.repo.update({ where, data, ...options });
  }

  getInvoiceOrThrow<T extends Prisma.InvoiceFindUniqueArgs>(
    args: Prisma.SelectSubset<T, Prisma.InvoiceFindUniqueArgs>
  ) {
    return this.repo.findOneOrThrow(args);
  }
}
