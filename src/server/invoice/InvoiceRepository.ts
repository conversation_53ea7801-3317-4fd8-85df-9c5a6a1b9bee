// src/server/invoice/InvoiceRepository.ts
import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';
import { BaseRepository } from '@/server/infra/BaseRepository';

type Tx = Prisma.TransactionClient;

type GroupRow = {
  customerStatus: Prisma.InvoiceGroupByOutputType['customerStatus'];
  _count: { _all: number } | null;
  _sum: { totalGross: number | null } | null;
};

export class InvoiceRepository extends BaseRepository<
  (c: PrismaClient | Tx) => Prisma.InvoiceDelegate
> {
  constructor(db: PrismaClient) {
    super(db, (c) => c.invoice);
  }

  groupByStatusForAgencyRaw(
    agencyNumber: string,
    tx?: Tx
  ): Promise<GroupRow[]> {
    return this.d(tx).groupBy({
      by: ['customerStatus'],
      where: { agencyNumber },
      _count: { _all: true },
      _sum: { totalGross: true },
    }) as unknown as Promise<GroupRow[]>;
  }

  findOneOrThrow<T extends Prisma.InvoiceFindUniqueArgs>(
    args: Prisma.SelectSubset<T, Prisma.InvoiceFindUniqueArgs>
  ) {
    return this.db.invoice.findUniqueOrThrow(args);
  }
}
