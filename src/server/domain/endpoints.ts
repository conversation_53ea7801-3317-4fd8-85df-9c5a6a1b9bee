import {NextApiRequest, NextApiResponse} from "next";
import {Logger} from "@/server/domain/logging";

type ValidateParams = {
    req: NextApiRequest;
}
type CheckPermissionsParams = {
    req: NextApiRequest;
}
type ExecuteParams = {
    req: NextApiRequest;
    res: NextApiResponse;
    logger: Logger;
}

export type EndpointHandler = {
    validate?: (params: ValidateParams) => string | void | Promise<string | void>
    checkPermissions?: (params: CheckPermissionsParams) => boolean | Promise<boolean>
    execute: (params: ExecuteParams) => any
}