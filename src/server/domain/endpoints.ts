// domain/endpoints.ts
import type { PrismaClient } from '@prisma/client';
import type { NextApiRequest, NextApiResponse } from 'next';

import type { Logger } from './logging';

// What you derive from the verified Keycloak access token
export type AuthContext = {
  isAuthenticated: boolean;
  sub?: string; // Keycloak user id (JWT "sub")
  accessToken?: string; // the raw access token (optional)
  roles: string[];
  agencyNumber?: string;
  agentNumber?: string;
};

type BaseParams = {
  req: NextApiRequest;
  auth: AuthContext;
};

export type ValidateParams = BaseParams;
export type CheckPermissionsParams = BaseParams;

export type ExecuteParams = BaseParams & {
  res: NextApiResponse;
  logger: Logger;
  db: PrismaClient;
};

// Per-handler auth policy
export type AuthMode = 'required' | 'optional' | 'none';

export type EndpointHandler = {
  auth?: AuthMode; // default "required" is common for APIs; set "optional"/"none" per endpoint
  validate?: (params: ValidateParams) => string | void | Promise<string | void>;
  checkPermissions?: (
    params: CheckPermissionsParams
  ) => boolean | Promise<boolean>;
  execute: (params: ExecuteParams) => any;
};
