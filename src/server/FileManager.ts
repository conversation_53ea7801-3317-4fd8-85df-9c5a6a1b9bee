import {S3} from "aws-sdk";
import {ConsoleLogger} from "@/server/ConsoleLogger";
import {NextApiResponse} from "next";

const s3 = new S3({
    accessKeyId: process.env.IONOS_ACCESS_KEY_ID,
    secretAccessKey: process.env.IONOS_SECRET_ACCESS_KEY,
    endpoint: process.env.IONOS_ENDPOINT_URL,
    region: 'eu-central-3',
    s3ForcePathStyle: true, // Required for IONOS S3
});

const BUCKET_NAME = process.env.IONOS_BUCKET_NAME!;

class FileManager {
    private readonly logger = new ConsoleLogger()

    async upload(key: string, bytes: Uint8Array): Promise<string> {
        const params = {
            Bucket: BUCKET_NAME,
            Key: key,
            Body: Buffer.from(bytes),
            ContentType: 'application/pdf',
        };
        const uploadResult = await s3.upload(params).promise();
    
        this.logger.log('File uploaded to S3 successfully', {
            uploadParams: {
                Bucket: BUCKET_NAME,
                Key: key,
                ContentType: 'application/pdf',
            },
            uploadResult
        });
    
        return uploadResult.Location;
    }

    streamAsResponse(res: NextApiResponse, key: string) {
        const params = {
            Bucket: BUCKET_NAME,
            Key: key,
        }
        const handleError = (error: Error) => {
            this.logger.error('Failed to download a file from S3', error, {
                downloadParams: params,
            });
            res.status(503).json({ error: 'Failed to download a file from S3' })
        }

        s3.getObject(params)
            .createReadStream()
            .on('error', handleError)
            .pipe(res)
    }

}

export const fileManager = new FileManager()