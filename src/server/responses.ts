import { NextApiRequest, NextApiResponse} from "next";

export const unauthenticated = (res: NextApiResponse) => {
    // 401 is officially Unauthorized, but https://stackoverflow.com/a/6937030/13679562
    return res.status(401).json({ error: 'Unauthenticated' });
}

export const forbidden = (res: NextApiResponse) => {
    return res.status(403).json({ error: 'Forbidden' });
}

export const notAllowed = (res: NextApiResponse, req: NextApiRequest, whitelist: string[]) => {
    return res
        .setHeader('Allow', whitelist)
        .status(405)
        .json({ error: `Method ${req.method} Not Allowed` });
}

export const badRequest = (res: NextApiResponse, message: string) => {
    return res.status(400).json({ error: message });
}

export const internalServerError = (res: NextApiResponse, payload?: any) => {
    return res.status(500).json(payload ?? { error: 'An error occurred while processing the request.' });
}
