import {ContractData, User} from "@/types";
import {permissionManager} from "@/utils/permissionManager";
import {NotFoundError} from "@/server/errors";
import {HttpClient} from "@/utils/HttpClient";

export class ContractsRepository {
    constructor(
        private readonly httpClient: HttpClient,
        private readonly authUser: User
    ) {}

    async findByContractNumber(contractNumber: string): Promise<ContractData> {
        const isAdmin = permissionManager.isUserAdmin(this.authUser)

        let url = `/contracts?filters[contract_number][$eq]=${contractNumber}`
        if (!isAdmin) {
            url += `&filters[agency_number][$eq][1]=${this.authUser.agency_number}`
        }

        const contracts = await this.httpClient.strapiRequest(url);
        if (contracts.length === 0) {
            throw new NotFoundError();
        }

        return contracts[0];
    }
}