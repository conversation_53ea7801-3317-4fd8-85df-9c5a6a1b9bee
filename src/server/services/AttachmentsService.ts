import {AttachmentsRepository} from "@/server/repositories/AttachmentsRepository";
import {FileStorage} from "@/server/infra/FileStorage";
import {NotFoundError} from "@/server/errors";
import {ConsoleLogger} from "@/server/infra/ConsoleLogger";

export class AttachmentsService {
    async download(attachmentId: string) {
        const attachmentsRepository = new AttachmentsRepository()
        const fileStorage = new FileStorage()
        const logger = new ConsoleLogger()

        const attachment = await attachmentsRepository.findById(attachmentId)
        if (!attachment) {
            throw new NotFoundError()
        }

        const downloadStream = fileStorage.download(attachment.fileId)
        downloadStream.on('error', (error: any) => {
            logger.log('error', { message: 'Failed to download the file', error, attachment });
            throw error
        });

        return {
            attachmentName: attachment.name,
            downloadStream
        }
    }
}