import {Repository} from "@/server/domain/entities";

export abstract class BaseRepository<TEntity> implements Repository<TEntity> {
    protected constructor() {}

    async findById(id: string): Promise<TEntity | null> {
        return null
    }

    async findOne(): Promise<TEntity | null> {
        return null
    }

    async findAll(): Promise<TEntity[]> {
        return []
    }

    async create(entity: TEntity): Promise<TEntity> {
        return null as never
    }

    async set(id: string, entity: TEntity): Promise<TEntity> {
        return null as never
    }
}
