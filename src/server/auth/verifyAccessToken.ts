// src/server/auth/verifyAccessToken.ts
import { createRemoteJWKSet, type JWTPayload, jwtVerify } from 'jose';

const issuer = process.env.AUTH_KEYCLOAK_ISSUER!; // e.g. https://kc.example.com/realms/myrealm
const clientId = process.env.AUTH_KEYCLOAK_ID!; // your app's clientId
const jwks = createRemoteJWKSet(
  new URL(`${issuer}/protocol/openid-connect/certs`)
);

export type VerifiedUser = {
  sub: string;
  roles: string[];
  agencyNumber?: string;
  agentNumber?: string;
  token: string;
  payload: JWTPayload & Record<string, any>;
};

export async function verifyAccessToken(
  token?: string
): Promise<VerifiedUser | null> {
  if (!token) return null;

  // 1) Verify signature & issuer. Omit 'audience' to avoid JOSE's strict check.
  const { payload } = await jwtVerify(token, jwks, { issuer });

  // 2) Do our own audience/azp check that matches Keycloak behavior.
  const aud = payload.aud;
  const azp = payload.azp as string | undefined; // "authorized party" (clientId)
  const audIncludesClient = Array.isArray(aud)
    ? aud.includes(clientId)
    : aud === clientId;
  const okAudience = audIncludesClient || azp === clientId || aud === 'account';
  if (!okAudience) {
    throw Object.assign(
      new Error(`Invalid audience: aud=${JSON.stringify(aud)}, azp=${azp}`),
      {
        code: 'ERR_INVALID_AUDIENCE',
      }
    );
  }

  // Roles
  const realmRoles: string[] = (payload as any)?.realm_access?.roles ?? [];
  const clientRoles: string[] = Object.values(
    (payload as any)?.resource_access ?? {}
  ).flatMap((v: any) => v?.roles ?? []);
  const roles = Array.from(new Set([...realmRoles, ...clientRoles]));

  // Custom attributes (add protocol mappers for these)
  const agencyNumber = (payload as any)?.agencyNumber as string | undefined;
  const agentNumber = (payload as any)?.agentNumber as string | undefined;

  return {
    sub: String(payload.sub),
    roles,
    agencyNumber,
    agentNumber,
    token,
    payload,
  };
}
