import { type Prisma, type PrismaClient } from '@/generated/prisma-postgres';

import { CustomerRepository } from './CustomerRepository';

export class CustomerService {
  private repo: CustomerRepository;

  constructor(private readonly db: PrismaClient) {
    this.repo = new CustomerRepository(db);
  }

  async list(params: { where: any; orderBy: any; skip: number; take: number }) {
    const [items, total] = await this.db.$transaction([
      this.db.customer.findMany({
        where: params.where,
        orderBy: params.orderBy,
        skip: params.skip,
        take: params.take,
      }),
      this.db.customer.count({ where: params.where }),
    ]);

    return { items, total };
  }

  /** Find a single customer by arbitrary filter */
  findOne(
    where: Prisma.CustomerWhereInput,
    options?: {
      select?: Prisma.CustomerSelect;
      include?: Prisma.CustomerInclude;
      orderBy?: Prisma.CustomerOrderByWithRelationInput;
    }
  ) {
    return this.db.customer.findFirst({ where, ...options });
  }

  /** Optional helper if you prefer throwing semantics */
  async findOneOrThrow(
    where: Prisma.CustomerWhereInput,
    options?: {
      select?: Prisma.CustomerSelect;
      include?: Prisma.CustomerInclude;
      orderBy?: Prisma.CustomerOrderByWithRelationInput;
    }
  ) {
    const row = await this.findOne(where, options);
    if (!row) throw new Error('Customer not found');
    return row;
  }

  async update(
    where: Prisma.CustomerWhereUniqueInput, // { id } | { documentId } | { customerNumber }
    data: Prisma.CustomerUpdateInput,
    options?: {
      select?: Prisma.CustomerSelect;
      include?: Prisma.CustomerInclude;
    }
  ) {
    return this.db.customer.update({ where, data, ...options });
  }

  async create(
    data: Prisma.CustomerCreateInput,
    options?: {
      select?: Prisma.CustomerSelect;
      include?: Prisma.CustomerInclude;
    }
  ) {
    return this.repo.create({ data, ...options });
  }
}
