// src/pages/customer/new.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import CustomerForm from '../../components/CustomerForm';
import Layout from '../../components/layout';

export default function NewCustomer() {
    const router = useRouter();
    const { agent_number, agency_number } = router.query;

    const [agent, setAgent] = useState<string | null>(null);
    const [agency, setAgency] = useState<string | null>(null);

    useEffect(() => {
        if (router.isReady) {
            setAgent((agent_number as string) || localStorage.getItem("agent_number"));
            setAgency((agency_number as string) || localStorage.getItem("agencyNumber"));
        }
    }, [router.isReady, agent_number, agency_number]);

    if (!router.isReady || !agent || !agency) {
        return <p>Lade Daten...</p>;
    }

    return (
        <Layout title="Neuer Kunde" description="<PERSON><PERSON> werden neue Kunden angelegt">
            <CustomerForm
                agent_number={agent}
                agency_number={agency}
            />
        </Layout>
    );
}
