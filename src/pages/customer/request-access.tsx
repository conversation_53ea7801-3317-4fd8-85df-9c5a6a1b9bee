import Layout from "@/components/layout/layout";
import httpClient from "@/utils/HttpClient";
import EmailInputCard from "@/components/card/EmailInputCard";

export default function CustomerRequestAccessPage() {
    async function handleSubmit(input: string) {
        await httpClient.request("/api/customer/restore-access", {
            method: "POST",
            body: {
                email: input
            },
        });
    }

    return (
        <Layout
            htmlTitle="Zugang anfordern"
            metaDescription="Geben Sie Ihre E-Mail-Adresse ein, um Zugang zu Refeo zu erhalten."
        >
            <EmailInputCard onSubmit={handleSubmit} translationsNamespace='customerRequestAccess' />
        </Layout>
    );
}
