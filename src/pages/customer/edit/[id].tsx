// src/pages/customer/edit/[id].tsx
import { useRouter } from 'next/router';
import Layout from '../../../components/layout';
import CustomerForm from '../../../components/CustomerForm';

export default function EditCustomerPage() {
    const router = useRouter();
    const { id } = router.query; 

    if (!id || typeof id !== 'string') {
        return <p>Lade Kundendaten...</p>; 
    }

    return (
        <Layout title="Kunde anpassen" description="Bereich zum anpassen der Kunden">
            <CustomerForm edit_mode={true} customer_number={id} />
        </Layout>
    );
}
