// src/pages/customer/edit/[id].tsx
import { useRouter } from 'next/router';

import CustomerForm from '@/components/CustomerForm';
import Layout from '@/components/layout';

export default function EditCustomerPage() {
  const router = useRouter();
  const { customerNumber } = router.query;

  if (!customerNumber || typeof customerNumber !== 'string') {
    return <p>Lade Kundendaten...</p>;
  }

  return (
    <Layout
      title="Kunde anpassen"
      description="Bereich zum anpassen der Kunden"
    >
      <CustomerForm editMode={true} customerNumber={customerNumber} />
    </Layout>
  );
}
