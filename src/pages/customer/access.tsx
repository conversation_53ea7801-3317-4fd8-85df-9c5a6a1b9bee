import { GetServerSideProps } from "next";
import { prisma } from "@/server/prisma";
import { identityManager } from "@/server/IdentityManager";
import {paths} from "@/paths";

export const getServerSideProps: GetServerSideProps = async (context) => {
  const token = context.query.token;
  if (!token || typeof token !== "string") {
    return { redirect: { destination: paths.customer.unauthorized, permanent: false } };
  }

  const accessToken = await prisma.customerAccessToken.findUnique({
    where: { token },
  });
  // if (!accessToken || accessToken.expiresAt < new Date() || accessToken.usedAt) {
  if (!accessToken || accessToken.expiresAt < new Date()) {
    return { redirect: { destination: paths.customer.unauthorized, permanent: false } };
  }
  await prisma.customerAccessToken.update({
    where: { id: accessToken.id },
    data: { usedAt: new Date() },
  });

  const identity = await identityManager.createIdentity({ customerId: accessToken.customerId })
  context.res.setHeader(
    "Set-Cookie",
    `customerSession=${identity}; Path=/; HttpOnly; Max-Age=${60*60*24}; SameSite=Lax`
  );

  return {
    redirect: { destination: paths.customer.landing, permanent: false },
  };
};

export default function ToDashboardPage() {
  return <p>Redirecting…</p>;
}
