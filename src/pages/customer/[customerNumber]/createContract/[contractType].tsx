// src/pages/customer/[id]/createContract/[contractType].tsx
import { useRouter } from 'next/router';

import ContractForm from '@/components/ContractForm';
import AccidentInsuranceContactForm from '@/components/insuranceForms/AccidentInsuranceContactForm';
import Layout from '@/components/layout';
import { useIsAdmin } from '@/utils/authUtils';

const CreateContractPage = () => {
  return (
    <Layout
      title="Vertrag anlegen"
      description="Bereich zum erstellen des Vertrags"
    >
      <Content />
    </Layout>
  );
}

export default CreateContractPage

const Content = () => {
  const router = useRouter();
  const { customerNumber, contractType } = router.query;
  const isAdmin = useIsAdmin();

  if (!customerNumber || !contractType) {
    return <p>Lade...</p>;
  }

  return contractType == 'unfallversicherung' && !isAdmin ? (
      <AccidentInsuranceContactForm
          customerNumber={customerNumber as string}
      />
  ) : (
      <ContractForm
        customerNumber={customerNumber as string}
        contractType={contractType as string}
        isOffer={false}
      />
  )
};
