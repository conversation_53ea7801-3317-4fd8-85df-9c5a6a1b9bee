// pages/customer/[id]/createContract/[contractType].tsx
import { useRouter } from 'next/router';

import ContractForm from '@/components/ContractForm';
import AccidentInsuranceContactForm from '@/components/insuranceForms/AccidentInsuranceContactForm';
import Layout from '@/components/layout';

const CreateContractPage = () => {
  const router = useRouter();
  const { customerNumber, contractType } = router.query;

  if (!customerNumber || !contractType) {
    return <p>Lade...</p>;
  }

  const renderForm = () => {
    if (contractType === 'unfallversicherung') {
      return (
        <AccidentInsuranceContactForm
          customerNumber={customerNumber as string}
        />
      );
    }
    return (
      <ContractForm
        customerNumber={customerNumber as string}
        contractType={contractType as string}
        isOffer={true}
      />
    );
  };

  return (
    <div>
      <Layout
        title="Vertrag anlegen"
        description="Bereich zum erstellen des Vertrags"
      >
        {renderForm()}
      </Layout>
    </div>
  );
};

export default CreateContractPage;
