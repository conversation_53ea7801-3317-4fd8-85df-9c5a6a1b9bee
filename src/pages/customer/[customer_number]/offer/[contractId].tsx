// src/pages/customer/[id]/offer/[contractId].tsx
import { useRouter } from 'next/router';
import Layout from '../../../../components/layout';
import ContractDetails from '../../../../components/ContractDetails';

const ContractPage = () => {
    const router = useRouter();
    const { customer_number, contractId } = router.query;

    if (!customer_number || !contractId) {
        return <p>Lade...</p>;
    }

    return (
        <div>
            <Layout title="Angebotsdetails" description="Angebotsdetails ansehen">
                <ContractDetails customer_number={customer_number as string} contract_number={contractId as string} isOffer={true} />
            </Layout>

        </div>
    );
};

export default ContractPage;
