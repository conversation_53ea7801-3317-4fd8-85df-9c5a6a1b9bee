// src/pages/customer/[id]/createContract/[contractType].tsx
import { useRouter } from 'next/router';
import Layout from '../../../../components/layout';
import ContractForm from '../../../../components/ContractForm';
import AccidentInsuranceContactForm from '../../../../components/insuranceForms/AccidentInsuranceContactForm';

const CreateContractPage = () => {
    const router = useRouter();
    const { customer_number, contractType } = router.query;

    if (!customer_number || !contractType) {
        return <p>Lade...</p>;
    }

    const renderForm = () => {
        return <ContractForm customer_number={customer_number as string} contractType={contractType as string} isOffer={false} />;
    };

    return (
        <div>
            <Layout title="Vertrag anlegen" description="Bereich zum erstellen des Vertrags">
                {(contractType == 'unfallversicherung' && localStorage.getItem('is_admin') != 'true') ? <AccidentInsuranceContactForm customer_number={customer_number as string}/> : renderForm()}
            </Layout>
        </div>
    );
};

export default CreateContractPage;
