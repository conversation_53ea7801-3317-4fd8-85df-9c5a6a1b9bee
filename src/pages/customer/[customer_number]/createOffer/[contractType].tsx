// pages/customer/[id]/createContract/[contractType].tsx
import { useRouter } from 'next/router';
import Layout from '../../../../components/layout';
import ContractForm from '../../../../components/ContractForm';
import AccidentInsuranceContactForm from '@/components/insuranceForms/AccidentInsuranceContactForm';

const CreateContractPage = () => {
    const router = useRouter();
    const { customer_number, contractType } = router.query;

    if (!customer_number || !contractType) {
        return <p>Lade...</p>;
    }

    const renderForm = () => {
        if (contractType === 'unfallversicherung') {
            return <AccidentInsuranceContactForm customer_number={customer_number as string}/>;
        }
        return <ContractForm customer_number={customer_number as string} contractType={contractType as string} isOffer={true} />;
    };

    return (
        <div>
            <Layout title="Vertrag anlegen" description="Bereich zum erstellen des Vertrags">
                {renderForm()}
            </Layout>
        </div>
    );
};

export default CreateContractPage;
