// src/pages/customer/[customer_number]/contract/[contract_number].tsx
import { useRouter } from 'next/router';
import Layout from '../../../../components/layout';
import ContractDetails from '../../../../components/ContractDetails';

const ContractPage = () => {
    const router = useRouter();
    const { contract_number, customer_number } = router.query;

    return (
        <div>
            <Layout title="Vertragsdetails" description="Vertragsdetails ansehen">
                <ContractDetails contract_number={contract_number as string} customer_number={customer_number as string}/>
            </Layout>
        </div>
    );
};

export default ContractPage;