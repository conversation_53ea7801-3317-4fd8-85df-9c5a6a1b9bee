// pages/customer/[id]/editOffer/[contractId].tsx
import { useRouter } from 'next/router';
import Layout from '../../../../components/layout';
import ContractForm from '../../../../components/ContractForm';

const CreateContractPage = () => {
    const router = useRouter();
    const { customer_number, contract_number } = router.query;

    if (!customer_number || !contract_number) {
        return <p>Lade...</p>;
    }

    return (
        <div>
            <Layout title="Vertrag editieren" description="Bereich zum editieren des Vertrags">
                <ContractForm customer_number={customer_number as string} isOffer={false} edit_mode={true} contract_number={contract_number as string}/>
            </Layout>
        </div>
    );
};

export default CreateContractPage;
