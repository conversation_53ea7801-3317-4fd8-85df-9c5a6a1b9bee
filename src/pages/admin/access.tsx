import { GetServerSideProps } from "next";
import { prisma } from "@/server/prisma";
import { identityManager } from "@/server/IdentityManager";
import {paths} from "@/paths";

const unauthorized = { redirect: { destination: paths.referrer.unauthorized, permanent: false } };

export const getServerSideProps: GetServerSideProps = async (context) => {
  const token = context.query.token;
  if (!token || typeof token !== "string") {
    return unauthorized
  }

  const refToken = await prisma.referrerAccessToken.findUnique({
    where: { token },
    include: { referrer: true }
  });

  if (!refToken || refToken.expiresAt < new Date() || refToken.usedAt) {
    return unauthorized
  }
  if (!refToken.referrer.isAdmin) {
    return unauthorized
  }

  await prisma.referrerAccessToken.update({
    where: { id: refToken.id },
    data: { usedAt: new Date() },
  });

  const identity = await identityManager.createIdentity({
    referrerId: refToken.referrerId,
    isAdmin: true,
  })
  context.res.setHeader(
    "Set-Cookie",
    `referrerSession=${identity}; Path=/; HttpOnly; Max-Age=${60*60*24}; SameSite=Lax`
  );

  return {
    redirect: { destination: paths.referrer.referralsHistory, permanent: false },
  };
};

export default function ReferrerAccessPage() {
  return <p>Redirecting…</p>;
}
