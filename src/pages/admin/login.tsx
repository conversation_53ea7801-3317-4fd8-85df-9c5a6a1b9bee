import Layout from "@/components/layout/layout";
import httpClient from "@/utils/HttpClient";
import EmailInputCard from "@/components/card/EmailInputCard";

export default function AdminLoginPage() {
    async function handleSubmit(input: string) {
        await httpClient.request("/api/admin/login", {
            method: "POST",
            body: {
                email: input
            },
        });
    }

    return (
        <Layout
            htmlTitle="Administrator Zugang"
        >
            <EmailInputCard onSubmit={handleSubmit} translationsNamespace='adminLogin' />
        </Layout>
    );
}
