import { useRouter } from "next/router";
import ReferrerLoginEmail from "@/emails/ReferrerLoginEmail";
import { getEmailMessages } from "@/utils/getEmailMessages";

export default function ReferrerLoginEmailPage() {
  const router = useRouter();
  const { locale = "de", email = "<EMAIL>"} = router.query;

  const messages = getEmailMessages(locale as string, "referrerLogin");

  return (
    <ReferrerLoginEmail
      ctaUrl="https://example.com/test"
      messages={messages}
      referrerEmail={email as string}
    />
  );
}
