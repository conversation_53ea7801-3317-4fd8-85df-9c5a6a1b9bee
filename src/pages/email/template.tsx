import { useRouter } from "next/router";
import EmailTemplate from "@/emails/EmailTemplate";
import { getEmailMessages } from "@/utils/getEmailMessages";

export default function EmailTemplatePage() {
  const router = useRouter();
  const { locale = "de", message = "referrerLogin" } = router.query; // example: http://localhost:3000/email/template?locale=de&message=referrerLogin

  const messages = getEmailMessages(locale as string, message as string);

  return <EmailTemplate ctaUrl="https://example.com/test" messages={messages} />;
}
