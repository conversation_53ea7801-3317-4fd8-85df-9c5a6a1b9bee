import { useRouter } from "next/router";
import CustomerInvitationEmail from "@/emails/CustomerInvitationEmail";
import { getEmailMessages } from "@/utils/getEmailMessages";

export default function CustomerInvitationEmailPage() {
  const router = useRouter();
  const { locale = "de", recipient = "<PERSON>", product = "Wohngebäudeversicherung", referrer = "Frau Allianz TG", promo = "Sichern Sie Ihr Zuhause mit wenigen Klicks ab - individuell und flexibel" } = router.query;

  const messages = getEmailMessages(locale as string, "invitation");

  return (
    <CustomerInvitationEmail
      ctaUrl="https://example.com/test"
      messages={messages}
      recipientName={recipient as string}
      productName={product as string}
      referrerName={referrer as string}
      promoText={promo as string}
    />
  );
}
