import { Box, Button, Container, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";

export default function Custom404() {
  const t = useTranslations();
  return (
    <Container
      maxWidth="md"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        textAlign: "center",
        minHeight: "100vh",
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: "1rem", mb: 1 }}>
        <Image src="/logo.svg" alt="Logo" width={200} height={100} priority />
              <Typography variant="h2" component="h1" gutterBottom>
        {t("header.title")}
      </Typography>
      </Box>

      <Typography variant="h2" component="h1" gutterBottom>
        {t("404.title")}
      </Typography>

      <Typography variant="h5" color="text.secondary">
        {t("404.message")}
      </Typography>

      <Button
        component={Link}
        href="/"
        variant="contained"
        size="large"
        sx={{ mt: 3 }}
      >
        {t("404.button")}
      </Button>
    </Container>
  );
}
