// src/pages/api/contracts/index.ts
import { AttachmentService } from '@/server/attachment/AttachmentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        const { contractNumber } = req.query;
        const limit = -1;
        const offset = 0;
        const orderBy = { contractNumber: 'desc' } as const;
        const where = { contractNumber };

        const service = new AttachmentService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ attachments: items, meta });
    },
};

export default createEndpoint({ GET });
