// src/pages/api/contracts/index.ts
import formidable from 'formidable';
import fs from 'fs';

import { AttachmentService } from '@/server/attachment/AttachmentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { fileManager } from '@/utils/fileManager';

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        // Initialize formidable directly (no 'new')
        const form = formidable({ multiples: true });

        form.parse(req, async (err, fields, files) => {
            if (err) {
                console.error(err);
                return res.status(500).json({ error: 'File parsing error' });
            }

            // Check Authorization
            const contractNumber = fields.contractNumber
                ? fields.contractNumber[0]
                : undefined;
            const agentNumber = fields.agentNumber
                ? fields.agentNumber[0]
                : undefined;

            if (!contractNumber && !agentNumber) {
                return res
                    .status(400)
                    .json({ error: 'No contract number or agent number provided' });
            }

            if (!req.headers.authorization) {
                return res.status(401).end('Unauthorized');
            }

            const filename = fields.filename;
            const uploadedFile = Array.isArray(files.file)
                ? files.file[0]
                : files.file;

            if (!uploadedFile?.filepath) {
                return res.status(400).json({ error: 'No file uploaded' });
            }

            // Read the file into a Buffer and convert it to Uint8Array if needed
            const fileBuffer = await fs.promises.readFile(uploadedFile.filepath);
            const fileData = new Uint8Array(fileBuffer);
            const filePath = agentNumber
                ? `agent_files/${agentNumber}_${filename}`
                : `${contractNumber}_${filename}`;

            // Upload file to bucket
            await fileManager.uploadFile(fileData, filePath);

            // create entry in strapi
            const attachmentService = new AttachmentService(db);
            attachmentService.create({
                agentNumber,
                contractNumber,
                bucketPath: filePath,
                type: agentNumber ? 'agent' : 'contract_attachment',
                agent: { connect: { agentNumber } },
                contract: { connect: { contractNumber } }
            })

            return res.status(200).json({ message: 'File uploaded successfully' });
        });
    },
};

export default createEndpoint({ GET });
