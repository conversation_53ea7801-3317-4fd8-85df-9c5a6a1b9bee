// src/pages/api/agent/[agent_number]/revisions.ts

import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { getRevisions } from '@/utils/mongoDB/mongoDBFunctions';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { agentNumber } = req.query;
        if (!agentNumber || Array.isArray(agentNumber)) {
            return 'Invalid or missing agent number';
        }
    },

    execute: async ({ req, res }) => {
        const agentNumber = String(req.query.agentNumber);

        // Fetch revisions from Mongo
        const revisions = await getRevisions('agent', agentNumber);

        // Return 200 with array (empty array if none)
        return res.status(200).json(revisions);
    },
};

export default createEndpoint({ GET });
