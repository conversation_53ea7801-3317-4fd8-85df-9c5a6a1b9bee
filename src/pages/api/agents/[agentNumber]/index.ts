// src/pages/api/agent/[agentNumber].ts
import { AgentService } from '@/server/agent/AgentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { agentNumber } = req.query;
        if (!agentNumber) {
            return 'Invalid or missing agent number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const agentNumber = Array.isArray(req.query.agentNumber)
            ? req.query.agentNumber[0]
            : String(req.query.agentNumber);

        // service
        const service = new AgentService(db);

        const agent = await service.findOne({ agentNumber: agentNumber });

        if (!agent) {
            res.status(404).json({ message: 'Agent not found' });
            return;
        }

        res.status(200).json(agent);
    },
};

export default createEndpoint({ GET });
