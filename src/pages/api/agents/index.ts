// src/pages/api/agent/index.ts
import { agentFieldConfig } from '@/components/filters/agentFieldConfig';
import { type Agent, Prisma } from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {

        // pagination + sort
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'username');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;

        // filters
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Agent,
            fields: agentFieldConfig,
        });

        const service = new AgentService(db as any);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ agents: items, meta });
    },
};

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { agent } = (req.body ?? {}) as { agent?: Agent };
        if (!agent || typeof agent !== 'object')
            return "Invalid body: 'agent' is required";
        if (!agent.documentId)
            return "Invalid body: 'agent.documentId' is required";
    },

    execute: async ({ req, res, db }) => {
        const data: Agent = req.body;

        // service
        const service = new AgentService(db);

        const updated = await service.update({ documentId: data.documentId }, data);

        if (!updated) {
            res.status(404).json({ message: 'Agent not found' });
            return;
        }

        res.status(200).json(updated);
    },
};

export default createEndpoint({ GET, PUT });
