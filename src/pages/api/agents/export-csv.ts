// src/pages/api/agent/export-csv.ts
import { Parser } from '@json2csv/plainjs';

import { agentFieldConfig } from '@/components/filters/agentFieldConfig';
import { type Agent, Prisma } from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization header';
        // Only allow GET
        if (req.method !== 'GET') return `Method ${req.method} Not Allowed`;
    },

    execute: async ({ req, res, db }) => {
        try {
            // ---- Sort --------------------------------------------------------------
            const sortField = String(req.query.sortField ?? 'username');
            const sortDirection =
                String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                    ? 'desc'
                    : 'asc';
            const orderBy = { [sortField]: sortDirection } as const;

            // ---- Filters (new logic) ----------------------------------------------
            // Accepts the same query format that your new list endpoint uses.
            // Example: ?username[$contains]=mike&city[$eq]=Berlin
            const where = parseQuery(req.query, {
                rootModel: Prisma.ModelName.Agent,
                fields: agentFieldConfig,
            });

            // ---- Fetch all rows in batches ----------------------------------------
            // Defaults mirror your old limit of 1000 but can be overridden.
            const batchSize =
                Math.max(1, Number((req.query.batchSize as string) ?? 1000)) || 1000;

            const service = new AgentService(db);

            // First batch to learn total
            const first = await service.list({
                where,
                orderBy,
                skip: 0,
                take: batchSize,
            });

            const allItems = [...first.items];
            let fetched = first.items.length;
            const total = first.total;

            while (fetched < total) {
                const next = await service.list({
                    where,
                    orderBy,
                    skip: fetched,
                    take: batchSize,
                });
                allItems.push(...next.items);
                fetched += next.items.length;
            }

            // ---- CSV mapping (same column names as your old exporter) -------------
            const csvData = allItems.map((item: Agent) => ({
                Benutzername: item.username ?? '',
                Maklernummer: item.agentNumber ?? '',
                Agenturname: item.companyName ?? '',
                Agenturnummer: item.agencyNumber ?? '',
                Straße: item.street ?? '',
                Hausnummer: item.houseNumber ?? '',
                Postleitzahl: item.postalCode ?? '',
                Ort: item.city ?? '',
                Emailadresse: item.email ?? '',
                Website: item.url ?? '',
                Telefonnummer: item.telephoneNumber ?? '',
                Courtage: item.commission ?? '',
            }));

            const parser = new Parser({});
            const csv = parser.parse(csvData);

            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=agents.csv'
            );
            res.status(200).send(csv);
        } catch (error) {
            console.error('CSV Export Error:', error);
            // Normalize 405 from validate()
            if (req.method !== 'GET') {
                res.setHeader('Allow', ['GET']);
                res.status(405).end(`Method ${req.method} Not Allowed`);
                return;
            }
            res.status(500).json({ error: 'Failed to export agents to CSV' });
        }
    },
};

export default createEndpoint({ GET });
