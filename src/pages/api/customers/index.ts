// src/pages/api/customers/index.ts
import { customerFieldConfig } from '@/components/filters/customerFieldConfig';
import { type Customer, Prisma } from '@/generated/prisma-postgres';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { generateCustomerNumber } from '@/utils/numberGenerator';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        // pagination + sort
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'customerNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;

        // filters
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Customer,
            fields: customerFieldConfig,
        });

        const service = new CustomerService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ items, meta });
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const customer = req.body as Customer;
        if (!customer) return 'Invalid body';
    },

    execute: async ({ req, res, db }) => {
        const customer = req.body as Customer;

        const input =
            customer.salutation +
            (customer.namePrefix || '') +
            customer.lastName +
            customer.firstName +
            customer.careOf +
            customer.street +
            customer.houseNumber +
            customer.postalCode +
            customer.city +
            customer.email;
        customer.customerNumber = generateCustomerNumber(input);

        // service
        const service = new CustomerService(db);

        const updated = await service.create({
            ...customer,
            agency: { connect: { agencyNumber: customer.agencyNumber! } },
        });

        if (!updated) {
            res.status(404).json({ message: 'Customer creation failed' });
            return;
        }

        res.status(200).json(updated);
    },
};

export default createEndpoint({ GET, POST });
