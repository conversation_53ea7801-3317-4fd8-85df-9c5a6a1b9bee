// src/pages/api/customers/[customerNumber]/contracs.ts
import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { customerNumber } = req.query;
        if (
            !customerNumber ||
            (Array.isArray(customerNumber) && customerNumber.length !== 1)
        ) {
            return 'Invalid or missing customer number';
        }
    },

    execute: async ({ req, res, db }) => {

        // param
        const customerNumber = Array.isArray(req.query.customerNumber)
            ? req.query.customerNumber[0]
            : String(req.query.customerNumber);

        // service
        const service = new ContractService(db);

        const contracts = await service.list({
            where: { customerNumber: customerNumber },
            orderBy: { customerNumber: 'desc' },
            skip: 0,
            take: -1,
        });

        if (!contracts) {
            res.status(404).json({ message: 'Contracts not found' });
            return;
        }

        res.status(200).json(contracts);
    },
};

export default createEndpoint({ GET });
