// src/pages/api/customers/[customerNumber]/revisions.ts

import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { getRevisions } from '@/utils/mongoDB/mongoDBFunctions';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { customerNumber } = req.query;
        if (!customerNumber || Array.isArray(customerNumber)) {
            return 'Invalid or missing customer number';
        }
    },

    execute: async ({ req, res }) => {
        const customerNumber = String(req.query.customerNumber);

        // Fetch revisions from Mongo
        const revisions = await getRevisions('customer', customerNumber);

        // Return 200 with array (empty array if none)
        return res.status(200).json(revisions);
    },
};

export default createEndpoint({ GET });
