// src/pages/api/customer/export-csv.ts
import { Parser } from '@json2csv/plainjs';

import { customerFieldConfig } from '@/components/filters/customerFieldConfig';
import { type Customer, Prisma } from '@/generated/prisma-postgres';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
        if (req.method !== 'GET') return `Method ${req.method} Not Allowed`;
    },

    execute: async ({ req, res, db, auth }) => {
        try {
            const isAdmin = auth.roles.includes('asevo-admin');

            // --- Sorting -----------------------------------------------------------
            const sortField = String(req.query.sortField ?? 'customer_number');
            const sortDirection =
                String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                    ? 'desc'
                    : 'asc';
            const orderBy = { [sortField]: sortDirection } as const;

            // --- Filters (new logic) ----------------------------------------------
            const baseWhere = parseQuery(req.query, {
                rootModel: Prisma.ModelName.Customer,
                fields: customerFieldConfig,
            });

            // Non-admins: restrict to their agency
            const where =
                !isAdmin && auth.agencyNumber
                    ? {
                        AND: [
                            baseWhere,
                            { agency_number: String(auth.agencyNumber) },
                        ],
                    }
                    : baseWhere;

            // --- Fetch all rows in batches ----------------------------------------
            const batchSize =
                Math.max(1, Number((req.query.batchSize as string) ?? 1000)) || 1000;

            const service = new CustomerService(db as any);

            const first = await service.list({
                where,
                orderBy,
                skip: 0,
                take: batchSize,
            });

            const allItems = [...first.items];
            let fetched = first.items.length;
            const total = first.total;

            while (fetched < total) {
                const next = await service.list({
                    where,
                    orderBy,
                    skip: fetched,
                    take: batchSize,
                });
                allItems.push(...next.items);
                fetched += next.items.length;
            }

            // --- CSV mapping (same columns as before) ------------------------------
            const csvData = allItems.map((item: Customer) => ({
                Anrede: item.salutation ?? '',
                Titel: item.namePrefix ?? '',
                Vorname: item.firstName ?? '',
                Nachname: item.lastName ?? '',
                CO: item.careOf ?? '',
                Straße: item.street ?? '',
                Hausnummer: item.houseNumber ?? '',
                Postleitzahl: item.postalCode ?? '',
                Ort: item.city ?? '',
                Email: item.email ?? '',
                Makler: item.agentNumber ?? '',
                Agentur: item.agencyNumber ?? '',
            }));

            const parser = new Parser({});
            const csv = parser.parse(csvData);

            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=customers.csv'
            );
            res.status(200).send(csv);
        } catch (error) {
            console.error('CSV Export Error:', error);
            if (req.method !== 'GET') {
                res.setHeader('Allow', ['GET']);
                res.status(405).end(`Method ${req.method} Not Allowed`);
                return;
            }
            res.status(500).json({ error: 'Failed to export customers to CSV' });
        }
    },
};

export default createEndpoint({ GET });
