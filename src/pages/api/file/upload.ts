// src/pages/api/contracts/[contract_number]/pdf.ts
import { fileManager } from '@/utils/fileManager';
import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import { permissionManager } from '@/utils/permissionManager';

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    try {
        // Initialize formidable directly (no 'new')
        const form = formidable({ multiples: true });

        form.parse(req, async (err, fields, files) => {
            if (err) {
                console.error(err);
                return res.status(500).json({ error: 'File parsing error' });
            }

            // Check Authorization
            const contractNumber = fields.contract_number ? fields.contract_number[0] : undefined;
            const agent_number = fields.agent_number ? fields.agent_number[0] : undefined;

            if (!contractNumber && !agent_number) {
                return res.status(400).json({ error: 'No contract number or agent number provided' });
            }

            if (!req.headers.authorization) {
                return res.status(401).end('Unauthorized');
            }

            const token = req.headers.authorization!.split(" ")[1];

            const authUser = await permissionManager.getAuthUser(token)
            const isAdmin = permissionManager.isUserAdmin(authUser)

            let strapiResponse: any | undefined

            if (!agent_number) {
                if (authUser && !isAdmin)
                    strapiResponse = await fetchDataFromAPI(
                        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contractNumber}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
                        'contract',
                        token
                    );
                else if (authUser && isAdmin)
                    strapiResponse = await fetchDataFromAPI(
                        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contractNumber}`,
                        'contract',
                        token
                    );

                if (!strapiResponse) {
                    return res.status(404).json({ error: 'Contract not found' });
                }
            }

            const filename = fields.filename;
            const uploadedFile = Array.isArray(files.file) ? files.file[0] : files.file;

            if (!uploadedFile?.filepath) {
                return res.status(400).json({ error: 'No file uploaded' });
            }

            // Read the file into a Buffer and convert it to Uint8Array if needed
            const fileBuffer = await fs.promises.readFile(uploadedFile.filepath);
            const fileData = new Uint8Array(fileBuffer);
            const filePath = agent_number ? `agent_files/${agent_number}_${filename}` : `${contractNumber}_${filename}`;

            // Upload file to bucket
            await fileManager.uploadFile(fileData, filePath);

            // create entry in strapi
            fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/attachments`,
                'attachment',
                token,
                'POST',
                JSON.stringify({
                    data: {
                        'agent_number': agent_number,
                        'contract_number': contractNumber,
                        'bucket_path': filePath,
                        'type': agent_number ? "agent" : "contract_attachment"
                    }
                })
            )

            return res.status(200).json({ message: 'File uploaded successfully' });
        });
    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}
