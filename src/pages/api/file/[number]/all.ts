// src/pages/api/contracts/[contract_number]/pdf.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { number } = req.query;

    if (!number || typeof number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    const is_contract = number.split("-").length == 3

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const authUser = await permissionManager.getAuthUser(token)
        const isAdmin = permissionManager.isUserAdmin(authUser)
        let strapiUrl = ""

        if (number && is_contract) {
            if (authUser && !isAdmin)
                strapiUrl = `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${number}&filters[agency_number][$eq][1]=${authUser['agency_number']}&pagination[limit]=100`
            else if (authUser && isAdmin)
                strapiUrl = `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${number}&pagination[limit]=100`

            const strapiResponse = (await fetchDataFromAPI(strapiUrl,
                'contract',
                token
            ))?.[0];

            if (!strapiResponse) {
                return res.status(404).json({ error: 'Contract not found' });
            }
        }

        const files = await fetchDataFromAPI(
            `${process.env.STRAPI_BASE_URL}/attachments?filters[${is_contract ? "contract_number": "agent_number"}][$eq]=${number}`,
            'attachment',
            token
        );

        res.status(201).json(files)

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

