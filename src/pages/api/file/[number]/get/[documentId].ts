// src/pages/api/file/[contract_number]/get/[bucket_path].ts
import { fileManager } from '@/utils/fileManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { number, documentId, type } = req.query;

    if (!number || typeof number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    const is_contract = number.split("-").length == 3

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {
        // Fetch contract data

        const authUser = await permissionManager.getAuthUser(token)
        const isAdmin = permissionManager.isUserAdmin(authUser)

        let strapiResponse: any | undefined

        if (is_contract) {
            if (authUser && !isAdmin)
                strapiResponse = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
                    'contract',
                    token
                );
            else if (authUser && isAdmin)
                strapiResponse = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${number}`,
                    'contract',
                    token
                );

            if (!strapiResponse) {
                return res.status(404).json({ error: 'Contract not found' });
            }
        }

        //fetch attachment data from strapi
        const url = isAdmin ? 
        `${process.env.STRAPI_BASE_URL}/attachments?&filters[documentId][$eq][1]=${documentId}&pagination[limit]=100`
        : `${process.env.STRAPI_BASE_URL}/attachments?filters[${is_contract ? "contract_number" : "agent_number"}][$eq][0]=${is_contract ? number : authUser.agent_number}&filters[documentId][$eq][1]=${documentId}&pagination[limit]=100` //TODO made a quick fix, check filter 
        
        const attachment = await fetchDataFromAPI(
            url, 
            'contract',
            token
        );

        const pdf_stream = await fileManager.downloadFile(attachment['bucket_path'])

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="${attachment['bucket_path'].replace(`${number}_`, "")}"`
        );

        // Pipe the S3 stream to the response
        pdf_stream.on('error', (err: any) => {
            console.error('Error fetching File from S3:', err);
            res.status(500).json({ error: 'Failed to fetch File' });
        });

        pdf_stream.pipe(res);
    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

