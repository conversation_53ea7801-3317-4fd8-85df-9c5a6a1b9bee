// src/pages/api/file/info/[file_name].ts
import { type NextApiRequest, type NextApiResponse } from 'next';

import { fileManager } from '@/utils/fileManager';
import { permissionManager } from '@/utils/permissionManager';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const { file_name } = req.query;

  if (!req.headers.authorization) {
    res.status(401);
    return;
  }
  const token = req.headers.authorization!.split(' ')[1];
  const authUser = await permissionManager.getAuthUser(token);
  if (authUser == undefined) {
    res.status(401).end('Unaurtharized');
    return;
  }

  try {
    //fetch data from ionos
    const file_stream = await fileManager.downloadFile(
      'assets/' + file_name + '.pdf'
    );

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${file_name}"`);

    // Pipe the S3 stream to the response
    file_stream.on('error', (err: any) => {
      console.error('Error fetching File from S3:', err);
      res.status(500).json({ error: 'Failed to fetch File' });
    });

    file_stream.pipe(res);
  } catch (error) {
    console.error('Error in API handler:', error);
    res
      .status(500)
      .json({ error: 'An error occurred while processing the request.' });
  }
}
