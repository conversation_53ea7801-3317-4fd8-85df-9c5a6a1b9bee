import { promises as fs } from 'fs';

import { type Report } from '@/generated/prisma-postgres';
import { AttachmentService } from '@/server/attachment/AttachmentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportService } from '@/server/report/ReportService';
import { fileManager } from '@/utils/fileManager';
import { generateReportNumber } from '@/utils/numberGenerator';
import { parseMultipart } from '@/utils/parseMultipart';

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        // Only check auth here. Body cannot be read twice.
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db, auth }) => {
        // ⬇️ Parse multipart form (fields + files)
        const { fields, files } = await parseMultipart(req);

        const base: Partial<Report> =
            typeof fields.report === 'object' && fields.report !== null
                ? fields.report
                : (fields as any);

        // Ensure required fields exist:
        if (!base.customerNumber || !base.agencyNumber || !base.contractNumber) {
            res.status(400).json({
                error:
                    'Missing required fields (customerNumber, agencyNumber, contractNumber)',
            });
            return;
        }

        const reportNumber = generateReportNumber(String(base.customerNumber));

        // Generate number and build data
        const data: Report = {
            ...(base as Report),
            reportNumber: reportNumber,
        };

        const reportService = new ReportService(db);
        const attachmentService = new AttachmentService(db);

        // 🔧 Persist the Report
        const created = await reportService.create({
            ...(data as any),
            customer: { connect: { customerNumber: data.customerNumber } },
            agency: { connect: { agencyNumber: data.agencyNumber } },
            contract: { connect: { contractNumber: data.contractNumber } },
        });

        // 🔧 Persist files
        const keyPrefix = `report_files/${created.reportNumber}`;

        await Promise.all(
            files.map(async (f) => {
                const bytes = await fs.readFile(f.filepath); // Buffer (Uint8Array)
                const filename =
                    f.originalFilename ??
                    (f as any).newFilename ??
                    `upload-${Date.now()}`;
                const key = `${keyPrefix}_${filename}`;

                await fileManager.uploadFile(bytes, key);

                // optional: remove temp file after successful upload
                try {
                    await fs.unlink(f.filepath);
                } catch { /* empty */ }

                // optional: persist file metadata in DB (pseudo)
                await attachmentService.create({
                    reportNumber: reportNumber,
                    bucketPath: key,
                    contractNumber: data.contractNumber,
                    type: 'report',
                    agentNumber: auth.agentNumber,
                });
            })
        );

        res.status(201).json(created);
    },
};

export default createEndpoint({ POST })
