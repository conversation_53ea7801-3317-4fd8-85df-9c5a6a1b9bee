import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportService } from '@/server/report/ReportService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { reportNumber } = req.query;
        if (
            !reportNumber ||
            (Array.isArray(reportNumber) && reportNumber.length !== 1)
        ) {
            return 'Invalid or missing report number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const reportNumber = Array.isArray(req.query.reportNumber)
            ? req.query.reportNumber[0]
            : String(req.query.reportNumber);

        // service
        const service = new ReportService(db);

        const report = await service.getReport({ reportNumber: reportNumber });

        if (!report) {
            res.status(404).json({ message: 'Report not found' });
            return;
        }

        res.status(200).json(report);
    },
};

export default createEndpoint({ GET });
