import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportPrecheckService } from '@/server/report/ReportPrecheckService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },
    execute: async ({ req, res, db }) => {

        const { reportNumber } = req.query;

        const service = new ReportPrecheckService(db);
        const { items, total } = await service.list({
            where: { reportNumber: reportNumber as string },
            orderBy: { timestamp: 'desc' },
            skip: 0,
            take: -1,
        });

        res.status(200).json({ entries: items, total });
    },
};

export default createEndpoint({ GET })
