import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportPrecheckService } from '@/server/report/ReportPrecheckService';

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },
    execute: async ({ req, res, db }) => {

        const { precheckId } = req.query;

        const service = new ReportPrecheckService(db);
        const updated = await service.update(
            { id: Number(precheckId as string) },
            req.body
        );

        res.status(200).json(updated);
    },
};

export default createEndpoint({ PUT })
