import { getExpectedDocumentTypes } from '@/common';
import {
    type PrismaClient,
    ReportAttachmentRelatedTo,
} from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportAttachmentService } from '@/server/report/ReportAttachmentService';
import { ReportTimelineEntryService } from '@/server/report/ReportTimeLineService';
import { internalServerError } from '@/server/responses';
import {
    type AnalyzeTimelineEntryRequestFile,
    type JobInitResponse,
    type TimelineEntryAnalysisJobResponse,
} from '@/types';
import { fileManager } from '@/utils/fileManager';
import { HttpClient } from '@/utils/HttpClient';
import { streamToBase64 } from '@/utils/streamToBase64';

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        // Only check auth here. Body cannot be read twice.
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db }) => {
        const { entryId } = req.body;

        const jobInitResponse = await startEntryAnalysis(db, entryId as number);

        if (jobInitResponse.status !== 'STARTED') {
            return internalServerError(res, {
                message: `The job has not been started`,
                status: jobInitResponse.status,
            });
        }

        const jobResult = await pollForJobResult(
            new HttpClient(''),
            jobInitResponse.job_id
        );
        if (!jobResult) {
            throw new Error('Timeout: Job did not finish in time.');
        }

        return res.status(200).json(jobResult);
    },
};

const startEntryAnalysis = async (
    db: PrismaClient,
    entryId: number
): Promise<JobInitResponse> => {
    const timelineEntriesService = new ReportTimelineEntryService(db);
    const reportAttachmentsService = new ReportAttachmentService(db);

    const getFiles = async () => {
        const attachments = await reportAttachmentsService.list({
            where: {
                relatedTo: ReportAttachmentRelatedTo.TIMELINE_ENTRY,
                relatedEntityId: entryId + '',
            },
            orderBy: { id: 'desc' },
            skip: 0,
            take: -1,
        });
        const promises = attachments.items.map<
            Promise<AnalyzeTimelineEntryRequestFile>
        >(async (attachemnt) => {
            const fileStream = await fileManager.downloadFile(attachemnt.bucketPath!);
            const base64 = await streamToBase64(fileStream);
            const name = attachemnt.name!;
            return {
                name,
                base64,
                url: 'https://pdfobject.com/pdf/sample.pdf', // TODO: ask Ole to remove validation and remove this line
            };
        });

        return Promise.all(promises);
    };
    const [entry, files] = await Promise.all([
        timelineEntriesService.getReportTimelineEntry({ id: entryId }),
        getFiles(),
    ]);

    const body = {
        entry: {
            ...entry,
            files,
        },
        expectedDocumentTypes: getExpectedDocumentTypes(),
    };
    const httpClient = new HttpClient('');
    return httpClient.request(`${process.env.LOCAL_API_BASE}/doc-analyze`, {
        method: 'POST',
        body,
    });
};

const pollForJobResult = async (httpClient: HttpClient, jobId: string) => {
    const maxAttempts = 30;
    let attempt = 0;

    while (attempt < maxAttempts) {
        await new Promise((res) => setTimeout(res, 5000));
        attempt++;

        const jobResponse: TimelineEntryAnalysisJobResponse =
            await httpClient.request(`${process.env.LOCAL_API_BASE}/jobs/${jobId}`);

        if (jobResponse.status === 'FAILED') {
            throw new Error('Job failed');
        }
        if (jobResponse.status === 'FINISHED') {
            return jobResponse;
        }
    }
};

export default createEndpoint({ POST });
