// src/pages/api/reports/[report/number]/coverage-assessment.ts

import { type PrismaClient } from '@/generated/prisma-postgres';
import { ContractService } from '@/server/contract/ContractService';
import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InsuranceConditionService } from '@/server/insurance-condition/InsuranceConditionService';
import { ReportCoverageAssessmentService } from '@/server/report/ReportCoverageAssessmentService';
import { ReportService } from '@/server/report/ReportService';
import { ReportSummaryService } from '@/server/report/ReportSummaryService';
import { fileManager } from '@/utils/fileManager';
import { streamToBase64 } from '@/utils/streamToBase64';

const fieldFallback = 'unbekannt';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { reportNumber } = req.query;
        if (!reportNumber || Array.isArray(reportNumber)) {
            return 'Invalid or missing report number';
        }
    },

    execute: async ({ req, res, db }) => {
        const reportNumber = String(req.query.reportNumber);

        // Fetch revisions from Mongo
        const service = new ReportCoverageAssessmentService(db);

        const rca = await service.list({
            where: { reportNumber },
            orderBy: { reportNumber: 'desc' },
            skip: 0,
            take: -1,
        });

        if (rca == null) {
            return res.status(404).json({ error: 'Coverage Assessment not found' });
        }

        // Return 200 with array (empty array if none)
        return res.status(200).json(rca.items[0]);
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { reportNumber } = req.query;
        if (!reportNumber || Array.isArray(reportNumber)) {
            return 'Invalid or missing report number';
        }
    },

    execute: async ({ req, res, db }) => {
        const reportNumber = String(req.query.reportNumber);
        getCoverageAssessmentInput(db, reportNumber)

        // Fetch revisions from Mongo
        const service = new ReportCoverageAssessmentService(db);

        const rca = await service.list({
            where: { reportNumber },
            orderBy: { reportNumber: 'desc' },
            skip: 0,
            take: -1,
        });

        if (rca == null) {
            return res.status(404).json({ error: 'Coverage Assessment not found' });
        }

        // Return 200 with array (empty array if none)
        return res.status(200).json(rca.items[0]);
    },
};

async function getCoverageAssessmentInput(
    db: PrismaClient,
    reportNumber: string
) {
    const reportService = new ReportService(db);
    const contractService = new ContractService(db);
    const conditionService = new InsuranceConditionService(db);
    const summaryService = new ReportSummaryService(db);

    const report = (await reportService.getReport({ reportNumber }))!;
    const contract = (await contractService.getContract({
        contractNumber: report.contractNumber!,
    }))!;

    const getConditionAttachments = async () => {
        try {
            const insuranceConditions = await conditionService.list({
                where: {
                    relevantContractTypes: {
                        contains: contract.contractType!,
                    },
                },
                orderBy: { createdAt: 'desc' },
                skip: 0,
                take: -1,
            });

            return Promise.all(
                insuranceConditions.items.map(async ({ id, bucketPath, fileName }) => {
                    const base64 = await fileManager
                        .downloadFile(bucketPath!)
                        .then(streamToBase64);
                    return {
                        id,
                        fileName,
                        base64,
                    };
                })
            );
        } catch (error) {
            console.error(error);
            return [];
        }
    };
    const getLatestSummary = async (): Promise<string> => {
        try {
            const summary = await summaryService.getReportSummary(
                { reportNumber }, // where
                { orderBy: { timestamp: 'desc' } }
            );

            return summary?.text ?? '';
        } catch (error) {
            console.error(error);
            return '';
        }
    };

    const [attachments, last_timelineSummary] = await Promise.all([
        getConditionAttachments(),
        getLatestSummary(),
    ]);

    return {
        claim_id: `${reportNumber} Alpha`, // TODO: unhardcode company name
        contract_details: buildContractDetails(contract),
        claim_details: {
            damage_amount:
                (report.dataRaw as any)!.damage_details?.damage_amount ?? 0,
            damage_description: report.text,
            damage_city: report.damageLocation,
            damage_date: report.damageDate || fieldFallback,
        },
        last_timelineSummary,
        attachments,
    };
}

function buildContractDetails(contract: any) {
    const value = (fieldName: string, fallback: any = fieldFallback) => {
        return contract[fieldName] ?? fallback;
    };
    const detailsFallback = {
        coverage: value('coverage_amount', 0),
        deductible: value('deductible'),
        risk: 'Keine Risikoinfos verfügbar',
        additional_agreements: 'Keine Zusatzvereinbarungen verfügbar',
        product_id: 'Keine Product ID verfügbar',
        category: 'Keine Kategorie verfügbar',
        product_name: 'Keine Produktname verfügbar',
        inclusive: 'Keine weiterer Service verfügbar',
    };

    const riskAddresses = value('risk_addresses', []).map(
        ({ street, house_number, postal_code, city, unit }: any) =>
            `\n${street} ${house_number}, ${postal_code} ${city}${unit?.trim() ? ', ' + unit.trim() : ''};`
    );
    const map: Record<string, any> = {
        hausrat: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `\nRisikobezeichnung: ${value('object_type')};` +
                    `\nBauartklasse: 1;` +
                    `\nWohnfläche: ${value('living_area')} m²;`,
                additional_agreements: `${value('additional_agreements')}`,
                product_id: ``,
                category: `Hausratsversicherung`,
                product_name: `ALPHA HAUSRATVERSICHERUNG EXPERT`,
                inclusive: ``,
            };
        },
        wohngebaeude: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}; ` +
                    `\nRisikobezeichnung: ${value('object_type')}; ` +
                    `\nBauartklasse: 1` +
                    `\nBaujahr: ${value('is_construction_year_unknown', true) ? fieldFallback : value('construction_year')}`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: 'Wohngebäude-Versicherung',
                product_name: 'ALPHA WOHNGEBÄUDE EXPERT',
                inclusive: ``,
            };
        },
        tierhalterhaftpflicht: () => {
            const risk =
                value('animal_data', [])
                    .map((animal: any) => {
                        let animalInfo = `Tierart: ${animal.animal_type}; Name: ${animal.animal_name}; `;
                        if (animal.animal_type === 'Hund' && animal.race) {
                            animalInfo += `Rasse: ${animal.race};`;
                        }
                        return animalInfo;
                    })
                    .join('\n') || fieldFallback;

            return {
                coverage: 0,
                deductible: 500,
                risk,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: 'Tierhalterhaftpflichtversicherung',
                product_name: `ALPHA TIERHALTERHAFTPFLICHT EXPERT`,
                inclusive: ``,
            };
        },
        privathaftpflicht: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk: `Risikobezeichnung: ${value('family_coverage', false) ? 'Familiendeckung' : 'Single/Alleinerziehend'};`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Privathaftpflichtversicherung`,
                product_name: `ALPHA PRIVATHAFTPFLICHT EXPERT`,
                inclusive: ``,
            };
        },
        haus_und_grundbesitzerhaftpflicht: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `\nRisikobezeichnung: ${value('object_type')};` +
                    `\nBauartklasse: 1;`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Haus- und Grundbesitzerhaftpflichtversicherung`,
                product_name: `ALPHA HAUS- UND GRUNDBESITZERHAFTPFLICHT EXPERT`,
                inclusive: ``,
            };
        },
        bauleistung: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `\nRisikobezeichnung: ${value('object_type')}; ` +
                    `\nBauartklasse: 1;`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Bauleistung`,
                product_name: `ALPHA BAULEISTUNG EXPERT`,
                inclusive: ``,
            };
        },
        bauherrenhaftpflicht: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `\nRisikobezeichnung: ${value('object_type')};` +
                    `\nBauartklasse: 1;`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Bauherrenhaftpflichtversicherung`,
                product_name: `ALPHA BAUHERRENHAFTPFLICHT EXPERT`,
                inclusive: ``,
            };
        },
        geschaeftsversicherung: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `Betriebsart: ${value('business_type')};`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Geschäftsversicherung`,
                product_name: `ALPHA GESCHÄFTSVERSICHERUNG EXPERT`,
                inclusive: ``,
            };
        },
        gebaeudeversicherung: () => {
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `Betriebsart: ${value('business_type')};` +
                    `Bauartklasse: 1;` +
                    `Baujahr: ${value('is_construction_year_unknown', true) ? fieldFallback : value('construction_year')};`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Gewerbliche Gebäudeversicherung`,
                product_name: `ALPHA GEWERBLICHE GEBÄUDE EXPERT`,
                inclusive: ``,
            };
        },
        betriebshaftpflicht: () => {
            const employeeCount = value('employee_count', 0);
            const employeeCountString = employeeCount
                ? `\nAnzahl der Mitarbeiter: max. ${employeeCount} ${employeeCount === 1 ? 'Vollzeitkraft' : 'Vollzeitkräfte'};`
                : '';
            return {
                coverage: 0,
                deductible: 500,
                risk:
                    `Risikoorte:${riskAddresses}` +
                    `Betriebsart: ${value('business_type')};` +
                    `${employeeCountString}`,
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Betriebshaftpflichtversicherung`,
                product_name: `ALPHA BETRIEBSHAFTPFLICHT EXPERT`,
                inclusive: ``,
            };
        },
        unfallversicherung: () => {
            const formatNumber = (value: any): string => {
                const number = Number(value);

                if (
                    isNaN(number) ||
                    value === '' ||
                    value === null ||
                    value === undefined ||
                    value === false
                ) {
                    return '0 EUR';
                }

                return `${number} EUR`;
            };

            return {
                coverage: 0,
                deductible: 500,
                risk: value('insured_persons', [])
                    .map((person: any, index: number) => {
                        let personInfo = `Person ${index + 1}:\n`;
                        personInfo += `Vorname: ${person.first_name}; `;
                        personInfo += `Nachname: ${person.last_name}; `;
                        personInfo += `Geburtstag: ${person.birth_date}; `;
                        if (person.type) personInfo += `Typ: ${person.type}; `;
                        if (person.occupation_group)
                            personInfo += `Berufsgruppe: ${person.occupation_group}; `;
                        if (person.occupation)
                            personInfo += `Beruf: ${person.occupation}; `;
                        personInfo += `Mehrleistungsklausel: ${formatNumber(person.increased_benefit_clause)}; `;
                        personInfo += `Invalidität: ${formatNumber(person.disability_coverage)} %; `;
                        personInfo += `Grundsumme Invalidität: ${formatNumber(person.basic_sum)}; `;
                        personInfo += `Unfallrente: ${formatNumber(person.accident_pension)}; `;
                        personInfo += `Unfalltod: ${formatNumber(person.accidental_death)}; `;
                        personInfo += `Krankentagegeld: ${formatNumber(person.daily_sickness_allowance)}; `;
                        personInfo += `Krankenhaustagegeld mit Genesungsgeld: ${formatNumber(person.hospital_daily_allowance)}; `;
                        personInfo += `Übergangsleistung: ${formatNumber(person.transitional_benefit)}; `;
                        if (typeof person.first_aid_module === 'boolean')
                            personInfo += `Erste-Hilfe-Baustein: ${formatNumber(person.first_aid_module) ? 'Ja' : 'Nein'};`;
                        return personInfo;
                    })
                    .join('\n'),
                additional_agreements: value('additional_agreements'),
                product_id: ``,
                category: `Unfallversicherung`,
                product_name: `ALPHA UNFALLVERSICHERUNG EXPERT`,
                inclusive: ``,
            };
        },
    };

    return map[contract.contract_type]?.() ?? detailsFallback;
}

export default createEndpoint({ GET, POST });
