import {
    type PrismaClient,
    ReportSummaryAuthorType,
} from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportPrecheckService } from '@/server/report/ReportPrecheckService';
import { ReportSummaryService } from '@/server/report/ReportSummaryService';
import { ReportTimelineEntryService } from '@/server/report/ReportTimeLineService';
import { internalServerError } from '@/server/responses';
import { type ReportSummaryJobResponse } from '@/types';
import { HttpClient } from '@/utils/HttpClient';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Only check auth here. Body cannot be read twice.
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db }) => {
        const { reportNumber } = req.body;

        const reportSummaryService = new ReportSummaryService(db);

        const summaries = await reportSummaryService.getReportSummary({
            reportNumber,
        });

        return res.status(200).json(summaries);
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        // Only check auth here. Body cannot be read twice.
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db, auth }) => {
        const { reportNumber } = req.body;

        const reportSummaryService = new ReportSummaryService(db);
        const httpClient = new HttpClient(auth.accessToken || '');

        const jobInitResponse = await startJob(httpClient, db, reportNumber);
        if (jobInitResponse.status !== 'STARTED') {
            return internalServerError(res, {
                message: `The job has not been started`,
                status: jobInitResponse.status,
            });
        }

        const jobResult = await pollForJobResult(
            httpClient,
            jobInitResponse.job_id
        );
        if (!jobResult) {
            throw new Error('Timeout: Job did not finish in time.');
        }

        const summary = await reportSummaryService.create({
            reportNumber: reportNumber as string,
            authorType: ReportSummaryAuthorType.AI,
            text: jobResult.timelineSummary || '',
            timestamp: new Date().toISOString(),
        });

        return res.status(200).json(summary);
    },
};

const startJob = async (
    httpClient: HttpClient,
    db: PrismaClient,
    reportNumber: string
) => {
    const timelineEntriesService = new ReportTimelineEntryService(db);
    const prechecksService = new ReportPrecheckService(db);

    const [timeline, prechecks] = await Promise.all([
        timelineEntriesService.list({
            where: { reportNumber: reportNumber },
            orderBy: { id: 'desc' },
            skip: 0,
            take: -1,
        }),
        prechecksService.list({
            where: { reportNumber: reportNumber },
            orderBy: { id: 'desc' },
            skip: 0,
            take: -1,
        }),
    ]);
    const body = {
        prechecks,
        timeline,
        contractSummary: {
            id: '',
            contract_number: '',
            timestamp: '',
            text: '',
        },
    };

    return httpClient.request(
        `${process.env.LOCAL_API_BASE}/timeline-summarize`,
        {
            method: 'POST',
            body,
        }
    );
};

const pollForJobResult = async (httpClient: HttpClient, jobId: string) => {
    const maxAttempts = 30;
    let attempt = 0;

    while (attempt < maxAttempts) {
        await new Promise((res) => setTimeout(res, 5000));
        attempt++;

        const jobResponse: ReportSummaryJobResponse = await httpClient.request(
            `${process.env.LOCAL_API_BASE}/timeline-jobs/${jobId}`
        );

        if (jobResponse.status === 'FAILED') {
            throw new Error('Job failed');
        }
        if (jobResponse.status === 'FINISHED') {
            return jobResponse;
        }
    }
};

export default createEndpoint({ GET, POST });
