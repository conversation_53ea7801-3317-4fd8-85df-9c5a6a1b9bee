// src/pages/api/reports/[report/number]/revisions.ts

import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { getRevisions } from '@/utils/mongoDB/mongoDBFunctions';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { reportNumber } = req.query;
        if (!reportNumber || Array.isArray(reportNumber)) {
            return 'Invalid or missing report number';
        }
    },

    execute: async ({ req, res }) => {
        const reportNumber = String(req.query.reportNumber);

        // Fetch revisions from Mongo
        const revisions = await getRevisions('report', reportNumber);

        // Return 200 with array (empty array if none)
        return res.status(200).json(revisions);
    },
};

export default createEndpoint({ GET });
