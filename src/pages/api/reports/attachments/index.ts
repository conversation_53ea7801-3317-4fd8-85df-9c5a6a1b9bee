// src/pages/api/reports/[contractNumber]/attachments.ts
import { Prisma } from '@/generated/prisma-postgres';
import { AttachmentService } from '@/server/attachment/AttachmentService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'reportNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;
        const where = {
            ...parseQuery(req.query, { rootModel: Prisma.ModelName.Attachment }),
            ...(req.query.reportNumber
                ? { reportNumber: String(req.query.reportNumber) }
                : {}),
        };

        const service = new AttachmentService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ attachments: items, meta });
    },
};

export default createEndpoint({ GET });
