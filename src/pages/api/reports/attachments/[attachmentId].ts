import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ReportAttachmentService } from '@/server/report/ReportAttachmentService';
import { fileManager } from '@/utils/fileManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { attachmentId } = req.query;
        if (
            !attachmentId ||
            (Array.isArray(attachmentId) && attachmentId.length !== 1)
        ) {
            return 'Invalid or missing contract number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const { attachmentId } = req.query;

        // service
        const service = new ReportAttachmentService(db);

        const attachment = await service.getReportAttachment({
            id: Number(attachmentId),
        });

        if (!attachment) {
            res.status(404).json({ message: 'Invoice not found' });
            return;
        }

        const pdf_stream = await fileManager.downloadFile(attachment.bucketPath!);

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="${attachment.name}"`
        );

        // Pipe the S3 stream to the response
        pdf_stream.on('error', (err: any) => {
            console.error('Error fetching File from S3:', err);
            res.status(500).json({ error: 'Failed to fetch File' });
        });

        pdf_stream.pipe(res);
    },
};

export default createEndpoint({ GET });
