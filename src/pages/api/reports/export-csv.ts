// src/pages/api/report/export-csv.ts
import { Parser } from '@json2csv/plainjs';

import { reportFieldConfig } from '@/components/filters/reportFieldConfig';
import { Prisma, type Report } from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { ReportService } from '@/server/report/ReportService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
        if (req.method !== 'GET') return `Method ${req.method} Not Allowed`;
    },

    execute: async ({ req, res, db, auth }) => {
        try {
            const isAdmin = auth.roles.includes('asevo-admin');

            // --- Sorting -----------------------------------------------------------
            const sortField = String(req.query.sortField ?? 'reportNumber');
            const sortDirection =
                String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                    ? 'desc'
                    : 'asc';
            const orderBy = { [sortField]: sortDirection } as const;

            // --- Filters -----------------------------------------------------------
            const baseWhere = parseQuery(req.query, {
                rootModel: Prisma.ModelName.Report,
                fields: reportFieldConfig,
            });

            const where =
                !isAdmin
                    ? { AND: [baseWhere, { agencyNumber: String(auth.agencyNumber) }] }
                    : baseWhere;

            // --- Fetch all rows in batches ----------------------------------------
            const batchSize =
                Math.max(1, Number((req.query.batchSize as string) ?? 1000)) || 1000;

            const service = new ReportService(db as any);

            const first = await service.list({
                where,
                orderBy,
                skip: 0,
                take: batchSize,
            });

            const allItems = [...first.items];
            let fetched = first.items.length;
            const total = first.total;

            while (fetched < total) {
                const next = await service.list({
                    where,
                    orderBy,
                    skip: fetched,
                    take: batchSize,
                });
                allItems.push(...next.items);
                fetched += next.items.length;
            }

            // --- CSV mapping (camelCase values only) -------------------------------
            const csvData = allItems.map((item: Report) => ({
                vertragsnummer: item.contractNumber,
                kundennummer: item.customerNumber,
                meldung: item.text,
                schadensnummer: item.reportNumber,
                externeSchadensnummer: item.externalReportNumber,
                erstellungsdatum: item.createdAt,
                schadensdatum: item.damageDate,
                schadensort: item.damageLocation,
                iban: item.iban,
                versicherteGefahr: item.coveredRisk,
            }));

            const parser = new Parser({});
            const csv = parser.parse(csvData);

            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader('Content-Disposition', 'attachment; filename=reports.csv');
            res.status(200).send(csv);
        } catch (error) {
            console.error('CSV Export Error:', error);
            if (req.method !== 'GET') {
                res.setHeader('Allow', ['GET']);
                res.status(405).end(`Method ${req.method} Not Allowed`);
                return;
            }
            res.status(500).json({ error: 'Failed to export reports to CSV' });
        }
    },
};

export default createEndpoint({ GET });
