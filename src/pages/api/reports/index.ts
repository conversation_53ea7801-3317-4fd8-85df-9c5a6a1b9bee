import { invoiceFieldConfig } from '@/components/filters/invoiceFieldConfig';
import { Prisma } from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { ReportService } from '@/server/report/ReportService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },
    execute: async ({ req, res, db }) => {
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'reportNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Report,
            fields: invoiceFieldConfig,
        });

        const service = new ReportService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ items, meta });
    },
};

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        // Only check auth here. Body cannot be read twice.
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db }) => {
        const { reportData } = req.body;

        const reportService = new ReportService(db);

        // 🔧 Update the Report
        const updated = await reportService.update(
            { reportNumber: reportData.reportNumber },
            reportData
        );

        res.status(201).json(updated);
    },
};

export default createEndpoint({ GET, PUT });
