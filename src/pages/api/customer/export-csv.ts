// src/pages/api/customer/export-csv.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { Parser } from '@json2csv/plainjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization.split(" ")[1];

    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
        return;
    }

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        const { filters = '{}', sortField = 'customer_number', sortDirection = 'asc' } = req.query;
        const parsedFilters = JSON.parse(filters as string);
        const limit = 100;

        const filterParams = new URLSearchParams();
        if (authUser && !isAdmin) {
            filterParams.append('filters[agency_number][$eq]', authUser['agency_number']);
        }
        Object.entries(parsedFilters).forEach(([field, value]) => {
            filterParams.append(`filters[${field}][$contains]`, value as string);
        });
        const sort = `${sortField}:${sortDirection}`;
        filterParams.append('sort', sort);

        const customers: any[] = [];
        let offset = 0;
        let total = 0;


        const firstUrl = `${process.env.STRAPI_BASE_URL}/customers?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
        const firstRes = await fetch(firstUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!firstRes.ok) {
            const errorDetails = await firstRes.json();
            throw new Error(errorDetails?.error?.message || firstRes.statusText);
        }

        const firstData = await firstRes.json();
        customers.push(...firstData.data);
        total = firstData.meta.pagination.total;


        while (customers.length < total) {
            offset += limit;
            const pageUrl = `${process.env.STRAPI_BASE_URL}/customers?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
            const pageRes = await fetch(pageUrl, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!pageRes.ok) break;
            const pageData = await pageRes.json();
            customers.push(...pageData.data);
        }

        const csvData = customers.map((item) => ({
            Anrede: item.salutation,
            Titel: item.name_prefix,
            Vorname: item.first_name,
            Nachname: item.last_name,
            CO: item.care_of,
            Straße: item.street,
            Hausnummer: item.house_number,
            Postleitzahl: item.postal_code,
            Ort: item.city,
            Email: item.email,
            Makler: item.agent_number,
            Agentur: item.agency_number,
        }));

        const opts = {};
        const parser = new Parser(opts);
        const csv = parser.parse(csvData);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=customers.csv');
        res.status(200).send(csv);

    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).json({ error: 'Failed to export customers to CSV' });
    }
}
