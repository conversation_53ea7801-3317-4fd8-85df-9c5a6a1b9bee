import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/server/domain"
import {createEndpoint} from "@/server/createEndpoint"
import {prisma} from "@/server/prisma"
import {TipStatus, CustomerOffer, tipStatusOrder} from "@/domain"
import type {Tip} from "@prisma/client"
import {paginate} from "@/utils/paginate";
import {identityManager} from "@/server/IdentityManager";

type Query = {
    page?: string
    pageSize?: string
    sortBy?: keyof CustomerOffer
    order?: 'asc' | 'desc'
}

const GET: EndpointHandler = {
    execute: async ({ res, req }) => {
        const { page: pageString, pageSize: pageSizeString } = req.query as Query
        const page = pageString ? Number(pageString) : undefined
        const pageSize = pageSizeString ? Number(pageSizeString) : undefined

        const {customerId} = await identityManager.getCustomerOrThrow(req)

        const customer = await prisma.customer.findUnique({
            where: {
                id: customerId,
            }
        })
        if (!customer) {
            // TODO: probably some kind of error reporting and fallback?
            return res.status(200).json({ offers: [] })
        }

        const tips = await prisma.tip.findMany({
            where: {
                customerId,
            },
        })
        const raw = await Promise.all(
            tips.map(tip => createCustomerOffer(tip))
        )
        const sorted = sort(raw)
        const offers = paginate(sorted, page, pageSize)

        return res.status(200).json({ offers })
    }
}

export default createEndpoint({ GET })

const createCustomerOffer = async (tip: Tip): Promise<CustomerOffer> => {
    const [product, policy, offer] = await Promise.all([
        prisma.product.findUniqueOrThrow({where: {id: tip.productId}}),
        prisma.policy.findUnique({ where: { tipId: tip.id } }),
        prisma.offer.findUnique({ where: { tipId: tip.id } }),
    ])

    const premium = policy?.premium ?? offer?.premium

    return {
        id: tip.id,
        status: tip.status as TipStatus,
        productName: product.name,
        premium: premium ? {
            amount: premium,
            span: 'Jahr'
        } : undefined,
        expiresAt: offer?.expiresAt?.toISOString(),
        productExtensions: offer?.productExtensions,
        offerPdf: offer?.pdf,
        policyPdf: policy?.pdf,
        risk: tip.risk ?? undefined,
    }
}

const sort = (raw: CustomerOffer[]) => {
    return raw.toSorted((a, b) => {
        return tipStatusOrder[a.status] - tipStatusOrder[b.status]
    })
}
