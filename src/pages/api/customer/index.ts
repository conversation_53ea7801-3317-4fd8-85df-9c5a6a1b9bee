// src/pages/api/customer/liability-insurances.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const isAlpha = permissionManager.isUserAdmin(authUser)

            const { limit = 10, offset = 0, filters = '{}', sortField = 'customer_number', sortDirection = 'asc' } = req.query;

            // Parse filters from query
            const parsedFilters = JSON.parse(filters as string);
            const filterParams = new URLSearchParams();

            // Add agency filter if not admin
            if (authUser && !isAlpha) {
                filterParams.append('filters[agency_number][$eq]', authUser['agency_number']);
            }

            // Add dynamic filters
            Object.entries(parsedFilters).forEach(([field, value]) => {
                filterParams.append(`filters[${field}][$contains]`, value as string);
            });

            // Add sorting
            const sort = `${sortField}:${sortDirection}`;
            filterParams.append('sort', sort);

            // Construct the Strapi URL
            const strapiUrl = `${process.env.STRAPI_BASE_URL}/customers?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;

            const strapiResponse = await fetch(strapiUrl, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                console.error('Strapi Error:', errorDetails);
                throw new Error(`Failed to fetch customers: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }

            const data = await strapiResponse.json();

            const totalItems = data.meta.pagination.total;
            const pageCount = Math.ceil(totalItems / Number(limit));

            const meta = {
                total: totalItems,
                pageCount,
                pageSize: Number(limit),
                page: Math.floor(Number(offset) / Number(limit)) + 1,
            };

            res.status(200).json({
                customers: data.data,
                meta,
            });
        } catch (error) {
            console.error('Error fetching customers:', error);
            res.status(500).json({ error: 'Failed to fetch customers' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
