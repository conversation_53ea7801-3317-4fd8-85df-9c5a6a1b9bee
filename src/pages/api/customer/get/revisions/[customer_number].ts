// src/pages/api/customer/get/revisions/[customer_number].ts
import { getRevisions } from "@/utils/mongoDB/mongoDBFunctions";
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { customer_number } = req.query;

    if (!customer_number || typeof customer_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing customer number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const hasPermission = permissionManager.hasCustomerPermissions(customer_number, token)
        if (!hasPermission) {
            res.status(401)
        }

        const revs = await getRevisions(customer_number, 'customer_revision')

        res.status(201).json(revs)

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}