// src/pages/api/customer/create.ts
import { generateCustomerNumber } from '@/utils/numberGenerator';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        const { salutation, name_prefix, first_name, last_name, care_of, street, house_number, postal_code, city, email, agent_number, agency_number } = req.body;
        const input = salutation + name_prefix + first_name + last_name + care_of + street + house_number + postal_code + city + email
        const customer_number = generateCustomerNumber(input)

        if (!req.headers.authorization) {
            res.status(401)
            return
        }
        const token = req.headers.authorization!.split(" ")[1]
        const authUser = await permissionManager.getAuthUser(token)
        const isAdmin = permissionManager.isUserAdmin(authUser)

        try {
            // send POST request to strapi endpoint
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/customers`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`, // API token for authentication
                },
                body: JSON.stringify({
                    data: {
                        salutation,
                        name_prefix,
                        first_name,
                        last_name,
                        care_of,
                        street,
                        house_number,
                        postal_code,
                        city,
                        email,
                        active: true,
                        customer_number: customer_number,
                        agent_number: isAdmin ? agent_number : authUser['agent_number'],
                        agency_number: isAdmin ? agency_number : authUser['agency_number']
                    },
                }),
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to create customer: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }

            const responseData = await strapiResponse.json();
            const newCustomer = responseData.data; // strapi returns new created entry

            res.status(201).json(newCustomer);
        } catch (error) {
            console.error('Error creating customer in Strapi:', error);
            res.status(500).json({ error: 'Failed to create customer' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

