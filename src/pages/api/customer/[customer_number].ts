// src/pages/api/customer/[customer_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { CustomerData } from '../../../types';
import { dbWriter } from '@/utils/dbWriter';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const { customer_number } = req.query;
    const token = req.headers.authorization!.split(" ")[1]

    if (!customer_number || typeof customer_number !== 'string') {
        res.status(400).json({ error: 'Invalid or missing customer number' });
        return;
    }

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const isAdmin = permissionManager.isUserAdmin(authUser)
            let strapiResponse: any | undefined

            if (authUser && !isAdmin) {
                strapiResponse = await fetch(
                    `${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${customer_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
                    {
                        method: 'GET',
                        headers: {
                            Authorization: `Bearer ${token}`
                        },
                    }
                );
            } else if (authUser && isAdmin) {
                strapiResponse = await fetch(
                    `${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${customer_number}`,
                    {
                        method: 'GET',
                        headers: {
                            Authorization: `Bearer ${token}`
                        },
                    }
                );
            }

            if (strapiResponse.ok) {
                const data = await strapiResponse.json();
                if (data.data.length === 0) {
                    res.status(404).json({ message: 'Customer not found' });
                    return;
                }
                res.status(200).json(data.data[0]); // Return the first match
            } else {
                const errorDetails = await strapiResponse.json();
                throw new Error(errorDetails.error?.message || strapiResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching customer from Strapi:', error);
            res.status(500).json({ error: 'Failed to fetch customer' });
        }
    } else if (req.method === 'PUT') {
        try {
            const data: CustomerData = req.body;

            if (!data || typeof data !== 'object') {
                res.status(400).json({ error: 'Invalid or missing request body' });
                return;
            }

            const user = await permissionManager.getAuthUser(token)
            if (user == undefined) {
                res.status(401)
            }

            const customerData = await dbWriter.updateDocument(token, "customers", data.documentId, data, "customer", 'customer_number');
            res.status(200).json(customerData);

        } catch (error) {
            console.error('Error updating customer in Strapi:', error);
            res.status(500).json({ error: 'Failed to update customer' });
        }
    } else {
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
