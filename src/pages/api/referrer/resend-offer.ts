import * as z from "zod";
import { prisma } from "@/server/prisma";
import {EndpointHandler} from "@/server/domain";
import {createEndpoint} from "@/server/createEndpoint";
import {identityManager} from "@/server/IdentityManager";

const RequestBodySchema = z.strictObject({
    tipId: z.number(),
});
type RequestBody = z.infer<typeof RequestBodySchema>

const POST: EndpointHandler = {
    schemas: {
        body: RequestBodySchema,
    },
    execute: async ({ req, res, emailService }) => {
        const { tipId } = req.body as RequestBody
        const { referrerId } = await identityManager.getReferrerOrThrow(req)

        const tip = await prisma.tip.findUniqueOrThrow({
            where: { id: tipId },
        })
        const { customerId, productId } = tip

        const referrer = await prisma.referrer.findUniqueOrThrow({
            where: { id: referrerId },
        })!
        const customer = await prisma.customer.findUniqueOrThrow({
            where: { id: customerId }
        })!

        await emailService.sendCustomerInvitationEmail({
            customer,
            referrer,
            productId,
        })

        return res.status(200).json({ success: true });
    }
};

export default createEndpoint({ POST });
