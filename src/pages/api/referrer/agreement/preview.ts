import { Endpoint<PERSON>and<PERSON> } from "@/server/domain";
import {createEndpoint} from "@/server/createEndpoint";
import {createAgreement} from "@/utils/createAgreement";
import {ReferralFlowFormSchema} from "@/zod";

const POST: EndpointHandler = {
    schemas: {
        body: ReferralFlowFormSchema,
    },
    execute: async ({ req, res }) => {
        const bytes = await createAgreement(req.body)
        const buffer = Buffer.from(bytes);
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'inline; filename="preview.pdf"');
        res.status(200).send(buffer);
    }
};

export default createEndpoint({ POST });