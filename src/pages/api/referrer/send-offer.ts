import * as z from "zod";
import { prisma } from "@/server/prisma";
import {EndpointHandler} from "@/server/domain";
import {createEndpoint} from "@/server/createEndpoint";
import {createAgreement} from "@/utils/createAgreement";
import {fileManager} from "@/server/FileManager";
import {identityManager} from "@/server/IdentityManager";
import {ReferralFlowFormSchema} from "@/zod";

const productId = 1

type RequestBody = z.infer<typeof ReferralFlowFormSchema>

const POST: EndpointHandler = {
    schemas: {
        body: ReferralFlowFormSchema,
    },
    execute: async ({ req, res, emailService }) => {
        const { referrer, customer, residentialBuildingInsurance } = req.body as RequestBody
        const { referrerId } = await identityManager.getReferrerOrThrow(req)

        const updatedReferrer = await prisma.referrer.update({
            where: { id: referrerId },
            data: {
                firstName: referrer.firstName,
                lastName: referrer.lastName,
                street: referrer.street,
                houseNumber: referrer.houseNumber,
                postalCode: referrer.postalCode,
                city: referrer.city,
                phoneNumber: referrer.phoneNumber,
                agencyNumber: referrer.agencyNumber,
                salesDirection: referrer.salesDirection,
                iban: referrer.iban,
            },

        })
        const createdCustomer = await prisma.customer.create({
            data: {
                salutation: customer.salutation,
                firstName: customer.firstName,
                lastName: customer.lastName,
                email: customer.email,
                // stay empty for now
                street: '',
                houseNumber: '',
                postalCode: '',
                city: '',
                phoneNumber: '',
            }
        })
        const previousInsurance = await prisma.previousInsurance.create({
            data: {
                contractNumber: residentialBuildingInsurance?.contractNumber ?? '',
                consentGiven: true
            }
        })

        const tip = await prisma.tip.create({
            data: {
                agreementPdf: '',
                status: 'invited',
                referrerId,
                productId,
                customerId: createdCustomer.id,
                previousInsuranceId: previousInsurance.id,
            }
        })
        const agreement = await createAgreementAndPersist(req.body, tip.id)

        await Promise.all([
            emailService.sendCustomerInvitationEmail({
                customer: createdCustomer,
                referrer: updatedReferrer,
                productId,
            }),
            emailService.sendReferrerAgreementEmail({
                referrer: updatedReferrer,
                agreement,
            })
        ])

        return res.status(200).json({ success: true });
    }
};

export default createEndpoint({ POST });

const createAgreementAndPersist = async ({ referrer, customer }: RequestBody, tipId: number) => {
    const agreement = await createAgreement({
        referrer,
        customer,
    })
    const agreementPath = await fileManager.upload(`tips/${tipId}/agreement.pdf`, agreement)
    await prisma.tip.update({
        where: { id: tipId },
        data: {
            agreementPdf: agreementPath,
        }
    })

    return agreement
}
