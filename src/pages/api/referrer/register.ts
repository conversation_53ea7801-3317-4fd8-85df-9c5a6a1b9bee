import { NextApiRequest, NextApiResponse } from "next";
import crypto from "crypto";
import { prisma } from "@/server/prisma";
import { sendEmail } from "@/utils/mailManager";
import { renderReferrerLoginEmail } from "@/emails/renderReferrerLoginEmail";
import {urls} from "@/server/urls";

const ALLOWED_DOMAINS =
  process.env.ALLOWED_DOMAINS?.split(",").map((d) => d.trim().toLowerCase()) || [];

async function sendThemedEmail(to: string, ctaUrl: string) {
  const html = await renderReferrerLoginEmail({
    ctaUrl,
    referrerEmail: to
  });

  await sendEmail({
    to,
    subject: "Ihr Zugang zum Tippgeber-Bereich",
    html,
  });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") return res.status(405).json({ error: "Method not allowed" });

  const { email } = req.body as { email: string };
  if (!email) return res.status(400).json({ error: "Email required" });

  const domain = email.split("@")[1].toLowerCase();
  if (!ALLOWED_DOMAINS.includes(domain))
    return res.status(400).json({ error: "Domain not allowed" });

  let referrer = await prisma.referrer.findUnique({ where: { email } });
  if (!referrer) {
    referrer = await prisma.referrer.create({
      data: {
        email,
        firstName: '',
        lastName: '',
        street: '',
        houseNumber: '',
        postalCode: '',
        city: '',
        phoneNumber: '',
        agencyNumber: '',
        salesDirection: '',
        iban: '',
      }
    });
  }

  const token = crypto.randomBytes(32).toString("hex");
  await prisma.referrerAccessToken.create({
    data: {
      referrerId: referrer.id,
      token,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30), // 30 Tage
    },
  });

  const link = urls.referrer.access(token);

  console.log("[REGISTER] Mail link:", link);
  await sendThemedEmail(email, link);

  return res.status(200).json({ success: true });
}
