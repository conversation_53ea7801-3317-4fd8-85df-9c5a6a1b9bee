import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from "@/server/domain"
import {createEndpoint} from "@/server/createEndpoint"
import {prisma} from "@/server/prisma"
import {TipStatus, tipStatusOrder} from "@/domain"
import type {Tip} from "@prisma/client"
import {paginate} from "@/utils/paginate";
import {forbidden} from "@/server/responses";
import {Parser} from "@json2csv/plainjs";
import {getTranslations} from "@/server/translations";
import {formatDate} from "@/utils/format";
import {identityManager} from "@/server/IdentityManager";

type CsvReferral = {
    status: TipStatus
    createdAt: string
    customerName: string
    customerEmail: string
    productName: string
    commission?: number
    premium?: number
    referrer: {
        name: string
        phoneNumber: string
        iban: string
    }
};

type Query = {
    search?: string
    status?: TipStatus
    page?: string
    pageSize?: string
    sortBy?: keyof CsvReferral
    order?: 'asc' | 'desc'
}

const translations = getTranslations()

const GET: EndpointHandler = {
    execute: async ({ res, req, logger }) => {
        const referrer = await identityManager.getReferrerOrThrow(req)
        if (!referrer.isAdmin) {
            return forbidden(res)
        }

        const { search, status, sortBy, order, page: pageString, pageSize: pageSizeString } = req.query as Query
        const page = pageString ? Number(pageString) : undefined
        const pageSize = pageSizeString ? Number(pageSizeString) : undefined

        const tips = await prisma.tip.findMany({
            where: {
                status: status || undefined,
            },
        })
        const promises = tips.map(async (tip) => {
            try {
                return await createCsvReferral({
                    tip,
                    rawSearchTerm: search,
                })
            } catch (error) {
                logger.error(`Failed while aggregating fields for a CSV row for [tipId=${tip.id}]`, error)

                return null
            }
        })
        const referralsAndNulls = await Promise.all(promises)

        const referrals = referralsAndNulls.filter(r => r !== null)
        const sorted = sort(referrals, sortBy, order)
        const paginated = paginate(sorted, page, pageSize)

        const csvRows = paginated.map((item) => ({
            'Kunde': item.customerName,
            'Kunden-Emailadresse': item.customerEmail,
            'Produkt': item.productName,
            'Status': translations.tipStatus[item.status],
            'Vergütung': typeof item.commission === 'number' ? `${item.commission} €` : '',
            'Prämie': typeof item.premium === 'number' ? `${item.premium} €` : '',
            'Tippgeber': item.referrer.name,
            "Tippgeber-Telefonnummer": item.referrer.phoneNumber,
            'Tippgeber-IBAN': item.referrer.iban,
            'Erstellt am': formatDate(item.createdAt),
        }));
        const csv = new Parser().parse(csvRows);

        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader(
            'Content-Disposition',
            'attachment; filename=tips.csv'
        );
        res.status(200).send(csv);
    }
}

export default createEndpoint({ GET })

type Params = {
    tip: Tip
    rawSearchTerm?: string
}
const createCsvReferral = async ({ tip, rawSearchTerm }: Params): Promise<CsvReferral | null> => {
    const [customer, referrer] = await Promise.all([
        prisma.customer.findUniqueOrThrow({where: { id: tip.customerId}}),
        prisma.referrer.findUniqueOrThrow({where: { id: tip.referrerId}}),
    ])

    const customerName = `${customer.firstName} ${customer.lastName}`
    const searchTerm = rawSearchTerm?.trim().toLowerCase()
    if (searchTerm) {
        const satisfies = customerName.toLowerCase().includes(searchTerm) || customer.email.toLowerCase().includes(searchTerm)
        if (!satisfies) {
            return null
        }
    }

    const [product, policy, offer] = await Promise.all([
        prisma.product.findUniqueOrThrow({where: { id: tip.productId}}),
        prisma.policy.findUnique({ where: { tipId: tip.id } }),
        prisma.offer.findUnique({ where: { tipId: tip.id } }),
    ])

    return {
        customerName,
        customerEmail: customer.email,
        status: tip.status as TipStatus,
        premium: policy?.premium ?? offer?.premium,
        commission: policy?.commission,
        createdAt: tip.createdAt.toISOString(),
        productName: product.name,
        referrer: {
            name: `${referrer.firstName} ${referrer.lastName}`,
            phoneNumber: referrer.phoneNumber,
            iban: referrer.iban,
        },
    }
}

const sort = (referrals: CsvReferral[], sortBy: keyof CsvReferral = 'createdAt', order: 'asc' | 'desc' = 'desc') => {
    const cmp = (a: CsvReferral, b: CsvReferral) => {
        let va: number | string;
        let vb: number | string;

        switch (sortBy) {
            case "createdAt":
                va = new Date(a.createdAt).getTime();
                vb = new Date(b.createdAt).getTime();
                break;
            case "premium":
                va = a.premium ?? 0;
                vb = b.premium ?? 0;
                break;
            case "commission":
                va = a.commission ?? 0;
                vb = b.commission ?? 0;
                break;
            case "status":
                va = tipStatusOrder[a.status];
                vb = tipStatusOrder[b.status];
                break;
            case "customerName":
                va = a.customerName.toLowerCase();
                vb = b.customerName.toLowerCase();
                break;
            default:
                va = String(a[sortBy] ?? "");
                vb = String(b[sortBy] ?? "");
        }

        const res =
            typeof va === "number" && typeof vb === "number"
                ? va - vb
                : String(va).localeCompare(String(vb), "de");

        return order === "asc" ? res : -res;
    };

    return referrals.toSorted(cmp);
}