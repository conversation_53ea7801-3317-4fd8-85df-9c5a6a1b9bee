import { End<PERSON><PERSON><PERSON><PERSON> } from "@/server/domain"
import {createEndpoint} from "@/server/createEndpoint"
import {prisma} from "@/server/prisma"
import {TipStatus, Referral, tipStatusOrder} from "@/domain"
import type {Tip} from "@prisma/client"
import {paginate} from "@/utils/paginate";
import {identityManager} from "@/server/IdentityManager";

type Query = {
    search?: string
    status?: TipStatus
    page?: string
    pageSize?: string
    sortBy?: keyof Referral
    order?: 'asc' | 'desc'
}

const GET: EndpointHandler = {
    execute: async ({ res, req, logger }) => {
        const { search, status, sortBy, order, page: pageString, pageSize: pageSizeString } = req.query as Query
        const page = pageString ? Number(pageString) : undefined
        const pageSize = pageSizeString ? Number(pageSizeString) : undefined

        const {referrerId, isAdmin} = await identityManager.getReferrerOrThrow(req)

        const tips = await prisma.tip.findMany({
            where: {
                referrerId: isAdmin ? undefined : referrerId,
                status: status || undefined,
            },
        })
        const promises = tips.map(async (tip) => {
            try {
                return await createReferral(tip, search)
            } catch (error) {
                logger.error(`Failed while aggregating fields for a referral with [tipId=${tip.id}]`, error)
                return null
            }
        })
        const referralsAndNulls = await Promise.all(promises)

        const referrals = referralsAndNulls.filter(r => r !== null)
        const sorted = sort(referrals, sortBy, order)
        const paginated = paginate(sorted, page, pageSize)

        return res.status(200).json({ referrals: paginated })
    }
}

export default createEndpoint({ GET })

const createReferral = async (tip: Tip, rawSearchTerm?: string): Promise<Referral | null> => {
    const customer = await prisma.customer.findUniqueOrThrow({
        where: { id: tip.customerId }
    })

    const customerName = `${customer.firstName} ${customer.lastName}`
    const searchTerm = rawSearchTerm?.trim().toLowerCase()
    if (searchTerm) {
        const satisfies = customerName.toLowerCase().includes(searchTerm) || customer.email.toLowerCase().includes(searchTerm)
        if (!satisfies) {
            return null
        }
    }

    const [previousInsurance, product, policy, offer] = await Promise.all([
        prisma.previousInsurance.findUniqueOrThrow({ where: { id: tip.previousInsuranceId } }),
        prisma.product.findUniqueOrThrow({ where: { id: tip.productId } }),
        prisma.policy.findUnique({ where: { tipId: tip.id } }),
        prisma.offer.findUnique({ where: { tipId: tip.id } }),
    ])

    return {
        id: String(tip.id),
        tipId: tip.id,
        customerName,
        customerEmail: customer.email,
        status: tip.status as TipStatus,
        premium: policy?.premium ?? offer?.premium,
        commission: policy?.commission,
        createdAt: tip.createdAt.toISOString(),
        expiresAt: offer?.expiresAt?.toISOString(),
        productExtensions: offer?.productExtensions,
        productName: product.name,
        previousContractNumber: previousInsurance.contractNumber,
    }
}

const sort = (referrals: Referral[], sortBy: keyof Referral = 'createdAt', order: 'asc' | 'desc' = 'desc') => {
    const cmp = (a: Referral, b: Referral) => {
        let va: number | string;
        let vb: number | string;

        switch (sortBy) {
            case "createdAt":
                va = new Date(a.createdAt).getTime();
                vb = new Date(b.createdAt).getTime();
                break;
            case "premium":
                va = a.premium ?? 0;
                vb = b.premium ?? 0;
                break;
            case "commission":
                va = a.commission ?? 0;
                vb = b.commission ?? 0;
                break;
            case "status":
                va = tipStatusOrder[a.status];
                vb = tipStatusOrder[b.status];
                break;
            case "customerName":
                va = a.customerName.toLowerCase();
                vb = b.customerName.toLowerCase();
                break;
            default:
                va = String(a[sortBy] ?? "");
                vb = String(b[sortBy] ?? "");
        }

        const res =
            typeof va === "number" && typeof vb === "number"
                ? va - vb
                : String(va).localeCompare(String(vb), "de");

        return order === "asc" ? res : -res;
    };

    return referrals.toSorted(cmp);
}