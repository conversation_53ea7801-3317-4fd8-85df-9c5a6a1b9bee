import { Endpoint<PERSON><PERSON><PERSON> } from "@/server/domain"
import {createEndpoint} from "@/server/createEndpoint"
import {prisma} from "@/server/prisma"
import {identityManager} from "@/server/IdentityManager";

const GET: EndpointHandler = {
    execute: async ({ res, req }) => {
        const {referrerId} = await identityManager.getReferrerOrThrow(req)

        const dbReferrer = await prisma.referrer.findUnique({
            where: { id: referrerId }
        })
        if (!dbReferrer) {
            return res.status(404).json({ error: 'Unknown referrer identity' })
        }

        const { id, isAdmin, createdAt, updatedAt, ...referrer } = dbReferrer

        return res.status(200).json({ referrer })
    }
}

export default createEndpoint({ GET })
