// src/pages/api/contracts/clientId/[clientId].ts
import { type NextApiRequest, type NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (!req.headers.authorization) {
    res.status(401);
    return;
  }
  const token = req.headers.authorization!.split(' ')[1];

  if (req.method === 'POST') {
    fetch(`${process.env.STRAPI_BASE_URL}/auth/change-password`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(req.body),
    })
      .then(async (response) => {
        const json = await response.json();
        if (response.ok) {
          res.status(201).json(json);
        } else {
          res.status(401).json({ error: json.error.message });
        }
      })
      .catch((error) => {
        // Handle fetch or network errors
        res
          .status(500)
          .json({ error: 'Internal server error', details: error.message });
      });
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
