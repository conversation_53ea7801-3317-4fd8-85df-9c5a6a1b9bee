// src/pages/api/user/register.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { mailManager } from '@/utils/mailManager';
import { generateAgencyNumber, generateAgentNumber } from '@/utils/numberGenerator';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { salutation,
        first_name,
        last_name,
        street,
        house_number,
        postal_code,
        city,
        email,
        agency_name,
        url,
        telephone_number
    } = req.body;

    const combined_string = salutation + first_name + last_name + street + house_number + postal_code + city + email + url + telephone_number

    const agent_number = generateAgentNumber(combined_string)
    const agency_number = generateAgencyNumber((combined_string + agency_name))

    try {

        mailManager.sendRegistrationMail(agent_number, agency_number, req.body)

        res.status(201).json({ error: 'Error sending Mail' })
    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}
