// src/pages/api/contracts/clientId/[clientId].ts
import { type NextApiRequest, type NextApiResponse } from 'next';

import { permissionManager } from '@/utils/permissionManager';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (!req.headers.authorization) {
    res.status(401);
    return;
  }
  const token = req.headers.authorization!.split(' ')[1];
  const authUser = await permissionManager.getAuthUser(token);
  if (authUser == undefined) {
    res.status(401).end('Unaurtharized');
    return;
  }

  if (req.method === 'GET') {
    res.status(200).json(authUser);
  } else {
    res.setHeader('Allow', ['GET']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
