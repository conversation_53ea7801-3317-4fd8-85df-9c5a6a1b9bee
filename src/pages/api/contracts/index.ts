// src/pages/api/contracts/index.ts

import { contractFieldConfig } from '@/components/filters/contractFieldConfig';
import {
    type Agent,
    type CalculationParameter,
    type Contract,
    type Customer,
    type Invoice,
    Prisma,
} from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { AttachmentService } from '@/server/attachment/AttachmentService';
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { ContractService } from '@/server/contract/ContractService'; // assumes you have a ContractService similar to AgentService
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { type ContractStatusData } from '@/types';
import {
    calculateContract,
    calculateContractWithInvoiceAmount,
    calculateContractWithPremie,
    calculateContractWithPremieAndInvoiceAmount,
} from '@/utils/contractCalculator';
import { fileManager } from '@/utils/fileManager';
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import {
    InvoiceAgentStatusType,
    InvoiceCustomerStatusType,
    type InvoiceDetailStatusData,
    InvoiceInsuranceStatusType,
    InvoiceStatusType,
    InvoiceType,
} from '@/utils/invoice/types';
import { mailManager } from '@/utils/mailManager';
import { generateContractNumber } from '@/utils/numberGenerator';
import { createContractPDF } from '@/utils/pdfManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'contractNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Contract,
            fields: contractFieldConfig,
        });

        const service = new ContractService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ items, meta });
    },
};

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const contract = req.body.contract as Contract;
        if (!contract) return "Invalid body: 'contract' is required";
    },

    execute: async ({ req, res, db, auth }) => {
        const contract = req.body.contract as Contract;
        const isAdmin = auth.roles.includes('asevo-admin');

        const {
            premie,
            premieHouseholdTech,
            premiePvSystem,
            premieGlassInsurance,
            invoiceAmount,
        } = contract;

        const calculationParameterService = new CalculationParameterService(db);
        let contractData: Contract = await calculateContract(
            contract,
            calculationParameterService
        );
        // overwrite premien if set by admin
        let temp_premie = premie || 0;
        if (isAdmin) {
            if (premieHouseholdTech != null) {
                contractData.premieHouseholdTech = premieHouseholdTech;
            }
            if (premiePvSystem != null) {
                contractData.premiePvSystem = premiePvSystem;
            }
            if (premieGlassInsurance != null) {
                contractData.premieGlassInsurance = premieGlassInsurance;
            }
        }

        if (premieHouseholdTech || premiePvSystem || premieGlassInsurance) {
            temp_premie = 0;
        }

        if (premieHouseholdTech) {
            temp_premie += premieHouseholdTech;
        }

        if (premiePvSystem) {
            temp_premie += premiePvSystem;
        }

        if (
            premieHouseholdTech ||
            premiePvSystem ||
            premieGlassInsurance ||
            premie != null ||
            invoiceAmount
        ) {
            contractData.isIndividuallyCalculated = true;
        }

        if (premie != null && invoiceAmount == null && isAdmin) {
            contractData = await calculateContractWithPremie(
                contractData,
                calculationParameterService,
                temp_premie
            );
        }

        if (premie == null && invoiceAmount != null && isAdmin) {
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithInvoiceAmount(
                contractData,
                calculationParameterService
            );
        }

        if (premie != null && invoiceAmount != null && isAdmin) {
            contractData.premie = premie;
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithPremieAndInvoiceAmount(
                contract,
                calculationParameterService
            );
        }

        const input = `${contractData.contractType}  ${contractData.insuranceStartDate}  ${contractData.insuranceEndDate}  ${contractData.paymentMode}  ${contractData.iban}  ${contractData.bic}  ${contractData.previousInsurance}  ${contractData.previousInsuranceNumber}  ${contractData.previousClaims}  ${contractData.isOffer}  ${contractData.riskAddresses}  ${contractData.buildingType}  ${contractData.isPermanentlyOccupied}  ${contractData.insuranceSum}  ${contractData.coveredRisks}  ${contractData.isElementar}  ${contractData.coverageAmount}  ${contractData.objectType}  ${contractData.tariffGroup} ${contractData.employeeCount} ${contractData.businessType} ${contractData.livingArea}  ${contractData.insuranceSum1914}  ${contractData.isConstructionYearUnknown} ${contractData.constructionYear}  ${contractData.coverageUsage}  ${contractData.householdTech}  ${contractData.pvSystem}  ${contractData.glassInsurance}  ${contractData.animalType}  ${contractData.animalData} ${contractData.shareData} ${contractData.insuredPersons} ${contractData.privateFirstName}  ${contractData.privateName}  ${contractData.familyCoverage}  ${contractData.isSingle}  ${contractData.premiumRate}  ${contractData.buildingSum}  ${contractData.premie}  ${contractData.premieHouseholdTech}  ${contractData.premiePvSystem}  ${contractData.premieGlassInsurance}  ${contractData.customerId}`;

        const newContractNumber = generateContractNumber(
            contractData.contractType!,
            input
        );

        const defaultContractStatus: ContractStatusData = {
            create_police: true,
            send_email: true,
            create_invoice: true,
            send_invoice: true,
            send_automatic_invoice: true,
            active: false,
        };

        const service = new ContractService(db);

        // set contract_number in original offer
        if (!contractData.isOffer && contractData.contractNumber != '') {
            const originalUpdated = await service.update(
                { documentId: contractData.documentId },
                { toContract: newContractNumber }
            );

            if (!originalUpdated) {
                throw new Error(`Failed to set newContractNumber to original offer`);
            }
        }

        contractData.contractNumber = newContractNumber;
        //contractData.contractStatus = defaultContractStatus as any;

        const agentService = new AgentService(db);
        const agent: Agent = (await agentService.findOne({
            agentNumber: auth.agentNumber,
        }))!;

        const customerService = new CustomerService(db);
        const customer: Customer = (await customerService.findOne({
            customerNumber: contractData.customerNumber!,
        }))!;

        contractData.activeStatus = 'ACTIVE';
        contractData.commission = isAdmin
            ? contractData.commission
            : agent.commission;
        contractData.fromOffer = !contractData.isOffer
            ? contractData.contractNumber
            : '';
        contractData.contractStatus = isAdmin
            ? contractData.contractStatus
            : defaultContractStatus;

        // set relations
        contractData.agentNumber = agent.agentNumber;
        contractData.agentId = agent.id;
        contractData.agencyNumber = agent.agencyNumber;
        contractData.agencyId = agent.agencyId;
        contractData.customerNumber = customer.customerNumber;
        contractData.customerId = customer.id;

        // create new contract
        const newContract = await service.create(contractData as any);

        if (!newContract) {
            res.status(404).json({ message: 'Error while creating contract' });
            return;
        }

        // Create and Upload PDF
        // only create pdf if create_police is true
        if (
            (newContract.contractStatus as unknown as ContractStatusData)
                .create_police
        ) {
            const calculationParameter: CalculationParameter =
                (await calculationParameterService.getCalculationParameters(
                    newContract.insuranceStartDate!
                ))!;

            const pdfBytes = await createContractPDF(
                newContract,
                customer,
                agent,
                calculationParameter
            );
            await fileManager.uploadFile(pdfBytes, newContract.contractNumber);

            const attachmentSevice = new AttachmentService(db);
            attachmentSevice.create({
                contractNumber: newContract.contractNumber,
                bucketPath: newContract.contractNumber,
                type: 'contract',
            });
        }

        // create first invoice
        if (
            (newContract.contractStatus as unknown as ContractStatusData)
                .create_invoice
        ) {
            const calculationParameters: CalculationParameter =
                (await calculationParameterService.getCalculationParameters(
                    newContract.insuranceStartDate!
                ))!;

            const positions = invoiceManager.generatePositionsFirstInvoice(
                newContract,
                calculationParameters
            );

            const invoiceStatus: InvoiceDetailStatusData = {
                send_date: '',
                overdue_date: '',
                dunning_date: '',
                partially_paid_date: '',
                partially_payment_amount: 0,
                payment_date: '',
                refund_date: '',
                cancel_date: '',
                fail_date: '',
                proccessing_date: '',
            };

            // create invoice entry in strapi
            const invoiceData: Invoice = {
                documentId: '',
                type: InvoiceType.FRIST_INVOICE,
                contractNumber: newContract.contractNumber,
                versionNumber: 0,
                invoiceDetailStatus: invoiceStatus as any,
                invoiceStatus: InvoiceStatusType.BOOKED,
                agentStatus: InvoiceAgentStatusType.OPEN,
                insuranceStatus: InvoiceInsuranceStatusType.OPEN,
                customerStatus: InvoiceCustomerStatusType.OPEN,
                invoiceNumber: '',
                dueDate: newContract.insuranceStartDate,
                totalNet: newContract.firstInvoiceNet!,
                totalGross: newContract.firstInvoiceGross!,
                positions: positions as any,
                billingStreet: customer.street,
                billingHouseNumber: customer.houseNumber,
                billingCity: customer.city,
                billingPostalCode: customer.postalCode,
                billingCareOf: customer.careOf,
                firstName: customer.firstName,
                lastName: customer.lastName,
                namePrefix: customer.namePrefix,
                paymentMode: newContract.paymentMode,
                insuranceStartDate: newContract.insuranceStartDate,
                insuranceEndDate: newContract.insuranceEndDate,
                iban: newContract.iban,
                bic: newContract.bic,
                agentStreet: agent.street,
                agentHouseNumber: agent.houseNumber,
                agentCity: agent.city,
                agentPostalCode: agent.postalCode,
                agentCompanyName: agent.companyName,
                agentNumber: agent.agentNumber,
                customerNumber: customer.customerNumber,
                subject: '',
                updatedAt: new Date(),
                createdAt: new Date(),
                automaticallyGenerated: true,
                agencyNumber: customer.agencyNumber,
                id: 0,
                customerId: customer.id,
                agencyId: agent.agencyId,
                agentId: agent.id,
                contractId: newContract.id,
            };

            const newInvoiceData = await invoiceManager.createEntry(db, invoiceData);

            const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData);
            await fileManager.uploadFile(pdfBytes, newInvoiceData.invoiceNumber);
        }

        if (
            (newContract.contractStatus! as unknown as ContractStatusData)
                .create_police &&
            (newContract.contractStatus! as unknown as ContractStatusData).send_email
        ) {
            if (newContract.isOffer == true) {
                await mailManager.sendOffer(customer, newContract, agent.email);
            } else {
                await mailManager.sendPolice(customer, newContract, agent.email);
            }
        }

        res.status(200).json(newContract);
    },
};

export default createEndpoint({ GET, POST });
