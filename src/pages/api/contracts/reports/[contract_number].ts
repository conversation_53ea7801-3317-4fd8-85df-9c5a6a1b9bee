// src/pages/api/contracts/reports/[contract_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { contract_number } = req.query;

    if (!contract_number || typeof contract_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {
        const authUser = await permissionManager.getAuthUser(token)
        const isAdmin = permissionManager.isUserAdmin(authUser)

        let strapiResponse: any | undefined

        if (authUser && !isAdmin)
            strapiResponse = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}&pagination[limit]=100`,
                'contract',
                token
            ))?.[0];
        else if (authUser && isAdmin)
            strapiResponse = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}&pagination[limit]=100`,
                'contract',
                token
            ))?.[0];

        if (!strapiResponse) {
            return res.status(404).json({ error: 'Contract not found' });
        }

        // Get customer_number from contract
        const customerNumber = strapiResponse.customer_number;
        if (!customerNumber) {
            throw new Error('Customer number is missing in contract data.');
        }

        const reports = await fetchDataFromAPI(
            `${process.env.STRAPI_BASE_URL}/reports?filters[contract_number][$eq][0]=${contract_number}`,
            'report',
            token
        );
        res.status(200).json(reports);

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}
