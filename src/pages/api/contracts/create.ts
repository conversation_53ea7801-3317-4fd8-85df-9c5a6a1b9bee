// src/pages/api/contracts/create.ts
import { CalculationParametersData, ContractData, ContractStatusData } from '@/types';
import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceData, InvoiceInsuranceStatusType, InvoiceDetailStatusData, InvoiceStatusType, InvoiceType } from "@/utils/invoice/types";
import { calculatePrice, getTax } from '@/utils/calculationUtil';
import { calculateContract, calculateContractWithInvoiceAmount, calculateContractWithPremie, calculateContractWithPremieAndInvoiceAmount, getCalculationParameters } from '@/utils/contractCalculator';
import { fileManager } from '@/utils/fileManager';
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { formatLabel } from '@/utils/keyFormatter';
import { mailManager } from '@/utils/mailManager';
import { generateContractNumber } from '@/utils/numberGenerator';
import { createContractPDF, createInvoicePDF } from '@/utils/pdfManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const authUser = await permissionManager.getAuthUser(token)
    const isAdmin = permissionManager.isUserAdmin(authUser)
    const { formData, customer } = req.body

    if (req.method === 'POST') {
        const {
            contract_type,
            insurance_start_date,
            insurance_end_date,
            insurance_main_due_date,
            payment_mode,
            iban,
            bic,
            previous_insurance,
            previous_insurance_number,
            previous_claims,
            is_offer,
            risk_addresses,
            building_type,
            is_permanently_occupied,
            insurance_sum,
            covered_risks,
            is_elementar,
            coverage_amount,
            object_type,
            tariff_group,
            employee_count,
            business_type,
            living_area,
            insurance_sum_1914,
            is_construction_year_unknown,
            construction_year,
            coverage_usage,
            household_tech,
            pv_system,
            glass_insurance,
            animal_type,
            animal_data,
            private_first_name,
            private_name,
            family_coverage,
            is_single,
            premium_rate,
            building_sum,
            additional_agreements,
            customer_id,
            customer_number,
            zuers_zone,
            share_data,
            insured_persons,
            is_individual_unit,
            individual_unit,
            commission,
            contract_status,
            contract_number,
            documentId,
            premie,
            premie_household_tech,
            premie_pv_system,
            premie_glass_insurance,
            monument_protection,
            sgk,
            risk_living_unit_amount,
            risk_commercial_unit_amount,
            risk_gerage_amount,
            commercial_area,
            garage_area,
            is_individually_calculated,
            invoice_amount
        } = formData;

        let contractData = await calculateContract(formData, token)
        // overwrite premien if set by admin
        let temp_premie = premie
        if (isAdmin) {
            if (premie_household_tech != null) {
                contractData.premie_household_tech = premie_household_tech
            }
            if (premie_pv_system != null) {
                contractData.premie_pv_system = premie_pv_system
            }
            if (premie_glass_insurance != null) {
                contractData.premie_glass_insurance = premie_glass_insurance
            }
        }

        if (premie_household_tech || premie_pv_system || premie_glass_insurance) {
            temp_premie = 0
        }

        if (premie_household_tech) {
            temp_premie += premie_household_tech
        }

        if (premie_pv_system) {
            temp_premie += premie_pv_system
        }

        if (premie_household_tech || premie_pv_system || premie_glass_insurance || premie != null || invoice_amount) {
            contractData.is_individually_calculated = true
        }

        if ((premie != null && invoice_amount == null && isAdmin)) {
            contractData = await calculateContractWithPremie(contractData, token, temp_premie)
        }

        if ((premie == null && invoice_amount != null && isAdmin)) {
            contractData.invoice_amount = invoice_amount
            contractData = await calculateContractWithInvoiceAmount(contractData, token)
        }

        if ((premie != null && invoice_amount != null && isAdmin)) {
            contractData.premie = premie
            contractData.invoice_amount = invoice_amount
            contractData = await calculateContractWithPremieAndInvoiceAmount(formData, token)
        }

        const input = `${contract_type}  ${insurance_start_date}  ${insurance_end_date}  ${payment_mode}  ${iban}  ${bic}  ${previous_insurance}  ${previous_insurance_number}  ${previous_claims}  ${is_offer}  ${risk_addresses}  ${building_type}  ${is_permanently_occupied}  ${insurance_sum}  ${covered_risks}  ${is_elementar}  ${coverage_amount}  ${object_type}  ${tariff_group} ${employee_count} ${business_type} ${living_area}  ${insurance_sum_1914}  ${is_construction_year_unknown} ${construction_year}  ${coverage_usage}  ${household_tech}  ${pv_system}  ${glass_insurance}  ${animal_type}  ${animal_data} ${share_data} ${insured_persons} ${private_first_name}  ${private_name}  ${family_coverage}  ${is_single}  ${premium_rate}  ${building_sum}  ${contractData.premie}  ${contractData.premie_household_tech}  ${contractData.premie_pv_system}  ${contractData.premie_glass_insurance}  ${customer_id}`;

        const new_contract_number = generateContractNumber(contract_type, input)

        const default_contract_status: ContractStatusData = {
            create_police: true,
            send_email: true,
            create_invoice: true,
            send_invoice: true,
            send_automatic_invoice: true,
            active: false
        }

        // set contract_number in original offer
        if (!is_offer && contract_number != '') {
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts/${documentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    data: {
                        to_contract: new_contract_number
                    },
                }),
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to set new_contract_number to original offer: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }
        }

        try {
            // send POST request to strapi endpoint
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`, // API token for authentication
                },
                body: JSON.stringify({
                    data: {
                        contract_type,
                        insurance_start_date,
                        insurance_end_date,
                        insurance_main_due_date,
                        payment_mode,
                        iban,
                        bic,
                        previous_insurance,
                        previous_insurance_number,
                        previous_claims,
                        is_offer,
                        risk_addresses,
                        building_type,
                        is_permanently_occupied,
                        insurance_sum,
                        covered_risks,
                        is_elementar,
                        coverage_amount,
                        object_type,
                        tariff_group,
                        employee_count,
                        business_type,
                        living_area,
                        insurance_sum_1914,
                        is_construction_year_unknown,
                        construction_year,
                        coverage_usage,
                        household_tech,
                        pv_system,
                        glass_insurance,
                        animal_type,
                        animal_data,
                        private_first_name,
                        private_name,
                        family_coverage,
                        is_single,
                        premium_rate,
                        building_sum,
                        premie: contractData.premie,
                        first_invoice_net: contractData.first_invoice_net,
                        first_invoice_tax: contractData.first_invoice_tax,
                        first_invoice_gross: contractData.first_invoice_gross,
                        first_invoice_glass_net: contractData.first_invoice_glass_net,
                        first_invoice_glass_tax: contractData.first_invoice_glass_tax,
                        first_invoice_glass_gross: contractData.first_invoice_glass_gross,
                        premie_household_tech: contractData.premie_household_tech,
                        premie_pv_system: contractData.premie_pv_system,
                        premie_glass_insurance: contractData.premie_glass_insurance,
                        contract_number: new_contract_number,
                        customer_number,
                        'additional_agreements': authUser['agency']['is_admin'] == true ? additional_agreements : "",
                        agency_number: isAdmin ? customer['agency_number'] : authUser['agency_number'],
                        agent_number: isAdmin ? customer['agent_number'] : authUser['agent_number'],
                        tax: contractData.tax,
                        invoice_amount: contractData.invoice_amount,
                        zuers_zone,
                        share_data,
                        insured_persons,
                        is_individual_unit,
                        individual_unit,
                        commission: isAdmin ? commission : authUser['commission'],
                        contract_status: isAdmin ? contract_status : default_contract_status,
                        from_offer: !is_offer ? contract_number : '',
                        'to_contract': "",
                        monument_protection,
                        sgk,
                        risk_living_unit_amount,
                        risk_commercial_unit_amount,
                        risk_gerage_amount,
                        commercial_area,
                        garage_area,
                        is_individually_calculated: contractData.is_individually_calculated,
                        glass_tax: contractData.glass_tax,
                        active_status: "ACTIVE"
                    },
                }),
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to create contract in database: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }

            const responseData = await strapiResponse.json();
            const newContractData: ContractData = responseData.data; // strapi returns new created entry

            // Create and Upload PDF
            // only create pdf if create_police is true
            if (newContractData.contract_status.create_police) {

                const agent = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${customer.agent_number}`,
                    'user',
                    token,
                    'GET',
                )

                const pdfBytes = await createContractPDF(newContractData, customer, agent, token)
                await fileManager.uploadFile(pdfBytes, new_contract_number)

                // create entry in strapi
                await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/attachments`,
                    'attachment',
                    token,
                    'POST',
                    JSON.stringify({
                        data: {
                            'contract_number': new_contract_number,
                            'bucket_path': `${new_contract_number}`,
                            'type': "contract"
                        }
                    })
                )
            }

            if (newContractData.contract_status.create_invoice) {
                const agentData = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${customer.agent_number != null ? customer.agent_number : authUser['agent_number']}`,
                    'user',
                    token,
                    'GET',
                )
                const calculationParameters = await getCalculationParameters(token, newContractData.insurance_start_date)

                const positions = invoiceManager.generatePositionsFirstInvoice(newContractData, calculationParameters);

                const invoiceStatus: InvoiceDetailStatusData = {
                    send_date: '',
                    overdue_date: '',
                    dunning_date: '',
                    partially_paid_date: '',
                    partially_payment_amount: 0,
                    payment_date: '',
                    refund_date: '',
                    cancel_date: '',
                    fail_date: '',
                    proccessing_date: ''
                }

                // create invoice entry in strapi
                const invoiceData: InvoiceData = {
                    documentId: '',
                    type: InvoiceType.FRIST_INVOICE,
                    contract_number: newContractData.contract_number,
                    version_number: 0,
                    invoice_detail_status: invoiceStatus,
                    invoice_status: InvoiceStatusType.BOOKED,
                    agent_status: InvoiceAgentStatusType.OPEN,
                    insurance_status: InvoiceInsuranceStatusType.OPEN,
                    customer_status: InvoiceCustomerStatusType.OPEN,
                    invoice_number: '',
                    due_date: newContractData.insurance_start_date,
                    total_net: newContractData.first_invoice_net!,
                    total_gross: newContractData.first_invoice_gross!,
                    positions: positions,
                    billing_street: customer.street,
                    billing_house_number: customer.house_number,
                    billing_city: customer.city,
                    billing_postal_code: customer.postal_code,
                    billing_care_of: customer.care_of,
                    first_name: customer.first_name,
                    last_name: customer.last_name,
                    name_prefix: customer.name_prefix,
                    payment_mode: newContractData.payment_mode,
                    insurance_start_date: newContractData.insurance_start_date,
                    insurance_end_date: newContractData.insurance_end_date,
                    iban: newContractData.iban,
                    bic: newContractData.bic,
                    agent_street: agentData.street,
                    agent_house_number: agentData.house_number,
                    agent_city: agentData.city,
                    agent_postal_code: agentData.postal_code,
                    agent_company_name: agentData.company_name,
                    agent_number: agentData.agent_number,
                    customer_number: customer.customer_number,
                    subject: '',
                    updatedAt: '',
                    createdAt: '',
                    automatically_generated: true,
                    agency_number: customer.agency_number
                }

                const newInvoiceData = await invoiceManager.createEntry(token, invoiceData)

                const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData)
                await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number)
            }

            // Send email
            // only send email if create_police and send_email is true
            if (newContractData.contract_status.create_police && newContractData.contract_status.send_email) {
                let agentEmail: string = ""
                if (isAdmin) {
                    const agent = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq]=${customer.agent_number}`,
                        'user',
                        token
                    )
                    agentEmail = agent['email']
                }

                if (is_offer) {
                    await mailManager.sendOffer(customer, newContractData, isAdmin ? agentEmail : authUser["email"])
                } else {
                    await mailManager.sendPolice(customer, newContractData, isAdmin ? agentEmail : authUser["email"])
                }
            }

            res.status(201).json(newContractData);
        } catch (error) {
            console.error('Error creating contract or offer:', error);
            res.status(500).json({ error: 'Failed to create contract or offer' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

