// src/pages/api/contracts/[id]/update.ts
import { ContractData, ContractStatusData } from '@/types';
import { calculateContract, calculateContractWithInvoiceAmount, calculateContractWithPremie, calculateContractWithPremieAndInvoiceAmount } from '@/utils/contractCalculator';
import { dbWriter } from '@/utils/dbWriter';
import { fileManager } from '@/utils/fileManager';
import { mailManager } from '@/utils/mailManager';
import { createContractPDF, createInvoicePDF } from '@/utils/pdfManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const authUser = await permissionManager.getAuthUser(token)
    const isAdmin = permissionManager.isUserAdmin(authUser)
    const { formData, customer } = req.body

    if (req.method === 'PUT') {
        const {
            documentId,
            contract_number,
            premie,
            premie_household_tech,
            premie_pv_system,
            premie_glass_insurance,
            invoice_amount
        } = formData;

        let contractData = await calculateContract(formData, token)
        // overwrite premien if set by admin
        let temp_premie = premie
        if (isAdmin) {
            if (premie_household_tech != null) {
                contractData.premie_household_tech = premie_household_tech
            }
            if (premie_pv_system != null) {
                contractData.premie_pv_system = premie_pv_system
            }
            if (premie_glass_insurance != null) {
                contractData.premie_glass_insurance = premie_glass_insurance
            }
        }

        if (premie_household_tech || premie_pv_system || premie_glass_insurance) {
            temp_premie = 0
        }

        if (premie_household_tech) {
            temp_premie += premie_household_tech
        }

        if (premie_pv_system) {
            temp_premie += premie_pv_system
        }

        if (premie_household_tech || premie_pv_system || premie_glass_insurance || premie != null || invoice_amount) {
            contractData.is_individually_calculated = true
        }

        if ((premie != null && invoice_amount == null && isAdmin)) {
            contractData = await calculateContractWithPremie(contractData, token, temp_premie)
        }

        if ((premie == null && invoice_amount != null && isAdmin)) {
            contractData.invoice_amount = invoice_amount
            contractData = await calculateContractWithInvoiceAmount(contractData, token)
        }

        if ((premie != null && invoice_amount != null && isAdmin)) {
            contractData.premie = premie
            contractData.invoice_amount = invoice_amount
            contractData = await calculateContractWithPremieAndInvoiceAmount(formData, token)
        }

        const hasPermissions = await permissionManager.hasUserContractPermissions(authUser, contract_number, token)
        if (!hasPermissions) {
            res.status(401).end('Unaurtharized')
            return
        }

        try {

            const newContract: ContractData = await dbWriter.updateDocument(token, "contracts", documentId, contractData, "contract", 'contract_number');

            // Create and Upload PDF
            // only create pdf if create_police is true
            if (newContract.contract_status.create_police) {

                const agent = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${customer.agent_number}`,
                    'user',
                    token,
                    'GET',
                )

                const pdfBytes = await createContractPDF(newContract, customer, agent, token)
                await fileManager.uploadFile(pdfBytes, contract_number)
            }

            // Send email
            //TODO Fix MailManager to allow sending emails after editing a contract.
            // only send email if create_police and send_email is true

            if (newContract.contract_status.create_police && newContract.contract_status.send_email) {
                if (newContract.is_offer == true) {
                    await mailManager.sendOffer(customer, newContract, authUser["email"])
                } else {
                    await mailManager.sendPolice(customer, newContract, authUser["email"])
                }
            }

            res.status(201).json(newContract);
            res.status(200).json({ message: 'Contract updated successfully' });
        } catch (error) {
            console.error('Error updating contract:', error);
            res.status(500).json({ error: 'Failed to update contract' });
        }
    } else {
        res.setHeader('Allow', ['PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}
