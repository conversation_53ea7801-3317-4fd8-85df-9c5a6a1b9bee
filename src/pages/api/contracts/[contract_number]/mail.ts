// src/pages/api/contracts/[contract_number]/mail.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { mailManager } from '@/utils/mailManager';
import { permissionManager } from '@/utils/permissionManager';
import { AgentData } from '@/types';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { contract_number } = req.query;
    const { customer, contract_status, documentId, type, invoice_number, agent_number } = req.body;

    if (!contract_number || typeof contract_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {
        const authUser = await permissionManager.getAuthUser(token)
        const isAdmin = permissionManager.isUserAdmin(authUser)

        let strapiResponse: any | undefined

        if (authUser && !isAdmin)
            strapiResponse = await fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
                'contract',
                token
            );
        else if (authUser && isAdmin)
            strapiResponse = await fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}`,
                'contract',
                token
            );

        if (!strapiResponse) {
            return res.status(404).json({ error: 'Contract not found' });
        }

        // Fetch customer data
        const customerNumber = strapiResponse.customer_number;
        if (!customerNumber) {
            throw new Error('Customer number is missing in contract data.');
        }

        // Fetch agent
        const agent: AgentData = await fetchAgentFromAPI(
            agent_number,
            token
        );

        // sendEmail
        if (type == 'offer') {
            await mailManager.sendOffer(customer, strapiResponse, agent.email)
        } else if (type == 'police') {
            await mailManager.sendPolice(customer, strapiResponse, agent.email)
        } else if (type == 'invoice') {
            await mailManager.sendInvoice(customer, strapiResponse, invoice_number, agent.email)
        }

        // Update contract status
        // TODO: change status for invoice in attachments
        if (type == 'offer' || type == 'police') {
            contract_status[`send_email`] = true
            const strapiStatusResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts/${documentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    data: {
                        contract_status: contract_status
                    },
                }),
            });
            if (!strapiStatusResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to set status: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }
        }

        res.status(201).json({error: 'Error sending Mail'})
    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

async function fetchAgentFromAPI(agent_number: string, token: string) {
    try {
        const response = await fetch(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${agent_number}`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`Agent Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch Agent: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data[0] || null;
    } catch (error) {
        console.error(`Error fetching Agent data:`, error);
        throw error;
    }
}

