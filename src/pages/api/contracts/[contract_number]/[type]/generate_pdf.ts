// src/pages/api/contracts/[contract_number]/[type]/generate_pdf.ts
import { CalculationParametersData, ContractData } from '@/types';
import { InvoiceDetailStatusData, InvoiceStatusType } from "@/utils/invoice/types";
import { calculatePrice, getTax } from '@/utils/calculationUtil';
import { fileManager } from '@/utils/fileManager';
import { formatLabel } from '@/utils/keyFormatter';
import { createContractPDF, createInvoicePDF } from '@/utils/pdfManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const authUser = await permissionManager.getAuthUser(token)
    const isAdmin = permissionManager.isUserAdmin(authUser)
    const { contract_status, documentId, customer, due_date } = req.body
    const { type } = req.query;

    if (!isAdmin) {
        res.status(401).end(`Unauthorized`);
    }

    if (req.method === 'POST') {
        try {
            // Update contract status
            contract_status[`create_${type}`] = true
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts/${documentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    data: {
                        contract_status: contract_status
                    },
                }),
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to create contract: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }

            const responseData = await strapiResponse.json();
            const contractData: ContractData = responseData.data; // strapi returns new created entry


            if (type == "police") {
                // Create and Upload Plocie PDF
                // only create pdf if create_police is true
                const agent = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${customer.agent_number != null ? customer.agent_number : authUser['agent_number']}`,
                    'user',
                    token,
                    'GET',
                ))?.[0]

                const pdfBytes = await createContractPDF(contractData, customer, agent, token)
                await fileManager.uploadFile(pdfBytes, contractData.contract_number)
                if (!(await hasAttachment(token, contractData.contract_number, "contract"))) {
                    // create entry in strapi
                    await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/attachments`,
                        'attachment',
                        token,
                        'POST',
                        JSON.stringify({
                            data: {
                                'contract_number': contractData.contract_number,
                                'bucket_path': `${contractData.contract_number}`,
                                'type': "contract"
                            }
                        })
                    )
                }
            } else {
                res.status(501)
            }

            res.status(201).json(contractData);
        } catch (error) {
            console.error('Error creating PDF:', error);
            res.status(500).json({ error: 'Failed to create PDF' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

export async function getCalculationParameters(token: string, startDate: string) {
    const data = await fetch(`${process.env.STRAPI_BASE_URL}/calculation-parameters`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    })
    if (!data.ok) {
        throw new Error("CALCULATION_PARAMETERS not found")
    }
    const dataJson = await data.json()
    const calculationParameterList: CalculationParametersData[] = dataJson['data']

    const final_date = findClosestPastDate(calculationParameterList, startDate)

    return calculationParameterList.filter((entry) => entry.valid_from == final_date)[0]
}

function findClosestPastDate(dates: CalculationParametersData[], referenceDate: string) {
    const refDate = new Date(referenceDate);
    // Convert dates to date-objects and only keep ealier dates
    const pastDates = dates
        .map(date => new Date(date.valid_from))
        .filter(date => date < refDate);
    if (pastDates.length === 0) return null;
    // Return the maxiumum date before the reference date
    return pastDates.reduce((closest, date) =>
        date > closest ? date : closest
    ).toISOString().split('T')[0]; // date format YYYY-MM-DD
}

async function hasAttachment(token: string, contract_number: string, type: string) {
    const attachments = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/attachments?filters[contract_number][$eq][0]=${contract_number}&filters[type][$eq][1]=${type}`,
        'attachment',
        token,
        'GET'
    )
    return attachments['meta']['pagination']['total'] > 0;
}

async function hasInvoice(token: string, bucket_path: string) {
    const attachments = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoices?filters[bucket_path][$eq][0]=${bucket_path}`,
        'attachment',
        token,
        'GET'
    )
    if (attachments['meta']['pagination']['total'] > 0)
        return attachments['data'][0].documentId;
    else return null
}
