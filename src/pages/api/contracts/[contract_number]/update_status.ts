// src/pages/api/contracts/[id]/update_status.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const authUser = await permissionManager.getAuthUser(token)
    const isAdmin = permissionManager.isUserAdmin(authUser)
    const { active_status, documentId } = req.body

    if (req.method === 'PUT') {

        if (!isAdmin) {
            res.status(401).end('Unaurtharized')
            return
        }

        try {
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts/${documentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
                body: JSON.stringify({
                    data: {
                        active_status: active_status
                    },
                }),
            });

            if (!strapiResponse.ok) {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to update ducument: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }

            res.status(200).json({ message: 'Status updated successfully' });
        } catch (error) {
            console.error('Error updating contract:', error);
            res.status(500).json({ error: 'Failed to update contract' });
        }
    } else {
        res.setHeader('Allow', ['PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}
