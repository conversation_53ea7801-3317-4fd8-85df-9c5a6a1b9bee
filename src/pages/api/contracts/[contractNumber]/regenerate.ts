import {
    type CalculationParameter,
    type Customer,
} from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { ContractService } from '@/server/contract/ContractService';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { type ContractStatusData } from '@/types';
import { fileManager } from '@/utils/fileManager';
import { createContractPDF } from '@/utils/pdfManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { contractNumber } = req.query;
        if (!contractNumber) return "Invalid query: 'contractNumber' is required";
    },

    execute: async ({ req, res, db }) => {
        const { contractNumber } = req.query as { contractNumber: string };

        // service
        const contractService = new ContractService(db);
        const agentService = new AgentService(db);
        const customerService = new CustomerService(db);
        const calculationParameterService = new CalculationParameterService(db);

        const contract = (await contractService.getContract({ contractNumber }))!;
        const customer: Customer = (await customerService.findOne({
            customerNumber: contract.customerNumber!,
        }))!;
        const agent = (await agentService.findOne({
            agentNumber: contract.agentNumber!,
        }))!;
        const calculationParameter: CalculationParameter =
            (await calculationParameterService.getCalculationParameters(
                contract.insuranceStartDate!
            ))!;

        const pdfBytes = await createContractPDF(
            contract,
            customer,
            agent,
            calculationParameter
        );
        await fileManager.uploadFile(pdfBytes, contract.contractNumber);

        const contractStatus = contract.contractStatus as ContractStatusData;

        contractStatus.create_police = true;

        contractService.update(
            {
                documentId: contract.documentId!,
            },
            {
                contractStatus: contractStatus,
            }
        );

        res.status(201).end();
    },
};

export default createEndpoint({ GET });
