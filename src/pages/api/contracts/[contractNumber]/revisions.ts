// src/pages/api/agent/[agent_number]/revisions.ts

import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { getRevisions } from '@/utils/mongoDB/mongoDBFunctions';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { contractNumber } = req.query;
        if (!contractNumber || Array.isArray(contractNumber)) {
            return 'Invalid or missing contract number';
        }
    },

    execute: async ({ req, res }) => {
        const contractNumber = String(req.query.contractNumber);

        // Fetch revisions from Mongo
        const revisions = await getRevisions('contract', contractNumber);

        // Return 200 with array (empty array if none)
        return res.status(200).json(revisions);
    },
};

export default createEndpoint({ GET });
