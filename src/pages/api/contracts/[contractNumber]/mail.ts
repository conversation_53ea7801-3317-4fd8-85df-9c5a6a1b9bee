// src/pages/api/contracts/[contractNumber]/mail.ts
import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { type ContractStatusData } from '@/types';
import { mailManager } from '@/utils/mailManager';

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { contractNumber } = req.query;
        if (!contractNumber) return 'Invalid query';
    },

    execute: async ({ req, res, db }) => {
        const { contractNumber } = req.query as { contractNumber: string };

        // service
        const contractService = new ContractService(db);

        const contract = await contractService.getContractOrThrow({
            where: { contractNumber },
            include: { customer: true, agent: { select: { email: true } } },
        });

        if (contract.isOffer) {
            await mailManager.sendOffer(
                contract.customer!,
                contract,
                contract.agent!.email
            );
        } else {
            await mailManager.sendPolice(
                contract.customer!,
                contract,
                contract.agent!.email
            );
        }

        const contractStatus = contract.contractStatus as ContractStatusData;
        contractStatus.send_email = true;

        contractService.update(
            { documentId: contract.documentId },
            { contractStatus: contractStatus }
        );

        res.status(201).end();
    },
};

export default createEndpoint({ PUT });
