// src/pages/api/contracts/[contractNumber]/updateStatus.ts
import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { activeStatus, documentId } = req.body;
        if (!activeStatus || !documentId) return 'Invalid body';
    },

    execute: async ({ req, res, db }) => {
        const { activeStatus, documentId } = req.body;

        // service
        const contractService = new ContractService(db);

        contractService.update(
            { documentId: documentId },
            { activeStatus: activeStatus }
        );

        res.status(201).end();
    },
};

export default createEndpoint({ PUT });
