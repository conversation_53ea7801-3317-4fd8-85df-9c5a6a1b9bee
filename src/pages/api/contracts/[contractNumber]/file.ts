import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { fileManager } from '@/utils/fileManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { contractNumber } = req.query;
        if (
            !contractNumber ||
            (Array.isArray(contractNumber) && contractNumber.length !== 1)
        ) {
            return 'Invalid or missing contract number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const contractNumber = Array.isArray(req.query.contractNumber)
            ? req.query.contractNumber[0]
            : String(req.query.contractNumber);

        // service
        const service = new ContractService(db);

        const contract = await service.getContract({
            contractNumber: contractNumber,
        });

        if (!contract) {
            res.status(404).json({ message: 'Invoice not found' });
            return;
        }

        const pdf_stream = await fileManager.downloadFile(contract.contractNumber);

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="${contract.contractNumber}"`
        );

        // Pipe the S3 stream to the response
        pdf_stream.on('error', (err: any) => {
            console.error('Error fetching File from S3:', err);
            res.status(500).json({ error: 'Failed to fetch File' });
        });

        pdf_stream.pipe(res);
    },
};

export default createEndpoint({ GET });
