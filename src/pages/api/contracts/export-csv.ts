// src/pages/api/contracts/export-csv.ts
import { Parser } from '@json2csv/plainjs';

import { contractFieldConfig } from '@/components/filters/contractFieldConfig';
import { type Contract, Prisma } from '@/generated/prisma-postgres';
import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
        if (req.method !== 'GET') return `Method ${req.method} Not Allowed`;
    },

    execute: async ({ req, res, db, auth }) => {
        const isAdmin = auth.roles.includes('asevo-admin');
        try {
            // --- Sorting -----------------------------------------------------------
            const sortField = String(req.query.sortField ?? 'contract_number');
            const sortDirection =
                String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                    ? 'desc'
                    : 'asc';
            const orderBy = { [sortField]: sortDirection } as const;

            // --- Filters (new logic) ----------------------------------------------
            const baseWhere = parseQuery(req.query, {
                rootModel: Prisma.ModelName.Contract,
                fields: contractFieldConfig,
            });

            // Non-admins see only their agency’s contracts (as in old route)
            const where = !isAdmin
                ? {
                    AND: [
                        baseWhere,
                        { agency_number: String(auth.agencyNumber) },
                    ],
                }
                : baseWhere;

            // --- Fetch all rows in batches ----------------------------------------
            const batchSize =
                Math.max(1, Number((req.query.batchSize as string) ?? 1000)) || 1000;

            const service = new ContractService(db as any);

            const first = await service.list({
                where,
                orderBy,
                skip: 0,
                take: batchSize,
            });

            const allItems = [...first.items];
            let fetched = first.items.length;
            const total = first.total;

            while (fetched < total) {
                const next = await service.list({
                    where,
                    orderBy,
                    skip: fetched,
                    take: batchSize,
                });
                allItems.push(...next.items);
                fetched += next.items.length;
            }

            // --- CSV mapping (same columns as before) ------------------------------
            const csvData = allItems.map((item: Contract) => ({
                Kundennummer: item.customerNumber ?? '',
                Vertragsnummer: item.contractNumber ?? '',
                Vertragstyp: item.contractType ?? '',
                Versicherungsbeginn: item.insuranceStartDate ?? '',
                Versicherungsablauf: item.insuranceEndDate ?? '',
                Hauptfälligkeit: item.insuranceMainDueDate ?? '',
                Zahlungsweise: item.paymentMode ?? '',
                IBAN: item.iban ?? '',
                BIC: item.bic ?? '',
                Vorversicherer: item.previousInsurance ?? '',
                Nr_Vorversicherer: item.previousInsuranceNumber ?? '',
                Vorschäden: item.previousClaims ?? '',
                Angebot: item.isOffer ?? '',
                Zusatzvereinbarungen: item.additionalAgreements ?? '',
                Anteile: item.shareData ?? '',
                Agenturnummer: item.agencyNumber ?? '',
                Courtage: item.commission ?? '',
                Versicherungsstatus: item.contractStatus ?? '',
                Ursprungsangebot: item.fromOffer ?? '',
                Zielvertrag: item.toContract ?? '',
                Aktualisierungsdatum: item.updatedAt ?? '',
                Maklernnummer: item.agentNumber ?? '',
                Individuelle_Berechnung: item.isIndividuallyCalculated ?? '',
            }));

            const parser = new Parser({});
            const csv = parser.parse(csvData);

            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=contracts.csv'
            );
            res.status(200).send(csv);
        } catch (error) {
            console.error('CSV Export Error:', error);
            if (req.method !== 'GET') {
                res.setHeader('Allow', ['GET']);
                res.status(405).end(`Method ${req.method} Not Allowed`);
                return;
            }
            res.status(500).json({ error: 'Failed to export contracts to CSV' });
        }
    },
};

export default createEndpoint({ GET });
