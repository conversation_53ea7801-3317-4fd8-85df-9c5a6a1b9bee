// src/pages/api/contracts/export-csv.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { Parser } from '@json2csv/plainjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization.split(" ")[1];

    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
        return;
    }

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        const { filters = '{}', sortField = 'contract_number', sortDirection = 'asc' } = req.query;
        const parsedFilters = JSON.parse(filters as string);
        const limit = 100;

        const filterParams = new URLSearchParams();
        if (authUser && !isAdmin) {
            filterParams.append('filters[agency_number][$eq]', authUser['agency_number']);
        }
        Object.entries(parsedFilters).forEach(([field, value]) => {
            filterParams.append(`filters[${field}][$contains]`, value as string);
        });
        const sort = `${sortField}:${sortDirection}`;
        filterParams.append('sort', sort);

        const contracts: any[] = [];
        let offset = 0;
        let total = 0;


        const firstUrl = `${process.env.STRAPI_BASE_URL}/contracts?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
        const firstRes = await fetch(firstUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!firstRes.ok) {
            const errorDetails = await firstRes.json();
            throw new Error(errorDetails?.error?.message || firstRes.statusText);
        }

        const firstData = await firstRes.json();
        contracts.push(...firstData.data);
        total = firstData.meta.pagination.total;


        while (contracts.length < total) {
            offset += limit;
            const pageUrl = `${process.env.STRAPI_BASE_URL}/contracts?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
            const pageRes = await fetch(pageUrl, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!pageRes.ok) break;
            const pageData = await pageRes.json();
            contracts.push(...pageData.data);
        }

        //TODO more fields needs to be added to map
        const csvData = contracts.map((item) => ({
            Kundennummer: item.customer_number,
            Vertragsnummer: item.contract_number,
            Vertragstyp: item.contract_type,
            Versicherungsbeginn: item.insurance_start_date,
            Versicherungsablauf: item.insurance_end_date,
            Hauptfälligkeit: item.insurance_main_due_date,
            Zahlungsweise: item.payment_mode,
            IBAN: item.iban,
            BIC: item.bic,
            Vorversicherer: item.previous_insurance,
            Nr_Vorversicherer: item.previous_insurance_number,
            Vorschäden: item.previous_claims,
            Angebot: item.is_offer,
            Zusatzvereinbarungen: item.additional_agreements,
            Anteile: item.share_data,
            Agenturnummer: item.agency_number,
            Courtage: item.commission,
            Versicherungsstatus: item.contract_status,
            Ursprungsangebot: item.from_offer,
            Zielvertrag: item.to_contract,
            Aktualisierungsdatum: item.updatedAt,
            Maklernnummer: item.agent_number,
            Individuelle_Berechnung: item.is_individually_calculated,
        }));

        const opts = {};
        const parser = new Parser(opts);
        const csv = parser.parse(csvData);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=contracts.csv');
        res.status(200).send(csv);

    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).json({ error: 'Failed to export contracts to CSV' });
    }
}
