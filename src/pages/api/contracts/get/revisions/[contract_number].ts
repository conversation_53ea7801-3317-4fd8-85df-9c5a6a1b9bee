// src/pages/api/contracts/get/revisions/[contract_number].ts
import { getRevisions } from "@/utils/mongoDB/mongoDBFunctions";
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { contract_number } = req.query;

    if (!contract_number || typeof contract_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const hasPermission = permissionManager.hasContractPermissions(contract_number, token)
        if (!hasPermission) {
            res.status(401)
        }

        const revs = await getRevisions(contract_number, 'contract_revision')

        res.status(201).json(revs)

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}