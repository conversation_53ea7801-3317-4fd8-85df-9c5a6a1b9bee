// src/pages/api/contracts/clientId/[clientId].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const { customer_number } = req.query;

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'GET') {

        try {

            const authUser = await permissionManager.getAuthUser(token)
            const isAdmin = permissionManager.isUserAdmin(authUser)

            let strapiResponse: any | undefined

            if (authUser && !isAdmin)
                strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts?filters[customer_number][$eq][0]=${customer_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}&pagination[limit]=100`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });
            else if (authUser && isAdmin)
                strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/contracts?filters[customer_number][$eq][0]=${customer_number}&pagination[limit]=100`, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                });

            if (strapiResponse && strapiResponse.ok) {
                const data = await strapiResponse.json();
                res.status(200).json(data.data); // return all contracts data from strapi
            } else {
                const errorDetails = await strapiResponse.json();
                throw new Error(`Failed to fetch contracts: ${errorDetails.error?.message || strapiResponse.statusText}`);
            }
        } catch (error) {
            console.error('Error fetching contracts:', error);
            res.status(500).json({ error: 'Failed to fetch contracts' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
