// src/pages/api/contracts/report.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { mailManager } from '@/utils/mailManager';
import { generateContractNumber } from '@/utils/numberGenerator';
import { permissionManager } from '@/utils/permissionManager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return
    }

    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const authUser = await permissionManager.getAuthUser(token)

    if (authUser == undefined) {
        res.status(401).json({ error: 'Unauthorized' });
        return
    }

    const { text, customer_data, contract_type } = req.body;
    const { salutation, first_name, last_name, care_of, street, house_number, postal_code, city, email, customer_number } = customer_data;
    const input = text + contract_type + salutation + first_name + last_name + care_of + street + house_number + postal_code + city + email + customer_number

    const contract_number = generateContractNumber(contract_type, input)

    // Validating input fields
    if (!text || !customer_data) {
        return res.status(400).json({ error: 'All fields are required: text and customer_data' });
    }

    try {
        // Send damage report email
        await mailManager.sendSpecial({
            subject: `Anfrage Unfallversicherung ${contract_number}`,
            contract_number,
            customer_data,
            text
        });

        res.status(200).json({ message: 'Email successfully send.'});
    } catch (error) {
        console.error('Error in report API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing your report.' });
    }
}
