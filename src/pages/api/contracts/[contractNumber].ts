// src/pages/api/contracts/[contract_number].ts
// src/pages/api/agent/[contractNumber].ts
import {
    type Agent,
    type CalculationParameter,
    type Contract,
    type Customer,
} from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { ContractService } from '@/server/contract/ContractService';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { type ContractStatusData } from '@/types';
import {
    calculateContract,
    calculateContractWithInvoiceAmount,
    calculateContractWithPremie,
    calculateContractWithPremieAndInvoiceAmount,
} from '@/utils/contractCalculator';
import { fileManager } from '@/utils/fileManager';
import { mailManager } from '@/utils/mailManager';
import { createContractPDF } from '@/utils/pdfManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { contractNumber } = req.query;
        if (
            !contractNumber ||
            (Array.isArray(contractNumber) && contractNumber.length !== 1)
        ) {
            return 'Invalid or missing contract number';
        }
    },

    execute: async ({ req, res, db }) => {

        // param
        const contractNumber = Array.isArray(req.query.contractNumber)
            ? req.query.contractNumber[0]
            : String(req.query.contractNumber);

        // service
        const service = new ContractService(db);

        const contract = await service.getContract({ contractNumber: contractNumber });

        if (!contract) {
            res.status(404).json({ message: 'Contract not found' });
            return;
        }

        res.status(200).json(contract);
    },
};

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const contract = req.body.contract as Contract;
        if (!contract) return "Invalid body: 'contract' is required";
        if (!contract.documentId)
            return "Invalid body: 'contract.documentId' is required";
    },

    execute: async ({ req, res, db, auth }) => {
        const contract = req.body.contract as Contract;
        const isAdmin = auth.roles.includes('asevo-admin');

        const {
            premie,
            premieHouseholdTech,
            premiePvSystem,
            premieGlassInsurance,
            invoiceAmount,
        } = contract;

        const calculationParameterService = new CalculationParameterService(db);
        let contractData: Contract = await calculateContract(
            contract,
            calculationParameterService
        );
        // overwrite premien if set by admin
        let temp_premie = premie || 0;
        if (isAdmin) {
            if (premieHouseholdTech != null) {
                contractData.premieHouseholdTech = premieHouseholdTech;
            }
            if (premiePvSystem != null) {
                contractData.premiePvSystem = premiePvSystem;
            }
            if (premieGlassInsurance != null) {
                contractData.premieGlassInsurance = premieGlassInsurance;
            }
        }

        if (premieHouseholdTech || premiePvSystem || premieGlassInsurance) {
            temp_premie = 0;
        }

        if (premieHouseholdTech) {
            temp_premie += premieHouseholdTech;
        }

        if (premiePvSystem) {
            temp_premie += premiePvSystem;
        }

        if (
            premieHouseholdTech ||
            premiePvSystem ||
            premieGlassInsurance ||
            premie != null ||
            invoiceAmount
        ) {
            contractData.isIndividuallyCalculated = true;
        }

        if (premie != null && invoiceAmount == null && isAdmin) {
            contractData = await calculateContractWithPremie(
                contractData,
                calculationParameterService,
                temp_premie
            );
        }

        if (premie == null && invoiceAmount != null && isAdmin) {
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithInvoiceAmount(
                contractData,
                calculationParameterService
            );
        }

        if (premie != null && invoiceAmount != null && isAdmin) {
            contractData.premie = premie;
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithPremieAndInvoiceAmount(
                contract,
                calculationParameterService
            );
        }

        // service
        const service = new ContractService(db);

        const updated = await service.update(
            { documentId: req.body.contract.documentId },
            contractData as any
        );

        if (!updated) {
            res.status(404).json({ message: 'Contract not found' });
            return;
        }

        const agentService = new AgentService(db);
        const customerService = new CustomerService(db);

        const customer: Customer = (await customerService.findOne({
            customerNumber: updated.customerNumber!,
        }))!;

        const agent: Agent = (await agentService.findOne({
            agentNumber: auth.agentNumber,
        }))!;

        // Create and Upload PDF
        // only create pdf if create_police is true
        if (
            (updated.contractStatus as unknown as ContractStatusData).create_police
        ) {
            const calculationParameter: CalculationParameter =
                (await calculationParameterService.getCalculationParameters(
                    updated.insuranceStartDate!
                ))!;

            const pdfBytes = await createContractPDF(
                updated,
                customer,
                agent,
                calculationParameter
            );
            await fileManager.uploadFile(pdfBytes, updated.contractNumber);
        }

        if (
            (updated.contractStatus! as unknown as ContractStatusData)
                .create_police &&
            (updated.contractStatus! as unknown as ContractStatusData).send_email
        ) {
            if (updated.isOffer == true) {
                await mailManager.sendOffer(customer, updated, agent.email);
            } else {
                await mailManager.sendPolice(customer, updated, agent.email);
            }
        }

        res.status(200).json(updated);
    },
};

export default createEndpoint({ GET, PUT });
