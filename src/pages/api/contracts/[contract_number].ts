// src/pages/api/contracts/[contract_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {ContractsRepository} from "@/server/ContractsRepository";
import {HttpClient} from "@/utils/HttpClient";
import {NotFoundError} from "@/server/errors";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    const { contract_number } = req.query;

    if (!contract_number || typeof contract_number !== 'string') {
        res.status(400).json({ error: 'Invalid or missing contract number' });
        return;
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const httpClient = new HttpClient(token)

            const contractsRepository = new ContractsRepository(httpClient, authUser);
            const contract = await contractsRepository.findByContractNumber(contract_number);

            res.status(200).json(contract);
        } catch (error) {
            console.error(error);

            if (error instanceof NotFoundError) {
                res.status(404).json({ message: 'contract not found' });
                return;
            }
            res.status(500).json({ error: 'Failed to fetch contract' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
