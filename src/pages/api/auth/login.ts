// src/pages/api/auth/login.ts
import { type NextApiRequest, type NextApiResponse } from 'next';

export default async function login(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { identifier, password } = req.body;

  if (!identifier || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }

  try {
    // log of request url and body
    const strapiUrl = `${process.env.STRAPI_BASE_URL}/auth/local`;
    console.log('Sending request to Strapi:', strapiUrl);

    const response = await fetch(strapiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ identifier, password }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Strapi error:', errorText);
      return res.status(response.status).json({ message: errorText });
    }

    const data = await response.json();

    // login acepted and return jwt and user data
    res.status(200).json(data);
  } catch (error) {
    console.error('Error during Strapi request:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
