import {createEndpoint} from "@/server/infra/createEndpoint";
import {Endpoint<PERSON>and<PERSON>} from "@/server/domain/endpoints";
import {AttachmentsRepository} from "@/server/repositories/AttachmentsRepository";
import {notFound} from "@/server/responses";
import {DefaultFileStorage} from "@/server/infra/DefaultFileStorage";

type Query = {
    attachmentId: string;
}

const GET: EndpointHandler = {
    validate: ({ req }) => {
        const { attachmentId } = req.query;

        if (!attachmentId || typeof attachmentId !== 'string') {
            return 'Invalid or missing attachmentId in path'
        }
    },

    execute: async ({ req, res, logger }) => {
        const { attachmentId } = req.query as Query

        const attachmentsRepository = new AttachmentsRepository()
        const fileStorage = new DefaultFileStorage()

        const attachment = await attachmentsRepository.findById(attachmentId)
        if (!attachment) {
            return notFound(res)
        }

        const readStream = fileStorage.download(attachment.fileId)

         // TODO: add headers, return result, extract this into a Service
    }
}

export default createEndpoint({ GET })
