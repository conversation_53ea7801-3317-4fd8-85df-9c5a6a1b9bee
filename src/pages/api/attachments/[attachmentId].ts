import {createEndpoint} from "@/server/createEndpoint";
import {EndpointHandler} from "@/server/domain/endpoints";
import {AttachmentsService} from "@/server/services/AttachmentsService";

type Query = {
    attachmentId: string;
}

const GET: EndpointHandler = {
    validate: ({ req }) => {
        const { attachmentId } = req.query;

        if (!attachmentId || typeof attachmentId !== 'string') {
            return 'Invalid or missing attachmentId in path'
        }
    },
    execute: async ({ req, res }) => {
        const { attachmentId } = req.query as Query

        const service = new AttachmentsService()
        const { attachmentName, downloadStream } = await service.download(attachmentId)

        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename=${attachmentName}`);

        downloadStream.pipe(res)
    }
}

export default createEndpoint({ GET })
