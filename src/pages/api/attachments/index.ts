import {createEndpoint} from "@/server/createEndpoint";
import {End<PERSON><PERSON><PERSON><PERSON>} from "@/server/domain/endpoints";
import {AttachmentsRepository} from "@/server/repositories/AttachmentsRepository";
import {Attachment} from "@/server/domain/attachments";

const GET: EndpointHandler = {
    execute: async ({ res }) => {
        const attachmentsRepository = new AttachmentsRepository()
        const attachments = await attachmentsRepository.findAll()

        return res.status(200).json({ attachments })
    }
}

const POST: EndpointHandler = {
    execute: async ({ req, res }) => {
        const attachmentToCreate = req.body.attachment as Attachment

        const attachmentsRepository = new AttachmentsRepository()
        const createdAttachment = await attachmentsRepository.create(attachmentToCreate)

        return res.status(200).json({ attachment: createdAttachment })
    }
}

export default createEndpoint({ GET, POST })
