// src/pages/api/agent/export-csv.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { Parser } from '@json2csv/plainjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization!.split(" ")[1];

    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
        return;
    }

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        if (!isAdmin) {
            res.status(403).json({ error: 'Not authorized' });
            return;
        }

        const { filters = '{}', sortField = 'username', sortDirection = 'asc' } = req.query;
        const parsedFilters = JSON.parse(filters as string);

        const filterParams = new URLSearchParams();
        Object.entries(parsedFilters).forEach(([field, value]) => {
            filterParams.append(`filters[${field}][$contains]`, value as string);
        });

        const sort = `${sortField}:${sortDirection}`;
        filterParams.append('sort', sort);

        const strapiUrl = `${process.env.STRAPI_BASE_URL}/users?${filterParams.toString()}&pagination[limit]=1000&pagination[start]=0`;

        const strapiResponse = await fetch(strapiUrl, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!strapiResponse.ok) {
            const errorDetails = await strapiResponse.json();
            throw new Error(errorDetails?.error?.message || strapiResponse.statusText);
        }

        const data = await strapiResponse.json();
        const agents = data;

        const csvData = agents.map((item: any) => ({
            Benutzername: item.username,
            Maklernummer: item.agent_number,
            Agenturname: item.company_name,
            Agenturnummer: item.agency_number,
            Straße: item.street,
            Hausnummer: item.house_number,
            Postleitzahl: item.postal_code,
            Ort: item.city,
            Emailadresse: item.email,
            Website: item.url,
            Telefonnummer: item.telephone_number,
            Courtage: item.commission,
        }));

        const opts = {};
        const parser = new Parser(opts);
        const csv = parser.parse(csvData);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=agents.csv');
        res.status(200).send(csv);
    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).json({ error: 'Failed to export agents to CSV' });
    }
}
