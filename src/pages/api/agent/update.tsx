// src/pages/api/user/update.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { AgentData } from "@/types";
import { dbWriter } from '@/utils/dbWriter';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'PUT') {
        try {

            const { agentData } = req.body as { agentData: AgentData };

            if (!(await permissionManager.hasAgentPermissions(agentData.agent_number, token)) && await permissionManager.isAdmin(token)) {
                res.status(401)
            }

            if (!agentData || typeof agentData !== 'object') {
                res.status(400).json({ error: 'Invalid or missing request body' });
                return;
            }

            const newAgentData = await dbWriter.updateDocument(token, 'users', agentData.documentId, agentData, "agent", 'id');
            if (newAgentData) {
                res.status(200).json(newAgentData);
            } else {
                throw new Error(newAgentData);
            }
        } catch (error) {
            console.error('Error updating agent in Strapi:', error);
            res.status(500).json({ error: 'Failed to update agent' });
        }
    } else {
        res.setHeader('Allow', ['PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

