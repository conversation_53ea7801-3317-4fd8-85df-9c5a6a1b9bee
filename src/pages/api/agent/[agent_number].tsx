// src/pages/api/agent/[agent_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    
    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const { agent_number } = req.query;
    const token = req.headers.authorization!.split(" ")[1]

    if (!agent_number || typeof agent_number !== 'string') {
        res.status(400).json({ error: 'Invalid or missing agent number' });
        return;
    }

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const isAdmin = permissionManager.isUserAdmin(authUser)
            let strapiResponse: any | undefined

            if (authUser && !isAdmin) {
                strapiResponse = await fetch(
                    `${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${agent_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
                    {
                        method: 'GET',
                        headers: {
                            Authorization: `Bearer ${token}`
                        },
                    }
                );
            } else if (authUser && isAdmin) {
                strapiResponse = await fetch(
                    `${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${agent_number}`,
                    {
                        method: 'GET',
                        headers: {
                            Authorization: `Bearer ${token}`
                        },
                    }
                );
            }

            if (strapiResponse.ok) {
                const data = await strapiResponse.json();
                if (data.length === 0) {
                    res.status(404).json({ message: 'Agent not found' });
                    return;
                }
                res.status(200).json(data[0]);
            } else {
                const errorDetails = await strapiResponse.json();
                throw new Error(errorDetails.error?.message || strapiResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching agent from Strapi:', error);
            res.status(500).json({ error: 'Failed to fetch agent' });
        }
    }  else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
