import { permissionManager } from "@/utils/permissionManager";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).end();
        return;
    }
    const token = req.headers.authorization!.split(" ")[1];

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token);
            const isUserAdmin = permissionManager.isUserAdmin(authUser);

            const {
                limit = 10,
                offset = 0,
                filters = '{}',
                sortField = 'invoice_number',
                sortDirection = 'asc',
            } = req.query;

            // Safely parse filters
            let parsedFilters: Record<string, any> = {};
            try {
                parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : (filters as any);
                if (typeof parsedFilters !== 'object' || parsedFilters === null) {
                    parsedFilters = {};
                }
            } catch (e) {
                console.warn('Failed to parse filters, falling back to empty:', e);
                parsedFilters = {};
            }

            const filterParams = new URLSearchParams();

            // Add agent filter if not admin
            if (authUser && !isUserAdmin) {
                filterParams.append('filters[agent_number][$eq]', authUser['agent_number']);
            }

            // Fields that should be matched exactly (or treated as enums/numeric)
            const exactMatchFields = new Set([
                'invoice_status',
                'customer_status',
                'type',
                'agent_number',
                'agency_number',
            ]);

            // Add dynamic filters with appropriate operators
            Object.entries(parsedFilters).forEach(([field, value]) => {
                if (value === '' || value === null || value === undefined) return;

                // Arrays -> use $in
                if (Array.isArray(value)) {
                    const normalized = value.map(String).join(',');
                    filterParams.append(`filters[${field}][$in]`, normalized);
                } else if (exactMatchFields.has(field) || !isNaN(Number(value))) {
                    // Numeric or enum-ish fields -> exact match
                    filterParams.append(`filters[${field}][$eq]`, String(value));
                } else {
                    // Fallback to contains for free text
                    filterParams.append(`filters[${field}][$contains]`, String(value));
                }
            });

            // Add sorting
            const sort = `${sortField}:${sortDirection}`;
            filterParams.append('sort', sort);

            // Construct the Strapi URL
            const strapiUrl = `${process.env.STRAPI_BASE_URL}/invoices?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;

            const invoicesResponse = await fetch(strapiUrl, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!invoicesResponse.ok) {
                const errorDetails = await invoicesResponse.json().catch(() => ({}));
                console.error('Strapi Error:', errorDetails);
                throw new Error(
                    `Failed to fetch contracts: ${(errorDetails as any).error?.message || invoicesResponse.statusText
                    }`
                );
            }

            const data = await invoicesResponse.json();

            const totalItems = data.meta?.pagination?.total || 0;
            const pageCount = Math.ceil(Number(totalItems) / Number(limit));

            const meta = {
                total: totalItems,
                pageCount,
                pageSize: Number(limit),
                page: Math.floor(Number(offset) / Number(limit)) + 1,
            };

            res.status(200).json({
                contracts: data.data,
                meta,
            });
        } catch (error) {
            console.error('Error fetching contracts:', error);
            res.status(500).json({ error: 'Failed to fetch contracts' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
