// src/pages/api/contracts/[contract_number]/[type]/generate_pdf.ts
import { CalculationParametersData } from '@/types';
import { InvoiceData} from "@/utils/invoice/types";
import { fileManager } from '@/utils/fileManager';
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const { invoiceData } = req.body as {invoiceData: InvoiceData}

    if (req.method === 'POST') {
        try {
            const hasPermission = await permissionManager.hasContractPermissions(invoiceData.contract_number, token)
            if (!hasPermission) {
                res.status(401)
            }

            // prepare pdf
            const pdfBytes = await invoiceManager.generatePDFBytes(invoiceData)

            // upload File
            await fileManager.uploadFile(pdfBytes, invoiceData.invoice_number)

            res.status(201);

        } catch (error) {
            console.error('Error creating PDF:', error);
            res.status(500).json({ error: 'Failed to create PDF' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

export async function getCalculationParameters(token: string, startDate: string) {
    const data = await fetch(`${process.env.STRAPI_BASE_URL}/calculation-parameters`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    })
    if (!data.ok) {
        throw new Error("CALCULATION_PARAMETERS not found")
    }
    const dataJson = await data.json()
    const calculationParameterList: CalculationParametersData[] = dataJson['data']

    const final_date = findClosestPastDate(calculationParameterList, startDate)

    return calculationParameterList.filter((entry) => entry.valid_from == final_date)[0]
}

function findClosestPastDate(dates: CalculationParametersData[], referenceDate: string) {
    const refDate = new Date(referenceDate);
    // Convert dates to date-objects and only keep ealier dates
    const pastDates = dates
        .map(date => new Date(date.valid_from))
        .filter(date => date < refDate);
    if (pastDates.length === 0) return null;
    // Return the maxiumum date before the reference date
    return pastDates.reduce((closest, date) =>
        date > closest ? date : closest
    ).toISOString().split('T')[0]; // date format YYYY-MM-DD
}

