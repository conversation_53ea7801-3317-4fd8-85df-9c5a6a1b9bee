// src/pages/api/invoice/export-csv.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { Parser } from '@json2csv/plainjs';
import { formatInvoiceAgentStatus, formatInvoiceCustomerStatus, formatInvoiceInsuranceStatus, formatInvoiceStatus } from '@/utils/keyFormatter';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization.split(" ")[1];

    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
        return;
    }

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        const limit = 100;
        let offset = 0;
        const invoices: any[] = [];
        let total = 0;

        const { filters = '{}', sortField = 'invoice_number', sortDirection = 'asc' } = req.query;

        // Safely parse filters
        let parsedFilters: Record<string, any> = {};
        try {
            parsedFilters = typeof filters === 'string'
                ? JSON.parse(filters)
                : (filters as any);
            if (typeof parsedFilters !== 'object' || parsedFilters === null) {
                parsedFilters = {};
            }
        } catch {
            console.warn('Failed to parse filters, falling back to empty');
            parsedFilters = {};
        }

        const filterParams = new URLSearchParams();

        // Add agency filter if not admin
        if (authUser && !isAdmin) {
            filterParams.append(
                'filters[agency_number][$eq]',
                String(authUser['agency_number'])
            );
        }

        // Fields that require exact matching (enums or numeric)
        const exactMatchFields = new Set([
            'invoice_status',
            'customer_status',
            'type',
            'agent_number',
            'agency_number',
        ]);

        // Build dynamic filters just like in index.ts
        Object.entries(parsedFilters).forEach(([field, value]) => {
            // skip empty
            if (value === '' || value === null || value === undefined) return;

            // Array values → $in
            if (Array.isArray(value)) {
                const normalized = value.map(String).join(',');
                filterParams.append(`filters[${field}][$in]`, normalized);

                // exactMatchFields or pure numbers → $eq
            } else if (exactMatchFields.has(field) || !isNaN(Number(value))) {
                filterParams.append(`filters[${field}][$eq]`, String(value));

                // everything else → $contains
            } else {
                filterParams.append(
                    `filters[${field}][$contains]`,
                    String(value)
                );
            }
        });

        // Add sorting
        filterParams.append('sort', `${sortField}:${sortDirection}`);
        const firstUrl = `${process.env.STRAPI_BASE_URL}/invoices?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=0`;

        const firstRes = await fetch(firstUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!firstRes.ok) {
            const errorDetails = await firstRes.json();
            throw new Error(errorDetails?.error?.message || firstRes.statusText);
        }

        const firstData = await firstRes.json();
        invoices.push(...firstData.data);
        total = firstData.meta.pagination.total;


        while (invoices.length < total) {
            offset += limit;
            const pageUrl = `${process.env.STRAPI_BASE_URL}/invoices?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
            const pageRes = await fetch(pageUrl, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!pageRes.ok) break;
            const pageData = await pageRes.json();
            invoices.push(...pageData.data);
        }

        const csvData = invoices.map((item) => ({
            Rechnungsnummer: item.invoice_number,
            Rechnungsdatum: item.due_date,
            Rechnung_Netto: item.total_net,
            Rechnung_Brutto: item.total_gross,
            Positionen: item.positions,
            Rechnungsdetailstatus: item.invoice_detail_status,
            Rechnungsstatus: formatInvoiceStatus(item.invoice_status),
            Versionsnummer: item.version_number,
            Typ: item.type,
            Vertragsnummer: item.contract_number,
            Versicherungsbeginn: item.insurance_start_date,
            Versicherungsablauf: item.insurance_end_date,
            Versicherungsstatus: formatInvoiceInsuranceStatus(item.insurance_status),
            Zahlungsweise: item.payment_mode,
            IBAN: item.iban,
            BIC: item.bic,
            Kundennummer: item.customer_number,
            Kunde_Titel: item.name_prefix,
            Kunde_Vorname: item.first_name,
            Kunde_Nachname: item.last_name,
            Kunde_CO: item.billing_care_of,
            Kunde_Straße: item.billing_street,
            Kunde_Hausnummer: item.billing_house_number,
            Kunde_Postleitzahl: item.billing_postal_code,
            Kunde_Ort: item.billing_city,
            Kundenstatus: formatInvoiceCustomerStatus(item.customer_status),
            Agentur: item.agent_company_name,
            Maklernnummer: item.agent_number,
            Makler_Straße: item.agent_street,
            Makler_Hausnummer: item.agent_house_number,
            Makler_Postleitzahl: item.agent_postal_code,
            Makler_Ort: item.agent_city,
            Maklerstatus: formatInvoiceAgentStatus(item.agent_status),
            Betreff: item.subject,
            Aktualisiert: item.updatedAt,
            Erstellt: item.createdAt,
            Atomatisierte_Erstellung: item.automatically_generated,
        }));

        const opts = {};
        const parser = new Parser(opts);
        const csv = parser.parse(csvData);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=invoices.csv');
        res.status(200).send(csv);

    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).json({ error: 'Failed to export invoices to CSV' });
    }
}
