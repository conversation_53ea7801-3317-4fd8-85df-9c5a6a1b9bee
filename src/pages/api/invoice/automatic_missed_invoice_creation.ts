// // src/pages/api/invoice/automatic_missed_invoice_creation
// // execution time 1min 40sec on dev with only creating one invoice
// import { NextApiRequest, NextApiResponse } from 'next';
// import { strapi } from '@strapi/client';
// import { CalculationParametersData, ContractData } from '@/types';
// import { DateTime } from 'luxon';
// import { invoiceManager } from '@/utils/invoice/invoiceManager';
// import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceData, InvoiceDetailStatusData, InvoiceInsuranceStatusType, InvoiceStatusType, InvoiceType } from '@/utils/invoice/types';
// import { fileManager } from '@/utils/fileManager';
// import { toUTCDateString } from '@/utils/dateUtils';
// import pLimit from 'p-limit';

// const timeIntervals = {
//     "Einmalzahlung": 1,
//     "jährlich": 1,
//     "halbjährlich": 2,
//     "vierteljährlich": 4,
//     "monatlich": 12,
// } as const;

// const PAGE_SIZE = 100;                      // Strapi default max is 100
// const PARALLEL_CONTRACTS = 5;
// const limit = pLimit(PARALLEL_CONTRACTS);

// const client = strapi({
//     baseURL: process.env.STRAPI_BASE_URL,
//     auth: process.env.STRAPI_API_TOKEN,
// });

// const invoices = client.collection('invoices');
// const contracts = client.collection('contracts')

// export default async function handler(req: NextApiRequest, res: NextApiResponse) {

//     if (!req.headers.authorization) {
//         res.status(401)
//         return
//     }
//     const token = req.headers.authorization!.split(" ")[1]

//     if (token != process.env.CORONJOB_API_KEY){
//         res.status(401).end()
//         return
//     }

//     if (req.method === 'GET') {
//         try {

//             console.log('[InvoiceCreation] started');

//             // ──────────────────────────────────────────────────────────
//             // PAGINATION LOOP
//             // ──────────────────────────────────────────────────────────
//             let page = 1;
//             let pageCount = 1;
//             const currentDate = new Date();
//             const currentDateString = toUTCDateString(currentDate)
//             const calculationParameters = (await getCalculationParameters(process.env.STRAPI_API_TOKEN, currentDate.toDateString()))
//             do {
//                 const { data, meta } = await contracts.find({
//                     sort: 'id:asc',
//                     pagination: { page, pageSize: PAGE_SIZE },
//                 });

//                 // Process contracts (PARALLEL_CONTRACTS at a time)
//                 const contractJobs = data.map((c: any) =>
//                     limit(() =>
//                         processContract(c as ContractData, currentDateString, calculationParameters)
//                     )
//                 );

//                 const results = await Promise.allSettled(contractJobs);
//                 const failures = results.filter(r => r.status === 'rejected');
//                 if (failures.length) {
//                     console.error(`[InvoiceCreation] ${failures.length} contracts(s) failed.`);
//                     failures.forEach(f => console.error(f.reason));
//                 }

//                 pageCount = meta.pagination!.pageCount;

//                 page += 1;
//             } while (page <= pageCount);

//             console.log('[InvoiceCreation] finished');
//             res.status(200).end();
//         } catch (err) {
//             console.error('[InvoiceCreation] ERROR', err);
//             res.status(500).json({ error: 'Failed to create invoices' });
//         }
//     }

// }

// // ---------------------------------------------------------------------------
// // SINGLE CONTRACT WORKER
// // ---------------------------------------------------------------------------
// async function processContract(
//     contractData: ContractData,
//     currentDateISO: string,
//     calculationParameters: CalculationParametersData,
// ) {
//     // check if the contract actually has a payment dates
//     const paymentDates = getPaymentDates(
//         new Date(contractData.insurance_start_date),
//         new Date(),
//         contractData.payment_mode,
//     );
//     if (paymentDates.length === 0) return;

//     console.log('[InvoiceCreation] checking contract:', contractData.contract_number);

//     // prepare and create invoice
//     const agentData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${contractData.agent_number}`,
//         'agent',
//         process.env.STRAPI_API_TOKEN,
//         'GET',
//     ))[0]
//     const customerData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${contractData.customer_number}`,
//         'customer',
//         process.env.STRAPI_API_TOKEN,
//         'GET',
//     )).data[0]

//     for (const date of paymentDates) {
//         // check if invoice exists
//         const { data: existing } = await invoices.find({
//             filters: {
//                 contract_number: contractData.contract_number,
//                 due_date: toUTCDateString(date),
//                 automatically_generated: true,
//             },
//             pagination: { limit: 1 },
//         });
//         if (existing.length) return;

//         console.log('[InvoiceCreation] creating invoice for contract:', contractData.contract_number, "date:", date);

//         try {
//             const positions = invoiceManager.generatePositions(currentDateISO, contractData, calculationParameters);

//             const status: InvoiceDetailStatusData = {
//                 send_date: '', overdue_date: '', dunning_date: '',
//                 partially_paid_date: '', partially_payment_amount: 0,
//                 payment_date: '', refund_date: '', cancel_date: '',
//                 fail_date: '', proccessing_date: '',
//             };

//             const invoiceData: InvoiceData = {
//                 documentId: '',
//                 type: InvoiceType.FRIST_INVOICE,
//                 contract_number: contractData.contract_number,
//                 version_number: 0,
//                 invoice_detail_status: status,
//                 invoice_status: InvoiceStatusType.BOOKED,
//                 agent_status: InvoiceAgentStatusType.OPEN,
//                 insurance_status: InvoiceInsuranceStatusType.OPEN,
//                 customer_status: InvoiceCustomerStatusType.OPEN,
//                 invoice_number: '',
//                 due_date: toUTCDateString(date),
//                 total_net: contractData.premie!,
//                 total_gross: contractData.invoice_amount!,
//                 positions,
//                 billing_street: customerData.street,
//                 billing_house_number: customerData.house_number,
//                 billing_city: customerData.city,
//                 billing_postal_code: customerData.postal_code,
//                 billing_care_of: customerData.care_of,
//                 first_name: customerData.first_name,
//                 last_name: customerData.last_name,
//                 name_prefix: customerData.name_prefix,
//                 payment_mode: contractData.payment_mode,
//                 insurance_start_date: contractData.insurance_start_date,
//                 insurance_end_date: contractData.insurance_end_date,
//                 iban: contractData.iban,
//                 bic: contractData.bic,
//                 agent_street: agentData.street,
//                 agent_house_number: agentData.house_number,
//                 agent_city: agentData.city,
//                 agent_postal_code: agentData.postal_code,
//                 agent_company_name: agentData.company_name,
//                 agent_number: agentData.agent_number,
//                 customer_number: contractData.customer_number,
//                 subject: '',
//                 updatedAt: '',
//                 createdAt: '',
//                 automatically_generated: true,
//                 agency_number: customerData.agency_number
//             };

//             // persist + generate PDF
//             const newInvoiceData = await invoiceManager.createEntry(
//                 process.env.STRAPI_API_TOKEN!,
//                 invoiceData,
//             );

//             const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData);
//             await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number);
//         } catch (error) {
//             console.log("Failed to create invoice for contract:", contractData.contract_number)
//             console.error(error)
//         }
//     }

// }

// export function getPaymentDates(
//     startDate: Date,
//     endDate: Date,
//     mode: string
// ): Date[] {

//     if (!(mode in timeIntervals)) return [];
//     const intervalCount = timeIntervals[mode as keyof typeof timeIntervals];
//     const paymentDates: Date[] = [];
//     startDate = new Date(Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), startDate.getUTCDate()));
//     endDate = new Date(Date.UTC(endDate.getUTCFullYear(), endDate.getUTCMonth(), endDate.getUTCDate()));
//     let current = new Date(startDate);

//     while (current <= endDate) {
//         paymentDates.push(new Date(current));

//         if (mode === 'Einmalzahlung') break;

//         const monthsToAdd = 12 / intervalCount;
//         const nextDate = DateTime.fromJSDate(current).plus({ months: monthsToAdd }).toJSDate()

//         if (nextDate > endDate) break;
//         current = nextDate;
//     }
//     return paymentDates;
// }

// async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
//     try {
//         const response = body ? await fetch(url, {
//             method: method,
//             headers: {
//                 Authorization: `Bearer ${token}`,
//                 'Content-Type': 'application/json',
//             },
//             body: body
//         }) :
//             await fetch(url, {
//                 method: method,
//                 headers: {
//                     Authorization: `Bearer ${token}`,
//                     'Content-Type': 'application/json',
//                 },
//             });

//         if (!response.ok) {
//             const errorDetails = await response.json();
//             console.error(`${dataType} Fetch Error:`, errorDetails);
//             throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
//         }

//         const data = await response.json();
//         return data || null;
//     } catch (error) {
//         console.error(`Error fetching ${dataType} data:`, error);
//         throw error;
//     }
// }

// export async function getCalculationParameters(token: string, startDate: string) {
//     const data = await fetch(`${process.env.STRAPI_BASE_URL}/calculation-parameters`, {
//         method: 'GET',
//         headers: { Authorization: `Bearer ${token}` }
//     })
//     if (!data.ok) {
//         throw new Error("CALCULATION_PARAMETERS not found")
//     }
//     const dataJson = await data.json()
//     const calculationParameterList: CalculationParametersData[] = dataJson['data']

//     const final_date = findClosestPastDate(calculationParameterList, startDate)

//     return calculationParameterList.filter((entry) => entry.valid_from == final_date)[0]
// }

// function findClosestPastDate(dates: CalculationParametersData[], referenceDate: string) {
//     const refDate = new Date(referenceDate);
//     // Convert dates to date-objects and only keep ealier dates
//     const pastDates = dates
//         .map(date => new Date(date.valid_from))
//         .filter(date => date < refDate);
//     if (pastDates.length === 0) return null;
//     // Return the maxiumum date before the reference date
//     return pastDates.reduce((closest, date) =>
//         date > closest ? date : closest
//     ).toISOString().split('T')[0]; // date format YYYY-MM-DD
// }
