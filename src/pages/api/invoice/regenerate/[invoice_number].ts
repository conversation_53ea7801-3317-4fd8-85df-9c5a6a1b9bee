// src/pages/api/invoice/update.ts
import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceData, InvoiceDetailStatusData, InvoiceInsuranceStatusType, InvoiceStatusType, InvoiceType } from "@/utils/invoice/types";
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { fileManager } from "@/utils/fileManager";
import { ContractData } from "@/types";
import { getCalculationParameters } from "@/utils/contractCalculator";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'GET') {
        try {

            const { invoice_number } = req.query;

            if (!permissionManager.hasInvoicePermissionsInvoiceNumber(invoice_number as string, token)) {
                res.status(401)
            }

            const authUser = await permissionManager.getAuthUser(token);

            const oldInvoiceData = await invoiceManager.getInvoice(token, invoice_number as string);

            const strapiUrl = `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq]=${oldInvoiceData.contract_number}`;

            const contractResponse = await fetch(strapiUrl, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!contractResponse.ok) {
                const errorDetails = await contractResponse.json();
                console.error('Strapi Error:', errorDetails);
                throw new Error(`Failed to fetch contract: ${errorDetails.error?.message || contractResponse.statusText}`);
            }

            const contractData: ContractData = (await contractResponse.json()).data[0];

            if (oldInvoiceData.type == InvoiceType.FRIST_INVOICE) {

                const agentData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${contractData.agent_number}`,
                    'user',
                    token,
                    'GET',
                ))[0];

                const customerData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${contractData.customer_number}`,
                    'customer',
                    token,
                    'GET',
                )).data[0];
                const calculationParameters = await getCalculationParameters(token, contractData.insurance_start_date)

                const positions = invoiceManager.generatePositionsFirstInvoice(contractData, calculationParameters);

                const invoiceStatus: InvoiceDetailStatusData = {
                    send_date: '',
                    overdue_date: '',
                    dunning_date: '',
                    partially_paid_date: '',
                    partially_payment_amount: 0,
                    payment_date: '',
                    refund_date: '',
                    cancel_date: '',
                    fail_date: '',
                    proccessing_date: ''
                }

                // create invoice entry in strapi
                const invoiceData: InvoiceData = {
                    documentId: '',
                    type: InvoiceType.FRIST_INVOICE,
                    contract_number: contractData.contract_number,
                    version_number: oldInvoiceData.version_number + 1,
                    invoice_detail_status: invoiceStatus,
                    invoice_status: InvoiceStatusType.BOOKED,
                    agent_status: InvoiceAgentStatusType.OPEN,
                    insurance_status: InvoiceInsuranceStatusType.OPEN,
                    customer_status: InvoiceCustomerStatusType.OPEN,
                    invoice_number: '',
                    due_date: contractData.insurance_start_date,
                    total_net: contractData.first_invoice_net!,
                    total_gross: contractData.first_invoice_gross!,
                    positions: positions,
                    billing_street: customerData.street,
                    billing_house_number: customerData.house_number,
                    billing_city: customerData.city,
                    billing_postal_code: customerData.postal_code,
                    billing_care_of: customerData.care_of,
                    first_name: customerData.first_name,
                    last_name: customerData.last_name,
                    name_prefix: customerData.name_prefix,
                    payment_mode: contractData.payment_mode,
                    insurance_start_date: contractData.insurance_start_date,
                    insurance_end_date: contractData.insurance_end_date,
                    iban: contractData.iban,
                    bic: contractData.bic,
                    agent_street: agentData.street,
                    agent_house_number: agentData.house_number,
                    agent_city: agentData.city,
                    agent_postal_code: agentData.postal_code,
                    agent_company_name: agentData.company_name,
                    agent_number: agentData.agent_number,
                    customer_number: customerData.customer_number,
                    subject: '',
                    updatedAt: '',
                    createdAt: '',
                    automatically_generated: true,
                    agency_number: customerData.agency_number
                }

                const newInvoiceData = await invoiceManager.updateInvoice(token, oldInvoiceData.documentId, invoiceData)

                const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData)
                await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number)
            }
            else if (oldInvoiceData.type == InvoiceType.BILL && oldInvoiceData.automatically_generated) {
                const agentData = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${contractData.agent_number != null ? contractData.agent_number : authUser['agent_number']}`,
                    'user',
                    token,
                    'GET',
                )

                const customerData = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${contractData.customer_number}`,
                    'customer',
                    token,
                    'GET',
                )
                const calculationParameters = await getCalculationParameters(token, contractData.insurance_start_date)

                const positions = invoiceManager.generatePositions(oldInvoiceData.due_date, contractData, calculationParameters);

                const invoiceStatus: InvoiceDetailStatusData = {
                    send_date: '',
                    overdue_date: '',
                    dunning_date: '',
                    partially_paid_date: '',
                    partially_payment_amount: 0,
                    payment_date: '',
                    refund_date: '',
                    cancel_date: '',
                    fail_date: '',
                    proccessing_date: ''
                }

                // create invoice entry in strapi
                const invoiceData: InvoiceData = {
                    documentId: '',
                    type: InvoiceType.BILL,
                    contract_number: contractData.contract_number,
                    version_number: oldInvoiceData.version_number + 1,
                    invoice_detail_status: invoiceStatus,
                    invoice_status: InvoiceStatusType.BOOKED,
                    agent_status: InvoiceAgentStatusType.OPEN,
                    insurance_status: InvoiceInsuranceStatusType.OPEN,
                    customer_status: InvoiceCustomerStatusType.OPEN,
                    invoice_number: '',
                    due_date: oldInvoiceData.due_date,
                    total_net: contractData.first_invoice_net!,
                    total_gross: contractData.first_invoice_gross!,
                    positions: positions,
                    billing_street: customerData.street,
                    billing_house_number: customerData.house_number,
                    billing_city: customerData.city,
                    billing_postal_code: customerData.postal_code,
                    billing_care_of: customerData.care_of,
                    first_name: customerData.first_name,
                    last_name: customerData.last_name,
                    name_prefix: customerData.name_prefix,
                    payment_mode: contractData.payment_mode,
                    insurance_start_date: contractData.insurance_start_date,
                    insurance_end_date: contractData.insurance_end_date,
                    iban: contractData.iban,
                    bic: contractData.bic,
                    agent_street: agentData.street,
                    agent_house_number: agentData.house_number,
                    agent_city: agentData.city,
                    agent_postal_code: agentData.postal_code,
                    agent_company_name: agentData.company_name,
                    agent_number: agentData.agent_number,
                    customer_number: customerData.customer_number,
                    subject: '',
                    updatedAt: '',
                    createdAt: '',
                    automatically_generated: true,
                    agency_number: customerData.agency_number
                }

                const newInvoiceData = await invoiceManager.updateInvoice(token, oldInvoiceData.documentId, invoiceData)

                const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData)
                await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number)
            } else {
                res.status(403).json({ error: 'Update invoice not allowed' });
            }

            res.status(200).end();

        } catch (error) {
            console.error('Error updating invoice in Strapi:', error);
            res.status(500).json({ error: 'Failed to update invoice' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

