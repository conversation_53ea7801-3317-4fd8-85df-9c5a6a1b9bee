// src/pages/api/invoice/update.ts
import { InvoiceData } from "@/utils/invoice/types";
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { fileManager } from "@/utils/fileManager";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'PUT') {
        try {

            const { invoiceData } = req.body as { invoiceData: InvoiceData };

            if (!permissionManager.hasInvoicePermissions(invoiceData.documentId, token)) {
                res.status(401)
            }

            if (!invoiceData || typeof invoiceData !== 'object') {
                res.status(400).json({ error: 'Invalid or missing request body' });
                return;
            }

            const newInvoiceData = await invoiceManager.updateInvoice(token, invoiceData.documentId, invoiceData);
            const pdfBytes = await invoiceManager.generatePDFBytes(newInvoiceData)
            await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number)

            if (newInvoiceData) {
                res.status(200).json(newInvoiceData);
            } else {
                throw new Error(newInvoiceData);
            }
        } catch (error) {
            console.error('Error updating invoice in Strapi:', error);
            res.status(500).json({ error: 'Failed to update invoice' });
        }
    } else {
        res.setHeader('Allow', ['PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

