// src/pages/api/contracts/[contract_number]/[type]/generate_pdf.ts
import { CalculationParametersData, ContractData } from '@/types';
import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceData, InvoiceInsuranceStatusType, InvoiceSequenceData, InvoiceDetailStatusData, InvoiceStatusType } from "@/utils/invoice/types";
import { fileManager } from '@/utils/fileManager';
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const { invoiceData } = req.body as { invoiceData: InvoiceData };

    if (req.method === 'POST') {
        try {

            const authUser = await permissionManager.getAuthUser(token)
            const hasPermission = await permissionManager.hasUserContractPermissions(authUser, invoiceData.contract_number, token)
            if (!hasPermission) {
                res.status(401)
            }

            // fetch contract and agent data
            const contractResponse = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${invoiceData.contract_number}`, 'contract', token, 'GET')
            const contractData: ContractData = contractResponse.data[0];
            const agentData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${contractData.agent_number != null ? contractData.agent_number : authUser['agent_number']}`,
                'user',
                token,
                'GET',
            ))?.[0]

            // check for sequence
            const sequenceData: InvoiceSequenceData = await invoiceManager.getSequence(invoiceData.contract_number, token);

            const invoiceNumber = invoiceManager.generateInvoiceNumber(invoiceData.contract_number, sequenceData.sequence)

            // if invoice already exist, throw error
            if (await hasInvoice(token, invoiceNumber) != null) {
                res.status(401)
            }

            const invoiceDetailStatus: InvoiceDetailStatusData = {
                send_date: '',
                overdue_date: '',
                dunning_date: '',
                partially_paid_date: '',
                partially_payment_amount: 0,
                payment_date: '',
                refund_date: '',
                cancel_date: '',
                fail_date: '',
                proccessing_date: '',
            }

            invoiceData.invoice_status = InvoiceStatusType.BOOKED;
            invoiceData.agent_status = InvoiceAgentStatusType.OPEN;
            invoiceData.insurance_status = InvoiceInsuranceStatusType.OPEN;
            invoiceData.customer_status = InvoiceCustomerStatusType.OPEN;
            invoiceData.invoice_detail_status = invoiceDetailStatus;

            invoiceData.iban = contractData.iban;
            invoiceData.bic = contractData.bic;
            invoiceData.insurance_start_date = contractData.insurance_start_date;
            invoiceData.insurance_end_date = contractData.insurance_end_date;
            invoiceData.payment_mode = contractData.payment_mode;

            invoiceData.customer_number = contractData.customer_number;

            invoiceData.agent_city = agentData.city;
            invoiceData.agent_postal_code = agentData.postal_code;
            invoiceData.agent_street = agentData.street;
            invoiceData.agent_house_number = agentData.house_number;
            invoiceData.agent_number = agentData.agency_number;
            invoiceData.agent_company_name = agentData.company_name;

            invoiceData.agency_number = agentData.agency_number;

            // create invoice entry in strapi
            const newInvoiceData = await invoiceManager.createEntry(token, invoiceData);

            // upload File
            const pdfBytes = await invoiceManager.generatePDFBytes(invoiceData)
            await fileManager.uploadFile(pdfBytes, newInvoiceData.invoice_number)

            res.status(201).json(newInvoiceData);

        } catch (error) {
            console.error('Error creating Invoice:', error);
            res.status(500).json({ error: 'Failed to create Invoice' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

export async function getCalculationParameters(token: string, startDate: string) {
    const data = await fetch(`${process.env.STRAPI_BASE_URL}/calculation-parameters`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    })
    if (!data.ok) {
        throw new Error("CALCULATION_PARAMETERS not found")
    }
    const dataJson = await data.json()
    const calculationParameterList: CalculationParametersData[] = dataJson['data']

    const final_date = findClosestPastDate(calculationParameterList, startDate)

    return calculationParameterList.filter((entry) => entry.valid_from == final_date)[0]
}

function findClosestPastDate(dates: CalculationParametersData[], referenceDate: string) {
    const refDate = new Date(referenceDate);
    // Convert dates to date-objects and only keep ealier dates
    const pastDates = dates
        .map(date => new Date(date.valid_from))
        .filter(date => date < refDate);
    if (pastDates.length === 0) return null;
    // Return the maxiumum date before the reference date
    return pastDates.reduce((closest, date) =>
        date > closest ? date : closest
    ).toISOString().split('T')[0]; // date format YYYY-MM-DD
}

async function hasInvoice(token: string, invoice_number: string) {
    const attachments = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoices?filters[invoice_number][$eq][0]=${invoice_number}`,
        'attachment',
        token,
        'GET'
    )
    if (attachments['meta']['pagination']['total'] > 0)
        return attachments['data'][0].documentId;
    else return null
}
