// src/pages/api/invoice/download/[documentId].ts
import { fileManager } from '@/utils/fileManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { documentId } = req.query;

    if (!documentId || typeof documentId !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing ducumentId' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const invoiceData = (await fetchDataFromAPI(
            `${process.env.STRAPI_BASE_URL}/invoices/${documentId}`,
            'invoices',
            token
        ));

        const hasPermissions = await permissionManager.hasContractPermissions(invoiceData.contract_number, token);
        if (hasPermissions) {
            const pdf_stream = await fileManager.downloadFile(invoiceData['invoice_number'])

            // Set response headers
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename="${invoiceData['invoice_number']}"`
            );

            // Pipe the S3 stream to the response
            pdf_stream.on('error', (err: any) => {
                console.error('Error fetching File from S3:', err);
                res.status(500).json({ error: 'Failed to fetch File' });
            });

            pdf_stream.pipe(res);
        } else res.status(401).end('Unauthorized')

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

