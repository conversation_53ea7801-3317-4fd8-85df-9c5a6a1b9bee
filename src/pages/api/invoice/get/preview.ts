// src/pages/api/invoice/preview.ts
import { ContractData } from '@/types';
import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceData, InvoiceInsuranceStatusType, InvoiceDetailStatusData, InvoiceStatusType } from "@/utils/invoice/types";
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]
    const { invoiceData } = req.body as { invoiceData: InvoiceData }

    if (req.method === 'POST') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const hasPermission = await permissionManager.hasUserContractPermissions(authUser, invoiceData.contract_number, token)
            if (!hasPermission) {
                res.status(401)
            }

            // fetch contract and agent data
            const contractResponse = await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${invoiceData.contract_number}`, 'contract', token, 'GET')
            const contractData: ContractData = contractResponse.data[0];
            const agentData = (await fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${contractData.agent_number != null ? contractData.agent_number : authUser['agent_number']}`,
                'user',
                token,
                'GET',
            ))?.[0]

            const invoiceNumber = 'preview'

            const invoiceDetailStatus: InvoiceDetailStatusData = {

                send_date: '',
                overdue_date: '',
                dunning_date: '',
                partially_paid_date: '',
                partially_payment_amount: 0,
                payment_date: '',
                refund_date: '',
                cancel_date: '',
                fail_date: '',
                proccessing_date: '',

            }

            invoiceData.invoice_number = invoiceNumber;
            invoiceData.invoice_status = InvoiceStatusType.BOOKED;
            invoiceData.agent_status = InvoiceAgentStatusType.OPEN;
            invoiceData.insurance_status = InvoiceInsuranceStatusType.OPEN;
            invoiceData.customer_status = InvoiceCustomerStatusType.OPEN;
            invoiceData.invoice_detail_status = invoiceDetailStatus;

            invoiceData.iban = contractData.iban;
            invoiceData.bic = contractData.bic;
            invoiceData.insurance_start_date = contractData.insurance_start_date;
            invoiceData.insurance_end_date = contractData.insurance_end_date;
            invoiceData.payment_mode = contractData.payment_mode;

            invoiceData.customer_number = contractData.customer_number;

            invoiceData.agent_city = agentData.city;
            invoiceData.agent_postal_code = agentData.postal_code;
            invoiceData.agent_street = agentData.street;
            invoiceData.agent_house_number = agentData.house_number;
            invoiceData.agent_number = agentData.agency_number;
            invoiceData.agent_company_name = agentData.company_name;

            // prepare pdf
            const pdfBytes = await invoiceManager.generatePDFBytes(invoiceData, true)
            const buffer = Buffer.from(pdfBytes);
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', 'inline; filename="preview.pdf"');
            res.status(200).send(buffer);

        } catch (error) {
            console.error('Error creating PDF:', error);
            res.status(500).json({ error: 'Failed to create PDF' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

