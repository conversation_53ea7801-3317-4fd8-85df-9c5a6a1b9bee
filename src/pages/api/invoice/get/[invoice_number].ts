// src/pages/api/invoice/get/[invoice_number].ts
import { InvoiceData } from "@/utils/invoice/types";
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { invoice_number } = req.query;

    if (!invoice_number || typeof invoice_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing contract number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const hasPermission = permissionManager.hasInvoicePermissionsInvoiceNumber(invoice_number, token)
        if(!hasPermission) {
            res.status(401)
        }

        const invoiceData: InvoiceData = (await fetchDataFromAPI(
            `${process.env.STRAPI_BASE_URL}/invoices?filters[invoice_number][$eq]=${invoice_number}`,
            'invoices',
            token
        ))[0];

        res.status(201).json(invoiceData)

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

