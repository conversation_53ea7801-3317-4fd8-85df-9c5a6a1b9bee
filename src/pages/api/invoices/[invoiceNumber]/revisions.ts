// src/pages/api/customers/[invoiceNumber]/revisions.ts

import type { EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { getRevisions } from '@/utils/mongoDB/mongoDBFunctions';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        // Require auth header
        if (!req.headers.authorization) return 'Missing authorization in headers';

        // Validate route param
        const { invoiceNumber } = req.query;
        if (!invoiceNumber || Array.isArray(invoiceNumber)) {
            return 'Invalid or missing invoice number';
        }
    },

    execute: async ({ req, res }) => {
        const invoiceNumber = String(req.query.invoiceNumber);

        // Fetch revisions from Mongo
        const revisions = await getRevisions('invoice', invoiceNumber);

        // Return 200 with array (empty array if none)
        return res.status(200).json(revisions);
    },
};

export default createEndpoint({ GET });
