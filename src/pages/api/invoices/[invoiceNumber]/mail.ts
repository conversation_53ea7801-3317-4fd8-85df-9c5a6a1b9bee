// src/pages/api/invoices/[invoiceNumber]/mail.ts
import { ContractService } from '@/server/contract/ContractService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InvoiceService } from '@/server/invoice/InvoiceService';
import { type ContractStatusData } from '@/types';
import { mailManager } from '@/utils/mailManager';

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { invoiceNumber } = req.query;
        if (!invoiceNumber) return 'Invalid query';
    },

    execute: async ({ req, res, db }) => {
        const { invoiceNumber } = req.query as { invoiceNumber: string };

        // service
        const invoiceService = new InvoiceService(db);
        const contractService = new ContractService(db);

        const invoice = await invoiceService.getInvoiceOrThrow({
            where: { invoiceNumber },
            include: {
                contract: true,
                customer: true,
                agent: { select: { email: true } },
            },
        });

        await mailManager.sendInvoice(
            invoice.customer!,
            invoice.contract!,
            invoice.invoiceNumber,
            invoice.agent!.email
        );

        const contractStatus = invoice.contract!
            .contractStatus as ContractStatusData;
        contractStatus.send_invoice = true;

        contractService.update(
            { documentId: invoice.contract!.documentId },
            { contractStatus: contractStatus }
        );

        res.status(201).end();
    },
};

export default createEndpoint({ PUT });
