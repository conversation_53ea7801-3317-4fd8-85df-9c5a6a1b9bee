import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InvoiceService } from '@/server/invoice/InvoiceService';
import { fileManager } from '@/utils/fileManager';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { invoiceNumber } = req.query;
        if (
            !invoiceNumber ||
            (Array.isArray(invoiceNumber) && invoiceNumber.length !== 1)
        ) {
            return 'Invalid or missing invoice number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const invoiceNumber = Array.isArray(req.query.invoiceNumber)
            ? req.query.invoiceNumber[0]
            : String(req.query.invoiceNumber);

        // service
        const service = new InvoiceService(db);

        const invoice = await service.findOne({ invoiceNumber: invoiceNumber });

        if (!invoice) {
            res.status(404).json({ message: 'Invoice not found' });
            return;
        }

        const pdf_stream = await fileManager.downloadFile(invoice.invoiceNumber);

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="${invoice.invoiceNumber}"`
        );

        // Pipe the S3 stream to the response
        pdf_stream.on('error', (err: any) => {
            console.error('Error fetching File from S3:', err);
            res.status(500).json({ error: 'Failed to fetch File' });
        });

        pdf_stream.pipe(res);
    },
};

export default createEndpoint({ GET });
