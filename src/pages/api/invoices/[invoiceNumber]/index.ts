// src/pages/api/agent/[agentNumber].ts
import { type Customer } from '@/generated/prisma-postgres';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InvoiceService } from '@/server/invoice/InvoiceService';
// If you already have an AgentService, use it; otherwise fallback to direct db usage.
// import { AgentService } from "@/server/agents/AgentService";

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { invoiceNumber } = req.query;
        if (
            !invoiceNumber ||
            (Array.isArray(invoiceNumber) && invoiceNumber.length !== 1)
        ) {
            return 'Invalid or missing invoice number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const invoiceNumber = Array.isArray(req.query.invoiceNumber)
            ? req.query.invoiceNumber[0]
            : String(req.query.invoiceNumber);

        // service
        const service = new InvoiceService(db);

        const invoice = await service.findOne({ invoiceNumber: invoiceNumber });

        if (!invoice) {
            res.status(404).json({ message: 'Invoice not found' });
            return;
        }

        res.status(200).json(invoice);
    },
};

const PUT: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { customerNumber } = req.query;
        if (
            !customerNumber ||
            (Array.isArray(customerNumber) && customerNumber.length !== 1)
        ) {
            return 'Invalid or missing customer number';
        }
    },

    execute: async ({ req, res, db }) => {

        const data: Customer = req.body;

        // service
        const service = new CustomerService(db);

        const updated = await service.update({ documentId: data.documentId }, data);

        if (!updated) {
            res.status(404).json({ message: 'Customer not found' });
            return;
        }

        res.status(200).json(updated);
    },
};

export default createEndpoint({ GET, PUT });
