import { type CalculationParameter } from '@/generated/prisma-postgres';
import { AgentService } from '@/server/agent/AgentService';
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { ContractService } from '@/server/contract/ContractService';
import { CustomerService } from '@/server/customer/CustomerService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InvoiceService } from '@/server/invoice/InvoiceService';
import { fileManager } from '@/utils/fileManager';
import { invoiceManager } from '@/utils/invoice/invoiceManager';
import {
    InvoiceAgentStatusType,
    InvoiceCustomerStatusType,
    type InvoiceDetailStatusData,
    InvoiceInsuranceStatusType,
    InvoiceStatusType,
    InvoiceType,
} from '@/utils/invoice/types';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { invoiceNumber } = req.query;
        if (
            !invoiceNumber ||
            (Array.isArray(invoiceNumber) && invoiceNumber.length !== 1)
        ) {
            return 'Invalid or missing customer number';
        }
    },

    execute: async ({ req, res, db }) => {
        // param
        const invoiceNumber = Array.isArray(req.query.invoiceNumber)
            ? req.query.invoiceNumber[0]
            : String(req.query.invoiceNumber);

        const invoiceService = new InvoiceService(db);
        const invoice = (await invoiceService.findOne({ invoiceNumber }))!;

        const contractService = new ContractService(db);
        const contract = (await contractService.getContract({
            contractNumber: invoice.contractNumber!,
        }))!;

        const customerService = new CustomerService(db);
        const customer = (await customerService.findOne({
            customerNumber: contract.customerNumber!,
        }))!;

        const agentService = new AgentService(db);
        const agent = (await agentService.findOne({
            agentNumber: invoice.agentNumber!,
        }))!;

        const calculationParameterService = new CalculationParameterService(db);
        const calculationParameters: CalculationParameter =
            (await calculationParameterService.getCalculationParameters(
                contract.insuranceStartDate!
            ))!;

        const positions = invoiceManager.generatePositionsFirstInvoice(
            contract,
            calculationParameters
        );

        const invoiceStatus: InvoiceDetailStatusData = {
            send_date: '',
            overdue_date: '',
            dunning_date: '',
            partially_paid_date: '',
            partially_payment_amount: 0,
            payment_date: '',
            refund_date: '',
            cancel_date: '',
            fail_date: '',
            proccessing_date: '',
        };

        // create invoice entry in strapi
        const invoiceData = {
            type: InvoiceType.FRIST_INVOICE,
            versionNumber: invoice.versionNumber! + 1,
            invoiceDetailStatus: invoiceStatus as any,
            invoiceStatus: InvoiceStatusType.BOOKED,
            agentStatus: InvoiceAgentStatusType.OPEN,
            insuranceStatus: InvoiceInsuranceStatusType.OPEN,
            customerStatus: InvoiceCustomerStatusType.OPEN,
            dueDate: contract.insuranceStartDate,
            totalNet: contract.firstInvoiceNet!,
            totalGross: contract.firstInvoiceGross!,
            positions: positions as any,
            billingStreet: customer.street,
            billingHouseNumber: customer.houseNumber,
            billingCity: customer.city,
            billingPostalCode: customer.postalCode,
            billingCareOf: customer.careOf,
            firstName: customer.firstName,
            lastName: customer.lastName,
            namePrefix: customer.namePrefix,
            paymentMode: contract.paymentMode,
            insuranceStartDate: contract.insuranceStartDate,
            insuranceEndDate: contract.insuranceEndDate,
            iban: contract.iban,
            bic: contract.bic,
            agentStreet: agent.street,
            agentHouseNumber: agent.houseNumber,
            agentCity: agent.city,
            agentPostalCode: agent.postalCode,
            agentCompanyName: agent.companyName,
            agentNumber: agent.agentNumber,
            customerNumber: customer.customerNumber,
            subject: '',
            automaticallyGenerated: true,
        };

        const updated = await invoiceManager.updateInvoice(
            db,
            invoice.documentId,
            invoiceData
        );

        const pdfBytes = await invoiceManager.generatePDFBytes(updated);
        await fileManager.uploadFile(pdfBytes, updated.invoiceNumber);

        if (!updated) {
            res.status(404).json({ message: 'Customer not found' });
            return;
        }

        res.status(200).json(updated);
    },
};

export default createEndpoint({ GET });
