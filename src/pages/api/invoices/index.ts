import { invoiceFieldConfig } from '@/components/filters/invoiceFieldConfig';
import { Prisma } from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { InvoiceService } from '@/server/invoice/InvoiceService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },
    execute: async ({ req, res, db }) => {

        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'agencyNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Invoice,
            fields: invoiceFieldConfig,
        });

        const service = new InvoiceService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ items, meta });
    },
};

export default createEndpoint({ GET });
