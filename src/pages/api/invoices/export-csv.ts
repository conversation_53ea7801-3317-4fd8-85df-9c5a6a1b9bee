// src/pages/api/invoice/export-csv.ts
import { Parser } from '@json2csv/plainjs';

import { invoiceFieldConfig } from '@/components/filters/invoiceFieldConfig';
import { type Invoice, Prisma } from '@/generated/prisma-postgres';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';
import { InvoiceService } from '@/server/invoice/InvoiceService';
import {
    formatInvoiceAgentStatus,
    formatInvoiceCustomerStatus,
    formatInvoiceInsuranceStatus,
    formatInvoiceStatus,
} from '@/utils/keyFormatter';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
        if (req.method !== 'GET') return `Method ${req.method} Not Allowed`;
    },

    execute: async ({ req, res, db, auth }) => {
        try {
            const isAdmin = auth.roles.includes('asevo-admin');

            // --- Sorting -----------------------------------------------------------
            const sortField = String(req.query.sortField ?? 'invoiceNumber');
            const sortDirection =
                String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                    ? 'desc'
                    : 'asc';
            const orderBy = { [sortField]: sortDirection } as const;

            // --- Filters -----------------------------------------------------------
            const baseWhere = parseQuery(req.query, {
                rootModel: Prisma.ModelName.Invoice,
                fields: invoiceFieldConfig,
            });

            const where =
                !isAdmin
                    ? { AND: [baseWhere, { agencyNumber: String(auth.agencyNumber) }] }
                    : baseWhere;

            // --- Fetch all rows in batches ----------------------------------------
            const batchSize =
                Math.max(1, Number((req.query.batchSize as string) ?? 1000)) || 1000;

            const service = new InvoiceService(db as any);

            const first = await service.list({
                where,
                orderBy,
                skip: 0,
                take: batchSize,
            });

            const allItems = [...first.items];
            let fetched = first.items.length;
            const total = first.total;

            while (fetched < total) {
                const next = await service.list({
                    where,
                    orderBy,
                    skip: fetched,
                    take: batchSize,
                });
                allItems.push(...next.items);
                fetched += next.items.length;
            }

            // --- CSV mapping (camelCase values only) -------------------------------
            const csvData = allItems.map((item: Invoice) => ({
                rechnungsnummer: item.invoiceNumber,
                rechnungsdatum: item.dueDate,
                rechnungNetto: item.totalNet,
                rechnungBrutto: item.totalGross,
                positionen: item.positions,
                rechnungsdetailstatus: item.invoiceDetailStatus,
                rechnungsstatus: formatInvoiceStatus(item.invoiceStatus || 0),
                versionsnummer: item.versionNumber,
                typ: item.type,
                vertragsnummer: item.contractNumber,
                versicherungsbeginn: item.insuranceStartDate,
                versicherungsablauf: item.insuranceEndDate,
                versicherungsstatus: formatInvoiceInsuranceStatus(item.insuranceStatus || 0),
                zahlungsweise: item.paymentMode,
                iban: item.iban,
                bic: item.bic,
                kundennummer: item.customerNumber,
                kundeTitel: item.namePrefix,
                kundeVorname: item.firstName,
                kundeNachname: item.lastName,
                kundeCo: item.billingCareOf,
                kundeStraße: item.billingStreet,
                kundeHausnummer: item.billingHouseNumber,
                kundePostleitzahl: item.billingPostalCode,
                kundeOrt: item.billingCity,
                kundenstatus: formatInvoiceCustomerStatus(item.customerStatus || 0),
                agentur: item.agentCompanyName,
                maklernummer: item.agentNumber,
                maklerStraße: item.agentStreet,
                maklerHausnummer: item.agentHouseNumber,
                maklerPostleitzahl: item.agentPostalCode,
                maklerOrt: item.agentCity,
                maklerstatus: formatInvoiceAgentStatus(item.agentStatus || 0),
                betreff: item.subject,
                aktualisiert: item.updatedAt,
                erstellt: item.createdAt,
                automatisierteErstellung: item.automaticallyGenerated,
            }));

            const parser = new Parser({});
            const csv = parser.parse(csvData);

            res.setHeader('Content-Type', 'text/csv; charset=utf-8');
            res.setHeader(
                'Content-Disposition',
                'attachment; filename=invoices.csv'
            );
            res.status(200).send(csv);
        } catch (error) {
            console.error('CSV Export Error:', error);
            if (req.method !== 'GET') {
                res.setHeader('Allow', ['GET']);
                res.status(405).end(`Method ${req.method} Not Allowed`);
                return;
            }
            res.status(500).json({ error: 'Failed to export invoices to CSV' });
        }
    },
};

export default createEndpoint({ GET });
