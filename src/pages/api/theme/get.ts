// src/pages/api/theme/get.ts
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (req.method === 'GET') {
        try {
            const themeResponse = await fetch(`${process.env.STRAPI_BASE_URL}/themes/${process.env.THEME_ID}`,
                {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${process.env.STRAPI_API_TOKEN}`,
                        'Content-Type': 'application/json',
                    }
                }
            );

            if (!themeResponse.ok) {
                const text = await themeResponse.text();
                throw new Error(text || themeResponse.statusText);
            }

            const { data } = await themeResponse.json();
            let theme = data.theme_string;

            if (typeof theme === 'string') {
                try {
                    theme = JSON.parse(data.theme_string);
                } catch (e) {
                    console.error('Invalid JSON in theme_string:', e);
                    return res.status(500).json({ error: 'Bad theme JSON' });
                }
            }

            return res.status(200).json(theme);

        } catch (error) {
            console.error('Error fetching theme:', error);
            res.status(500).json({ error: 'Failed to fetch theme' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
    try {
        const response = body ? await fetch(url, {
            method: method,
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: body
        }) :
            await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

