import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { ThemeService } from '@/server/theme/ThemeService';

const GET: EndpointHandler = {
  auth: 'none',
  execute: async ({ res, db }) => {
    const themeService = new ThemeService(db);

    const theme = await themeService.findOne({
      documentId: process.env.THEME_ID,
    });

    res.status(200).json(JSON.parse(theme?.themeString || ''));
  },
};

export default createEndpoint({ GET });
