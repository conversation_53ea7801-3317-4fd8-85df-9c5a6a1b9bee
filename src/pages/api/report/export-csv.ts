// src/pages/api/report/export-csv.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { Parser } from '@json2csv/plainjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }

    const token = req.headers.authorization.split(" ")[1];

    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
        return;
    }

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        const { filters = '{}', sortField = 'report_number', sortDirection = 'asc' } = req.query;
        const parsedFilters = JSON.parse(filters as string);
        const limit = 100;

        const filterParams = new URLSearchParams();
        if (authUser && !isAdmin) {
            filterParams.append('filters[agency_number][$eq]', authUser['agency_number']);
        }
        Object.entries(parsedFilters).forEach(([field, value]) => {
            filterParams.append(`filters[${field}][$contains]`, value as string);
        });
        const sort = `${sortField}:${sortDirection}`;
        filterParams.append('sort', sort);

        const reports: any[] = [];
        let offset = 0;
        let total = 0;


        const firstUrl = `${process.env.STRAPI_BASE_URL}/reports?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
        const firstRes = await fetch(firstUrl, {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        });

        if (!firstRes.ok) {
            const errorDetails = await firstRes.json();
            throw new Error(errorDetails?.error?.message || firstRes.statusText);
        }

        const firstData = await firstRes.json();
        reports.push(...firstData.data);
        total = firstData.meta.pagination.total;


        while (reports.length < total) {
            offset += limit;
            const pageUrl = `${process.env.STRAPI_BASE_URL}/reports?${filterParams.toString()}&pagination[limit]=${limit}&pagination[start]=${offset}`;
            const pageRes = await fetch(pageUrl, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!pageRes.ok) break;
            const pageData = await pageRes.json();
            reports.push(...pageData.data);
        }

        const csvData = reports.map((item) => ({
            Vertragsnummer: item.contract_number,
            Kundennummer: item.customer_number,
            Meldung: item.text,
            Schadensnummer: item.report_number,
            Externe_Schadensnummer: item.external_report_number,
            Erstellungdatum: item.createdAt,
            Schadensdatum: item.damage_date,
            Schadensort: item.damage_location,
            IBAN: item.iban,
            Versicherte_Gefahr: item.coverd_risk,
        }));

        const opts = {};
        const parser = new Parser(opts);
        const csv = parser.parse(csvData);

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=reports.csv');
        res.status(200).send(csv);

    } catch (error) {
        console.error('CSV Export Error:', error);
        res.status(500).json({ error: 'Failed to export reports to CSV' });
    }
}
