import { NextApiRequest, NextApiResponse } from 'next';
// import { mailManager } from '@/utils/mailManager';
import { generateReportNumber } from '@/utils/numberGenerator';
import formidable, { Files } from 'formidable';
import fs from 'fs';
import { fileManager } from '@/utils/fileManager';
import { fetchDataFromAPI } from '@/utils/fetchUtil';
import { permissionManager } from '@/utils/permissionManager';
import {getToken} from "@/utils/getToken";
import {ReportPrecheckTemplatesRepository} from "@/server/reports/prechecks/ReportPrecheckTemplatesRepository";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";
import {ContractsRepository} from "@/server/ContractsRepository";
import {ReportPrechecksService} from "@/server/reports/prechecks/ReportPrechecksService";
import {ReportData} from "@/types";
import {HttpClient} from "@/utils/HttpClient";

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Authorization header
    const token = getToken.onServer(req)

    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    try {
        const form = formidable({ multiples: true });

        form.parse(req, async (err, fields, files) => {
            if (err) {
                console.error('File parsing error:', err);
                return res.status(500).json({ error: 'File parsing error' });
            }

            const customer_number = fields.customer_number?.[0] || '';
            const contract_number = fields.contract_number?.[0] || '';
            const agency_number = fields.agency_number?.[0] || '';
            const external_report_number = fields.external_report_number?.[0] || '';
            const damage_date = fields.damage_date?.[0] || '';
            const damage_location = fields.damage_location?.[0] || '';
            const iban = fields.iban?.[0] || '';
            const coverd_risk = fields.coverd_risk?.[0] || '';
            const text = fields.text?.[0] || '';
            const data_raw = fields.formulars?.[0] || '';

            if (!customer_number || !contract_number ) {
                return res.status(400).json({ error: 'Missing required fields' });
            }

            const report_number = generateReportNumber(customer_number);

            // Fetch customer data using customer_number from form data
            const customerData = await fetch(
                `${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${customer_number}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            const customerDataJson = await customerData.json();

            if (customerDataJson.meta.pagination.total < 1) {
                throw new Error(`Failed to fetch customer`);
            }

            // Create a new report entry in Strapi
            const strapiResponse = await fetch(`${process.env.STRAPI_BASE_URL}/reports`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${process.env.STRAPI_API_TOKEN}`,
                },
                body: JSON.stringify({
                    data: {
                        customer_number,
                        contract_number,
                        agency_number,
                        damage_date,
                        damage_location,
                        iban,
                        coverd_risk,
                        text,
                        report_number,
                        external_report_number,
                        data_raw
                    },
                }),
            });

            if (!strapiResponse.ok) {
                let errorDetails;
                try {
                    errorDetails = await strapiResponse.json();
                } catch {
                    throw new Error(`Failed to create report: ${strapiResponse.statusText}`);
                }
                throw new Error(
                    `Failed to create report: ${errorDetails.error?.message || strapiResponse.statusText}`
                );
            }

            const createdReport = await strapiResponse.json().then(json => json.data);

/*             await mailManager.sendReport({
                subject: `Schadensmeldung für Vertrag ${contract_number}`,
                contract_number: contract_number,
                customer: customerDataJson.data[0],
                reportData: createdReport
            }); */

            const agent_number = fields.agent_number?.[0] || ''
            const authUser = await permissionManager.getAuthUser(token)

            const [uploadedFilePaths] = await Promise.all([
                uploadFiles({ files, contract_number, report_number, agent_number }),
                createPrechecks({ report: createdReport, token, authUser }),
            ])

            res.status(200).json({
                message: 'Report successfully submitted.',
                data: createdReport,
                uploadedFilePaths,
            });
        });
    } catch (error) {
        console.error('Error in report API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing your report.' });
    }
}

type UploadFilesParams = {
    files: Files,
    report_number: string,
    agent_number: string,
    contract_number: string
}
const uploadFiles = async ({ files, contract_number, report_number, agent_number }: UploadFilesParams) => {
    const fileOrFiles = files.file as formidable.File | formidable.File[] | undefined;
    if (!fileOrFiles) return []

    const uploadedFilePaths: string[] = [];
    const filesToUpload = Array.isArray(fileOrFiles) ? fileOrFiles : [fileOrFiles];

    await Promise.all(filesToUpload.map(
        async (file) => {
            const fileBuffer = await fs.promises.readFile(file.filepath);
            const fileData = new Uint8Array(fileBuffer);
            const fileName = file.originalFilename;
            const filePath = `report_files/${report_number}_${fileName}`;

            await fileManager.uploadFile(fileData, filePath);
            uploadedFilePaths.push(filePath);

            await fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/attachments`,
                'attachment',
                process.env.STRAPI_API_TOKEN,
                'POST',
                JSON.stringify({
                    data: {
                        agent_number,
                        contract_number,
                        bucket_path: filePath,
                        type: 'report',
                        report_number,
                    },
                })
            );
        }
    ))

    return uploadedFilePaths;
}

type CreatePrechecksParams = {
    report: ReportData
    authUser: any
    token: string
}
const createPrechecks = async ({ report, token, authUser }: CreatePrechecksParams) => {
    const httpClient = new HttpClient(token)
    const contractsRepository = new ContractsRepository(httpClient, authUser)
    const precheckTemplatesRepository = new ReportPrecheckTemplatesRepository(token)
    const prechecksRepository = new ReportPrechecksRepository(httpClient)
    const prechecksService = new ReportPrechecksService(contractsRepository, precheckTemplatesRepository, prechecksRepository, authUser)

    return prechecksService.createForReport(report)
}