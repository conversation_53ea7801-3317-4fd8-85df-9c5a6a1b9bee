// src/pages/api/report/[report_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing report number' });
    }

    if (!req.headers.authorization) {
        return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = req.headers.authorization!.split(" ")[1];

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        let strapiUrl = `${process.env.STRAPI_BASE_URL}/reports?filters[report_number][$eq]=${report_number}`;

        if (authUser && !isAdmin) {
            strapiUrl += `&filters[agency_number][$eq]=${authUser['agency_number']}`;
        }

        const reportData = await fetchDataFromAPI(strapiUrl, 'report', token);

        if (!reportData || reportData.length === 0) {
            return res.status(404).json({ error: 'Report not found' });
        }

        res.status(200).json(reportData[0]);

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const data = await response.json();
        return data.data || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}
