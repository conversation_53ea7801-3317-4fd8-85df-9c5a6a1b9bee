// src/pages/api/report/[report_number]/prechecks/[precheck_id].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {badRequest, internalServerError, notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'GET') {
            return await GET(req, res)
        }
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['GET', 'POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res)
    }
}

type QueryParams = {
    report_number: string,
}
const GET = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number } = req.query as QueryParams;
    const token = getToken.onServer(req)

    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const response = await fetch(`${process.env.STRAPI_BASE_URL}/report-summaries?filters[report_number][$eq]=${report_number}`, {
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
    })
    if (!response.ok) {
        const errorText = await response.text();
        return res.status(response.status).json({ message: errorText });
    }
    const strapiResponse = await response.json()

    return res.status(200).json(strapiResponse.data);
}


const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number } = req.query as QueryParams;
    const token = getToken.onServer(req)

    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const response = await fetch(`${process.env.STRAPI_BASE_URL}/report-summaries`, {
        method: 'POST',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: req.body }),
    })
    if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to create report summary', errorText);
        return res.status(response.status).json({ message: errorText });
    }
    const strapiResponse = await response.json()

    return res.status(200).json(strapiResponse.data);
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
}
