import {NextApiRequest, NextApiResponse} from "next";
import {badRequest, internalServerError, notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {permissionManager} from "@/utils/permissionManager";
import {HttpClient} from "@/utils/HttpClient";
import {ReportAttachmentsRepository} from "@/server/reports/ReportAttachmentsRepository";
import {fileManager} from "@/utils/fileManager";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'GET') {
            return await GET(req, res)
        }

        return notAllowed(res, req, ['GET'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res);
    }
}

type QueryParams = {
    report_number: string;
    attachment_id: string
}
const GET = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { attachment_id, report_number } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const reportAttachmentsRepository = new ReportAttachmentsRepository(httpClient)
    const attachment = await reportAttachmentsRepository.findById(attachment_id)

    const downloadStream = await fileManager.downloadFile(attachment.bucket_path)

    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader(
        'Content-Disposition',
        `attachment; filename="${attachment.name}"`
    );

    // Pipe the S3 stream to the response
    downloadStream.on('error', (error: any) => {
        console.error('Failed to download the file', { error, attachment });
        internalServerError(res)
    });

    downloadStream.pipe(res);
}

const validateRequest = (req: NextApiRequest) => {
    const { attachment_id, report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report_number in the path'
    }
    if (!attachment_id || typeof attachment_id !== 'string') {
        return 'Invalid or missing attachment_id in the path'
    }
}
