import {NextApiRequest, NextApiResponse} from "next";
import {badRequest, internalServerError, notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {permissionManager} from "@/utils/permissionManager";
import {HttpClient} from "@/utils/HttpClient";
import {ReportAttachmentsRepository} from "@/server/reports/ReportAttachmentsRepository";
import {ReportAttachment} from "@/types";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'GET') {
            return await GET(req, res)
        }

        return notAllowed(res, req, ['GET'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res);
    }
}

type QueryParams = {
    report_number: string;
    related_to?: ReportAttachment['related_to'],
    timeline_entry_id?: string,
}
const GET = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { related_to, timeline_entry_id, report_number } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const reportAttachmentsRepository = new ReportAttachmentsRepository(httpClient)
    const attachments = await reportAttachmentsRepository.findAll({
        related_to: related_to ?? 'report',
        related_entity_id: related_to === 'timeline_entry' ? timeline_entry_id : report_number,
    })

    return res.status(200).json(attachments);
}

const validateRequest = (req: NextApiRequest) => {
    const { related_to, timeline_entry_id, report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report_number in the path'
    }
    if (related_to === 'timeline_entry' && typeof timeline_entry_id !== 'string') {
        return 'Invalid or missing timeline_entry_id'
    }
}
