// src/pages/api/report/[report_number]/prechecks/[precheck_id].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {badRequest,  notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {HttpClient} from "@/utils/HttpClient";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'PUT') {
        return PUT(req, res)
    }

    return notAllowed(res, req, ['PUT'])
}

type QueryParams = {
    report_number: string,
    precheck_id: string
}
const PUT = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number, precheck_id } = req.query as QueryParams;

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const prechecksRepository = new ReportPrechecksRepository(httpClient)

    const updatedPrecheck = await prechecksRepository.set(precheck_id, req.body);

    return res.status(200).json(updatedPrecheck);
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number, precheck_id } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report_number'
    }
    if (!precheck_id || typeof precheck_id !== 'string') {
        return 'Invalid or missing precheck_id'
    }
}
