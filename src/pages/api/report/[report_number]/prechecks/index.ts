import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {badRequest, notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {HttpClient} from "@/utils/HttpClient";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'GET') {
        return GET(req, res);
    }

    return notAllowed(res, req,['GET'] )
}

type QueryParams = {
    report_number: string,
}
const GET = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number } = req.query as QueryParams;

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const prechecksRepository = new ReportPrechecksRepository(httpClient)
    const prechecks = await prechecksRepository.findAll({ report_number });

    res.status(200).json(prechecks);
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
}