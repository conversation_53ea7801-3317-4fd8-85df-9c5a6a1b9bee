// src/pages/api/report/[report_number]/timeline-entries/analyze.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {ReportTimelineEntriesRepository} from "@/server/reports/ReportTimelineEntriesRepository";
import {AnalyzeTimelineEntryRequest, AnalyzeTimelineEntryRequestFile, JobResponse} from "@/types";
import {getExpectedDocumentTypes} from "@/common";
import {fileManager} from "@/utils/fileManager";
import {notAllowed, unauthorized, badRequest, internalServerError} from "@/server/responses";
import {streamToBase64} from "@/utils/streamToBase64";
import {getToken} from "@/utils/getToken";
import {HttpClient} from "@/utils/HttpClient";
import {ReportAttachmentsRepository} from "@/server/reports/ReportAttachmentsRepository";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res)
    }
}


type QueryParams = {
    report_number: string,
    entry_id: string
}
const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError);
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { report_number, entry_id } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const analyzeResponse = await analyzeTimelineEntry(httpClient, entry_id)

    if (analyzeResponse.status !== 'STARTED') {
        return internalServerError(res, {
            message: `The job has not been started`,
            status: analyzeResponse.status,
        });
    }

    return res.status(200).json(analyzeResponse);
}

const analyzeTimelineEntry = async (httpClient: HttpClient, entry_id: string): Promise<JobResponse> => {
    const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
    const reportAttachmentsRepository = new ReportAttachmentsRepository(httpClient)

    const getFiles = async () => {
        const attachments = await reportAttachmentsRepository.findAll({
            related_to: 'timeline_entry',
            related_entity_id: entry_id
        })
        const promises = attachments.map<Promise<AnalyzeTimelineEntryRequestFile>>(async ({ name, bucket_path }) => {
            const fileStream = await fileManager.downloadFile(bucket_path)
            const base64 = await streamToBase64(fileStream)
            return {
                name,
                base64,
                url: 'https://pdfobject.com/pdf/sample.pdf', // TODO: ask Ole to remove validation and remove this line
            }
        })

        return Promise.all(promises);
    }
    const [entry, files] = await Promise.all([
        timelineEntriesRepository.findById(entry_id),
        getFiles()
    ]);

    const body: AnalyzeTimelineEntryRequest = {
        entry: {
            ...entry,
            files
        },
        expectedDocumentTypes: getExpectedDocumentTypes()
    }
    return httpClient.request('http://localhost:8000/doc-analyze', {
        method: 'POST',
        body,
    })
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number, entry_id } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
    if (!entry_id || typeof entry_id !== 'string') {
        return 'Invalid or missing entry id'
    }
}
