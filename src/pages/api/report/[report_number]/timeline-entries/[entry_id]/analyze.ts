// src/pages/api/report/[report_number]/timeline-entries/analyze.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {ReportTimelineEntriesRepository} from "@/server/reports/ReportTimelineEntriesRepository";
import {
    AnalyzeTimelineEntryRequest,
    AnalyzeTimelineEntryRequestFile,
    JobInitResponse,
    TimelineEntryAnalysisJobResponse
} from "@/types";
import {getExpectedDocumentTypes} from "@/common";
import {fileManager} from "@/utils/fileManager";
import {notAllowed, unauthorized, badRequest, internalServerError} from "@/server/responses";
import {streamToBase64} from "@/utils/streamToBase64";
import {getToken} from "@/utils/getToken";
import {HttpClient} from "@/utils/HttpClient";
import {ReportAttachmentsRepository} from "@/server/reports/ReportAttachmentsRepository";

const LOCAL_API_BASE = 'http://localhost:8000';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res)
    }
}


type QueryParams = {
    report_number: string,
    entry_id: string
}
const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError);
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { report_number, entry_id } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const jobInitResponse = await startEntryAnalysis(httpClient, entry_id)

    if (jobInitResponse.status !== 'STARTED') {
        return internalServerError(res, {
            message: `The job has not been started`,
            status: jobInitResponse.status,
        });
    }

    const jobResult = await pollForJobResult(httpClient, jobInitResponse.job_id)
    if (!jobResult) {
        throw new Error('Timeout: Job did not finish in time.');
    }

    return res.status(200).json(jobResult);
}

const startEntryAnalysis = async (httpClient: HttpClient, entry_id: string): Promise<JobInitResponse> => {
    const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
    const reportAttachmentsRepository = new ReportAttachmentsRepository(httpClient)

    const getFiles = async () => {
        const attachments = await reportAttachmentsRepository.findAll({
            related_to: 'timeline_entry',
            related_entity_id: entry_id
        })
        const promises = attachments.map<Promise<AnalyzeTimelineEntryRequestFile>>(async ({ name, bucket_path }) => {
            const fileStream = await fileManager.downloadFile(bucket_path)
            const base64 = await streamToBase64(fileStream)
            return {
                name,
                base64,
                url: 'https://pdfobject.com/pdf/sample.pdf', // TODO: ask Ole to remove validation and remove this line
            }
        })

        return Promise.all(promises);
    }
    const [entry, files] = await Promise.all([
        timelineEntriesRepository.findById(entry_id),
        getFiles()
    ]);

    const body: AnalyzeTimelineEntryRequest = {
        entry: {
            ...entry,
            files
        },
        expectedDocumentTypes: getExpectedDocumentTypes()
    }
    return httpClient.request(`${LOCAL_API_BASE}/doc-analyze`, {
        method: 'POST',
        body,
    })
}

const pollForJobResult = async (httpClient: HttpClient, jobId: string) => {
    const maxAttempts = 30;
    let attempt = 0;

    while (attempt < maxAttempts) {
        await new Promise(res => setTimeout(res, 5000));
        attempt++;

        const jobResponse: TimelineEntryAnalysisJobResponse = await httpClient.request(`${LOCAL_API_BASE}/jobs/${jobId}`);

        if (jobResponse.status === 'FAILED') {
            throw new Error('Job failed');
        }
        if (jobResponse.status === 'FINISHED') {
            return jobResponse;
        }
    }
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number, entry_id } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
    if (!entry_id || typeof entry_id !== 'string') {
        return 'Invalid or missing entry id'
    }
}
