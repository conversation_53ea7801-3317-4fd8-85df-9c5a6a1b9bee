// src/pages/api/report/[report_number]/timeline-entries/liability-insurances.ts
import {permissionManager} from '@/utils/permissionManager';
import {NextApiRequest, NextApiResponse} from 'next';
import formidable from "formidable";
import fs from "fs";
import {fileManager} from "@/utils/fileManager";
import {AuthorType, TimelineEntry, User} from "@/types";
import {badRequest, internalServerError, notAllowed, unauthorized} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {ReportTimelineEntriesRepository} from "@/server/reports/ReportTimelineEntriesRepository";
import {HttpClient} from "@/utils/HttpClient";
import {ReportAttachmentsRepository} from "@/server/reports/ReportAttachmentsRepository";

export const config = {
    api: {
        bodyParser: false, // Disable Next.js body parsing
    },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'GET') {
            return await GET(req, res)
        }
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['GET', 'POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res);
    }
}


type QueryParams = {
    report_number: string,
}
const GET = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { report_number } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
    const entries = await timelineEntriesRepository.findAll({ report_number })

    return res.status(200).json(entries);
}

const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const token = getToken.onServer(req)
    if (!token) {
        return unauthorized(res);
    }

    const { report_number } = req.query as QueryParams;
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const form = formidable({ multiples: true });

    form.parse(req, async (err, fields, { files: fileOrFiles }) => {
        if (err) {
            console.error('Formidable failed to parse the request:', err);
            return internalServerError(res)
        }

        const authUser = await permissionManager.getAuthUser(token)
        const httpClient = new HttpClient(token);
        const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
        const attachmentsRepository = new ReportAttachmentsRepository(httpClient)

        const entryToCreate = fieldsToEntry(fields, authUser)
        const createdEntry = await timelineEntriesRepository.create(entryToCreate)

        const files = ([fileOrFiles ?? []]).flat()
        await Promise.all(
            files.map(file => uploadFile(file, report_number, createdEntry.id, attachmentsRepository))
        )

        res.status(200).json(createdEntry)
    })
}

const fieldsToEntry = (fields: formidable.Fields, authUser: User): TimelineEntry => {
    const entry: TimelineEntry = Object.entries(fields).reduce((acc, [key, rawValue]) => {
        let value: any = rawValue?.[0]
        if (key === 'entry_type' || key === 'author_type') {
            value = Number(value)
        }

        return { ...acc, [key]: value }
    }, {} as any);

    if (entry.author_type === AuthorType.AGENT) {
        entry.author_id = authUser.agent_number
    }

    return entry
}

const uploadFile = async (file: formidable.File, report_number: string, entry_id: string, attachmentsRepository: ReportAttachmentsRepository) => {
    const fileName = file.originalFilename ?? `unnamed${Math.random()}`;
    const uploadPath = `reports/${report_number}/timeline_entries/${entry_id}/attachments/${fileName}`;
    const fileBuffer = await fs.promises.readFile(file.filepath);
    const fileData = new Uint8Array(fileBuffer);

    await fileManager.uploadFile(fileData, uploadPath);

    await attachmentsRepository.create({
        id: '',
        name: fileName,
        bucket_path: uploadPath,
        related_to: "timeline_entry",
        related_entity_id: entry_id,
    })
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
}
