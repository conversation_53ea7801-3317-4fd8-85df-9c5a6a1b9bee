// src/pages/api/report/[report_number]/create/coverage-assesments.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { permissionManager } from "@/utils/permissionManager";
import { unauthorized } from "@/server/responses";
import { HttpClient } from "@/utils/HttpClient";
import { ReportsRepository } from "@/server/reports/ReportsRepository";
import { ContractsRepository } from "@/server/ContractsRepository";
import { User } from "@/types";
import { InsuranceConditionsRepository } from "@/server/InsuranceConditionsRepository";
import { fileManager } from "@/utils/fileManager";
import { streamToBase64 } from "@/utils/streamToBase64";
import { ReportSummariesRepository } from "@/server/reports/ReportSummariesRepository";

const LOCAL_API_BASE = 'http://localhost:8002';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { report_number } = req.query;
    const token = req.headers.authorization?.split(' ')[1];

    if (!report_number || typeof report_number !== 'string') {
        return res.status(400).json({ error: 'Missing or invalid report_number' });
    }

    if (!token) {
        return res.status(401).json({ error: 'Authorization token missing' });
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const authUser = await permissionManager.getAuthUser(token)

    try {
        const inputData = await getCoverageAssessmentInput(httpClient, authUser, report_number);

        // 1. start coverage check (async)
        const coverageInitRes = await fetch(`${LOCAL_API_BASE}/coverage-check`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(inputData)
        });

        if (!coverageInitRes.ok) {
            const details = await coverageInitRes.text();
            throw new Error(`Start failed: ${details}`);
        }

        const { job_id } = await coverageInitRes.json();

        // 2. poll job status
        const maxAttempts = 30;
        let attempt = 0;
        let jobStatus = 'PENDING';
        let jobResult = null;

        while (attempt < maxAttempts && jobStatus !== 'SUCCESS') {
            await new Promise(res => setTimeout(res, 5000));
            attempt++;

            const jobRes = await fetch(`${LOCAL_API_BASE}/jobs/${job_id}`);
            if (!jobRes.ok) {
                throw new Error(`Polling failed at attempt ${attempt}`);
            }
            const data = await jobRes.json();

            if (!data.status && data.coverage_assessment) {
                jobStatus = 'SUCCESS';
                jobResult = data;
                break;
            }

            if (data.status === 'FAILED') {
                throw new Error('Job failed');
            }
        }

        if (jobStatus !== 'SUCCESS') {
            throw new Error('Timeout: Job did not finish in time.');
        }

        // 3. sending result to database
        const strapiRes = await fetch(`${process.env.STRAPI_BASE_URL}/report-coverage-assessments`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data: {
                    ...jobResult,
                    report_number: report_number
                }
            }),
        });

        if (!strapiRes.ok) {
            const errorDetails = await strapiRes.text();
            throw new Error(`Strapi Save Error: ${errorDetails}`);
        }

        const result = await strapiRes.json();
        return res.status(201).json(result);

    } catch (error: any) {
        console.error('Error:', error);
        return res.status(500).json({ error: error.message || 'Internal server error' });
    }
}

async function getCoverageAssessmentInput(httpClient: HttpClient, authUser: User, report_number: string) {
    const reportsRepository = new ReportsRepository(httpClient);
    const contractsRepository = new ContractsRepository(httpClient, authUser)
    const conditionsRepository = new InsuranceConditionsRepository(httpClient)
    const summariesRepository = new ReportSummariesRepository(httpClient)

    const getAttachments = async () => {
        try {
            const { contract_number } = await reportsRepository.findOne({ report_number })
            const { contract_type } = await contractsRepository.findByContractNumber(contract_number);
            const insuranceConditions = await conditionsRepository.findAll({
                relevant_contract_types: {
                    $contains: contract_type
                }
            })

            return Promise.all(
                insuranceConditions.map(async ({ id, bucket_path, file_name }) => {
                    const base64 = await fileManager.downloadFile(bucket_path).then(streamToBase64)
                    return {
                        id,
                        file_name,
                        base64
                    }
                })
            )
        } catch (error) {
            console.error(error);
            return []
        }
    }
    const getLatestSummary = async (): Promise<string> => {
        try {
            const summary = await summariesRepository.findOne({ report_number }, {
                sort: ['timestamp:desc'],
                pagination: {
                    limit: 1
                }
            })
            return summary.text
        } catch (error) {
            console.error(error);
            return ''
        }
    }
    const getContractDetails = async () => {
        try {
            const { contract_number } = await reportsRepository.findOne({ report_number })
            const contract = await contractsRepository.findByContractNumber(contract_number)
            return contract
        } catch (error) {
            console.error(error);
            return ''
        }
    }

    const [attachments, last_timelineSummary, contract] = await Promise.all([
        getAttachments(),
        getLatestSummary(),
        getContractDetails()
    ]);

    return {
        claim_id: `${report_number} Alpha`, // TODO: unhardcode company name
        contract_details: buildContractDetails(contract),
        claim_details: {
            damage_amount: "1500",
            damage_description: "WE02 - Whg. Frau Musterfrau: Der VN hat uns folgendes geschildert: ...",
            damage_city: "Straße 1",
            damage_date: "2023-10-01"
        },
        last_timelineSummary,
        attachments
    };
}

function buildContractDetails(contract: any) {
    switch (contract.contract_type) {
        case "hausrat":
            return {
                coverage: ``,
                deductible: 500,
                risk: `Risikoort: ; ` +
                    `Risikobezeichnung:${contract.risk_description}; ` +
                    `Bauartklasse: ${contract.building_class}; ` +
                    `Wohnfläche: ${contract.living_area}; `,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Hausratsversicherung`,
                product_name: `ALPHA HAUSRATVERSICHERUNG EXPERT`,
                inclusive: ``
            };

        case "wohngebaeude":
            return {
                coverage: ``,
                deductible: 500,
                risk: `Risikoort: ; ` +
                    `Risikobezeichnung:${contract.risk_description}; ` +
                    `Bauartklasse: ${contract.building_class}` +
                    `Baujahr: ${contract.build_year}`,
                additional_agreements: `${contract.additional_agreements}; `,
               product_id: ``,
                category: "Wohngebäude-Versicherung",
                product_name: '„ALPHA“ WOHNGEBÄUDE EXPERT',
                inclusive: ``
            };

        case "tierhalterhaftpflicht":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: "Tierhalterhaftpflichtversicherung",
                product_name: `ALPHA TIERHALTERHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "privathaftpflicht":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Privathaftpflichtversicherung`,
                product_name: `ALPHA PRIVATHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "haus_und_grundbesitzerhaftpflicht":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Haus- und Grundbesitzerhaftpflichtversicherung`,
                product_name: `ALPHA HAUS- UND GRUNDBESITZERHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "bauleistung":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Bauleistung`,
                product_name: `ALPHA BAULEISTUNG EXPERT`,
                inclusive: ``
            };

        case "bauherrenhaftpflicht":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Bauherrenhaftpflichtversicherung`,
                product_name: `ALPHA BAUHERRENHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "geschaeftsversicherung":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Geschäftsversicherung`,
                product_name: `ALPHA GESCHÄFTSVERSICHERUNG EXPERT`,
                inclusive: ``
            };

        case "gebaeudeversicherung":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Haus- und Grundbesitzerhaftpflichtversicherung`,
                product_name: `ALPHA HAUS- UND GRUNDBESITZERHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "betriebshaftpflicht":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Betriebshaftpflichtversicherung`,
                product_name: `ALPHA BETRIEBSHAFTPFLICHT EXPERT`,
                inclusive: ``
            };

        case "unfallversicherung":
            return {
                coverage: ``,
                deductible: 500,
                risk: ``,
                additional_agreements: `${contract.additional_agreements}; `,
                product_id: ``,
                category: `Unfallversicherung`,
                product_name: ``,
                inclusive: ``
            };

        default:
            return {
                coverage: contract.coverage_amount || "unbekannt",
                deductible: contract.deductible || "unbekannt",
                risk: "Keine Risikoinfos verfügbar",
                additional_agreements: "Keine Zusatzvereinbarungen verfügbar",
                product_id: "Keine Product ID verfügbar",
                category: "Keine Kategorie verfügbar",
                product_name: "Keine Produktname verfügbar",
                inclusive: "Keine weiterer Service verfügbar"
            };
    }
}
