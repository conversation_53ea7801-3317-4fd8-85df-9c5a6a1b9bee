// src/pages/api/report/[report_number]/prechecks/[precheck_id].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {
    AuthorType,
    CreateReportSummaryRequest,
    ReportSummary,
    ReportSummaryJobResponse,
} from "@/types";
import {ReportTimelineEntriesRepository} from "@/server/reports/ReportTimelineEntriesRepository";
import {notAllowed, unauthorized, badRequest, internalServerError} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";
import {HttpClient} from "@/utils/HttpClient";
import {ReportSummariesRepository} from "@/server/reports/ReportSummariesRepository";

const LOCAL_API_BASE = 'http://localhost:8001';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res);
    }
}

type QueryParams = {
    report_number: string,
}
const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number } = req.query as QueryParams;
    const token = getToken.onServer(req)

    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
    const prechecksRepository = new ReportPrechecksRepository(httpClient)
    const summariesRepository = new ReportSummariesRepository(httpClient)

    const [timelineEntries, prechecks] = await Promise.all([
        timelineEntriesRepository.findAll({report_number}),
        prechecksRepository.findAll({ report_number })
    ])

    const body: CreateReportSummaryRequest = {
        prechecks,
        timeline: timelineEntries,
        contractSummary: {
            id: '',
            contract_number: '',
            timestamp: '',
            text: '',
        }
    }
    const jobInitResponse = await httpClient.request(`${LOCAL_API_BASE}/timeline-summarize`, {
        method: 'POST',
        body,
    })
    if (jobInitResponse.status !== 'STARTED') {
        return internalServerError(res, {
            message: `The job has not been started`,
            status: jobInitResponse.status,
        });
    }

    const jobResult = await pollForJobResult(httpClient, jobInitResponse.job_id)
    if (!jobResult) {
        throw new Error('Timeout: Job did not finish in time.');
    }

    const summaryToCreate: ReportSummary = {
        id: '',
        report_number: report_number as string,
        author_type: AuthorType.AI,
        text: jobResult.timelineSummary || '',
        timestamp: new Date().toISOString(),
    }
    const createdSummary = await summariesRepository.create(summaryToCreate)

    return res.status(200).json(createdSummary);
}

const pollForJobResult = async (httpClient: HttpClient, jobId: string) => {
    const maxAttempts = 30;
    let attempt = 0;

    while (attempt < maxAttempts) {
        await new Promise(res => setTimeout(res, 5000));
        attempt++;

        const jobResponse: ReportSummaryJobResponse = await httpClient.request(`${LOCAL_API_BASE}/timeline-jobs/${jobId}`);

        if (jobResponse.status === 'FAILED') {
            throw new Error('Job failed');
        }
        if (jobResponse.status === 'FINISHED') {
            return jobResponse;
        }
    }
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
}
