// src/pages/api/report/[report_number]/prechecks/[precheck_id].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import {CreateReportSummaryRequest } from "@/types";
import {ReportTimelineEntriesRepository} from "@/server/reports/ReportTimelineEntriesRepository";
import {notAllowed, unauthorized, badRequest, internalServerError} from "@/server/responses";
import {getToken} from "@/utils/getToken";
import {ReportPrechecksRepository} from "@/server/reports/prechecks/ReportPrechecksRepository";
import {HttpClient} from "@/utils/HttpClient";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req.method === 'POST') {
            return await POST(req, res)
        }

        return notAllowed(res, req, ['POST'])
    } catch (error) {
        console.error('Error in API handler:', error);
        return internalServerError(res);
    }
}

type QueryParams = {
    report_number: string,
}
const POST = async (req: NextApiRequest, res: NextApiResponse) => {
    const validationError = validateRequest(req)
    if (validationError) {
        return badRequest(res, validationError)
    }

    const { report_number } = req.query as QueryParams;
    const token = getToken.onServer(req)

    if (!token) {
        return unauthorized(res);
    }
    const isPermitted = await permissionManager.hasReportPermissions(report_number, token)
    if (!isPermitted) {
        return unauthorized(res);
    }

    const httpClient = new HttpClient(token);
    const timelineEntriesRepository = new ReportTimelineEntriesRepository(httpClient)
    const prechecksRepository = new ReportPrechecksRepository(httpClient)

    const [timelineEntries, prechecks] = await Promise.all([
        timelineEntriesRepository.findAll({report_number}),
        prechecksRepository.findAll({ report_number })
    ])

    const payload: CreateReportSummaryRequest = {
        prechecks,
        timeline: timelineEntries,
        contractSummary: {
            id: '',
            contract_number: '',
            timestamp: '',
            text: '',
        }
    }
    const response = await fetch('http://localhost:8001/timeline-summarize', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
    })
    if (!response.ok) throw new Error(`Failed to fetch('http://localhost:8001/timeline-summarize': ${await response.text()}`);

    const job = await response.json()

    return res.status(200).json(job);
}

const validateRequest = (req: NextApiRequest) => {
    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return 'Invalid or missing report number'
    }
}
