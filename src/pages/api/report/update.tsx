// src/pages/api/report/update.ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';
import { ReportData } from "@/types";
import { dbWriter } from '@/utils/dbWriter';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'PUT') {
        try {
            const { reportData } = req.body as { reportData: ReportData };
            if (!permissionManager.hasReportPermissions(reportData.report_number, token) && await permissionManager.isAdmin(token)) {
                res.status(401)
            }

            if (!reportData || typeof reportData !== 'object') {
                res.status(400).json({ error: 'Invalid or missing request body' });
                return;
            }

            const newReportData = await dbWriter.updateDocument(token, "reports", reportData.documentId, reportData, "report", 'report_number');

            if (newReportData) {
                res.status(200).json(newReportData);
            } else {
                throw new Error(newReportData);
            }
        } catch (error) {
            console.error('Error updating report in Strapi:', error);
            res.status(500).json({ error: 'Failed to update report' });
        }
    } else {
        res.setHeader('Allow', ['PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

