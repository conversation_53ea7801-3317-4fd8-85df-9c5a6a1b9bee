// src/pages/api/report/get/revisions/[report_number].ts
import { getRevisions } from "@/utils/mongoDB/mongoDBFunctions";
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing report number' });
    }

    if (!req.headers.authorization) {
        res.status(401)
        return
    }
    const token = req.headers.authorization!.split(" ")[1]

    try {

        const hasPermission = permissionManager.hasReportPermissions(report_number, token)
        if (!hasPermission) {
            res.status(401)
        }

        const revs = await getRevisions(report_number, 'report_revision')

        res.status(201).json(revs)

    } catch (error) {
        console.error('Error in API handler:', error);
        res.status(500).json({ error: 'An error occurred while processing the request.' });
    }
}