// src/pages/api/report/get/coverage-assessment/[report_number].ts
import { NextApiRequest, NextApiResponse } from 'next';
import { permissionManager } from '@/utils/permissionManager';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    const { report_number } = req.query;

    if (!report_number || typeof report_number !== 'string') {
        return res.status(400).json({ error: 'Invalid or missing report number' });
    }

    if (!req.headers.authorization) {
        return res.status(401).json({ error: 'Authorization token missing' });
    }

    const token = req.headers.authorization.split(" ")[1];

    try {
        const authUser = await permissionManager.getAuthUser(token);
        const isAdmin = permissionManager.isUserAdmin(authUser);

        let strapiUrl = `${process.env.STRAPI_BASE_URL}/report-coverage-assessments?filters[report_number][$eq]=${report_number}`;

        if (authUser && !isAdmin) {
            strapiUrl += `&filters[agency_number][$eq]=${authUser['agency_number']}`;
        }

        const coverageData = await fetchDataFromAPI(strapiUrl, 'coverage-assessment', token);

        if (!coverageData || coverageData.length === 0) {
            return res.status(404).json({ error: 'Coverage Assessment not found' });
        }

        return res.status(200).json(coverageData[0]); // direkt zurückgeben

    } catch (error) {
        console.error('API Fehler:', error);
        return res.status(500).json({ error: 'Ein Fehler ist aufgetreten.' });
    }
}

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(`Fehler beim Abruf von ${dataType}: ${errorDetails.error?.message || response.statusText}`);
        }

        const result = await response.json();
        return result.data;
    } catch (error) {
        console.error(`Fetch Error (${dataType}):`, error);
        throw error;
    }
}
