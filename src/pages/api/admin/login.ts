import { prisma } from "@/server/prisma";
import { sendEmail } from "@/utils/mailManager";
import { renderEmailTemplate } from "@/emails/renderEmailTemplate";
import {urls} from "@/server/urls";
import {EndpointHandler} from "@/server/domain";
import {createEndpoint} from "@/server/createEndpoint";
import * as z from "zod";

const RequestBodySchema = z.object({
    email: z.email(),
});
type RequestBody = z.infer<typeof RequestBodySchema>

const POST: EndpointHandler = {
    schemas: {
        body: RequestBodySchema,
    },
    execute: async ({ res, req, accessTokenRepository, logger }) => {
        const { email } = req.body as RequestBody
        const referrer = await prisma.referrer.findUniqueOrThrow({ where: { email } });

        if (referrer?.isAdmin) {
            const {token} = await accessTokenRepository.create({
                referrerId: referrer.id,
            });
            const link = urls.admin.access(token)

            await sendThemedEmail(email, link);
        } else {
            logger.log(
                'info',
                '/api/admin/login: Someone tried to log in as admin with a non-admin email:',
                email
            )
        }

        return res.status(200).json({ result: 'If this email belongs to an admin, a login link has been sent' });
    }
}

export default createEndpoint({ POST })

async function sendThemedEmail(to: string, ctaUrl: string) {
    const html = await renderEmailTemplate({
        ctaUrl,
        group: 'referrerLogin'
    });

    await sendEmail({
        to,
        subject: "Ihr Zugang zu Refeo",
        html,
    });
}
