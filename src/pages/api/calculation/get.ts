// src/pages/api/calculation/paramters.ts
import { getCalculationParameters } from '@/utils/contractCalculator';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {

    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const { valid_from } = req.query

    const token = req.headers.authorization!.split(" ")[1]

    if (req.method === 'GET') {
        try {
            const data = await getCalculationParameters(token, valid_from as string)
            res.status(200).json(data);
        } catch (error) {
            console.error('Error fetching calculation parameters:', error);
            res.status(500).json({ error: 'Failed to fetch calculation parameters' });
        }
    } else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
