// src/pages/api/calculation/paramters.ts
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        if (!req.headers.authorization) return 'Missing authorization header';
    },

    execute: async ({ req, res, db }) => {
        const { validFrom } = req.query;

        const calculationParameterService = new CalculationParameterService(db);

        const data = await calculationParameterService.getCalculationParameters(
            validFrom as string
        );
        res.status(200).json(data);
    },
};

export default createEndpoint({ GET });
