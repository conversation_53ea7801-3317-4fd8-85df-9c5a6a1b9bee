// src/pages/api/calculation/preview.ts
import { type Contract } from '@/generated/prisma-postgres';
import { CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import {
    calculateContract,
    calculateContractWithInvoiceAmount,
    calculateContractWithPremie,
    calculateContractWithPremieAndInvoiceAmount,
} from '@/utils/contractCalculator';

const POST: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';
    },

    execute: async ({ req, res, db, auth }) => {
        const contract = req.body.contractData as Contract;

        const isAdmin = auth.roles.includes('asevi-admin');

        const {
            premie,
            premieHouseholdTech,
            premiePvSystem,
            premieGlassInsurance,
            invoiceAmount,
        } = contract;

        const calculationParameterService = new CalculationParameterService(db);
        let contractData: Contract = await calculateContract(
            contract,
            calculationParameterService
        );
        // overwrite premien if set by admin
        let temp_premie = premie || 0;
        if (isAdmin) {
            if (premieHouseholdTech != null) {
                contractData.premieHouseholdTech = premieHouseholdTech;
            }
            if (premiePvSystem != null) {
                contractData.premiePvSystem = premiePvSystem;
            }
            if (premieGlassInsurance != null) {
                contractData.premieGlassInsurance = premieGlassInsurance;
            }
        }

        if (premieHouseholdTech || premiePvSystem || premieGlassInsurance) {
            temp_premie = 0;
        }

        if (premieHouseholdTech) {
            temp_premie += premieHouseholdTech;
        }

        if (premiePvSystem) {
            temp_premie += premiePvSystem;
        }

        if (
            premieHouseholdTech ||
            premiePvSystem ||
            premieGlassInsurance ||
            premie != null ||
            invoiceAmount
        ) {
            contractData.isIndividuallyCalculated = true;
        }

        if (premie != null && invoiceAmount == null && isAdmin) {
            contractData = await calculateContractWithPremie(
                contractData,
                calculationParameterService,
                temp_premie
            );
        }

        if (premie == null && invoiceAmount != null && isAdmin) {
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithInvoiceAmount(
                contractData,
                calculationParameterService
            );
        }

        if (premie != null && invoiceAmount != null && isAdmin) {
            contractData.premie = premie;
            contractData.invoiceAmount = invoiceAmount;
            contractData = await calculateContractWithPremieAndInvoiceAmount(
                contract,
                calculationParameterService
            );
        }
        res.status(200).json(contractData);
    },
};

export default createEndpoint({ POST });
