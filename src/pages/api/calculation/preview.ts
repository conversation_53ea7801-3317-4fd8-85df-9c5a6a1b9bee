// src/pages/api/calculation/preview.ts
import { ContractData } from '@/types';
import {
    calculateContract,
    calculateContractWithInvoiceAmount,
    calculateContractWithPremie,
    calculateContractWithPremieAndInvoiceAmount
} from '@/utils/contractCalculator';
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (!req.headers.authorization) {
        res.status(401).json({ error: 'Unauthorized: No token provided' });
        return;
    }

    const { contractData } = req.body as { contractData: ContractData };

    const token = req.headers.authorization!.split(" ")[1];
    const authUser = await permissionManager.getAuthUser(token);
    const isAdmin = permissionManager.isUserAdmin(authUser);

    if (req.method === 'POST') {
        const {
            premie,
            invoice_amount,
            premie_household_tech,
            premie_pv_system,
            premie_glass_insurance
        } = contractData;


        // Check contract access
        if (!(await permissionManager.hasCustomerPermissions(contractData.customer_number, token))) {
            res.status(401).json({ error: 'Unauthorized: No permission for this contract' });
            return;
        }

        try {
            let data = await calculateContract(contractData, token)
            // overwrite premien if set by admin
            let temp_premie = premie ??0
            if (isAdmin) {
                if (premie_household_tech != null) {
                    data.premie_household_tech = premie_household_tech
                }
                if (premie_pv_system != null) {
                    data.premie_pv_system = premie_pv_system
                }
                if (premie_glass_insurance != null) {
                    data.premie_glass_insurance = premie_glass_insurance
                }
            }

            if (premie_household_tech || premie_pv_system || premie_glass_insurance) {
                temp_premie = 0
            }

            if (premie_household_tech) {
                temp_premie += premie_household_tech
            }

            if (premie_pv_system) {
                temp_premie += premie_pv_system
            }

            if (premie_household_tech || premie_pv_system || premie_glass_insurance || premie != null || invoice_amount) {
                data.is_individually_calculated = true
            }

            if ((premie != null && invoice_amount == null && isAdmin)) {
                data = await calculateContractWithPremie(data, token, temp_premie)
            }

            if ((premie == null && invoice_amount != null && isAdmin)) {
                data.invoice_amount = invoice_amount
                data = await calculateContractWithInvoiceAmount(data, token)
            }

            if ((premie != null && invoice_amount != null && isAdmin)) {
                data.premie = premie
                data.invoice_amount = invoice_amount
                data = await calculateContractWithPremieAndInvoiceAmount(contractData, token)
            }

            res.status(200).json(contractData);
        } catch (error) {
            console.error('Error calculating contract:', error);
            res.status(500).json({ error: 'Failed to calculate contract' });
        }
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
