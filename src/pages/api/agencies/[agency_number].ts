// src/pages/api/agency/[agency_number].ts
import { permissionManager } from '@/utils/permissionManager';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    
    if (!req.headers.authorization) {
        res.status(401)
        return
    }

    const { agency_number } = req.query;
    const token = req.headers.authorization!.split(" ")[1]

    if (!agency_number || typeof agency_number !== 'string') {
        res.status(400).json({ error: 'Invalid or missing agency number' });
        return;
    }

    if (req.method === 'GET') {
        try {
            const authUser = await permissionManager.getAuthUser(token)
            const isAdmin = permissionManager.isUserAdmin(authUser)
            let strapiResponse: any | undefined

            if (authUser && isAdmin) {
                strapiResponse = await fetch(
                    `${process.env.STRAPI_BASE_URL}/agencies?filters[agency_number][$eq][0]=${agency_number}`,
                    {
                        method: 'GET',
                        headers: {
                            Authorization: `Bearer ${token}`
                        },
                    }
                );
            }

            if (strapiResponse.ok) {
                const data = await strapiResponse.json();
                if (data.length === 0) {
                    res.status(404).json({ message: 'agency not found' });
                    return;
                }
                res.status(200).json(data.data[0]);
            } else {
                const errorDetails = await strapiResponse.json();
                throw new Error(errorDetails.error?.message || strapiResponse.statusText);
            }
        } catch (error) {
            console.error('Error fetching agency from Strapi:', error);
            res.status(500).json({ error: 'Failed to fetch agency' });
        }
    }  else {
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}
