// src/pages/api/agency/[agency_number]/statistics.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { permissionManager } from '@/utils/permissionManager';
import { InvoiceCustomerStatusType, InvoiceData } from '@/utils/invoice/types';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only GET is allowed
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  // Auth
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  const token = authHeader.split(' ')[1];

  // Validate agency_number
  const { agency_number } = req.query;
  if (typeof agency_number !== 'string') {
    return res.status(400).json({ error: 'Invalid or missing agency number' });
  }

  // Check permissions
  const authUser = await permissionManager.getAuthUser(token);
  if (!authUser || !permissionManager.isUserAdmin(authUser)) {
    return res.status(403).json({ error: 'Forbidden' });
  }

  // Pagination settings
  const pageSize = 100;
  let page = 1;
  let pageCount = 1;

  // Stats accumulators
  let openInvoices  = 0;
  let openAmount    = 0;
  let paiedInvoices = 0;
  let paiedAmount   = 0;

  try {
    // Loop through all pages
    while (page <= pageCount) {
      const params = new URLSearchParams({
        'filters[agency_number][$eq]': agency_number,
        'pagination[page]':            String(page),
        'pagination[pageSize]':        String(pageSize),
      });
      const url = `${process.env.STRAPI_BASE_URL}/invoices?${params.toString()}`;
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) {
        const err = await response.json();
        throw new Error(err.error?.message || response.statusText);
      }

      const payload = await response.json() as {
        data: InvoiceData[];
        meta: { pagination: { page: number; pageSize: number; pageCount: number; total: number } };
      };

      // If first page and no data, nothing to do
      if (page === 1 && payload.data.length === 0) {
        return res.status(404).json({ message: 'No invoices found for this agency' });
      }

      // Accumulate stats for this batch
      for (const invoice of payload.data) {
        if (invoice.customer_status === InvoiceCustomerStatusType.OPEN) {
          openInvoices += 1;
          openAmount   += invoice.total_gross;
        } else if (invoice.customer_status === InvoiceCustomerStatusType.PAID) {
          paiedInvoices += 1;
          paiedAmount   += invoice.total_gross;
        }
      }

      // Update loop counters
      pageCount = payload.meta.pagination.pageCount;
      page += 1;
    }

    // Return aggregated stats
    return res.status(200).json({
      openInvoices,
      paiedInvoices,
      openAmount,
      paiedAmount,
    });
  } catch (error) {
    console.error('Error fetching invoices from Strapi:', error);
    return res.status(500).json({ error: 'Failed to fetch invoices' });
  }
}
