// src/pages/api/agencies/index.ts
import { agencyFieldConfig } from '@/components/filters/agencyFilterConfig';
import { Prisma } from '@/generated/prisma-postgres';
import { AgencyService } from '@/server/agency/AgencyService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { parseQuery } from '@/server/infra/prismaFiltersBuilder';

const GET: EndpointHandler = {
    execute: async ({ db, req, res }) => {
        const limit = Math.max(1, Number(req.query.limit ?? 10));
        const offset = Math.max(0, Number(req.query.offset ?? 0));
        const sortField = String(req.query.sortField ?? 'agencyNumber');
        const sortDirection =
            String(req.query.sortDirection ?? 'asc').toLowerCase() === 'desc'
                ? 'desc'
                : 'asc';
        const orderBy = { [sortField]: sortDirection } as const;
        const where = parseQuery(req.query, {
            rootModel: Prisma.ModelName.Agency,
            fields: agencyFieldConfig,
        });

        const service = new AgencyService(db);
        const { items, total } = await service.list({
            where,
            orderBy,
            skip: offset,
            take: limit,
        });

        const meta = {
            total,
            pageCount: Math.ceil(total / limit),
            pageSize: limit,
            page: Math.floor(offset / limit) + 1,
        };

        res.status(200).json({ items, meta });
    },
};

export default createEndpoint({ GET });
