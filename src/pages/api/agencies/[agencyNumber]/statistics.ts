// src/pages/api/agency/[agencyNumber]/statistics.ts
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';
import { InvoiceService } from '@/server/invoice/InvoiceService';

const GET: EndpointHandler = {
    validate: async ({ req }) => {
        const auth = req.headers.authorization;
        if (!auth) return 'Missing authorization in headers';

        const { agencyNumber } = req.query;
        if (
            !agencyNumber ||
            (Array.isArray(agencyNumber) && agencyNumber.length !== 1)
        ) {
            return 'Invalid or missing agency number';
        }
    },

    execute: async ({ req, res, db }) => {
        // Only admins allowed (same behavior as your original)

        const agencyNumberRaw = req.query.agencyNumber;
        const agencyNumber = Array.isArray(agencyNumberRaw)
            ? agencyNumberRaw[0]
            : String(agencyNumberRaw);

        const service = new InvoiceService(db as any);
        const stats = await service.getAgencyStatistics(agencyNumber);

        if (stats.openInvoices === 0 && stats.paiedInvoices === 0) {
            res.status(404).json({ message: 'No invoices found for this agency' });
            return;
        }

        res.status(200).json(stats);
    },
};

export default createEndpoint({ GET });
