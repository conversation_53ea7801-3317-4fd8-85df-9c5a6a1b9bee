// src/pages/api/agency/[agencyNumber].ts
import { AgencyService } from '@/server/agency/AgencyService';
import { type EndpointHandler } from '@/server/domain/endpoints';
import { createEndpoint } from '@/server/infra/createEndpoint';

const GET: EndpointHandler = {
  validate: async ({ req }) => {
    const auth = req.headers.authorization;
    if (!auth) return 'Missing authorization in headers';

    const { agencyNumber } = req.query;
    if (
      !agencyNumber ||
      (Array.isArray(agencyNumber) && agencyNumber.length !== 1)
    ) {
      return 'Invalid or missing agency number';
    }
  },

  execute: async ({ req, res, db }) => {
    // param
    const agencyNumber = Array.isArray(req.query.agencyNumber)
      ? req.query.agencyNumber[0]
      : String(req.query.agencyNumber);

    // service
    const service = new AgencyService(db);

    const agency = await service.findOne({ agencyNumber: agencyNumber });

    if (!agency) {
      res.status(404).json({ message: 'agency not found' });
      return;
    }

    res.status(200).json(agency);
  },
};

export default createEndpoint({ GET });
