// src/pages/invoice/create/[invoice_number].tsx
import { useRouter } from 'next/router';

import AgentForm from '@/components/AgentForm';
import Layout from '@/components/layout';

export default function AgentEditPage() {
  const router = useRouter();
  const { agentNumber } = router.query;

  if (!agentNumber || typeof agentNumber !== 'string') {
    return <p>Lade Maklerdaten...</p>;
  }

  return (
    <div>
      <Layout
        title="Makler anpassen"
        description="Bereich zum anpassen des Maklers"
      >
        <AgentForm editMode={true} agentNumber={agentNumber} />
      </Layout>
    </div>
  );
}
