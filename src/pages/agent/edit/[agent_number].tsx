// src/pages/invoice/create/[invoice_number].tsx
import { useRouter } from 'next/router';
import AgentForm from '@/components/AgentForm';
import Layout from '@/components/layout';

export default function AgentEditPage() {
        const router = useRouter();
        const { agent_number } = router.query; 
    
        if (!agent_number || typeof agent_number !== 'string') {
            return <p>Lade <PERSON>...</p>; 
        }
    
    return (
        <div>
            <Layout title="Makler anpassen" description="Bereich zum anpassen des Maklers">
                <AgentForm edit_mode={true} agent_number={agent_number} />
            </Layout>
        </div>
    );
}
