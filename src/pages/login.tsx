// src/pages/login.tsx
import { Box, Card, CssBaseline } from '@mui/material';
import { styled } from '@mui/material/styles';
import React from 'react';

import Footer from '@/components/footer';
import LoginForm from '@/components/LoginForm';

const LoginWrapper = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  background: theme.palette.background.default,
}));

const CenterBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexGrow: 1,
  padding: theme.spacing(4),
}));

const StyledCard = styled(Card)(({ theme }) => ({
  maxWidth: 420,
  width: '100%',
  padding: theme.spacing(4),
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: Number(theme.shape.borderRadius) * 1,
}));

export default function Login() {
  return (
    <LoginWrapper>
      <CssBaseline />
      <CenterBox>
        <StyledCard>
          <LoginForm />
        </StyledCard>
      </CenterBox>
      <Footer />
    </LoginWrapper>
  );
}
