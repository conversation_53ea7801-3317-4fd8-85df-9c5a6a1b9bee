// src/pages/contract/[contractNumber].tsx
import { useRouter } from 'next/router';

import ContractDetails from '@/components/ContractDetails';
import Layout from '@/components/layout';

const ContractPage = () => {
  const router = useRouter();
  const { contractNumber } = router.query;

  return (
    <div>
      <Layout title="Vertragsdetails" description="Vertragsdetails ansehen">
        <ContractDetails contractNumber={contractNumber as string} />
      </Layout>
    </div>
  );
};

export default ContractPage;
