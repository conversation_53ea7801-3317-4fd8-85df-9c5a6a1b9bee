// pages/customer/[id]/editOffer/[contractId].tsx
import { useRouter } from 'next/router';

import ContractForm from '@/components/ContractForm';
import Layout from '@/components/layout';

const CreateContractPage = () => {
  const router = useRouter();
  const { contractNumber } = router.query;

  if (!contractNumber) {
    return <p>Lade...</p>;
  }

  return (
    <div>
      <Layout
        title="Vertrag editieren"
        description="Bereich zum editieren des Vertrags"
      >
        <ContractForm
          isOffer={false}
          editMode={true}
          contractNumber={contractNumber as string}
        />
      </Layout>
    </div>
  );
};

export default CreateContractPage;
