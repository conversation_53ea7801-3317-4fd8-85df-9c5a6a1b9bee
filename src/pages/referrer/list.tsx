import Layout from "@/components/layout/layout";
import ReferralsHistory from "@/components/ReferralsHistory/ReferralsHistory";
import {GetServerSideProps} from "next";
import {identityManager} from "@/server/IdentityManager";
import {paths} from "@/paths";

type Props = {
  isAdmin?: boolean
}

export const getServerSideProps: GetServerSideProps<Props> = async (context) => {
  const { referrerSession } = context.req.cookies
  if (!referrerSession) {
    return { redirect: { destination: paths.referrer.unauthorized, permanent: false } }
  }

  const { isAdmin } = await identityManager.getReferrerOrThrow(referrerSession)

  return {
    props: { isAdmin }
  };
};

export default function EmpfehlungsverlaufPage({ isAdmin }: Props) {
  return (
    <Layout
      htmlTitle="Empfehlungsverlauf"
      metaDescription="Hier bekommt man einen kompletten Überblick über alle gegebenen Tipps"
      showMenu
    >
      <ReferralsHistory isAdmin={isAdmin} />
    </Layout>
  );
}
