import Layout from "@/components/layout/layout";
import httpClient from "@/utils/HttpClient";
import EmailInputCard from "@/components/card/EmailInputCard";

export default function ReferrerRegisterPage() {
  async function handleSubmit(input: string) {
    await httpClient.request("/api/referrer/register", {
      method: "POST",
      body: {
        email: input
      },
    });
  }

  return (
    <Layout
      htmlTitle="Tippgeber Zugang anfordern"
      metaDescription="Geben Sie Ihre geschäftliche E-Mail-Adresse ein, um Zugang zum Tippgeber-Bereich zu erhalten."
    >
      <EmailInputCard onSubmit={handleSubmit} translationsNamespace='referrerRegister' />
    </Layout>
  );
}
