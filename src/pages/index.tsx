// src/pages/index.tsx
import { Box, CircularProgress, Typography } from '@mui/material';
import Image from 'next/image';
import router from 'next/router';
import { useEffect } from 'react';

import Footer from '@/components/footer';

export default function IndexPage() {
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push('/dashboard');
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Box
      display="flex"
      flexDirection="column"
      minHeight="100vh"
      justifyContent="space-between"
    >
      <Box
        display="flex"
        flexGrow={1}
        justifyContent="center"
        alignItems="center"
      >
        <Box textAlign="center">
          <Image src="/images/logo.svg" alt="Logo" width={250} height={250} />
          <Typography variant="h5" color="primary" sx={{ marginTop: 2 }}>
            VERWALTUNGSPORTAL
          </Typography>
        </Box>
      </Box>

      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>

      <Footer />
    </Box>
  );
}
