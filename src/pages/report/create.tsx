// src/pages/report/create.tsx
import { useRouter } from 'next/router';

import Layout from '@/components/layout';
import { ReportForm } from '@/components/ReportForm/ReportForm';

export default function NewReport() {
  const router = useRouter();
  const { customerNumber, contractNumber, agencyNumber } = router.query;

  if (!customerNumber || !contractNumber || !agencyNumber) {
    return <p>Lade Daten...</p>;
  }

  return (
    <div>
      <Layout
        title="Schaden melden"
        description="Hier wird ein Schaden gemeldet"
      >
        <ReportForm
          customerNumber={customerNumber as string}
          contractNumber={contractNumber as string}
          agencyNumber={agencyNumber as string}
        />
      </Layout>
    </div>
  );
}
