// src/pages/report/create/new.tsx
import { useRouter } from 'next/router';
import ReportForm from '../../../components/ReportForm'
import Layout from '../../../components/layout';

export default function NewReport() {
    const router = useRouter();
    const { customer_number, contract_number, agency_number } = router.query;

    if (!customer_number || !contract_number || !agency_number) {
        return <p>Lade Daten...</p>;
    } 
    
    return (
        <div>
            <Layout title="Schaden melden" description="Hier wird ein Schaden gemeldet">
                <ReportForm
                    customer_number={customer_number as string}
                    contract_number={contract_number as string}
                    agency_number={agency_number as string}
                />
            </Layout>
        </div>
    );
}