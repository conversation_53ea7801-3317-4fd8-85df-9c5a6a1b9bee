// pages/report/editReport/[report_number].tsx
import Layout from '@/components/layout';
import ReportForm from '@/components/ReportForm';
import { useRouter } from 'next/router';

const CreateContractPage = () => {
    const router = useRouter();
    const { report_number } = router.query;

    if (!report_number) {
        return <p>Lade...</p>;
    }

    return (
        <div>
            <Layout title="Report editieren" description="Bereich zum editieren des Angebots">
                <ReportForm report_number={report_number as string} />
            </Layout>
        </div>
    );
};

export default CreateContractPage;
