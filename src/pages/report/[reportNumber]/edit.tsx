// pages/report/editReport/[report_number].tsx
import { useRouter } from 'next/router';

import Layout from '@/components/layout';
import { ReportForm } from '@/components/ReportForm/ReportForm';

const CreateContractPage = () => {
  const router = useRouter();
  const { reportNumber } = router.query;

  // TODO: add ErrorBoundary that redirects to the homepage and throw an error here?
  if (!reportNumber) {
    return <p>Lade...</p>;
  }

  return (
    <div>
      <Layout
        title="Report editieren"
        description="Bereich zum editieren des Angebots"
      >
        <ReportForm reportNumber={reportNumber as string} />
      </Layout>
    </div>
  );
};

export default CreateContractPage;
