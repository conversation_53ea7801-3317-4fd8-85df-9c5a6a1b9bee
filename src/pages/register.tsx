// pages/register.tsx
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/footer';
import { Button, TextField, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent } from '@mui/material';
import AgentRegistrationForm from '@/components/AgentRegistrationForm';

export default function RegisterPage() {
    return (
        <div>
            <AgentRegistrationForm />
            <Footer />
        </div>
    );
} 
