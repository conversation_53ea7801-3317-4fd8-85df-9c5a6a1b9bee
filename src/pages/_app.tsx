import "@/styles/globals.css";
import type { AppProps } from "next/app";
import { NextIntlClientProvider } from 'next-intl';
import { ThemeProvider } from '@mui/material/styles';
import muiTheme from "../utils/muiTheme";
import translations from '../../locales/de/common.json'
import CookieConsent from "@/components/CookieConsent";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <NextIntlClientProvider
      locale="de"
      timeZone="Europe/Vienna"
      messages={translations}
    >
      <ThemeProvider theme={muiTheme}>
        <Component {...pageProps} />
        <CookieConsent />
      </ThemeProvider>
    </NextIntlClientProvider>
  )
}
