import '@/app/globals.css';

import { CssBaseline, ThemeProvider } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import type { AppProps } from 'next/app';
import { useEffect, useState } from 'react';

// a tiny helper to resolve our {{…}} placeholders
function resolveRefs<T>(obj: any, themeBase: any): T {
  if (typeof obj === 'string') {
    const m = obj.match(/{{\s*([\w.]+)\s*}}/);
    if (m) {
      return m[1]
        .split('.')
        .reduce((acc: any, key: string) => acc && acc[key], themeBase);
    }
    return obj as any;
  }
  if (Array.isArray(obj)) {
    return obj.map((o) => resolveRefs(o, themeBase)) as any;
  }
  if (obj !== null && typeof obj === 'object') {
    const out: any = {};
    for (const k of Object.keys(obj)) {
      out[k] = resolveRefs(obj[k], themeBase);
    }
    return out;
  }
  return obj;
}

export default function MyApp({ Component, pageProps }: AppProps) {
  const [raw, setRaw] = useState<any>(null);

  useEffect(() => {
    fetch('/api/theme')
      .then((r) => r.json())
      .then(setRaw)
      .catch(console.error);
  }, []);

  if (!raw) return null;

  // 1) create a base theme from just the palette+typography
  const base = createTheme({
    palette: raw.palette,
    typography: raw.typography,
  });

  // 2) resolve all our {{…}} placeholders against that base
  const components = resolveRefs(raw.components, base);

  // 3) build the final theme
  const theme = createTheme(base, { components });

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Component {...pageProps} />
    </ThemeProvider>
  );
}
