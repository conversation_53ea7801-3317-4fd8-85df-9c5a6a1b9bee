// pages/info.tsx
import DownloadIcon from '@mui/icons-material/Download';
import {
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useState } from 'react';

import StaticBox from '@/components/box/StaticBox';
import Layout from '@/components/layout';

export default function CustomersPage() {
  const [documents] = useState([
    {
      title: 'Unfall-Versicherung',
      id: 'declaration_-_accident_insurance',
      updated_at: '04.07.2025',
    },
    {
      title: 'Tierhalter-Haftpflichtversicherung',
      id: 'declaration_-_animal_liability_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Gewerbliche Gebäudeversicherung',
      id: 'declaration_-_building_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Geschäftsversicherung',
      id: 'declaration_-_business_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Betriebs-Haftpflichtversicherung',
      id: 'declaration_-_business_liability_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Haus- und Grundbesitzer-Haftpflichtversicherung',
      id: 'declaration_-_home_and_landowner_liability',
      updated_at: '13.02.2025',
    },
    {
      title: 'Hausratversicherung',
      id: 'declaration_-_household_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Privat-Haftpflichtversicherung',
      id: 'declaration_-_personal_liability_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Wohngebäudeversicherung',
      id: 'declaration_-_residential_building_insurance',
      updated_at: '13.02.2025',
    },
    {
      title: 'Allgemeine Versicherungsbedingungen',
      id: 'general_insurance conditions',
      updated_at: '13.02.2025',
    },
  ]);
  const handleDownloadAttachment = async (bucket_path: string) => {
    const response = await fetch(`/api/file/info/${bucket_path}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = bucket_path;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download Attachment');
    }
  };

  return (
    <div>
      <Layout
        title="Information"
        description="Überblick über alle Vertragsvarianten"
      >
        <Box
          sx={{
            px: { xs: 2, md: 5 },
            py: 5,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
            maxWidth: '768px',
            mx: 'auto',
          }}
        >
          <Typography variant="h4" textAlign="center" color="primary">
            Information
          </Typography>

          {/* files */}
          {documents && documents.length > 0 && (
            <StaticBox title="Deklarationen">
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Dokumentenname</TableCell>
                      <TableCell>Zuletzt aktualisiert</TableCell>
                      <TableCell align="center">Aktion</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {documents.map((document, index) => (
                      <TableRow key={index}>
                        <TableCell>{document.title}</TableCell>
                        <TableCell>{document.updated_at}</TableCell>
                        <TableCell align="center">
                          <Tooltip
                            title={`Deklaration ${document.title} herunterladen`}
                          >
                            <IconButton
                              onClick={() =>
                                handleDownloadAttachment(document.id)
                              }
                              color="primary"
                            >
                              <DownloadIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </StaticBox>
          )}
        </Box>
      </Layout>
    </div>
  );
}
