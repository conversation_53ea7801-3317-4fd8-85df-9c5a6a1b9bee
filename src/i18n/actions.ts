'use server';

import { cookies } from 'next/headers';

export async function setUserLocale(locale: string) {
  const store = await cookies();

  // Validate locale
  const supportedLocales = ['de', 'en'];
  if (!supportedLocales.includes(locale)) {
    throw new Error(`Unsupported locale: ${locale}`);
  }

  // Set the locale cookie
  store.set('locale', locale, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    path: '/',
    sameSite: 'lax',
  });
}
