import { initTRPC, TRPCError } from '@trpc/server';
import { cache } from 'react';
import superjson from 'superjson';

import { auth } from '@/modules/auth/server/auth';

export const createTRPCContext = cache(async () => {
  // Return null context for unauthenticated users
  return {};
});
// Avoid exporting the entire t-object
// since it's not very descriptive.
// For instance, the use of a t variable
// is common in i18n libraries.
type Context = Awaited<ReturnType<typeof createTRPCContext>>;

const t = initTRPC.context<Context>().create({
  /**
   * @see https://trpc.io/docs/server/data-transformers
   */
  transformer: superjson,
});
// Base router and procedure helpers
export const createTRPCRouter = t.router;
export const createCallerFactory = t.createCallerFactory;
export const baseProcedure = t.procedure;
export const protectedProcedure = baseProcedure.use(
  async ({ ctx, next, path, type, getRawInput }) => {
    // Log the incoming request for debugging

    const session = await auth();
    if (!session) {
      const rawInput = await getRawInput();

      console.error('❌ UNAUTHORIZED - No session found for:', {
        path,
        type,
        input: rawInput,
      });
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'You must be logged in to access this resource.',
      });
    }
    const user = session.user;

    // Validate that user has been synced with database
    if (!user?.userId || user.userId === 0) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message:
          'User synchronization with database failed. Please try logging in again.',
      });
    }

    // Create plain user object without methods to avoid serialization issues
    const plainUser = {
      id: user.id, // Keycloak ID
      userId: user.userId, // Database user ID
      keycloakId: user.keycloakId || user.id,
      username: user.username, // Keycloak username
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
    };

    // Calculate role information
    const isAdmin = plainUser.role === 'admin';
    const isAgent = plainUser.role === 'agent';

    return next({
      ctx: {
        ...ctx,
        // Enhanced auth context with role information
        auth: {
          user: plainUser,
          // Role flags for easy access
          isAdmin,
          isAgent,
        },
      },
    });
  }
);

export const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
  if (ctx.auth.user.role !== 'admin') {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'You must be an admin to access this resource.',
    });
  }

  return next({
    ctx,
  });
});
