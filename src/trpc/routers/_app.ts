import { type inferRouterOutputs } from '@trpc/server';

import { administrationRouter } from '@/modules/administration/server/administration-router';
import { attachmentsRouter } from '@/modules/attachment/server/attachment-router';
import { casesRouter } from '@/modules/case/server/case-router';
import { chatRouter } from '@/modules/chat/server/chat-router';
import { customersRouter } from '@/modules/customer/server/customer-router';
import { filesRouter } from '@/modules/files/server/files-router';
import { messagesRouter } from '@/modules/messages/server/messages-router';
import { potentialInsuranceRouter } from '@/modules/potential-insurance/server/potential-insurance-router';
import { questionnaireRouter } from '@/modules/questionnaire/server/questionnaire-router';
import { quickCheckRouter } from '@/modules/quick-check/server/quick-check-router';
import { userRouter } from '@/modules/user/server/user-router';
import { welcomeRouter } from '@/modules/welcome/server/welcome-router';
import { createTRPCRouter } from '@/trpc/init';

export const appRouter = createTRPCRouter({
  attachments: attachmentsRouter,
  cases: casesRouter,
  customers: customersRouter,
  user: userRouter,
  chat: chatRouter,
  welcome: welcomeRouter,
  questionnaire: questionnaireRouter,
  potentialInsurance: potentialInsuranceRouter,
  files: filesRouter,
  messages: messagesRouter,
  administration: administrationRouter,
  quickCheck: quickCheckRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
export type AppRouterOutputs = inferRouterOutputs<AppRouter>;
