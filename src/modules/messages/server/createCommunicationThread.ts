import type { RiskPreRequest } from '@prisma/client';

import {
  type CommunicationThread,
  type CommunicationThreadAttachment,
} from '@/components/modal/communicationModal';
import { type MessageWithAttachments } from '@/modules/messages/types/message-types';
import prisma from '@/utils/prisma-db';

export const createCommunicationThread = async (
  message: MessageWithAttachments
): Promise<CommunicationThread> => {
  const { insurerName, tariffName } = await resolveRiskPreRequest(message);

  return {
    id: message.messageId,
    type: resolveThreadType(message),
    message: message.body ?? undefined,
    insurerName,
    tariffName,
    attachments: await resolveAttachments(message),
  };
};

const resolveRiskPreRequest = async (
  message: MessageWithAttachments
): Promise<Partial<RiskPreRequest>> => {
  const { riskPreRequestId } = message;
  if (!riskPreRequestId) {
    return {};
  }

  const riskPreRequest = await prisma.riskPreRequest.findUnique({
    where: { riskPreRequestId },
  });
  if (!riskPreRequest) {
    console.error(
      `Message[id=${message.messageId}] has riskPreRequestId=${riskPreRequestId} but the corresponding RiskPreRequest was not found`
    );
    return {};
  }

  return riskPreRequest;
};

const resolveThreadType = (
  message: MessageWithAttachments
): CommunicationThread['type'] => {
  if (message.applicationId) {
    return 'contract';
  } else if (message.riskPreRequestId) {
    return 'followUpRequest';
  }

  return 'upload';
};

const resolveAttachments = async (
  message: MessageWithAttachments
): Promise<CommunicationThreadAttachment[]> => {
  const attachments = await Promise.all(
    message.attachments.map(async (attachment) => {
      const { linkedAttachmentId } = attachment;
      if (!linkedAttachmentId) {
        return attachment;
      }

      const linkedAttachment = await prisma.attachment.findUnique({
        where: { attachmentId: linkedAttachmentId },
      });
      if (!linkedAttachment) {
        console.error(
          `Attachment[id=${attachment.attachmentId}] has linkedAttachmentId=${linkedAttachmentId} but the linked Attachment was not found`
        );
        return attachment;
      }

      return [attachment, linkedAttachment];
    })
  );

  const flattened = attachments.flat();

  return flattened.map(
    ({
      attachmentId,
      linkedAttachmentId,
      fileName,
      documentType,
      uploadedAt,
    }) => ({
      id: attachmentId,
      name: fileName,
      linkedAttachmentId: linkedAttachmentId ?? undefined,
      documentType: documentType ?? undefined,
      uploadedAt: uploadedAt,
    })
  );
};
