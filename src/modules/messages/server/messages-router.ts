import { createCommunicationThread } from '@/modules/messages/server/createCommunicationThread';
import { getCommunicationThreadsSchema } from '@/modules/messages/types/message-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';

export const messagesRouter = createTRPCRouter({
  getCommunicationThreads: protectedProcedure
    .input(getCommunicationThreadsSchema)
    .query(async ({ input: { caseId } }) => {
      // Optimized query: fetch all related data in a single query to avoid N+1 problem
      const messages = await prisma.message.findMany({
        where: { caseId },
        include: {
          attachments: true,
          riskPreRequest: {
            select: {
              insurerName: true,
              tariffName: true,
            },
          },
        },
        orderBy: {
          sentAt: 'desc',
        },
      });

      const threads = await Promise.all(
        messages.map(createCommunicationThread)
      );

      return { threads };
    }),
});

// IMPORTANT: Create caller for server-side use(also can be used in other routers)
export const messagesRouterCaller = messagesRouter.createCaller({});
