import { quickCheckFormSchema } from '@/modules/quick-check/types/quick-check-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';

/**
 * tRPC router for Quick Check (Schnellcheck/Risikocheck)
 *
 * This router will handle:
 * - Form submission
 * - Risk assessment calculation
 * - Result retrieval
 *
 * Note: Currently returns mock data. Will be connected to actual
 * risk assessment logic and database storage in future iterations.
 */

export const quickCheckRouter = createTRPCRouter({
  /**
   * Submit Quick Check form and get risk assessment result
   *
   * @param input - Quick Check form data
   * @returns Risk assessment result with eligibility and recommendations
   */
  submit: protectedProcedure
    .input(quickCheckFormSchema)
    .mutation(async ({ input }) => {
      // TODO: Implement actual risk assessment logic
      // TODO: Store submission in database
      // TODO: Connect to external risk assessment API if needed

      // Mock response for now
      const mockResult = {
        success: true,
        result: {
          eligible: true,
          riskLevel: 'low' as const,
          message:
            'Basierend auf Ihren Angaben scheinen Sie für eine Versicherung geeignet zu sein.',
          recommendations: [
            'Wir empfehlen eine umfassende Krankenversicherung.',
            'Zahnzusatzversicherung könnte für Sie vorteilhaft sein.',
          ],
          bmiResult:
            input.height && input.weight
              ? {
                  bmi:
                    Math.round(
                      (input.weight / Math.pow(input.height / 100, 2)) * 10
                    ) / 10,
                  category: 'normal' as const,
                  message: 'Ihr BMI liegt im Normalbereich.',
                }
              : undefined,
        },
        submissionId: `QC-${Date.now()}`,
      };

      return mockResult;
    }),

  /**
   * Get profession options for dropdown
   *
   * @returns List of available professions
   */
  getProfessions: protectedProcedure.query(async () => {
    // TODO: Fetch from database or external API
    // For now, return static list (will be handled by constants on frontend)

    return {
      professions: [
        { value: 'employee', label: 'Angestellter' },
        { value: 'civil_servant', label: 'Beamter' },
        { value: 'self_employed', label: 'Selbstständig' },
        { value: 'student', label: 'Student' },
        { value: 'retiree', label: 'Rentner' },
        { value: 'unemployed', label: 'Arbeitslos' },
        { value: 'homemaker', label: 'Hausfrau/Hausmann' },
        { value: 'craftsman', label: 'Handwerker' },
        { value: 'construction_worker', label: 'Bauarbeiter' },
        { value: 'doctor', label: 'Arzt/Ärztin' },
        { value: 'teacher', label: 'Lehrer/in' },
        { value: 'engineer', label: 'Ingenieur/in' },
        { value: 'merchant', label: 'Kaufmann/Kauffrau' },
        { value: 'other', label: 'Sonstiges' },
      ],
    };
  }),

  /**
   * Get user's previous Quick Check submissions (for future use)
   *
   * @returns List of previous submissions
   */
  getHistory: protectedProcedure.query(async () => {
    // TODO: Implement database query to fetch user's submission history

    return {
      submissions: [],
    };
  }),
});
