import { Box, Container } from '@mui/material';

import Navbar from '@/components/layout/navbar';
import { QuickCheckForm } from '@/modules/quick-check/ui/components/QuickCheckForm';
import { DIAGNOSIS_OPTIONS } from '@/modules/quick-check/ui/constants/diagnoses-data';

export function RisikocheckView() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
      }}
    >
      <Navbar showNewCaseButton={false} showOverviewButton={true} />

      <Container
        maxWidth="lg"
        sx={{
          flex: 1,
          py: 2,
        }}
      >
        <QuickCheckForm diagnosisOptions={DIAGNOSIS_OPTIONS} />
      </Container>
    </Box>
  );
}
