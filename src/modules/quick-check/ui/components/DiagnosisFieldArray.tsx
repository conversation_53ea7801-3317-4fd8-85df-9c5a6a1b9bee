'use client';

import AddCircleRoundedIcon from '@mui/icons-material/AddCircleRounded';
import RemoveCircleOutlineRoundedIcon from '@mui/icons-material/RemoveCircleOutlineRounded';
import { Box, Chip, IconButton, Stack } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useFieldArray, type UseFieldArrayRemove } from 'react-hook-form';

import { FormSelect, type SelectOption } from '@/components/form-inputs';
import { useReactHookFormContext } from '@/hooks/useFormContext';
import { MAX_DIAGNOSES } from '@/modules/quick-check/lib/constants';
import type { QuickCheckFormData } from '@/modules/quick-check/types/quick-check-schemas';

interface DiagnosisFieldArrayProps {
  /**
   * Name of the field array in the form
   * @example 'outpatientDiagnoses' | 'inpatientDiagnoses'
   */
  name: 'outpatientDiagnoses' | 'inpatientDiagnoses';

  /**
   * Translation key for the diagnosis label
   * @example 'quickCheck.sections.outpatientDiagnosis.diagnosisLabel'
   */
  labelKey: string;
  diagnosisOptions: SelectOption[];
}

/**
 * DiagnosisField component - renders a single diagnosis input field with remove button
 */
type DiagnosisFieldProps = {
  fieldIndex: number;
  fieldRemove: UseFieldArrayRemove;
  name: 'outpatientDiagnoses' | 'inpatientDiagnoses';
  labelKey: string;
  diagnosisOptions: SelectOption[];
};

const DiagnosisField = ({
  fieldIndex,
  fieldRemove,
  name,
  labelKey,
  diagnosisOptions,
}: DiagnosisFieldProps) => {
  const t = useTranslations();

  const handleRemoveClick = () => {
    fieldRemove(fieldIndex);
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
      <Box sx={{ flex: 1 }}>
        <FormSelect<QuickCheckFormData>
          name={`${name}.${fieldIndex}.description` as any}
          label={t(labelKey, { number: fieldIndex + 1 })}
          options={diagnosisOptions}
          isRequired
          skipTranslation
        />
      </Box>
      <IconButton
        onClick={handleRemoveClick}
        size="small"
        sx={{
          mt: 1,
          color: 'var(--error-red)',
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: 'rgba(211, 47, 47, 0.08)',
          },
        }}
      >
        <RemoveCircleOutlineRoundedIcon fontSize="small" />
      </IconButton>
    </Box>
  );
};

/**
 * DiagnosisFieldArray component - manages array of diagnosis fields
 *
 * Features:
 * - Add button to add more diagnosis fields (up to MAX_DIAGNOSES)
 * - Remove button on each field
 * - Shows current count vs max count
 * - Uses useFieldArray from react-hook-form
 */
export const DiagnosisFieldArray = ({
  name,
  labelKey,
  diagnosisOptions,
}: DiagnosisFieldArrayProps) => {
  const t = useTranslations();
  const { control } = useReactHookFormContext<QuickCheckFormData>();

  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  const handleAddClick = () => {
    if (fields.length < MAX_DIAGNOSES) {
      append({
        description: '',
      });
    }
  };

  return (
    <Stack spacing={1} sx={{ mt: 1 }}>
      {/* Diagnosis fields */}
      {fields.map((field, index) => (
        <DiagnosisField
          key={field.id}
          fieldIndex={index}
          fieldRemove={remove}
          name={name}
          labelKey={labelKey}
          diagnosisOptions={diagnosisOptions}
        />
      ))}

      {/* Add button with counter */}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <IconButton
          onClick={handleAddClick}
          disabled={fields.length >= MAX_DIAGNOSES}
          size="small"
          sx={{
            color: 'var(--teal)',
            backgroundColor: 'transparent',
            '&:hover': {
              backgroundColor: 'rgba(0, 150, 136, 0.08)',
            },
            '&:disabled': {
              color: 'var(--gray-400)',
            },
          }}
        >
          <AddCircleRoundedIcon fontSize="small" />
        </IconButton>
        <Chip
          label={t('quickCheck.sections.diagnosis.addButton', {
            current: fields.length,
            max: MAX_DIAGNOSES,
          })}
          size="small"
          sx={{
            backgroundColor: 'var(--gray-100)',
            color: 'var(--gray-700)',
            fontWeight: 500,
          }}
        />
      </Box>
    </Stack>
  );
};
