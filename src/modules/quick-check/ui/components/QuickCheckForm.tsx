'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Button, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import {
  FormSelect,
  FormText,
  type SelectOption,
} from '@/components/form-inputs';
import { FormErrorSummary } from '@/components/form-inputs/form-error-summary';
import { FormQuestionLabel } from '@/components/form-inputs/form-question-label';
import { FormRadioGroup } from '@/components/form-inputs/form-radio-group';
import { PROFESSION_OPTIONS } from '@/modules/quick-check/lib/constants';
import {
  createQuickCheckFormSchema,
  type QuickCheckFormData,
  quickCheckFormDefaults,
} from '@/modules/quick-check/types/quick-check-schemas';
import { DiagnosisFieldArray } from '@/modules/quick-check/ui/components/DiagnosisFieldArray';
import { QuickCheckResultModal } from '@/modules/quick-check/ui/components/QuickCheckResultModal';

interface QuickCheckFormProps {
  diagnosisOptions: SelectOption[];
}
export function QuickCheckForm({ diagnosisOptions }: QuickCheckFormProps) {
  const t = useTranslations();

  const [showResultModal, setShowResultModal] = useState(false);
  const [resultData, setResultData] = useState<QuickCheckFormData | null>(null);

  // Create schema with translations
  const schema = createQuickCheckFormSchema(t);

  // React Hook Form setup
  const form = useForm<QuickCheckFormData>({
    mode: 'all',
    resolver: zodResolver(schema),
    defaultValues: quickCheckFormDefaults,
  });

  const { handleSubmit, watch, setValue, clearErrors } = form;

  // Watch conditional fields
  const outpatientDiagnosis = watch('outpatientDiagnosis');
  const inpatientDiagnosis = watch('inpatientDiagnosis');
  const outpatientDiagnoses = watch('outpatientDiagnoses');
  const inpatientDiagnoses = watch('inpatientDiagnoses');

  // Handler for outpatient diagnosis change
  const handleOutpatientDiagnosisChange = (value: string) => {
    if (value === 'no') {
      setValue('outpatientDiagnoses', []);
      clearErrors('outpatientDiagnoses');
    } else if (value === 'yes') {
      // If 'yes' is selected and array is empty, add one empty diagnosis
      if (!outpatientDiagnoses || outpatientDiagnoses.length === 0) {
        setValue('outpatientDiagnoses', [{ description: '' }]);
      }
    }
  };

  // Handler for inpatient diagnosis change
  const handleInpatientDiagnosisChange = (value: string) => {
    if (value === 'no') {
      setValue('inpatientDiagnoses', []);
      clearErrors('inpatientDiagnoses');
    } else if (value === 'yes') {
      // If 'yes' is selected and array is empty, add one empty diagnosis
      if (!inpatientDiagnoses || inpatientDiagnoses.length === 0) {
        setValue('inpatientDiagnoses', [{ description: '' }]);
      }
    }
  };

  const onSubmit = async (data: QuickCheckFormData) => {
    setResultData(data);
    setShowResultModal(true);
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Title */}
      <Typography
        variant="h4"
        component="h1"
        sx={{ mb: 2, fontWeight: 600, color: 'var(--red)' }}
      >
        {t('quickCheck.title')}
      </Typography>

      {/* Description */}
      <Typography
        variant="body1"
        sx={{ mb: 4, color: 'var(--red)', fontWeight: 500 }}
      >
        {t('quickCheck.description')}
      </Typography>

      {/* Form Container with Border */}
      <Box
        sx={{
          border: '1px solid',
          borderColor: 'divider',
          borderRadius: 2,
          p: 4,
          backgroundColor: 'background.paper',
        }}
      >
        <FormProvider {...form}>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Error Summary */}
            <FormErrorSummary
              title={t('quickCheck.validation.errorSummaryTitle')}
              fieldLabelTranslator={(fieldName) => {
                // Try to get translation from quickCheck.fields
                // If not found, fallback to humanized field name
                const translationKey = `quickCheck.fields.${fieldName}`;
                const translated = t(translationKey);
                // If translation key is returned as-is, it means no translation exists
                return translated === translationKey
                  ? fieldName
                      .split(/(?=[A-Z])/)
                      .join(' ')
                      .replace(/^./, (char) => char.toUpperCase())
                  : translated;
              }}
            />

            <Stack spacing={3}>
              {/* 1. Vorversicherung (Previous Insurance) */}
              <FormRadioGroup<QuickCheckFormData>
                name="previousInsurance"
                questionLabel="quickCheck.sections.previousInsurance.question"
                options={[
                  { value: 'yes', label: t('common.yes') },
                  { value: 'no', label: t('common.no') },
                ]}
                row
                isRequired
              />

              {/* 2. Beruf (Profession) */}
              <FormSelect<QuickCheckFormData>
                name="profession"
                label="quickCheck.sections.profession.label"
                questionLabel="quickCheck.sections.profession.question"
                options={PROFESSION_OPTIONS}
                isRequired
              />

              {/* 3. BMI (Height and Weight) */}
              <Box>
                <Stack spacing={1}>
                  <FormQuestionLabel
                    questionLabel="quickCheck.sections.bmi.description"
                    required
                  />
                  <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                    <Box sx={{ flex: 1 }}>
                      <FormText<QuickCheckFormData>
                        name="height"
                        label="quickCheck.sections.bmi.heightLabel"
                        type="number"
                        asNumber
                        isRequired
                        slotProps={{
                          input: {
                            endAdornment: t('words.centimeter_short'),
                          },
                        }}
                      />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <FormText<QuickCheckFormData>
                        name="weight"
                        label="quickCheck.sections.bmi.weightLabel"
                        type="number"
                        asNumber
                        isRequired
                        slotProps={{
                          input: {
                            endAdornment: t('words.kilogramm_short'),
                          },
                        }}
                      />
                    </Box>
                  </Box>
                </Stack>
              </Box>

              {/* 4. Ambulante Diagnose (Outpatient Diagnosis) */}
              <Box>
                <FormRadioGroup<QuickCheckFormData>
                  name="outpatientDiagnosis"
                  questionLabel="quickCheck.sections.outpatientDiagnosis.question"
                  options={[
                    { value: 'yes', label: t('common.yes') },
                    { value: 'no', label: t('common.no') },
                  ]}
                  onChange={handleOutpatientDiagnosisChange}
                  row
                  isRequired
                />

                {/* Conditional diagnosis fields */}
                {outpatientDiagnosis === 'yes' && (
                  <DiagnosisFieldArray
                    name="outpatientDiagnoses"
                    labelKey="quickCheck.sections.outpatientDiagnosis.diagnosisLabel"
                    diagnosisOptions={diagnosisOptions}
                  />
                )}
              </Box>

              {/* 5. Stationäre Diagnose (Inpatient Diagnosis) */}
              <Box>
                <FormRadioGroup<QuickCheckFormData>
                  name="inpatientDiagnosis"
                  questionLabel="quickCheck.sections.inpatientDiagnosis.question"
                  options={[
                    { value: 'yes', label: t('common.yes') },
                    { value: 'no', label: t('common.no') },
                  ]}
                  onChange={handleInpatientDiagnosisChange}
                  row
                  isRequired
                />

                {/* Conditional diagnosis fields */}
                {inpatientDiagnosis === 'yes' && (
                  <DiagnosisFieldArray
                    name="inpatientDiagnoses"
                    labelKey="quickCheck.sections.inpatientDiagnosis.diagnosisLabel"
                    diagnosisOptions={diagnosisOptions}
                  />
                )}
              </Box>

              {/* 6. Fehlende Zähne (Missing Teeth) */}
              <FormText<QuickCheckFormData>
                name="missingTeeth"
                label="quickCheck.sections.missingTeeth.label"
                questionLabel="quickCheck.sections.missingTeeth.question"
                type="number"
                asNumber
                isRequired
              />

              {/* 7. Zahnersatz (Dental Replacements) */}
              <FormText<QuickCheckFormData>
                name="dentalReplacements"
                label="quickCheck.sections.dentalReplacements.label"
                questionLabel="quickCheck.sections.dentalReplacements.question"
                questionHelperText="quickCheck.sections.dentalReplacements.helperText"
                type="number"
                asNumber
                isRequired
              />

              {/* Submit Button */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  type="submit"
                  variant="contained"
                  sx={{
                    minWidth: 200,
                    backgroundColor: 'var(--teal)',
                    '&:hover': {
                      backgroundColor: 'var(--teal-dark)',
                    },
                  }}
                >
                  {t('quickCheck.submitButton')}
                </Button>
              </Box>
            </Stack>
          </form>
        </FormProvider>
      </Box>

      {/* Result Modal */}
      {showResultModal && resultData && (
        <QuickCheckResultModal
          data={resultData}
          onClose={() => {
            setShowResultModal(false);
            setResultData(null);
          }}
        />
      )}
    </Box>
  );
}
