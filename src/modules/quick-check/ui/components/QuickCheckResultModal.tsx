'use client';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { Box, Button, Chip, Divider, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { KvModal } from '@/components/modal/modal';
import { PROFESSION_OPTIONS } from '@/modules/quick-check/lib/constants';
import { calculateBMI, getBMIResult } from '@/modules/quick-check/lib/helpers';
import type { QuickCheckFormData } from '@/modules/quick-check/types/quick-check-schemas';

interface QuickCheckResultModalProps {
  data: QuickCheckFormData;
  onClose: () => void;
}

export function QuickCheckResultModal({
  data,
  onClose,
}: QuickCheckResultModalProps) {
  const t = useTranslations();

  // Calculate BMI if height and weight are provided
  const bmiValue = calculateBMI(data.height, data.weight);
  const bmiResult = getBMIResult(bmiValue);

  // Get profession label
  const professionOption = PROFESSION_OPTIONS.find(
    (p) => p.value === data.profession
  );
  const professionLabel = professionOption
    ? t(professionOption.label)
    : data.profession;

  // Filter out empty diagnoses
  const outpatientDiagnoses =
    data.outpatientDiagnoses?.filter(
      (d) => d.description && d.description.trim().length > 0
    ) || [];
  const inpatientDiagnoses =
    data.inpatientDiagnoses?.filter(
      (d) => d.description && d.description.trim().length > 0
    ) || [];

  return (
    <KvModal onClose={onClose}>
      <Stack
        direction="column"
        spacing={3}
        sx={{
          width: '100%',
          maxWidth: 700,
          maxHeight: '80vh',
          overflowY: 'auto',
          px: 2,
        }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <CheckCircleIcon
            sx={{
              fontSize: 80,
              color: 'var(--teal)',
              mb: 2,
            }}
          />
          <Typography
            variant="h4"
            component="h2"
            sx={{ fontWeight: 600, color: 'var(--teal)' }}
          >
            {t('quickCheck.result.title')}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {t('quickCheck.result.subtitle')}
          </Typography>
        </Box>

        <Divider />

        {/* BMI Section */}
        {bmiResult && (
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
              {t('quickCheck.result.bmiSection')}
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box
                sx={{
                  backgroundColor: 'var(--teal)',
                  color: 'white',
                  borderRadius: 2,
                  px: 3,
                  py: 2,
                  textAlign: 'center',
                }}
              >
                <Typography variant="h3" sx={{ fontWeight: 700 }}>
                  {bmiResult.bmi}
                </Typography>
                <Typography variant="caption">BMI</Typography>
              </Box>
              <Box>
                <Chip
                  label={t(bmiResult.message)}
                  color={
                    bmiResult.category === 'normal'
                      ? 'success'
                      : bmiResult.category === 'underweight' ||
                          bmiResult.category === 'overweight'
                        ? 'warning'
                        : 'error'
                  }
                  sx={{ fontWeight: 600 }}
                />
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  {t('quickCheck.result.heightWeight', {
                    height: data.height ?? 0,
                    weight: data.weight ?? 0,
                  })}
                </Typography>
              </Box>
            </Stack>
          </Box>
        )}

        {/* Previous Insurance */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            {t('quickCheck.sections.previousInsurance.question')}
          </Typography>
          <Typography variant="body1">
            {data.previousInsurance === 'yes'
              ? t('common.yes')
              : t('common.no')}
          </Typography>
        </Box>

        {/* Profession */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            {t('quickCheck.sections.profession.label')}
          </Typography>
          <Typography variant="body1">{professionLabel}</Typography>
        </Box>

        {/* Outpatient Diagnoses */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            {t('quickCheck.sections.outpatientDiagnosis.question')}
          </Typography>
          {data.outpatientDiagnosis === 'yes' &&
          outpatientDiagnoses.length > 0 ? (
            <Stack spacing={1}>
              {outpatientDiagnoses.map((diagnosis, index) => (
                <Box
                  key={index}
                  sx={{
                    backgroundColor: '#f5f5f5',
                    borderRadius: 1,
                    px: 2,
                    py: 1,
                  }}
                >
                  <Typography variant="body2">
                    {index + 1}. {diagnosis.description}
                  </Typography>
                </Box>
              ))}
            </Stack>
          ) : (
            <Typography variant="body1">{t('common.no')}</Typography>
          )}
        </Box>

        {/* Inpatient Diagnoses */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
            {t('quickCheck.sections.inpatientDiagnosis.question')}
          </Typography>
          {data.inpatientDiagnosis === 'yes' &&
          inpatientDiagnoses.length > 0 ? (
            <Stack spacing={1}>
              {inpatientDiagnoses.map((diagnosis, index) => (
                <Box
                  key={index}
                  sx={{
                    backgroundColor: '#f5f5f5',
                    borderRadius: 1,
                    px: 2,
                    py: 1,
                  }}
                >
                  <Typography variant="body2">
                    {index + 1}. {diagnosis.description}
                  </Typography>
                </Box>
              ))}
            </Stack>
          ) : (
            <Typography variant="body1">{t('common.no')}</Typography>
          )}
        </Box>

        {/* Dental Information */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            {t('quickCheck.result.dentalSection')}
          </Typography>
          <Stack spacing={2}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography variant="body2">
                {t('quickCheck.sections.missingTeeth.label')}:
              </Typography>
              <Chip
                label={data.missingTeeth ?? 0}
                color={(data.missingTeeth ?? 0) === 0 ? 'success' : 'warning'}
                size="small"
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
            >
              <Typography variant="body2">
                {t('quickCheck.sections.dentalReplacements.label')}:
              </Typography>
              <Chip
                label={data.dentalReplacements ?? 0}
                color={
                  (data.dentalReplacements ?? 0) === 0 ? 'success' : 'info'
                }
                size="small"
              />
            </Box>
          </Stack>
        </Box>

        <Divider />

        {/* Footer Note */}
        <Box sx={{ backgroundColor: '#f0f9ff', borderRadius: 2, p: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {t('quickCheck.result.footerNote')}
          </Typography>
        </Box>

        {/* Close Button */}
        <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }}>
          <Button
            variant="contained"
            onClick={onClose}
            sx={{
              minWidth: 200,
              backgroundColor: 'var(--teal)',
              '&:hover': {
                backgroundColor: 'var(--teal-dark)',
              },
            }}
          >
            {t('common.close')}
          </Button>
        </Box>
      </Stack>
    </KvModal>
  );
}
