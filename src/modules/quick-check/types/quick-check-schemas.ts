import { type useTranslations } from 'next-intl';
import { type RefinementCtx, z } from 'zod';

/**
 * Schema for Quick Check (Schnellcheck/Risikocheck) form
 *
 * This form assesses insurance risk based on:
 * - Previous insurance status
 * - Profession
 * - BMI (height/weight)
 * - Medical diagnoses (ambulant and stationary)
 * - Dental health (missing teeth and replacements)
 */

/**
 * Helper function to validate diagnosis fields
 * Reusable for both outpatient and inpatient diagnoses
 */
const validateDiagnosisField = (
  diagnosisType: 'yes' | 'no' | undefined,
  diagnoses: Array<{ description: string }> | undefined,
  ctx: RefinementCtx,
  path: 'outpatientDiagnoses' | 'inpatientDiagnoses',
  errorMessage: string
) => {
  if (diagnosisType === 'yes') {
    const hasValidDiagnosis =
      diagnoses &&
      diagnoses.length > 0 &&
      diagnoses.some((d) => d.description && d.description.trim().length > 0);

    if (!hasValidDiagnosis) {
      ctx.addIssue({
        code: 'custom',
        message: errorMessage,
        path: [path],
      });
    }
  }
};

/**
 * Helper function to validate individual diagnosis descriptions
 * Checks each diagnosis in the array and adds errors for empty descriptions
 */
const validateIndividualDiagnoses = (
  diagnoses: Array<{ description: string }> | undefined,
  ctx: RefinementCtx,
  path: 'outpatientDiagnoses' | 'inpatientDiagnoses',
  errorMessage: string
) => {
  diagnoses?.forEach((diagnosis, index) => {
    if (!diagnosis.description || diagnosis.description.trim().length === 0) {
      ctx.addIssue({
        code: 'custom',
        message: errorMessage,
        path: [path, index, 'description'],
      });
    }
  });
};

/**
 * Creates Quick Check form schema with translated error messages
 * @param t - Translation function from next-intl
 * @returns Zod schema with translated error messages
 */
export const createQuickCheckFormSchema = (
  t: ReturnType<typeof useTranslations>
) => {
  // Helper schema for yes/no radio buttons
  const yesNoSchema = z.enum(['yes', 'no'], {
    message: t('quickCheck.validation.selectOption'),
  });

  // Schema for diagnosis object
  const diagnosisSchema = z.object({
    description: z
      .string()
      .min(1, t('quickCheck.validation.diagnosisRequired')),
  });

  // Schema for diagnosis array (conditional, max 5)
  const diagnosisArraySchema = z
    .array(diagnosisSchema)
    .max(5, t('quickCheck.validation.maxDiagnoses'))
    .optional();

  // Main Quick Check form schema
  return z
    .object({
      // 1. Previous Insurance
      previousInsurance: yesNoSchema,

      // 2. Profession
      profession: z
        .string()
        .min(1, t('quickCheck.validation.professionRequired')),

      // 3. BMI - Height and Weight
      height: z
        .number({
          message: t('quickCheck.validation.heightMustBeNumber'),
        })
        .int(t('quickCheck.validation.heightMustBeInteger'))
        .min(50, t('quickCheck.validation.heightMin'))
        .max(250, t('quickCheck.validation.heightMax')),

      weight: z
        .number({
          message: t('quickCheck.validation.weightMustBeNumber'),
        })
        .int(t('quickCheck.validation.weightMustBeInteger'))
        .min(20, t('quickCheck.validation.weightMin'))
        .max(300, t('quickCheck.validation.weightMax')),

      // 4. Outpatient Diagnosis (last 3 years)
      outpatientDiagnosis: yesNoSchema,
      outpatientDiagnoses: diagnosisArraySchema,

      // 5. Inpatient Diagnosis (last 10 years, or 5 years if under 32)
      inpatientDiagnosis: yesNoSchema,
      inpatientDiagnoses: diagnosisArraySchema,

      // 6. Missing Teeth (excluding wisdom teeth)
      missingTeeth: z
        .number({
          message: t('quickCheck.validation.teethMustBeNumber'),
        })
        .int(t('quickCheck.validation.teethMustBeInteger'))
        .min(0, t('quickCheck.validation.teethMin'))
        .max(32, t('quickCheck.validation.teethMax')),

      // 7. Dental Replacements (crowns, implants, bridges, etc.)
      dentalReplacements: z
        .number({
          message: t('quickCheck.validation.dentalMustBeNumber'),
        })
        .int(t('quickCheck.validation.dentalMustBeInteger'))
        .min(0, t('quickCheck.validation.dentalMin'))
        .max(32, t('quickCheck.validation.dentalMax')),
    })
    .superRefine((data, ctx) => {
      // Validate outpatient diagnoses
      validateDiagnosisField(
        data.outpatientDiagnosis,
        data.outpatientDiagnoses,
        ctx,
        'outpatientDiagnoses',
        t('quickCheck.validation.outpatientDiagnosisRequired')
      );

      if (data.outpatientDiagnosis === 'yes') {
        validateIndividualDiagnoses(
          data.outpatientDiagnoses,
          ctx,
          'outpatientDiagnoses',
          t('quickCheck.validation.diagnosisRequired')
        );
      }

      // Validate inpatient diagnoses
      validateDiagnosisField(
        data.inpatientDiagnosis,
        data.inpatientDiagnoses,
        ctx,
        'inpatientDiagnoses',
        t('quickCheck.validation.inpatientDiagnosisRequired')
      );

      if (data.inpatientDiagnosis === 'yes') {
        validateIndividualDiagnoses(
          data.inpatientDiagnoses,
          ctx,
          'inpatientDiagnoses',
          t('quickCheck.validation.diagnosisRequired')
        );
      }
    });
};

/**
 * Base schema for server-side validation (without translations)
 * Uses the same factory function but with a simple key-returning function
 */
export const quickCheckFormSchema = createQuickCheckFormSchema(
  ((key: string) => key) as ReturnType<typeof useTranslations>
);

// Export TypeScript type
export type QuickCheckFormData = z.infer<typeof quickCheckFormSchema>;

// Default values for the form
export const quickCheckFormDefaults: Partial<QuickCheckFormData> = {
  previousInsurance: undefined,
  profession: '',
  height: undefined,
  weight: undefined,
  outpatientDiagnosis: undefined,
  outpatientDiagnoses: [],
  inpatientDiagnosis: undefined,
  inpatientDiagnoses: [],
  missingTeeth: undefined,
  dentalReplacements: undefined,
};
