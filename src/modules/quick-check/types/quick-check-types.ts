import type { QuickCheckFormData } from './quick-check-schemas';

/**
 * Type definitions for Quick Check module
 */

// Profession option for dropdown
export interface ProfessionOption {
  value: string;
  label: string;
  riskCategory?: 'low' | 'medium' | 'high'; // For future risk assessment
}

// BMI calculation result
export interface BMIResult {
  bmi: number;
  category: 'underweight' | 'normal' | 'overweight' | 'obese';
  message: string;
}

// Re-export form data type
export type { QuickCheckFormData };
