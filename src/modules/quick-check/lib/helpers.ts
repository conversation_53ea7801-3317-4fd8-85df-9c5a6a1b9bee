import type { BMIResult } from '@/modules/quick-check/types';

import { BMI_THRESHOLDS } from './constants';

/**
 * Helper functions for Quick Check module
 */

/**
 * Calculate BMI from height (cm) and weight (kg)
 */
export function calculateBMI(
  heightCm: number | null | undefined,
  weightKg: number | null | undefined
): number | null {
  if (!heightCm || !weightKg || heightCm <= 0 || weightKg <= 0) {
    return null;
  }

  const heightM = heightCm / 100;
  const bmi = weightKg / (heightM * heightM);

  return Math.round(bmi * 10) / 10; // Round to 1 decimal place
}

/**
 * Get BMI category and message based on BMI value
 */
export function getBMIResult(bmi: number | null): BMIResult | null {
  if (bmi === null) {
    return null;
  }

  let category: BMIResult['category'];
  let message: string;

  if (bmi < BMI_THRESHOLDS.UNDERWEIGHT) {
    category = 'underweight';
    message = 'quickCheck.bmi.underweight';
  } else if (bmi < BMI_THRESHOLDS.NORMAL) {
    category = 'normal';
    message = 'quickCheck.bmi.normal';
  } else if (bmi < BMI_THRESHOLDS.OVERWEIGHT) {
    category = 'overweight';
    message = 'quickCheck.bmi.overweight';
  } else {
    category = 'obese';
    message = 'quickCheck.bmi.obese';
  }

  return {
    bmi,
    category,
    message,
  };
}

/**
 * Validate diagnosis array - remove empty strings
 */
export function cleanDiagnosisArray(diagnoses: string[] | undefined): string[] {
  if (!diagnoses) {
    return [];
  }

  return diagnoses.filter((d) => d && d.trim().length > 0);
}

/**
 * Check if user should provide BMI data based on age
 * (BMI check is for age 16+)
 */
export function shouldShowBMIFields(age: number | null | undefined): boolean {
  if (!age) {
    return true; // Show by default if age is unknown
  }

  return age >= 16;
}

/**
 * Get diagnosis period text based on age for stationary diagnosis
 */
export function getStationaryDiagnosisPeriod(
  age: number | null | undefined
): number {
  if (!age) {
    return 10; // Default to 10 years
  }

  return age <= 32 ? 5 : 10;
}
