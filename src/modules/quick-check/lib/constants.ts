import type { ProfessionOption } from '@/modules/quick-check/types';

/**
 * Constants for Quick Check module
 */

// Profession options for dropdown
// Note: These are example professions - should be replaced with actual list from requirements
export const PROFESSION_OPTIONS: ProfessionOption[] = [
  {
    value: 'employee',
    label: 'quickCheck.professions.employee',
    riskCategory: 'low',
  },
  {
    value: 'civil_servant',
    label: 'quickCheck.professions.civil_servant',
    riskCategory: 'low',
  },
  {
    value: 'self_employed',
    label: 'quickCheck.professions.self_employed',
    riskCategory: 'low',
  },
  {
    value: 'student',
    label: 'quickCheck.professions.student',
    riskCategory: 'low',
  },
  {
    value: 'retiree',
    label: 'quickCheck.professions.retiree',
    riskCategory: 'low',
  },
  {
    value: 'unemployed',
    label: 'quickCheck.professions.unemployed',
    riskCategory: 'medium',
  },
  {
    value: 'homemaker',
    label: 'quickCheck.professions.homemaker',
    riskCategory: 'low',
  },
  {
    value: 'craftsman',
    label: 'quickCheck.professions.craftsman',
    riskCategory: 'medium',
  },
  {
    value: 'construction_worker',
    label: 'quickCheck.professions.construction_worker',
    riskCategory: 'high',
  },
  {
    value: 'doctor',
    label: 'quickCheck.professions.doctor',
    riskCategory: 'low',
  },
  {
    value: 'teacher',
    label: 'quickCheck.professions.teacher',
    riskCategory: 'low',
  },
  {
    value: 'engineer',
    label: 'quickCheck.professions.engineer',
    riskCategory: 'low',
  },
  {
    value: 'merchant',
    label: 'quickCheck.professions.merchant',
    riskCategory: 'low',
  },
  {
    value: 'other',
    label: 'quickCheck.professions.other',
    riskCategory: 'medium',
  },
];

// Maximum number of diagnosis fields
export const MAX_DIAGNOSES = 5;

// BMI thresholds (WHO standards)
export const BMI_THRESHOLDS = {
  UNDERWEIGHT: 18.5,
  NORMAL: 25,
  OVERWEIGHT: 30,
  OBESE: 30,
} as const;

// Minimum age for BMI check
export const MIN_AGE_FOR_BMI = 16;

// Maximum teeth count
export const MAX_TEETH_COUNT = 32;

// Diagnosis time periods
export const DIAGNOSIS_PERIODS = {
  AMBULANT_YEARS: 3,
  STATIONARY_YEARS: 10,
  STATIONARY_YEARS_UNDER_32: 5,
} as const;
