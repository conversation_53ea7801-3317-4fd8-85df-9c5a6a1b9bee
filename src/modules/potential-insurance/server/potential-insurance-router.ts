import { type Prisma } from '@prisma/client';
import { TRPCError } from '@trpc/server';
import z from 'zod';

import { createPotentialInsuranceSchema } from '@/modules/potential-insurance/types/potential-insurance-schema';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';

export type PotentialInsuranceCreateInput =
  Prisma.PotentialInsuranceCreateInput;

export const potentialInsuranceRouter = createTRPCRouter({
  addPotentialInsuranceToCase: protectedProcedure
    .input(
      z.object({
        caseId: z.number().int().positive(),
        data: createPotentialInsuranceSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const data: Prisma.PotentialInsuranceCreateInput = {
          insurerName: input.data.insurerName,
          productName: input.data.productName || '',
          createdByUserType: ctx.auth.isAdmin ? 'admin' : 'user',
          status: 'created',
          user: {
            connect: { userId: ctx.auth.user.userId },
          },
          case: {
            connect: { caseId: input.caseId },
          },
        };

        if (ctx.auth.isAgent) {
          const existingPotentialInsurancesCount =
            await prisma.potentialInsurance.count({
              where: { caseId: input.caseId, createdByUserType: 'user' },
            });
          if (existingPotentialInsurancesCount >= 5) {
            throw new TRPCError({
              code: 'INTERNAL_SERVER_ERROR',
              message: 'Cannot create more than 5 potential insurances.',
            });
          }
        }
        const createdPotentialInsurance =
          await prisma.potentialInsurance.create({
            data: data,
          });

        return createdPotentialInsurance;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create customer with case',
          cause: error,
        });
      }
    }),

  deleteOne: protectedProcedure
    .input(z.object({ potentialInsuranceId: z.number().int().positive() }))
    .mutation(async ({ input }) => {
      const potentialInsurance = await prisma.potentialInsurance.delete({
        where: { potentialInsuranceId: input.potentialInsuranceId },
      });

      if (!potentialInsurance) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Delete: Potential Insurance not found',
        });
      }

      return potentialInsurance;
    }),

  getOne: protectedProcedure
    .input(z.object({ id: z.number().int().positive() }))
    .query(async ({ input }) => {
      const potentialInsurance = await prisma.potentialInsurance.findFirst({
        where: { potentialInsuranceId: input.id },
      });

      if (!potentialInsurance) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'getOne: Potential Insurance not found',
        });
      }

      return potentialInsurance;
    }),

  getAllForCaseCreatedByAnyConsultant: protectedProcedure
    .input(z.object({ caseId: z.number().int().positive() }))
    .query(async ({ input }) => {
      const _case = await prisma.case.findFirst({
        where: { caseId: input.caseId },
      });

      if (!_case) {
        return [];
        // throw new TRPCError({
        //   code: 'NOT_FOUND',
        //   message: 'Potential Insurance not found',
        // });
      }

      const potentialInsuranceCreatedByConsultant =
        await prisma.potentialInsurance.findMany({
          where: {
            caseId: _case.caseId,
            createdByUserType: 'user',
          },
        });

      return potentialInsuranceCreatedByConsultant;
    }),

  getAllForCaseCreatedByKvcare: protectedProcedure
    .input(z.object({ caseId: z.number().int().positive() }))
    .query(async ({ input }) => {
      const _case = await prisma.case.findFirst({
        where: { caseId: input.caseId },
      });

      if (!_case) {
        return [];
        // throw new TRPCError({
        //   code: 'NOT_FOUND',
        //   message: 'Potential Insurance not found',
        // });
      }

      const potentialInsuranceCreatedByConsultant =
        await prisma.potentialInsurance.findMany({
          where: {
            caseId: _case.caseId,
            createdByUserType: { in: ['ai', 'admin'] },
          },
        });

      return potentialInsuranceCreatedByConsultant;
    }),

  customerHasPotentialInsuranceCreatedByConsultant: protectedProcedure
    .input(z.object({ customerId: z.number().int().positive() }))
    .query(async ({ input }) => {
      const caseForCustomer = await prisma.case.findFirst({
        where: { customerId: input.customerId },
      });

      if (!caseForCustomer) {
        return false;
      }

      const potentialInsuranceCreatedByConsultant =
        await prisma.potentialInsurance.findFirst({
          where: {
            caseId: caseForCustomer.caseId,
            createdByUserType: 'admin',
          },
        });

      return potentialInsuranceCreatedByConsultant != null;
    }),
});

export const potentialInsuranceRouterCaller =
  potentialInsuranceRouter.createCaller({});
