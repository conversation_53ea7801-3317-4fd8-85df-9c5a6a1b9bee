import {
  type Application,
  type Case,
  CaseStatus,
  type PotentialInsurance,
  type Questionnaire,
  type RiskPreRequest,
} from '@prisma/client';

// Types for enhanced case data
export type CaseWithInsuranceData = Case & {
  potentialInsurances: PotentialInsurance[];
  applications: Application[];
  riskPreRequests: RiskPreRequest[];
  caseCustomer: Array<{
    questionnaires: Questionnaire[];
  }>;
};

// Dashboard status types - now using Prisma enum directly
export type DashboardCaseStatus = CaseStatus;

export interface CaseStatusInfo {
  status: DashboardCaseStatus;
  displayText: string;
  actionRequired: boolean;
  count?: number; // For processing and applications ready
  insuranceCount?: number; // Number of insurances for this case
  totalPremium?: number; // Total premium amount
}

// Mapping function to determine if a status requires action
export function isActionRequiredStatus(status: CaseStatus): boolean {
  const actionRequiredStatuses: CaseStatus[] = [
    CaseStatus.DataEntry,
    CaseStatus.Processing,
    CaseStatus.ApplicationReady,
  ];
  return actionRequiredStatuses.includes(status);
}

// Mapping function to determine if a status is completed
export function isCompletedStatus(status: CaseStatus): boolean {
  const completedStatuses: CaseStatus[] = [
    CaseStatus.Applied,
    CaseStatus.Rejected,
  ];
  return completedStatuses.includes(status);
}

// Translation function type for status helpers
export type StatusTranslationFunction = (
  key: string,
  values?: Record<string, any>
) => string;

/**
 * Determines the dashboard status info for a case using the new enum-based system
 */
export function calculateCaseStatus(
  caseData: CaseWithInsuranceData,
  t?: StatusTranslationFunction
): CaseStatusInfo {
  const { status, potentialInsurances, applications, riskPreRequests } =
    caseData;

  // Count insurances
  const insuranceCount = potentialInsurances.length;

  // Calculate total premium from applications or potential insurances
  const totalPremium =
    applications
      .filter((app) => app.premiumAmount)
      .reduce((sum, app) => sum + Number(app.premiumAmount || 0), 0) ||
    potentialInsurances
      .filter((ins) => ins.monthlyPremium)
      .reduce((sum, ins) => sum + Number(ins.monthlyPremium || 0), 0);

  // Get display text and action required status based on the case status
  const getStatusInfo = (status: CaseStatus) => {
    switch (status) {
      case CaseStatus.DataEntry:
        return {
          displayText: t ? t('case_status.data_entry') : 'Data Entry',
          actionRequired: true,
        };
      case CaseStatus.RiskInquiryRunning:
        return {
          displayText: t
            ? t('case_status.risk_inquiry_running')
            : 'Risk Inquiry Running',
          actionRequired: false,
        };
      case CaseStatus.ApplicationReady:
        return {
          displayText: t
            ? t('case_status.application_ready')
            : 'Application Ready',
          actionRequired: true,
        };
      case CaseStatus.Processing: {
        const processingCount =
          riskPreRequests.filter((req) => req.status === 'InputRequired')
            .length +
          applications.filter((app) => app.status === 'Processing').length;
        return {
          displayText: t
            ? t('case_status.processing', { count: processingCount })
            : `${processingCount} Processing`,
          actionRequired: true,
          count: processingCount,
        };
      }
      case CaseStatus.Applied:
        return {
          displayText: t ? t('case_status.applied') : 'Applied',
          actionRequired: false,
        };
      case CaseStatus.Rejected:
        return {
          displayText: t ? t('case_status.rejected') : 'Rejected',
          actionRequired: false,
        };
      default:
        return {
          displayText: 'Unknown Status',
          actionRequired: false,
        };
    }
  };

  const statusInfo = getStatusInfo(status);

  return {
    status,
    displayText: statusInfo.displayText,
    actionRequired: statusInfo.actionRequired,
    count: statusInfo.count,
    insuranceCount,
    totalPremium,
  };
}

/**
 * Calculates dashboard statistics from case data
 */
export interface DashboardStatistics {
  openCases: {
    count: number;
    totalPremium: number;
  };
  completedCases: {
    count: number;
    totalPremium: number;
  };
  actionRequiredCases: {
    count: number;
    breakdown: {
      dataEntry: number;
      processing: number;
      applicationsReady: number;
    };
  };
  totalInsurances: number;
}

export function calculateDashboardStatistics(
  cases: CaseWithInsuranceData[]
): DashboardStatistics {
  const stats: DashboardStatistics = {
    openCases: { count: 0, totalPremium: 0 },
    completedCases: { count: 0, totalPremium: 0 },
    actionRequiredCases: {
      count: 0,
      breakdown: { dataEntry: 0, processing: 0, applicationsReady: 0 },
    },
    totalInsurances: 0,
  };

  cases.forEach((caseData) => {
    const statusInfo = calculateCaseStatus(caseData);

    // Count total insurances
    stats.totalInsurances += statusInfo.insuranceCount || 0;

    // Categorize cases
    if (statusInfo.actionRequired) {
      stats.actionRequiredCases.count++;

      switch (statusInfo.status) {
        case CaseStatus.DataEntry:
          stats.actionRequiredCases.breakdown.dataEntry++;
          break;
        case CaseStatus.Processing:
          stats.actionRequiredCases.breakdown.processing++;
          break;
        case CaseStatus.ApplicationReady:
          stats.actionRequiredCases.breakdown.applicationsReady++;
          break;
      }
    }

    // Determine if case is open or completed
    if (isCompletedStatus(statusInfo.status)) {
      stats.completedCases.count++;
      stats.completedCases.totalPremium += statusInfo.totalPremium || 0;
    } else {
      stats.openCases.count++;
      stats.openCases.totalPremium += statusInfo.totalPremium || 0;
    }
  });

  return stats;
}
