import { TRPCError } from '@trpc/server';

import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import { getOneInputSchema } from '@/utils/baseSchema';
import prisma from '@/utils/prisma-db';

export const welcomeRouter = createTRPCRouter({
  getOne: protectedProcedure
    .input(getOneInputSchema)
    .query(async ({ input }) => {
      const existingCase = await prisma.case.findFirst({
        where: { caseId: input.caseId },
      });

      if (!existingCase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      return existingCase;
    }),
});

// IMPORTANT: Create caller for server-side use(also can be used in other routers)

export const welcomeRouterCaller = welcomeRouter.createCaller({});
