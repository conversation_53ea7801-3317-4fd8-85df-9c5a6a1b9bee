import { Box, Typography } from '@mui/material';

import { type UserGetCurrentUser } from '@/modules/user/types/user-types';

interface WelcomeHeaderProps {
  user: UserGetCurrentUser;
}

export function WelcomeHeader({ user }: WelcomeHeaderProps) {
  const getGreetingText = () => {
    const username = user.firstName || user.username;
    if (user.firstName && user.lastName) {
      return `Willkommen Herr ${user.firstName} ${user.lastName}`;
    }
    return `Willkommen ${username}`;
  };

  return (
    <Box
      sx={{
        backgroundColor: 'var(--teal)',
        py: 2.5,
        px: 4,
        width: '100%',
      }}
    >
      <Typography
        variant="h5"
        component="h1"
        sx={{
          color: 'white',
          fontWeight: 700,
          textAlign: 'center',
          fontSize: '1.5rem',
        }}
      >
        {getGreetingText()}
      </Typography>
    </Box>
  );
}
