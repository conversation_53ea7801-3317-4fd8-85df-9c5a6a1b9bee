import CheckIcon from '@mui/icons-material/Check';
import { Box, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

interface InfoItem {
  title: string;
  description: string;
}

interface WelcomeInfoProps {
  subtitle?: string;
  items?: InfoItem[];
}

export function WelcomeInfo({ subtitle, items }: WelcomeInfoProps) {
  const t = useTranslations('welcome.info');

  const defaultItems = [
    {
      title: t('items.item1.title'),
      description: t('items.item1.description'),
    },
    {
      title: t('items.item2.title'),
      description: t('items.item2.description'),
    },
    {
      title: t('items.item3.title'),
      description: t('items.item3.description'),
    },
  ];

  const displayItems = items || defaultItems;
  return (
    <Stack
      direction="column"
      sx={{
        backgroundColor: 'var(--teal-light)',
        paddingLeft: '15px',
        paddingRight: '15px',
        paddingTop: '106px',
        paddingBottom: '48px',
        // height: '100%',
        minHeight: '500px',
        maxWidth: '550px',
        marginLeft: 'auto',
        marginRight: 'auto',
      }}
    >
      <Stack spacing={4}>
        {/* Title with Avatar */}
        <Stack direction="row" alignItems="center" spacing={2}>
          <Typography
            variant="h3"
            component="h2"
            sx={{
              fontWeight: 600,
              fontSize: '2.5rem',
            }}
          >
            <span style={{ color: 'var(--teal-dark)' }}>
              {t('title_prefix')}
            </span>
            <span style={{ color: 'var(--teal)' }}>{t('title_brand')}</span>
            <span style={{ color: 'var(--teal-dark)' }}>
              {t('title_suffix')}
            </span>
          </Typography>
        </Stack>

        {/* Subtitle if provided */}
        {subtitle && (
          <Typography variant="body1" color="text.secondary">
            {subtitle}
          </Typography>
        )}

        {/* Info Items with Check Icons */}
        <Stack spacing={3.5} sx={{ mt: 1.5 }}>
          {displayItems.map((item, index) => (
            <Stack
              key={index}
              direction="row"
              spacing={3}
              alignItems="flex-start"
            >
              {/* Custom checkmark matching Figma design */}
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  borderRadius: '50%',
                  backgroundColor: 'var(--teal)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                  mt: 0.25,
                }}
              >
                <CheckIcon
                  sx={{
                    color: 'white',
                    fontSize: 28,
                  }}
                />
              </Box>
              <Box>
                <Typography
                  variant="h6"
                  component="h3"
                  sx={{
                    color: 'var(--teal-dark)',
                    fontWeight: 600,
                    mb: 1,
                    fontSize: '1.25rem',
                  }}
                >
                  {item.title}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{
                    color: '#333',
                    lineHeight: 1.6,
                    fontSize: '1.1rem',
                  }}
                >
                  {item.description}
                </Typography>
              </Box>
            </Stack>
          ))}
        </Stack>
      </Stack>
    </Stack>
  );
}
