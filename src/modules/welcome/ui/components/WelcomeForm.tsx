'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Alert, Box, Button, Stack, Typography } from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { FormProvider, useForm } from 'react-hook-form';

import { FormCheckbox, FormSelect, FormText } from '@/components/form-inputs';
import { type UserGetCurrentUser } from '@/modules/user/types/user-types';
import {
  type WelcomeFormData,
  welcomeFormSchema,
} from '@/modules/welcome/types/welcome-schemas';
import { useTRPC } from '@/trpc/client';

interface WelcomeFormProps {
  title?: string;
  user: UserGetCurrentUser;
}

export function WelcomeForm({ title, user }: WelcomeFormProps) {
  const t = useTranslations('welcome.form');
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { update, data: session } = useSession();
  const regularCellMinWidth = '250px';
  const smallCellMinWidth = '120px';

  // tRPC mutation for updating user profile
  const {
    mutateAsync: updateProfile,
    isPending,
    error,
  } = useMutation(
    trpc.user.updateProfile.mutationOptions({
      onSuccess: async (data, variables) => {
        console.log('Profile updated successfully', data);
        console.log('variables', variables);

        await queryClient.invalidateQueries({
          queryKey: trpc.user.getCurrentUser.queryKey(),
        });

        // Ispravno ažuriranje sesije - proslediti ceo user objekat
        const updatedSession = await update({
          ...session?.user,
          termsAndConditionsAccepted: variables.termsAndConditionsAccepted,
        });
        console.log('Updated session:', updatedSession);

        // Reset form to clear any unsaved changes
        reset();

        // Redirect to overview page after successful terms acceptance
        if (variables.termsAndConditionsAccepted) {
          router.push('/start');
        }
      },
      onError: (error: any) => {
        console.error('Profile update error:', error);
      },
    })
  );

  // React Hook Form setup
  const form = useForm<WelcomeFormData>({
    resolver: zodResolver(welcomeFormSchema),
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phoneNumber: user?.phoneNumber || '',
      street: user?.street || '',
      houseNumber: user?.houseNumber || '',
      postalCode: user?.postalCode || '',
      city: user?.city || '',
      termsAndConditionsAccepted: false,
      // TODO check if we need to add this fields to the user model
      salutation:
        (user?.salutation as WelcomeFormData['salutation']) || undefined,
      academicTitle:
        (user?.academicTitle as WelcomeFormData['academicTitle']) || undefined,
      agencyName:
        (user?.agencyName as WelcomeFormData['agencyName']) || undefined,
    },
  });

  const { handleSubmit, reset } = form;

  const onSubmit = async (data: WelcomeFormData) => {
    console.log('onSubmit', data);
    await updateProfile(data);
  };

  return (
    <Box
      sx={{
        backgroundColor: 'white',
        paddingLeft: '15px',
        paddingRight: '15px',
        // paddingLeft: '76px',
        // paddingRight: '48px',
        paddingTop: '111px',
        paddingBottom: '48px',
        height: '100%',
        minHeight: '500px',
        maxWidth: '580px',
        marginLeft: 'auto',
        marginRight: 'auto',
      }}
    >
      <Typography
        variant="h4"
        component="h2"
        sx={{
          color: 'var(--teal)',
          fontWeight: 600,
          mb: 4,
          fontSize: '1.75rem',
        }}
      >
        {title || t('title')}
      </Typography>

      {/* Success/Error Message */}
      {error?.message && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error?.message}
        </Alert>
      )}

      <FormProvider {...form}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            {/* Anrede Row */}
            <Box sx={{ display: 'flex', gap: 2.5, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormSelect<WelcomeFormData>
                  name="salutation"
                  label="welcome.form.labels.salutation"
                  options={[
                    { value: 'Herr', label: 'welcome.form.options.mr' },
                    { value: 'Frau', label: 'welcome.form.options.ms' },
                    { value: 'Divers', label: 'welcome.form.options.diverse' },
                  ]}
                  autoComplete={false}
                  textFieldProps={{
                    size: 'medium',
                    slotProps: {
                      inputLabel: { sx: { fontSize: '1.1rem' } },
                      select: { sx: { fontSize: '1.1rem' } },
                    },
                  }}
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormSelect<WelcomeFormData>
                  name="academicTitle"
                  label="welcome.form.labels.title"
                  options={[
                    { value: '', label: 'welcome.form.options.noTitle' },
                    { value: 'Dr.', label: 'welcome.form.options.dr' },
                    { value: 'Prof.', label: 'welcome.form.options.prof' },
                    {
                      value: 'Prof. Dr.',
                      label: 'welcome.form.options.profDr',
                    },
                  ]}
                  autoComplete={false}
                  textFieldProps={{
                    size: 'medium',
                    slotProps: {
                      inputLabel: { sx: { fontSize: '1.1rem' } },
                      select: { sx: { fontSize: '1.1rem' } },
                    },
                  }}
                />
              </Box>
            </Box>

            {/* Name Row */}
            <Box sx={{ display: 'flex', gap: 2.5, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormText<WelcomeFormData>
                  name="firstName"
                  label="welcome.form.labels.firstName"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormText<WelcomeFormData>
                  name="lastName"
                  label="welcome.form.labels.lastName"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
            </Box>

            {/* Contact Row */}
            <Box sx={{ display: 'flex', gap: 2.5, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormText<WelcomeFormData>
                  name="email"
                  label="welcome.form.labels.email"
                  type="email"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
              <Box
                sx={{
                  flex: 1,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormText<WelcomeFormData>
                  name="phoneNumber"
                  label="welcome.form.labels.phone"
                  type="tel"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
            </Box>

            {/* Address Row */}
            <Box sx={{ display: 'flex', gap: 2.5, flexWrap: 'wrap' }}>
              <Box sx={{ flex: 2, minWidth: '300px', maxWidth: '100%' }}>
                <FormText<WelcomeFormData>
                  name="street"
                  label="welcome.form.labels.street"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
              <Box sx={{ flex: 1, minWidth: '120px', maxWidth: '100%' }}>
                <FormText<WelcomeFormData>
                  name="houseNumber"
                  label="welcome.form.labels.houseNumber"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
            </Box>

            {/* PLZ/Ort Row */}
            <Box sx={{ display: 'flex', gap: 2.5, flexWrap: 'wrap' }}>
              <Box
                sx={{ flex: 1, minWidth: smallCellMinWidth, maxWidth: '100%' }}
              >
                <FormText<WelcomeFormData>
                  name="postalCode"
                  label="welcome.form.labels.postalCode"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
              <Box
                sx={{
                  flex: 2,
                  minWidth: regularCellMinWidth,
                  maxWidth: '100%',
                }}
              >
                <FormText<WelcomeFormData>
                  name="city"
                  label="welcome.form.labels.city"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
            </Box>

            {/* Agenturname */}
            <Box sx={{ display: 'flex', gap: 2.5 }}>
              <Box sx={{ width: '260px', maxWidth: '100%' }}>
                <FormText<WelcomeFormData>
                  name="agencyName"
                  label="welcome.form.labels.agencyName"
                  size="medium"
                  slotProps={{
                    inputLabel: { sx: { fontSize: '1.1rem' } },
                    input: { sx: { fontSize: '1.1rem' } },
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                {/* Empty space to match Figma layout */}
              </Box>
            </Box>

            {/* AGB Checkbox */}
            <FormCheckbox<WelcomeFormData>
              name="termsAndConditionsAccepted"
              label="welcome.form.termsCheckbox"
              checkboxProps={{
                sx: {
                  color: 'var(--teal)',
                  '&.Mui-checked': {
                    color: 'var(--teal)',
                  },
                },
              }}
              formControlLabelProps={{
                sx: {
                  mt: 1,
                  '& .MuiFormControlLabel-label': {
                    fontSize: '1.1rem',
                  },
                },
              }}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              variant="contained"
              size="large"
              disabled={isPending}
              sx={{
                backgroundColor: 'var(--teal-dark)',
                '&:hover': {
                  backgroundColor: 'var(--dark-button-hover)',
                },
                mt: 2,
                py: 1.5,
                px: 4,
                alignSelf: 'flex-end',
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              {isPending ? t('messages.saving') : t('submit_button')}
            </Button>
          </Stack>
        </form>
      </FormProvider>
    </Box>
  );
}
