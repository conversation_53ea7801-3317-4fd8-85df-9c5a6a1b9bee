'use client';

import { Stack } from '@mui/material';

import { useUserSuspense } from '@/modules/user/hooks/useUser';
import { WelcomeForm } from '@/modules/welcome/ui/components/WelcomeForm';
import { WelcomeHeader } from '@/modules/welcome/ui/components/WelcomeHeader';
import { WelcomeInfo } from '@/modules/welcome/ui/components/WelcomeInfo';

export const WelcomeView = () => {
  const { user } = useUserSuspense();

  return (
    <Stack
      flexDirection="column"
      sx={{
        width: '100%',
        minHeight: '100vh',
        display: 'flex',
        flexWrap: 'wrap',
      }}
    >
      {/* Header Section */}
      <WelcomeHeader user={user} />

      {/* Two Column Layout */}
      <Stack
        direction="row"
        sx={{
          display: 'flex',
          width: '100%',
          minHeight: '100vh',
          flexWrap: 'wrap',
        }}
      >
        {/* Left Column - Info Section */}
        <Stack
          direction="row"
          sx={{
            display: 'flex',
            flex: 1,
            backgroundColor: 'var(--teal-light)',
            // marginLeft: 10,
            // marginLeft: 'auto',
            // marginRight: 'auto',
            width: '50%',
            minWidth: '350px',
            maxWidth: '100%',
          }}
        >
          <WelcomeInfo />
        </Stack>

        {/* Right Column - Form Section */}
        <Stack
          sx={{ flex: 1, minWidth: '350px', width: '50%', maxWidth: '100%' }}
        >
          <WelcomeForm user={user} />
        </Stack>
      </Stack>
    </Stack>
  );
};
