import { CaseStatus } from '@prisma/client';
import { z } from 'zod';

// Schema for creating a new case
export const casesCreateSchema = z.object({
  customerId: z.number().int().positive(),
  assignedUserId: z.number().int().positive().optional(),
  caseType: z.string().min(1),
  status: z.nativeEnum(CaseStatus).default(CaseStatus.DataEntry),
  caseNumber: z.string().optional(), // Usually auto-generated
});

// Schema for updating an existing case
export const updateCaseSchema = z.object({
  caseId: z.number().int().positive(),
  customerId: z.number().int().positive().optional(),
  assignedUserId: z.number().int().positive().optional().nullable(),
  caseType: z.string().min(1).optional(),
  status: z.nativeEnum(CaseStatus).optional(),
});

// Schema for welcome form submission (matching User model fields)
export const welcomeFormSchema = z.object({
  // Personal Information (matching User model)
  firstName: z.string().min(1, 'Vorname ist erforderlich'),
  lastName: z.string().min(1, 'Nachname ist erforderlich'),

  // Contact Information
  email: z.email({ message: 'Ungültige E-Mail-Adresse' }),
  phoneNumber: z.string().optional(),

  // Address Information
  street: z.string().optional().nullable(),
  houseNumber: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  city: z.string().optional().nullable(),

  // Terms and Conditions
  termsAndConditionsAccepted: z.boolean().refine((val) => val === true, {
    message: 'AGB müssen akzeptiert werden',
  }),

  // UI-only fields (not stored in User model, used only for form display)
  salutation: z
    .enum(['Herr', 'Frau', 'Divers'], {
      message: 'Anrede ist erforderlich',
    })
    .optional(),
  academicTitle: z.enum(['Dr.', 'Prof.', 'Prof. Dr.']).optional(),
  agencyName: z.string().optional().nullable(),
});

// Export types for TypeScript usage
export type CasesCreateInput = z.infer<typeof casesCreateSchema>;
export type UpdateCaseInput = z.infer<typeof updateCaseSchema>;
export type WelcomeFormData = z.infer<typeof welcomeFormSchema>;
