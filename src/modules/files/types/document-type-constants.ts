/**
 * Single source of truth for all document types
 * Add new document types here and they will automatically propagate to:
 * - Zod validation schemas
 * - TypeScript types
 * - Frontend dropdown options
 */
export const DOCUMENT_TYPE_VALUES = [
  'examination_booklet',
  'medical_report',
  'lab_report',
  'hospital_report',
  'vaccination_card',
  'application_form',
  'authorization_consent',
  'rehab_report',
  'medication_plan',
  'medical_certificate',
  'psychological_report',
  'surgery_report',
  'followup_report',
  'termination_confirmation',
  'advisory_protocol',
  'health_self_disclosure',
  'risk_pre_request',
  'insurance_policy',
  'modified_document',
  'additional_information',
  'other_document',
] as const;

/**
 * TypeScript type derived from DOCUMENT_TYPE_VALUES
 * This is equivalent to an enum but without duplication
 */
export type DocumentType = (typeof DOCUMENT_TYPE_VALUES)[number];
