import { z } from 'zod';

import { DOCUMENT_TYPE_VALUES } from './document-type-constants';

// Zod enum created from the single source of truth
export const documentTypeEnum = z.enum(DOCUMENT_TYPE_VALUES);

// Input schemas for file operations
export const initiateFileUploadInputSchema = z.object({
  caseId: z.number().int().positive(),
  fileName: z.string(),
});

export const confirmFileUploadInputSchema = z.object({
  caseId: z.number().int().positive(),
  fileName: z.string(),
  fileId: z.number().int().positive(),
});

export const uploadPdfInputSchema = z.object({
  caseId: z.number().int().positive(),
  fileName: z.string(),
  fileBuffer: z.instanceof(Uint8Array),
  optionalMessage: z.string().optional(),
  documentType: documentTypeEnum,
});

export const getFileInputSchema = z.object({
  caseId: z.number().int().positive(),
  fileName: z.string(),
  attachmentId: z.number().int().positive(),
});

// Type exports
export type InitiateFileUploadInput = z.infer<
  typeof initiateFileUploadInputSchema
>;
export type ConfirmFileUploadInput = z.infer<
  typeof confirmFileUploadInputSchema
>;
export type UploadPdfInput = z.infer<typeof uploadPdfInputSchema>;
export type GetFileInput = z.infer<typeof getFileInputSchema>;
export type DocumentType = z.infer<typeof documentTypeEnum>;
