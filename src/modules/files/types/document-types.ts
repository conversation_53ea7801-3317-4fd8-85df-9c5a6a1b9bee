import {
  DOCUMENT_TYPE_VALUES,
  type DocumentType,
} from './document-type-constants';

// Re-export for convenience
export { DOCUMENT_TYPE_VALUES, type DocumentType };

// Helper function to get translation key for a document type
export const getDocumentTypeTranslationKey = (
  documentType: DocumentType
): string => {
  return `document_upload.dropdown_document_type.options.${documentType}`;
};
