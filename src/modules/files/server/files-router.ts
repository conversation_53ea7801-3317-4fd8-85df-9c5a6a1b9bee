import {
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { type Prisma } from '@prisma/client';
import { TRPCError } from '@trpc/server';
import { type Readable } from 'stream';
import { z } from 'zod';

import {
  confirmFileUploadInputSchema,
  getFileInputSchema,
  initiateFileUploadInputSchema,
  uploadPdfInputSchema,
} from '@/modules/files/types/file-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';
import { s3, s3bucketName } from '@/utils/s3';

export const filesRouter = createTRPCRouter({
  initiateFileUpload: protectedProcedure
    .input(initiateFileUploadInputSchema)
    .mutation(async ({ input, ctx }) => {
      const caseWhere: Prisma.CaseWhereInput = {
        caseId: input.caseId,
      };
      if (ctx.auth.isAgent) {
        caseWhere.assignedUserId = ctx.auth.user.userId;
      }

      const ccase = await prisma.case.findFirst({
        where: caseWhere,
      });

      if (!ccase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      //   const filePath = `attachments/case-${input.caseId}/user-${userId}/${crypto.randomUUID()}.pdf`;
      const filePath = makeS3PathForAttachment(
        ccase.caseId,
        ctx.auth.user.userId,
        input.fileName
      );

      const uploadUrl = await getSignedUrl(
        s3,
        new PutObjectCommand({ Bucket: s3bucketName, Key: filePath as string }),
        { expiresIn: 60 }
      );

      const newAttachment = await prisma.attachment.create({
        data: {
          uploadedBy: ctx.auth.user.userId,
          caseId: ccase.caseId,
          status: 'pending',
          fileName: input.fileName,
          filePath: filePath,
        },
      });

      return { uploadUrl: uploadUrl, fileId: newAttachment.attachmentId };
    }),

  confirmFileUpload: protectedProcedure
    .input(confirmFileUploadInputSchema)
    .mutation(async ({ input, ctx }) => {
      const caseWhere: Prisma.CaseWhereInput = {
        caseId: input.caseId,
      };
      if (ctx.auth.isAgent) {
        caseWhere.assignedUserId = ctx.auth.user.userId;
      }

      const ccase = await prisma.case.findFirst({
        where: caseWhere,
      });

      if (!ccase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const uploadedByWhere: Prisma.AttachmentWhereInput = {
        attachmentId: input.fileId,
      };
      if (ctx.auth.isAgent) {
        uploadedByWhere.uploadedBy = ctx.auth.user.userId;
      }

      const attachment = await prisma.attachment.findFirst({
        where: uploadedByWhere,
      });

      if (!attachment) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      if (
        attachment.fileName !== input.fileName ||
        (ctx.auth.isAgent && attachment.uploadedBy !== ctx.auth.user.userId)
      ) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const fileExists = await s3
        .send(
          new HeadObjectCommand({
            Bucket: s3bucketName,
            Key: attachment.filePath,
          })
        )
        .then(() => true) // success → file exists
        .catch((err) => {
          if (
            err.name === 'NotFound' ||
            err.$metadata?.httpStatusCode === 404
          ) {
            return false; // not found
          }
          throw err; // rethrow other errors
        });

      if (fileExists === true) {
        const updatedAttachment = await prisma.attachment.update({
          where: { attachmentId: input.fileId },
          data: {
            status: 'uploaded',
          },
        });
        if (updatedAttachment) {
          return true;
        }
      }

      //   return uploadUrl;
      return false;
    }),

  uploadPdf: protectedProcedure
    .input(uploadPdfInputSchema)
    .mutation(async ({ input, ctx }) => {
      console.log('uploadPdf input:', input);
      const userId = ctx.auth.user.userId;

      const caseWhere: Prisma.CaseWhereInput = {
        caseId: input.caseId,
      };

      if (ctx.auth.isAgent) {
        caseWhere.assignedUserId = userId;
      }

      console.log('caseWhere', caseWhere);

      try {
        //   const filePath = `attachments/case-${input.caseId}/user-${userId}/${crypto.randomUUID()}.pdf`;
        const ccase = await prisma.case.findFirst({
          where: caseWhere,
        });

        console.log('ccase', ccase);

        if (!ccase) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Case not found',
          });
        }
        const filePath = makeS3PathForAttachment(
          ccase.caseId,
          userId,
          input.fileName
        );
        const uploadCommand = new PutObjectCommand({
          Bucket: s3bucketName,
          Key: filePath,
          Body: Buffer.from(input.fileBuffer),
          ContentType: 'application/pdf',
        });

        const uploadResult = await s3.send(uploadCommand);
        console.log('uploadResult', uploadResult);

        // Create attachment and message in a transaction
        const result = await prisma.$transaction(async (tx) => {
          // Create the attachment
          const newAttachment = await tx.attachment.create({
            data: {
              uploadedBy: userId,
              caseId: ccase.caseId,
              status: 'uploaded',
              fileName: input.fileName,
              filePath: filePath,
              documentType: input.documentType,
            },
          });

          // Create a message to link the attachment
          // The message represents the upload action by the user
          // Generate subject based on document type
          const subject = input.documentType
            ? input.documentType === 'modified_document'
              ? 'Follow-up'
              : 'Additional Information'
            : `Document uploaded: ${input.fileName}`;

          const newMessage = await tx.message.create({
            data: {
              caseId: ccase.caseId,
              senderId: userId,
              recipientId: userId, // For now, sender and recipient are the same (the user who uploaded)
              subject: subject,
              body: input.optionalMessage ?? null,
              isRead: false,
            },
          });

          // Update the attachment to link it to the message
          await tx.attachment.update({
            where: { attachmentId: newAttachment.attachmentId },
            data: { messageId: newMessage.messageId },
          });

          return { attachment: newAttachment, message: newMessage };
        });

        if (result) {
          return true;
        }
        return false;
      } catch (err: any) {
        console.error('upload pdf error', err);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: err?.message || 'Internal Server Error',
        });
      }
    }),

  getFileDownloadUrl: protectedProcedure
    .input(getFileInputSchema)
    .mutation(async ({ input, ctx }) => {
      let attachmentWhere: Prisma.AttachmentWhereInput = {
        attachmentId: input.attachmentId,
      };

      if (ctx.auth.isAgent) {
        attachmentWhere = {
          ...attachmentWhere,
          case: {
            assignedUserId: ctx.auth.user.userId,
          },
        };
      }

      const attachment = await prisma.attachment.findFirst({
        where: attachmentWhere,
      });

      if (!attachment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Attachment not found',
        });
      }

      if (attachment.fileName !== input.fileName) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'File name mismatch',
        });
      }

      const downloadUrl = await getSignedUrl(
        s3,
        new GetObjectCommand({
          Bucket: s3bucketName,
          Key: attachment.filePath,
        }),
        { expiresIn: 60 }
      );

      if (downloadUrl && attachment.status === 'uploaded') {
        const updatedAttachment = await prisma.attachment.update({
          where: { attachmentId: input.attachmentId },
          data: {
            status: 'requested',
          },
        });
      }

      return downloadUrl;
    }),

  getFilesList: protectedProcedure
    // .input(z.object({ take: z.number().default(5) }))
    .query(async ({ input, ctx }) => {
      if (ctx.auth.isAgent) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Not authorized' });
      }

      try {
        const attachments = await prisma.attachment.findMany({
          take: 100,
          orderBy: {
            uploadedAt: 'asc',
          },
        });

        return attachments;
      } catch (error: any) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }
    }),

  downloadFile: protectedProcedure
    .input(z.object({ attachmentId: z.number().int().positive() }))
    .mutation(async ({ input, ctx }) => {
      if (ctx.auth.isAgent) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Not authorized' });
      }

      const attachment = await prisma.attachment.findFirst({
        where: { attachmentId: input.attachmentId },
      });
      if (!attachment) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      try {
        const downloadCommand = new GetObjectCommand({
          Bucket: s3bucketName,
          Key: attachment.filePath,
        });

        const downloadResponse = await s3.send(downloadCommand);

        if (!downloadResponse.Body) {
          throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
        }

        const buffer = await streamToBuffer(downloadResponse.Body as Readable);

        return { blob: Array.from(buffer), fileName: attachment.fileName };
      } catch (error: any) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }
    }),

  getDownloadFileUrl: protectedProcedure
    .input(z.object({ attachmentId: z.number().int().positive() }))
    .mutation(async ({ input, ctx }) => {
      if (ctx.auth.isAgent) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Not authorized' });
      }

      const attachment = await prisma.attachment.findFirst({
        where: { attachmentId: input.attachmentId },
      });
      if (!attachment) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      try {
        const downloadCommand = new GetObjectCommand({
          Bucket: s3bucketName,
          Key: attachment.filePath,
          ResponseContentDisposition: `attachment; filename="${attachment.fileName}"`,
        });

        const downloadUrl = await getSignedUrl(s3, downloadCommand, {
          expiresIn: 60,
        });

        if (
          typeof downloadUrl !== 'string' ||
          downloadUrl?.trim()?.length <= 0
        ) {
          throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
        }

        return { url: downloadUrl, fileName: attachment.fileName };
      } catch (error: any) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }
    }),

  getCaseAttachments: protectedProcedure
    .input(z.object({ caseId: z.number().int().positive() }))
    .query(async ({ input, ctx }) => {
      const caseWhere: Prisma.CaseWhereInput = {
        caseId: input.caseId,
      };
      if (ctx.auth.isAgent) {
        caseWhere.assignedUserId = ctx.auth.user.userId;
      }

      // Fetch attachments with related data in a single query
      const attachments = await prisma.attachment.findMany({
        where: {
          case: caseWhere,
        },
        include: {
          message: {
            include: {
              riskPreRequest: {
                select: {
                  insurerName: true,
                  tariffName: true,
                },
              },
            },
          },
          riskPreRequest: {
            select: {
              insurerName: true,
              tariffName: true,
            },
          },
        },
        orderBy: {
          uploadedAt: 'asc',
        },
      });

      return attachments;
    }),
});

// IMPORTANT: Create caller for server-side use(also can be used in other routers)
export const filesRouterCaller = filesRouter.createCaller({});

async function streamToBuffer(stream: Readable): Promise<Buffer> {
  const chunks: Uint8Array[] = [];
  for await (const chunk of stream) {
    chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
}

export function makeS3PathForAttachment(
  caseId: string | number,
  userId: string | number,
  fileName: string
): string {
  return `attachments/case-${caseId}/user-${userId}/${crypto.randomUUID()}/${fileName}`;
}
