'use client';

import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';
import { useDebounceValue } from 'usehooks-ts';

export const customerSearchParamsClient = {
  limit: parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  offset: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  sortField: parseAsString
    .withDefault('customerId')
    .withOptions({ clearOnDefault: true }),
  sortDirection: parseAsString
    .withDefault('asc')
    .withOptions({ clearOnDefault: true }),
  search: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  firstName: parseAsString
    .withDefault('')
    .withOptions({ clearOnDefault: true }),
  lastName: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  email: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  city: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  country: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
};

export const useCustomerFilters = () => {
  const [urlFilters, setUrlFilters] = useQueryStates(
    customerSearchParamsClient,
    {
      shallow: true,
      // limitUrlUpdates: throttle(1000), // Maximum once per second,
    }
  );

  // console.log('urlFilters', urlFilters);

  const [debouncedSearch] = useDebounceValue(urlFilters, 300);

  // Return URL filters for immediate UI feedback and API filters for queries
  return {
    // Immediate filters for UI (input values, URL state)
    filters: urlFilters,
    // Setter function for immediate URL updates
    setFilters: setUrlFilters,
  } as const;
};
