import { CaseStatus, type Prisma } from '@prisma/client';
import { TRPCError } from '@trpc/server';

import { auditCustomerChange } from '@/lib/audit-helpers';
import { AuditOperation, mongoDBPrisma } from '@/lib/mongo-db-client';
import { customerHistoryQuerySchema } from '@/modules/customer/types/customer-audit-schemas';
import { customerSearchParamsSchema } from '@/modules/customer/types/customer-params';
import {
  createInCaseGroupSchema,
  customerCreateSchema,
  customerCreateWithCaseSchema,
  getOneCustomerInputSchema,
  removeCustomerSchema,
  updateCustomerSchema,
  updateCustomerWithRelationSchema,
} from '@/modules/customer/types/customer-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';

export const customersRouter = createTRPCRouter({
  getOneWithCaseGroupRelationType: protectedProcedure
    .input(getOneCustomerInputSchema)
    .query(async ({ input, ctx }) => {
      // Extract user info from mock context

      const existingCustomer = await prisma.customer.findFirst({
        where: { customerId: input.customerId },
        include: {
          cases: {
            include: { caseGroupMembership: true },
          },
        },
      });

      const caseGroupMembership = existingCustomer?.cases?.find(
        (it) => it.caseGroupMembership?.length > 0
      );
      const caseGroupRelation = caseGroupMembership?.caseGroupMembership.find(
        (it) => it.relationType != null
      );
      const caseGroupRelationType = caseGroupRelation?.relationType;

      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      return {
        customer: existingCustomer,
        relationType: caseGroupRelationType,
      };
    }),

  getOne: protectedProcedure
    .input(getOneCustomerInputSchema)
    .query(async ({ input }) => {
      const existingCustomer = await prisma.customer.findFirst({
        where: { customerId: input.customerId },
      });

      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      return existingCustomer;
    }),

  getMany: protectedProcedure
    .input(customerSearchParamsSchema)
    .query(async ({ input }) => {
      const {
        limit,
        offset,
        sortField,
        sortDirection,
        search,
        firstName,
        lastName,
        email,
        city,
        country,
      } = input;

      // Build where clause for filtering
      const whereClause: Prisma.CustomerWhereInput = {};

      if (search) {
        whereClause.OR = [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (firstName) {
        whereClause.firstName = { contains: firstName, mode: 'insensitive' };
      }

      if (lastName) {
        whereClause.lastName = { contains: lastName, mode: 'insensitive' };
      }

      if (email) {
        whereClause.email = { contains: email, mode: 'insensitive' };
      }

      if (city) {
        whereClause.city = { contains: city, mode: 'insensitive' };
      }

      if (country) {
        whereClause.country = { contains: country, mode: 'insensitive' };
      }

      const orderBy = { [sortField]: sortDirection };

      const [items, total] = await prisma.$transaction([
        prisma.customer.findMany({
          where: whereClause,
          orderBy,
          skip: offset,
          take: limit,
          include: {
            cases: {
              select: {
                caseId: true,
                caseNumber: true,
                caseType: true,
                status: true,
                createdAt: true,
              },
            },
          },
        }),
        prisma.customer.count({ where: whereClause }),
      ]);

      const meta = {
        total,
        pageCount: Math.ceil(total / limit),
        pageSize: limit,
        page: Math.floor(offset / limit) + 1,
      };

      return { items, meta };
    }),

  create: protectedProcedure
    .input(customerCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      const createdCustomer = await prisma.customer.create({ data: input });

      // Create audit log for customer creation
      await auditCustomerChange({
        customerId: createdCustomer.customerId,
        userId: userId!,
        operation: AuditOperation.CREATE,
        dataBefore: null,
        dataAfter: createdCustomer,
        action: 'CustomerCreated',
      });

      return createdCustomer;
    }),

  createWithCaseGroup: protectedProcedure
    .input(customerCreateWithCaseSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.auth.user.userId;

      console.log('createWithCaseGroup input:', input);

      try {
        const result = await prisma.$transaction(async (tx) => {
          // Extract customer data
          const {
            caseType,
            caseStatus,
            relationType,
            questionnaires,
            ...customerData
          } = input;

          // Create customer
          const customer = await tx.customer.create({ data: customerData });

          const createCaseGroupMembership: Prisma.CaseGroupMembershipCreateInput =
            {
              relationType: relationType,
              caseGroup: {
                create: {
                  groupType: '',
                  description: '',
                  assignedUserId: userId,
                },
                connectOrCreate: undefined,
                connect: undefined,
              },
              case: {
                create: {
                  customerId: customer.customerId,
                  caseType: caseType,
                  status: caseStatus,
                  assignedUserId: userId,
                },
                connectOrCreate: undefined,
                connect: undefined,
              },
            };

          const caseGroupMembership = await tx.caseGroupMembership.create({
            data: createCaseGroupMembership,
          });

          const updateCase = await tx.case.update({
            where: { caseId: caseGroupMembership.caseId },
            data: { caseGroupCaseGroupId: caseGroupMembership.caseGroupId },
          });

          // Create questionnaires if provided
          const createdQuestionnaires = [];
          if (questionnaires) {
            for (const questionnaireData of questionnaires) {
              const questionnaire = await tx.questionnaire.create({
                data: {
                  caseCaseId: caseGroupMembership.caseId,
                  formId: questionnaireData.formId,
                  type: questionnaireData.type,
                  status: questionnaireData.status || '0',
                  answersJson: questionnaireData.answersJson as any,
                },
              });
              createdQuestionnaires.push(questionnaire);
            }
          }

          const caseGroup = await tx.caseGroup.findFirst({
            where: { caseGroupId: caseGroupMembership.caseGroupId },
          });

          const ccase = await tx.case.findFirst({
            where: { caseId: caseGroupMembership.caseId },
          });

          // Create audit logs
          await auditCustomerChange({
            customerId: customer.customerId,
            userId: userId!,
            operation: AuditOperation.CREATE,
            dataBefore: null,
            dataAfter: customer,
            action: 'CustomerCreatedWithCase',
          });

          return {
            caseGroupId: caseGroupMembership.caseGroupId,
            caseGroupNumber: caseGroup?.caseGroupNumber,
            caseNumber: ccase?.caseNumber,
            customer,
            case: createCaseGroupMembership.case,
            questionnaires: createdQuestionnaires,
          };
        });

        return result;
      } catch (error) {
        console.error('createWithCaseGroup error', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create customer with case',
          cause: error,
        });
      }
    }),

  createInCaseGroup: protectedProcedure
    .input(createInCaseGroupSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.auth.user.userId;

      console.log('createInCaseGroup input:', input);

      try {
        const result = await prisma.$transaction(async (tx) => {
          // Extract customer data
          const {
            customerData,
            caseGroupId,
            relationType,
            caseType,
            questionnaires,
          } = input;

          if (ctx.auth.isAdmin) {
            const group = await tx.caseGroup.findFirst({
              where: { caseGroupId: caseGroupId, assignedUserId: userId },
            });

            if (!group) {
              throw new TRPCError({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to create customer with case',
                cause:
                  'Case group not found or user is not allowed to modify it.',
              });
            }
          }

          const customer = await tx.customer.create({ data: customerData });

          const createCaseGroupMembership: Prisma.CaseGroupMembershipCreateInput =
            {
              relationType: relationType,
              caseGroup: {
                create: undefined,
                connectOrCreate: undefined,
                connect: { caseGroupId: caseGroupId },
              },
              case: {
                create: {
                  customerId: customer.customerId,
                  caseType,
                  status: CaseStatus.DataEntry,
                  assignedUserId: userId,
                },
                connectOrCreate: undefined,
                connect: undefined,
              },
            };

          const caseGroupMembership = await tx.caseGroupMembership.create({
            data: createCaseGroupMembership,
          });

          // Create questionnaires if provided
          const createdQuestionnaires = [];
          if (questionnaires) {
            for (const questionnaireData of questionnaires) {
              const questionnaire = await tx.questionnaire.create({
                data: {
                  caseCaseId: caseGroupMembership.caseId,
                  formId: questionnaireData.formId,
                  type: questionnaireData.type,
                  status: questionnaireData.status || '0',
                  answersJson: questionnaireData.answersJson as any,
                },
              });
              createdQuestionnaires.push(questionnaire);
            }
          }

          const updatedCase = await tx.case.update({
            where: { caseId: caseGroupMembership.caseId },
            data: {
              caseGroupCaseGroupId: caseGroupMembership.caseGroupId,
            },
          });

          // Create audit logs
          await auditCustomerChange({
            customerId: customer.customerId,
            userId: userId!,
            operation: AuditOperation.CREATE,
            dataBefore: null,
            dataAfter: customer,
            action: 'CustomerCreatedWithCase',
          });

          return {
            customer,
            case: updatedCase,
            questionnaires: createdQuestionnaires,
          };
        });

        return result;
      } catch (error) {
        console.error('createWithCaseGroup error', error);
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create customer with case',
          cause: error,
        });
      }
    }),

  createWithCase: protectedProcedure
    .input(customerCreateWithCaseSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.auth.user.userId;

      try {
        const result = await prisma.$transaction(async (tx) => {
          // Extract customer data
          const {
            caseType,
            caseStatus,
            assignedUserId,
            relationType,
            questionnaires,
            ...customerData
          } = input;

          // Create customer
          const customer = await tx.customer.create({ data: customerData });

          // Create case
          const createdCase = await tx.case.create({
            data: {
              customerId: customer.customerId,
              caseType,
              status: caseStatus,
              assignedUserId,
            },
          });

          // Create questionnaires if provided
          const createdQuestionnaires = [];
          if (questionnaires) {
            for (const questionnaireData of questionnaires) {
              const questionnaire = await tx.questionnaire.create({
                data: {
                  // TODO: caseCaseId
                  formId: questionnaireData.formId,
                  type: questionnaireData.type,
                  status: questionnaireData.status || 'Pending',
                  answersJson: questionnaireData.answersJson as any,
                },
              });
              createdQuestionnaires.push(questionnaire);
            }
          }

          // Create audit logs
          await auditCustomerChange({
            customerId: customer.customerId,
            userId: userId!,
            operation: AuditOperation.CREATE,
            dataBefore: null,
            dataAfter: customer,
            action: 'CustomerCreatedWithCase',
          });

          return {
            customer,
            case: createdCase,
            questionnaires: createdQuestionnaires,
          };
        });

        return result;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create customer with case',
          cause: error,
        });
      }
    }),

  updateWithGroupRelation: protectedProcedure
    .input(updateCustomerWithRelationSchema)
    .mutation(async ({ ctx, input }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      const { customerId, ...updateData } = input.customer;
      const relationType = input.relationType;

      // Check if customer exists and capture before state
      const existingCustomer = await prisma.customer.findFirst({
        where: { customerId: customerId },
      });
      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      const updatedCustomer = await prisma.customer.update({
        where: { customerId: customerId },
        data: updateData,
        include: { cases: true },
      });

      const caseGroupId = updatedCustomer.cases?.[0]?.caseGroupCaseGroupId;

      if (typeof caseGroupId === 'number') {
        const membership = await prisma.caseGroupMembership.findFirst({
          where: { caseGroupId: caseGroupId },
        });
        if (membership) {
          const updateGroupMembership = await prisma.caseGroupMembership.update(
            {
              where: {
                caseGroupMembershipId: membership.caseGroupMembershipId,
              },
              data: { relationType: relationType },
            }
          );
        }
      }

      // Create audit log for customer update
      await auditCustomerChange({
        customerId,
        userId: userId!,
        operation: AuditOperation.UPDATE,
        dataBefore: existingCustomer,
        dataAfter: updatedCustomer,
        action: 'CustomerUpdated',
      });

      return updatedCustomer;
    }),

  update: protectedProcedure
    .input(updateCustomerSchema)
    .mutation(async ({ ctx, input }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      const { customerId, ...updateData } = input;

      // Check if customer exists and capture before state
      const existingCustomer = await prisma.customer.findFirst({
        where: { customerId },
      });
      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      const updatedCustomer = await prisma.customer.update({
        where: { customerId },
        data: updateData,
      });

      // Create audit log for customer update
      await auditCustomerChange({
        customerId,
        userId: userId!,
        operation: AuditOperation.UPDATE,
        dataBefore: existingCustomer,
        dataAfter: updatedCustomer,
        action: 'CustomerUpdated',
      });

      return updatedCustomer;
    }),

  remove: protectedProcedure
    .input(removeCustomerSchema)
    .mutation(async ({ ctx, input }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      // Check if customer exists and capture before state
      const existingCustomer = await prisma.customer.findFirst({
        where: { customerId: input.customerId },
      });
      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      const deletedCustomer = await prisma.customer.delete({
        where: { customerId: input.customerId },
      });

      // Create audit log for customer deletion
      await auditCustomerChange({
        customerId: input.customerId,
        userId: userId!,
        operation: AuditOperation.DELETE,
        dataBefore: existingCustomer,
        dataAfter: null,
        action: 'CustomerDeleted',
      });

      return deletedCustomer;
    }),

  deleteCustomerWithCase: protectedProcedure
    .input(removeCustomerSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.auth.user.userId;

      const caseWhereClause: Prisma.CaseWhereInput = {
        customerId: input.customerId,
      };

      if (ctx.auth.isAgent) {
        caseWhereClause.assignedUserId = userId;
      }

      const caseWithCustomerAndMembership = await prisma.case.findFirst({
        where: caseWhereClause,
        include: { caseGroupMembership: true, customer: true },
      });

      if (!caseWithCustomerAndMembership) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      // Check if customer exists and capture before state
      const existingCustomer = caseWithCustomerAndMembership.customer;

      if (!existingCustomer) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Customer not found',
        });
      }

      const deletedCustomer = await prisma.customer.delete({
        where: { customerId: input.customerId },
      });

      // Create audit log for customer deletion
      await auditCustomerChange({
        customerId: input.customerId,
        userId: userId,
        operation: AuditOperation.DELETE,
        dataBefore: existingCustomer,
        dataAfter: null,
        action: 'CustomerDeleted',
      });

      const deletedCase = await prisma.case.delete({
        where: { caseId: caseWithCustomerAndMembership.caseId },
      });

      const caseGroupMembershipIds =
        caseWithCustomerAndMembership.caseGroupMembership.map(
          (m) => m.caseGroupMembershipId
        );

      await prisma.caseGroupMembership.deleteMany({
        where: { caseGroupMembershipId: { in: caseGroupMembershipIds } },
      });

      return deletedCustomer;
    }),

  getHistory: protectedProcedure
    .input(customerHistoryQuerySchema)
    .query(async ({ input, ctx: _ctx }) => {
      // Build where clause for filtering
      const whereClause: any = { customerId: input.customerId };

      if (input.operation) {
        whereClause.operation = input.operation;
      }

      if (input.userId) {
        whereClause.userId = input.userId;
      }

      if (input.startDate || input.endDate) {
        whereClause.timestamp = {};
        if (input.startDate) {
          whereClause.timestamp.gte = input.startDate;
        }
        if (input.endDate) {
          whereClause.timestamp.lte = input.endDate;
        }
      }

      // Get audit history with pagination from MongoDB
      const [history, totalCount] = await Promise.all([
        mongoDBPrisma.customerHistory.findMany({
          where: whereClause,
          orderBy: { timestamp: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        mongoDBPrisma.customerHistory.count({ where: whereClause }),
      ]);

      return {
        history,
        totalCount,
        hasMore: input.offset + input.limit < totalCount,
      };
    }),
});

export const customersRouterCaller = customersRouter.createCaller({});
