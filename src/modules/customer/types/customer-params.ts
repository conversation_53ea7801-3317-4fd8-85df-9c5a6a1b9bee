import {
  createLoader,
  createStandardSchemaV1,
  parseAsInteger,
  parseAsString,
} from 'nuqs/server';
import { type z } from 'zod';

export const customerSearchParamsServer = {
  limit: parseAsInteger.withDefault(10),
  offset: parseAsInteger.withDefault(0),
  sortField: parseAsString.withDefault('customerId'),
  sortDirection: parseAsString.withDefault('asc'),
  search: parseAsString,
  firstName: parseAsString,
  lastName: parseAsString,
  email: parseAsString,
  city: parseAsString,
  country: parseAsString,
};

export const loadCustomerSearchParams = createLoader(
  customerSearchParamsServer
);
export const customerSearchParamsSchema = createStandardSchemaV1(
  customerSearchParamsServer
);

export type CustomerSearchParams = z.infer<typeof customerSearchParamsSchema>;
