import { type Customer } from '@prisma/client';

import { type AppRouterOutputs } from '@/trpc/routers/_app';

// Base customer type from Prisma
export type CustomerModel = Customer;

export type CustomerCreateWithCaseGroupResult =
  AppRouterOutputs['customers']['createWithCaseGroup'];

// Customer with relations
export interface CustomerWithRelations extends Customer {
  cases?: Array<{
    caseId: number;
    caseNumber: string;
    caseType: string;
    status: string;
    createdAt: Date;
  }>;
}

// Customer list response
export interface CustomerListResponse {
  items: CustomerModel[];
  meta: {
    total: number;
    pageCount: number;
    pageSize: number;
    page: number;
  };
}

// Customer filter options
export interface CustomerFilters {
  search?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  city?: string;
  country?: string;
}

// Customer sort options
export type CustomerSortField =
  | 'customerId'
  | 'firstName'
  | 'lastName'
  | 'email'
  | 'city'
  | 'country'
  | 'createdAt';

export type CustomerSortDirection = 'asc' | 'desc';
