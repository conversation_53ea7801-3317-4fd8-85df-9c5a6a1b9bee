import { z } from 'zod';

/**
 * Schema for querying customer audit history
 */
export const customerHistoryQuerySchema = z.object({
  customerId: z.number().int().positive(),
  operation: z.enum(['CREATE', 'UPDATE', 'DELETE']).optional(),
  userId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  limit: z.number().int().positive().max(100).default(50),
  offset: z.number().int().nonnegative().default(0),
});

export type CustomerHistoryQueryInput = z.infer<
  typeof customerHistoryQuerySchema
>;
