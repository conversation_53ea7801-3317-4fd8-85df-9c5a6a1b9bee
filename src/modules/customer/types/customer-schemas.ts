import { CaseStatus, RelationType } from '@prisma/client';
import { z } from 'zod';

import { customerDataForCaseSchema } from '@/modules/case-customer/types/case-customer-schemas';

// Schema for getting a single customer
export const getOneCustomerInputSchema = z.object({
  customerId: z.number().int().positive(),
});

// Schema for creating a customer
export const customerCreateSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  dateOfBirth: z.coerce.date(),
  email: z.email('Invalid email format').optional(),
  gender: z.string().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
  street: z.string().optional().nullable(),
  houseNumber: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
});

export const createInCaseGroupSchema = z.object({
  customerData: customerDataForCaseSchema,
  caseGroupId: z.number().int().positive(),
  relationType: z.enum(RelationType),
  caseType: z.string().min(1, 'Case type is required'),
  questionnaires: z
    .array(
      z.object({
        formId: z.string().min(1, 'Form ID is required'),
        type: z.string().min(1, 'Type is required'),
        status: z.string().default('0'),
        answersJson: z.record(z.string(), z.any()).optional(),
      })
    )
    .optional(),
});

// Schema for creating customer with case
export const customerCreateWithCaseSchema = z
  .object({
    // Customer data
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    dateOfBirth: z.coerce.date(),
    gender: z.string().optional(),
    email: z.email('Invalid email format').optional(),
    phoneNumber: z.string().optional().nullable(),
    street: z.string().optional().nullable(),
    houseNumber: z.string().optional().nullable(),
    postalCode: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    country: z.string().optional().nullable(),
    // Case data
    caseType: z.string().min(1, 'Case type is required'),
    caseStatus: z.nativeEnum(CaseStatus).default(CaseStatus.DataEntry),
    assignedUserId: z.number().int().positive().optional(),
    // CaseCustomer data
    relationType: z.enum(RelationType),
    // Questionnaire data
    questionnaires: z
      .array(
        z.object({
          formId: z.string().min(1, 'Form ID is required'),
          type: z.string().min(1, 'Type is required'),
          status: z.string().default('0'),
          answersJson: z.record(z.string(), z.any()).optional(),
        })
      )
      .optional(),
  })
  .refine(
    (data) => {
      // If this is a paying customer, email is required
      if (
        data.relationType === RelationType.paying ||
        data.relationType === RelationType.paying_and_insured_primary
      ) {
        return data.email && data.email.trim().length > 0;
      }
      return true;
    },
    {
      message: 'Email is required for paying customers',
      path: ['email'],
    }
  );

// Schema for updating a customer
export const updateCustomerSchema = z.object({
  customerId: z.number().int().positive(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  dateOfBirth: z.coerce.date().optional(),
  email: z.email('Invalid email format').optional().nullable(),
  gender: z.string().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
  street: z.string().optional().nullable(),
  houseNumber: z.string().optional().nullable(),
  postalCode: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  salutation: z.string().optional().nullable(),
  academicTitle: z.string().optional().nullable(),
  agencyName: z.string().optional().nullable(),
});

export const updateCustomerWithRelationSchema = z.object({
  customer: updateCustomerSchema,
  relationType: z.enum(RelationType),
});

// Schema for removing a customer
export const removeCustomerSchema = z.object({
  customerId: z.number().int().positive(),
});

// Type exports
export type GetOneCustomerInput = z.infer<typeof getOneCustomerInputSchema>;
export type CustomerCreateInput = z.infer<typeof customerCreateSchema>;
export type CustomerUpdateInput = z.infer<typeof updateCustomerSchema>;
export type RemoveCustomerInput = z.infer<typeof removeCustomerSchema>;
