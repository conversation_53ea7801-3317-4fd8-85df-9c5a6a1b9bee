'use client';

import {
  AddCircle as AddCircleIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { Box, IconButton, Tooltip, Typography } from '@mui/material';
import { type Customer } from '@prisma/client';
import { useState } from 'react';

import { AdminCustomerList } from '@/modules/customer/ui/components/AdminCustomerList';
import { DeleteCustomerModal } from '@/modules/customer/ui/components/DeleteCustomerModal';

// Mock data based on Figma design - exactly matching the design
const mockCustomers: Customer[] = [
  {
    customerId: 1,
    firstName: 'Frank',
    lastName: 'Assekurichter',
    dateOfBirth: new Date('1975-03-15'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '030.268 55 34',
    street: 'Hauptstraße',
    houseNumber: '123',
    postalCode: '10115',
    city: 'Berlin',
    country: 'Germany',
    status: 'Eingeladen',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: null,
    agencyName: 'Assekurichter & Partner',
  },
  {
    customerId: 2,
    firstName: 'Thorsten',
    lastName: 'Müller-Terstenheim',
    dateOfBirth: new Date('1982-07-22'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '0177.200 63 44',
    street: 'Berliner Allee',
    houseNumber: '45',
    postalCode: '40212',
    city: 'Düsseldorf',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: 'Dr.',
    agencyName: 'Thorsten Müller-Terstenheim',
  },
  {
    customerId: 3,
    firstName: 'Frank',
    lastName: 'Assekurichter',
    dateOfBirth: new Date('1975-03-15'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '030.268 55 34',
    street: 'Hauptstraße',
    houseNumber: '123',
    postalCode: '10115',
    city: 'Berlin',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: null,
    agencyName: 'Assekurichter & Partner',
  },
  {
    customerId: 4,
    firstName: 'Thorsten',
    lastName: 'Müller-Terstenheim',
    dateOfBirth: new Date('1982-07-22'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '0177.200 63 44',
    street: 'Berliner Allee',
    houseNumber: '45',
    postalCode: '40212',
    city: 'Düsseldorf',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: 'Dr.',
    agencyName: 'Thorsten Müller-Terstenheim',
  },
  {
    customerId: 5,
    firstName: 'Frank',
    lastName: 'Assekurichter',
    dateOfBirth: new Date('1975-03-15'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '030.268 55 34',
    street: 'Hauptstraße',
    houseNumber: '123',
    postalCode: '10115',
    city: 'Berlin',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: null,
    agencyName: 'Assekurichter & Partner',
  },
  {
    customerId: 6,
    firstName: 'Thorsten',
    lastName: 'Müller-Terstenheim',
    dateOfBirth: new Date('1982-07-22'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '0177.200 63 44',
    street: 'Berliner Allee',
    houseNumber: '45',
    postalCode: '40212',
    city: 'Düsseldorf',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: 'Dr.',
    agencyName: 'Thorsten Müller-Terstenheim',
  },
  {
    customerId: 7,
    firstName: 'Frank',
    lastName: 'Assekurichter',
    dateOfBirth: new Date('1975-03-15'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '030.268 55 34',
    street: 'Hauptstraße',
    houseNumber: '123',
    postalCode: '10115',
    city: 'Berlin',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: null,
    agencyName: 'Assekurichter & Partner',
  },
  {
    customerId: 8,
    firstName: 'Thorsten',
    lastName: 'Müller-Terstenheim',
    dateOfBirth: new Date('1982-07-22'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '0177.200 63 44',
    street: 'Berliner Allee',
    houseNumber: '45',
    postalCode: '40212',
    city: 'Düsseldorf',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: 'Dr.',
    agencyName: 'Thorsten Müller-Terstenheim',
  },
  {
    customerId: 9,
    firstName: 'Frank',
    lastName: 'Assekurichter',
    dateOfBirth: new Date('1975-03-15'),
    gender: 'male',
    email: '<EMAIL>',
    phoneNumber: '030.268 55 34',
    street: 'Hauptstraße',
    houseNumber: '123',
    postalCode: '10115',
    city: 'Berlin',
    country: 'Germany',
    status: 'Active',
    createdAt: new Date('2024-09-02'),
    salutation: 'Herr',
    academicTitle: null,
    agencyName: 'Assekurichter & Partner',
  },
];

export const AdminCustomersView = () => {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deletingCustomerId, setDeletingCustomerId] = useState<number | null>(
    null
  );
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleDeleteCustomer = (customerId: number) => {
    setDeletingCustomerId(customerId);
    setDeleteModalOpen(true);
  };

  const handlePageChange = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const refetch = () => {
    // Mock refetch function
    console.log('Refetching data...');
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Header Section */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{ fontWeight: 'bold', mb: 1 }}
            >
              Daten zur Person
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Verwaltung und Suche von Kundendatensätzen
            </Typography>
          </Box>
          <Tooltip title="Kunde einladen">
            <IconButton size="small" sx={{ color: 'primary.main' }}>
              <AddCircleIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Action Icons */}
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Filter">
            <IconButton size="small">
              <FilterIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Suchen">
            <IconButton size="small">
              <SearchIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Results Summary */}
      <Box>
        <Typography variant="body2" color="text.secondary">
          9 Kunden gefunden
        </Typography>
      </Box>

      {/* Customer Table */}
      <AdminCustomerList
        customers={mockCustomers}
        totalCount={mockCustomers.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onDeleteCustomer={handleDeleteCustomer}
      />

      {/* Delete Customer Modal */}
      {deletingCustomerId && (
        <DeleteCustomerModal
          open={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setDeletingCustomerId(null);
          }}
          onSuccess={() => {
            refetch();
          }}
          customerId={deletingCustomerId}
        />
      )}
    </Box>
  );
};
