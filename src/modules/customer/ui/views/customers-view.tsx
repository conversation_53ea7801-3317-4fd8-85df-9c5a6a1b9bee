'use client';

import { Box, Button, Typography } from '@mui/material';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useState } from 'react';

import { useCustomerFilters } from '@/modules/customer/hooks/useCustomerFilters';
import { CreateCustomerModal } from '@/modules/customer/ui/components/CreateCustomerModal';
import { CustomerList } from '@/modules/customer/ui/components/CustomerList';
import { DeleteCustomerModal } from '@/modules/customer/ui/components/DeleteCustomerModal';
import { EditCustomerModal } from '@/modules/customer/ui/components/EditCustomerModal';
import { useTRPC } from '@/trpc/client';

export const CustomersView = () => {
  const { filters } = useCustomerFilters();
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [editingCustomerId, setEditingCustomerId] = useState<number | null>(
    null
  );
  const [deletingCustomerId, setDeletingCustomerId] = useState<number | null>(
    null
  );

  const trpc = useTRPC();
  const { data, refetch } = useSuspenseQuery(
    trpc.customers.getMany.queryOptions({ ...filters })
  );

  const handleCreateCustomer = () => {
    setCreateModalOpen(true);
  };

  const handleViewCustomer = (customerId: number) => {
    console.log('View customer:', customerId);
  };

  const handleEditCustomer = (customerId: number) => {
    setEditingCustomerId(customerId);
    setEditModalOpen(true);
  };

  const handleDeleteCustomer = (customerId: number) => {
    setDeletingCustomerId(customerId);
    setDeleteModalOpen(true);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Results Summary and Actions */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1" color="text.secondary">
          {data?.meta?.total} customer{data?.meta.total !== 1 ? 's' : ''} found
        </Typography>
        <Button
          variant="contained"
          color="success"
          onClick={handleCreateCustomer}
          sx={{ minWidth: 150 }}
        >
          Create Customer
        </Button>
      </Box>

      {/* Customer List */}
      <CustomerList
        customers={data?.items || []}
        onViewCustomer={handleViewCustomer}
        onEditCustomer={handleEditCustomer}
        onDeleteCustomer={handleDeleteCustomer}
      />

      {/* Pagination */}
      {data?.meta && data.meta.pageCount > 1 && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Showing {data.meta.pageSize * (data.meta.page - 1) + 1} to{' '}
            {Math.min(data.meta.pageSize * data.meta.page, data.meta.total)} of{' '}
            {data.meta.total} customers
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Button
              variant="outlined"
              size="small"
              disabled={data.meta.page <= 1}
            >
              Previous
            </Button>
            <Typography variant="body2" sx={{ px: 2 }}>
              Page {data.meta.page} of {data.meta.pageCount}
            </Typography>
            <Button
              variant="outlined"
              size="small"
              disabled={data.meta.page >= data.meta.pageCount}
            >
              Next
            </Button>
          </Box>
        </Box>
      )}

      {/* Create Customer Modal */}
      <CreateCustomerModal
        open={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        onSuccess={() => {
          refetch();
        }}
      />

      {/* Edit Customer Modal */}
      {editingCustomerId && (
        <EditCustomerModal
          open={editModalOpen}
          onClose={() => {
            setEditModalOpen(false);
            setEditingCustomerId(null);
          }}
          onSuccess={() => {
            refetch();
          }}
          customerId={editingCustomerId}
        />
      )}

      {/* Delete Customer Modal */}
      {deletingCustomerId && (
        <DeleteCustomerModal
          open={deleteModalOpen}
          onClose={() => {
            setDeleteModalOpen(false);
            setDeletingCustomerId(null);
          }}
          onSuccess={() => {
            refetch();
          }}
          customerId={deletingCustomerId}
        />
      )}
    </Box>
  );
};
