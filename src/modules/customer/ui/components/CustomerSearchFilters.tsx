'use client';

import { <PERSON>, But<PERSON>, Card, CardContent, TextField } from '@mui/material';

import { useCustomerFilters } from '@/modules/customer/hooks/useCustomerFilters';

export const CustomerSearchFilters = () => {
  const { filters, setFilters } = useCustomerFilters();

  const handleClearFilters = () => {
    setFilters({
      search: '',
      firstName: '',
      lastName: '',
      email: '',
      city: '',
      country: '',
      offset: 0, // Reset to first page when clearing filters
    });
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* Search Fields */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' },
              gap: 2,
            }}
          >
            <TextField
              placeholder="Search customers..."
              variant="outlined"
              size="small"
              value={filters.search}
              onChange={(e) => setFilters({ search: e.target.value })}
              helperText="Search across name and email"
            />
            <TextField
              placeholder="First name..."
              variant="outlined"
              size="small"
              value={filters.firstName}
              onChange={(e) => setFilters({ firstName: e.target.value })}
            />
            <TextField
              placeholder="Last name..."
              variant="outlined"
              size="small"
              value={filters.lastName}
              onChange={(e) => setFilters({ lastName: e.target.value })}
            />
          </Box>

          {/* Additional Filters */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' },
              gap: 2,
            }}
          >
            <TextField
              placeholder="Email..."
              variant="outlined"
              size="small"
              value={filters.email}
              onChange={(e) => setFilters({ email: e.target.value })}
            />
            <TextField
              placeholder="City..."
              variant="outlined"
              size="small"
              value={filters.city}
              onChange={(e) => setFilters({ city: e.target.value })}
            />
            <TextField
              placeholder="Country..."
              variant="outlined"
              size="small"
              value={filters.country || ''}
              onChange={(e) => setFilters({ country: e.target.value || null })}
            />
          </Box>

          {/* Clear Filters Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              size="small"
              onClick={handleClearFilters}
              disabled={
                !filters.search &&
                !filters.firstName &&
                !filters.lastName &&
                !filters.email &&
                !filters.city &&
                !filters.country
              }
            >
              Clear Filters
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};
