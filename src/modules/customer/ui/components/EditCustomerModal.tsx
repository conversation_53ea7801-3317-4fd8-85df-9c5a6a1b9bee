'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';

import { useTRPC } from '@/trpc/client';

interface EditCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  customerId: number;
}

const GENDER_OPTIONS = ['Male', 'Female', 'Other', 'Prefer not to say'];

export const EditCustomerModal: React.FC<EditCustomerModalProps> = ({
  open,
  onClose,
  onSuccess,
  customerId,
}) => {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [dateOfBirth, setDateOfBirth] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [street, setStreet] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [country, setCountry] = useState<string>('');

  const trpc = useTRPC();

  // Fetch existing customer data
  const {
    data: customerData,
    isLoading: isLoadingCustomer,
    error: customerError,
  } = useQuery({
    ...trpc.customers.getOne.queryOptions({ customerId }),
    enabled: open && customerId > 0,
  });

  // Update mutation
  const { mutateAsync: updateCustomerMutation } = useMutation(
    trpc.customers.update.mutationOptions({
      onSuccess: () => {
        setSubmitError(null);
        setIsSubmitting(false);
        onSuccess();
        onClose();
      },
      onError: (error: any) => {
        setSubmitError(error.message || 'Failed to update customer');
        setIsSubmitting(false);
      },
    })
  );

  // Format date for input field
  const formatDateForInput = (date: Date | string) => {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  };

  // Populate form fields when customer data is loaded
  useEffect(() => {
    if (customerData) {
      setFirstName(customerData.firstName || '');
      setLastName(customerData.lastName || '');
      setEmail(customerData.email || '');
      setPhoneNumber(customerData.phoneNumber || '');
      setDateOfBirth(formatDateForInput(customerData.dateOfBirth));
      setGender(customerData.gender || '');
      setStreet(customerData.street || '');
      setCity(customerData.city || '');
      setCountry(customerData.country || '');
    }
  }, [customerData]);

  const resetForm = () => {
    setFirstName('');
    setLastName('');
    setEmail('');
    setPhoneNumber('');
    setDateOfBirth('');
    setGender('');
    setStreet('');
    setCity('');
    setCountry('');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!firstName || !lastName || !email || !dateOfBirth) {
      setSubmitError('Please fill in all required fields');
      return;
    }

    setSubmitError(null);
    setIsSubmitting(true);

    // Prepare update data - only include fields that have values
    const updateData: any = {
      customerId,
      firstName,
      lastName,
      email,
      dateOfBirth: new Date(dateOfBirth),
    };

    // Add optional fields only if they have values
    if (phoneNumber) updateData.phoneNumber = phoneNumber;
    if (gender) updateData.gender = gender;
    if (street) updateData.street = street;
    if (city) updateData.city = city;
    if (country) updateData.country = country;

    updateCustomerMutation(updateData);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      setSubmitError(null);
      onClose();
    }
  };

  // Show loading state while fetching customer data
  if (isLoadingCustomer) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              py: 4,
            }}
          >
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state if customer data failed to load
  if (customerError) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Alert severity="error">
            Failed to load customer data. Please try again.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Edit Customer</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            {submitError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {submitError}
              </Alert>
            )}

            {/* First Name */}
            <TextField
              fullWidth
              required
              label="First Name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              variant="outlined"
            />

            {/* Last Name */}
            <TextField
              fullWidth
              required
              label="Last Name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              variant="outlined"
            />

            {/* Email */}
            <TextField
              fullWidth
              required
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              variant="outlined"
            />

            {/* Phone Number */}
            <TextField
              fullWidth
              label="Phone Number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              variant="outlined"
            />

            {/* Date of Birth */}
            <TextField
              fullWidth
              required
              label="Date of Birth"
              type="date"
              value={dateOfBirth}
              onChange={(e) => setDateOfBirth(e.target.value)}
              variant="outlined"
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />

            {/* Gender */}
            <FormControl fullWidth>
              <InputLabel>Gender</InputLabel>
              <Select
                value={gender}
                onChange={(e) => setGender(e.target.value)}
                label="Gender"
              >
                <MenuItem value="">
                  <em>Select Gender</em>
                </MenuItem>
                {GENDER_OPTIONS.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Address Fields */}
            <TextField
              fullWidth
              label="Street Address"
              value={street}
              onChange={(e) => setStreet(e.target.value)}
              variant="outlined"
            />

            <TextField
              fullWidth
              label="City"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              variant="outlined"
            />

            <TextField
              fullWidth
              label="Country"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              variant="outlined"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
          >
            {isSubmitting ? 'Updating...' : 'Update Customer'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
