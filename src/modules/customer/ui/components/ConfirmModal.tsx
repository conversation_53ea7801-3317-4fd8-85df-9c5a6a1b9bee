import { Button, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { KvcSecondaryButton } from '@/components/buttons/KvcSecondaryButton';
import { KvModal } from '@/components/modal/modal';

interface Props {
  onCloseModalClick: () => void;
  onConfirmButtonClick: () => void;
  onCancelButtonClick: () => void;
  cancelButtonLabelTranslationKey?: string;
  confirmButtonLabelTranslationkey?: string;
  headlineTextTranslationKey?: string;
  bodyTextTranslationKey?: string;
  buttonsDisabled?: boolean;
}

export function ConfirmModal(props: Props) {
  const t = useTranslations();
  return (
    <>
      <KvModal
        onClose={() => {
          props.onCloseModalClick?.();
        }}
      >
        <Stack direction="column" gap={2}>
          {typeof props.bodyTextTranslationKey === 'string' && (
            <Typography>{t(props.bodyTextTranslationKey)}</Typography>
          )}
          <Stack direction="row" gap={2}>
            {typeof props.confirmButtonLabelTranslationkey === 'string' && (
              <Button
                disabled={props.buttonsDisabled === true}
                onClick={() => {
                  props.onConfirmButtonClick?.();
                }}
              >
                {t(props.confirmButtonLabelTranslationkey)}
              </Button>
            )}
            {typeof props.cancelButtonLabelTranslationKey === 'string' && (
              <KvcSecondaryButton
                disabled={props.buttonsDisabled === true}
                onClick={() => {
                  props.onCancelButtonClick?.();
                }}
              >
                {t(props.cancelButtonLabelTranslationKey)}
              </KvcSecondaryButton>
            )}
          </Stack>
        </Stack>
      </KvModal>
    </>
  );
}
