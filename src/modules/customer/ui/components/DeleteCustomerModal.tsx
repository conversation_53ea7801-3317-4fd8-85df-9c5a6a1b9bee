'use client';

import { WarningAmber } from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';

import { useTRPC } from '@/trpc/client';

interface DeleteCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  customerId: number;
}

export const DeleteCustomerModal: React.FC<DeleteCustomerModalProps> = ({
  open,
  onClose,
  onSuccess,
  customerId,
}) => {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const trpc = useTRPC();

  // Fetch customer data to show in confirmation dialog
  const {
    data: customerData,
    isLoading: isLoadingCustomer,
    error: customerError,
  } = useQuery({
    ...trpc.customers.getOne.queryOptions({ customerId }),
    enabled: open && customerId > 0,
  });

  // Delete mutation
  const { mutateAsync: deleteCustomerMutation } = useMutation(
    trpc.customers.remove.mutationOptions({
      onSuccess: () => {
        setSubmitError(null);
        setIsDeleting(false);
        onSuccess();
        onClose();
      },
      onError: (error: any) => {
        setSubmitError(error.message || 'Failed to delete customer');
        setIsDeleting(false);
      },
    })
  );

  const handleDelete = async () => {
    setSubmitError(null);
    setIsDeleting(true);

    try {
      await deleteCustomerMutation({ customerId });
    } catch (error) {
      // Error is handled by the mutation's onError callback
    }
  };

  const handleClose = () => {
    if (!isDeleting) {
      setSubmitError(null);
      onClose();
    }
  };

  // Show loading state while fetching customer data
  if (isLoadingCustomer) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              py: 4,
            }}
          >
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // Show error state if customer data failed to load
  if (customerError) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Alert severity="error">
            Failed to load customer data. Please try again.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <WarningAmber color="warning" />
        Delete Customer
      </DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
          {submitError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {submitError}
            </Alert>
          )}

          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Warning:</strong> This action cannot be undone. The
              customer and all associated data will be permanently deleted.
            </Typography>
          </Alert>

          {customerData && (
            <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                Customer to be deleted:
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                {customerData.firstName} {customerData.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                ID: {customerData.customerId}
              </Typography>
              {customerData.email && (
                <Typography variant="body2" color="text.secondary">
                  Email: {customerData.email}
                </Typography>
              )}
              {customerData.city && (
                <Typography variant="body2" color="text.secondary">
                  Location: {customerData.city}
                  {customerData.country && `, ${customerData.country}`}
                </Typography>
              )}
            </Box>
          )}

          <Typography variant="body2" color="text.secondary">
            Are you sure you want to delete this customer? This will also
            delete:
          </Typography>
          <Box component="ul" sx={{ pl: 2, m: 0 }}>
            <Typography component="li" variant="body2" color="text.secondary">
              All associated cases and case history
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              All customer audit records
            </Typography>
            <Typography component="li" variant="body2" color="text.secondary">
              Any related questionnaires and applications
            </Typography>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={isDeleting}>
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          color="error"
          disabled={isDeleting}
          startIcon={isDeleting ? <CircularProgress size={20} /> : null}
        >
          {isDeleting ? 'Deleting...' : 'Delete Customer'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
