'use client';

import {
  <PERSON>,
  But<PERSON>,
  Card,
  CardActions,
  CardContent,
  Typography,
} from '@mui/material';
import { type Customer } from '@prisma/client';

interface CustomerListProps {
  customers: Customer[];
  onViewCustomer: (customerId: number) => void;
  onEditCustomer: (customerId: number) => void;
  onDeleteCustomer: (customerId: number) => void;
}

export const CustomerList = ({
  customers,
  onViewCustomer,
  onEditCustomer,
  onDeleteCustomer,
}: CustomerListProps) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: 'repeat(2, 1fr)',
            lg: 'repeat(3, 1fr)',
          },
          gap: 2,
        }}
      >
        {customers.map((customer) => (
          <Card
            key={customer.customerId}
            sx={{
              transition: 'box-shadow 0.2s',
              '&:hover': {
                boxShadow: 2,
              },
            }}
          >
            <CardContent>
              <Typography variant="h6" component="h3" gutterBottom>
                {customer.firstName} {customer.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                ID: {customer.customerId}
              </Typography>
              {customer.email && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {customer.email}
                </Typography>
              )}
              {customer.city && (
                <Typography variant="body2" color="text.secondary">
                  {customer.city}
                  {customer.country && `, ${customer.country}`}
                </Typography>
              )}
            </CardContent>

            <CardActions sx={{ gap: 1 }}>
              <Button
                size="small"
                variant="contained"
                color="primary"
                onClick={() => onViewCustomer(customer.customerId)}
              >
                View
              </Button>
              <Button
                size="small"
                variant="contained"
                color="success"
                onClick={() => onEditCustomer(customer.customerId)}
              >
                Edit
              </Button>
              <Button
                size="small"
                variant="contained"
                color="error"
                onClick={() => onDeleteCustomer(customer.customerId)}
              >
                Delete
              </Button>
            </CardActions>
          </Card>
        ))}
      </Box>

      {customers.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="body1" color="text.secondary">
            No customers found.
          </Typography>
        </Box>
      )}
    </Box>
  );
};
