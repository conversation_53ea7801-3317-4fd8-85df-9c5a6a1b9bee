'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import React, { useState } from 'react';

import { useTRPC } from '@/trpc/client';

interface CreateCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const GENDER_OPTIONS = ['Male', 'Female', 'Other', 'Prefer not to say'];

export const CreateCustomerModal: React.FC<CreateCustomerModalProps> = ({
  open,
  onClose,
  onSuccess,
}) => {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [dateOfBirth, setDateOfBirth] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [street, setStreet] = useState<string>('');
  const [city, setCity] = useState<string>('');
  const [country, setCountry] = useState<string>('');

  const trpc = useTRPC();

  const { mutateAsync: createCustomerMutation } = useMutation(
    trpc.customers.create.mutationOptions({
      onSuccess: () => {
        // Reset form
        resetForm();
        setSubmitError(null);
        setIsSubmitting(false);
        onSuccess();
        onClose();
      },
      onError: (error: any) => {
        setSubmitError(error.message || 'Failed to create customer');
        setIsSubmitting(false);
      },
    })
  );

  const resetForm = () => {
    setFirstName('');
    setLastName('');
    setEmail('');
    setPhoneNumber('');
    setDateOfBirth('');
    setGender('');
    setStreet('');
    setCity('');
    setCountry('');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!firstName || !lastName || !email || !dateOfBirth) {
      setSubmitError('Please fill in all required fields');
      return;
    }

    setSubmitError(null);
    setIsSubmitting(true);

    createCustomerMutation({
      firstName,
      lastName,
      email,
      phoneNumber: phoneNumber || undefined,
      dateOfBirth: new Date(dateOfBirth),
      gender: gender || undefined,
      street: street || undefined,
      city: city || undefined,
      country: country || undefined,
    });
  };

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      setSubmitError(null);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Create New Customer</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            {submitError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {submitError}
              </Alert>
            )}

            {/* First Name */}
            <TextField
              fullWidth
              required
              label="First Name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              variant="outlined"
            />

            {/* Last Name */}
            <TextField
              fullWidth
              required
              label="Last Name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              variant="outlined"
            />

            {/* Email */}
            <TextField
              fullWidth
              required
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              variant="outlined"
            />

            {/* Phone Number */}
            <TextField
              fullWidth
              label="Phone Number"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              variant="outlined"
            />

            {/* Date of Birth */}
            <TextField
              fullWidth
              required
              label="Date of Birth"
              type="date"
              value={dateOfBirth}
              onChange={(e) => setDateOfBirth(e.target.value)}
              variant="outlined"
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
              }}
            />

            {/* Gender */}
            <FormControl fullWidth>
              <InputLabel>Gender</InputLabel>
              <Select
                value={gender}
                onChange={(e) => setGender(e.target.value)}
                label="Gender"
              >
                <MenuItem value="">
                  <em>Select Gender</em>
                </MenuItem>
                {GENDER_OPTIONS.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Address Fields */}
            <TextField
              fullWidth
              label="Street Address"
              value={street}
              onChange={(e) => setStreet(e.target.value)}
              variant="outlined"
            />

            <TextField
              fullWidth
              label="City"
              value={city}
              onChange={(e) => setCity(e.target.value)}
              variant="outlined"
            />

            <TextField
              fullWidth
              label="Country"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              variant="outlined"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
          >
            {isSubmitting ? 'Creating...' : 'Create Customer'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
