'use client';

import { Delete as DeleteIcon } from '@mui/icons-material';
import {
  Box,
  Chip,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
} from '@mui/material';
import { type Customer } from '@prisma/client';
import { format } from 'date-fns';

interface AdminCustomerListProps {
  customers: Customer[];
  totalCount: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: unknown, newPage: number) => void;
  onRowsPerPageChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteCustomer: (customerId: number) => void;
}

export const AdminCustomerList = ({
  customers,
  totalCount,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  onDeleteCustomer,
}: AdminCustomerListProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'success';
      case 'Invited':
        return 'warning';
      case 'Draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatPhoneNumber = (phoneNumber: string | null) => {
    if (!phoneNumber) return '-';
    // Format phone number for display (simple formatting)
    return phoneNumber.replace(/(\d{3})(\d{3})(\d{2})(\d{2})/, '$1.$2 $3 $4');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  // Mock data for demonstration - in real app this would come from API
  const getMockContribution = (customerId: number) => {
    const amounts = [26045, 12553, 28233, 18999];
    return amounts[customerId % amounts.length];
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>
                Name
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 180 }}>
                Unternehmen
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 150 }}>
                Telefonnummer
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>
                Status
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 130 }}>
                Zuletzt Aktiv
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>
                Beitrag
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', width: 80 }}>
                Aktionen
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {customers.map((customer) => (
              <TableRow
                key={customer.customerId}
                hover
                sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
              >
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {customer.firstName} {customer.lastName}
                    </Typography>
                    {customer.email && (
                      <Typography variant="caption" color="text.secondary">
                        {customer.email}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {customer.agencyName || '-'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatPhoneNumber(customer.phoneNumber)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={customer.status || 'Active'}
                    color={getStatusColor(customer.status || 'Active')}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {format(customer.createdAt, 'dd.MM.yyyy')}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {formatCurrency(getMockContribution(customer.customerId))}
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => onDeleteCustomer(customer.customerId)}
                    sx={{ ml: 1 }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {customers.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="body1" color="text.secondary">
            Keine Kunden gefunden.
          </Typography>
        </Box>
      )}

      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={onPageChange}
        onRowsPerPageChange={onRowsPerPageChange}
        labelRowsPerPage="Zeilen pro Seite:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}–${to} von ${count !== -1 ? count : `mehr als ${to}`}`
        }
      />
    </Paper>
  );
};
