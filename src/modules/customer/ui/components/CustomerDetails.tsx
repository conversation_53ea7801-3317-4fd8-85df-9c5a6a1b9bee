'use client';

import { useSuspenseQuery } from '@tanstack/react-query';

import { useTRPC } from '@/trpc/client';

interface CustomerDetailsWrapperProps {
  customerId: number;
}

export function CustomerDetails({ customerId }: CustomerDetailsWrapperProps) {
  const trpc = useTRPC();
  const { data: customer } = useSuspenseQuery(
    trpc.customers.getOne.queryOptions({ customerId })
  );

  return (
    <div>
      <div>Customer Data:</div>
      {customer && (
        <>
          <div>id: {customer.customerId}</div>
          <div>
            {customer.firstName} {customer.lastName}
          </div>
        </>
      )}
    </div>
  );
}
