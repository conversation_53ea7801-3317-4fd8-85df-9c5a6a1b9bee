'use client';

import { LogoutOutlined } from '@mui/icons-material';
import { Button } from '@mui/material';
import { useTranslations } from 'next-intl';

import { signoutKeycloak } from '@/modules/auth/lib/auth-libs';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export function LogoutIconButton() {
  const t = useTranslations();

  return (
    <Button
      onClick={() => signoutKeycloak()}
      startIcon={
        <LogoutOutlined
          sx={{
            color: 'white',
            stroke: 'white',
            fill: 'white',
            fontSize: (theme) => theme.typography.h4.fontSize,
          }}
        />
      }
    >
      {t.rich('menu.button_logout_label', intlTranslationRichHelper)}
    </Button>
  );
}
