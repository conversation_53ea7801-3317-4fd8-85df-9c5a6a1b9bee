'use client';

import { Box, Container } from '@mui/material';
import { type ReactNode } from 'react';

interface AuthLayoutProps {
  children: ReactNode;
}

export function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #2faa97 0%, #34746a 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
      }}
    >
      <Container maxWidth="sm">
        {/* Content Card */}
        <Box
          sx={{
            width: '100%',
            maxWidth: 480,
            backgroundColor: 'white',
            borderRadius: 3,
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            mx: 'auto',
          }}
        >
          {children}
        </Box>
      </Container>
    </Box>
  );
}
