'use client';

import { <PERSON><PERSON>ontent, <PERSON>, Stack, Typography } from '@mui/material';
import { type FormEvent } from 'react';

import { keycloakSignIn } from '@/modules/auth/lib/auth-libs';

import { LoginLogo } from './LoginLogo';
import SignInButton from './SignInButton';

export function LoginForm() {
  // Form state

  const validateFields = () => {
    const errors: { username?: string; password?: string } = {};

    // setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateFields()) {
      return;
    }

    await keycloakSignIn();
  };

  return (
    <CardContent sx={{ p: 4 }}>
      <Stack spacing={4}>
        {/* Logo */}

        <LoginLogo />

        <Typography
          variant="h6"
          component="h1"
          textAlign="center"
          sx={{ color: '#2faa97', fontWeight: 500, fontSize: '1.25rem', mb: 3 }}
        >
          Melden Sie sich mit Ihren Zugangsdaten an
        </Typography>
        <SignInButton />

        {/* <form onSubmit={handleSubmit}>
          <Stack spacing={2.5}>
            <TextField
              placeholder="E-Mail-Adresse *"
              value={username}
              onChange={(e) => handleFieldChange('username', e.target.value)}
              fullWidth
              required
              error={!!fieldErrors.username}
              helperText={fieldErrors.username}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': { borderColor: '#2faa97' },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2faa97',
                    borderWidth: 2,
                  },
                },
                '& .MuiInputBase-input::placeholder': {
                  color: '#666',
                  opacity: 1,
                },
              }}
            /> */}

        {/* <TextField
              placeholder="Passwort *"
              type="password"
              value={password}
              onChange={(e) => handleFieldChange('password', e.target.value)}
              fullWidth
              required
              error={!!fieldErrors.password}
              helperText={fieldErrors.password}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': { borderColor: '#2faa97' },
                  '&.Mui-focused fieldset': {
                    borderColor: '#2faa97',
                    borderWidth: 2,
                  },
                },
                '& .MuiInputBase-input::placeholder': {
                  color: '#666',
                  opacity: 1,
                },
              }}
            /> */}

        {/* Remember Me Checkbox */}
        {/* <FormControlLabel
              control={
                <Checkbox
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  sx={{
                    color: '#2faa97',
                    '&.Mui-checked': { color: '#2faa97' },
                  }}
                />
              }
              label="Angemeldet bleiben"
              sx={{
                alignSelf: 'flex-start',
                '& .MuiFormControlLabel-label': {
                  fontSize: '0.875rem',
                  color: '#333',
                },
              }}
            /> */}

        {/* <Button
              type="submit"
              variant="contained"
              fullWidth
              sx={{
                mt: 1,
                py: 1.2,
                backgroundColor: '#2faa97',
                fontSize: '1rem',
                fontWeight: 600,
                textTransform: 'none',
                borderRadius: 1,
                '&:hover': { backgroundColor: '#34746a' },
                '&:disabled': { backgroundColor: '#ccc' },
              }}
            >
              Einloggen
            </Button>
          </Stack>
        </form> */}

        {/* Links Section */}
        <Stack spacing={1} alignItems="center" sx={{ mt: 2 }}>
          <Link
            href="#"
            underline="hover"
            sx={{ color: '#2faa97', fontSize: '0.875rem', fontWeight: 500 }}
          >
            Passwort vergessen?
          </Link>

          <Typography
            variant="body2"
            sx={{ color: '#666', fontSize: '0.875rem' }}
          >
            Noch kein Konto?{' '}
            <Link
              href="#"
              underline="hover"
              sx={{ color: '#2faa97', fontWeight: 500 }}
            >
              Zugang anfragen
            </Link>
          </Typography>
        </Stack>
      </Stack>
    </CardContent>
  );
}
