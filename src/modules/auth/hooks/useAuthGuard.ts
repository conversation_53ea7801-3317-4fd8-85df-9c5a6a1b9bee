'use client';

import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

export const useAuthGuard = (currentPath?: string) => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/login');
      return;
    }

    // If user hasn't accepted terms, redirect to welcome (except if already on welcome)
    if (
      session &&
      session.user?.termsAndConditionsAccepted === false &&
      currentPath !== '/welcome'
    ) {
      router.push('/welcome');
      return;
    }

    // If user has accepted terms and is on welcome page, redirect to overview
    if (
      session &&
      session.user?.termsAndConditionsAccepted === true &&
      currentPath === '/welcome'
    ) {
      router.push('/start');
      return;
    }
  }, [session, status, router, currentPath]);

  return { session, status };
};
