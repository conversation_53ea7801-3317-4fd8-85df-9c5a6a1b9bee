import { jwtDecode } from 'jwt-decode';
import NextAuth, { type DefaultSession, type User } from 'next-auth';
import Keycloak from 'next-auth/providers/keycloak';

import { extractKeycloakRoles } from '@/modules/auth/lib/keycloak-utils';
import { UserSyncService } from '@/modules/auth/services/user-sync-service';
import {
  type KeycloakAccessToken,
  type UserRole,
} from '@/modules/auth/types/keycloak-types';

export interface CustomAuthUser extends User {
  id: string;
  keycloakId?: string;
  userId?: number; // Database user ID for tRPC compatibility
  role?: UserRole;
  firstName?: string;
  lastName?: string;
  email: string;
  emailVerified: Date | null; // Required by NextAuth AdapterUser
  username?: string; // Keycloak username
  termsAndConditionsAccepted?: boolean; // For redirect logic
}

declare module 'next-auth' {
  interface Session {
    user?: CustomAuthUser & DefaultSession['user'];
  }
}
declare module '@auth/core/jwt' {
  interface JWT {
    user?: CustomAuthUser;
    accessToken?: string;
    refreshToken?: string;
  }
}

export const { handlers, auth, signIn, signOut, unstable_update } = NextAuth({
  debug: process.env.NODE_ENV === 'development',
  providers: [
    Keycloak({
      issuer:
        process.env.AUTH_KEYCLOAK_ISSUER ??
        process.env.NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER,
      clientId:
        process.env.NEXT_PUBLIC_AUTH_KEYCLOAK_ID ??
        process.env.AUTH_KEYCLOAK_ID,
      clientSecret:
        process.env.NEXT_PUBLIC_AUTH_KEYCLOAK_SECRET ??
        process.env.AUTH_KEYCLOAK_SECRET,
      authorization: {
        params: { scope: 'openid profile email', prompt: 'consent' },
      },
      async profile(profile /* userinfo */) {
        // This is just userinfo - often without roles/groups
        console.log('profile', profile);
        return {
          id: profile.sub,
          email: profile.email ?? '',
          name:
            profile.name ??
            `${profile.given_name ?? ''} ${profile.family_name ?? ''}`.trim(),
          firstName: (profile as any).given_name,
          lastName: (profile as any).family_name,
          emailVerified: null, // Not used but required by NextAuth
          // Include username from Keycloak
          username: (profile as any).preferred_username,
          // Default to false, will be updated in JWT callback after DB sync
          termsAndConditionsAccepted: false,
        } as CustomAuthUser;
      },
    }),
  ],
  callbacks: {
    // Redirect logic after sign in
    async redirect({ url, baseUrl }) {
      // Allow relative callback URLs
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      // Allow callback URLs on the same origin
      if (new URL(url).origin === baseUrl) {
        return url;
      }
      // Default redirect to base URL
      return baseUrl;
    },

    async jwt({ token, user, account, trigger, session }) {
      // Store basic fields on first login
      if (user)
        token.user = { ...(token.user ?? {}), ...(user as CustomAuthUser) };

      // Handle session update
      if (trigger === 'update' && session) {
        console.log('JWT update triggered with session:', session);
        // Update the whole user object
        token.user = {
          ...token.user,
          ...session, // session contains new data from useSession().update()
        };
        return token;
      }

      if (account?.access_token) {
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;

        try {
          // Decode access token and extract roles/groups
          const at = jwtDecode<KeycloakAccessToken>(account.access_token);

          // Sync user with database
          const syncResult = await UserSyncService.syncUserFromKeycloak(at);
          console.log('User sync result:', {
            isNewUser: syncResult.isNewUser,
            syncedFields: syncResult.syncedFields,
            userId: syncResult.user.userId,
          });

          // Update token with database user info and Keycloak data
          token.user = {
            ...(token.user ?? {}),
            id: at.sub,
            keycloakId: at.sub,
            email: at.email ?? token.user?.email ?? '',
            firstName: at.given_name ?? token.user?.firstName,
            lastName: at.family_name ?? token.user?.lastName,
            role: syncResult.user.role as UserRole,
            emailVerified: null, // Not used but required by NextAuth
            username: at.preferred_username,
            // Add database user ID for tRPC compatibility
            userId: syncResult.user.userId,
            // Add terms acceptance status for redirect logic
            termsAndConditionsAccepted:
              syncResult.user.termsAndConditionsAccepted,
          };
        } catch (error) {
          console.error('Failed to sync user with database:', error);

          // Fallback to basic token data if sync fails
          const at = jwtDecode<KeycloakAccessToken>(account.access_token);
          const role = extractKeycloakRoles(at);

          if (role === null) {
            throw new Error('Unauthorized user');
          }

          token.user = {
            ...(token.user ?? {}),
            id: at.sub,
            keycloakId: at.sub,
            email: at.email ?? token.user?.email ?? '',
            firstName: at.given_name ?? token.user?.firstName,
            lastName: at.family_name ?? token.user?.lastName,
            role,
            emailVerified: null, // Not used but required by NextAuth
            username: at.preferred_username,
            // Use 0 as fallback userId to indicate sync failure
            userId: 0,
            // Default to false for fallback case
            termsAndConditionsAccepted: false,
          };
        }
      }
      return token;
    },
    async session({ session, token, trigger }) {
      // Handle session update
      if (trigger === 'update') {
        console.log('Session update triggered');
        // Use data from token because session can be undefined
        if (token.user) {
          session.user = {
            ...session.user,
            ...token.user,
          } as CustomAuthUser & DefaultSession['user'];
        }
        return session;
      }

      // Normal flow
      if (token.user) {
        session.user = {
          ...(session.user ?? {}),
          ...token.user,
        } as CustomAuthUser & DefaultSession['user'];
      }
      return session;
    },
  },
  pages: { signIn: '/login' },
});
