import {
  type KeycloakAccessToken,
  type UserRole,
} from '@/modules/auth/types/keycloak-types';

/**
 * Extracts and maps Keycloak roles to application user role
 */
export function extractKeycloakRoles(
  token: KeycloakAccessToken
): UserRole | null {
  const realmRoles = token.realm_access?.roles ?? [];
  const clientRoles = Object.values(token.resource_access ?? {}).flatMap(
    (r: any) => r.roles ?? []
  );
  const allRoles = Array.from(new Set([...realmRoles, ...clientRoles]));

  const rolePrefix = `${process.env.ENV}-app`;

  // Check for agent role first, default to admin
  if (allRoles.includes(`${rolePrefix}-admin`)) {
    return 'admin';
  } else if (allRoles.includes(`${rolePrefix}-agent`)) {
    return 'agent';
  }
  return null;
}

/**
 * Basic token validation
 */
export function validateKeycloakToken(token: KeycloakAccessToken): void {
  if (!token.sub) {
    throw new Error('Invalid token: missing subject (sub) claim');
  }

  if (!token.email) {
    throw new Error('Invalid token: missing email claim');
  }

  // Check token expiration
  if (token.exp && token.exp < Math.floor(Date.now() / 1000)) {
    throw new Error('Token has expired');
  }
}

/**
 * Simple input sanitization
 */
export function sanitizeUserInput(input: string): string {
  if (!input) return '';

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 255); // Limit length
}
