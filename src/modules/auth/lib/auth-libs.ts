'use server';

import { type Route } from 'next';
import { redirect } from 'next/navigation';

import { auth, signIn as rawSignIn } from '@/modules/auth/server/auth';
import { fetchPublicVariables } from '@/utils/publicVariables';

export const keycloakSignIn = async () => {
  await rawSignIn('keycloak', { redirectTo: '/start' });
};

export const authGuard = async (currentPath?: Route) => {
  const session = await auth();

  if (!session) {
    redirect('/login');
  }

  // If user hasn't accepted terms, redirect to welcome (except if already on welcome)
  if (
    session &&
    session.user?.termsAndConditionsAccepted === false &&
    currentPath !== '/welcome'
  ) {
    redirect('/welcome');
  }

  // If user has accepted terms and is on welcome page, redirect to overview
  if (
    session &&
    session.user?.termsAndConditionsAccepted === true &&
    currentPath === '/welcome'
  ) {
    redirect('/start');
  }

  return session;
};

export const generateKeycloakLogoutUrl = async (idToken?: string) => {
  const publicVariables = await fetchPublicVariables();
  const CLIENT_ID = publicVariables.NEXT_PUBLIC_AUTH_KEYCLOAK_ID;
  const AUTH_KEYCLOAK_ISSUER =
    publicVariables.AUTH_KEYCLOAK_ISSUER ??
    publicVariables.NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER;
  const redirectUrl = publicVariables.SITE_URL;

  const urlParams = new URLSearchParams();
  urlParams.append('client_id', CLIENT_ID);
  urlParams.append(
    'post_logout_redirect_uri',
    `${redirectUrl}/api/auth/logout`
  );
  if (idToken) {
    urlParams.append('id_token_hint', idToken);
  }
  return `${AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${urlParams.toString()}`;
};

export const signoutKeycloak = async () => {
  const url = await generateKeycloakLogoutUrl();
  redirect(url);
};
