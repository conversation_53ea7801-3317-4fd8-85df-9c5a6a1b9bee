import { type User } from '@prisma/client';

/**
 * Keycloak Access Token structure
 * Contains user information and roles from Keycloak JWT
 */
export interface KeycloakAccessToken {
  /** Subject - unique user identifier in Keycloak */
  sub: string;
  /** User's email address */
  email?: string;
  /** User's first name */
  given_name?: string;
  /** User's last name */
  family_name?: string;
  /** User's full name */
  name?: string;
  /** Preferred username */
  preferred_username?: string;
  /** Realm-level roles */
  realm_access?: {
    roles?: string[];
  };
  /** Client-specific roles */
  resource_access?: Record<
    string,
    {
      roles?: string[];
    }
  >;
  /** Token expiration time */
  exp?: number;
  /** Token issued at time */
  iat?: number;
}

/**
 * User roles supported in the application
 */
export type UserRole = 'agent' | 'admin';

/**
 * User data for creating new users from Keycloak
 */
export interface CreateUserFromKeycloakData {
  keycloakId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  username: string;
  role: UserRole;
}

/**
 * User data for updating existing users from Keycloak
 */
export interface UpdateUserFromKeycloakData {
  role: UserRole;
  lastLogin: Date;
}

/**
 * User synchronization result
 */
export interface UserSyncResult {
  user: User;
  isNewUser: boolean;
  syncedFields: string[];
}
