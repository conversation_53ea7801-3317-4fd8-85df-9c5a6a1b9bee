import { type User } from '@prisma/client';

import {
  extractKeycloakRoles,
  sanitizeUserInput,
  validateKeycloakToken,
} from '@/modules/auth/lib/keycloak-utils';
import {
  type CreateUserFromKeycloakData,
  type KeycloakAccessToken,
  type UpdateUserFromKeycloakData,
  type UserSyncResult,
} from '@/modules/auth/types/keycloak-types';
import prisma from '@/utils/prisma-db';

/**
 * Service for synchronizing users between Keycloak and local database
 */
export class UserSyncService {
  /**
   * Synchronizes a user from Keycloak token data with the local database
   * Creates new user if doesn't exist, updates existing user otherwise
   */
  static async syncUserFromKeycloak(
    token: KeycloakAccessToken
  ): Promise<UserSyncResult> {
    // Basic validation
    validateKeycloakToken(token);

    const keycloakId = token.sub;
    const email = token.email!; // Safe after validation

    try {
      // Try to find existing user by keycloakId or email
      const existingUser = await this.findExistingUser(keycloakId, email);

      if (existingUser) {
        // Update existing user
        const updateData = this.mapKeycloakToUpdateData(token);
        const updatedUser = await this.updateUser(
          existingUser.userId,
          updateData
        );

        return {
          user: updatedUser,
          isNewUser: false,
          syncedFields: Object.keys(updateData),
        };
      } else {
        // Create new user
        const createData = this.mapKeycloakToCreateData(token);
        const newUser = await this.createUser(createData);

        return {
          user: newUser,
          isNewUser: true,
          syncedFields: Object.keys(createData),
        };
      }
    } catch (error) {
      console.error('User sync failed:', error);
      throw new Error(
        `Failed to sync user with database: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Finds existing user by keycloakId or email (optimized single query)
   */
  private static async findExistingUser(
    keycloakId: string,
    email: string
  ): Promise<User | null> {
    // Single query to find user by keycloakId or email
    let user = await prisma.user.findFirst({
      where: {
        OR: [{ keycloakId }, { email, keycloakId: null }],
      },
    });

    // If found by email but no keycloakId, update the keycloakId
    if (user && !user.keycloakId) {
      user = await prisma.user.update({
        where: { userId: user.userId },
        data: { keycloakId },
      });
    }

    return user;
  }

  /**
   * Creates a new user from Keycloak data
   */
  private static async createUser(
    data: CreateUserFromKeycloakData
  ): Promise<User> {
    return await prisma.user.create({
      data: {
        keycloakId: data.keycloakId,
        username: data.username,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        role: data.role,
        termsAndConditionsAccepted: false, // Will be handled separately
      },
    });
  }

  /**
   * Updates an existing user with Keycloak data
   */
  private static async updateUser(
    userId: number,
    data: UpdateUserFromKeycloakData
  ): Promise<User> {
    return await prisma.user.update({
      where: { userId },
      data,
    });
  }

  /**
   * Maps Keycloak token data to user creation data
   */
  private static mapKeycloakToCreateData(
    token: KeycloakAccessToken
  ): CreateUserFromKeycloakData {
    const role = extractKeycloakRoles(token);
    if (role === null) {
      throw new Error('Unauthorized user');
    }
    // Use preferred_username from Keycloak directly, with fallback
    const username = token.preferred_username || token.email!.split('@')[0];

    return {
      keycloakId: token.sub,
      email: token.email!,
      firstName: token.given_name,
      lastName: token.family_name,
      username: sanitizeUserInput(username),
      role,
    };
  }

  /**
   * Maps Keycloak token data to user update data
   */
  private static mapKeycloakToUpdateData(
    token: KeycloakAccessToken
  ): UpdateUserFromKeycloakData {
    const role = extractKeycloakRoles(token);

    if (role === null) {
      throw new Error('Unauthorized user');
    }

    return {
      role,
      lastLogin: new Date(),
    };
  }
}
