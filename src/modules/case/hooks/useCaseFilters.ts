import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';
import { useMemo } from 'react';
import { useDebounceValue } from 'usehooks-ts';

export const caseFiltersSearchParamsClient = {
  // TODO: implement actual pagination instead of huge limit.
  limit: parseAsInteger.withDefault(200).withOptions({ clearOnDefault: true }),
  offset: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  sortField: parseAsString
    .withDefault('caseId')
    .withOptions({ clearOnDefault: true }),
  sortDirection: parseAsString
    .withDefault('asc')
    .withOptions({ clearOnDefault: true }),
  search: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  status: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  caseType: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  customerId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  assignedUserId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  orderBy: parseAsString
    .withDefault('caseId')
    .withOptions({ clearOnDefault: true }),
};

export const useCaseFilters = () => {
  // URL state - updates immediately for sharing/bookmarking
  const [urlFilters, setUrlFilters] = useQueryStates(
    caseFiltersSearchParamsClient
  );

  // Debounced search value for API calls - reduces server load
  const [debouncedSearch] = useDebounceValue(urlFilters.search, 300);

  // Create filters object for API calls with debounced search
  const apiFilters = useMemo(
    () => ({
      ...urlFilters,
      search: debouncedSearch,
    }),
    [urlFilters, debouncedSearch]
  );

  // Return URL filters for immediate UI feedback and API filters for queries
  return {
    // Immediate filters for UI (input values, URL state)
    filters: urlFilters,
    // Debounced filters for API calls
    apiFilters,
    // Setter function for immediate URL updates
    setFilters: setUrlFilters,
  } as const;
};
