import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';

import { DataInputSteps } from '@/utils/helperTypes';

/**
 * Search params configuration for DataInputFrame component
 * Manages stepId and customerId URL parameters
 */
export const dataInputSearchParamsClient = {
  stepId: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  customerId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
};

/**
 * Custom hook for managing DataInputFrame URL search parameters
 * Provides type-safe access to stepId and customerId parameters
 *
 * @returns Object containing current params and setter function
 */
export const useDataInputParams = () => {
  const [params, setParams] = useQueryStates(dataInputSearchParamsClient);

  // Helper function to get stepId as BasicDataSteps enum or null
  const getStepId = (): DataInputSteps | null => {
    if (!params.stepId) return null;

    // Validate that the stepId is a valid BasicDataSteps value
    const validSteps = Object.values(DataInputSteps) as string[];
    if (validSteps.includes(params.stepId)) {
      return params.stepId as DataInputSteps;
    }

    return null;
  };

  // Helper function to get customerId as number or null
  const getCustomerId = (): number | null => {
    return params.customerId || null;
  };

  // Helper function to set stepId with type safety
  const setStepId = (stepId: DataInputSteps | null) => {
    setParams({ stepId: stepId || '' });
  };

  // Helper function to set customerId
  const setCustomerId = (customerId: number | null) => {
    console.log('setCustomerId', customerId);
    setParams({ customerId: customerId || 0 });
  };

  // Helper function to set both parameters at once
  const setBothParams = (
    stepId: DataInputSteps | null,
    customerId: number | null
  ) => {
    setParams({ stepId: stepId || '', customerId: customerId || 0 });
  };

  return {
    // Raw params for direct access
    params,
    // Type-safe getters
    stepId: getStepId(),
    customerId: getCustomerId(), // Now returns number | null
    // Setters
    setStepId,
    setCustomerId, // Now accepts number | null
    setBothParams, // Now accepts number | null for customerId
    setParams,
  } as const;
};
