'use client';
import { Stack, Typography } from '@mui/material';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { useTRPC } from '@/trpc/client';
import { type CaseNavbarInfo } from '@/utils/helperTypes';

interface Props {
  caseId?: number;
  caseInfo?: CaseNavbarInfo;
  caseNumber?: string;
  noCaseNumber?: boolean;
}

export function CaseNavbarTitle(props: Props) {
  const t = useTranslations();
  const trpc = useTRPC();

  const { data: caseWithRelations } = useSuspenseQuery(
    trpc.cases.getWithAllRelations.queryOptions({
      number: props.caseNumber,
    })
  );

  const caseData = useMemo(() => {
    const info: CaseNavbarInfo = {
      firstName:
        caseWithRelations?.customer?.firstName ||
        props?.caseInfo?.firstName ||
        '',
      lastName:
        caseWithRelations?.customer?.lastName || props.caseInfo?.lastName || '',
      caseNumber:
        caseWithRelations?.caseNumber || props.caseInfo?.caseNumber || '',
      caseType: caseWithRelations?.caseType || props.caseInfo?.caseType || '',
    };

    return info;
  }, [caseWithRelations, props.caseInfo]);

  return (
    <Stack direction="row" gap={1} color="white">
      <Typography
        fontWeight={700}
        fontSize="1.3rem"
      >{`${caseData.firstName} ${caseData?.lastName}`}</Typography>
      {props.noCaseNumber === true ? (
        <Typography fontSize="1.3rem">{`/ ${caseData.caseType}`}</Typography>
      ) : (
        <Typography fontSize="1.3rem">{`/ ${t('words.case')}: ${caseData.caseNumber} / ${caseData.caseType}`}</Typography>
      )}
    </Stack>
  );
}
