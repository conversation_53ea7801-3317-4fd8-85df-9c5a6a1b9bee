'use client';

import AttachFileIcon from '@mui/icons-material/AttachFile';
import { Box, Button, Typography } from '@mui/material';
import Image from 'next/image';
import { useState } from 'react';

import { CommunicationModal } from '@/components/modal/communicationModal';
import { KvModal } from '@/components/modal/modal';
import type { TransformedRiskPreRequest } from '@/modules/case/types/case-types';

import { StatusChip } from './StatusChips';

interface RiskPreRequestRowProps {
  riskPreRequest: TransformedRiskPreRequest;
  caseId: number;
  customerName: string;
  caseType: string;
}

// No need for mapping function anymore - we pass enum values directly

export function RiskPreRequestRow({
  riskPreRequest,
  caseId,
  customerName,
  caseType,
}: RiskPreRequestRowProps) {
  const attachmentCount = riskPreRequest.attachments?.length || 0;
  const hasProcessing = riskPreRequest.status === 'InputRequired';
  const processingCount = hasProcessing ? 1 : undefined;
  const [showModal, setShowModal] = useState(false);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor:
          riskPreRequest.status === 'Ready' || riskPreRequest.status === 'Sent'
            ? '#C6F3E4'
            : '#F4F4F4',
        borderRadius: '8px',
        px: 3,
        py: 2,
        mb: 2,
        minHeight: '60px',
        position: 'relative',
        gap: 3,
      }}
    >
      {/* KV-care badge with white background and teal logo */}
      {riskPreRequest.agentSelect && (
        <Box
          sx={{
            width: 28,
            height: 28,
            borderRadius: '50%',
            backgroundColor: 'white',
            border: '2px solid #e0e0e0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            left: -10,
            top: -8,
            padding: '2px',
            flexShrink: 0,
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          }}
        >
          <Image
            src="/images/logo.svg"
            alt="KV Care Logo"
            width={16}
            height={16}
          />
        </Box>
      )}

      {/* Company Name - Fixed width */}
      <Box sx={{ width: '280px', flexShrink: 0 }}>
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            fontWeight: 600,
            color: '#2faa97',
          }}
        >
          {riskPreRequest.insurerName}
        </Typography>
      </Box>

      {/* Tariff - Fixed width */}
      <Box sx={{ width: '200px', flexShrink: 0 }}>
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '14px',
            color: '#666',
          }}
        >
          Tarif:{' '}
          <span
            style={{
              fontWeight: 600,
              textDecoration: 'underline',
              color: '#212121',
            }}
          >
            {riskPreRequest.tariffName}
          </span>
        </Typography>
      </Box>

      {/* Prices - Fixed width */}
      {riskPreRequest.additionalFee && riskPreRequest.monthlyPremium && (
        <>
          <Box sx={{ width: '150px', flexShrink: 0 }}>
            <Typography
              sx={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '14px',
                color: '#212121',
              }}
            >
              Zuschlag:{' '}
              <span style={{ fontWeight: 700, color: '#2faa97' }}>
                {riskPreRequest.additionalFee} €
              </span>
            </Typography>
          </Box>
          <Box sx={{ width: '150px', flexShrink: 0 }}>
            <Typography
              sx={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '14px',
                color: '#838383',
              }}
            >
              Beitrag:{' '}
              <span style={{ fontWeight: 700, color: '#2faa97' }}>
                {riskPreRequest.monthlyPremium} €
              </span>
            </Typography>
          </Box>
        </>
      )}

      {/* Right side - Buttons and Status */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          marginLeft: 'auto',
        }}
      >
        {/* Status chip */}
        <StatusChip
          status={riskPreRequest.status}
          processingCount={processingCount}
        />

        {/* Attachment button */}

        <Button
          variant="outlined"
          onClick={(e) => {
            e.stopPropagation();
            setShowModal(true);
          }}
          sx={{
            minWidth: '56px',
            width: '56px',
            height: '38px',
            borderColor: '#CECECE',
            backgroundColor: 'white',
            color: '#212121',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 0.5,
            borderRadius: '7px',
            p: 0,
            border: '1px solid #CECECE',
            '&:hover': {
              borderColor: '#CECECE',
              backgroundColor: '#f5f5f5',
            },
          }}
        >
          <AttachFileIcon sx={{ fontSize: '24px', color: '#1C1B1F' }} />
          <Typography
            sx={{
              fontWeight: 700,
              fontSize: '14px',
              fontFamily: 'Inter, sans-serif',
              lineHeight: '22px',
            }}
          >
            {attachmentCount}
          </Typography>
        </Button>
      </Box>

      {/* Individual Police Modal */}
      {showModal && (
        <KvModal onClose={() => setShowModal(false)}>
          <CommunicationModal
            caseId={caseId}
            caseType={caseType}
            customerName={customerName}
            preset="allDocuments"
            insurerName={riskPreRequest.insurerName}
          />
        </KvModal>
      )}
    </Box>
  );
}
