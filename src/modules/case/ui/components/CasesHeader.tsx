'use client';

import AddIcon from '@mui/icons-material/Add';
import GridViewIcon from '@mui/icons-material/GridView';
import { Box, Button, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

interface CasesHeaderProps {
  title: string;
  onOverviewClick?: () => void;
  onNewCaseClick?: () => void;
  showOverviewButton?: boolean;
  showNewCaseButton?: boolean;
}

export function CasesHeader({
  title,
  onOverviewClick,
  onNewCaseClick,
  showOverviewButton = true,
  showNewCaseButton = true,
}: CasesHeaderProps) {
  const t = useTranslations();
  return (
    <Box
      sx={{
        height: '66px',
        backgroundColor: '#2faa97',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        px: 4,
      }}
    >
      {/* Left - Overview Button */}
      {showOverviewButton ? (
        <Button
          variant="contained"
          startIcon={<GridViewIcon />}
          onClick={onOverviewClick}
          sx={{
            backgroundColor: '#C5F3E4',
            color: '#34746A',
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            textTransform: 'none',
            borderRadius: '7px',
            height: '41px',
            '&:hover': { backgroundColor: '#B0EDD8' },
          }}
        >
          {t('cases.header.overview_button')}
        </Button>
      ) : (
        <Box sx={{ width: '132px' }} /> // Placeholder to maintain spacing
      )}

      {/* Center - Title */}
      <Typography
        sx={{
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          color: 'white',
          textAlign: 'center',
          fontWeight: 400,
        }}
      >
        {title}
      </Typography>

      {/* Right - New Case Button */}
      {showNewCaseButton ? (
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onNewCaseClick}
          sx={{
            backgroundColor: '#34746A',
            color: 'white',
            fontFamily: 'Inter, sans-serif',
            fontSize: '16px',
            textTransform: 'none',
            borderRadius: '7px',
            height: '41px',
            '&:hover': { backgroundColor: '#2d5f56' },
          }}
        >
          {t('menu.create_new_case')}
        </Button>
      ) : (
        <Box sx={{ width: '172px' }} /> // Placeholder to maintain spacing
      )}
    </Box>
  );
}
