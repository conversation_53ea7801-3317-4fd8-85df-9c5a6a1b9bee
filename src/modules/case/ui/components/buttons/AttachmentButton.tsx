import AttachFileIcon from '@mui/icons-material/AttachFile';
import { Button, Typography } from '@mui/material';

interface Props {
  count: number;
  onClick?: () => void;
}

export function AttachmentButton(props: Props) {
  return (
    <>
      <Button
        variant="outlined"
        sx={{
          minWidth: '56px',
          width: '56px',
          height: '38px',
          borderColor: '#CECECE',
          backgroundColor: 'white',
          color: '#212121',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 0.5,
          borderRadius: '7px',
          p: 0,
          border: '1px solid #CECECE',
          '&:hover': {
            borderColor: '#CECECE',
            backgroundColor: '#f5f5f5',
          },
        }}
        onClick={(e) => {
          e.stopPropagation();
          props.onClick?.();
        }}
      >
        <AttachFileIcon sx={{ fontSize: '24px', color: '#1C1B1F' }} />
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            lineHeight: '22px',
          }}
        >
          {props.count}
        </Typography>
      </Button>
    </>
  );
}
