import EditNoteIcon from '@mui/icons-material/EditNote';
import { Button, type SxProps, type Theme } from '@mui/material';
import { useTranslations } from 'next-intl';

interface Props {
  caseNumber?: string;
  sx?: SxProps<Theme>;
}

export function DataInputButton(props: Props) {
  const t = useTranslations();
  return (
    <>
      <Button
        variant="contained"
        disableElevation
        component={props.caseNumber ? 'a' : 'button'}
        href={
          props.caseNumber
            ? `/dateneingabe/case/${props.caseNumber}`
            : undefined
        }
        onClick={(e: any) => {
          e.stopPropagation();
        }}
        sx={{
          width: 'auto',
          backgroundColor: 'white',
          color: '#000000',
          fontWeight: 400,
          textTransform: 'none',
          borderRadius: '7px',
          fontSize: '1rem',
          height: '38px',
          '&:hover': { backgroundColor: '#BDBDBD' },
          border: 1,
          borderColor: 'colors.lightGray',
          ...props.sx,
        }}
      >
        <EditNoteIcon />
        {t('components.case_details_view.button_go_to_data_input')}
      </Button>
    </>
  );
}
