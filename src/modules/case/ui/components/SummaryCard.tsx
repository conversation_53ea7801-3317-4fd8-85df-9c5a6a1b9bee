'use client';

import { Box, Typography } from '@mui/material';

interface SummaryCardProps {
  count: number;
  title: string;
  color?: string;
}

export function SummaryCard({
  count,
  title,
  color = '#2faa97',
}: SummaryCardProps) {
  return (
    <Box
      sx={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '25px',
        display: 'flex',
        alignItems: 'center',
        gap: 1.5,
        flex: 1,
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }}
    >
      {/* Large number */}
      <Typography
        sx={{
          fontSize: '36px',
          fontWeight: 700,
          color: color,
          fontFamily: 'Inter, sans-serif',
          lineHeight: 1,
          minWidth: '40px',
        }}
      >
        {count}
      </Typography>

      {/* Title text */}
      <Typography
        sx={{
          fontSize: '14px',
          fontWeight: 400,
          color: color,
          fontFamily: 'Inter, sans-serif',
          lineHeight: '18px',
          flex: 1,
        }}
      >
        {title}
      </Typography>
    </Box>
  );
}
