'use client';

import { Button, Typography } from '@mui/material';
import { CaseStatus, type PotentialInsuranceStatus } from '@prisma/client';
import { useTranslations } from 'next-intl';

interface StatusChipProps {
  status: PotentialInsuranceStatus;
  processingCount?: number;
}

interface CaseStatusChipProps {
  status: CaseStatus;
  processingCount?: number;
  onClick?: (e: any) => void;
}

export function StatusChip({ status, processingCount = 1 }: StatusChipProps) {
  const t = useTranslations('case_status');

  switch (status) {
    case 'Ready':
    case 'Sent':
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#2faa97',
            color: 'white',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            '&:hover': { backgroundColor: '#26967f' },
          }}
        >
          {t('application_ready')}
        </Button>
      );

    case 'Pending':
      return (
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            color: '#B67900',
            textAlign: 'center',
          }}
        >
          {t('risk_inquiry_running')}
        </Typography>
      );

    case 'InputRequired':
    case 'InProgress':
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#FFE979',
            color: '#000000',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,

            '&:hover': { backgroundColor: '#FFE979' },
          }}
        >
          <span style={{ fontWeight: 700 }}>{processingCount}</span>{' '}
          {t('processing')}
        </Button>
      );

    case 'Denied':
      return (
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            color: '#f44336',
            textAlign: 'center',
          }}
        >
          {t('rejected')}
        </Typography>
      );

    default:
      return null;
  }
}

export function CaseStatusChip({
  status,
  processingCount = 1,
  onClick,
}: CaseStatusChipProps) {
  const t = useTranslations('case_status');

  const handleClick = (e: any) => {
    e.stopPropagation();
    onClick?.(e);
  };

  switch (status) {
    case CaseStatus.DataEntry:
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#E0E0E0',
            color: '#000000',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            '&:hover': { backgroundColor: '#BDBDBD' },
          }}
          onClick={handleClick}
        >
          {t('data_entry')}
        </Button>
      );

    case CaseStatus.RiskInquiryRunning:
      return (
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            color: '#B67900',
            textAlign: 'center',
          }}
        >
          {t('risk_inquiry_running')}
        </Typography>
      );

    case CaseStatus.ApplicationReady:
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#2196F3',
            color: 'white',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            '&:hover': { backgroundColor: '#1976D2' },
          }}
          onClick={handleClick}
        >
          {t('application_ready')}
        </Button>
      );

    case CaseStatus.Processing:
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#FFE979',
            color: '#000000',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            '&:hover': { backgroundColor: '#FFE979' },
          }}
          onClick={handleClick}
        >
          <span style={{ fontWeight: 700 }}>{processingCount}</span>{' '}
          {t('processing')}
        </Button>
      );

    case CaseStatus.Applied:
      return (
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#4CAF50',
            color: 'white',
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            textTransform: 'none',
            borderRadius: '7px',
            height: '32px',
            px: 2,
            minWidth: 'auto',
            '&:hover': { backgroundColor: '#388E3C' },
          }}
          onClick={handleClick}
        >
          {t('applied')}
        </Button>
      );

    case CaseStatus.Rejected:
      return (
        <Typography
          sx={{
            fontFamily: 'Inter, sans-serif',
            fontSize: '12px',
            fontWeight: 400,
            color: '#f44336',
            textAlign: 'center',
          }}
        >
          {t('rejected')}
        </Typography>
      );

    default:
      return null;
  }
}
