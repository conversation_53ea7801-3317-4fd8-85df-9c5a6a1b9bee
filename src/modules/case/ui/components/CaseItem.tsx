'use client';

import AttachFileIcon from '@mui/icons-material/AttachFile';
import {
  Box,
  Button,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import {
  CommunicationModal,
  type CommunicationModalPreset,
} from '@/components/modal/communicationModal';
import { KvModal } from '@/components/modal/modal';
import { type GetManyWithGroup_Case } from '@/modules/case/types/case-types';
import { type CaseWithCustomersAndAttachments } from '@/utils/helperTypes';

import { DataInputButton } from './buttons/DataInputButton';
import { CaseStatusChip } from './StatusChips';

interface CaseItemProps {
  caseData: CaseWithCustomersAndAttachments;
  variant?: 'card' | 'list';
  sx?: SxProps<Theme>;
}

export function CaseItem({
  caseData,
  variant: _variant = 'card',
  sx,
}: CaseItemProps) {
  const router = useRouter();
  const [activeModal, setActiveModal] =
    useState<CommunicationModalPreset | null>(null);

  const customerName = `${caseData.customer?.firstName} ${caseData.customer?.lastName}`;

  // Simple row layout similar to InsuranceCompanyRow
  return (
    <>
      <Link href={`/cases/${caseData.caseNumber}`}>
        <Stack
          direction="row"
          sx={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: '#F4F4F4',
            borderRadius: '8px',
            px: 3,
            py: 2,
            minHeight: '60px',
            gap: 3,
            cursor: 'pointer',
            '&:hover': {
              backgroundColor: '#E8E8E8',
            },
            flexWrap: 'wrap',
            ...sx,
            maxWidth: '100%',
          }}
        >
          {/* Customer icon and name */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              minWidth: 200,
            }}
          >
            {/* <PersonIcon sx={{ fontSize: 20, color: '#2faa97' }} /> */}
            <Typography
              sx={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '14px',
                fontWeight: 600,
                color: '#212121',
              }}
            >
              {customerName}
            </Typography>
          </Box>

          {/* Case type */}
          <Typography
            sx={{
              fontFamily: 'Inter, sans-serif',
              fontSize: '12px',
              fontWeight: 400,
              color: '#666',
              minWidth: 120,
            }}
          >
            {caseData.caseType}
          </Typography>

          {/* Case number as link */}
          <Typography
            style={{
              fontFamily: 'Inter, sans-serif',
              fontSize: '12px',
              fontWeight: 600,
              color: '#2faa97',
              textDecoration: 'none',
              minWidth: 100,
            }}
          >
            {caseData.caseNumber}
          </Typography>

          {caseData.status !== 'DataEntry' &&
            GetNumberOfInsurancePlansForCase(caseData as GetManyWithGroup_Case)}

          {/* Status chip and attachment button */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              marginLeft: 'auto',
            }}
          >
            {/* {caseData.status} */}
            {caseData.status === null || caseData.status === 'DataEntry' ? (
              <DataInputButton />
            ) : (
              <CaseStatusChip
                status={caseData.status}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (caseData.status === 'DataEntry') {
                    return router.push(
                      `/dateneingabe/case/${caseData.caseNumber}`
                    );
                  }
                  if (caseData.status === 'Processing') {
                    return setActiveModal('followUpRequests');
                  }
                  if (caseData.status === 'ApplicationReady') {
                    return setActiveModal('contractsToSign');
                  }
                }}
              />
            )}

            {/* Attachment button */}
            <Button
              variant="outlined"
              sx={{
                minWidth: '56px',
                width: '56px',
                height: '38px',
                borderColor: '#CECECE',
                backgroundColor: 'white',
                color: '#212121',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 0.5,
                borderRadius: '7px',
                p: 0,
                border: '1px solid #CECECE',
                '&:hover': {
                  borderColor: '#CECECE',
                  backgroundColor: '#f5f5f5',
                },
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // onAttachmentButtonClick?.(caseData.caseId)
                setActiveModal('allDocuments');
              }}
            >
              <AttachFileIcon sx={{ fontSize: '24px', color: '#1C1B1F' }} />
              <Typography
                sx={{
                  fontWeight: 700,
                  fontSize: '14px',
                  fontFamily: 'Inter, sans-serif',
                  lineHeight: '22px',
                }}
              >
                {caseData.attachments.length}
              </Typography>
            </Button>
          </Box>
        </Stack>
      </Link>

      {activeModal && (
        <KvModal
          onClose={() => {
            setActiveModal(null);
          }}
        >
          <CommunicationModal
            caseId={caseData.caseId}
            caseType={caseData.caseType}
            customerName={customerName}
            preset={activeModal}
          />
        </KvModal>
      )}
    </>
  );
}

export default CaseItem;

const GetNumberOfInsurancePlansForCase = (caseData: GetManyWithGroup_Case) => {
  const t = useTranslations();
  const count =
    caseData.riskPreRequests.length + caseData.potentialInsurances.length;
  return (
    <Stack direction="row" gap={1} fontSize={'1rem'}>
      <Typography fontSize="inherit" fontWeight={700}>
        {count}
      </Typography>
      <Typography fontSize="inherit">{t('words.insurance_plans')}</Typography>
    </Stack>
  );
};
