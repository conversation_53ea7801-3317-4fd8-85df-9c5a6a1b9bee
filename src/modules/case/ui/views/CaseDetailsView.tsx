'use client';

import { Stack, Typography } from '@mui/material';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

import { PotentialInsuranceCreationList } from '@/components/forms/PotentialInsuranceCreationList';
import {
  CommunicationModal,
  type CommunicationModalPreset,
} from '@/components/modal/communicationModal';
import { KvModal } from '@/components/modal/modal';
import { AttachmentButton } from '@/modules/case/ui/components/buttons/AttachmentButton';
import { DataInputButton } from '@/modules/case/ui/components/buttons/DataInputButton';
import { RiskPreRequestRow } from '@/modules/case/ui/components/RiskPreRequestRow';
import { useUserSuspense } from '@/modules/user/hooks/useUser';
import { useTRPC } from '@/trpc/client';
import { type CaseWithAllTheRelations } from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface CaseDetailsViewProps {
  //   caseId?: number;
  caseNumber?: string;
  //   onCaseDataLoaded?: ()
}

export function CaseDetailsView(props: CaseDetailsViewProps) {
  const { user: _user } = useUserSuspense();
  const trpc = useTRPC();
  const t = useTranslations();
  const [activeModal, setActiveModal] =
    useState<CommunicationModalPreset | null>(null);

  // Fetch case data with all relations (customer, assignedUser, insurance data)
  const { data: caseWithRelations } = useSuspenseQuery(
    trpc.cases.getWithAllRelations.queryOptions({
      number: props.caseNumber,
    })
  );

  const dataInputInProgress = caseWithRelations?.status === 'DataEntry';

  const insurancePlanQuestionnaire = useMemo(() => {
    return (
      caseWithRelations?.questionnaires.find(
        (it) => it.formId === 'insurancePlanSelection'
      )?.answersJson || null
    );
  }, [caseWithRelations]);

  const selectedInsurancePlanMix = useMemo(() => {
    const result: string[] = [];
    if (
      insurancePlanQuestionnaire !== null &&
      typeof insurancePlanQuestionnaire === 'object'
    ) {
      const q = insurancePlanQuestionnaire as any;
      if (
        typeof q.insurancePlanMix === 'object' &&
        Array.isArray(q.insurancePlanMix) &&
        q.insurancePlanMix.length > 0
      ) {
        const mixArray = q.insurancePlanMix as string[];
        mixArray.forEach((item) => {
          if (typeof item === 'string' && item.trim().length > 0) {
            result.push(item);
          }
        });
      }
    }
    return [...new Set(result)];
  }, [insurancePlanQuestionnaire]);

  const insurancePlanMixOptions = useMemo<Record<string, string>>(() => {
    const raw = t.raw('insurance_plan_mix');
    if (typeof raw === 'object' && Object.entries(raw).length > 0) {
      return raw;
    }
    return {};
  }, [t]);

  const selectedInsurancePlanMixNamesConcat = useMemo(() => {
    let result = '';
    selectedInsurancePlanMix.forEach((m) => {
      if (result.length > 0) {
        result += ', ';
      }
      if (typeof insurancePlanMixOptions[m] === 'string') {
        result += insurancePlanMixOptions[m];
      } else {
        result += m;
      }
    });
    return result.length > 0 ? result : null;
  }, [selectedInsurancePlanMix, insurancePlanMixOptions]);

  return (
    <>
      <Stack direction="column" gap={3} marginTop={5}>
        {dataInputInProgress ? (
          <>
            <Stack direction="column" display="flex" justifyContent="end">
              <Stack
                direction="row"
                display="flex"
                gap={2}
                paddingRight={3}
                justifyContent="end"
              >
                <Typography marginTop="auto" marginBottom="auto">
                  {t.rich('components.case_details_view.documents_in_case')}
                </Typography>

                <AttachmentButton
                  count={caseWithRelations.attachments.length}
                  onClick={() => setActiveModal('allDocuments')}
                />
              </Stack>
            </Stack>

            <Stack
              direction="column"
              gap={2}
              display="flex"
              justifyContent="center"
              textAlign="center"
              sx={{
                backgroundColor: 'background.paper',
                padding: 3,
                borderRadius: '7px',
              }}
            >
              <Typography variant="h6" fontWeight={700}>
                {t.rich(
                  'components.case_details_view.data_input_in_progress_before_name',
                  intlTranslationRichHelper
                )}
                {`${caseWithRelations.customer.firstName} ${caseWithRelations.customer.lastName}`}
                {t.rich(
                  'components.case_details_view.data_input_in_progress_after_name',
                  intlTranslationRichHelper
                )}
              </Typography>

              <DataInputButton
                caseNumber={caseWithRelations.caseNumber}
                sx={{
                  marginLeft: 'auto',
                  marginRight: 'auto',
                }}
              />
            </Stack>
          </>
        ) : (
          <>
            <Stack direction="row" justifyContent="space-between">
              <Stack direction="column">
                <Typography variant="h6" fontWeight={700} color="var(--teal)">
                  {t(
                    'components.case_details_view.risk_pre_requests_in_progress'
                  )}
                </Typography>
                {/* <Stack direction="row">
          {caseWithRelations.customer.firstName}{' '}
          {caseWithRelations.customer.lastName}
          </Stack> */}
                <Stack direction="row" gap={1}>
                  <Typography>
                    {t.rich(
                      'components.case_details_view.selected_insurance_plan_mix_is',
                      intlTranslationRichHelper
                    )}
                  </Typography>
                  <Typography fontWeight={700}>
                    {selectedInsurancePlanMixNamesConcat !== null
                      ? selectedInsurancePlanMixNamesConcat
                      : t.rich(
                          'components.case_details_view.none_selected',
                          intlTranslationRichHelper
                        )}
                  </Typography>
                </Stack>
              </Stack>
              {/*  */}
              <Stack direction="column">
                <Stack direction="row" display="flex" gap={2} paddingRight={3}>
                  <Typography marginTop="auto" marginBottom="auto">
                    {t.rich('components.case_details_view.documents_in_case')}
                  </Typography>

                  <AttachmentButton
                    count={caseWithRelations.attachments.length}
                    onClick={() => setActiveModal('allDocuments')}
                  />
                </Stack>
              </Stack>
            </Stack>

            {caseWithRelations.riskPreRequests.length <= 0 && (
              <Stack
                direction="row"
                display="flex"
                textAlign="center"
                sx={{
                  backgroundColor: 'background.paper',
                  padding: 3,
                  paddingTop: 5,
                  paddingBottom: 5,
                }}
              >
                <Typography
                  variant="h6"
                  fontWeight={700}
                  marginLeft="auto"
                  marginRight="auto"
                >
                  {t.rich(
                    'components.case_details_view.you_dont_have_risk_pre_requests_in_progress',
                    intlTranslationRichHelper
                  )}
                </Typography>
              </Stack>
            )}

            <Stack direction="column">
              {caseWithRelations.riskPreRequests.map((riskPreRequest) => (
                <RiskPreRequestRow
                  key={riskPreRequest.riskPreRequestId}
                  riskPreRequest={riskPreRequest}
                  caseId={caseWithRelations.caseId}
                  customerName={`${caseWithRelations.customer?.firstName} ${caseWithRelations.customer?.lastName}`}
                  caseType={caseWithRelations.caseType}
                />
              ))}
            </Stack>

            <PotentialInsuranceCreationList
              caseData={caseWithRelations as unknown as CaseWithAllTheRelations}
              required={false}
            ></PotentialInsuranceCreationList>
          </>
        )}
      </Stack>

      {activeModal && (
        <KvModal
          onClose={() => {
            setActiveModal(null);
          }}
        >
          <CommunicationModal
            caseId={caseWithRelations.caseId}
            caseType={caseWithRelations.caseType}
            customerName={`${caseWithRelations.customer.firstName} ${caseWithRelations.customer.lastName}`}
            preset={activeModal}
            // onDocumentUploaded={async () => {
            //   await queryClient.invalidateQueries({
            //     queryKey: trpc.cases.getWithAllRelations.queryKey(),
            //   });
            // }}
          />
        </KvModal>
      )}
    </>
  );
}
