'use client';

import FamilyRestroomIcon from '@mui/icons-material/FamilyRestroom';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import {
  Box,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { CaseStatus } from '@prisma/client';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { useCaseFilters } from '@/modules/case/hooks/useCaseFilters';
import {
  type GetManyWithGroup,
  type GetManyWithGroup_Case,
} from '@/modules/case/types/case-types';
import { CaseItem } from '@/modules/case/ui/components/CaseItem';
import { SummaryCard } from '@/modules/case/ui/components/SummaryCard';
import { useTRPC } from '@/trpc/client';
import {
  CaseGroupRelationType,
  CaseGroupRelationTypeComparator,
} from '@/utils/helperTypes';

export const CasesGroupView = () => {
  const t = useTranslations();

  // Use real data from tRPC
  const { apiFilters } = useCaseFilters();
  const trpc = useTRPC();
  const { data } = useSuspenseQuery(
    trpc.cases.getManyWithGroup.queryOptions({ ...apiFilters })
  );

  const groups = useMemo(() => {
    return data?.items || [];
  }, [data]);

  const noCasesFound = useMemo(() => {
    return groups.some((group) => group.cases.length > 0) === false;
  }, [groups]);

  const statistics = useMemo(() => {
    let appliedCount = 0;
    let inProgressCount = 0;
    groups.forEach((group) => {
      group.cases?.forEach((c) => {
        if (
          c.status === CaseStatus.DataEntry ||
          c.status === CaseStatus.RiskInquiryRunning ||
          c.status === CaseStatus.ApplicationReady ||
          c.status === CaseStatus.Processing
        ) {
          inProgressCount++;
        } else if (c.status === CaseStatus.Applied) {
          appliedCount++;
        }
      });
    });
    return { casesInProgress: inProgressCount, appliedCases: appliedCount };
  }, [groups]);

  const gapWithinGroup = 0.3;
  const gapBetweenGroups = 2;

  const baseCaseStyle: SxProps<Theme> = {
    background: 'var(--gray-background)',
    borderRadius: 0,
  };
  const singleCaseStyle: SxProps<Theme> = {
    ...baseCaseStyle,
    borderTopRightRadius: '8px',
    borderBottomRightRadius: '8px',
  };
  const groupCaseStyle: SxProps<Theme> = {
    ...baseCaseStyle,
    borderRadius: 0,
  };
  const lastInGroupCaseStyle: SxProps<Theme> = {
    ...baseCaseStyle,
    borderBottomRightRadius: '8px',
  };

  const getStyleForCaseItem = (
    index: number,
    count: number
  ): SxProps<Theme> => {
    if (count === 1) {
      return singleCaseStyle;
    }
    if (count > 1 && index === count - 1) {
      return lastInGroupCaseStyle;
    }
    return groupCaseStyle;
  };

  return (
    <Box sx={{ width: '100%', backgroundColor: 'white' }}>
      {/* Summary Cards Section */}
      <Stack
        sx={{
          backgroundColor: '#C5F3E4',
          py: 4,
          px: 4,
        }}
      >
        <Stack
          direction="row"
          display="flex"
          gap={3}
          justifyContent="flex-start"
          alignItems="center"
          flexWrap="wrap"
        >
          <SummaryCard
            count={statistics.casesInProgress}
            title={t('cases.overview.cases_in_progress')}
            color="primary.main"
          />
          <SummaryCard
            count={statistics.appliedCases}
            title={t('cases.overview.applied_cases')}
            color="primary.main"
          />
        </Stack>
      </Stack>

      {noCasesFound && (
        <Stack
          direction="column"
          justifyContent="center"
          textAlign="center"
          gap={3}
          marginTop={5}
        >
          <Typography variant="h6" fontWeight={700}>
            {t(
              'cases.overview.you_have_no_cases_yet_click_new_case_to_create_one'
            )}
          </Typography>
        </Stack>
      )}

      {/* Cases List Section */}
      <Stack
        sx={{
          px: 4,
          py: 3,
          paddingTop: 10,
          maxWidth: '1300px',
          display: 'flex',
          justifyContent: 'center',
          marginLeft: 'auto',
          marginRight: 'auto',
        }}
      >
        <Stack
          spacing={0}
          direction="column"
          gap={gapBetweenGroups}
          maxWidth="100%"
        >
          {groups.map((group, groupIndex) => (
            <Stack
              direction="column"
              key={groupIndex}
              gap={gapWithinGroup}
              maxWidth="100%"
            >
              {group.cases.length > 1 && (
                <Stack direction="row" gap={gapWithinGroup} maxWidth="100%">
                  <Stack
                    width={40}
                    minWidth={40}
                    sx={{
                      background: 'var(--gray-background)',
                      borderTopLeftRadius: '8px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignContent: 'center',
                      textAlign: 'center',
                    }}
                  >
                    <FamilyRestroomIcon
                      sx={{ margin: 'auto', color: 'primary.main' }}
                    />
                  </Stack>
                  <Stack
                    height={60}
                    sx={{
                      background: 'var(--gray-background)',
                      borderTopRightRadius: '8px',
                      alignContent: 'center',
                    }}
                    display="flex"
                    flexGrow={1}
                  >
                    <Typography
                      fontWeight={700}
                      color="primary.main"
                      marginLeft={2}
                      marginTop="auto"
                      marginBottom="auto"
                    >
                      {/* {group.caseGroupNumber} */}
                      {MakeGroupName(group) || group.caseGroupNumber}
                    </Typography>
                  </Stack>
                </Stack>
              )}

              <Stack
                direction="row"
                display="flex"
                // flexGrow={1}
                gap={gapWithinGroup}
                // maxWidth={
                //   group.cases.length > 1
                //     ? `calc(100% - 40px - ${gapWithinGroup * 8}px)`
                //     : '100%'
                // }
                // maxWidth="calc(100% - 40px)"
                maxWidth="100%"
              >
                <Stack
                  width={40}
                  minWidth={40}
                  sx={{
                    background: 'var(--gray-background)',
                    borderBottomLeftRadius: '8px',
                    borderTopLeftRadius: group.cases.length > 1 ? null : '8px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignContent: 'center',
                    textAlign: 'center',
                  }}
                >
                  {group.cases.length === 1 &&
                    (group.cases[0].customer.gender === 'female' ? (
                      <WomanIcon
                        sx={{ margin: 'auto', color: 'primary.main' }}
                      />
                    ) : (
                      <ManIcon sx={{ margin: 'auto', color: 'primary.main' }} />
                    ))}
                </Stack>
                <Stack
                  direction="column"
                  display="flex"
                  flexGrow={1}
                  flexShrink={0}
                  gap={gapWithinGroup}
                  maxWidth={`calc(100% - 40px - ${gapWithinGroup * 8}px)`}
                >
                  {group.cases
                    .sort(CaseGroupRelationTypeComparator)
                    .map((caseItem, caseItemIndex) => (
                      <CaseItem
                        key={caseItem.caseId}
                        sx={getStyleForCaseItem(
                          caseItemIndex,
                          group.cases.length
                        )}
                        caseData={caseItem}
                        variant="list"
                      />
                    ))}
                </Stack>
              </Stack>
            </Stack>
          ))}
        </Stack>
      </Stack>
    </Box>
  );
};

const MakeGroupName = (group: GetManyWithGroup[0]): string | null => {
  let mainCase: GetManyWithGroup_Case | null = null;
  let membership =
    group.memberships.find(
      (it) =>
        it.relationType === CaseGroupRelationType.paying_and_insured_primary
    ) || null;
  if (membership === null) {
    membership =
      group.memberships.find(
        (it) => it.relationType === CaseGroupRelationType.paying
      ) || null;
  }
  if (membership === null) {
    membership =
      group.memberships.find(
        (it) => it.relationType === CaseGroupRelationType.spouse
      ) || null;
  }
  if (membership === null) {
    membership =
      group.memberships.find(
        (it) => it.relationType === CaseGroupRelationType.child
      ) || null;
  }
  if (membership !== null) {
    mainCase =
      group.cases.find((it) => it.caseId === membership.caseId) || null;
  }

  if (mainCase === null) {
    mainCase = group.cases[0] || null;
  }

  if (mainCase !== null) {
    return `Gruppe ${mainCase?.customer?.firstName} ${mainCase?.customer?.lastName} +${group.cases.length - 1} Personen`;
  }

  return null;
};
