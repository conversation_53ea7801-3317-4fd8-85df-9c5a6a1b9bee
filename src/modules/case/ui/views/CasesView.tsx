'use client';

import { Box, Stack } from '@mui/material';
import { CaseStatus } from '@prisma/client';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { useCaseFilters } from '@/modules/case/hooks/useCaseFilters';
import { CaseItem } from '@/modules/case/ui/components/CaseItem';
import { CasesHeader } from '@/modules/case/ui/components/CasesHeader';
import { SummaryCard } from '@/modules/case/ui/components/SummaryCard';
import { useTRPC } from '@/trpc/client';
import { type CaseWithCustomersAndAttachments } from '@/utils/helperTypes';

export const CasesView = () => {
  const router = useRouter();
  const t = useTranslations();

  // Use real data from tRPC
  const { apiFilters } = useCaseFilters();
  const trpc = useTRPC();
  const { data } = useSuspenseQuery(
    trpc.cases.getMany.queryOptions({ ...apiFilters })
  );

  // Extract cases from the response
  const cases = data?.items || [];

  const handleOverviewClick = () => {
    // Navigate to overview/dashboard
    router.push('/start');
  };

  const handleNewCaseClick = () => {
    console.log('Create new case');
    // TODO: Navigate to case creation
  };

  // Calculate statistics from real cases data
  const casesInProgress = cases.filter(
    (c) =>
      c.status === CaseStatus.DataEntry ||
      c.status === CaseStatus.RiskInquiryRunning ||
      c.status === CaseStatus.ApplicationReady ||
      c.status === CaseStatus.Processing
  ).length;

  const appliedCases = cases.filter(
    (c) => c.status === CaseStatus.Applied
  ).length;

  return (
    <Box sx={{ width: '100%', minHeight: '100vh', backgroundColor: 'white' }}>
      {/* Green Banner */}
      <CasesHeader
        title={t('cases.overview.title')}
        onOverviewClick={handleOverviewClick}
        onNewCaseClick={handleNewCaseClick}
      />

      {/* Summary Cards Section */}
      <Box
        sx={{
          backgroundColor: '#C5F3E4',
          py: 4,
          px: 4,
        }}
      >
        <Stack
          direction="row"
          spacing={3}
          justifyContent="flex-start"
          alignItems="center"
        >
          <SummaryCard
            count={casesInProgress}
            title={t('cases.overview.cases_in_progress')}
            color="#2faa97"
          />
          <SummaryCard
            count={appliedCases}
            title={t('cases.overview.applied_cases')}
            color="#2faa97"
          />
        </Stack>
      </Box>

      {/* Cases List Section */}
      <Box sx={{ px: 4, py: 3, paddingTop: 10 }}>
        <Stack spacing={0}>
          {cases.map((caseItem) => (
            <CaseItem
              key={caseItem.caseId}
              caseData={caseItem as unknown as CaseWithCustomersAndAttachments}
              variant="list"
            />
          ))}
        </Stack>
      </Box>
    </Box>
  );
};
