import type { AppRouterOutputs } from '@/trpc/routers/_app';

export type CaseGetOne = AppRouterOutputs['cases']['getOne'];
export type CasesGetMany = AppRouterOutputs['cases']['getMany']['items'];
export type GetManyWithGroup =
  AppRouterOutputs['cases']['getManyWithGroup']['items'];

export type GetManyWithGroup_Case =
  AppRouterOutputs['cases']['getManyWithGroup']['items'][number]['cases'][number];

export type GetManyWithGroup_Case_Customer =
  AppRouterOutputs['cases']['getManyWithGroup']['items'][number]['cases'][number]['customer'];

export type CaseWithAllRelations =
  AppRouterOutputs['cases']['getWithAllRelations'];

// Extract transformed types for components
export type TransformedRiskPreRequest =
  CaseWithAllRelations['riskPreRequests'][0];
export type TransformedApplication = CaseWithAllRelations['applications'][0];
export type TransformedPotentialInsurance =
  CaseWithAllRelations['potentialInsurances'][0];
