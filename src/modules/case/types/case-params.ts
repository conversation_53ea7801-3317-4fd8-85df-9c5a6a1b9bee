import 'server-only'; // <-- ensure this file cannot be imported from the client

import {
  createLoader,
  createStandardSchemaV1,
  parseAsInteger,
  parseAsString,
} from 'nuqs/server';

export const caseFiltersSearchParamsServer = {
  limit: parseAsInteger.withDefault(200).withOptions({ clearOnDefault: true }),
  offset: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  sortField: parseAsString
    .withDefault('caseId')
    .withOptions({ clearOnDefault: true }),
  sortDirection: parseAsString
    .withDefault('asc')
    .withOptions({ clearOnDefault: true }),
  search: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  status: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  caseType: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  customerId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  assignedUserId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  orderBy: parseAsString
    .withDefault('caseId')
    .withOptions({ clearOnDefault: true }),
};

// Create loader for server-side parsing
export const loadCaseSearchParams = createLoader(caseFiltersSearchParamsServer);

// Create Standard Schema V1 compatible validator for tRPC
export const caseSearchParamsSchema = createStandardSchemaV1(
  caseFiltersSearchParamsServer
);

// Type exports
export type CaseFiltersSearchParams = typeof caseFiltersSearchParamsServer;
