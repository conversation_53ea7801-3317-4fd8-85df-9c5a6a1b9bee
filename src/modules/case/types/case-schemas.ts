import { CaseStatus } from '@prisma/client';
import { z } from 'zod';

// Base schema for Case ID validation
export const getOneInputSchema = z.object({
  caseId: z.number().int().positive(),
});

// Schema for creating a new case
export const casesCreateSchema = z.object({
  customerId: z.number().int().positive(),
  assignedUserId: z.number().int().positive().optional(),
  caseType: z.string().min(1),
  status: z.nativeEnum(CaseStatus).default(CaseStatus.DataEntry),
  caseNumber: z.string().optional(), // Usually auto-generated
});

// Schema for updating an existing case
export const updateCaseSchema = z.object({
  caseId: z.number().int().positive(),
  customerId: z.number().int().positive().optional(),
  assignedUserId: z.number().int().positive().optional().nullable(),
  caseType: z.string().min(1).optional(),
  status: z.nativeEnum(CaseStatus).optional(),
});

// Export types for TypeScript usage
export type GetOneInput = z.infer<typeof getOneInputSchema>;

export type CasesCreateInput = z.infer<typeof casesCreateSchema>;
export type UpdateCaseInput = z.infer<typeof updateCaseSchema>;
