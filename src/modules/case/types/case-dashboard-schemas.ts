import { CaseStatus } from '@prisma/client';
import { z } from 'zod';

// Schema for dashboard statistics filtering
export const dashboardStatisticsInputSchema = z
  .object({
    dateRange: z
      .object({
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      })
      .optional(),
    assignedUserId: z.number().int().positive().optional(),
    caseType: z.string().optional(),
    status: z.nativeEnum(CaseStatus).optional(),
  })
  .optional();

// Schema for dashboard cases filtering
export const dashboardCasesInputSchema = z.object({
  limit: z.number().int().positive().max(100).default(20),
  offset: z.number().int().nonnegative().default(0),
  statusFilter: z
    .enum(['all', 'action-required', 'open', 'completed'])
    .default('all'),
  status: z.nativeEnum(CaseStatus).optional(), // Direct status filter
  assignedUserId: z.number().int().positive().optional(),
  caseType: z.string().optional(),
});

// Type exports
export type DashboardStatisticsInput = z.infer<
  typeof dashboardStatisticsInputSchema
>;
export type DashboardCasesInput = z.infer<typeof dashboardCasesInputSchema>;
