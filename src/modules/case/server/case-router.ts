import { CaseStatus, type Prisma } from '@prisma/client';
import { TRPCError } from '@trpc/server';

import { auditCaseChange, auditCustomerChange } from '@/lib/audit-helpers';
import { AuditOperation, mongoDBPrisma } from '@/lib/mongo-db-client';
import { caseHistoryQuerySchema } from '@/modules/case/types/case-audit-schemas';
import {
  dashboardCasesInputSchema,
  dashboardStatisticsInputSchema,
} from '@/modules/case/types/case-dashboard-schemas';
import { caseSearchParamsSchema } from '@/modules/case/types/case-params';
import { updateCaseSchema } from '@/modules/case/types/case-schemas';
import { addCustomerToCaseGroupSchema } from '@/modules/case-customer/types/case-customer-schemas';
import {
  calculateCaseStatus,
  calculateDashboardStatistics,
} from '@/modules/dashboard/lib/status-helpers';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import {
  getCaseGroupByGroupIdOrNumberOrCaseIdOrNumber,
  getOneByIdOrNumber,
  getOneInputSchema,
  updateCaseDataInputProgressSchema,
} from '@/utils/baseSchema';
import prisma from '@/utils/prisma-db';

export const casesRouter = createTRPCRouter({
  getOne: protectedProcedure
    .input(getOneByIdOrNumber)
    .query(async ({ input }) => {
      const existingCase = await prisma.case.findFirst({
        where:
          typeof input.number === 'string' && input.number.trim().length > 0
            ? { caseNumber: input.number }
            : { caseId: input.id },
      });

      if (!existingCase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      return existingCase;
    }),

  completeDataInputForCaseInGroup: protectedProcedure
    .input(getOneByIdOrNumber)
    .mutation(async ({ input, ctx }) => {
      const isAdmin = ctx.auth.user.role === 'admin';
      const isAgent = !isAdmin;
      const userId = ctx.auth.user.userId;

      const caseWhere: Prisma.CaseWhereInput = {
        status: 'DataEntry',
      };

      if (typeof input.id === 'number') {
        caseWhere.caseId = input.id;
      } else if (typeof input.number === 'string') {
        caseWhere.caseNumber = input.number;
      }

      if (isAgent) {
        caseWhere.assignedUserId = userId;
      }

      const ccase = await prisma.case.findFirst({
        where: caseWhere,
      });

      if (!ccase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Case group not found',
        });
      }

      const updatedCase = prisma.case.update({
        where: { caseId: ccase.caseId },
        data: { status: 'RiskInquiryRunning' },
      });

      return updatedCase;
    }),

  //   TODO: changing status for all cases in group may not be required?
  completeDataInputForCaseGroup: protectedProcedure
    .input(getCaseGroupByGroupIdOrNumberOrCaseIdOrNumber)
    .mutation(async ({ input, ctx }) => {
      let caseGroupWhere: Prisma.CaseGroupWhereInput | null = null;
      let caseWhere: Prisma.CaseWhereInput | null = null;

      if (typeof input.caseGroupId === 'number') {
        caseGroupWhere = { caseGroupId: input.caseGroupId };
      } else if (typeof input.caseGroupNumber === 'string') {
        caseGroupWhere = { caseGroupNumber: input.caseGroupNumber };
      } else if (typeof input.caseId === 'number') {
        caseWhere = { caseId: input.caseId };
      } else if (typeof input.caseNumber === 'string') {
        caseWhere = { caseNumber: input.caseNumber };
      }

      // Apply role-based filtering for agents
      if (ctx.auth.isAgent) {
        caseWhere = {
          ...caseWhere,
          assignedUserId: ctx.auth.user.userId,
        };
      }

      let caseGroupId: number | undefined;

      if (caseGroupWhere === null && caseWhere !== null) {
        const ccase = await prisma.case.findFirst({
          where: caseWhere,
          include: {
            CaseGroup: true,
          },
        });

        caseGroupId = ccase?.CaseGroup?.caseGroupId;

        if (!ccase || typeof caseGroupId !== 'number') {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Case group not found',
          });
        }
      }

      caseGroupWhere = {
        ...caseGroupWhere,
        caseGroupId: caseGroupId,
      };

      const caseGroupWithRelations = await prisma.caseGroup.findFirst({
        where: caseGroupWhere,
        include: {
          memberships: {
            include: {
              case: {
                include: {
                  customer: true,
                  questionnaires: true,
                  riskPreRequests: true,
                  attachments: true,
                  caseGroupMembership: true,
                  applications: true,
                  messages: true,
                  potentialInsurances: true,
                },
              },
            },
          },
        },
      });

      if (!caseGroupWithRelations) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const cases = caseGroupWithRelations.memberships.map((m) => m.case);

      if (cases.length <= 0) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const casesInDataEntry = cases.filter((it) => it.status === 'DataEntry');

      const updateStatusResult = await Promise.all(
        casesInDataEntry.map((ccase) =>
          prisma.case.update({
            where: { caseId: ccase.caseId },
            data: { status: 'RiskInquiryRunning' },
          })
        )
      );

      return updateStatusResult;
    }),

  // Get case with all customer relations and insurance data
  getCaseGroupWithAllRelations: protectedProcedure
    .input(getCaseGroupByGroupIdOrNumberOrCaseIdOrNumber)
    .query(async ({ input, ctx }) => {
      let caseGroupWhere: Prisma.CaseGroupWhereInput | null = null;
      let caseWhere: Prisma.CaseWhereInput | null = null;

      if (typeof input.caseGroupId === 'number') {
        caseGroupWhere = { caseGroupId: input.caseGroupId };
      } else if (typeof input.caseGroupNumber === 'string') {
        caseGroupWhere = { caseGroupNumber: input.caseGroupNumber };
      } else if (typeof input.caseId === 'number') {
        caseWhere = { caseId: input.caseId };
      } else if (typeof input.caseNumber === 'string') {
        caseWhere = { caseNumber: input.caseNumber };
      }

      // Apply role-based filtering for agents
      if (ctx.auth.isAgent) {
        caseWhere = {
          ...caseWhere,
          assignedUserId: ctx.auth.user.userId,
        };
      }

      let caseGroupId: number | undefined;

      if (caseGroupWhere === null && caseWhere !== null) {
        const ccase = await prisma.case.findFirst({
          where: caseWhere,
          include: {
            CaseGroup: true,
          },
        });

        caseGroupId = ccase?.CaseGroup?.caseGroupId;

        if (!ccase || typeof caseGroupId !== 'number') {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Case group not found',
          });
        }
      }

      caseGroupWhere = {
        ...caseGroupWhere,
        caseGroupId: caseGroupId,
      };

      const caseGroupWithRelations = await prisma.caseGroup.findFirst({
        where: caseGroupWhere,
        include: {
          memberships: {
            include: {
              case: {
                include: {
                  customer: true,
                  questionnaires: true,
                  riskPreRequests: true,
                  attachments: true,
                  caseGroupMembership: true,
                  applications: true,
                  messages: true,
                  potentialInsurances: true,
                },
              },
            },
          },
        },
      });

      if (!caseGroupWithRelations) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const customers = caseGroupWithRelations.memberships.map(
        (m) => m.case.customer
      );

      const cases = caseGroupWithRelations.memberships.map((m) => m.case);

      if (customers.length <= 0 || cases.length <= 0) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const transformedObject = {
        caseGroupNumber: caseGroupWithRelations.caseGroupNumber,
        caseGroupId: caseGroupWithRelations.caseGroupId,
        cases: caseGroupWithRelations.memberships.map((m) => m.case),
        customers: caseGroupWithRelations.memberships.map(
          (m) => m.case.customer
        ),
      };

      return transformedObject;
    }),

  getWithAllRelations: protectedProcedure
    .input(getOneByIdOrNumber)
    .query(async ({ input }) => {
      const caseWithRelations = await prisma.case.findFirst({
        where:
          typeof input.number === 'string' && input.number.trim().length > 0
            ? { caseNumber: input.number }
            : { caseId: input.id },
        include: {
          customer: true, // Primary customer for backward compatibility
          assignedUser: true,
          potentialInsurances: true,
          applications: true,
          riskPreRequests: {
            include: {
              attachments: {
                include: {
                  uploadedByUser: {
                    select: {
                      userId: true,
                      firstName: true,
                      lastName: true,
                      email: true,
                    },
                  },
                },
              },
            },
          },
          questionnaires: true,
          attachments: {
            include: {
              uploadedByUser: {
                select: {
                  userId: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!caseWithRelations) {
        // console.log('getWithAllRelations not found for ', input);
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      // Transform Decimal fields to numbers for client compatibility
      const transformedCase = {
        ...caseWithRelations,
        potentialInsurances: caseWithRelations.potentialInsurances.map(
          (insurance) => ({
            ...insurance,
            monthlyPremium: insurance.monthlyPremium?.toNumber() ?? null,
          })
        ),
        applications: caseWithRelations.applications.map((app) => ({
          ...app,
          premiumAmount: app.premiumAmount?.toNumber() ?? null,
        })),
        riskPreRequests: caseWithRelations.riskPreRequests.map((request) => ({
          ...request,
          monthlyPremium: request.monthlyPremium?.toNumber() ?? null,
          additionalFee: request.additionalFee?.toNumber() ?? null,
        })),
      };

      return transformedCase;
    }),

  getManyWithGroup: protectedProcedure
    .input(caseSearchParamsSchema)
    .query(async ({ ctx, input }) => {
      const {
        limit,
        offset,
        sortField,
        sortDirection,
        search,
        status,
        caseType,
        customerId,
        assignedUserId,
      } = input;

      // Build where clause for filtering with role-based access
      const caseWhereClause: Prisma.CaseWhereInput = {};

      // Apply role-based filtering for agents
      if (ctx.auth.isAgent) {
        caseWhereClause.assignedUserId = ctx.auth.user.userId;
      }

      if (search) {
        caseWhereClause.OR = [
          { caseNumber: { contains: search, mode: 'insensitive' } },
          { caseType: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status) {
        // Cast string status to CaseStatus enum if it's a valid enum value
        const validStatuses = Object.values(CaseStatus);
        if (validStatuses.includes(status as CaseStatus)) {
          caseWhereClause.status = status as CaseStatus;
        }
      }

      if (caseType) {
        caseWhereClause.caseType = caseType;
      }

      if (customerId) {
        caseWhereClause.customerId = customerId;
      }

      if (assignedUserId) {
        caseWhereClause.assignedUserId = assignedUserId;
      }

      // Build CaseGroup where clause with role-based filtering
      const caseGroupWhereClause: Prisma.CaseGroupWhereInput = {};
      if (ctx.auth.isAgent) {
        caseGroupWhereClause.assignedUserId = ctx.auth.user.userId;
      }

      const [items, total] = await prisma.$transaction([
        prisma.caseGroup.findMany({
          where: caseGroupWhereClause,
          include: {
            memberships: {
              include: { case: true },
            },
            cases: {
              include: {
                customer: true,
                attachments: true,
                questionnaires: true,
                riskPreRequests: true,
                potentialInsurances: true,
                caseGroupMembership: true,
              },
            },
          },
        }),
        prisma.caseGroup.count({
          where: {
            cases: {
              some: caseWhereClause,
            },
          },
        }),
      ]);

      const meta = {
        total,
        pageCount: Math.ceil(total / limit),
        pageSize: limit,
        page: Math.floor(offset / limit) + 1,
      };

      return { items, meta };
    }),

  getMany: protectedProcedure
    .input(caseSearchParamsSchema)
    .query(async ({ ctx, input }) => {
      const {
        limit,
        offset,
        sortField,
        sortDirection,
        search,
        status,
        caseType,
        customerId,
        assignedUserId,
      } = input;

      console.log('case getmany input', input);

      // Build where clause for filtering with role-based access
      const whereClause: Prisma.CaseWhereInput = {};

      // Apply role-based filtering for agents
      if (ctx.auth.isAgent) {
        whereClause.assignedUserId = ctx.auth.user.userId;
      }

      if (search) {
        whereClause.OR = [
          { caseNumber: { contains: search, mode: 'insensitive' } },
          { caseType: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (status) {
        // Cast string status to CaseStatus enum if it's a valid enum value
        const validStatuses = Object.values(CaseStatus);
        if (validStatuses.includes(status as CaseStatus)) {
          whereClause.status = status as CaseStatus;
        }
      }

      if (caseType) {
        whereClause.caseType = caseType;
      }

      if (customerId) {
        whereClause.customerId = customerId;
      }

      if (assignedUserId) {
        whereClause.assignedUserId = assignedUserId;
      }

      const orderBy = { [sortField]: sortDirection };

      const [items, total] = await prisma.$transaction([
        prisma.case.findMany({
          where: whereClause,
          orderBy,
          skip: offset,
          take: limit,
          include: {
            customer: true,
            assignedUser: true,
            CaseGroup: true,
            caseGroupMembership: true,
          },
        }),
        prisma.case.count({ where: whereClause }),
      ]);

      const meta = {
        total,
        pageCount: Math.ceil(total / limit),
        pageSize: limit,
        page: Math.floor(offset / limit) + 1,
      };

      return { items, meta };
    }),

  updateCaseDataInputProgress: protectedProcedure
    .input(updateCaseDataInputProgressSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.auth.user.userId;

      const updatedCase = await prisma.case.update({
        where: { caseId: input.caseId },
        data: { dataInputProgress: input.data || undefined },
      });

      return updatedCase;
    }),

  // Add customer to existing case
  addCustomerToCaseGroup: protectedProcedure
    .input(addCustomerToCaseGroupSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.auth.user.userId;

      try {
        // Check if case exists
        const existingCase = await prisma.case.findFirst({
          where: { caseId: input.caseGroupId },
        });

        console.log('existingCase', existingCase);

        if (!existingCase) {
          throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
        }

        const result = await prisma.$transaction(async (tx) => {
          // Create customer
          const customer = await tx.customer.create({
            data: {
              firstName: input.customerData.firstName,
              lastName: input.customerData.lastName,
              dateOfBirth: input.customerData.dateOfBirth,
              gender: input.customerData.gender,
              email: input.customerData.email,
              phoneNumber: input.customerData.phoneNumber,
              street: input.customerData.street,
              houseNumber: input.customerData.houseNumber,
              postalCode: input.customerData.postalCode,
              city: input.customerData.city,
              country: input.customerData.country,
            },
          });

          // Create questionnaires if provided
          const questionnaires = [];
          if (input.customerData.questionnaires) {
            for (const questionnaireData of input.customerData.questionnaires) {
              const questionnaire = await tx.questionnaire.create({
                data: {
                  formId: questionnaireData.formId,
                  type: questionnaireData.type,
                  status: questionnaireData.status || 'Pending',
                  answersJson: questionnaireData.answersJson as any,
                },
              });
              questionnaires.push(questionnaire);
            }
          }

          // Create audit logs
          await auditCustomerChange({
            customerId: customer.customerId,
            userId: userId!,
            operation: AuditOperation.CREATE,
            dataBefore: null,
            dataAfter: customer,
            action: 'CustomerAddedToCase',
          });

          return { customer, questionnaires };
        });

        return result;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to add customer to case',
          cause: error,
        });
      }
    }),

  update: protectedProcedure
    .input(updateCaseSchema)
    .mutation(async ({ ctx, input }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      const { caseId, ...updateData } = input;

      // Check if case exists and capture before state
      const existingCase = await prisma.case.findFirst({ where: { caseId } });
      if (!existingCase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const updatedCase = await prisma.case.update({
        where: { caseId },
        data: updateData,
      });

      // Create audit log for case update
      await auditCaseChange({
        caseId,
        userId: userId!,
        operation: AuditOperation.UPDATE,
        dataBefore: existingCase,
        dataAfter: updatedCase,
        action: 'CaseUpdated',
      });

      return updatedCase;
    }),

  // Basic implementation - no complex WIP customer processing yet
  // Other developers can implement this later

  remove: protectedProcedure
    .input(getOneInputSchema)
    .mutation(async ({ ctx, input }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId;

      // Check if case exists and capture before state
      const existingCase = await prisma.case.findFirst({
        where: { caseId: input.caseId },
      });
      if (!existingCase) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Case not found' });
      }

      const deletedCase = await prisma.case.delete({
        where: { caseId: input.caseId },
      });

      // Create audit log for case deletion
      await auditCaseChange({
        caseId: input.caseId,
        userId: userId!,
        operation: AuditOperation.DELETE,
        dataBefore: existingCase,
        dataAfter: null,
        action: 'CaseDeleted',
      });

      return deletedCase;
    }),

  getHistory: protectedProcedure
    .input(caseHistoryQuerySchema)
    .query(async ({ input, ctx }) => {
      const _userId = ctx.auth.user.userId;

      // Build where clause for filtering
      const whereClause: any = { caseId: input.caseId };

      if (input.operation) {
        whereClause.operation = input.operation;
      }

      if (input.userId) {
        whereClause.userId = input.userId;
      }

      if (input.startDate || input.endDate) {
        whereClause.timestamp = {};
        if (input.startDate) {
          whereClause.timestamp.gte = input.startDate;
        }
        if (input.endDate) {
          whereClause.timestamp.lte = input.endDate;
        }
      }

      // Get audit history with pagination from MongoDB
      const [history, totalCount] = await Promise.all([
        mongoDBPrisma.caseHistory.findMany({
          where: whereClause,
          orderBy: { timestamp: 'desc' },
          take: input.limit,
          skip: input.offset,
        }),
        mongoDBPrisma.caseHistory.count({ where: whereClause }),
      ]);

      return {
        history,
        totalCount,
        hasMore: input.offset + input.limit < totalCount,
      };
    }),

  // Dashboard-specific endpoints
  getDashboardStatistics: protectedProcedure
    .input(dashboardStatisticsInputSchema)
    .query(async ({ input, ctx }) => {
      const filters = input || {};

      // Build where clause for filtering
      const whereClause: Prisma.CaseWhereInput = {};

      if (filters.dateRange?.startDate) {
        whereClause.createdAt = { gte: filters.dateRange.startDate };
      }
      if (filters.dateRange?.endDate) {
        whereClause.createdAt = {
          ...((whereClause.createdAt as object) || {}),
          lte: filters.dateRange.endDate,
        };
      }
      if (filters.assignedUserId) {
        whereClause.assignedUserId = filters.assignedUserId;
      }
      if (filters.caseType) {
        whereClause.caseType = filters.caseType;
      }
      if (filters.status) {
        whereClause.status = filters.status;
      }

      // Fetch cases with all related data
      const cases = await prisma.case.findMany({
        where: whereClause,
        include: {
          potentialInsurances: true,
          applications: true,
          riskPreRequests: true,
          questionnaires: true,
          customer: true,
        },
      });

      // Calculate statistics
      const statistics = calculateDashboardStatistics(cases as any);

      return statistics;
    }),

  getCasesForDashboard: protectedProcedure
    .input(dashboardCasesInputSchema)
    .query(async ({ input, ctx }) => {
      const { limit, offset, statusFilter, status, assignedUserId, caseType } =
        input;

      // Build where clause for filtering
      const whereClause: Prisma.CaseWhereInput = {};

      if (assignedUserId) {
        whereClause.assignedUserId = assignedUserId;
      }
      if (caseType) {
        whereClause.caseType = caseType;
      }
      if (status) {
        whereClause.status = status;
      }

      // Fetch cases with all related data
      const [cases, total] = await prisma.$transaction([
        prisma.case.findMany({
          where: whereClause,
          include: {
            customer: {
              select: {
                customerId: true,
                firstName: true,
                lastName: true,
              },
            },
            assignedUser: {
              select: {
                userId: true,
                firstName: true,
                lastName: true,
              },
            },
            potentialInsurances: true,
            applications: true,
            riskPreRequests: true,
            questionnaires: true,
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.case.count({ where: whereClause }),
      ]);

      // Enhance cases with status information
      const enhancedCases = cases.map((caseData) => {
        const statusInfo = calculateCaseStatus(caseData as any);

        return {
          ...caseData,
          dashboardStatus: statusInfo,
          customerName: caseData.customer
            ? `${caseData.customer.firstName} ${caseData.customer.lastName}`
            : 'Unknown Customer',
        };
      });

      // Apply status filter if specified
      let filteredCases = enhancedCases;
      if (statusFilter !== 'all') {
        filteredCases = enhancedCases.filter((caseData) => {
          switch (statusFilter) {
            case 'action-required':
              return caseData.dashboardStatus.actionRequired;
            case 'open':
              return !(
                [CaseStatus.Rejected, CaseStatus.Applied] as CaseStatus[]
              ).includes(caseData.status);
            case 'completed':
              return (
                [CaseStatus.Rejected, CaseStatus.Applied] as CaseStatus[]
              ).includes(caseData.status);
            default:
              return true;
          }
        });
      }

      const meta = {
        total: filteredCases.length,
        pageCount: Math.ceil(filteredCases.length / limit),
        pageSize: limit,
        page: Math.floor(offset / limit) + 1,
      };

      return {
        items: filteredCases,
        meta,
        totalUnfiltered: total,
      };
    }),
});

// IMPORTANT: Create caller for server-side use(also can be used in other routers)
export const casesRouterCaller = casesRouter.createCaller({});
