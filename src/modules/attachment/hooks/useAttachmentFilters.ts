'use client';

import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';
import { useDebounceValue } from 'usehooks-ts';

export const attachmentSearchParamsClient = {
  limit: parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  offset: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  sortField: parseAsString
    .withDefault('uploadedAt')
    .withOptions({ clearOnDefault: true }),
  sortDirection: parseAsString
    .withDefault('desc')
    .withOptions({ clearOnDefault: true }),
  search: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  caseId: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  messageId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  uploadedBy: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  fileType: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  orderBy: parseAsString
    .withDefault('uploadedAt')
    .withOptions({ clearOnDefault: true }),
};

export const useAttachmentFilters = () => {
  const [urlFilters, setUrlFilters] = useQueryStates(
    attachmentSearchParamsClient,
    {
      shallow: true,
      // limitUrlUpdates: throttle(1000), // Maximum once per second,
    }
  );

  console.log('attachment urlFilters', urlFilters);

  const [debouncedFilters] = useDebounceValue(urlFilters, 300);

  // Helper function to update filters
  const updateFilters = (newFilters: Partial<typeof urlFilters>) => {
    setUrlFilters(newFilters);
  };

  // Helper function to clear all filters
  const clearFilters = () => {
    setUrlFilters({
      limit: 10,
      offset: 0,
      sortField: 'uploadedAt',
      sortDirection: 'desc',
      search: '',
      caseId: 0,
      messageId: 0,
      uploadedBy: 0,
      fileType: '',
      orderBy: 'uploadedAt',
    });
  };

  // Return URL filters for immediate UI feedback and debounced filters for queries
  return {
    // Immediate filters for UI (input values, URL state)
    filters: urlFilters,
    // Debounced filters for API queries
    debouncedFilters,
    // Helper functions
    updateFilters,
    clearFilters,
    // Setter function for immediate URL updates
    setFilters: setUrlFilters,
  } as const;
};
