import { z } from 'zod';

// Input schemas for attachment operations
export const getOneAttachmentInputSchema = z.object({
  attachmentId: z.number().int().positive(),
});

export const attachmentCreateSchema = z.object({
  fileName: z.string().min(1).max(255),
  caseId: z.number().int().positive().optional(),
  messageId: z.number().int().positive().optional(),
  // File content will be handled separately via FormData
});

export const attachmentUpdateSchema = z.object({
  attachmentId: z.number().int().positive(),
  fileName: z.string().min(1).max(255).optional(),
});

export const removeAttachmentSchema = z.object({
  attachmentId: z.number().int().positive(),
});

// File upload schema for FormData validation
export const fileUploadSchema = z.object({
  file: z.instanceof(File),
  caseId: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : undefined)),
  messageId: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : undefined)),
});

// Response schemas
export const attachmentResponseSchema = z.object({
  attachmentId: z.number(),
  caseId: z.number().nullable(),
  messageId: z.number().nullable(),
  fileName: z.string(),
  filePath: z.string(),
  uploadedAt: z.date(),
  uploadedBy: z.number(),
  uploadedByUser: z
    .object({
      userId: z.number(),
      firstName: z.string().nullable(),
      lastName: z.string().nullable(),
      email: z.string(),
    })
    .optional(),
  case: z
    .object({
      caseId: z.number(),
      caseNumber: z.string(),
      caseType: z.string(),
    })
    .optional(),
  message: z
    .object({
      messageId: z.number(),
      subject: z.string().nullable(),
    })
    .optional(),
});

export const attachmentListResponseSchema = z.object({
  items: z.array(attachmentResponseSchema),
  meta: z.object({
    total: z.number(),
    pageCount: z.number(),
    pageSize: z.number(),
    page: z.number(),
  }),
});

// Type exports
export type GetOneAttachmentInput = z.infer<typeof getOneAttachmentInputSchema>;
export type AttachmentCreateInput = z.infer<typeof attachmentCreateSchema>;
export type AttachmentUpdateInput = z.infer<typeof attachmentUpdateSchema>;
export type RemoveAttachmentInput = z.infer<typeof removeAttachmentSchema>;
export type FileUploadInput = z.infer<typeof fileUploadSchema>;
export type AttachmentResponse = z.infer<typeof attachmentResponseSchema>;
export type AttachmentListResponse = z.infer<
  typeof attachmentListResponseSchema
>;
