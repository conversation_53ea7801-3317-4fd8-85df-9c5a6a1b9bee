import 'server-only'; // <-- ensure this file cannot be imported from the client

import {
  createLoader,
  createStandardSchemaV1,
  parseAsInteger,
  parseAsString,
} from 'nuqs/server';

export const attachmentFiltersSearchParamsServer = {
  limit: parseAsInteger.withDefault(10).withOptions({ clearOnDefault: true }),
  offset: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  sortField: parseAsString
    .withDefault('uploadedAt')
    .withOptions({ clearOnDefault: true }),
  sortDirection: parseAsString
    .withDefault('desc')
    .withOptions({ clearOnDefault: true }),
  search: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  caseId: parseAsInteger.withDefault(0).withOptions({ clearOnDefault: true }),
  messageId: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  uploadedBy: parseAsInteger
    .withDefault(0)
    .withOptions({ clearOnDefault: true }),
  fileType: parseAsString.withDefault('').withOptions({ clearOnDefault: true }),
  orderBy: parseAsString
    .withDefault('uploadedAt')
    .withOptions({ clearOnDefault: true }),
};

// Create loader for server-side parsing
export const loadAttachmentSearchParams = createLoader(
  attachmentFiltersSearchParamsServer
);

// Create Standard Schema V1 compatible validator for tRPC
export const attachmentSearchParamsSchema = createStandardSchemaV1(
  attachmentFiltersSearchParamsServer
);

// Type exports
export type AttachmentFiltersSearchParams =
  typeof attachmentFiltersSearchParamsServer;
