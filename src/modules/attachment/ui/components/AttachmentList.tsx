'use client';

import {
  AttachFile as AttachFileIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import {
  Box,
  Card,
  CardActions,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import { type Attachment } from '@prisma/client';

// Type for attachment with included relations
type AttachmentWithRelations = Attachment & {
  uploadedByUser?: {
    userId: number;
    firstName: string | null;
    lastName: string | null;
    email: string;
  } | null;
  case?: {
    caseId: number;
    caseNumber: string;
    caseType: string;
  } | null;
  message?: {
    messageId: number;
    subject: string | null;
  } | null;
};

interface AttachmentListProps {
  attachments: AttachmentWithRelations[];
  onViewAttachment: (attachmentId: number) => void;
  onEditAttachment: (attachmentId: number) => void;
  onDeleteAttachment: (attachmentId: number) => void;
  onDownloadAttachment?: (attachmentId: number) => void;
}

export const AttachmentList = ({
  attachments,
  onViewAttachment,
  onEditAttachment,
  onDeleteAttachment,
  onDownloadAttachment,
}: AttachmentListProps) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const getFileExtension = (fileName: string) => {
    return fileName.split('.').pop()?.toUpperCase() || 'FILE';
  };

  const getUserDisplayName = (
    user?: AttachmentWithRelations['uploadedByUser']
  ) => {
    if (!user) return 'Unknown User';
    const fullName = [user.firstName, user.lastName].filter(Boolean).join(' ');
    return fullName || user.email;
  };

  if (attachments.length === 0) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: 8,
          textAlign: 'center',
        }}
      >
        <AttachFileIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No attachments found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create your first attachment to get started
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: 'repeat(2, 1fr)',
            lg: 'repeat(3, 1fr)',
          },
          gap: 2,
        }}
      >
        {attachments.map((attachment) => (
          <Card
            key={attachment.attachmentId}
            sx={{
              transition: 'box-shadow 0.2s',
              '&:hover': {
                boxShadow: 2,
              },
            }}
          >
            <CardContent>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: 2,
                  mb: 2,
                }}
              >
                <AttachFileIcon color="primary" />
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant="h6"
                    component="h3"
                    gutterBottom
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {attachment.fileName}
                  </Typography>
                  <Chip
                    label={getFileExtension(attachment.fileName)}
                    size="small"
                    variant="outlined"
                    sx={{ mb: 1 }}
                  />
                </Box>
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>ID:</strong> {attachment.attachmentId}
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Uploaded:</strong> {formatDate(attachment.uploadedAt)}
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>By:</strong>{' '}
                {getUserDisplayName(attachment.uploadedByUser)}
              </Typography>

              {attachment.case && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  <strong>Case:</strong> {attachment.case.caseNumber} (
                  {attachment.case.caseType})
                </Typography>
              )}

              {attachment.message && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  <strong>Message:</strong>{' '}
                  {attachment.message.subject ||
                    `Message #${attachment.message.messageId}`}
                </Typography>
              )}

              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  display: 'block',
                  mt: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                Path: {attachment.filePath}
              </Typography>
            </CardContent>

            <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
              <Box>
                <Tooltip title="View Details">
                  <IconButton
                    size="small"
                    onClick={() => onViewAttachment(attachment.attachmentId)}
                    color="primary"
                  >
                    <ViewIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Edit">
                  <IconButton
                    size="small"
                    onClick={() => onEditAttachment(attachment.attachmentId)}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                </Tooltip>

                {onDownloadAttachment && (
                  <Tooltip title="Download">
                    <IconButton
                      size="small"
                      onClick={() =>
                        onDownloadAttachment(attachment.attachmentId)
                      }
                      color="primary"
                    >
                      <DownloadIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>

              <Tooltip title="Delete">
                <IconButton
                  size="small"
                  onClick={() => onDeleteAttachment(attachment.attachmentId)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            </CardActions>
          </Card>
        ))}
      </Box>
    </Box>
  );
};
