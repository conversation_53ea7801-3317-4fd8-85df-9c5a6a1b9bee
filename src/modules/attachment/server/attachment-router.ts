import { type Prisma } from '@prisma/client';
import { TRPCError } from '@trpc/server';

import { attachmentSearchParamsSchema } from '@/modules/attachment/types/attachment-params';
import {
  attachmentCreateSchema,
  attachmentUpdateSchema,
  getOneAttachmentInputSchema,
  removeAttachmentSchema,
} from '@/modules/attachment/types/attachment-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';

export const attachmentsRouter = createTRPCRouter({
  getOne: protectedProcedure
    .input(getOneAttachmentInputSchema)
    .query(async ({ input }) => {
      // Extract user info from mock context

      const existingAttachment = await prisma.attachment.findFirst({
        where: { attachmentId: input.attachmentId },
        include: {
          uploadedByUser: {
            select: {
              userId: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          case: { select: { caseId: true, caseNumber: true, caseType: true } },
          message: { select: { messageId: true, subject: true } },
        },
      });

      if (!existingAttachment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Attachment not found',
        });
      }

      console.log('existingAttachment', existingAttachment);

      return existingAttachment;
    }),

  getMany: protectedProcedure
    .input(attachmentSearchParamsSchema)
    .query(async ({ input }) => {
      // Extract user info from mock context

      const {
        limit,
        offset,
        sortField,
        sortDirection,
        search,
        caseId,
        messageId,
        uploadedBy,
        fileType,
      } = input;

      // Build where clause for filtering
      const whereClause: Prisma.AttachmentWhereInput = {};

      if (search) {
        whereClause.OR = [
          { fileName: { contains: search, mode: 'insensitive' } },
          { filePath: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (caseId && caseId > 0) {
        whereClause.caseId = caseId;
      }

      if (messageId && messageId > 0) {
        whereClause.messageId = messageId;
      }

      if (uploadedBy && uploadedBy > 0) {
        whereClause.uploadedBy = uploadedBy;
      }

      if (fileType) {
        // This would require adding a mimeType field to the schema
        // For now, we'll filter by file extension
        whereClause.fileName = { endsWith: fileType };
      }

      const orderBy = { [sortField]: sortDirection };

      const [items, total] = await prisma.$transaction([
        prisma.attachment.findMany({
          where: whereClause,
          orderBy,
          skip: offset,
          take: limit,
          include: {
            uploadedByUser: {
              select: {
                userId: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            case: {
              select: { caseId: true, caseNumber: true, caseType: true },
            },
            message: { select: { messageId: true, subject: true } },
          },
        }),
        prisma.attachment.count({ where: whereClause }),
      ]);

      const meta = {
        total,
        pageCount: Math.ceil(total / limit),
        pageSize: limit,
        page: Math.floor(offset / limit) + 1,
      };

      return { items, meta };
    }),

  create: protectedProcedure
    .input(attachmentCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Extract user info from mock context
      const userId = ctx.auth.user.userId!;

      // Ensure the user exists, create if not
      const user = await prisma.user.upsert({
        where: { userId: userId },
        create: {
          userId: userId,
          username: `mock_user_${userId}`,
          email: `mock_user_${userId}@example.com`,
          role: 'admin',
          firstName: 'Mock',
          lastName: 'User',
        },
        update: {}, // Don't update if exists
      });

      const createdAttachment = await prisma.attachment.create({
        data: {
          fileName: input.fileName,
          filePath: `/uploads/${Date.now()}-${input.fileName}`,
          caseId: input.caseId || null,
          messageId: input.messageId || null,
          uploadedBy: user.userId,
        },
        include: {
          uploadedByUser: {
            select: {
              userId: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return createdAttachment;
    }),

  update: protectedProcedure
    .input(attachmentUpdateSchema)
    .mutation(async ({ input }) => {
      // Extract user info from mock context

      const { attachmentId, ...updateData } = input;

      // Check if attachment exists
      const existingAttachment = await prisma.attachment.findFirst({
        where: { attachmentId },
      });
      if (!existingAttachment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Attachment not found',
        });
      }

      const updatedAttachment = await prisma.attachment.update({
        where: { attachmentId },
        data: updateData,
        include: {
          uploadedByUser: {
            select: {
              userId: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return updatedAttachment;
    }),

  remove: protectedProcedure
    .input(removeAttachmentSchema)
    .mutation(async ({ input }) => {
      // Extract user info from mock context

      // Check if attachment exists
      const existingAttachment = await prisma.attachment.findFirst({
        where: { attachmentId: input.attachmentId },
      });
      if (!existingAttachment) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Attachment not found',
        });
      }

      const deletedAttachment = await prisma.attachment.delete({
        where: { attachmentId: input.attachmentId },
      });

      return deletedAttachment;
    }),
});
