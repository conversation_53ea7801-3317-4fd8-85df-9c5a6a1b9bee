import { z } from 'zod';

export const updateQuestionnaireSchema = z.object({
  questionnaireId: z.number(),
  caseCustomerId: z.number(),
  formId: z.string(),
  type: z.string(),
  status: z.string(),
  answersJson: z.any(),
});

export const createQuestionnaireSchema = z.object({
  caseCaseId: z.number(),
  formId: z.string(),
  type: z.string(),
  status: z.string(),
  answersJson: z.any(),
});

export type UpdateOrCreateQuestionnaireSchemaInputs = z.infer<
  typeof updateQuestionnaireSchema
>;
