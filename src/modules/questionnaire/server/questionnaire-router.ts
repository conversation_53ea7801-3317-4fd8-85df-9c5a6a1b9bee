import { TRPCError } from '@trpc/server';
import z from 'zod';

import { getOneInputSchema } from '@/modules/case/types/case-schemas';
import {
  createQuestionnaireSchema,
  updateQuestionnaireSchema,
} from '@/modules/questionnaire/types/questionnaire-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';

export const questionnaireRouter = createTRPCRouter({
  getQuestionnairesByCustomerId: protectedProcedure
    .input(z.object({ caseCustomerId: z.number() }))
    .query(async ({ input, ctx: _ctx }) => {
      const caseForCustomerWithQuestionnaires = await prisma.case.findFirst({
        where: { customerId: input.caseCustomerId },
        include: {
          questionnaires: true,
        },
      });

      if (
        !caseForCustomerWithQuestionnaires ||
        !(caseForCustomerWithQuestionnaires.questionnaires?.length > 0)
      ) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Questionnaires not found',
        });
      }

      return caseForCustomerWithQuestionnaires.questionnaires;
    }),

  updateAnswersByQuestionnaireId: protectedProcedure
    .input(
      z.object({
        questionnaireId: z.number(),
        answersJson: z.any(),
        status: z.string().optional().nullable(),
      })
    )
    .mutation(async ({ input, ctx: _ctx }) => {
      const updateQuestionnaire = await prisma.questionnaire.update({
        where: { questionnaireId: input.questionnaireId },
        data:
          typeof input.status === 'string'
            ? { answersJson: input.answersJson, status: input.status }
            : { answersJson: input.answersJson },
      });

      if (!updateQuestionnaire) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Questionnaires not found',
        });
      }

      return updateQuestionnaire;
    }),

  update: protectedProcedure
    .input(updateQuestionnaireSchema)
    .mutation(async ({ input, ctx: _ctx }) => {
      const updateQuestionnaire = await prisma.questionnaire.update({
        where: { questionnaireId: input.questionnaireId },
        data: { ...input },
      });

      if (!updateQuestionnaire) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Questionnaires not found',
        });
      }

      return updateQuestionnaire;
    }),

  create: protectedProcedure
    .input(createQuestionnaireSchema)
    .mutation(async ({ input, ctx: _ctx }) => {
      const existingQuestionnaire = await prisma.questionnaire.findFirst({
        where: { formId: input.formId, caseCaseId: input.caseCaseId },
      });

      if (existingQuestionnaire === null) {
        const updateQuestionnaire = await prisma.questionnaire.create({
          data: { ...input },
        });

        if (!updateQuestionnaire) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Error creating questionnaire',
          });
        }
        return updateQuestionnaire;
      }

      return existingQuestionnaire;
    }),

  getOne: protectedProcedure
    .input(getOneInputSchema)
    .query(async ({ input, ctx: _ctx }) => {
      const existingCase = await prisma.case.findFirst({
        where: { caseId: input.caseId },
      });

      if (!existingCase) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Case not found',
        });
      }

      return existingCase;
    }),
});

// IMPORTANT: Create caller for server-side use(also can be used in other routers)
export const questionnaireRouterCaller = questionnaireRouter.createCaller({});
