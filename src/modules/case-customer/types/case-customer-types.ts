import {
  type Case,
  type Customer,
  type Questionnaire,
  RelationType,
} from '@prisma/client';

import { type CustomerWithRelations } from '@/utils/helperTypes';

// Complete case with all customer data
export interface CaseWithAllCustomerData extends Case {
  caseCustomer: Array<{
    caseCustomerId: number;
    customerId: number;
    relationType: RelationType | null;
    email: string | null;
    customer: Customer;
    questionnaires: Questionnaire[];
  }>;
  customer: Customer; // Primary customer (for backward compatibility)
  assignedUser?: {
    userId: number;
    firstName: string | null;
    lastName: string | null;
    email: string;
  } | null;
  //   monthlyPremium is a Prisma decimal
  potentialInsurances: Array<{
    monthlyPremium: number | null;
    caseId: number;
    insurerName: string;
    potentialInsuranceId: number;
    productName: string;
    coverageDetails: string | null;
    notes: string | null;
  }>;
}

// Customer data for case creation
export interface CustomerForCaseCreation {
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  gender?: string;
  email?: string;
  phoneNumber?: string;
  street?: string;
  houseNumber?: string;
  postalCode?: string;
  city?: string;
  country?: string;
  relationType: RelationType;
  questionnaires?: Array<{
    formId: string;
    type: string;
    status?: string;
    answersJson?: Record<string, any>;
  }>;
}

// Response type for case creation with customers
export interface CaseCreationResponse {
  case: Case;
  customers: Array<{
    customer: Customer;
    questionnaires: Questionnaire[];
  }>;
}

// Case customer list response
export interface CustomerListResponse {
  items: CustomerWithRelations[];
  meta: {
    total: number;
    pageCount: number;
    pageSize: number;
    page: number;
  };
}

// Case customer filter options
export interface CaseCustomerFilters {
  caseId?: number;
  customerId?: number;
  relationType?: RelationType;
  search?: string;
}

// Case customer sort options
export type CaseCustomerSortField =
  | 'caseCustomerId'
  | 'caseId'
  | 'customerId'
  | 'relationType';

export type CaseCustomerSortDirection = 'asc' | 'desc';

// Questionnaire with structured data
export interface StructuredQuestionnaire extends Questionnaire {
  answersJson: Record<string, any> | null;
}

// Relation type options for frontend
export const RELATION_TYPE_OPTIONS = [
  { value: RelationType.paying, label: 'Paying Customer' },
  {
    value: RelationType.paying_and_insured_primary,
    label: 'Paying & Primary Insured',
  },
  { value: RelationType.insured_primary, label: 'Primary Insured' },
  { value: RelationType.child, label: 'Child' },
  { value: RelationType.spouse, label: 'Spouse' },
] as const;

// Helper type for relation type labels
export type RelationTypeOption = (typeof RELATION_TYPE_OPTIONS)[number];

// Email validation helper type
export interface EmailValidationResult {
  isValid: boolean;
  isRequired: boolean;
  message?: string;
}

// Customer deletion result
export interface CustomerDeletionResult {
  deletedCustomer: Customer;
  affectedCases: number[];
}
