import { RelationType } from '@prisma/client';
import { z } from 'zod';

// Schema for getting a single case customer
export const getOneCaseCustomerInputSchema = z.object({
  caseCustomerId: z.number().int().positive(),
});

// Schema for creating a case customer
export const caseCustomerCreateSchema = z.object({
  caseId: z.number().int().positive(),
  customerId: z.number().int().positive(),
  relationType: z
    .enum([
      'paying',
      'paying_and_insured_primary',
      'insured_primary',
      'child',
      'spouse',
    ])
    .optional(),
  email: z.email('Invalid email format').optional(),
});

// Schema for updating a case customer
export const updateCaseCustomerSchema = z.object({
  caseCustomerId: z.number().int().positive(),
  relationType: z
    .enum([
      'paying',
      'paying_and_insured_primary',
      'insured_primary',
      'child',
      'spouse',
    ])
    .optional(),
  email: z.email('Invalid email format').optional(),
});

// Schema for removing a case customer
export const removeCaseCustomerSchema = z.object({
  caseCustomerId: z.number().int().positive(),
});

// Schema for questionnaire with formId and structured JSON
export const questionnaireCreateSchema = z.object({
  caseCustomerId: z.number().int().positive(),
  formId: z.string().min(1, 'Form ID is required'),
  type: z.string().min(1, 'Type is required'),
  status: z.string().default('Pending'),
  answersJson: z.record(z.string(), z.any()).optional(), // Structured JSON object
});

// Schema for updating questionnaire
export const updateQuestionnaireSchema = z.object({
  questionnaireId: z.number().int().positive(),
  formId: z.string().min(1, 'Form ID is required').optional(),
  type: z.string().min(1, 'Type is required').optional(),
  status: z.string().optional(),
  answersJson: z.record(z.string(), z.any()).optional(), // Structured JSON object
});

// Schema for customer data in case creation (multiple customers)
export const customerDataForCaseSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  dateOfBirth: z.coerce.date(),
  gender: z.string().optional(),
  email: z.email('Invalid email format').optional(),
  phoneNumber: z.string().optional(),
  street: z.string().optional(),
  houseNumber: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  // Questionnaire data
  questionnaires: z
    .array(
      z.object({
        formId: z.string().min(1, 'Form ID is required'),
        type: z.string().min(1, 'Type is required'),
        status: z.string().default('Pending'),
        answersJson: z.record(z.string(), z.any()).optional(),
      })
    )
    .optional(),
});

// REMOVED: createCaseWithCustomersSchema - use customers.createWithCase instead

// REMOVED: saveWipCustomerToCaseSchema - basic implementation doesn't need this yet

// Schema for adding customer to existing case
export const addCustomerToCaseGroupSchema = z.object({
  caseGroupId: z.number().int().positive(),
  relationType: z.enum(RelationType).optional(),
  email: z.email('Invalid email format').optional(),
  customerData: customerDataForCaseSchema,
});

export const addCaseCustomerCaseSchema = z.object({
  caseId: z.number().int().positive(),
  relationType: z.enum(RelationType).optional(),
  email: z.email('Invalid email format').optional(),
  questionnaires: questionnaireCreateSchema.array().optional(),
  customer: customerDataForCaseSchema,
});

// Schema for upserting (create or update) customer to case
export const upsertCaseCustomerCaseSchema = z.object({
  caseId: z.number().int().positive(),
  customerId: z.number().int().positive().optional(), // If provided, update existing customer
  relationType: z.enum(RelationType).optional(),
  email: z.email('Invalid email format').optional(),
  questionnaires: questionnaireCreateSchema.array().optional(),
  customer: customerDataForCaseSchema,
});

// Type exports
export type GetOneCaseCustomerInput = z.infer<
  typeof getOneCaseCustomerInputSchema
>;
export type CaseCustomerCreateInput = z.infer<typeof caseCustomerCreateSchema>;
export type UpdateCaseCustomerInput = z.infer<typeof updateCaseCustomerSchema>;
export type RemoveCaseCustomerInput = z.infer<typeof removeCaseCustomerSchema>;
export type QuestionnaireCreateInput = z.infer<
  typeof questionnaireCreateSchema
>;
export type UpdateQuestionnaireInput = z.infer<
  typeof updateQuestionnaireSchema
>;
export type CustomerDataForCaseInput = z.infer<
  typeof customerDataForCaseSchema
>;
// REMOVED: SaveWipCustomerToCaseInput - not needed for basic implementation
export type AddCustomerToCaseInput = z.infer<
  typeof addCustomerToCaseGroupSchema
>;
export type UpsertCaseCustomerCaseInput = z.infer<
  typeof upsertCaseCustomerCaseSchema
>;
