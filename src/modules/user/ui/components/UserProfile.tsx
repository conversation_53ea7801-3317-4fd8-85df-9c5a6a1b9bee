'use client';

import {
  <PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Typography,
} from '@mui/material';

import { useUser } from '@/modules/user/hooks/useUser';

export function UserProfile() {
  const { user, isLoading, error } = useUser();

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Typography color="error">
          Error loading user profile: {error.message}
        </Typography>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box p={3}>
        <Typography>No user data available</Typography>
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          User Profile
        </Typography>

        <Box sx={{ display: 'grid', gap: 1 }}>
          <Typography>
            <strong>Name:</strong> {user.firstName} {user.lastName}
          </Typography>
          <Typography>
            <strong>Username:</strong> {user.username}
          </Typography>
          <Typography>
            <strong>Email:</strong> {user.email}
          </Typography>
          <Typography>
            <strong>Role:</strong> {user.role}
          </Typography>
          {user.phoneNumber && (
            <Typography>
              <strong>Phone:</strong> {user.phoneNumber}
            </Typography>
          )}
          {user.city && (
            <Typography>
              <strong>City:</strong> {user.city}
            </Typography>
          )}
          <Typography>
            <strong>Last Login:</strong>{' '}
            {user.lastLogin
              ? new Date(user.lastLogin).toLocaleString()
              : 'Never'}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
}
