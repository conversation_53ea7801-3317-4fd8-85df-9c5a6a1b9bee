import { z } from 'zod';

// Schema for getting current user (no input needed - uses ctx.auth.user.id)
export const getCurrentUserInputSchema = z.object({});

// Schema for getting a specific user by ID (for future admin functionality)
export const getUserInputSchema = z.object({
  userId: z.number().int().positive(),
});

// Schema for updating user profile (for future use)
export const updateUserProfileSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  email: z.email('Invalid email format').optional(),
  phoneNumber: z.string().optional(),
  street: z.string().optional(),
  houseNumber: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
});

// Schema for changing password (for future use)
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

// Response schema for user data (defines what fields to return)
export const userResponseSchema = z.object({
  userId: z.number(),
  username: z.string(),
  role: z.string(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  email: z.string(),
  phoneNumber: z.string().nullable(),
  street: z.string().nullable(),
  houseNumber: z.string().nullable(),
  postalCode: z.string().nullable(),
  city: z.string().nullable(),
  country: z.string().nullable(),
  createdAt: z.date(),
  lastLogin: z.date().nullable(),
  termsAndConditionsAccepted: z.boolean(),
});

// Type exports following project conventions
export type GetCurrentUserInput = z.infer<typeof getCurrentUserInputSchema>;
export type GetUserInput = z.infer<typeof getUserInputSchema>;
export type UpdateUserProfileInput = z.infer<typeof updateUserProfileSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type UserResponse = z.infer<typeof userResponseSchema>;
