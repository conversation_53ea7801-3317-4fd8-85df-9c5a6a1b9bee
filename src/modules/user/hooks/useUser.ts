'use client';

import { useQuery, useSuspenseQuery } from '@tanstack/react-query';

import { useTRPC } from '@/trpc/client';

export function useUser() {
  const trpc = useTRPC();

  // Get current user data (fresh from database)
  const {
    data: userData,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser,
  } = useQuery({
    ...trpc.user.getCurrentUser.queryOptions(),
  });

  return {
    user: userData,
    isLoading: isUserLoading,
    error: userError,
    refetch: refetchUser,
  };
}

// Hook for getting current user with suspense (for use in components wrapped with Suspense)
export function useUserSuspense() {
  const trpc = useTRPC();

  const userData = useSuspenseQuery(trpc.user.getCurrentUser.queryOptions());

  const userInitials = `${userData?.data?.firstName?.charAt(0)}${userData?.data?.lastName?.charAt(0)}`;
  const userName = `${userData?.data?.firstName} ${userData?.data?.lastName}`;

  return {
    user: userData?.data,
    refetch: userData?.refetch,
    userInitials,
    userName,
  };
}
