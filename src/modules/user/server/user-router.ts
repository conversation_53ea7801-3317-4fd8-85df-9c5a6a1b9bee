import { TRPCError } from '@trpc/server';

import { welcomeFormSchema } from '@/modules/welcome/types/welcome-schemas';
import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import prisma from '@/utils/prisma-db';
export const userRouter = createTRPCRouter({
  getCurrentUser: protectedProcedure.query(async ({ ctx }) => {
    // Extract user info from context
    const userKeycloakId = ctx.auth.user.keycloakId;

    try {
      // Fetch fresh user data from database
      const user = await prisma.user.findUnique({
        where: { keycloakId: userKeycloakId },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return user;
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error('Get current user error:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'An error occurred while fetching user data',
      });
    }
  }),

  updateProfile: protectedProcedure
    .input(welcomeFormSchema)
    .mutation(async ({ ctx, input }) => {
      const userKeycloakId = ctx.auth.user.keycloakId;

      try {
        const updatedUser = await prisma.user.update({
          where: { keycloakId: userKeycloakId },
          data: input,
        });

        return updatedUser;
      } catch (error) {
        console.error('Update profile error:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Error updating profile',
        });
      }
    }),
});

export const userRouterCaller = userRouter.createCaller({});
