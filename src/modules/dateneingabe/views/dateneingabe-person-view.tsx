'use client';
import EditNoteIcon from '@mui/icons-material/EditNote';
import { Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { DataInputFrame } from '@/components/forms/DataInputFrame';
import Navbar from '@/components/layout/navbar';
import StickyBottomBar from '@/components/StickyBottomBar';
import { CaseNavbarTitle } from '@/modules/case/ui/components/CaseNavbarTitle';

interface DateneingabePersonViewProps {
  caseNumber: string;
}
export default function DateneingabePersonView({
  caseNumber,
}: DateneingabePersonViewProps) {
  const t = useTranslations();
  const [stickyBottomBarContent, setStickyBottomBarContent] =
    useState<React.ReactElement | null>(null);

  return (
    <>
      <Navbar showOverviewButton={true} showNewCaseButton={true}>
        <EditNoteIcon sx={{ color: 'white' }} />
        <Typography color="white" fontSize="1.2rem">
          {t('words.data_input')}:
        </Typography>
        <CaseNavbarTitle caseNumber={caseNumber} noCaseNumber={true} />
      </Navbar>

      <DataInputFrame
        caseNumber={caseNumber}
        setStickyBottomBarContent={(content) => {
          setStickyBottomBarContent(content);
        }}
      />

      {stickyBottomBarContent && (
        <StickyBottomBar sx={{ marginTop: 'auto' }}>
          {stickyBottomBarContent}
        </StickyBottomBar>
      )}
    </>
  );
}

export const NotFoundDateneingabePersonView = () => {
  return <Typography>Vorgang nicht gefunden</Typography>;
};
