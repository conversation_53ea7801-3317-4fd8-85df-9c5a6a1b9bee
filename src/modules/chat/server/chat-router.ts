import { StreamChat } from 'stream-chat';

import { createTRPCRouter, protectedProcedure } from '@/trpc/init';
import { ENV } from '@/utils/env';
import prisma from '@/utils/prisma-db';

export const chatRouter = createTRPCRouter({
  getStreamToken: protectedProcedure.query(async ({ ctx }) => {
    // Extract user info from mock context
    const authUser = ctx.auth.user;

    const serverClient = StreamChat.getInstance(
      ENV.STREAM_API_KEY!,
      ENV.STREAM_API_SECRET!
    );

    const token = serverClient.createToken(String(authUser.userId));

    await serverClient.upsertUser({
      id: String(authUser.id),
      name: `${authUser.firstName} ${authUser.lastName}`,
    });

    return {
      token,
      user: {
        id: String(authUser.userId),
        name: `${authUser.firstName} ${authUser.lastName}`,
      },
    };
  }),

  getCasesForChat: protectedProcedure.query(async ({ ctx }) => {
    // Extract user info from mock context
    const _session = ctx.auth.user;

    // Get all cases - we'll use Stream Chat channels for messaging
    const cases = await prisma.case.findMany({
      include: {
        customer: {
          select: { customerId: true, firstName: true, lastName: true },
        },
        assignedUser: {
          select: { userId: true, firstName: true, lastName: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return cases;
  }),
});

export const chatRouterCaller = chatRouter.createCaller({});
