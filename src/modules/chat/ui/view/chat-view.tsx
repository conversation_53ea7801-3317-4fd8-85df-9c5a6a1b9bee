'use client';

import 'stream-chat-react/dist/css/v2/index.css';
import '@/modules/chat/ui/styles/chat-overrides.css';

import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import type { Channel as StreamChannel } from 'stream-chat';
import {
  Channel,
  ChannelHeader,
  MessageInput,
  MessageList,
  Thread,
  useChatContext,
  Window,
} from 'stream-chat-react';

import { CaseListSidebar } from '@/modules/chat/ui/components/CaseListSidebar';

interface ChatViewProps {
  selectedCaseId?: number;
}

export const ChatView = ({ selectedCaseId }: ChatViewProps) => {
  const router = useRouter();
  const [channel, setChannel] = useState<StreamChannel>();
  const { client } = useChatContext();

  // Create or get channel for selected case
  useEffect(() => {
    if (!client || !selectedCaseId) {
      setChannel(undefined);
      return;
    }

    const channelId = `case-${selectedCaseId}`;
    const caseChannel = client.channel('messaging', channelId, {
      members: [client.userID!],
    });

    console.log('Setting up channel for case:', selectedCaseId);
    setChannel(caseChannel);
  }, [selectedCaseId, client]);

  const handleCaseSelect = (caseId: number) => {
    router.push(`/chat/${caseId}`);
  };

  if (!client) {
    return null;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        height: 'calc(100vh - 160px)', // Account for header (105px) and footer (55px)
        maxHeight: 'calc(100vh - 160px)',
        overflow: 'hidden',
      }}
    >
      {/* Left Sidebar - Cases List */}
      <Box sx={{ width: 320, flexShrink: 0, height: '100%' }}>
        <CaseListSidebar
          selectedCaseId={selectedCaseId}
          onCaseSelect={handleCaseSelect}
        />
      </Box>

      {/* Right Panel - Stream Chat */}
      <Box
        sx={{
          flex: 1,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        {channel ? (
          <Channel channel={channel}>
            <Window>
              <ChannelHeader />
              <MessageList />
              <MessageInput />
            </Window>
            <Thread />
          </Channel>
        ) : (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: 'text.secondary',
            }}
          >
            Select a case to start chatting
          </Box>
        )}
      </Box>
    </Box>
  );
};
