/* Stream Chat React CSS Overrides for Fixed Layout */

/* Ensure the main chat container uses full height */
.str-chat {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Channel container should use full height */
.str-chat__channel {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Window container should use full height and be flex */
.str-chat__main-panel {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Channel header should be fixed height */
.str-chat__header-livestream {
  flex-shrink: 0 !important;
  height: auto !important;
  min-height: 60px !important;
}

/* Message list should take remaining space and be scrollable */
.str-chat__list {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: auto !important;
  max-height: none !important;
}

/* Message list container */
.str-chat__list--messaging {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 16px !important;
}

/* Message input should be fixed at bottom */
.str-chat__input-flat {
  flex-shrink: 0 !important;
  position: relative !important;
  bottom: 0 !important;
  background: white !important;
  border-top: 1px solid #e0e0e0 !important;
  padding: 16px !important;
  z-index: 10 !important;
}

/* Message input wrapper */
.str-chat__input-flat--wrapper {
  margin: 0 !important;
}

/* Thread panel adjustments */
.str-chat__thread {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure proper scrolling for message list */
.str-chat__virtual-list {
  height: 100% !important;
  overflow-y: auto !important;
}

/* Fix for message list virtualization */
.str-chat__list-container {
  height: 100% !important;
  overflow-y: auto !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .str-chat__input-flat {
    padding: 12px !important;
  }

  .str-chat__list--messaging {
    padding: 12px !important;
  }
}

/* Ensure message bubbles don't break layout */
.str-chat__message-simple {
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* Fix for attachment previews */
.str-chat__attachment-list {
  max-height: 200px !important;
  overflow-y: auto !important;
}

/* Loading indicator positioning */
.str-chat__loading-indicator {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* Ensure proper height for the entire chat window */
.str-chat__container {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Fix for message input container */
.str-chat__input {
  flex-shrink: 0 !important;
  margin: 0 !important;
}

/* Ensure message list scrolls properly */
.str-chat__list--messaging .str-chat__reverse-infinite-scroll {
  display: flex !important;
  flex-direction: column-reverse !important;
  overflow-y: auto !important;
  height: 100% !important;
}

/* Fix for empty state */
.str-chat__list--messaging .str-chat__list-notifications {
  position: sticky !important;
  top: 0 !important;
  z-index: 1 !important;
}
