'use client';

import { Chat, useCreateChatClient } from 'stream-chat-react';

import { type ChatGetStreamToken } from '@/modules/chat/types/chat-types';

export function ChatProvider({
  children,
  envPublic,
  streamToken,
}: {
  children: React.ReactNode;
  envPublic: any;
  streamToken: ChatGetStreamToken;
}) {
  const token = streamToken?.token;
  const user = streamToken?.user;

  const client = useCreateChatClient({
    apiKey: envPublic?.STREAM_API_KEY,
    tokenOrProvider: token,

    userData: user,
    options: {
      // Enable better error handling and reconnection
      timeout: 6000,
      logger: (logLevel: string, message: string, extraData?: any) => {
        if (logLevel === 'error') {
          console.error('Stream Chat Error:', message, extraData);
        }
      },
    },
  });

  if (!client) return null;

  return <Chat client={client}>{children}</Chat>;
}
