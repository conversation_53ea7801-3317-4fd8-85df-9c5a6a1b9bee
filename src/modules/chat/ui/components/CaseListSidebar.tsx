'use client';

import {
  Avatar,
  Box,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  TextField,
  Typography,
} from '@mui/material';
import { useSuspenseQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useState } from 'react';

import { ErrorFallback } from '@/components/ui/ErrorFallback';
import { useTRPC } from '@/trpc/client';

interface CaseListSidebarProps {
  selectedCaseId?: number;
  onCaseSelect: (caseId: number) => void;
}

export function CaseListSidebar({
  selectedCaseId,
  onCaseSelect,
}: CaseListSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const trpc = useTRPC();

  const {
    data: cases,
    error,
    refetch,
  } = useSuspenseQuery(trpc.chat.getCasesForChat.queryOptions());

  // Filter cases based on search query
  const filteredCases = cases.filter((caseItem) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      caseItem.caseNumber.toLowerCase().includes(searchLower) ||
      caseItem.caseType.toLowerCase().includes(searchLower) ||
      `${caseItem.customer?.firstName} ${caseItem.customer?.lastName}`
        .toLowerCase()
        .includes(searchLower)
    );
  });

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <ErrorFallback
          error={error as unknown as Error}
          resetErrorBoundary={() => refetch()}
        />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRight: 1,
        borderColor: 'divider',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" component="h2" gutterBottom>
          Cases
        </Typography>
        <TextField
          fullWidth
          size="small"
          placeholder="Search cases..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          sx={{ mt: 1 }}
        />
      </Box>

      {/* Cases List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {filteredCases.length === 0 ? (
          <Box
            sx={{
              p: 3,
              textAlign: 'center',
              color: 'text.secondary',
            }}
          >
            <Typography variant="body2">
              {searchQuery
                ? 'No cases found matching your search.'
                : 'No cases available for chat.'}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {filteredCases.map((caseItem, index) => {
              const isSelected = selectedCaseId === caseItem.caseId;
              const customerName = caseItem.customer
                ? `${caseItem.customer.firstName} ${caseItem.customer.lastName}`
                : 'Unknown Customer';

              return (
                <Box key={caseItem.caseId}>
                  <ListItem disablePadding>
                    <ListItemButton
                      selected={isSelected}
                      onClick={() => onCaseSelect(caseItem.caseId)}
                      sx={{
                        py: 2,
                        px: 2,
                        '&.Mui-selected': {
                          backgroundColor: 'primary.light',
                          '&:hover': {
                            backgroundColor: 'primary.light',
                          },
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            bgcolor: isSelected ? 'primary.main' : 'grey.400',
                            width: 40,
                            height: 40,
                          }}
                        >
                          {customerName.charAt(0).toUpperCase()}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              mb: 0.5,
                            }}
                          >
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              noWrap
                              sx={{ flex: 1, mr: 1 }}
                            >
                              {caseItem.caseNumber}
                            </Typography>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              {format(new Date(caseItem.createdAt), 'MMM dd')}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              noWrap
                              sx={{ mb: 0.5 }}
                            >
                              {customerName} • {caseItem.caseType}
                            </Typography>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              noWrap
                              sx={{
                                fontStyle: 'italic',
                                opacity: 0.8,
                              }}
                            >
                              Status: {caseItem.status}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                  {index < filteredCases.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </Box>
              );
            })}
          </List>
        )}
      </Box>
    </Box>
  );
}
