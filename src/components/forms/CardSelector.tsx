'use client';
import {
  Checkbox,
  FormControlLabel,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { KvcCard } from '@/components/cards/kvcCard';
import { GetIconById } from '@/components/modal/basicModal';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface Props {
  data?: any;
  options?: any;
  configPath: string;
  value: any[] | null;
  onValueChange: (newValue: any) => void;
  sx?: SxProps<Theme>;
}

export function CardSelector(props: Props) {
  const t = useTranslations();

  const configuration = useMemo<CardSelectorConfiguration | null>(() => {
    if (typeof props.configPath !== 'string') {
      return null;
    }
    const rawConfiguration = t.raw(props.configPath);
    if (
      typeof rawConfiguration === 'object' &&
      typeof rawConfiguration.options === 'object' &&
      Array.isArray(rawConfiguration.options) &&
      typeof rawConfiguration.none_option_label === 'string'
    ) {
      return rawConfiguration;
    }
    return null;
  }, [props.configPath, t]);

  const optionsDisabled = useMemo(() => {
    return props.value === null || typeof props.value === 'undefined';
  }, [props.value]);

  return (
    <>
      {configuration !== null ? (
        <>
          <Stack
            direction="column"
            gap={1}
            sx={{
              maxWidth: '100%',
              display: 'flex',
              flexWrap: 'wrap',
              ...props.sx,
            }}
          >
            {/* <div>{JSON.stringify(props.value)}</div> */}
            <Typography color="var(--teal)" variant="h6" fontWeight={700}>
              {t.rich(
                'form.insurancePlanSelection.kvcare_insurance_plan_mix.headline',
                intlTranslationRichHelper
              )}
            </Typography>
            <Stack direction="row"></Stack>
            <Typography>
              {t.rich(
                'form.insurancePlanSelection.kvcare_insurance_plan_mix.body',
                intlTranslationRichHelper
              )}
            </Typography>
            <Stack direction="row"></Stack>
            <Stack
              direction="row"
              gap={2}
              sx={{
                maxWidth: '100%',
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
              }}
            >
              {configuration.options.map((option) => {
                return (
                  <div
                    style={{
                      width: 'auto',
                      minWidth: 200,
                      display: 'flex',
                      flexGrow: 1,
                      cursor: 'pointer',
                    }}
                    key={option.value}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!props.value?.includes(option.value)) {
                        props.onValueChange([
                          //   ...(props.value || []),
                          //   option.value,
                          option.value,
                        ]);
                      } else {
                        props.onValueChange([
                          //   ...(props.value?.filter(
                          //     (it) => it !== option.value
                          //   ) || []),
                        ]);
                      }
                    }}
                  >
                    <KvcCard
                      sx={{
                        width: 'auto',
                        minWidth: 200,
                        display: 'flex',
                        flexGrow: 1,
                        //   cursor: 'pointer',
                        ...(optionsDisabled && {
                          border: 'solid 2px gray',
                          opacity: 0.5,
                          background: 'transparent',
                          cursor: 'not-allowed',
                        }),
                      }}
                      key={option.value}
                      contentAreaSx={{
                        justifyContent: 'left',
                      }}
                    >
                      <Stack
                        direction="column"
                        display="flex"
                        justifyContent="left"
                        gap={1}
                      >
                        {GetIconById(option.icon, {
                          color: optionsDisabled ? 'gray' : 'var(--teal)',
                          fontSize: 42,
                        })}
                        <Typography
                          color={optionsDisabled ? 'gray' : 'var(--teal-dark)'}
                          variant="h6"
                          fontWeight={700}
                        >
                          {t(option.name)}
                        </Typography>

                        <Checkbox
                          disabled={optionsDisabled}
                          disableRipple
                          sx={{
                            width: '20px',
                            p: 0,
                            m: 0,
                            '& .MuiSvgIcon-root': {
                              fontSize: 22,
                              alignItems: 'start !important',
                            },
                            marginLeft: -0.1,
                          }}
                          checked={props.value?.includes(option.value)}
                          onChange={(e) => {
                            e.stopPropagation();
                            // if (e.target.checked === true) {
                            //   props.onValueChange([
                            //     // ...(props.value || []),
                            //     // option.value,
                            //     option.value,
                            //   ]);
                            // } else {
                            //   props.onValueChange([
                            //     // ...(props.value?.filter(
                            //     //   (it) => it !== option.value
                            //     // ) || []),
                            //   ]);
                            // }
                          }}
                        />
                      </Stack>
                    </KvcCard>
                  </div>
                );
              })}
            </Stack>

            <FormControlLabel
              label={configuration.none_option_label}
              control={
                <Checkbox
                  checked={props.value == null}
                  onChange={(e) => {
                    if (e.target.checked === true) {
                      props.onValueChange(null);
                    } else {
                      props.onValueChange([]);
                    }
                  }}
                />
              }
            />
          </Stack>
        </>
      ) : (
        <div>Bad Selector Configuration</div>
      )}
    </>
  );
}

export interface CardSelectorConfiguration {
  options: { name: string; value: any; icon: string }[];
  none_option_label: string;
}
