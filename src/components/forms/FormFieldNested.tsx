import { Stack } from '@mui/material';
import equal from 'fast-deep-equal';
import { useMemo } from 'react';

import {
  type FormFieldConfiguration,
  ParseFormFieldsFromJson,
} from '@/components/forms/domain';
import { Subtitle } from '@/components/forms/Subtitle';
import {
  type CaseWithAllTheRelations,
  type GetCaseGroupWithAllRelationsResponse,
  type NextIntlTranslationHookType,
} from '@/utils/helperTypes';
import { FieldIsVisible, GetListOfInvisibleFields } from '@/utils/json';

import { type KvcContentProps, KvcFormField } from './FormField';

export type FormFieldRootProps = KvcContentProps & {
  fields?: FormFieldConfiguration[];
  t: NextIntlTranslationHookType;
  caseData?: CaseWithAllTheRelations;
  inputDisabled?: boolean;
  caseGroupData?: GetCaseGroupWithAllRelationsResponse;
};

export function FormFieldNested(props: FormFieldRootProps) {
  const subformItems = useMemo(() => {
    return ParseFormFieldsFromJson(
      props.t.raw(`form_configurations.${props.nestedForm}`)
    ) as FormFieldConfiguration[];
  }, [props.nestedForm, props.t]);

  const visibleSubformItems = useMemo(() => {
    return subformItems.filter(
      (it) =>
        FieldIsVisible(props.state?.[props.id], it) === true &&
        props.omitNestedFields?.includes(it.id) !== true
    );
  }, [subformItems, props.state, props.id, props.omitNestedFields]);

  return (
    <>
      {typeof props.label === 'string' && (
        <Stack
          direction="row"
          width="100%"
          display="flex"
          justifyContent="left"
          flexShrink={0}
          flexGrow={1}
          minWidth="100%"
          flexWrap="wrap"
          marginBottom={3}
          sx={{
            flexGrow: 1,
            // background: 'red',
          }}
          className="formW100"
        >
          <Subtitle translationKey={props.label}></Subtitle>
        </Stack>
      )}
      {visibleSubformItems.map((subItem, fieldIndex) => (
        <KvcFormField
          key={fieldIndex}
          element={subItem.element}
          label={subItem.label}
          id={subItem.id}
          state={props.state?.[props.id] ?? {}}
          onChange={(newContentState) => {
            const newPatchedState = {
              ...(props.state?.[props.id] ?? {}),
              [subItem.id]: newContentState,
            };

            if (!equal(props.state?.[props.id], newPatchedState)) {
              const invisibleFields = GetListOfInvisibleFields(
                props.state?.[props.id],
                subformItems
              );
              invisibleFields.forEach((invisibleField) => {
                delete newPatchedState[invisibleField];
              });
            }

            props.onChange(newPatchedState);
          }}
          options={subItem.options}
          showIf={subItem.showIf}
          showIfNot={subItem.showIfNot}
          required={subItem.required}
          optionsPath={subItem.optionsPath}
          defaultValue={subItem.defaultValue}
          showModalOnValueClicked={subItem.showModalOnValueClicked}
          valuesArraysPaths={subItem.valuesArraysPaths}
          autocompleteFreeSolo={subItem.autocompleteFreeSolo}
          useFullWidthOfParent={subItem.useFullWidthOfParent}
          subtitleTranslationKey={subItem.subtitleTranslationKey}
          disabled={subItem.disabled}
          arrayOfForm={subItem.arrayOfForm}
          nestedForm={subItem.nestedForm}
          arrayAddLabel={subItem.arrayAddLabel}
          t={props.t}
          arrayItemLabel={subItem.arrayItemLabel}
          caseData={props.caseData}
          caseGroupData={props.caseGroupData}
          requiredIfKv={subItem.requiredIfKv}
          omitNestedFields={subItem.omitNestedFields}
          inputDisabled={props.inputDisabled}
          inputFieldSuffix={subItem.inputFieldSuffix}
          numberMaxValue={subItem.numberMaxValue}
          numberMinValue={subItem.numberMinValue}
          dateDisableFuture={subItem.dateDisableFuture}
          dateDisablePast={subItem.dateDisablePast}
        ></KvcFormField>
      ))}
      {/* <Divider
        sx={{
          marginTop: 2,
          marginBottom: 2,
          height: '1px',
          //   borderTop: 'solid 1px var(--gray-light)',
          background: 'var(--gray-light)',
          opacity: 0.4,
          width: '100%',
        }}
      /> */}
    </>
  );
}
