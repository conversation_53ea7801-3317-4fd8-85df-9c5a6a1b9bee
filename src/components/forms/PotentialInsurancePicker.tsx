import { Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import React from 'react';

import {
  type CaseFromGetCaseGroupWithAllRelationsResponse,
  type PotentialInsuranceFromCaseGroup,
} from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { PotentialInsuranceCreationListItem } from './PotentialInsuranceCreationListItem';

interface PotentialInsurancePickerProps {
  excludeCaseId: number;
  onClick: (potentialInsurance: PotentialInsuranceFromCaseGroup) => void;
  cases?: CaseFromGetCaseGroupWithAllRelationsResponse[];
}

export function PotentialInsurancePicker(props: PotentialInsurancePickerProps) {
  const t = useTranslations();
  return (
    <Stack direction="column" display="flex" gap={2} sx={{ width: '100%' }}>
      {props.cases
        ?.filter(
          (it) =>
            it.caseId !== props.excludeCaseId &&
            it.potentialInsurances.length > 0
        )
        .map((ccase) => {
          return (
            <React.Fragment key={ccase.caseId}>
              <Typography>
                {t.rich(
                  'form.insurancePlanSelection.insurance_plan_selection.add_insurance_plan_from_other_customer_in_group',
                  intlTranslationRichHelper
                )}
              </Typography>
              <Stack direction="row" gap={2} justifyContent="center">
                <Typography>
                  {t.rich(
                    'form.insurancePlanSelection.insurance_plan_selection.insurance_plans_for_customer_in_group',
                    intlTranslationRichHelper
                  )}
                  :
                </Typography>
                <Typography fontWeight={700}>
                  {`${ccase.customer.firstName} ${ccase.customer.lastName}`}
                </Typography>
              </Stack>
              {ccase.potentialInsurances.map((potentialInsurance) => (
                <React.Fragment key={potentialInsurance.potentialInsuranceId}>
                  <PotentialInsuranceCreationListItem
                    potentialInsurance={potentialInsurance}
                    onAdd={() => {
                      props.onClick(potentialInsurance);
                    }}
                  />
                </React.Fragment>
              ))}
            </React.Fragment>
          );
        })}
    </Stack>
  );
}
