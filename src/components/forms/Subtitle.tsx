import { type SxProps, type Theme, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { type FC, type ReactNode } from 'react';

import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

type Props = {
  translationKey?: string;
  plaintext?: string;
  suffix?: string;
  sx?: SxProps<Theme>;
  children?: ReactNode;
};

export const Subtitle: FC<Props> = ({
  translationKey,
  plaintext,
  suffix,
  sx,
  children,
}) => {
  const t = useTranslations();
  return (
    <Typography
      variant="h6"
      component="h3"
      fontWeight={700}
      color="var(--teal)"
      sx={{
        display: 'flex',
        width: '100%',
        margin: '20px 0 5px',
        flexShrink: 0,
        flexGrow: 1,
        // minWidth: '100%',
        ...sx,
      }}
    >
      {typeof translationKey === 'string' &&
        translationKey.trim().length > 0 &&
        `${t.rich(translationKey, intlTranslationRichHelper)}`}
      {typeof plaintext === 'string' && plaintext}
      {typeof children !== 'undefined' && children}
      {typeof suffix === 'string' && `${suffix}`}
    </Typography>
  );
};
