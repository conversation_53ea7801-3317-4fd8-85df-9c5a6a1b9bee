import CheckIcon from '@mui/icons-material/Check';
import { Button, Stack, Typography } from '@mui/material';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo } from 'react';

import { useTRPC } from '@/trpc/client';
import {
  LoadDataInputConfiguration,
  ValidateFormDataForCaseGroup,
} from '@/utils/formValidation';
import { type CaseNavbarInfo } from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface Props {
  caseId?: number;
  caseNumber?: string;
  groupId?: number;
  caseGroupNumber?: string;
  onBackToDataInputClick?: () => void;
  onBackToCaseList?: () => void;
  onGoToCaseDetails?: () => void;
  onSuccess?: () => void;
  onCaseInfoChanged?: (caseInfo: CaseNavbarInfo) => void;
}

export function CompleteDataInput(props: Props) {
  const t = useTranslations();
  const trpc = useTRPC();
  const {
    data: caseGroupData,
    refetch: refetchCaseGroupData,
    isRefetching: caseGroupDataIsRefetching,
    isLoadingError: caseGroupDataLoadingError,
    isFetching: caseGroupDataIsFetching,
    isLoading: caseGroupDataIsLoading,
  } = useSuspenseQuery(
    trpc.cases.getCaseGroupWithAllRelations.queryOptions({
      caseGroupNumber: props.caseGroupNumber,
      caseNumber: props.caseNumber,
    })
  );

  const caseData = useMemo(() => {
    return caseGroupData.cases.find((it) => it.caseNumber === props.caseNumber);
  }, [caseGroupData]);

  const dataInputConfiguration = useMemo(() => {
    return LoadDataInputConfiguration(t);
  }, [t]);

  const validationOk = useMemo(() => {
    const result = ValidateFormDataForCaseGroup(
      caseGroupData,
      dataInputConfiguration,
      t
    );
    console.log('v', caseGroupData, dataInputConfiguration, result);
    if (result !== null) {
      let complete = true;
      let total = 0;
      let done = 0;

      for (const [userId, validationResults] of Object.entries(result)) {
        for (const [perm, result] of Object.entries(validationResults)) {
          total += result.required.length;
          done += result.required.length - result.bad.length;
          if (result.bad.length > 0) {
            complete = false;
          }
        }
      }
      console.error('validation error', done, 'of', total);
      return complete;
    }
    return false;
  }, [caseGroupData, dataInputConfiguration, t]);

  const {
    mutateAsync: updateCaseStatus,
    isPending: updateCaseStatusIsPending,
    data: updateCaseStatusResult,
  } = useMutation(
    trpc.cases.completeDataInputForCaseInGroup.mutationOptions({
      onSuccess: () => {
        props.onSuccess?.();
      },
      onError: (error) => {
        console.log('error', error);
      },
    })
  );

  useEffect(() => {
    if (typeof props.onCaseInfoChanged !== 'undefined') {
      const caseData = caseGroupData.cases.find(
        (it) => it.caseNumber === props.caseNumber
      );
      if (caseData != null) {
        props.onCaseInfoChanged({
          firstName: caseData.customer.firstName,
          lastName: caseData.customer.lastName,
          caseNumber: caseData.caseNumber,
          caseType: caseData.caseType,
        });
      }
    }
  }, [caseGroupData]);

  if (caseData?.status !== 'DataEntry') {
    return (
      <>
        <>
          <Stack
            direction="column"
            justifyContent="center"
            textAlign="center"
            width="100%"
            display="flex"
            gap={2}
            paddingTop={15}
          >
            {/* <div>{`${validationOk}`}</div> */}
            <CheckIcon
              sx={{
                fontSize: 137,
                color: 'white',
                background: 'var(--teal)',
                borderRadius: '50%',
                marginLeft: 'auto',
                marginRight: 'auto',
              }}
            ></CheckIcon>
            <Typography variant="h5" fontWeight={700}>
              Für diesen Vorgang wurde die Dateneingabe bereits abeschlossen.
            </Typography>

            <Stack direction="row" justifyContent="center">
              <Button
                sx={{ fontWeight: 700 }}
                onClick={() => {
                  props.onGoToCaseDetails?.();
                }}
              >
                Zum Vorgang
              </Button>
            </Stack>
          </Stack>
        </>
      </>
    );
  } else if (updateCaseStatusIsPending) {
    return (
      <>
        <Typography fontSize={42}>
          Dateneingabe wird abgeschlossen...
        </Typography>
      </>
    );
  } else {
    return caseGroupDataIsRefetching ||
      caseGroupDataIsLoading ||
      caseGroupDataIsFetching ? (
      <></>
    ) : (
      <>
        <Stack
          direction="column"
          justifyContent="center"
          textAlign="center"
          width="100%"
          display="flex"
          gap={2}
          paddingTop={15}
        >
          {/* <div>{`${validationOk}`}</div> */}
          <CheckIcon
            sx={{
              fontSize: 137,
              color: 'white',
              background: 'var(--teal)',
              borderRadius: '50%',
              marginLeft: 'auto',
              marginRight: 'auto',
            }}
          ></CheckIcon>
          <Typography variant="h5" fontWeight={700}>
            {t.rich('data_input_complete.headline', intlTranslationRichHelper)}
          </Typography>
          <Typography>
            {t.rich('data_input_complete.body', intlTranslationRichHelper)}
          </Typography>
          <Typography color="red" fontWeight={700}>
            {t.rich('data_input_complete.warning', intlTranslationRichHelper)}
          </Typography>

          <br />

          <Stack direction="row" justifyContent="center">
            <Button
              sx={{ fontWeight: 700 }}
              onClick={async () => {
                await updateCaseStatus({ number: props.caseNumber });
              }}
            >
              {t('data_input_complete.button_start_text')}
            </Button>
          </Stack>

          <br />

          <Typography>
            {t.rich(
              'data_input_complete.or_return_to_data_input',
              intlTranslationRichHelper
            )}
          </Typography>

          <br />

          <Stack direction="row" justifyContent="center">
            <Button
              sx={{ fontWeight: 700 }}
              onClick={() => {
                props.onBackToDataInputClick?.();
              }}
            >
              {t('data_input_complete.button_back_to_data_input_text')}
            </Button>
          </Stack>
        </Stack>
      </>
    );
  }
}
