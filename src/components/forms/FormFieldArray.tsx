import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Box,
  Button,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import equal from 'fast-deep-equal';
import React, { useMemo, useState } from 'react';
import { useDeepCompareEffectNoCheck } from 'use-deep-compare-effect';

import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import {
  type FormFieldConfiguration,
  ParseFormFieldsFromJson,
} from '@/components/forms/domain';
import {
  type CaseWithAllTheRelations,
  type GetCaseGroupWithAllRelationsResponse,
  type NextIntlTranslationHookType,
  RemoveObjectFiedldsByList,
} from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';
import { FieldIsVisible, GetListOfInvisibleFields } from '@/utils/json';

import { FormDivider, FormDividerLight } from './FormDivider';
import { type KvcContentProps, KvcFormField } from './FormField';
// import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';

type FormFieldArrayProps = KvcContentProps & {
  t: NextIntlTranslationHookType;
  caseData?: CaseWithAllTheRelations;
  inputDisabled?: boolean;
  caseGroupData?: GetCaseGroupWithAllRelationsResponse;
};

export function FormFieldArray(props: FormFieldArrayProps) {
  //   const [subformStates, setSubformStates] = useState<any[]>(
  //     typeof props.state[props.id] === 'object' &&
  //       Array.isArray(props.state[props.id])
  //       ? [...props.state[props.id]]
  //       : []
  //   );

  const [subformStates, setSubformStates] = useState<any[]>(() => {
    // console.log('subformStatesinitial', props.state[props.id]);
    const existingArray =
      typeof props.state[props.id] === 'object' &&
      Array.isArray(props.state[props.id])
        ? [...props.state[props.id]]
        : [];
    if (existingArray.length === 0 && props.required === true) {
      return [{}];
    }
    return existingArray;
  });

  const [deleteElementNumber, setDeleteElementNumber] = useState<number | null>(
    null
  );

  useDeepCompareEffectNoCheck(() => {
    const hasChanged = !equal(props.state[props.id], subformStates);
    // console.log(
    //   'useDeepCompareEffectNoCheck',
    //   `'${JSON.stringify(props.state?.[props.id])}'`,
    //   Array.isArray(props.state?.[props.id]),
    //   `'${JSON.stringify(subformStates)}'`,
    //   Array.isArray(subformStates),
    //   'equals:',
    //   !hasChanged,
    //   'maybe?'
    // );
    if (hasChanged) {
      props.onChange(subformStates);
    }
  }, [subformStates]);

  const subformFields = useMemo(() => {
    return ParseFormFieldsFromJson(
      props.t.raw(`form_configurations.${props.arrayOfForm}`)
    ) as FormFieldConfiguration[];
  }, [props.t, props.arrayOfForm]);

  return (
    <>
      {typeof props.label === 'string' && (
        <Typography>
          {props.t.rich(props.label, intlTranslationRichHelper)}
        </Typography>
      )}
      {subformStates.map((subState, arrayItemIndex) => (
        <React.Fragment key={arrayItemIndex}>
          {deleteElementNumber !== null &&
            deleteElementNumber === arrayItemIndex && (
              <Box className={`array-delete-start`}></Box>
            )}
          {
            <>
              {deleteElementNumber !== null &&
                deleteElementNumber === arrayItemIndex && (
                  <Stack
                    direction="column"
                    display="flex"
                    flexWrap="wrap"
                    minWidth="calc(100% - 20px) !important"
                    className="array-delete-ui"
                    textAlign="center"
                  >
                    <FormDivider />
                    <Typography
                      className="array-delete-ui"
                      fontWeight={700}
                      fontSize={25}
                    >
                      {props.t('form.form_array.element_delete_warning')}
                    </Typography>
                    <Stack
                      direction="row"
                      display="flex"
                      flexWrap="wrap"
                      justifyContent="center"
                      gap={2}
                      className="array-delete-ui"
                    >
                      <Button
                        className="array-delete-ui"
                        sx={{ background: 'red', color: 'black' }}
                        onClick={() => {
                          if (subformStates.length === 1) {
                            setSubformStates([]);
                          } else {
                            setSubformStates((prev) =>
                              prev.filter(
                                (_, index) => index !== deleteElementNumber
                              )
                            );
                          }
                          setDeleteElementNumber(null);
                        }}
                        disabled={props.inputDisabled === true}
                      >
                        {props.t('form.form_array.confirm_delete_item')}
                      </Button>
                      <Button
                        className="array-delete-ui"
                        onClick={() => {
                          setDeleteElementNumber(null);
                        }}
                        disabled={props.inputDisabled === true}
                      >
                        {props.t('form.form_array.cancel_delete_item')}
                      </Button>
                    </Stack>
                  </Stack>
                )}

              <Stack
                className="formW100"
                direction="row"
                width="100%"
                display="flex"
                //   justifyContent="right"
              >
                {/* <Subtitle sx={{ width: 'auto' }} suffix={`${arrayItemIndex + 1}`}>
                {props.t.rich(props.label, intlTranslationRichHelper)}
              </Subtitle> */}

                <Typography fontSize={'1.3rem'} fontWeight={700}>
                  {typeof props.arrayItemLabel === 'string'
                    ? props.t.rich(
                        props.arrayItemLabel,
                        intlTranslationRichHelper
                      )
                    : `Element`}
                  {` ${arrayItemIndex + 1}`}
                </Typography>

                {(deleteElementNumber === null ||
                  deleteElementNumber !== arrayItemIndex) && (
                  <Tooltip
                    title={props.t(
                      'form.form_array.remove_item_button_label_suffix'
                    )}
                  >
                    <IconButton
                      sx={{
                        fontSize: 16,
                        fontWeight: 700,
                        color: 'black',
                        marginTop: -1,
                      }}
                      onClick={() => {
                        setDeleteElementNumber(arrayItemIndex);
                      }}
                      disabled={props.inputDisabled === true}
                    >
                      <DeleteIcon
                        className="array-remove-item-button"
                        sx={{
                          color:
                            props.inputDisabled === true
                              ? 'colors.lightGray'
                              : 'primary.main',
                          '&:hover': {
                            color: 'red',
                          },
                        }}
                      />
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            </>
          }

          {subformFields
            .filter(
              (it) =>
                FieldIsVisible(subState, it) === true &&
                props.omitNestedFields?.includes(it.id) !== true
            )
            .map((subField, fieldIndex) => (
              <KvcFormField
                key={`${arrayItemIndex}-${fieldIndex}`}
                element={subField.element}
                label={subField.label}
                id={subField.id}
                state={subState}
                onChange={(newContentState) => {
                  if (!equal(subState, newContentState)) {
                    // Fields that will no longer be visible after this
                    // change need to be deleted from the object using
                    // RemoveObjectFiedldsByList
                    const invisibleFields = GetListOfInvisibleFields(
                      subState,
                      subformFields
                    );
                    setSubformStates((prev) =>
                      prev.map((item, i) =>
                        i === arrayItemIndex
                          ? {
                              ...RemoveObjectFiedldsByList(
                                item,
                                invisibleFields
                              ),
                              [subField.id]: newContentState,
                            }
                          : item
                      )
                    );
                  }
                }}
                options={subField.options}
                showIf={subField.showIf}
                showIfNot={subField.showIfNot}
                required={subField.required}
                optionsPath={subField.optionsPath}
                defaultValue={subField.defaultValue}
                showModalOnValueClicked={subField.showModalOnValueClicked}
                valuesArraysPaths={subField.valuesArraysPaths}
                autocompleteFreeSolo={subField.autocompleteFreeSolo}
                useFullWidthOfParent={subField.useFullWidthOfParent}
                subtitleTranslationKey={subField.subtitleTranslationKey}
                disabled={subField.disabled}
                arrayOfForm={subField.arrayOfForm}
                arrayAddLabel={subField.arrayAddLabel}
                t={props.t}
                requiredIfKv={subField.requiredIfKv}
                arrayItemLabel={subField.arrayItemLabel}
                omitNestedFields={subField.omitNestedFields}
                inputDisabled={props.inputDisabled}
                inputFieldSuffix={subField.inputFieldSuffix}
                numberMaxValue={subField.numberMaxValue}
                numberMinValue={subField.numberMinValue}
                dateDisableFuture={subField.dateDisableFuture}
                dateDisablePast={subField.dateDisablePast}
                caseData={props.caseData}
                caseGroupData={props.caseGroupData}
              ></KvcFormField>
            ))}
          {deleteElementNumber !== null &&
            deleteElementNumber === arrayItemIndex && (
              <Box className={`array-delete-end`}></Box>
            )}
          {subformStates.length > 1 &&
            arrayItemIndex < subformStates.length - 1 && <FormDividerLight />}
        </React.Fragment>
      ))}

      {props.required === true && subformStates.length <= 0 && (
        <input
          style={{ maxHeight: 0, maxWidth: '100%', opacity: 0 }}
          required
        />
      )}

      <Stack
        className="formW100"
        direction="row"
        sx={{ width: '100%' }}
        display="flex"
        justifyContent="center"
      >
        <KvcPlainTextButton
          sx={{
            fontSize: 16,
            fontWeight: 700,
            color: 'black',
            marginTop: 3,
            marginBottom: 3,
            width: 'auto',
            flexGrow: 0,
          }}
          startIcon={
            <AddCircleOutlineIcon
              sx={{
                color:
                  props.inputDisabled === true
                    ? 'colors.lightGray'
                    : 'primary.main',
                marginRight: 1,
              }}
            />
          }
          onClick={() => {
            setSubformStates([...subformStates, {}]);
          }}
        >
          {typeof props.arrayAddLabel === 'string'
            ? props.t(props.arrayAddLabel)
            : props.label}
        </KvcPlainTextButton>
      </Stack>
    </>
  );
}
