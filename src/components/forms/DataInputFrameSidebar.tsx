// import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import CheckIcon from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import {
  Box,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import React from 'react';

import {
  type CaseWithAllTheRelations,
  type DataInputConfiguration,
  type DataInputCurrent,
  DataInputSteps,
  type DataValidationResult,
  type GetCaseGroupWithAllRelationsResponse,
  GetPersonNameFromRawObject,
  type KvcInputStep,
} from '@/utils/helperTypes';

interface Props {
  caseGroup: GetCaseGroupWithAllRelationsResponse;
  caseData: CaseWithAllTheRelations;
  visitedSteps: string[];
  selectedCustomerId: string | number | null;
  setSelectedCustomerId: (customerId: string | number | null) => void;
  onClickAddNewCustomer: () => void;
  deleteCustomerByCustomerId: (customerId: string | number | null) => void;

  showCustomerListOnly?: boolean;
  onEditCustomerClick?: (customerId: number) => void;
  dataInputConfiguration: DataInputConfiguration;

  current: DataInputCurrent;
  setCurrent: (current: DataInputCurrent) => void;
  dataValidationResult: DataValidationResult;
}

export function DataInputFrameSidebar(props: Props) {
  const t = useTranslations();
  return (
    <>
      <Stack direction="column">
        {[
          ...props.caseGroup.customers.filter(
            (c) => c.customerId === props.selectedCustomerId
          ),
          ...props.caseGroup.customers.filter(
            (c) => c.customerId !== props.selectedCustomerId
          ),
        ].map((customerObject, customerIndex) => {
          const isSelectedCustomer =
            customerObject.customerId === props.selectedCustomerId;
          const totalStepsCount = props.dataInputConfiguration.steps.length;
          return (
            <Stack key={`wipCustomers ${customerIndex}`}>
              <Stack direction="column">
                <Stack direction="row">
                  <Typography
                    sx={{
                      cursor: 'pointer',
                      minWidth: '30px',
                      overflowWrap: 'break-word',
                      fontWeight: isSelectedCustomer ? 700 : 400,
                      fontSize: isSelectedCustomer ? '1.3rem' : '1.15rem',
                    }}
                    onClick={() => {
                      if (
                        props.selectedCustomerId === customerObject.customerId
                      ) {
                        props.setSelectedCustomerId(null);
                      } else {
                        props.setSelectedCustomerId(
                          customerObject.customerId || null
                        );
                      }
                    }}
                    color={
                      isSelectedCustomer
                        ? 'var(--teal-dark)'
                        : 'var(--gray-light)'
                    }
                    variant="body1"
                  >
                    {GetPersonNameFromRawObject(customerObject) ??
                      `Person ${customerIndex + 1}`}{' '}
                    {/* {customerObject.customerId} */}
                  </Typography>

                  {isSelectedCustomer && (
                    <>
                      {/* <Tooltip title={t('form.delete_customer')}>
                        <IconButton
                          sx={{
                            marginTop: -1,
                            '&:hover': {
                              color: 'red',
                            },
                          }}
                          onClick={() => {
                            props.deleteCustomerByCustomerId(
                              customerObject.customerId
                            );
                          }}
                        >
                          <RemoveCircleOutlineIcon />
                        </IconButton>
                      </Tooltip> */}
                      <Tooltip title={t('form.edit_customer.headline')}>
                        <IconButton
                          sx={{
                            marginTop: -1,
                          }}
                          onClick={() => {
                            props.onEditCustomerClick?.(
                              customerObject.customerId
                            );
                          }}
                        >
                          <EditIcon
                            sx={{
                              fontSize: 18,
                              color: 'var(--teal)',
                              marginTop: 0.5,
                            }}
                          />
                        </IconButton>
                      </Tooltip>
                    </>
                  )}
                </Stack>

                {/* {isSelectedCustomer && props.showCustomerListOnly !== true && (
                  <>
                    {(props.selectedStep === DataInputSteps.PersonalData ||
                      props.selectedStep ===
                        DataInputSteps.CitizenshipAndProfession) && (
                      <SidebarChapter text={t(`form.basisdaten.headline`)} />
                    )}
                  </>
                )} */}

                {isSelectedCustomer && props.showCustomerListOnly !== true && (
                  <Stack direction="column" gap={2}>
                    {props.dataInputConfiguration.steps.map(
                      (step: KvcInputStep, stepIndex) => {
                        const isSelectedStep =
                          props.current.step.step === step.step;
                        const showCurrentStepAsHeadline =
                          isSelectedStep && (step.substeps?.length ?? 0) > 0;
                        const stepIsComplete =
                          props.dataValidationResult?.[
                            customerObject.customerId
                          ]?.[step.step]?.isComplete === true;

                        return (
                          <React.Fragment key={stepIndex}>
                            {!showCurrentStepAsHeadline && (
                              <SidebarStep
                                key={step.step}
                                number={stepIndex + 1}
                                text={t(
                                  `form.basisdaten.personliche_daten.steps.${step.step}`
                                )}
                                isSelectedStep={isSelectedStep}
                                onClick={() => {
                                  props.setCurrent({
                                    step: step,
                                    substep: step.substeps?.[0],
                                  });
                                }}
                                validationResult={undefined}
                                isComplete={stepIsComplete}
                                customerId={customerObject.customerId}
                                // step={step as DataInputSteps}
                                isVisitedStep={props.visitedSteps?.includes(
                                  step.step
                                )}
                              ></SidebarStep>
                            )}

                            {showCurrentStepAsHeadline && (
                              <SidebarChapter
                                text={t(
                                  `form.basisdaten.personliche_daten.steps.${step.step}`
                                )}
                              ></SidebarChapter>
                            )}

                            {isSelectedStep &&
                              step.substeps?.map((substep, substepIndex) => {
                                let substepIsComplete = false;
                                if (
                                  step.substep === 'formSegmentation' &&
                                  typeof step.substepform === 'string'
                                ) {
                                  substepIsComplete =
                                    props.dataValidationResult?.[
                                      customerObject.customerId
                                    ]?.[step.substepform]?.bad?.includes(
                                      substep.id
                                    ) !== true;
                                } else {
                                  substepIsComplete =
                                    props.dataValidationResult?.[
                                      customerObject.customerId
                                    ]?.[substep.id]?.isComplete === true;
                                }

                                return (
                                  <SidebarStep
                                    key={substepIndex}
                                    number={substepIndex + 1}
                                    text={
                                      substep.label
                                        ? t(substep.label)
                                        : substep.id
                                    }
                                    isComplete={substepIsComplete}
                                    isSelectedStep={
                                      props.current.substep?.id === substep.id
                                    }
                                    onClick={() => {
                                      props.setCurrent({
                                        step: step,
                                        substep: substep,
                                      });
                                    }}
                                    validationResult={undefined}
                                    customerId={0}
                                    isVisitedStep={props.visitedSteps?.includes(
                                      `${step.step}>${substep.id}`
                                    )}
                                  ></SidebarStep>
                                );
                              })}
                            {isSelectedStep &&
                              (step.substeps?.length ?? 0) > 0 &&
                              stepIndex < totalStepsCount - 1 && (
                                <SidebarChapter
                                  text={`Weitere Daten`}
                                ></SidebarChapter>
                              )}
                          </React.Fragment>
                        );
                      }
                    )}
                  </Stack>
                )}
              </Stack>

              <Divider
                sx={{
                  marginTop: 4,
                  marginBottom: 3,
                  borderColor: 'separator.light',
                }}
              />
            </Stack>
          );
        })}
      </Stack>
    </>
  );
}

interface SidebarChapterProps {
  text: string;
}

function SidebarChapter(props: SidebarChapterProps) {
  return (
    <Typography fontSize={'1rem'} fontWeight={700} color="black" marginTop={2}>
      {props.text}
    </Typography>
  );
}

interface SidebarStepProps {
  text: string;
  isSelectedStep: boolean;
  onClick: () => void;
  validationResult: any;
  customerId: number;
  //   step: DataInputSteps;
  number?: number;
  isComplete?: boolean;
  isVisitedStep?: boolean;
}

function SidebarStep(props: SidebarStepProps) {
  let color = 'var(--teal)';
  if (props.isSelectedStep === true) {
    color = 'var(--teal-dark)';
  } else if (props.isComplete !== true && props.isVisitedStep === true) {
    color = 'var(--data-input-step-incomplete)';
  } else if (props.isComplete === true) {
    color = 'var(--teal)';
  } else {
    color = 'gray';
  }

  return (
    <>
      <Stack
        direction="row"
        sx={
          {
            //   marginTop: 0,
          }
        }
      >
        {GetDataInputStepIcon(
          props.isSelectedStep,
          props.isVisitedStep,
          props.isComplete,
          props.number
        )}

        <Typography
          fontSize={14}
          color={color}
          fontWeight={props.isSelectedStep ? 700 : 400}
          sx={{
            cursor: 'pointer',
          }}
          onClick={() => {
            props.onClick();
          }}
        >
          {props.text}
        </Typography>
      </Stack>
    </>
  );
}

function GetStepStatisticsFromValidationResults(
  step: DataInputSteps,
  validationResult: any,
  customerId: any
): string {
  let total: any = undefined;
  let done: any = undefined;
  if (typeof customerId === 'string' || typeof customerId === 'number') {
    const id = `${customerId}`;
    if (step === DataInputSteps.PersonalData) {
      done = (validationResult?.personalData as any)?.[id]?.done ?? '0';

      total = (validationResult?.personalData as any)?.[id]?.total ?? '10';
    } else if (step === DataInputSteps.CitizenshipAndProfession) {
      total =
        (validationResult?.citizenshipAndProfession as any)?.[id]?.done ?? '0';
      done =
        (validationResult?.citizenshipAndProfession as any)?.[id]?.total ??
        '10';
    } else if (step === DataInputSteps.PreviousInsurance) {
      total = (validationResult?.vorversicherung as any)?.[id]?.done ?? '0';
      done = (validationResult?.vorversicherung as any)?.[id]?.total ?? '10';
    }
    if (typeof done !== 'undefined') {
      return ` ${done}/${total}`;
    }
  }
  return '';
}

interface DataInputStepStatusIconProps {
  number?: number;
  color?: string;
  children?: React.ReactElement;
  border?: string;
  background?: string;
  borderWidth?: number;
}

export function DataInputStepStatusIcon(props: DataInputStepStatusIconProps) {
  return (
    <>
      <Box
        sx={{
          border:
            props.borderWidth && props.color
              ? `solid ${props.borderWidth}px ${props.color}`
              : null,
          width: 25,
          height: 25,
          borderRadius: '50%',
          bgcolor: props.background ? props.background : null,
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontWeight: 'bold',
          fontSize: 12,
          marginRight: 1,
          position: 'relative',
          flexShrink: 0,
        }}
      >
        {typeof props.number === 'number' && `${props.number} `}
        {typeof props.children !== 'undefined' && props.children}
      </Box>
    </>
  );
}

export function DataInputStepStatusIncompleteIcon() {
  return (
    <DataInputStepStatusIcon
      borderWidth={2}
      color="var(--data-input-step-incomplete)"
    >
      <PriorityHighIcon
        sx={{
          fontSize: '15px',
          fontWeight: 'bold',
          color: 'var(--data-input-step-incomplete)',
        }}
      />
    </DataInputStepStatusIcon>
  );
}

export function DataInputStepStatusCompleteIcon() {
  return (
    <DataInputStepStatusIcon background="var(--teal)" color="var(--teal)">
      <CheckIcon
        sx={{
          color: 'white',
          fontSize: '17px',
        }}
      />
    </DataInputStepStatusIcon>
  );
}

interface DataInputStepStatusUnvisitedAndIncompleteIconProps {
  number?: number;
}

export function DataInputStepStatusUnvisitedAndIncompleteIcon(
  props: DataInputStepStatusUnvisitedAndIncompleteIconProps
) {
  return (
    <DataInputStepStatusIcon
      background="var(--gray-light)"
      color="white"
      number={props.number}
    ></DataInputStepStatusIcon>
  );
}

interface DataInputStepCurrentIconProps {
  number?: number;
}

export function DataInputStepCurrentIcon(props: DataInputStepCurrentIconProps) {
  return (
    <DataInputStepStatusIcon
      background="var(--teal-dark)"
      color="white"
      number={props.number}
    ></DataInputStepStatusIcon>
  );
}

export function GetDataInputStepIcon(
  isSelectedStep: boolean | undefined,
  isVisitedStep: boolean | undefined,
  isComplete: boolean | undefined,
  number: number | undefined
): React.ReactElement {
  if (isSelectedStep === true) {
    return (
      <DataInputStepCurrentIcon number={number}></DataInputStepCurrentIcon>
    );
  } else if (!isSelectedStep && isVisitedStep === true && !isComplete) {
    return (
      <DataInputStepStatusIncompleteIcon></DataInputStepStatusIncompleteIcon>
    );
  } else if (!isSelectedStep && isVisitedStep === true && isComplete === true) {
    return <DataInputStepStatusCompleteIcon></DataInputStepStatusCompleteIcon>;
  } else if (!isSelectedStep && !isVisitedStep && !isComplete) {
    return (
      <DataInputStepStatusUnvisitedAndIncompleteIcon
        number={number}
      ></DataInputStepStatusUnvisitedAndIncompleteIcon>
    );
  } else {
    return (
      <DataInputStepStatusIncompleteIcon></DataInputStepStatusIncompleteIcon>
    );
  }
}
