import equal from 'fast-deep-equal';
import { useTranslations } from 'next-intl';
import React, { useMemo } from 'react';

import { type KvcFormFieldProps } from '@/components/forms/domain';
import {
  type CaseWithAllTheRelations,
  type GetCaseGroupWithAllRelationsResponse,
} from '@/utils/helperTypes';
import { FieldIsVisible, GetListOfInvisibleFields } from '@/utils/json';

import { KvcFormField } from './FormField';

interface Props {
  fields: KvcFormFieldProps[];
  state: any;
  onChange: (newState: any) => void;
  caseData?: CaseWithAllTheRelations;
  caseGroupData?: GetCaseGroupWithAllRelationsResponse;
  inputDisabled?: boolean;
}

export function FormFieldRoot(props: Props) {
  const t = useTranslations();

  const visibleSubformItems = useMemo(() => {
    return props.fields?.filter((it) => FieldIsVisible(props.state, it));
  }, [props.fields, props.state]);

  return (
    <>
      {/* <div>I am root.</div> */}
      {/* <div>
        {props.fields.length}/{visibleSubformItems.length}
      </div> */}
      {/* <div>{JSON.stringify(props.fields)}</div> */}
      {visibleSubformItems?.map((subItem, fieldIndex) => (
        <React.Fragment key={fieldIndex}>
          {/* <div>
              {subItem.id} - {subItem.element}
            </div> */}
          <KvcFormField
            key={fieldIndex}
            element={subItem.element}
            label={subItem.label}
            id={subItem.id}
            state={props.state ?? {}}
            onChange={(newContentState) => {
              console.log('root newContentState', newContentState);
              const newPatchedState = {
                ...(props.state ?? {}),
                [subItem.id]: newContentState,
              };

              if (!equal(props.state, newPatchedState)) {
                const invisibleFields = GetListOfInvisibleFields(
                  props.state,
                  props.fields
                );
                invisibleFields.forEach((invisibleField) => {
                  delete newPatchedState[invisibleField];
                });
              }

              props.onChange(newPatchedState);
            }}
            options={subItem.options}
            showIf={subItem.showIf}
            showIfNot={subItem.showIfNot}
            required={subItem.required}
            optionsPath={subItem.optionsPath}
            defaultValue={subItem.defaultValue}
            showModalOnValueClicked={subItem.showModalOnValueClicked}
            valuesArraysPaths={subItem.valuesArraysPaths}
            autocompleteFreeSolo={subItem.autocompleteFreeSolo}
            useFullWidthOfParent={subItem.useFullWidthOfParent}
            subtitleTranslationKey={subItem.subtitleTranslationKey}
            disabled={subItem.disabled}
            arrayOfForm={subItem.arrayOfForm}
            nestedForm={subItem.nestedForm}
            arrayAddLabel={subItem.arrayAddLabel}
            t={t}
            arrayItemLabel={subItem.arrayItemLabel}
            caseData={props.caseData}
            caseGroupData={props.caseGroupData}
            requiredIfKv={subItem.requiredIfKv}
            omitNestedFields={subItem.omitNestedFields}
            inputDisabled={props.inputDisabled}
            inputFieldSuffix={subItem.inputFieldSuffix}
            numberMaxValue={subItem.numberMaxValue}
            numberMinValue={subItem.numberMinValue}
            dateDisableFuture={subItem.dateDisableFuture}
            dateDisablePast={subItem.dateDisablePast}
          ></KvcFormField>
        </React.Fragment>
      ))}
    </>
  );
}
