'use client';
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  type SxProps,
  TextField,
  type Theme,
  Typography,
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useMutation } from '@tanstack/react-query';
import dayjs, { type Dayjs } from 'dayjs';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { type FormEvent, useState } from 'react';

import { KvcSecondaryButton } from '@/components/buttons/KvcSecondaryButton';
import { type CustomerCreateWithCaseGroupResult } from '@/modules/customer/types/customer-types';
import { useTRPC } from '@/trpc/client';
import { CaseGroupRelationType } from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { classes } from './cssClasses';

interface Props {
  sx?: SxProps<Theme>;
  caseGroupId?: number;
  onCancelClick?: () => void;
  onSuccess?: () => void;
}

export function CustomerCreateForm(props: Props) {
  const t = useTranslations();
  const router = useRouter();
  const trpc = useTRPC();

  // Form state
  const [kundennummer, setKundennummer] = useState<string | undefined>(
    undefined
  );
  const [firstName, setFirstName] = useState<string | undefined>(undefined);
  const [lastName, setLastName] = useState<string | undefined>(undefined);
  const [email, setEmail] = useState<string | undefined>(undefined);
  const [dateOfBirth, setDateOfBirth] = useState<Date | null>(null);
  const [relationToGroup, setRelationToGroup] =
    useState<CaseGroupRelationType | null>(null);
  const relationTypes: string[] = Object.values(CaseGroupRelationType);

  // tRPC mutation
  const {
    mutateAsync: createCustomerWithCaseGroup,
    isPending: createCustomerWithCaseGroupPending,
  } = useMutation(
    trpc.customers.createWithCaseGroup.mutationOptions({
      onSuccess: (data: CustomerCreateWithCaseGroupResult) => {
        // Redirect to dateneingabe page with case number
        router.push(`/dateneingabe/case/${data.caseNumber}`);
      },
      onError: (error: any) => {
        alert(`Error: ${error.message}`);
      },
    })
  );

  const {
    mutateAsync: createCustomerInCaseGroup,
    isPending: createCustomerInCaseGroupPending,
  } = useMutation(
    trpc.customers.createInCaseGroup.mutationOptions({
      onSuccess: (data: any) => {
        console.log('CustomerCreateForm', data);
        props.onSuccess?.();
      },
      onError: (error: any) => {
        alert(`Error: ${error.message}`);
      },
    })
  );

  async function onCreateNewCustomer(
    event: FormEvent<HTMLFormElement>
  ): Promise<void> {
    event.preventDefault();

    if (!firstName || !lastName) {
      alert('Please fill in first name and last name');
      return;
    }

    if (typeof props.caseGroupId === 'number') {
      await createCustomerInCaseGroup({
        caseGroupId: props.caseGroupId,
        caseType: 'Risiko-Voranfrage',
        customerData: {
          firstName: firstName,
          lastName: lastName,
          dateOfBirth: dateOfBirth,
          email: email,
        },
        relationType: `spouse`,
        questionnaires: [
          //  Default questionnaires always included.
          {
            formId: 'citizenshipAndProfession',
            type: 'citizenshipAndProfession',
          },
          {
            formId: 'previousInsurance',
            type: 'previousInsurance',
          },
          {
            formId: 'healthChecklist',
            type: 'healthChecklist',
          },
        ],
      });
    } else {
      if (!email) {
        alert('Please fill in email address (required for paying customers)');
        return;
      }

      //   await createCustomerWithCase({
      await createCustomerWithCaseGroup({
        firstName,
        lastName,
        email,
        dateOfBirth: dateOfBirth, // Default date
        caseType: 'Risiko-Voranfrage',
        caseStatus: 'DataEntry',
        // relationType: RelationType.paying,
        relationType: 'paying',
        questionnaires: [
          //  Default questionnaires always included.
          {
            formId: 'citizenshipAndProfession',
            type: 'citizenshipAndProfession',
          },
          {
            formId: 'previousInsurance',
            type: 'previousInsurance',
          },
          {
            formId: 'healthChecklist',
            type: 'healthChecklist',
          },
        ],
      });
    }
  }

  return (
    <Stack
      direction="column"
      display="flex"
      justifyContent="center"
      flexGrow={1}
      textAlign="center"
      sx={{
        padding: 5,
        width: '50%',
        ...props.sx,
      }}
    >
      <form
        onSubmit={onCreateNewCustomer}
        style={{
          gap: '20px',
          display: 'flex',
          justifyContent: 'center',
          flexGrow: 1,
          textAlign: 'center',
          flexDirection: 'column',
        }}
      >
        <Typography
          color={'var(--teal)'}
          fontWeight={700}
          variant="h5"
          textAlign="center"
        >
          {t.rich('form.new_customer.headline', intlTranslationRichHelper)}
        </Typography>

        <TextField
          required
          value={firstName}
          onChange={(e) => {
            setFirstName(e.target.value);
          }}
          label={t('form.basisdaten.personliche_daten.stammdaten.first_name')}
        />

        <TextField
          required
          value={lastName}
          onChange={(e) => {
            setLastName(e.target.value);
          }}
          label={t('form.basisdaten.personliche_daten.stammdaten.last_name')}
        />

        <LocalizationProvider
          dateAdapter={AdapterDayjs}
          adapterLocale="de"
          localeText={{
            fieldYearPlaceholder: () => 'JJJJ',
            fieldMonthPlaceholder: () => 'MM',
            fieldDayPlaceholder: () => 'TT',
          }}
        >
          <DatePicker
            className={classes.formField}
            label={t(
              'form.basisdaten.personliche_daten.stammdaten.birthdate_DD_MM_YYYY'
            )}
            disableFuture
            value={dateOfBirth === null ? null : dayjs(dateOfBirth)}
            onChange={(newDateOfBirth: Dayjs | null) => {
              setDateOfBirth(newDateOfBirth?.toDate() || null);
            }}
            slotProps={{
              textField: {
                required: true,
              },
            }}
          />
        </LocalizationProvider>

        <FormControl
          variant="outlined"
          className={classes.formField}
          required
          // fullWidth={useFullWidthOfParent === true}
        >
          <InputLabel htmlFor={'relationType'}>Person ist...</InputLabel>
          <Select
            id="relationType"
            label={'Person ist...'}
            value={relationToGroup}
            onChange={(e) => {
              console.log('dropdown value change', e.target.value);
              setRelationToGroup(
                e.target.value !== null
                  ? (e.target.value as CaseGroupRelationType)
                  : null
              );
            }}
            required
            slotProps={{
              input: {
                required: true,
              },
            }}
          >
            {relationTypes.map((option) => (
              <MenuItem
                key={option}
                className={classes.dropdownOption}
                value={option}
              >
                {t(`case_relation_types.${option}`)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <TextField
          required={typeof props.caseGroupId !== 'number'}
          type="email"
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
          }}
          label="Email Address"
        />

        <TextField
          label={t('form.new_customer.customer_number')}
          value={kundennummer}
          onChange={(e) => {
            setKundennummer(e.target.value);
          }}
        />

        <Stack
          direction="row"
          display="flex"
          flexWrap="wrap"
          justifyContent="center"
          sx={{
            marginTop: 'auto',
          }}
          gap={1}
        >
          {typeof props.onCancelClick === 'function' && (
            <KvcSecondaryButton
              onClick={props.onCancelClick}
              sx={{ fontWeight: 700 }}
              disabled={
                createCustomerInCaseGroupPending ||
                createCustomerWithCaseGroupPending
              }
            >
              {t.rich(
                'form.new_customer.cancel_create_new_customer_button',
                intlTranslationRichHelper
              )}
            </KvcSecondaryButton>
          )}
          <Button
            type="submit"
            sx={{ fontWeight: 700 }}
            disabled={
              createCustomerInCaseGroupPending ||
              createCustomerWithCaseGroupPending
            }
          >
            {createCustomerInCaseGroupPending ||
            createCustomerWithCaseGroupPending
              ? 'Creating...'
              : t.rich(
                  'form.new_customer.create_new_customer_button',
                  intlTranslationRichHelper
                )}
          </Button>
        </Stack>
      </form>
    </Stack>
  );
}
