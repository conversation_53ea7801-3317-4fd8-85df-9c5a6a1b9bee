import 'dayjs/locale/de'; //  Required to show date in a German format.

import {
  Autocomplete,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  type TextFieldProps,
  Typography,
} from '@mui/material';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { type Dayjs } from 'dayjs';
import deLocale from 'i18n-iso-countries/langs/de.json';
import { type FC, useMemo } from 'react';

import {
  type KvcFormFieldProps,
  KvcInputFieldType,
} from '@/components/forms/domain';
import { FormFieldArray } from '@/components/forms/FormFieldArray';
import { FormFieldNested } from '@/components/forms/FormFieldNested';
import { Subtitle } from '@/components/forms/Subtitle';
import {
  type CaseWithAllTheRelations,
  type GetCaseGroupWithAllRelationsResponse,
  type NextIntlTranslationHookType,
} from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';
import { JsonUtils } from '@/utils/json';

import { CardSelector } from './CardSelector';
import { classes } from './cssClasses';
import { FormDivider } from './FormDivider';
import { PotentialInsuranceCreationList } from './PotentialInsuranceCreationList';

export type KvcContentProps = KvcFormFieldProps & {
  state: any;
  onChange: (newValue: any) => void;
  t: NextIntlTranslationHookType;
  caseData?: CaseWithAllTheRelations;
  inputDisabled?: boolean;
  caseGroupData?: GetCaseGroupWithAllRelationsResponse;
};

// THE GOOD ONE
export const KvcFormField: FC<KvcContentProps> = ({
  element,
  label,
  id,
  state,
  onChange,
  options,
  showIf,
  showIfNot,
  required,
  optionsPath,
  defaultValue,
  showModalOnValueClicked,
  valuesArraysPaths,
  autocompleteFreeSolo,
  useFullWidthOfParent,
  subtitleTranslationKey,
  disabled,
  arrayOfForm,
  nestedForm,
  arrayAddLabel,
  arrayItemLabel,
  t,
  caseData,
  caseGroupData,
  requiredIfKv,
  omitNestedFields,
  inputDisabled,
  inputFieldSuffix,
  numberMaxValue,
  numberMinValue,
  dateDisableFuture,
  dateDisablePast,
}) => {
  const isRequired = useMemo(() => {
    if (typeof requiredIfKv === 'object') {
      if (
        typeof state === 'object' &&
        Object.entries(requiredIfKv).length > 0
      ) {
        for (const [k, v] of Object.entries(requiredIfKv)) {
          if (typeof k === 'string' && k.trim().length > 0 && state[k] !== v) {
            return false;
          }
        }
        return true;
      }
    } else if (required === true) {
      return true;
    }
    return false;
  }, [required, requiredIfKv, state]);

  if (disabled === true) {
    return null;
  }

  if (element === KvcInputFieldType.Nested && typeof nestedForm === 'string') {
    return (
      <FormFieldNested
        element={element}
        label={label}
        id={id}
        state={state}
        onChange={(newState) => {
          console.log('nestedonchange 0', newState);
          onChange(newState);
        }}
        options={options}
        showIf={showIf}
        showIfNot={showIfNot}
        required={required}
        optionsPath={optionsPath}
        defaultValue={defaultValue}
        showModalOnValueClicked={showModalOnValueClicked}
        valuesArraysPaths={valuesArraysPaths}
        autocompleteFreeSolo={autocompleteFreeSolo}
        useFullWidthOfParent={useFullWidthOfParent}
        subtitleTranslationKey={subtitleTranslationKey}
        disabled={disabled}
        arrayOfForm={arrayOfForm}
        nestedForm={nestedForm}
        arrayAddLabel={arrayAddLabel}
        t={t}
        arrayItemLabel={arrayItemLabel}
        requiredIfKv={requiredIfKv}
        omitNestedFields={omitNestedFields}
        inputDisabled={inputDisabled}
        inputFieldSuffix={inputFieldSuffix}
        numberMaxValue={numberMaxValue}
        numberMinValue={numberMinValue}
        dateDisableFuture={dateDisableFuture}
        dateDisablePast={dateDisablePast}
        caseData={caseData}
        caseGroupData={caseGroupData}
      ></FormFieldNested>
    );
  }

  if (
    element === KvcInputFieldType.ArrayOf &&
    typeof arrayOfForm === 'string'
  ) {
    return (
      <FormFieldArray
        element={element}
        label={label}
        id={id}
        state={state}
        onChange={onChange}
        options={options}
        showIf={showIf}
        showIfNot={showIfNot}
        required={required}
        optionsPath={optionsPath}
        defaultValue={defaultValue}
        showModalOnValueClicked={showModalOnValueClicked}
        valuesArraysPaths={valuesArraysPaths}
        autocompleteFreeSolo={autocompleteFreeSolo}
        useFullWidthOfParent={useFullWidthOfParent}
        subtitleTranslationKey={subtitleTranslationKey}
        disabled={disabled}
        arrayOfForm={arrayOfForm}
        nestedForm={nestedForm}
        arrayAddLabel={arrayAddLabel}
        t={t}
        arrayItemLabel={arrayItemLabel}
        requiredIfKv={requiredIfKv}
        omitNestedFields={omitNestedFields}
        inputDisabled={inputDisabled}
        inputFieldSuffix={inputFieldSuffix}
        numberMaxValue={numberMaxValue}
        numberMinValue={numberMinValue}
        dateDisableFuture={dateDisableFuture}
        dateDisablePast={dateDisablePast}
        caseData={caseData}
        caseGroupData={caseGroupData}
      ></FormFieldArray>
    );
  }

  if (
    element === KvcInputFieldType.Database &&
    typeof caseData !== 'undefined'
  ) {
    return (
      <PotentialInsuranceCreationList
        caseData={caseData}
        caseGroupData={caseGroupData}
        required={isRequired}
        sx={{ width: '100%', minWidth: '100% !important' }}
      />
    );
  }

  // required && typeof propsLabel === 'string' ? `${propsLabel} *` : propsLabel;
  const additionalCssClassesString: string = useFullWidthOfParent
    ? ' formW100'
    : '';

  const formCssClasses: string =
    'FancyForm__formField' + useFullWidthOfParent ? ' formW100' : '';

  if (element === KvcInputFieldType.Subtitle) {
    if (
      (typeof subtitleTranslationKey === 'string' &&
        subtitleTranslationKey.trim().length > 0) ||
      (typeof label === 'string' && label.trim().length > 0)
    ) {
      return <Subtitle translationKey={label ?? subtitleTranslationKey} />;
    } else {
      return null;
    }
  }

  if (element === KvcInputFieldType.Subheadline) {
    if (typeof label === 'string' && label.trim().length > 0) {
      return (
        <Typography
          fontSize={'1.3rem'}
          fontWeight={700}
          sx={{
            minWidth: useFullWidthOfParent === true ? '100%' : null,
            // marginTop: 1,
          }}
        >
          {t(label)}
        </Typography>
      );
    } else {
      return null;
    }
  }

  if (element === KvcInputFieldType.Typography) {
    if (typeof label === 'string') {
      return (
        <Typography sx={{ minWidth: useFullWidthOfParent ? '100%' : null }}>
          {t.rich(label, intlTranslationRichHelper)}
        </Typography>
      );
    }
    return null;
  }

  if (element === KvcInputFieldType.PlainSubheadline) {
    if (typeof label === 'string') {
      return (
        <Typography
          sx={{
            minWidth: useFullWidthOfParent ? '100%' : null,
            fontSize: '1.3rem',
          }}
        >
          {t.rich(label, intlTranslationRichHelper)}
        </Typography>
      );
    }
    return null;
  }

  if (element === KvcInputFieldType.H6) {
    if (typeof label === 'string') {
      return (
        <Typography
          variant="h6"
          component="h6"
          fontWeight={700}
          sx={{
            marginTop: 5,
            minWidth: useFullWidthOfParent ? '100%' : null,
          }}
        >
          {t.rich(label, intlTranslationRichHelper)}
        </Typography>
      );
    }
    return null;
  }

  if (element === KvcInputFieldType.Divider) {
    return <FormDivider />;
  }

  if (element === KvcInputFieldType.Space) {
    return (
      <div style={{ marginTop: '46px', height: 1, minWidth: '100%' }}></div>
    );
  }

  let labelValue = typeof label === 'string' ? label : null;
  try {
    if (labelValue !== null) {
      labelValue = t(labelValue);
    }
  } catch (_) {
    /* empty */
  }

  if (element === KvcInputFieldType.Checkbox) {
    return (
      <FormControlLabel
        className={classes.formField}
        label={labelValue}
        control={
          <Checkbox
            name={id}
            checked={state[id] ?? false}
            onChange={(e) => onChange(e.target.checked)}
            disabled={inputDisabled === true}
          />
        }
      />
    );
  }

  if (element === KvcInputFieldType.CardSelector) {
    return (
      <CardSelector
        configPath={optionsPath || ''}
        value={typeof state[id] === 'undefined' ? [] : state[id]}
        onValueChange={(newValue: any) => {
          onChange(newValue);
        }}
        sx={{ width: '100%' }}
      />
    );
  }

  if (element === KvcInputFieldType.BooleanDropdown) {
    const value = id in state ? (state[id] ? 1 : 0) : '';

    return (
      <FormControl variant="outlined" className={formCssClasses}>
        <InputLabel htmlFor={id}>{labelValue}</InputLabel>
        <Select
          id={id}
          label={labelValue}
          name={id}
          value={value}
          onChange={(e) => onChange(Boolean(e.target.value))}
          disabled={inputDisabled === true}
        >
          <MenuItem value={1} className={classes.dropdownOption}>
            Ja
          </MenuItem>
          <MenuItem value={0} className={classes.dropdownOption}>
            Nein
          </MenuItem>
        </Select>
      </FormControl>
    );
  }
  if (element === KvcInputFieldType.Dropdown) {
    let optionsByKey: any = {};
    let defaultValueKey: string | undefined = undefined;
    if (optionsPath) {
      const rawArray = t.raw(optionsPath);
      if (typeof rawArray !== 'string') {
        optionsByKey = t.raw(optionsPath);
      }
    }

    JsonUtils.ProcessValuesArraysPathsAppendToObjectAsKeyValue(
      valuesArraysPaths,
      optionsByKey,
      t
    );
    if (typeof defaultValue === 'string') {
      const rawDefaultValue = t.raw(defaultValue);
      defaultValueKey = rawDefaultValue;
      if (
        typeof rawDefaultValue === 'string' &&
        rawDefaultValue !== defaultValue &&
        typeof state[id] === 'undefined'
      ) {
        onChange(defaultValueKey);
      }
    }

    return (
      <FormControl
        variant="outlined"
        className={classes.formField}
        required={required === true}
        fullWidth={useFullWidthOfParent === true}
      >
        <InputLabel htmlFor={id}>{labelValue}</InputLabel>
        <Select
          id={id}
          label={labelValue}
          name={id}
          value={state[id] ?? (defaultValueKey ? defaultValueKey : '')}
          onChange={(e) => {
            // console.log('dropdown value change', e.target.value);
            onChange(e.target.value);
          }}
          required={required === true}
          slotProps={{
            input: {
              required: required === true,
            },
          }}
          disabled={inputDisabled === true}
        >
          {options?.map((option) => (
            <MenuItem
              key={option.value}
              className={classes.dropdownOption}
              value={option.value}
            >
              {option.label}
            </MenuItem>
          ))}
          {optionsByKey &&
            Object.entries(optionsByKey).map(([key, value]) => {
              return (
                <MenuItem
                  key={`${key}`}
                  className={classes.dropdownOption}
                  value={`${value}`}
                >
                  {`${value}`}
                </MenuItem>
              );
            })}
        </Select>
      </FormControl>
    );
  }

  if (element === KvcInputFieldType.Datetime) {
    return (
      <DateTimePicker
        className={classes.formField}
        label={labelValue}
        disableFuture
        value={state[id] ? dayjs(state[id]) : null}
        onChange={(newValue: Dayjs | null) => onChange(newValue?.toISOString())}
        disabled={inputDisabled === true}
      />
    );
  }

  if (element === KvcInputFieldType.Year) {
    return (
      <LocalizationProvider
        dateAdapter={AdapterDayjs}
        adapterLocale="de"
        localeText={{
          fieldYearPlaceholder: () => 'JJJJ',
          fieldMonthPlaceholder: () => 'MM',
          fieldDayPlaceholder: () => 'TT',
        }}
      >
        <DatePicker
          className={classes.formField}
          views={['year']}
          openTo="year"
          label={labelValue}
          disableFuture={dateDisableFuture === true}
          disablePast={dateDisablePast === true}
          slotProps={{
            textField: {
              required: required === true,
            },
          }}
          value={
            typeof state[id] === 'string' && state[id].length > 0
              ? dayjs(`${state[id]}-01-01`)
              : null
          }
          onChange={(newValue: Dayjs | null) => {
            if (!newValue || !newValue.isValid()) {
              onChange(null);
              return;
            }
            onChange(`${newValue?.year()}`);
          }}
          disabled={inputDisabled === true}
        />
      </LocalizationProvider>
    );
  }

  if (element === KvcInputFieldType.MonthYear) {
    return (
      <LocalizationProvider
        dateAdapter={AdapterDayjs}
        adapterLocale="de"
        localeText={{
          fieldYearPlaceholder: () => 'JJJJ',
          fieldMonthPlaceholder: () => 'MM',
          fieldDayPlaceholder: () => 'TT',
        }}
      >
        <DatePicker
          className={classes.formField}
          views={['year', 'month']}
          openTo="year"
          label={labelValue}
          disableFuture={dateDisableFuture === true}
          disablePast={dateDisablePast === true}
          slotProps={{
            textField: {
              required: required === true,
            },
          }}
          value={
            typeof state[id] === 'string' && state[id].length > 0
              ? dayjs(`${state[id]}-01`)
              : null
          }
          onChange={(newValue: Dayjs | null) => {
            if (!newValue || !newValue.isValid()) {
              onChange(null);
              return;
            }
            onChange(`${newValue?.year()}-${newValue?.month()}`);
          }}
          disabled={inputDisabled === true}
        />
      </LocalizationProvider>
    );
  }

  // Country is a separate type because we are pulling the country list from
  // a specific dependency.
  if (element === KvcInputFieldType.Country) {
    return (
      <Autocomplete
        className={classes.formField}
        options={Object.values(
          Object.fromEntries(
            Object.entries(deLocale.countries)
              .filter(([code]) => code !== 'DE')
              .map(([code, value]) => [
                code,
                Array.isArray(value) ? value[0] : value,
              ])
          )
        )}
        value={state[id] ?? ''}
        onChange={(_, newValue) => onChange(newValue)}
        renderInput={(params) => (
          <TextField
            {...params}
            label={labelValue}
            required={required === true}
          />
        )}
        disabled={inputDisabled === true}
      />
    );
  }

  if (element === KvcInputFieldType.Autocomplete) {
    let autocompleteOptions = [];
    if (typeof optionsPath === 'string' && optionsPath.length > 0) {
      const rawOptionsArray = t.raw(optionsPath);
      if (Array.isArray(rawOptionsArray)) {
        autocompleteOptions = rawOptionsArray;
      }
    }

    autocompleteOptions = [
      ...autocompleteOptions,
      ...JsonUtils.ProcessValuesArraysPathsAppendToStringArray(
        valuesArraysPaths,
        t
      ),
    ];

    return (
      <Autocomplete
        className={`${classes.formField} ${additionalCssClassesString}`}
        freeSolo={autocompleteFreeSolo === true}
        options={autocompleteOptions}
        value={state[id] ?? ''}
        onChange={(_, newValue) => onChange(newValue)}
        renderInput={(params) => (
          <TextField
            {...params}
            label={labelValue}
            required={required === true}
          />
        )}
        disabled={inputDisabled === true}
      />
    );
  }

  if (element === KvcInputFieldType.Radio) {
    const rawOptions = t.raw('rawKv.boolean');
    if (typeof rawOptions !== 'string') {
      return (
        <FormControl
          sx={{ marginTop: '0px !important' }}
          fullWidth={useFullWidthOfParent === true}
          className={classes.formField + additionalCssClassesString}
          required={required === true}
          disabled={inputDisabled === true}
        >
          {typeof labelValue === 'string' && labelValue.trim().length > 0 && (
            <FormLabel sx={{ marginTop: 3 }}>{labelValue}</FormLabel>
          )}
          <RadioGroup
            row
            name="radio1"
            defaultValue={
              typeof defaultValue === 'boolean' ? defaultValue : undefined
            }
            value={state[id] ?? null}
            onChange={(e, newValue) => {
              onChange(newValue === 'true');
            }}
          >
            <FormControlLabel
              value={'true'}
              control={<Radio />}
              label={
                typeof rawOptions['true'] === 'string'
                  ? rawOptions['true']
                  : 'Ja?'
              }
              onClick={
                inputDisabled !== true &&
                typeof showModalOnValueClicked === 'object' &&
                showModalOnValueClicked.value === true
                  ? () => {
                      const url = new URL((window as any).location);
                      url.searchParams.set('showmodal', 'warning_no_offers');
                      window.history.pushState({}, '', url);
                    }
                  : undefined
              }
            />
            <FormControlLabel
              value={'false'}
              control={<Radio />}
              label={
                typeof rawOptions['true'] === 'string'
                  ? rawOptions['false']
                  : 'Nein?'
              }
              onClick={
                inputDisabled !== true &&
                typeof showModalOnValueClicked === 'object' &&
                showModalOnValueClicked.value === false
                  ? () => {
                      const url = new URL((window as any).location);
                      url.searchParams.set('showmodal', 'warning_no_offers');
                      window.history.pushState({}, '', url);
                    }
                  : undefined
              }
            />
          </RadioGroup>
        </FormControl>
      );
    }
  }

  let additionalProps: Partial<TextFieldProps> = {};
  if (element === KvcInputFieldType.Textarea) {
    additionalProps = {
      multiline: true,
      minRows: 3,
    };
  }

  //  Need to handle date that needs to be returned as string or as Date().
  if (
    element === KvcInputFieldType.Date ||
    element === KvcInputFieldType.DateString
  ) {
    return (
      <>
        <LocalizationProvider
          dateAdapter={AdapterDayjs}
          adapterLocale="de"
          localeText={{
            fieldYearPlaceholder: () => 'JJJJ',
            fieldMonthPlaceholder: () => 'MM',
            fieldDayPlaceholder: () => 'TT',
          }}
        >
          <DatePicker
            className={classes.formField}
            label={labelValue}
            disableFuture={dateDisableFuture === true}
            disablePast={dateDisablePast === true}
            value={
              (typeof state[id] === 'string' && state[id].length > 0) ||
              (typeof state[id] === 'object' && state[id] instanceof Date)
                ? dayjs(state[id])
                : null
            }
            onChange={(newValue: Dayjs | null) => {
              if (!newValue || !newValue.isValid()) {
                onChange(null);
                return;
              }
              if (element === KvcInputFieldType.DateString) {
                onChange(`${newValue?.toISOString()}`);
                return;
              }
              onChange(newValue?.toDate());
            }}
            slotProps={{
              textField: {
                required: required === true,
              },
            }}
            disabled={inputDisabled === true}
          />
        </LocalizationProvider>
      </>
    );
  }

  if (element === KvcInputFieldType.Number) {
    additionalProps = {
      type: 'number',
      // This may be required to make it number only.
      // inputProps: {
      //   pattern: '^[0-9]*\\.?[0-9]*$',
      //   inputMode: 'decimal',
      // },
    };
  }

  if (element === KvcInputFieldType.Price) {
    additionalProps = {
      type: 'number',
      slotProps: {
        input: {
          endAdornment: <InputAdornment position="end">€</InputAdornment>,
        },
      },
    };
  }

  if (element === KvcInputFieldType.Email) {
    additionalProps = {
      type: 'email',
    };
  }

  return (
    <TextField
      className={classes.formField}
      label={labelValue}
      name={id}
      value={state[id] ?? ''}
      onChange={(e) => onChange(e.target.value)}
      fullWidth={useFullWidthOfParent === true}
      {...additionalProps}
      required={required === true}
      sx={{
        minWidth:
          useFullWidthOfParent === true ? 'calc(100% - 20px) !important' : null,
        flexGrow: 1,
      }}
      disabled={inputDisabled === true}
      slotProps={{
        input: {
          endAdornment:
            typeof inputFieldSuffix === 'string' ? (
              <InputAdornment position="end">
                {t(inputFieldSuffix)}
              </InputAdornment>
            ) : null,
          inputProps: {
            min: typeof numberMinValue === 'number' ? numberMinValue : null,
            max: typeof numberMaxValue === 'number' ? numberMaxValue : null,
          },
        },
      }}
    />
  );
};
