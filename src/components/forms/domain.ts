import { type ReactElement } from 'react';

export const divider = {
  element: 'divider',
} as const;

export enum KvcInputFieldType {
  Text = 'text',
  Textarea = 'textarea',
  Date = 'date',
  DateString = 'datestring',
  Datetime = 'datetime',
  Dropdown = 'dropdown',
  BooleanDropdown = 'boolean-dropdown',
  Checkbox = 'checkbox',
  Price = 'price',
  Number = 'number',
  Radio = 'radio',
  Year = 'year',
  Autocomplete = 'autocomplete',
  Country = 'country',
  Email = 'email',
  Subtitle = 'subtitle',
  ArrayOf = 'arrayof',
  Nested = 'nested',
  Divider = 'divider',
  Typography = 'typography',
  Database = 'database',
  Dataset = 'dataset',
  CardSelector = 'cardselector',
  H6 = 'h6',
  Subheadline = 'subheadline',
  PlainSubheadline = 'plainsubheadline',
  Space = 'space',
  MonthYear = 'monthyear',
}

const elementsDontRequiredIds = [
  KvcInputFieldType.Subtitle,
  KvcInputFieldType.Divider,
  KvcInputFieldType.Typography,
  KvcInputFieldType.H6,
  KvcInputFieldType.Subheadline,
  KvcInputFieldType.PlainSubheadline,
  KvcInputFieldType.Space,
];

export enum DatasetConditionType {
  HasObjectWithKv = 'hasObjectWithKv',
}

export type DatasetCondition = {
  dataset: string;
  dataType: 'object' | 'array';
  condition: 'hasObjectWithKv' | 'hasObject';
  key: string;
  value: any;
};

export type KvcFormFieldProps = {
  element: KvcInputFieldType;
  label?: string;
  //   Name of the field that the value or data will appear under
  id: string;
  tooltip?: string;
  // only for 'dropdown'
  options?: { value: string | number; label: string }[];
  showIf?: string | Record<string, any>;
  showIfNot?: string | Record<string, any>;
  required?: boolean;
  optionsPath?: string;
  defaultValue?: string | boolean | number;
  showModalOnValueClicked?: { value: any; modalId: string };
  /** JSON paths to one or multiple string arrays inside the translation file
   * to pull data from. When used on a dropdown a string array will use every
   * element for both key and value.
   */
  valuesArraysPaths?: string[];
  autocompleteFreeSolo?: boolean;
  useFullWidthOfParent?: boolean;
  subtitleTranslationKey?: string;
  /**
   * Disable fields in the json configuration without erasing them.
   */
  disabled?: boolean;
  /**
   * Id of form to be made into an array.
   */
  arrayOfForm?: string;
  nestedForm?: string;
  arrayAddLabel?: string;
  arrayItemLabel?: string;
  datasetConditions?: DatasetCondition[];
  requiredIfKv?: Record<string, any>;
  omitNestedFields?: string[];
  inputFieldSuffix?: string;
  //   Only for TextField with type number
  numberMaxValue?: number;
  //   Only for TextField with type number
  numberMinValue?: number;
  //   Since we have like 3 different kinds of date input these
  //   settings will disable past or future dates for any of them.
  dateDisableFuture?: boolean;
  dateDisablePast?: boolean;
};

export type FormFieldConfiguration = KvcFormFieldProps;

type Layout = {
  element: ReactElement;
};

export enum KvcareInsurancePlanMixTypes {
  Basis = 'basic',
  Comfort = 'comfort',
  Premium = 'premium',
  None = 'none',
}

export type FormItem = KvcFormFieldProps | typeof divider | Layout;

export type FormState = Record<string, any>;

export const isFormField = (
  formItem: FormItem
): formItem is KvcFormFieldProps => {
  return typeof formItem.element === 'string' && formItem.element !== 'divider';
};

export function ParseFormFieldsFromJson(obj: any): KvcFormFieldProps[] {
  const result: KvcFormFieldProps[] = [];
  if (typeof obj === 'object' && Array.isArray(obj) && obj.length > 0) {
    obj.forEach((o) => {
      const formField = ParseFormFieldFromJson(o);
      if (formField !== null) {
        result.push(formField);
      }
    });
  }
  if (result.length > 0) {
    return result;
  }
  return [];
}

export function ParseFormFieldFromJson(obj: any): KvcFormFieldProps | null {
  if (typeof obj === 'object') {
    const id = obj?.id;
    const element = obj?.element;
    if (
      typeof element !== 'undefined' &&
      (typeof id === 'string' || elementsDontRequiredIds.includes(element))
    ) {
      const formField: KvcFormFieldProps = {
        ...obj,
        element: element,
        id: id,
      };

      return formField;
    }
  }
  return null;
}
