import CheckIcon from '@mui/icons-material/Check';
import PriorityHighIcon from '@mui/icons-material/PriorityHigh';
import { Box, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

interface Props {
  isSelectedStep: boolean;
  isValidated?: boolean;
  onClick: () => void;
  name: string;
  number: string;
}

export function DataInputFrameSidebarItem(props: Props) {
  const t = useTranslations();
  return (
    <Stack
      direction="row"
      sx={{
        marginTop: 2,
      }}
    >
      <Box
        sx={{
          width: 25,
          height: 25,
          borderRadius: '50%',
          bgcolor: props.isSelectedStep ? 'black' : 'var(--gray-light)',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontWeight: 'bold',
          fontSize: 12,
          marginRight: 1,
          position: 'relative',
        }}
      >
        <Box
          sx={{
            width: 12,
            height: 12,
            borderRadius: '50%',
            top: -3,
            right: -12,
            bgcolor: props.isValidated === true ? 'green' : 'red',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            fontSize: 12,
            marginRight: 1,
            position: 'absolute',
          }}
        >
          {props.isValidated ? (
            <CheckIcon
              sx={{
                fontSize: '12px',
              }}
            />
          ) : (
            <PriorityHighIcon
              sx={{
                fontSize: '12px',
              }}
            />
          )}
        </Box>
        {props.number}
      </Box>
      <Typography
        fontSize={14}
        color={props.isSelectedStep ? 'var(--teal-dark)' : 'var(--gray-light)'}
        fontWeight={props.isSelectedStep ? 700 : 400}
        sx={{
          cursor: 'pointer',
        }}
        onClick={() => {
          console.log('onQuestionnaireSelected 1');
          props.onClick();
        }}
      >
        {props.name}
      </Typography>
    </Stack>
  );
}
