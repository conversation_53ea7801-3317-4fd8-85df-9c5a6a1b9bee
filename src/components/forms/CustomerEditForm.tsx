'use client';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { type Customer } from '@prisma/client';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { type FormEvent, useEffect, useMemo, useState } from 'react';

import { KvcSecondaryButton } from '@/components/buttons/KvcSecondaryButton';
import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import {
  type FormFieldConfiguration,
  ParseFormFieldsFromJson,
} from '@/components/forms/domain';
import KvcFormStack from '@/components/ui/KvcFormStack';
import { useTRPC } from '@/trpc/client';
import { CaseGroupRelationType } from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { classes } from './cssClasses';
import { FormFieldRoot } from './FormFieldRoot';

interface Props {
  sx?: SxProps<Theme>;
  onCancelClick?: () => void;
  onSuccess?: () => void;
  customerId: number;
  onCustomerDeleted?: () => void;
}

type UpdateCustomer = Omit<Customer, 'dateOfBirth'> & {
  dateOfBirth?: Date;
};

export function CustomerEditForm(props: Props) {
  const t = useTranslations();
  const trpc = useTRPC();

  const [deleteCustomerId, setDeleteCustomerId] = useState<number | null>(null);

  const { data: customerFromDatabase, isLoading: customerIsLoading } =
    useSuspenseQuery(
      trpc.customers.getOneWithCaseGroupRelationType.queryOptions({
        customerId: props.customerId,
      })
    );
  const [customerInEdit, setCustomerInEdit] = useState<UpdateCustomer>(
    customerFromDatabase.customer
  );

  const personalDataForm = useMemo(() => {
    return ParseFormFieldsFromJson(t.raw('form_configurations.personalData'));
  }, [t]);

  const [relationToGroup, setRelationToGroup] =
    useState<CaseGroupRelationType | null>(
      customerFromDatabase.relationType != null
        ? (customerFromDatabase.relationType as CaseGroupRelationType)
        : null
    );

  const relationTypes: string[] = Object.values(CaseGroupRelationType);

  useEffect(() => {
    if (typeof customerFromDatabase === 'object') {
      setCustomerInEdit(customerFromDatabase.customer);
    }
  }, [customerFromDatabase]);

  const {
    mutateAsync: updateCustomer,
    isPending: addNewCustomerToCasePending,
  } = useMutation(
    trpc.customers.updateWithGroupRelation.mutationOptions({
      onSuccess: (data: any) => {
        console.log('CustomerCreateForm', data);
        props.onSuccess?.();
      },
      onError: (error: any) => {
        alert(`Error: ${error.message}`);
      },
    })
  );

  const {
    mutateAsync: deleteCustomerWithCase,
    isPending: deleteCustomerWithCasePending,
  } = useMutation(
    trpc.customers.deleteCustomerWithCase.mutationOptions({
      onSuccess: () => {
        console.log('mutateAsync: deleteCustomerWithCase success');
        props.onSuccess?.();
      },
      onError: (error: any) => {
        console.error('mutateAsync: updateCustomer error', error);
      },
    })
  );

  async function onCreateNewCustomer(
    event: FormEvent<HTMLFormElement>
  ): Promise<void> {
    event.preventDefault();

    if (relationToGroup !== null) {
      updateCustomer({
        customer: customerInEdit,
        relationType: relationToGroup,
      });
    }
  }

  return (
    <Stack
      direction="column"
      display="flex"
      justifyContent="center"
      flexGrow={1}
      textAlign="center"
      sx={{
        padding: 5,
        width: '100%',
        ...props.sx,
      }}
    >
      {customerInEdit !== null && (
        <form
          onSubmit={onCreateNewCustomer}
          style={{
            gap: '20px',
            display: 'flex',
            justifyContent: 'center',
            flexGrow: 1,
            textAlign: 'center',
            flexDirection: 'column',
          }}
        >
          {deleteCustomerId === null && (
            <Typography
              color={'var(--teal)'}
              fontWeight={700}
              variant="h5"
              textAlign="center"
            >
              {t.rich('form.edit_customer.headline', intlTranslationRichHelper)}
            </Typography>
          )}

          {deleteCustomerId === null && (
            <Stack direction="row" justifyContent="end">
              <KvcPlainTextButton
                onClick={() => {
                  setDeleteCustomerId(props.customerId);
                }}
                endIcon={<DeleteIcon sx={{ color: 'primary.main' }} />}
              >
                {t('form.edit_customer.delete_customer_button')}
              </KvcPlainTextButton>
            </Stack>
          )}

          {deleteCustomerId !== null && (
            <Stack direction="column" gap={2}>
              <Typography fontWeight={700} variant="h6">
                {t.rich(
                  'form.edit_customer.do_you_want_to_delete_this_customer'
                )}
              </Typography>

              <Stack direction="row" gap={2} justifyContent="center">
                <KvcSecondaryButton
                  onClick={() => {
                    setDeleteCustomerId(null);
                  }}
                  disabled={deleteCustomerWithCasePending}
                >
                  {t('form.edit_customer.cancel_delete_customer_button')}
                </KvcSecondaryButton>

                <Button
                  sx={{
                    backgroundColor: 'red',
                    color: 'black',
                    fontWeight: 700,
                    '&:hover': {
                      backgroundColor: '#8B0000',
                    },
                  }}
                  onClick={() => {
                    deleteCustomerWithCase({ customerId: props.customerId });
                  }}
                  disabled={deleteCustomerWithCasePending}
                >
                  {t('form.edit_customer.confirm_delete_customer_button')}
                </Button>
              </Stack>
            </Stack>
          )}

          <Stack
            direction="column"
            gap={2}
            display="flex"
            flexWrap="wrap"
            minWidth="100%"
            width="100%"
          >
            <KvcFormStack>
              <FormFieldRoot
                fields={(personalDataForm || []) as FormFieldConfiguration[]}
                state={customerInEdit}
                onChange={(newPatchedState) => {
                  setCustomerInEdit(newPatchedState);
                }}
                inputDisabled={deleteCustomerId !== null}
              ></FormFieldRoot>
            </KvcFormStack>

            <FormControl
              variant="outlined"
              className={classes.formField}
              required
              fullWidth={true}
            >
              <InputLabel htmlFor={'relationType'}>Person ist...</InputLabel>
              <Select
                id="relationType"
                label={'Person ist...'}
                value={relationToGroup}
                onChange={(e) => {
                  console.log('dropdown value change', e.target.value);
                  setRelationToGroup(
                    e.target.value !== null
                      ? (e.target.value as CaseGroupRelationType)
                      : null
                  );
                }}
                required
                slotProps={{
                  input: {
                    required: true,
                  },
                }}
                disabled={deleteCustomerId !== null}
              >
                {relationTypes.map((option) => (
                  <MenuItem
                    key={option}
                    className={classes.dropdownOption}
                    value={option}
                  >
                    {t(`case_relation_types.${option}`)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>

          {deleteCustomerId === null && (
            <Stack
              direction="row"
              display="flex"
              flexWrap="wrap"
              justifyContent="center"
              sx={{
                marginTop: 'auto',
              }}
              gap={1}
            >
              <Button
                type="submit"
                sx={{ fontWeight: 700 }}
                disabled={addNewCustomerToCasePending || customerIsLoading}
              >
                {addNewCustomerToCasePending
                  ? 'Updating...'
                  : t.rich(
                      'form.edit_customer.edit_customer_button',
                      intlTranslationRichHelper
                    )}
              </Button>

              {typeof props.onCancelClick === 'function' && (
                <Button
                  onClick={props.onCancelClick}
                  sx={{ fontWeight: 700 }}
                  disabled={addNewCustomerToCasePending || customerIsLoading}
                >
                  {t.rich(
                    'form.edit_customer.cancel_edit_new_customer_button',
                    intlTranslationRichHelper
                  )}
                </Button>
              )}
            </Stack>
          )}
        </form>
      )}
    </Stack>
  );
}
