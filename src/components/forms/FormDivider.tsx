import { Divider } from '@mui/material';
import { styled } from '@mui/material/styles';

export const FormDivider = styled(Divider)(({ theme }) => ({
  border: 1,
  borderStyle: 'solid',
  borderColor: theme.palette.separator.dark,
  minWidth: 'calc(100% - 20px)',
  width: 'calc(100% - 20px)',
  maxWidth: 'calc(100% - 20px)',
  marginTop: 55,
  marginBottom: 55,
  flexGrow: 1,
  //   marginRight: '40px',
}));

export const FormDividerLight = styled(Divider)(({ theme }) => ({
  border: 1,
  borderStyle: 'solid',
  borderColor: theme.palette.colors.lightGray,
  minWidth: 'calc(100% - 20px)',
  width: 'calc(100% - 20px)',
  maxWidth: 'calc(100% - 20px)',
  marginTop: 55,
  marginBottom: 55,
  flexGrow: 1,
  //   marginRight: '40px',
}));
