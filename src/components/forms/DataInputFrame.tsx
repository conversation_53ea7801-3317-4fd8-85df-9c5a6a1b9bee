/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { Button, Stack, Typography } from '@mui/material';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import equal from 'fast-deep-equal';
import debounce from 'lodash.debounce';
import type { Route } from 'next';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState } from 'react';
import { useDeepCompareEffectNoCheck } from 'use-deep-compare-effect';

import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import {
  type KvcFormFieldProps,
  ParseFormFieldsFromJson,
} from '@/components/forms/domain';
import { KvModal } from '@/components/modal/modal';
import KvcFormStack from '@/components/ui/KvcFormStack';
import { useTRPC } from '@/trpc/client';
import { CaseDataInputIsComplete } from '@/utils/formValidation';
import {
  type CaseDataInputVisitedSteps,
  type CaseNavbarInfo,
  type CustomerAndStepUrlParams,
  type DataInputConfiguration,
  type DataInputCurrent,
  DataInputSteps,
  type DataValidationResult,
  ExtractRootIdNamesFromFormConfigurationToLabelIdArray,
  type FormValidationResult,
  GetNextCaseCustomer,
  type KvcInputStep,
  KvcInputSubStepType,
  PatchCustomerForValidation,
  PatchQuestionnaireForValidation,
  ValidateFormDataBasedOnFormConfiguration,
} from '@/utils/helperTypes';

import { CustomerCreateForm } from './CustomerCreateForm';
import { CustomerEditForm } from './CustomerEditForm';
import { DataInputFrameSidebar } from './DataInputFrameSidebar';
import { FormFieldRoot } from './FormFieldRoot';

interface Props {
  onSelectedCustomerChange?: (customerName: string | null) => void;
  onProgressChange?: (progressPercent: number) => void;
  caseGroupNumber?: string;
  caseNumber?: string;
  createCustomersOnly?: boolean;
  onCaseInfoChanged?: (caseInfo: CaseNavbarInfo) => void;
  setStickyBottomBarContent?: (content: React.ReactElement | null) => void;
}

export function DataInputFrame(props: Props) {
  const router = useRouter();
  const pathName = usePathname();
  const searchParams = useSearchParams();

  const t = useTranslations();

  const dataInputConfiguration = useMemo(() => {
    const rawConfiguration = t.raw('data_input_configuration');
    const configuration = rawConfiguration as DataInputConfiguration;
    if (typeof configuration === 'object') {
      if (configuration?.steps?.length <= 0) {
        throw new Error('Bad configuration! ' + JSON.stringify(configuration));
      }
    }
    const runtimeConfiguration: DataInputConfiguration = {
      steps: configuration.steps.map((step, i) => {
        if (
          step.substep === 'formSegmentation' &&
          typeof step.substepform === 'string'
        ) {
          const form = ParseFormFieldsFromJson(
            t.raw(`form_configurations.${step.substepform}`)
          );
          const subSteps =
            ExtractRootIdNamesFromFormConfigurationToLabelIdArray(form);
          if (Array.isArray(subSteps) && subSteps.length > 0) {
            return {
              step: step.step,
              substep: step.substep,
              substepform: step.substepform,
              substeps: subSteps,
            };
          } else {
            return {
              step: step.step,
              substep: step.substep,
              substepform: step.substepform,
            };
          }
        } else if (
          step.substep === 'formArray' &&
          (step.substeps?.length ?? 0) > 0
        ) {
          return {
            step: step.step,
            substep: step.substep,
            substeps: step.substeps?.map((substep) => {
              if (typeof substep.label === 'string') {
                return substep;
              } else {
                return {
                  id: substep.id,
                  label: `form_names.${substep.id}`,
                };
              }
            }),
          };
        }
        return step;
      }),
    };
    console.log('DataInputConfiguration', runtimeConfiguration);
    return runtimeConfiguration;
  }, []);

  const [current, setCurrent] = useState<DataInputCurrent>({
    step: dataInputConfiguration.steps[0],
    substep: dataInputConfiguration.steps[0].substeps?.[0],
  });

  const nextSubStep = useMemo<KvcInputStep | null>(() => {
    const currentStepIndex = dataInputConfiguration.steps.findIndex(
      (it) => it.step === current.step.step
    );
    return dataInputConfiguration.steps[currentStepIndex + 1] ?? null;
  }, [dataInputConfiguration, current]);

  const nextStep = useMemo<KvcInputStep | null>(() => {
    const currentStepIndex = dataInputConfiguration.steps.findIndex(
      (it) => it.step === current.step.step
    );
    return dataInputConfiguration.steps[currentStepIndex + 1] ?? null;
  }, [dataInputConfiguration, current]);

  const nextStepAndSubstep = useMemo<DataInputCurrent | null>(() => {
    const currentStepIndex = dataInputConfiguration.steps.findIndex(
      (it) => it.step === current.step.step
    );
    if (typeof current.substep?.id === 'string') {
      const currentSubstepIndex = dataInputConfiguration.steps[
        currentStepIndex
      ]?.substeps?.findIndex((it) => it.id === current.substep?.id);
      if (typeof currentSubstepIndex === 'number') {
        const nextSubStep =
          dataInputConfiguration.steps[currentStepIndex]?.substeps?.[
            currentSubstepIndex + 1
          ];
        if (typeof nextSubStep !== 'undefined' && nextSubStep !== null) {
          const next: DataInputCurrent = {
            step: current.step,
            substep: nextSubStep,
          };
          return { step: current.step, substep: nextSubStep };
        }
      }
    }

    const nextS = dataInputConfiguration.steps[currentStepIndex + 1] ?? null;
    if (nextS === null) {
      return null;
    }
    return {
      step: nextS,
      substep: nextS.substeps?.[0],
    };
  }, [current]);

  //   Use State:

  const [showCreateCustomerModal, setShowCreateCustomerModal] = useState(false);
  const [deleteCustomerId, setDeleteCustomerId] = useState<number | null>(null);
  const [editCustomerId, setEditCustomerId] = useState<number | null>(null);

  //   const savingBlocked = useRef<boolean>(true);

  const [showDebugData, setShowDebugData] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<
    string | number | null
  >(null);
  const [selectedCaseId, setSelectedCaseId] = useState<string | number | null>(
    null
  );

  //   const previousTempDataRef = useRef<any | null>(null);
  const [tempData, setTempData] = useState<any | null>(null);
  const [loadingTempData, setLoadingTempData] = useState(true);
  const [currentQuestionnaireId, setCurrentQuestionnaireId] = useState<
    null | number
  >(null);
  const [currentQuestionnaireFormId, setCurrentQuestionnaireFormId] = useState<
    null | string
  >(null);

  const [currentlyLoadedQuestionnaire, setCurrentlyLoadedQuestionnaire] =
    useState<KvcFormFieldProps[] | null>(null);

  const [highlightInvalidFields, setHighlightInvalidFields] = useState(false);

  const [groupValidationSuccessful, setGroupValidationSuccessful] =
    useState(false);
  //   Use Memo:

  const personalDataForm = useMemo(() => {
    return ParseFormFieldsFromJson(t.raw('form_configurations.personalData'));
  }, []);

  const citizenshipForm = useMemo(() => {
    return ParseFormFieldsFromJson(
      t.raw('form_configurations.citizenshipAndProfession')
    );
  }, []);

  const previousInsuranceForm = useMemo(() => {
    return ParseFormFieldsFromJson(
      t.raw('form_configurations.previousInsurance')
    );
  }, []);

  const healthChecklistForm = useMemo(() => {
    return ParseFormFieldsFromJson(
      t.raw('form_configurations.healthChecklist')
    );
  }, []);

  //   TRPC:

  const trpc = useTRPC();
  const {
    data: userIsAdmin,
    refetch: refetchCaseData,
    isRefetching: caseDataIsLoading,
  } = useSuspenseQuery(trpc.administration.userIsAdmin.queryOptions());

  const {
    data: caseGroupData,
    refetch: refetchCaseGroupData,
    isRefetching: caseGroupDataIsLoading,
    isLoadingError: caseGroupDataLoadingError,
  } = useSuspenseQuery(
    trpc.cases.getCaseGroupWithAllRelations.queryOptions({
      caseGroupNumber: props.caseGroupNumber,
      caseNumber: props.caseNumber,
    })
  );
  const caseData = useMemo(() => {
    if (selectedCustomerId === null) {
      return caseGroupData.cases[0];
    }
    const selectedCase = caseGroupData.cases.find(
      (it) => it.customerId === selectedCustomerId
    );
    if (typeof selectedCase !== 'undefined') {
      return selectedCase;
    }
    return caseGroupData.cases[0];
  }, [caseGroupData, selectedCustomerId]);

  const selectedCustomer = useMemo(() => {
    if (selectedCustomerId === null) {
      return caseGroupData.customers[0];
    }
    const selectedCustomer = caseGroupData.customers.find(
      (it) => it.customerId === selectedCustomerId
    );
    if (typeof selectedCustomer !== 'undefined') {
      return selectedCustomer;
    }
    return caseGroupData.customers[0];
  }, [caseGroupData, selectedCustomerId]);

  useEffect(() => {
    if (typeof props.onCaseInfoChanged !== 'undefined') {
      props.onCaseInfoChanged({
        firstName: caseData.customer.firstName,
        lastName: caseData.customer.lastName,
        caseNumber: caseData.caseNumber,
        caseType: caseData.caseType,
      });
    }
  }, [caseData]);

  const { mutateAsync: updateCaseDataInputProgress } = useMutation(
    trpc.cases.updateCaseDataInputProgress.mutationOptions({
      onSuccess: () => {},
      onError: (error) => {
        console.log('error', error);
      },
    })
  );

  const {
    mutateAsync: deleteCustomerWithCase,
    isPending: deleteCustomerAndCaseCustomerPending,
  } = useMutation(
    trpc.customers.deleteCustomerWithCase.mutationOptions({
      onSuccess: () => {
        console.log('mutateAsync: deleteCustomerAndCaseCustomer success');
        refetchCaseGroupData();
      },
      onError: (error: any) => {
        console.error('mutateAsync: updateCustomer error', error);
      },
    })
  );

  const { mutateAsync: updateCaseCustomer } = useMutation(
    // TODO: update CaseCustomer
    trpc.customers.update.mutationOptions({
      onSuccess: () => {
        console.log('mutateAsync: updateCustomer success');
      },
      onError: (error: any) => {
        console.error('mutateAsync: updateCustomer error', error);
      },
    })
  );

  const { mutateAsync: updateCustomer } = useMutation(
    trpc.customers.update.mutationOptions({
      onSuccess: () => {
        console.log('mutateAsync: updateCustomer success');
      },
      onError: (error: any) => {
        console.error('mutateAsync: updateCustomer error', error);
      },
    })
  );

  const debounceUpdateCustomer = useMemo(
    () =>
      debounce((data: any) => {
        console.log('persisting... customer', data);
        updateCustomer(data);
      }, 1000),
    []
  );

  const debounceUpdateQuestionnaire = useMemo(
    () =>
      debounce((questionnaireId: number, data: any, status?: string | null) => {
        console.log('persisting... questionnaire', data);
        updateQuestionnaireAnswers({
          questionnaireId: questionnaireId,
          answersJson: data,
          status: status,
        });
      }, 1000),
    []
  );

  const caseDataCombinedWithTempDataForRealTimeValidation = useMemo(() => {
    if (tempData === null) {
      return caseData;
    }
    if (typeof selectedCustomerId === 'number') {
      if (current.step.step === DataInputSteps.PersonalData) {
        return PatchCustomerForValidation(
          caseData,
          selectedCustomerId,
          tempData
        );
      } else if (
        currentQuestionnaireId !== null &&
        currentQuestionnaireFormId !== null
      ) {
        return PatchQuestionnaireForValidation(
          structuredClone(caseData),
          selectedCustomerId,
          currentQuestionnaireId,
          currentQuestionnaireFormId,
          structuredClone(tempData)
        );
      }
    }
    return caseData;
  }, [
    caseData,
    tempData,
    current,
    currentQuestionnaireId,
    currentQuestionnaireFormId,
    selectedCustomerId,
  ]);

  const caseDataInputBlocked = useMemo(() => {
    return caseData.status !== 'DataEntry' && userIsAdmin !== true;
  }, [caseData, userIsAdmin]);

  const caseDataInputAdminOnly = useMemo(() => {
    return caseData.status !== 'DataEntry' && userIsAdmin === true;
  }, [caseData, userIsAdmin]);

  const caseDataInputComplete = useMemo(() => {
    return CaseDataInputIsComplete(
      [caseDataCombinedWithTempDataForRealTimeValidation],
      dataInputConfiguration,
      t
    );
  }, [caseDataCombinedWithTempDataForRealTimeValidation]);

  //   useEffect(() => {
  //     console.log(
  //       'caseDataCombinedWithTempDataForRealTimeValidation',
  //       caseDataCombinedWithTempDataForRealTimeValidation
  //     );
  //   }, [caseDataCombinedWithTempDataForRealTimeValidation]);

  useEffect(() => {
    return () => debounceUpdateCustomer.cancel();
  }, [debounceUpdateCustomer]);

  useEffect(() => {
    console.log(' * case data changed *');
  }, [caseData]);

  useEffect(() => {
    console.log(' <> LOADING case data changed <>', caseGroupDataIsLoading);
  }, [caseGroupDataIsLoading]);

  const {
    mutateAsync: addPotentialInsuranceToCase,
    isPending: addPotentialInsuranceToCaseIsPending,
  } = useMutation(
    trpc.potentialInsurance.addPotentialInsuranceToCase.mutationOptions({
      onSuccess: () => {
        //
      },
      onError: (error) => {
        //
      },
    })
  );

  useEffect(() => {
    const currentStepId = current?.step?.step;
    if (
      typeof selectedCustomerId === 'number' &&
      typeof currentStepId === 'string' &&
      currentStepId.length > 0
    ) {
      const currentSubstepId = current.substep?.id;
      const newDataInputProgress: CaseDataInputVisitedSteps =
        typeof caseData.dataInputProgress === 'object' &&
        caseData.dataInputProgress != null &&
        typeof (caseData.dataInputProgress as any).visitedSteps === 'object'
          ? (structuredClone(
              caseData.dataInputProgress
            ) as CaseDataInputVisitedSteps)
          : { visitedSteps: [] };

      const currentStepWithSubstepMark =
        typeof currentSubstepId === 'string'
          ? `${currentStepId}>${currentSubstepId}`
          : null;

      if (
        typeof newDataInputProgress.visitedSteps === 'object' &&
        (!newDataInputProgress.visitedSteps.includes(currentStepId) ||
          (currentStepWithSubstepMark !== null &&
            !newDataInputProgress.visitedSteps.includes(
              currentStepWithSubstepMark
            )))
      ) {
        newDataInputProgress.visitedSteps.push(currentStepId);
        if (currentStepWithSubstepMark !== null) {
          newDataInputProgress.visitedSteps.push(currentStepWithSubstepMark);
        }
        newDataInputProgress.visitedSteps = [
          ...new Set(newDataInputProgress.visitedSteps),
        ];
        updateCaseDataInputProgress({
          caseId: caseData.caseId,
          data: newDataInputProgress,
        });
      }
    }
  }, [caseData, current, selectedCustomerId]);

  const { mutateAsync: addQuestionnaireToCustomer, isPending } = useMutation(
    trpc.questionnaire.create.mutationOptions({
      onSuccess: () => {
        refetchCaseGroupData();
        // alert('Questionnaire added to case!');
      },
      onError: (error) => {
        console.log('error', error);
        alert('Error adding questionnaire to case!');
      },
    })
  );

  const {
    mutateAsync: updateQuestionnaireAnswers,
    isPending: updateQuestionnaireAnswersPending,
  } = useMutation(
    trpc.questionnaire.updateAnswersByQuestionnaireId.mutationOptions({
      onSuccess: () => {
        // refetchCaseData();
        console.log(`questionnaire updated`);
      },
      onError: (error) => {
        console.log('error', error);
        alert('Error adding customer to case!');
      },
    })
  );

  //   const onSubmitQuestionnaire = async (
  //     inputs: UpdateOrCreateQuestionnaireSchemaInputs
  //   ) => {
  //     await addQuestionnaireToCustomer(inputs);
  //   };

  const nextCustomer = useMemo(() => {
    return (
      GetNextCaseCustomer(caseGroupData, selectedCustomer.customerId) ?? null
    );
  }, [selectedCustomer, caseGroupData]);

  //   useEffect(() => {
  //     if (subStepList === null || subStepList.length <= 0) {
  //       setCurrentSubStep(null);
  //     } else if (
  //       currentSubStep === null &&
  //       selectedCustomerId !== null &&
  //       typeof subStepList === 'object' &&
  //       Array.isArray(subStepList) &&
  //       subStepList.length > 0
  //     ) {
  //       setCurrentSubStep(subStepList[0]);
  //     }
  //   }, [selectedCustomerId, currentSubStep, selectedStep, subStepList]);

  useDeepCompareEffectNoCheck(() => {
    // if (savingBlocked.current === true) {
    //   return;
    // }
    // console.log(
    //   'tempData',
    //   tempData,
    //   currentQuestionnaireId,
    //   previousTempDataRef.current === null
    // );
    // if (previousTempDataRef.current === null) {
    //   console.log('persistence skipped due to ref 🛑', tempData === null);
    // }
    if (
      loadingTempData === false &&
      tempData !== null &&
      typeof tempData === 'object' &&
      Object.entries(tempData).length > 0
      //    &&
      //   previousTempDataRef.current !== null
    ) {
      if (current.step.step === DataInputSteps.PersonalData) {
        debounceUpdateCustomer(tempData);
      } else if (currentQuestionnaireId !== null) {
        const questionnaireId = currentQuestionnaireId;
        const data = structuredClone(tempData);
        const loadedQuestionnaireForm = ParseFormFieldsFromJson(
          t.raw(`form_configurations.${currentQuestionnaireFormId}`)
        );
        const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
          data,
          loadedQuestionnaireForm,
          undefined,
          t,
          structuredClone(caseData)
        );
        let questionnaireCompletedPercent = 0;
        if (bad.length === 0) {
          questionnaireCompletedPercent = 100;
        } else if (bad.length > 0 && required.length > 0) {
          questionnaireCompletedPercent = Math.floor(
            (bad.length / required.length) * 100
          );
        }
        debounceUpdateQuestionnaire(
          questionnaireId,
          data,
          `${questionnaireCompletedPercent}`
        );
      }
    }
    // previousTempDataRef.current = tempData;
    // console.log(
    //   'ref updated and is null:',
    //   previousTempDataRef.current === null
    // );
  }, [tempData, loadingTempData]);

  useEffect(() => {
    if (currentQuestionnaireFormId === null) {
      setCurrentlyLoadedQuestionnaire(null);
    } else if (currentQuestionnaireFormId === 'personalData') {
      setCurrentlyLoadedQuestionnaire(personalDataForm);
    } else if (currentQuestionnaireFormId === 'citizenshipAndProfession') {
      setCurrentlyLoadedQuestionnaire(citizenshipForm);
    } else if (currentQuestionnaireFormId === 'previousInsurance') {
      setCurrentlyLoadedQuestionnaire(previousInsuranceForm);
    } else if (currentQuestionnaireFormId === 'healthChecklist') {
      setCurrentlyLoadedQuestionnaire(healthChecklistForm);
    } else {
      const loadedQuestionnaire = ParseFormFieldsFromJson(
        t.raw(`form_configurations.${currentQuestionnaireFormId}`)
      );
      if (loadedQuestionnaire.length > 0) {
        setCurrentlyLoadedQuestionnaire(loadedQuestionnaire);
      } else {
        setCurrentlyLoadedQuestionnaire(null);
      }
    }
  }, [currentQuestionnaireFormId]);

  useEffect(() => {
    if (caseGroupDataIsLoading) {
      return;
    }
    if (typeof selectedCustomer === 'object') {
      setCurrentQuestionnaireId(null);
      setCurrentQuestionnaireFormId(null);
      if (current.step.step === DataInputSteps.PersonalData) {
        console.log('setting ref to null - PersonalData');
        // previousTempDataRef.current = null;
        setTempData(selectedCustomer);
        setLoadingTempData(false);
        setCurrentQuestionnaireFormId('personalData');
      } else {
        let formId: string | null = null;
        if (
          current.step.substep === KvcInputSubStepType.formArray &&
          typeof current.substep?.id === 'string'
        ) {
          formId = current.substep.id;
        } else if (
          current.step.substep === KvcInputSubStepType.formSegmentation &&
          typeof current.step.substepform === 'string'
        ) {
          formId = current.step.substepform;
        } else if (
          typeof current.step.step === 'string' &&
          typeof current.step.substep === 'undefined' &&
          typeof current.step.substepform === 'undefined' &&
          typeof current.step.substeps === 'undefined' &&
          typeof current.substep === 'undefined'
        ) {
          formId = current.step.step;
        }

        if (typeof formId === 'string') {
          setCurrentQuestionnaireFormId(formId);

          const questionnaire = caseData.questionnaires.find(
            (q) => q.formId === formId
          );

          if (typeof questionnaire === 'object') {
            setTempData(questionnaire.answersJson || {});
            setLoadingTempData(false);
            setCurrentQuestionnaireId(questionnaire.questionnaireId);
          } else {
            addQuestionnaireToCustomer({
              caseCaseId: caseData.caseId,
              answersJson: {},
              formId: formId,
              status: '',
              type: '',
            });
          }
        }
      }
    }
  }, [selectedCustomer, caseData, caseGroupDataIsLoading]);

  useEffect(() => {
    if (caseGroupDataIsLoading) {
      console.log('refetching <<<');
    }
  }, [caseGroupDataIsLoading]);

  useEffect(() => {
    setTempData(null);
    setLoadingTempData(true);
    refetchCaseGroupData();
  }, [current, selectedCustomerId]);

  //   useEffect(() => {
  //     if (
  //       caseGroupDataIsLoading === true ||
  //       currentQuestionnaireId == null ||
  //       currentQuestionnaireFormId == null ||
  //       currentlyLoadedQuestionnaire == null ||
  //       tempData == null
  //     ) {
  //       savingBlocked.current = true;
  //     } else {
  //       savingBlocked.current = false;
  //     }
  //   }, [
  //     currentQuestionnaireId,
  //     currentQuestionnaireFormId,
  //     currentlyLoadedQuestionnaire,
  //     tempData,
  //     caseGroupDataIsLoading,
  //   ]);

  useEffect(() => {
    if (current.step.step === DataInputSteps.CitizenshipAndProfession) {
      //   setSelectedForm(citizenshipAndProfessionFormCards);
    } else if (current.step.step === DataInputSteps.PersonalData) {
      //   setSelectedForm(basisdatenFormCards);
    } else if (current.step.step === DataInputSteps.PreviousInsurance) {
      //   setSelectedForm(vorversicherungFormCards);
    }
    // setCustomerAndStepUrlParams({
    //   ...customerAndStepUrlParams,
    //   stepId: current.step.step,
    // });
  }, [current]);

  useEffect(() => {
    if (typeof props.onSelectedCustomerChange !== 'undefined') {
      if (selectedCustomerId === null) {
        props.onSelectedCustomerChange(null);
      }
      // else {
      //   if (typeof selectedCustomer.personalData.firstName === 'string') {
      //   }
      // }
    }
    // if (selectedCustomerId === null) {
    //   setCustomerAndStepUrlParams({
    //     customerId: null,
    //     stepId: current.step.step,
    //   });
    // } else {
    //   setCustomerAndStepUrlParams({
    //     customerId: selectedCustomerId ? `$selectedCustomerId` : null,
    //     stepId: current.step.step,
    //   });
    // }
  }, [selectedCustomerId]);

  const [customerAndStepUrlParams, setCustomerAndStepUrlParams] =
    useState<CustomerAndStepUrlParams>({ customerId: null, stepId: null });

  useEffect(() => {
    const currentParams = new URLSearchParams(searchParams.toString());
    if (customerAndStepUrlParams.customerId === null) {
      currentParams.delete('customerId');
    } else {
      currentParams.set('customerId', customerAndStepUrlParams.customerId);
    }

    if (customerAndStepUrlParams.stepId === null) {
      currentParams.delete('stepId');
    } else {
      currentParams.set('stepId', customerAndStepUrlParams.stepId);
    }

    router.replace(`${pathName}?${currentParams.toString()}` as Route, {
      scroll: false,
    });
  }, [customerAndStepUrlParams]);

  const [newValidation, setNewValidation] = useState<DataValidationResult>({});

  useEffect(() => {
    const v: DataValidationResult = {};
    caseGroupData.cases.forEach((ccase) => {
      const customerRecord: Record<string, FormValidationResult> = {};
      ccase.questionnaires.forEach((questionnaire) => {
        if (
          //   questionnaire.formId === 'citizenshipAndProfession' &&
          typeof questionnaire.formId === 'string'
        ) {
          const form = ParseFormFieldsFromJson(
            t.raw(`form_configurations.${questionnaire.formId}`)
          );
          if (form.length > 0) {
            const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
              questionnaire.answersJson,
              form,
              undefined,
              t,
              ccase
            );
            customerRecord[questionnaire.formId] = {
              bad: bad,
              required: required,
              total: required.length,
              done: required.length - bad.length,
              isComplete: bad.length === 0,
              formId: questionnaire.formId,
            };
          }
        }
      });
      dataInputConfiguration.steps.forEach((step) => {
        if (typeof customerRecord[step.step] !== 'object') {
          let stepIsCompleteBySubsteps: boolean | null = null;
          if (
            step.substep === 'formArray' &&
            typeof step.substeps === 'object' &&
            Array.isArray(step.substeps) &&
            step.substeps.length > 0
          ) {
            step.substeps.forEach((substep) => {
              if (
                stepIsCompleteBySubsteps !== false &&
                typeof substep.id === 'string' &&
                typeof customerRecord[substep.id] === 'object'
              ) {
                if (customerRecord[substep.id].isComplete === true) {
                  stepIsCompleteBySubsteps = true;
                } else {
                  stepIsCompleteBySubsteps = false;
                }
              }
            });
          } else if (step.substep === 'formSegmentation') {
            //
          }
          customerRecord[step.step] = {
            isComplete: stepIsCompleteBySubsteps === true,
            bad: [],
            required: [],
            total: 1,
            done: 1,
            formId: '',
          };
        }
      });
      v[ccase.customerId] = customerRecord;
    });
    // console.log('newvalidation', v);
    setNewValidation(v);
  }, [caseData, tempData, t, dataInputConfiguration]);

  useEffect(() => {
    console.log('newvalidation', newValidation);
    //     if (Object.entries(newValidation).length <= 0) {
    //       props.onProgressChange?.(0);
    //     } else {
    //       for (const value of Object.values(newValidation) as Human[HumanProp][]) {
    //         console.log(value); // value is number
    //       }

    //       let done = 0;
    //       let total = 0;

    //       Object.values(newValidation).forEach((result) => {
    //         const v = result.value as FormValidationResult;
    //         total += v.total;
    //         done += v.done;
    //       });

    //       props.onProgressChange?.(total / done);
    //     }
    let complete = true;
    let total = 0;
    let done = 0;

    for (const [userId, validationResults] of Object.entries(newValidation)) {
      for (const [perm, result] of Object.entries(validationResults)) {
        total += result.required.length;
        done += result.required.length - result.bad.length;
        if (result.bad.length > 0) {
          console.log('validation failed on', result);
          complete = false;
        }
      }
    }
    props.onProgressChange?.((done / total) * 100);
    console.log('progress%', total, done);
    setGroupValidationSuccessful(complete);
    // console.log('validation total', complete);
  }, [newValidation]);

  useEffect(() => {
    console.log('oncasenumberchange', props.caseNumber);
    if (selectedCustomerId === null) {
      const customerId = caseGroupData.cases.find(
        (it) => it.caseNumber === props.caseNumber
      )?.customerId;
      if (typeof customerId === 'number') {
        setSelectedCustomerId(customerId);
      }
    }
  }, [props.caseNumber]);

  useEffect(() => {
    if (typeof props.setStickyBottomBarContent !== 'undefined') {
      if (caseDataInputAdminOnly === true) {
        props.setStickyBottomBarContent(
          <Stack
            direction="row"
            display="flex"
            sx={{
              backgroundColor: 'colors.warningYellow',
              width: '100%',
              flexGrow: 1,
              justifyContent: 'center',
              p: 2,
            }}
          >
            <Stack
              direction="column"
              display="flex"
              sx={{ marginTop: 'auto', marginBottom: 'auto' }}
            >
              <Typography>{t('form.data_input_blocked')}</Typography>
            </Stack>
            <Stack
              direction="row"
              display="flex"
              justifyContent="right"
              paddingRight={'20px'}
              // marginTop={1}
            ></Stack>
          </Stack>
        );
      } else if (caseDataInputBlocked === true) {
        props.setStickyBottomBarContent(
          <Stack
            direction="row"
            display="flex"
            sx={{
              backgroundColor: 'colors.warningYellow',
              width: '100%',
              flexGrow: 1,
              justifyContent: 'center',
              p: 2,
            }}
          >
            <Stack
              direction="column"
              display="flex"
              sx={{ marginTop: 'auto', marginBottom: 'auto' }}
            >
              <Typography>{t('form.data_input_blocked')}</Typography>
            </Stack>
            <Stack
              direction="row"
              display="flex"
              justifyContent="right"
              paddingRight={'20px'}
              // marginTop={1}
            ></Stack>
            <Button
              type="submit"
              onClick={(e) => {
                router.push(`/cases/${caseData.caseNumber}`);
              }}
            >
              <Typography fontWeight={700}>Zum Vorgang</Typography>
            </Button>
          </Stack>
        );
      } else if (
        caseDataInputComplete === true &&
        selectedCustomerId !== null
      ) {
        props.setStickyBottomBarContent(
          <Stack direction="row" display="flex" justifyContent="center" p={2}>
            <Stack
              direction="column"
              display="flex"
              sx={{ marginTop: 'auto', marginBottom: 'auto' }}
            >
              <Typography>{t('form.data_input_complete')}</Typography>
            </Stack>
            <Stack
              direction="row"
              display="flex"
              justifyContent="right"
              paddingRight={'20px'}
              // marginTop={1}
            ></Stack>
            <Button
              type="submit"
              onClick={(e) => {
                router.push(
                  `/dateneingabe/beenden/case/${caseData.caseNumber}`
                );
              }}
            >
              <Typography fontWeight={700}>
                {`Dateneingabe für ${caseData.customer.firstName} ${caseData.customer.lastName} abschliessen`}
              </Typography>
            </Button>
          </Stack>
        );
      } else {
        props.setStickyBottomBarContent(null);
      }
    }
  }, [caseDataInputComplete, selectedCustomerId]);

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    setHighlightInvalidFields(true);
    e.preventDefault();
  };

  return (
    <Stack
      direction="column"
      sx={{ marginTop: 8 }}
      className={highlightInvalidFields === true ? 'doValidate' : undefined}
    >
      {showDebugData && (
        <div>
          <div>caseDataInputBlocked: {`${caseDataInputBlocked}`}</div>
          <div>
            ID: {props.caseGroupNumber} {typeof props.caseGroupNumber} ?{' '}
            {props.caseNumber} {typeof props.caseNumber}
            caseGroupDataLoadingError: {`${caseGroupDataLoadingError}`}
          </div>
          <div style={{ wordWrap: 'break-word' }}>
            V: {`${JSON.stringify(newValidation)}`}
          </div>
          {/* <div>V: {`${JSON.stringify(validationResult)}`}</div> */}
          <div>current: {`${JSON.stringify(current)}`}</div>
          <div>step: {current.step.step}</div>
          <div>substep: {current.substep?.id}</div>
          <div>currentQuestionnaireFormId: {currentQuestionnaireFormId}</div>
          <div>currentQuestionnaireId: {`${currentQuestionnaireId}`}</div>
          {/* <div>
            currentlyLoadedQuestionnaire:{' '}
            {JSON.stringify(currentlyLoadedQuestionnaire)}
          </div> */}

          <div>loadingTempData: {`${loadingTempData}`}</div>
          <div>highlightInvalidFields: {`${highlightInvalidFields}`}</div>
          <div>
            currentlyLoadedQuestionnaire = null:{' '}
            {currentlyLoadedQuestionnaire === null ? 'true' : 'false'}
          </div>
          <div>subSteps: {JSON.stringify(current.step.step)}</div>
        </div>
      )}
      <Stack direction="row">
        <Stack
          direction="row"
          display="flex"
          sx={{ width: 560, maxWidth: 560, minWidth: 300 }}
        >
          <Stack
            display="flex"
            direction="column"
            gap={1}
            sx={{
              minWidth: 300,
              maxWidth: 300,
              marginLeft: 'auto',
              marginRight: 'auto',
              marginBottom: 2,
              marginTop: 2,
              paddingLeft: '25px',
              paddingRight: '25px',
            }}
          >
            <DataInputFrameSidebar
              caseGroup={caseGroupData}
              selectedCustomerId={selectedCustomerId}
              setSelectedCustomerId={(customerId) => {
                setSelectedCustomerId(customerId);
              }}
              onClickAddNewCustomer={() => {}}
              deleteCustomerByCustomerId={(
                customerId: string | number | null
              ) => {
                if (typeof customerId === 'number') {
                  setDeleteCustomerId(customerId);
                }
              }}
              showCustomerListOnly={props.createCustomersOnly === true}
              onEditCustomerClick={(editCustomerId) => {
                setEditCustomerId(editCustomerId);
              }}
              dataInputConfiguration={dataInputConfiguration}
              current={current}
              setCurrent={setCurrent}
              dataValidationResult={newValidation}
              caseData={caseData}
              visitedSteps={
                caseData.dataInputProgress != null &&
                typeof caseData.dataInputProgress === 'object' &&
                typeof (caseData.dataInputProgress as any).visitedSteps ===
                  'object' &&
                Array.isArray((caseData.dataInputProgress as any).visitedSteps)
                  ? ((caseData.dataInputProgress as any)
                      .visitedSteps as string[])
                  : []
              }
            />
            <Stack direction="row">
              <KvcPlainTextButton
                onClick={() => {
                  setShowCreateCustomerModal(true);
                }}
                startIcon={
                  <AddCircleOutlineIcon
                    sx={{ color: 'var(--teal)', marginRight: 1 }}
                  />
                }
                sx={{ paddingLeft: 1, paddingRight: 0 }}
              >
                {t('components.data_input_frame.add-new-person')}
              </KvcPlainTextButton>
            </Stack>
          </Stack>
        </Stack>

        {caseGroupDataIsLoading !== true && (
          <Stack
            direction="column"
            maxWidth={800}
            sx={{
              flexGrow: 1,
              '& > form > *': {
                marginBottom: 2,
              },
            }}
            paddingRight="25px"
          >
            <form onSubmit={onSubmit} style={{ width: '100%' }}>
              {selectedCustomerId !== null &&
                loadingTempData === false &&
                tempData !== null &&
                currentQuestionnaireId === null &&
                currentlyLoadedQuestionnaire !== null &&
                current.step.step === DataInputSteps.PersonalData && (
                  <Stack
                    direction="column"
                    gap={2}
                    display="flex"
                    flexWrap="wrap"
                    minWidth="100%"
                    width="100%"
                  >
                    <KvcFormStack>
                      <FormFieldRoot
                        fields={currentlyLoadedQuestionnaire}
                        state={tempData}
                        onChange={(newPatchedState) => {
                          setTempData(newPatchedState);
                        }}
                        caseData={caseData}
                        caseGroupData={caseGroupData}
                        inputDisabled={caseDataInputBlocked === true}
                      ></FormFieldRoot>
                    </KvcFormStack>
                  </Stack>
                )}

              {selectedCustomerId !== null &&
                loadingTempData === false &&
                tempData !== null &&
                currentQuestionnaireId !== null &&
                currentlyLoadedQuestionnaire !== null &&
                current.step.substep !==
                  KvcInputSubStepType.formSegmentation && (
                  <Stack
                    direction="column"
                    gap={2}
                    display="flex"
                    flexWrap="wrap"
                    minWidth="100%"
                    width="100%"
                  >
                    <KvcFormStack>
                      <FormFieldRoot
                        fields={currentlyLoadedQuestionnaire}
                        state={tempData}
                        onChange={(newData) => {
                          if (!equal(tempData, newData)) {
                            setTempData(newData);
                          } else {
                            console.debug('skipping equal update');
                          }
                        }}
                        caseData={caseData}
                        caseGroupData={caseGroupData}
                        inputDisabled={caseDataInputBlocked === true}
                      ></FormFieldRoot>
                    </KvcFormStack>
                    {/* <Stack direction="row">
                      <Button type="submit">Speichern</Button>
                    </Stack> */}
                  </Stack>
                )}

              {/* {selectedCustomerId !== null &&
                loadingTempData === false &&
                tempData !== null &&
                currentlyLoadedQuestionnaire !== null &&
                selectedStep === DataInputSteps.HealthQuestions && (
                  <Stack
                    direction="column"
                    gap={2}
                    display="flex"
                    flexWrap="wrap"
                    minWidth="100%"
                    width="100%"
                  >
                    <div>☢️☢️☢️☢️☢️☢️☢️☢️☢️☢️☢️☢️</div>
                    <KvcFormStack>
                      <FormFieldRoot
                        fields={currentlyLoadedQuestionnaire?.[0]?.items}
                        state={tempData}
                        onChange={(newPatchedState) => {
                          setTempData(newPatchedState);
                        }}
                      ></FormFieldRoot>
                    </KvcFormStack>
                    <Stack direction="row">
                      <Button type="submit">Speichern</Button>
                    </Stack>
                  </Stack>
                )} */}

              {selectedCustomerId !== null &&
                loadingTempData === false &&
                tempData !== null &&
                currentlyLoadedQuestionnaire !== null &&
                typeof current.substep?.id === 'string' &&
                current.step.substep === KvcInputSubStepType.formSegmentation &&
                currentlyLoadedQuestionnaire?.filter(
                  (it: any) => it.id === current.substep?.id
                ).length > 0 && (
                  <Stack
                    direction="column"
                    gap={2}
                    display="flex"
                    flexWrap="wrap"
                    minWidth="100%"
                    width="100%"
                  >
                    <KvcFormStack>
                      <FormFieldRoot
                        fields={currentlyLoadedQuestionnaire?.filter(
                          (it: any) => it.id === current.substep?.id
                        )}
                        state={{
                          [current.substep?.id]: tempData[current.substep?.id],
                        }}
                        onChange={(newPatchedState) => {
                          setTempData((prev: any) => ({
                            ...(prev || {}),
                            ...newPatchedState,
                          }));
                        }}
                        caseData={caseData}
                        inputDisabled={caseDataInputBlocked === true}
                      ></FormFieldRoot>
                    </KvcFormStack>
                    {/* <Stack direction="row">
                      <Button
                        onClick={() => {
                          setHighlightInvalidFields(true);
                        }}
                        type="submit"
                      >
                        Speichern
                      </Button>
                    </Stack> */}
                  </Stack>
                )}

              {/* {selectedCustomerId !== null && nextStep !== null && (
                <Stack
                  direction="row"
                  display="flex"
                  justifyContent="right"
                  paddingRight={'20px'}
                  marginTop={8}
                >
                  <Button
                    type="submit"
                    onClick={(e) => {
                      setCurrent({ step: nextStep });
                    }}
                  >
                    <Typography fontWeight={700}>
                      {t('form.proceed_to_next_STEP')}{' '}
                      {t(
                        `form.basisdaten.personliche_daten.steps.${nextStep.step}`
                      )}
                    </Typography>
                  </Button>
                </Stack>
              )} */}

              {selectedCustomerId !== null && nextStepAndSubstep !== null && (
                <Stack
                  direction="row"
                  display="flex"
                  justifyContent="right"
                  paddingRight={'20px'}
                  marginTop={8}
                  minWidth="100%"
                  width="100%"
                  flexGrow={1}
                >
                  <Button
                    onClick={() => {
                      setCurrent(nextStepAndSubstep);
                    }}
                  >
                    <Typography fontWeight={700}>
                      {t('form.proceed_to_next_STEP')}
                      {current.step.step !== nextStepAndSubstep.step.step &&
                        ` ${t('form.basisdaten.personliche_daten.steps.' + nextStepAndSubstep.step.step)}`}
                      {current.step.step === nextStepAndSubstep.step.step &&
                        typeof nextStepAndSubstep.substep?.label === 'string' &&
                        ` ${t(nextStepAndSubstep.substep?.label)}`}

                      {/* {`Proceed ${nextStepAndSubstep?.step.step} ${nextStepAndSubstep?.substep?.label}`} */}
                    </Typography>
                  </Button>
                </Stack>
              )}

              {/* {selectedCustomerId !== null &&
                nextCustomer !== null &&
                caseGroupData.customers.length > 1 && (
                  <Stack
                    direction="row"
                    display="flex"
                    justifyContent="right"
                    paddingRight={'20px'}
                    marginTop={8}
                  >
                    <Button
                      type="submit"
                      onClick={(e) => {
                        if (e.currentTarget.form?.checkValidity()) {
                          setCurrent({
                            step: dataInputConfiguration.steps[0],
                            substep:
                              dataInputConfiguration.steps[0]?.substeps?.[0],
                          });
                          setSelectedCustomerId(nextCustomer.customerId);
                        }
                      }}
                    >
                      <Typography fontWeight={700}>
                        {t('form.proceed_to_next_CUSTOMER')}
                        {` ${nextCustomer?.firstName} ${nextCustomer?.lastName}`}
                      </Typography>
                    </Button>
                  </Stack>
                )} */}

              {props.createCustomersOnly === true &&
                selectedCustomerId !== null &&
                nextCustomer === null && (
                  <Stack
                    direction="row"
                    display="flex"
                    justifyContent="right"
                    paddingRight={'20px'}
                    marginTop={8}
                  >
                    <Button
                      type="submit"
                      onClick={(e) => {
                        if (e.currentTarget.form?.checkValidity()) {
                          router.push(
                            `/dateneingabe/case/${props.caseGroupNumber}`
                          );
                        }
                      }}
                    >
                      <Typography fontWeight={700}>
                        {t('form.proceed_to_data_input')}
                      </Typography>
                    </Button>
                  </Stack>
                )}

              {/* {groupValidationSuccessful && (
                <Stack direction="column" display="flex" marginTop={10}>
                  <Typography>{t('form.data_input_complete')}</Typography>
                  <Stack
                    direction="row"
                    display="flex"
                    justifyContent="right"
                    paddingRight={'20px'}
                    marginTop={1}
                  >
                    <Button
                      type="submit"
                      onClick={(e) => {
                        if (e.currentTarget.form?.checkValidity()) {
                          router.push(
                            `/dateneingabe/beenden/case/${caseData.caseNumber}`
                          );
                        }
                      }}
                    >
                      <Typography fontWeight={700}>
                        Dateneingabe abschliessen
                      </Typography>
                    </Button>
                  </Stack>
                </Stack>
              )} */}

              {/* {caseDataInputComplete && selectedCustomerId !== null && (
                <Stack direction="column" display="flex" marginTop={10}>
                  <Typography>{t('form.data_input_complete')}</Typography>
                  <Stack
                    direction="row"
                    display="flex"
                    justifyContent="right"
                    paddingRight={'20px'}
                    marginTop={1}
                  >
                    <Button
                      type="submit"
                      onClick={(e) => {
                        if (e.currentTarget.form?.checkValidity()) {
                          router.push(
                            `/dateneingabe/beenden/case/${caseData.caseNumber}`
                          );
                        }
                      }}
                    >
                      <Typography fontWeight={700}>
                        {`Dateneingabe für ${caseData.customer.firstName} ${caseData.customer.lastName} abschliessen`}
                      </Typography>
                    </Button>
                  </Stack>
                </Stack>
              )} */}
              <div style={{ height: '300px' }}></div>
            </form>
          </Stack>
        )}
      </Stack>
      {showDebugData && (
        <div>
          <div>Case data:</div>
          <div>{JSON.stringify(caseData)}</div>
          <div>Validation result:</div>
          <div>Loaded Questionnaire: {currentQuestionnaireFormId}</div>
          <div>{JSON.stringify(currentlyLoadedQuestionnaire)}</div>
        </div>
      )}
      {showCreateCustomerModal && (
        <KvModal
          onClose={() => {
            setShowCreateCustomerModal(false);
          }}
        >
          <Stack
            direction="column"
            gap={2}
            textAlign="center"
            justifyContent="center"
          >
            <Stack direction="column" sx={{ width: 550, maxWidth: '100%' }}>
              <CustomerCreateForm
                sx={{ width: '100%' }}
                caseGroupId={caseGroupData.caseGroupId}
                onSuccess={() => {
                  refetchCaseGroupData();
                  setShowCreateCustomerModal(false);
                }}
                onCancelClick={() => {
                  setShowCreateCustomerModal(false);
                }}
              />
            </Stack>
          </Stack>
        </KvModal>
      )}

      {/* TODO: restore? */}

      {/* {deleteCustomerId !== null && (
        <KvModal
          onClose={() => {
            setDeleteCustomerId(null);
          }}
        >
          <Stack
            direction="column"
            gap={2}
            textAlign="center"
            justifyContent="center"
          >
            <Typography variant="h5" color="var(--teal)" fontWeight={700}>
              {t.rich(
                'modal.delete_customer.headline',
                intlTranslationRichHelper
              )}
            </Typography>
            <Typography fontWeight={700}>
              {
                caseData.caseCustomer.find(
                  (it) => it.caseCustomerId === deleteCustomerId
                )?.customer?.firstName
              }{' '}
              {
                caseData.caseCustomer.find(
                  (it) => it.caseCustomerId === deleteCustomerId
                )?.customer?.lastName
              }
            </Typography>
            <Stack direction="row" gap={1} justifyContent="center">
              <Typography>{t('modal.delete_customer.dateOfBirth')}:</Typography>
              <Typography fontWeight={700}>
                {caseData.caseCustomer
                  .find((it) => it.caseCustomerId === deleteCustomerId)
                  ?.customer?.dateOfBirth?.toDateString()}
              </Typography>
            </Stack>
            <Stack direction="row" gap={1} justifyContent="center">
              <Button
                disabled={deleteCustomerAndCaseCustomerPending}
                onClick={() => {
                  deleteCustomerAndCaseCustomer({
                    customerId: deleteCustomerId,
                  });
                  setDeleteCustomerId(null);
                }}
              >
                {t('modal.delete_customer.yes_button_label')}
              </Button>
              <Button
                onClick={() => {
                  setDeleteCustomerId(null);
                }}
              >
                {t('modal.delete_customer.no_button_label')}
              </Button>
            </Stack>
          </Stack>
        </KvModal>
      )} */}

      {editCustomerId !== null && (
        <KvModal
          onClose={() => {
            setEditCustomerId(null);
          }}
        >
          <Stack
            direction="column"
            gap={2}
            textAlign="center"
            justifyContent="center"
          >
            <Stack direction="column" sx={{ width: 600, maxWidth: '100%' }}>
              <CustomerEditForm
                customerId={editCustomerId}
                onSuccess={() => {
                  setEditCustomerId(null);
                  refetchCaseGroupData();
                }}
                onCancelClick={() => {
                  setEditCustomerId(null);
                }}
              />
            </Stack>
          </Stack>
        </KvModal>
      )}
    </Stack>
  );
}
