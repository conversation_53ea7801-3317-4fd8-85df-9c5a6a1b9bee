'use client';
import VisibilityIcon from '@mui/icons-material/Visibility';
import {
  Button,
  IconButton,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { type Customer } from '@prisma/client';
import { useTranslations } from 'next-intl';
import { type ReactNode, useState } from 'react';

import {
  formatDateToDDMMYYYY,
  ObfuscateDDMMYYYY,
  ObfuscateNumber,
  ObfuscateString,
} from '@/utils/dataObfuscation';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface Props {
  customer: Customer;
  sx?: SxProps<Theme>;
}

export function CustomerImportForm(props: Props) {
  const t = useTranslations();
  const [obfuscateCustomerData, setObfuscateCustomerData] = useState(true);
  //   TODO: actually accept the customer number here and save it.
  const customerNumber = 'GSKR-2934973275';
  return (
    <Stack
      direction="column"
      display="flex"
      justifyContent="center"
      flexGrow={1}
      gap={2}
      sx={{
        '>*': {
          textAlign: 'center',
        },
        borderRight: 'solid 1px lightgray',
        width: '50%',
        padding: 5,
        ...props.sx,
      }}
    >
      <Typography
        color={'var(--teal)'}
        fontWeight={700}
        variant="h5"
        textAlign="center"
      >
        {t.rich('form.existing_customer.headline', intlTranslationRichHelper)}
      </Typography>
      <Stack direction="row" display="flex" justifyContent="center" gap={1}>
        <Typography fontWeight={700}>
          {ObfuscateString(props.customer.firstName, obfuscateCustomerData)}
        </Typography>
        <Typography fontWeight={700}>
          {ObfuscateString(props.customer.lastName, obfuscateCustomerData)}
        </Typography>
        <IconButton
          sx={{
            marginTop: -1,
          }}
          onClick={() => {
            setObfuscateCustomerData(!obfuscateCustomerData);
          }}
        >
          <VisibilityIcon></VisibilityIcon>
        </IconButton>
      </Stack>

      <Stack direction="column" display="flex" justifyContent="center">
        <MixedStyleTextLine
          textLeft={t.rich(
            'form.basisdaten.personliche_daten.stammdaten.email',
            intlTranslationRichHelper
          )}
          textRight={ObfuscateString(
            props.customer.email || '',
            obfuscateCustomerData
          )}
        ></MixedStyleTextLine>

        <MixedStyleTextLine
          textLeft={t.rich(
            'form.basisdaten.personliche_daten.stammdaten.phone_number',
            intlTranslationRichHelper
          )}
          textRight={ObfuscateNumber(
            props.customer.phoneNumber || '',
            obfuscateCustomerData
          )}
        ></MixedStyleTextLine>
      </Stack>

      <Stack direction="column" display="flex" justifyContent="center">
        <MixedStyleTextLine
          textLeft={t.rich(
            'form.basisdaten.personliche_daten.stammdaten.birthdate',
            intlTranslationRichHelper
          )}
          textRight={ObfuscateDDMMYYYY(
            formatDateToDDMMYYYY(props.customer.dateOfBirth),
            obfuscateCustomerData
          )}
        ></MixedStyleTextLine>

        <MixedStyleTextLine
          textLeft={t.rich(
            'form.existing_customer.customer_number',
            intlTranslationRichHelper
          )}
          textRight={ObfuscateString(
            ObfuscateNumber(customerNumber),
            obfuscateCustomerData
          )}
        ></MixedStyleTextLine>
      </Stack>

      <Stack
        direction="row"
        display="flex"
        justifyContent="center"
        sx={{
          marginTop: 'auto',
        }}
      >
        <Button sx={{ fontWeight: 700 }}>
          {t.rich(
            'form.existing_customer.proceed_with_this_customer',
            intlTranslationRichHelper
          )}
        </Button>
      </Stack>
    </Stack>
  );
}

interface MixedStyleTextLineProps {
  duck?: string;
  textLeft: ReactNode | string;
  textRight: ReactNode | string;
}

function MixedStyleTextLine(props: MixedStyleTextLineProps) {
  return (
    <Stack direction="row" display="flex" justifyContent="center">
      <Typography marginRight={1} color="gray">
        {props.textLeft}:{' '}
      </Typography>
      <Typography color="black">{props.textRight}</Typography>
    </Stack>
  );
}
