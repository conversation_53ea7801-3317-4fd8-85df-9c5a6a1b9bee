import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, IconButton, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { type PotentialInsuranceFromCaseGroup } from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface PotentialInsuranceCreationListItemProps {
  potentialInsurance: PotentialInsuranceFromCaseGroup;
  onDelete?: (potentialInsuranceId: number) => void;
  onAdd?: (potentialInsuranceId: number) => void;
}

export function PotentialInsuranceCreationListItem(
  props: PotentialInsuranceCreationListItemProps
) {
  const t = useTranslations();
  return (
    <Box
      sx={{
        backgroundColor: 'var(--gray-background)',
        padding: 2,
        borderRadius: '7px',
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        width: '100%',
      }}
      gap={2}
    >
      <Typography width="100px" color="var(--teal-dark)" fontWeight={700}>
        {props.potentialInsurance.insurerName}
      </Typography>

      <Stack
        direction="row"
        sx={{
          width: '100px',
        }}
      >
        <Typography sx={{ marginRight: 1 }}>
          {t.rich(
            'form.insurancePlanSelection.insurance_plan_selection.insurance_plan',
            intlTranslationRichHelper
          )}
          :
        </Typography>
        <Typography sx={{ fontWeight: 700 }}>
          {props.potentialInsurance.productName.trim().length <= 0
            ? '-'
            : props.potentialInsurance.productName}
        </Typography>
      </Stack>

      {typeof props.onDelete !== 'undefined' && (
        <IconButton
          onClick={() => {
            props.onDelete?.(props.potentialInsurance.potentialInsuranceId);
          }}
          sx={{ marginTop: -1 }}
        >
          <DeleteIcon />
        </IconButton>
      )}

      {typeof props.onAdd !== 'undefined' && (
        <IconButton
          onClick={() => {
            props.onAdd?.(props.potentialInsurance.potentialInsuranceId);
          }}
          sx={{ marginTop: -1 }}
        >
          <AddCircleOutlineIcon />
        </IconButton>
      )}
    </Box>
  );
}
