'use client';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  type SxProps,
  TextField,
  type Theme,
  Typography,
} from '@mui/material';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';
import React from 'react';

import { KvcSecondaryButton } from '@/components/buttons/KvcSecondaryButton';
import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import { classes } from '@/components/forms/cssClasses';
import { KvModal } from '@/components/modal/modal';
import { ConfirmModal } from '@/modules/customer/ui/components/ConfirmModal';
import { useTRPC } from '@/trpc/client';
import {
  type CaseFromGetCaseGroupWithAllRelationsResponse,
  type CaseWithAllTheRelations,
  type GetCaseGroupWithAllRelationsResponse,
} from '@/utils/helperTypes';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { PotentialInsuranceCreationListItem } from './PotentialInsuranceCreationListItem';
import { PotentialInsurancePicker } from './PotentialInsurancePicker';

export interface Props {
  caseData: CaseWithAllTheRelations;
  required: boolean;
  sx?: SxProps<Theme>;
  caseGroupData?: GetCaseGroupWithAllRelationsResponse;
}

export function PotentialInsuranceCreationList(props: Props) {
  const t = useTranslations();
  const trpc = useTRPC();

  const {
    data: userIsAdmin,
    refetch: refetchCaseData,
    isRefetching: caseDataIsLoading,
  } = useSuspenseQuery(trpc.administration.userIsAdmin.queryOptions());

  const minCountWhenRequired = 1;
  const maxCount: number | null = userIsAdmin === true ? null : 5;

  const [deletePotentialInsuranceId, setDeletePotentialInsuranceId] = useState<
    number | null
  >(null);
  const insuranceCompaniesStringArray = useMemo<string[]>(() => {
    const rawArray = t.raw('kvcare_private_insurance_provider_list.values');
    if (rawArray == null || !Array.isArray(rawArray) || rawArray.length <= 0) {
      return [];
    }
    return rawArray as string[];
  }, [t]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newInsuranceCompany, setNewInsuranceCompany] = useState<string | null>(
    null
  );
  const [newInsurancePlan, setNewInsurancePlan] = useState<string | null>(null);

  const addAtLeast1insurancePlanHint = useMemo(() => {
    return t(
      'form.insurancePlanSelection.insurance_plan_selection.hint_please_add_at_least_1_insurance_plan_or_allow_suggestions'
    );
  }, [t]);

  const casesInGroupWithPotentialInsurance = useMemo<
    CaseFromGetCaseGroupWithAllRelationsResponse[]
  >(() => {
    return (
      props.caseGroupData?.cases.filter(
        (it) =>
          it.caseId !== props.caseData.caseId &&
          it.potentialInsurances.length > 0
      ) || []
    );
  }, [props.caseGroupData, props.caseData]);

  const {
    data: potentialInsurances,
    isPending: potentialInsurancesPending,
    refetch: refetchPotentialInsurance,
  } = useSuspenseQuery(
    trpc.potentialInsurance.getAllForCaseCreatedByAnyConsultant.queryOptions({
      caseId: props.caseData.caseId,
    })
  );

  const addingNewInsuranceDisabled =
    maxCount !== null && potentialInsurances.length >= maxCount;

  const {
    mutateAsync: addPotentialInsuranceToCase,
    isPending: addPotentialInsuranceToCasePending,
  } = useMutation(
    trpc.potentialInsurance.addPotentialInsuranceToCase.mutationOptions({
      onSuccess: () => {
        setShowAddModal(false);
        setNewInsuranceCompany(null);
        setNewInsurancePlan(null);
        refetchPotentialInsurance();
      },
      onError: (error) => {},
    })
  );

  const {
    mutateAsync: deletePotentialInsurance,
    isPending: deletePotentialInsuranceIsPending,
  } = useMutation(
    trpc.potentialInsurance.deleteOne.mutationOptions({
      onSuccess: () => {
        refetchPotentialInsurance();
      },
      onError: (error) => {},
    })
  );

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('onsubmit');
    if (newInsuranceCompany !== null) {
      addPotentialInsuranceToCase({
        caseId: props.caseData.caseId,
        data: {
          insurerName: newInsuranceCompany,
          productName: newInsurancePlan,
          caseId: props.caseData.caseId,
        },
      });
    } else {
      console.error('bad data');
    }
  };

  return (
    <>
      <Stack
        direction="column"
        gap={2}
        sx={{
          ...props.sx,
        }}
      >
        <Stack direction="column">
          <Typography color="var(--teal)" variant="h6" fontWeight={700}>
            {t.rich(
              'form.insurancePlanSelection.insurance_plan_selection.headline',
              intlTranslationRichHelper
            )}
          </Typography>
          <Typography>
            {t.rich(
              'form.insurancePlanSelection.insurance_plan_selection.body',
              intlTranslationRichHelper
            )}
          </Typography>
        </Stack>

        <Stack direction="column" display="flex" gap={2} sx={{ width: '100%' }}>
          {potentialInsurances.map((potentialInsurance, i) => (
            <PotentialInsuranceCreationListItem
              key={potentialInsurance.potentialInsuranceId}
              potentialInsurance={potentialInsurance}
              onDelete={() => {
                setDeletePotentialInsuranceId(
                  potentialInsurance.potentialInsuranceId
                );
              }}
            />
          ))}
        </Stack>

        <Stack direction="row" display="flex" justifyContent="center">
          <Stack direction="column">
            <KvcPlainTextButton
              disabled={addingNewInsuranceDisabled}
              onClick={() => {
                setShowAddModal(true);
              }}
              startIcon={
                <AddCircleOutlineIcon
                  sx={{
                    color: addingNewInsuranceDisabled ? 'gray' : 'primary.main',
                    marginRight: 1,
                  }}
                />
              }
            >
              {t(
                'form.insurancePlanSelection.insurance_plan_selection.insurance_plan_add_button_label'
              )}
            </KvcPlainTextButton>
            {props.required === true &&
              potentialInsurances.length < minCountWhenRequired && (
                <Stack direction="row" display="flex" justifyContent="left">
                  <input
                    style={{ maxHeight: 0, maxWidth: 0, opacity: 0 }}
                    required
                    onInvalid={(e) => {
                      e.currentTarget.setCustomValidity(
                        addAtLeast1insurancePlanHint
                      );
                    }}
                    onInput={(e) => {
                      e.currentTarget.setCustomValidity('');
                    }}
                  />
                </Stack>
              )}
          </Stack>
        </Stack>

        {deletePotentialInsuranceId !== null && (
          <ConfirmModal
            bodyTextTranslationKey="form.insurancePlanSelection.insurance_plan_delete_modal.body"
            confirmButtonLabelTranslationkey="form.insurancePlanSelection.insurance_plan_delete_modal.confirm_delete_button_label"
            cancelButtonLabelTranslationKey="form.insurancePlanSelection.insurance_plan_delete_modal.cancel_delete_button_label"
            onCloseModalClick={function (): void {
              setDeletePotentialInsuranceId(null);
            }}
            onConfirmButtonClick={function (): void {
              deletePotentialInsurance({
                potentialInsuranceId: deletePotentialInsuranceId,
              });
              setDeletePotentialInsuranceId(null);
            }}
            onCancelButtonClick={function (): void {
              setDeletePotentialInsuranceId(null);
            }}
            buttonsDisabled={deletePotentialInsuranceIsPending}
          ></ConfirmModal>
        )}

        {showAddModal && (
          <KvModal
            onClose={() => {
              setShowAddModal(false);
            }}
          >
            <Stack
              direction="column"
              gap={2}
              textAlign="center"
              justifyContent="center"
              width={400}
            >
              <Stack
                direction="column"
                gap={2}
                sx={{ width: 600, maxWidth: '100%' }}
              >
                <form onSubmit={onSubmit}>
                  <Stack direction="column" sx={{ gap: 2 }}>
                    <Typography variant="h6" fontWeight={700}>
                      {t(
                        'form.insurancePlanSelection.insurance_plan_selection.headline'
                      )}
                    </Typography>
                    <FormControl
                      variant="outlined"
                      className={classes.formField}
                      required={true}
                    >
                      <InputLabel htmlFor={'insuranceProvider'}>
                        {t(
                          'form.insurancePlanSelection.insurance_plan_selection.insurance_company'
                        )}
                      </InputLabel>
                      <Select
                        id={'insuranceProvider'}
                        label={t(
                          'form.insurancePlanSelection.insurance_plan_selection.insurance_company'
                        )}
                        name={'insuranceProvider'}
                        value={newInsuranceCompany}
                        onChange={(e) => {
                          console.log('dropdown value change', e.target.value);
                          setNewInsuranceCompany(e.target.value);
                        }}
                        required={true}
                        slotProps={{
                          input: {
                            required: true,
                          },
                        }}
                      >
                        {insuranceCompaniesStringArray?.map(
                          (insuranceCompanyName) => (
                            <MenuItem
                              key={insuranceCompanyName}
                              className={classes.dropdownOption}
                              value={insuranceCompanyName}
                            >
                              {insuranceCompanyName}
                            </MenuItem>
                          )
                        )}
                      </Select>
                    </FormControl>

                    <TextField
                      className={classes.formField}
                      label={t(
                        'form.insurancePlanSelection.insurance_plan_selection.insurance_plan'
                      )}
                      name={'insurancePlan'}
                      value={newInsurancePlan}
                      onChange={(e) => setNewInsurancePlan(e.target.value)}
                      required={false}
                    />

                    <Stack direction="row" gap={2} justifyContent="right">
                      <Button
                        type="submit"
                        disabled={
                          potentialInsurancesPending ||
                          addPotentialInsuranceToCasePending
                        }
                      >
                        {t(
                          'form.insurancePlanSelection.insurance_plan_selection.insurance_plan_add_button_label'
                        )}
                      </Button>
                      <KvcSecondaryButton
                        onClick={() => {
                          setShowAddModal(false);
                        }}
                        disabled={
                          potentialInsurancesPending ||
                          addPotentialInsuranceToCasePending
                        }
                      >
                        {t('common.cancel_button_label')}
                      </KvcSecondaryButton>
                    </Stack>
                  </Stack>
                </form>

                {casesInGroupWithPotentialInsurance.length > 0 && (
                  <Stack
                    direction="column"
                    display="flex"
                    gap={2}
                    sx={{ width: '100%' }}
                  >
                    <PotentialInsurancePicker
                      excludeCaseId={props.caseData.caseId}
                      cases={casesInGroupWithPotentialInsurance}
                      onClick={(potentialInsurance) => {
                        addPotentialInsuranceToCase({
                          caseId: props.caseData.caseId,
                          data: {
                            caseId: props.caseData.caseId,
                            insurerName: potentialInsurance.insurerName,
                            productName: potentialInsurance.productName,
                          },
                        });
                      }}
                    ></PotentialInsurancePicker>
                  </Stack>
                )}
              </Stack>
            </Stack>
          </KvModal>
        )}
      </Stack>
    </>
  );
}
