import { useEffect, useState } from "react";
import { Box, Button, TextField, Tooltip, Typography } from '@mui/material';
import StaticBox from "./box/StaticBox";

interface User {
    username: string,
    email: string,
    company_name: string,
    street: string,
    house_number: string,
    postal_code: string,
    city: string,
    url: string,
    telephone_number: string
}

interface FormPasswordData {
    currentPassword?: string;
    password?: string;
    passwordConfirmation?: string;
}

export default function UserSettings() {

    const [formData, setFormData] = useState<User>()
    const [formPasswordData, setFormPasswordData] = useState<FormPasswordData>()

    useEffect(() => {
        fetch(`/api/user/me`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        })
            .then(res => res.json())
            .then(data => setFormData(data))
    }, [])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        fetch(`/api/user/changePassword`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
            body: JSON.stringify({
                'currentPassword': formPasswordData?.currentPassword,
                'password': formPasswordData?.password,
                'passwordConfirmation': formPasswordData?.passwordConfirmation
            })
        })
            .then(async (response) => {
                const json = await response.json();
                if (response.ok) {
                    alert("Neues Passwort erfolgreich gesetzt.")
                    localStorage.setItem("jwt", json.jwt)
                    location.reload()
                } else {
                    alert("Fehler beim setzen des neuen Passworts.")
                    location.reload()
                }
            })
    }

    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormPasswordData({
            ...formPasswordData,
            [name]: value,
        });
    };

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Benutzereinstellungen
            </Typography>
            <form>
                <div className='space-y-4 mx-auto relative'>
                    <StaticBox title='Passwort'>
                        <div className='grid grid-cols-2 gap-2'>
                            <Tooltip title="Geben Sie Ihren Benutzernamen ein.">
                                <TextField
                                    type="text"
                                    label="Benutzername"
                                    name="username"
                                    className="w-full p-2"
                                    value={formData?.username}
                                    disabled
                                    required
                                    InputLabelProps={{
                                        shrink: true,
                                    }}
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie Ihre E-Mail Adresse ein.">
                                <TextField
                                    type="email"
                                    label="E-Mail"
                                    name="email"
                                    className="w-full p-2"
                                    value={formData?.email}
                                    disabled
                                    required
                                    InputLabelProps={{
                                        shrink: true,
                                    }}
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 pt-4'>
                            <Tooltip title="Geben Sie Ihr altes Passwort ein.">
                                <TextField
                                    type="password"
                                    label="Altes Passwort"
                                    name="currentPassword"
                                    className="w-full p-2"
                                    value={formPasswordData?.currentPassword}
                                    onChange={handlePasswordChange}
                                    required
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 pt-4'>
                            <Tooltip title="Geben Sie Ihr neues Passwort ein.">
                                <TextField
                                    type="password"
                                    label="Passwort"
                                    name="password"
                                    className="w-full p-2"
                                    value={formPasswordData?.password}
                                    onChange={handlePasswordChange}
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Wiederholen Sie Ihr Passwort.">
                                <TextField
                                    type="password"
                                    label="Passwort Wiederholen"
                                    name="passwordConfirmation"
                                    className="w-full p-2"
                                    value={formPasswordData?.passwordConfirmation}
                                    onChange={handlePasswordChange}
                                    required
                                />
                            </Tooltip>
                        </div>
                    </StaticBox>
                    <div className='flex justify-end'>
                        <Tooltip title="Einstellungen sichern.">
                            <Button
                                type="submit"
                                variant='contained'
                                color='secondary'
                                onClick={handleSubmit}>
                                Sichern
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </form>
            <form>
                <div className='space-y-4 mx-auto relative'>
                    <StaticBox title='Anschrift'>
                        <div className="grid gap-y-4">
                            <div className='grid grid-cols-2 gap-2'>
                                <Tooltip title="Geben Sie Ihre Unternehmensname ein.">
                                    <TextField
                                        type="text"
                                        label="Unternehmensname"
                                        name="company_name"
                                        className="w-full p-2"
                                        value={formData?.company_name}
                                        required
                                        disabled
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                            </div>
                            <div className='grid grid-cols-2 gap-2'>
                                <Tooltip title="Geben Sie Ihre Straße ein.">
                                    <TextField
                                        type="text"
                                        label="Straße"
                                        name="street"
                                        className="w-full p-2"
                                        value={formData?.street}
                                        disabled
                                        required
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                                <Tooltip title="Geben Sie Ihre Hausmunner ein.">
                                    <TextField
                                        type="text"
                                        label="Hausmunner"
                                        name="house_number"
                                        className="w-full p-2"
                                        value={formData?.house_number}
                                        disabled
                                        required
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                            </div>
                            <div className='grid grid-cols-2 gap-2 pt-4'>
                                <Tooltip title="Geben Sie Ihre Postleitzahl ein.">
                                    <TextField
                                        type="text"
                                        label="Postleitzahl"
                                        name="postal_code"
                                        className="w-full p-2"
                                        value={formData?.postal_code}
                                        required
                                        disabled
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                                <Tooltip title="Wiederholen Sie Ihren Ort ein.">
                                    <TextField
                                        type="text"
                                        label="Ort"
                                        name="city"
                                        className="w-full p-2"
                                        value={formData?.city}
                                        required
                                        disabled
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                            </div>
                            <div className='grid grid-cols-2 gap-2 pt-4'>
                                <Tooltip title="Geben Sie Ihre Webseite ein.">
                                    <TextField
                                        type="url"
                                        label="Webseite"
                                        name="url"
                                        className="w-full p-2"
                                        value={formData?.url}
                                        required
                                        disabled
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                                <Tooltip title="Wiederholen Sie Ihre Telefonnummer ein.">
                                    <TextField
                                        type="tel"
                                        label="Telefonnummer"
                                        name="telephone_number"
                                        className="w-full p-2"
                                        value={formData?.telephone_number}
                                        required
                                        disabled
                                        InputLabelProps={{
                                            shrink: true,
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        </div>
                    </StaticBox>
                </div>
            </form>
        </Box>
    );
}
