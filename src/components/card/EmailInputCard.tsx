"use client";

import { useState } from "react";
import {
    Box,
    TextField,
    Button,
    Typography,
    Alert,
    CircularProgress,
} from "@mui/material";
import { useTranslations } from "next-intl";
import * as React from "react";
import {isEmail} from "@/utils/isEmail";

type Props = {
    onSubmit: (value: string) => Promise<void>
    translationsNamespace: string
}
export default function EmailInputCard({ onSubmit, translationsNamespace }: Props) {
    const unscopedT = useTranslations();
    const t = useTranslations(translationsNamespace);

    const [input, setInput] = useState("");
    const [loading, setLoading] = useState(false);
    const [validationErrorMessage, setValidationErrorMessage] = useState<string | null>(null);
    const [submitSuccessMessage, setSubmitSuccessMessage] = useState<string | null>(null);
    const [submitErrorMessage, setSubmitErrorMessage] = useState<string | null>(null);

    const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
        const newValue = e.target.value
        setInput(newValue)

        if (!isEmail(newValue)) {
            setValidationErrorMessage(
                unscopedT('errorMessage.validation.email')
            )
        } else if (validationErrorMessage) {
            setValidationErrorMessage(null)
        }
    }
    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        setLoading(true);
        setSubmitErrorMessage(null);
        setSubmitSuccessMessage(null);

        try {
            await onSubmit(input)

            setSubmitSuccessMessage(t("submitSuccessMessage"));
            setInput("");
        } catch (error: any) {
            console.error(error)

            if (error.code === 'CustomerNotFound') {
                setSubmitErrorMessage(t('submitErrorMessage.customerNotFound'))
            } else {
                setSubmitErrorMessage(t("submitErrorMessage.generic"));
            }
        } finally {
            setLoading(false);
        }
    }

    return (
        <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{
                width: "100%",
                maxWidth: 400,
                p: 4,
                bgcolor: "background.paper",
                borderRadius: 3,
                boxShadow: "0 4px 12px rgba(0,0,0,0.06)",
            }}
        >
            <Typography variant="h5" align="center" gutterBottom>
                {t("title")}
            </Typography>
            <Typography
                variant="body2"
                color="text.secondary"
                align="center"
                mb={3}
            >
                {t("message")}
            </Typography>

            <TextField
                type="email"
                label={t("inputLabel")}
                value={input}
                onChange={handleChange}
                error={Boolean(validationErrorMessage)}
                helperText={validationErrorMessage}
                required
                fullWidth
            />

            <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={loading || !input || Boolean(validationErrorMessage)}
                sx={{ mt: 2, mb: 2 }}
            >
                {loading ? (
                    <CircularProgress size={24} color="inherit" />
                ) : (
                    `${t("button")}`
                )}
            </Button>

            {submitSuccessMessage && (
                <Alert severity="success" sx={{ mt: 2 }}>
                    {submitSuccessMessage}
                </Alert>
            )}
            {submitErrorMessage && (
                <Alert severity="error" sx={{ mt: 2 }}>
                    {submitErrorMessage}
                </Alert>
            )}
        </Box>
    );
}
