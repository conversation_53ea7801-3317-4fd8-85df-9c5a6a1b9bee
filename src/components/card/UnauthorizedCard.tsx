"use client";

import { Box, Typography, <PERSON><PERSON>, Alert } from "@mui/material";
import LockIcon from "@mui/icons-material/Lock";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

type Props = {
    buttonUrl: string
}
export default function UnauthorizedCard({ buttonUrl }: Props) {
  const router = useRouter();
  const t = useTranslations('unauthorized');

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: 400,
        p: 4,
        bgcolor: "background.paper",
        borderRadius: 3,
        boxShadow: "0 4px 12px rgba(0,0,0,0.06)",
        textAlign: "center",
      }}
    >
      <LockIcon sx={{ fontSize: 64, color: "error.main", mb: 2 }} />
      <Typography variant="h5" gutterBottom>
        {t("title")}
      </Typography>
      <Typography variant="body2" color="text.secondary" mb={3}>
        {t("message")}
      </Typography>

      <Alert severity="error" sx={{ mb: 3 }}>
        {t("alert")}
      </Alert>

      <Button
        variant="contained"
        color="primary"
        fullWidth
        onClick={() => router.push(buttonUrl)}
      >
        {t("button")}
      </Button>
    </Box>
  );
}
