// src/components/ReportForm.tsx
import { TrashIcon } from '@heroicons/react/24/outline';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import ContractInformationBox from '@/components/box/ContractInformationBox';
import CustomerInformationBox from '@/components/box/CustomerInformationBox';
import MultiFormBox from '@/components/box/MultiFormBox';
import StaticBox from '@/components/box/StaticBox';
import { VisuallyHiddenInput } from '@/components/common';
import { type Contract, type Customer } from '@/generated/prisma-postgres';
import {
  determineShownFields,
  type FormState,
} from '@/inline-dependencies/FancyForm';
import { type FormConfig } from '@/types';
import { apiFetch } from '@/utils/apiFetch';

import { formConfigsRegistry } from './configs';

interface ReportFormProps {
  customerNumber?: string;
  contractNumber?: string;
  agencyNumber?: string;
  reportNumber?: string;
}

export const ReportForm: React.FC<ReportFormProps> = ({
  customerNumber,
  contractNumber,
  agencyNumber,
  reportNumber,
}) => {
  const [formData, setFormData] = useState({
    customerNumber: customerNumber || '',
    contractNumber: contractNumber || '',
    agencyNumber: agencyNumber || '',
    externalReportNumber: '',
    damageDate: '',
    damageLocation: '',
    iban: '',
    coveredRisk: '',
    text: '',
  });

  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formsMemory, setFormsMemory] = useState<Record<string, FormState>>({}); // the last value of every field regardless if it's shown or not
  const [dirtyFlags, setDirtyFlags] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)

      try {
        if (reportNumber != undefined) {
          setEditMode(true);
          const reportResponse = await apiFetch(
            `/api/reports/${reportNumber}`,
            {
              method: 'GET',
              raw: true,
            }
          );

          if (!reportResponse.ok) throw new Error('Failed to fetch');
          const reportData = await reportResponse.json();
          setFormData(reportData);
          setFormsMemory(reportData.dataRaw);
          // eslint-disable-next-line react-hooks/exhaustive-deps
          customerNumber = reportData.customerNumber;
          // eslint-disable-next-line react-hooks/exhaustive-deps
          contractNumber = reportData.contractNumber;
        }

        if (contractNumber) {
          const customerResponse = await apiFetch(
            `/api/customers/${customerNumber}`,
            {
              method: 'GET',
              raw: true,
            }
          );

          const contractResponse = await apiFetch(
            `/api/contracts/${contractNumber}`,
            {
              method: 'GET',
              raw: true,
            }
          );

          if (!customerResponse.ok || !contractResponse.ok)
            throw new Error('Failed to fetch data');

          const customerData = await customerResponse.json();
          const contractData = await contractResponse.json();

          setCustomer(customerData);
          setContract(contractData);
        }
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    void fetchData();
  }, [customerNumber, editMode]);

  const handleFormChange = (formKey: string, patch: Partial<FormState>) => {
    setFormsMemory((prev) => ({
      ...prev,
      [formKey]: {
        ...prev[formKey],
        ...patch,
      },
    }));

    setDirtyFlags((prev) => ({ ...prev, [formKey]: true }));
  };

  const [files, setFiles] = useState<File[]>([]);

  if (loading) {
    return (
        <div
            style={{ display: 'flex', justifyContent: 'center', marginTop: '200px' }}
        >
          <CircularProgress size='50px' />
        </div>
    );
  }
  if (!customer) return <div>Kundendaten nicht gefunden.</div>;
  if (!contract) {
    return <div>Vertragsdaten nicht gefunden.</div>;
  }

  // Remove file at the given index
  const handleRemoveFile = (fileIndex: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== fileIndex));
  };
  // If user selects files
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      // Convert FileList to a real array of File
      const newFiles = Array.from(e.target.files);
      setFiles((prev) => [...prev, ...newFiles]);
    }
  };

  const formConfigs = formConfigsRegistry[contract.contractType!] ?? [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formStates = determineFormStates(formsMemory, formConfigs);

    const url = '/api/reports/create';
    const headers: Record<string, string> = {};

    let body: FormData | string;

    if (editMode) {
      headers['Content-Type'] = 'application/json';
      const updated = {
        ...formData,
        dataRaw: formStates,
      };
      body = JSON.stringify({ reportData: updated });
    } else {
      // build real FormData
      const formDataToSend = new FormData();
      const payload = {
        ...formData,
        dataRaw: formStates,
      };

      Object.entries(payload).forEach(([key, value]) => {
        // if you have nested objects/arrays, JSON.stringify them
        formDataToSend.append(
          key,
          typeof value === 'object' ? JSON.stringify(value) : String(value)
        );
      });
      files.forEach((file) => formDataToSend.append('file', file));

      body = formDataToSend;
      // **do not** set Content-Type here
    }

    try {
      const response = await apiFetch(url, {
        method: editMode ? 'PUT' : 'POST',
        headers,
        raw: true,
        body,
      });

      if (response.ok) {
        const createdReport = await response.json();
        alert(
          `Der Schadensbericht wurde erfolgreich ${editMode ? 'aktualisiert' : 'erstellt'}.`
        );
        router.push(`/report/${createdReport.reportNumber}`);
      } else {
        alert(
          `Fehler beim ${editMode ? 'Aktualisieren' : 'Erstellen'} des Schadensberichts.`
        );
      }
    } catch (error) {
      console.error('Fehler beim Senden des Berichts:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Schaden melden
      </Typography>
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 mx-auto relative">
          <div className="pt-4">
            <CustomerInformationBox
              customerData={customer}
              customerNumber={customerNumber!}
              simplified
              visible
            />
          </div>
          <div>
            <ContractInformationBox
              contract={contract}
              isOffer={false}
              router={router}
              simplified
              visible
            />
          </div>
          <MultiFormBox
            formConfigs={formConfigs}
            formStates={formsMemory}
            dirtyFlags={dirtyFlags}
            onChange={handleFormChange}
            context={contract ? { contract } : undefined}
            disableDefaultValues={editMode}
          />

          {/* files */}
          {!editMode && (
            <StaticBox title="Anhänge">
              {files && files.length > 0 && (
                <div className="flex text-sm">
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Dateiname</TableCell>
                          <TableCell align="center">Entfernen</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {files.map((file, index) => (
                          <TableRow key={index}>
                            <TableCell>{file.name}</TableCell>
                            <TableCell align="center">
                              <IconButton
                                onClick={() => handleRemoveFile(index)}
                                color="primary"
                              >
                                <TrashIcon className="h-6 w-6" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </div>
              )}

              <div className="pt-4 justify-end flex">
                <Tooltip title="Hier können weitere Dokumente angehängt werden">
                  <Button
                    component="label"
                    variant="contained"
                    startIcon={<CloudUploadIcon />}
                  >
                    Dokumente anhängen
                    <VisuallyHiddenInput
                      type="file"
                      multiple
                      onChange={handleFileChange}
                    />
                  </Button>
                </Tooltip>
              </div>
            </StaticBox>
          )}

          <div className="flex gap-4 justify-end">
            <Tooltip title="Ohne sichern zurück.">
              <Button onClick={() => window.history.back()} variant="contained">
                Verwerfen
              </Button>
            </Tooltip>
            <Tooltip
              title={editMode ? 'Schaden aktulaiseren' : 'Neue Schaden anlegen'}
            >
              <Button
                type="submit"
                color="secondary"
                variant="contained"
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? editMode
                    ? 'Aktualisiere...'
                    : 'Melden...'
                  : editMode
                    ? 'Schaden aktualisieren'
                    : 'Schaden melden'}
              </Button>
            </Tooltip>
          </div>
        </div>
      </form>
    </Box>
  );
};

// returns only the values from formsMemory that are present on the screen
const determineFormStates = (
  formsMemory: Record<string, FormState>,
  formConfigs: FormConfig[]
) => {
  return Object.entries(formsMemory).reduce(
    (acc, [formKey, formMemory]) => {
      const formConfig = formConfigs.find((form) => form.key === formKey);
      if (!formConfig) {
        console.error('No form found with form.key=' + formKey);
      }

      const { items = [] } = formConfig ?? {};
      const shownFields = determineShownFields(items, formMemory);

      const formState = Object.entries(formMemory).reduce(
        (acc, [key, value]) => {
          if (shownFields.has(key)) {
            return { ...acc, [key]: value };
          }
          return acc;
        },
        {}
      );

      return {
        ...acc,
        [formKey]: formState,
      };
    },
    {} as Record<string, FormState>
  );
};
