import { type FormContext } from "@/components/ReportForm/domain";
import {type DropdownOption,type DropdownOptionsConfig} from "@/inline-dependencies/FancyForm";
import {type FormConfig, type RiskAddressData} from '@/types';

const damagePlaceAndTime: FormConfig<FormContext> = {
    key: 'damage_place_and_time',
    title: 'Schadenort und -zeitpunkt',
    items: [
        {
            element: 'dropdown',
            id: 'damage_location',
            label: 'Wo ereignete sich der Schaden?',
            required: true,
            options: ({ contract }) => {
                const riskAddresses = (contract.riskAddresses ?? []) as RiskAddressData[]
                const options = riskAddresses.map(
                    ({ street, city, postal_code, house_number }) => `${street} ${house_number}, ${postal_code} ${city}`
                )

                return options.map(option => ({ label: option, value: option }))
            },
            defaultValue: (_, options) => options?.[0]?.value
        },
        {
            element: 'date',
            id: 'damage_date',
            label: 'Schadendatum',
            required: true,
        },
        {
            element: 'date',
            id: 'damage_reported_date',
            label: 'Wann wurde der Schaden gemeldet?',
            required: true,
        },
        {
            element: 'text',
            id: 'damage_room',
            label: 'In welchem Raum ereignete sich der Schaden?',
        },
    ],
};

const getCoveredRiskOptions: DropdownOptionsConfig<FormContext> = ({ contract }) => {
    let options: DropdownOption[] = []

    if (contract.coveredRisks) {
        options = [
            ...options,
            { value: 'fire', label: 'Feuer' },
            { value: 'tap_water', label: 'Leitungswasser' },
            { value: 'wind_and_hail', label: 'Sturm/Hagel' },
        ]
    }
    if (contract.isElementar) {
        options = [
            ...options,
            { value: 'natural_hazard', label: 'Elementar' },
        ]
    }
    if (contract.glassInsurance) {
        options = [
            ...options,
            { value: 'glass', label: 'Glas' },
        ]
    }

    return options
}

const coveredRiskDetails: FormConfig<FormContext> = {
    key: 'covered_risk_details',
    title: 'Versicherte Gefahr',
    items: [
        {
            element: 'dropdown',
            id: 'covered_risk',
            label: 'Versicherte Gefahr',
            options: getCoveredRiskOptions,
            defaultValue: (_, options) => options?.[0]?.value,
        },
        {
            element: 'text',
            id: 'damage_cause',
            label: 'Schadenursache (bitte ausführlich schildern)',
            required: true,
        },
        {
            element: 'boolean-dropdown',
            id: 'is_someone_responsible_for_damage',
            label: 'Gibt es einen Schadenverursacher?',
            showIf: {
                covered_risk: 'fire',
            }
        },
        {
            element: 'boolean-dropdown',
            id: 'was_reported_to_police',
            label: 'Wurde der Schaden der Polizei gemeldet?',
            showIf: 'is_someone_responsible_for_damage',
            required: true,
        },
        {
            element: 'text',
            id: 'police_case_number',
            label: 'Polizeiliches Aktenzeichen',
            showIf: 'was_reported_to_police',
            required: true,
        },
        {
            element: 'text',
            id: 'police_station_address',
            label: 'Adresse der Dienstelle',
            showIf: 'was_reported_to_police'
        },
        {
            element: 'text',
            id: 'damage_causer_contact',
            label: 'Kontaktdaten des Schadenverursachers',
            showIf: 'was_reported_to_police'
        },
    ],
};

const costs: FormConfig<FormContext> = {
    key: 'costs',
    title: 'Kosten',
    items: [
        {
            element: 'text',
            id: 'claims_reserve',
            label: 'Eingabefeld für Schadensreserve (ALPHA)',
        },
        {
            element: 'text',
            id: 'claim_costs',
            label: 'Eingabefeld für Buchung der Schadenkosten (ALPHA)',
        },
    ],
};

export const homeownerInsuranceForms: FormConfig<FormContext>[] = [
    damagePlaceAndTime,
    coveredRiskDetails,
    costs,
];
