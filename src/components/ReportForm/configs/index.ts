import { type FormConfig } from '@/types';

import { accidentInsuranceForms } from './accident-insurance';
import { homeownerInsuranceForms } from './homeowner-insurance';
import { liabilityInsuranceForms } from './liability-insurances';
import { propertyInsurancesForms } from './property-insurances';

export const formConfigsRegistry: Record<string, FormConfig<any>[]> = {
  hausrat: propertyInsurancesForms,
  wohngebaeude: homeownerInsuranceForms,
  tierhalterhaftpflicht: liabilityInsuranceForms,
  privathaftpflicht: liabilityInsuranceForms,
  haus_und_grundbesitzerhaftpflicht: liabilityInsuranceForms,
  bauleistung: propertyInsurancesForms,
  bauherrenhaftpflicht: liabilityInsuranceForms,
  geschaeftsversicherung: propertyInsurancesForms,
  gebaeudeversicherung: propertyInsurancesForms,
  betriebshaftpflicht: liabilityInsuranceForms,
  unfallversicherung: accidentInsuranceForms,
};
