import { divider } from '@/inline-dependencies/FancyForm';
import { type FormConfig } from '@/types';

const damageLocation: FormConfig = {
  key: 'schaden_ort',
  title: 'Schadenort und -zeitpunkt',
  items: [
    {
      element: 'datetime',
      label: 'Wann ereignete sich der Schaden?',
      id: 'damage_date_time',
    },
    {
      element: 'datetime',
      label: 'Wann erhielten <PERSON> von dem Schaden Kenntnis?',
      id: 'knowledge_damage_date_time',
    },
    {
      element: 'text',
      label: 'Wo ereignete sich der Schaden?',
      id: 'damage_location',
    },
    {
      element: 'text',
      label: 'In welchem Raum ereignete sich der Schaden?',
      id: 'damage_location_room',
    },
    {
      element: 'dropdown',
      label: 'Gebäudeart und -nutzung',
      id: 'building_type_usage',
      options: [
        { label: 'Einfamilienhaus', value: 'single_family_house' },
        { label: 'Mehrfamilienhaus', value: 'multiple_family_house' },
        { label: 'Gewerbliche Nutzung', value: 'commercial_use' },
      ],
    },
    {
      element: 'dropdown',
      label: 'Wer ist Eigentümer des Gebäudes oder der Wohnung?',
      id: 'building_owner',
      options: [
        { label: 'Versicherungsnehmer', value: 'insured_person' },
        { label: 'Anderer', value: 'someone_else' },
      ],
    },
    {
      showIf: { building_owner: 'someone_else' },
      element: 'text',
      label: 'Name',
      id: 'building_owner_name',
    },
    {
      showIf: { building_owner: 'someone_else' },
      element: 'text',
      label: 'Adresse',
      id: 'building_owner_address',
    },
    {
      element: 'date',
      label: 'Wann wurde der Schaden gemeldet?',
      id: 'date_damage_reported',
    },
    {
      element: 'text',
      label: 'Wem wurde der Schaden gemeldet?',
      id: 'date_damage_reported_person',
    },
    {
      element: 'date',
      label: 'Wann wurde der Polizei Anzeige erstattet?',
      id: 'date_damage_reported_police',
    },
    {
      element: 'text',
      label: 'Polizei Tagebuch-Nr.',
      id: 'police_diary_nr',
    },
    {
      element: 'text',
      label: 'Dienststelle/Adresse',
      id: 'police_address',
    },
  ],
};
const owner: FormConfig = {
  key: 'owner',
  title: 'Eigentümer',
  items: [
    {
      element: 'dropdown',
      label: 'Wer ist Eigentümer der betroffenen Sachen?',
      id: 'owner',
      options: [
        { label: 'Versicherungsnehmer', value: 'insured_person' },
        { label: 'Anderer', value: 'someone_else' },
      ],
    },
    {
      showIf: { owner: 'someone_else' },
      element: 'text',
      label: 'Name',
      id: 'owner_name',
    },
    {
      showIf: { owner: 'someone_else' },
      element: 'text',
      label: 'Adresse',
      id: 'owner_address',
    },
  ],
};
const otherInsurances: FormConfig = {
  key: 'other_insurances',
  title: 'Andere Versicherungen',
  items: [
    {
      element: 'boolean-dropdown',
      label:
        'Bestehen anderweitige Versicherungen für die vom Schaden betroffenen Sachen?',
      id: 'has_other_insurances',
    },
    {
      showIf: 'has_other_insurances',
      element: 'text',
      label: 'Name der Gesellschaft',
      id: 'company_name',
    },
    {
      showIf: 'has_other_insurances',
      element: 'text',
      label: 'Ort der Gesellschaft',
      id: 'company_address',
    },
    {
      showIf: 'has_other_insurances',
      element: 'text',
      label: 'Aktenzeichen der Gesellschaft',
      id: 'company_file_number',
    },
  ],
};

const damageDetails: FormConfig = {
  key: 'damage_details',
  title: 'Angaben zum Schaden',
  items: [
    {
      element: 'dropdown',
      label: 'Wer hat den Schaden verschuldet?',
      id: 'person_fault',
      options: [
        { label: 'Versicherungsnehmer', value: 'insured_person' },
        { label: 'Anderer', value: 'someone_else' },
      ],
    },
    {
      showIf: { person_fault: 'someone_else' },
      element: 'datetime',
      label: 'Name',
      id: 'person_fault_name',
    },
    {
      showIf: { person_fault: 'someone_else' },
      element: 'datetime',
      label: 'Adresse',
      id: 'person_fault_address',
    },
    divider,
    {
      element: 'boolean-dropdown',
      label: 'Wurden Sie bereits von Schäden gleicher Art betroffen?',
      id: 'was_same_kind_damage',
    },
    {
      showIf: 'was_same_kind_damage',
      element: 'date',
      label: 'Wann',
      id: 'when_same_kind_damage',
    },
    {
      showIf: 'was_same_kind_damage',
      element: 'price',
      label: 'Schadenhöhe',
      id: 'same_kind_damage_money_amount',
    },
    divider,
    {
      element: 'price',
      label: 'Wie hoch schätzen Sie den Schaden?',
      id: 'damage_amount',
    },
    {
      element: 'price',
      label:
        'Wie hoch schätzen Sie den Neuwert der gesamten versicherten Sachen?',
      id: 'insured_things_price',
    },
    {
      element: 'boolean-dropdown',
      label: 'Sind Sie vorsteuerabzugsberechtigt (§15 UstG)?',
      id: 'is_entitled_input_tax_deduction',
    },
  ],
};
const damageCourse: FormConfig = {
  key: 'damage_course',
  title: 'Schadenhergang',
  items: [
    {
      element: 'textarea',
      label:
        'Bitte schildern Sie den Schadenhergang so ausführlich, dass ein möglichst genaues Bild entsteht:',
      id: 'course_of_damage',
    },
    {
      element: 'textarea',
      label: 'Welche Schadenminderungsmaßnahmen wurden ergriffen?',
      id: 'mitigation_measures',
    },
  ],
};
const additionalQuestions: FormConfig = {
  key: 'additional_questions',
  title: 'Zusatzfragen',
  items: [
    {
      element: 'checkbox',
      label: 'Einbruchdiebstahl',
      id: 'is_burglary_damage',
    },
    {
      showIf: 'is_burglary_damage',
      element: 'boolean-dropdown',
      label: 'Wurden Behältnisse gewaltsam geöffnet?',
      id: 'forced_open',
    },
    {
      showIf: 'forced_open',
      element: 'textarea',
      label: 'Beschreibung',
      id: 'forced_open_description',
    },
    {
      showIf: 'is_burglary_damage',
      element: 'text',
      label: 'Wo befanden sich die Schlüssel?',
      id: 'location_keys',
    },
    {
      showIf: 'is_burglary_damage',
      element: 'boolean-dropdown',
      label: 'Sind Einbruchsmerkmale sichtbar?',
      tooltip:
        'Beschädigungen an Wänden, Decken, Fenstern, Türen, Schlössern, Behältnissen usw.',
      id: 'burglary_characteristics',
    },
    {
      showIf: 'burglary_characteristics',
      element: 'textarea',
      label: 'Beschreibung',
      id: 'burglary_characteristics_description',
    },
    {
      showIf: 'is_burglary_damage',
      element: 'dropdown',
      label: 'Welche Mittel haben die Diebe zum Öffnen angewendet?',
      id: 'used_keys',
      options: [
        { label: 'Richtige Schlüssel', value: 'Richtige Schlüssel' },
        { label: 'Nachschlüssel', value: 'Nachschlüssel' },
        { label: 'Gewalt', value: 'Gewalt' },
      ],
    },
    divider,
    {
      element: 'checkbox',
      label: 'Leitungswasserschäden',
      id: 'is_tap_water_damage',
    },
    {
      showIf: 'is_tap_water_damage',
      element: 'dropdown',
      label: 'An welcher Anlage ist der Schaden entstanden?',
      id: 'tap_water_damage_location',
      options: [
        { label: 'Zuleitung', value: 'Zuleitung' },
        { label: 'Ableitung', value: 'Ableitung' },
        { label: 'Heizung', value: 'Heizung' },
      ],
    },
    {
      showIf: 'is_tap_water_damage',
      element: 'dropdown',
      label: 'Wer bewohnt die vom Schaden betroffene Wohnung?',
      id: 'tap_water_person',
      options: [
        { label: 'Versicherungsnehmer', value: 'Versicherungsnehmer' },
        { label: 'Andere', value: 'Andere' },
      ],
    },
    {
      showIf: { tap_water_person: 'Andere' },
      element: 'text',
      label: 'Name',
      id: 'tap_water_person_name',
    },
    {
      showIf: 'is_tap_water_damage',
      element: 'boolean-dropdown',
      label: 'Hat dieser eine Leitungswasser-Versicherung?',
      id: 'is_tap_insured',
    },
    {
      showIf: 'is_tap_water_damage',
      element: 'boolean-dropdown',
      label:
        'Haben Sie als Mieter Gebäudeteile auf eigene Rechnung eingebracht?',
      id: 'part_building_own_expense',
    },
    {
      showIf: 'part_building_own_expense',
      element: 'text',
      label: 'Welche Gebäudeteile?',
      id: 'part_building_own_expense_description',
    },
    {
      showIf: 'part_building_own_expense',
      element: 'price',
      label: 'Wert',
      id: 'part_building_own_expense_amount',
    },
    divider,
    {
      element: 'checkbox',
      label: 'Schäden an Fußböden durch Feuer, Leitungswasser oder Sturm',
      id: 'is_fe_lw_st',
    },
    {
      showIf: 'is_fe_lw_st',
      element: 'dropdown',
      label: 'Wer hat den Belag angeschafft?',
      id: 'person_floor_creation',
      options: [
        { label: 'Gebäudeeigentümer', value: 'Gebäudeeigentümer' },
        { label: 'Wohnungseigentümer', value: 'Wohnungseigentümer' },
        { label: 'Mieter', value: 'Mieter' },
      ],
    },
    {
      showIf: 'is_fe_lw_st',
      element: 'dropdown',
      label: 'Wie ist der Fußboden verlegt?',
      id: 'floor_attachment',
      options: [
        { label: 'Lose', value: 'lose' },
        { label: 'Vollflächig verklebt', value: 'vollflächig verklebt' },
        {
          label: 'An den Rändern mit doppelseitigem Klebeband befestigt',
          value: 'an den Rändern mit doppelseitigem Klebeband befestigt',
        },
      ],
    },
    {
      showIf: 'is_fe_lw_st',
      element: 'dropdown',
      label: 'Was befindet sich unter dem Fußbodenbelag?',
      id: 'below_floor_covering',
      options: [
        { label: 'Estrich/Beton', value: 'Estrich/Beton' },
        { label: 'Holzdielen', value: 'Holzdielen' },
        { label: 'Parkett', value: 'Parkett' },
        { label: 'PVC/Linoleum', value: 'NePVC/Lineumin' },
        { label: 'Anderes', value: 'Anderes' },
      ],
    },
    {
      showIf: { below_floor_covering: 'Anderes' },
      element: 'text',
      label: 'Anderes',
      id: 'below_floor_covering_other',
    },
    divider,
    {
      element: 'checkbox',
      label: 'Glasschäden',
      id: 'is_glas_damage',
    },
    {
      showIf: 'is_glas_damage',
      element: 'boolean-dropdown',
      label: 'War die Scheibe vor dem Schadenereignis fertig eingesetzt?',
      id: 'glas_inserted_before_damage',
    },
    {
      showIf: 'is_glas_damage',
      element: 'boolean-dropdown',
      label:
        'Sind Mängel an der Umrahmung vorhanden und ist hierauf der Schaden zurückzuführen?',
      id: 'glas_imperfactions',
    },
    {
      showIf: 'glas_imperfactions',
      element: 'textarea',
      label: 'Beschreibung',
      id: 'glas_imperfactions_decription',
    },
    {
      showIf: 'is_glas_damage',
      element: 'boolean-dropdown',
      label: 'Haben Sie den Reparaturauftrag bereits erteilt?',
      id: 'repair_order_booked',
    },
    {
      showIf: 'repair_order_booked',
      element: 'text',
      label: 'Firma',
      id: 'repair_order_company',
    },
    {
      showIf: 'is_glas_damage',
      element: 'dropdown',
      label: 'Die Entschädigung soll gezahlt werden an',
      id: 'glas_compensation_person',
      options: [
        { label: 'Versicherungsnehmer', value: 'insured_person' },
        { label: 'Glaser gemäß Rechnung', value: 'glazier' },
      ],
    },
  ],
};

const damageStatement: FormConfig = {
  key: 'schadenaufstelllung',
  title: 'Schadenaufstellung',
  items: [
    {
      element: 'date',
      label: 'Schadendatum',
      id: 'damage_date',
    },
    {
      element: 'text',
      label: 'Schadensort',
      id: 'damage_location',
    },
    {
      element: 'text',
      label: 'Schadensnummer der Versicherung',
      id: 'external_report_number',
    },
    {
      element: 'text',
      label: 'Versicherte Gefahr',
      id: 'coverd_risk',
    },
    {
      element: 'textarea',
      label: 'Beschreibung',
      tooltip: 'Geben Sie eine detaillierte Beschreibung des Schadens ein.',
      id: 'text',
    },
  ],
};

const bankDetails: FormConfig = {
  key: 'basis',
  title: 'Bankangaben',
  items: [
    {
      element: 'text',
      label: 'Bankinstitut',
      id: 'bank_company',
    },
    {
      element: 'text',
      label: 'IBAN',
      id: 'bank_iban',
    },
    {
      element: 'text',
      label: 'BIC',
      id: 'bank_bic',
    },
  ],
};

export const propertyInsurancesForms: FormConfig[] = [
  damageLocation,
  owner,
  otherInsurances,
  damageDetails,
  damageCourse,
  additionalQuestions,
  damageStatement,
  bankDetails,
];
