import {
  AssignmentInd,
  Build,
  Description,
  ExpandMore,
  GppMaybe,
  Group,
  GroupWork,
  Help,
  InsertDriveFile,
  Logout,
  Receipt,
  Settings,
} from '@mui/icons-material';
import MenuIcon from '@mui/icons-material/Menu';
import {
  Box,
  Button,
  Collapse,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import Image from 'next/image';
import Link from 'next/link';
import NextLink from 'next/link';
import { signOut, useSession } from 'next-auth/react';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';

export default function Header() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [headerH, setHeaderH] = useState(0);
  const open = Boolean(anchorEl);
  const drawerHostRef = useRef<HTMLDivElement>(null);
  const burgerButtonRef = useRef<HTMLButtonElement>(null);
  const { data: session } = useSession();

  /** measure header height */
  const headerRef = useRef<HTMLDivElement>(null);
  const measureHeader = () =>
    setHeaderH(headerRef.current ? headerRef.current.offsetHeight : 0);

  useLayoutEffect(measureHeader, []);
  useEffect(() => {
    window.addEventListener('resize', measureHeader);
    return () => window.removeEventListener('resize', measureHeader);
  }, []);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleLogout = () => {
    signOut({
      callbackUrl: '/login',
    });
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const clickedOutsideDrawer =
        drawerHostRef.current &&
        !drawerHostRef.current.contains(event.target as Node);
      const clickedBurgerButton =
        burgerButtonRef.current &&
        burgerButtonRef.current.contains(event.target as Node);

      if (clickedOutsideDrawer && !clickedBurgerButton) {
        setDrawerOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setDrawerOpen(false);
      }
    };

    if (drawerOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [drawerOpen]);

  return (
    <Box
      ref={headerRef}
      sx={{
        position: 'sticky',
        top: 0,
        zIndex: 10,
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        backdropFilter: 'blur(6px)',
        px: {
          xs: 2,
          md: 5,
        },
        py: 1,
      }}
    >
      {/* Top Row */}
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={{ xs: 1, md: 2 }}
        alignItems="center"
        justifyContent="flex-start"
        flexWrap="wrap"
        mr={0}
        mt={1}
      >
        <Link href="/dashboard">
          <Image src="/images/logo.svg" alt="Logo" width={100} height={100} />
        </Link>

        <Stack></Stack>

        <Box
          sx={{
            position: 'relative',
            display: { xs: 'none', sm: 'flex' },
          }}
        >
          <Image
            src="/images/banner.svg"
            alt="Banner"
            width={320}
            height={100}
          />
          <Typography
            variant="caption"
            sx={{
              position: 'absolute',
              top: 8,
              left: 16,
              fontWeight: 'bold',
              fontSize: '0.875rem',
              color: 'white',
            }}
          >
            VERWALTUNGSPORTAL
          </Typography>
        </Box>

        {(process.env.NEXT_PUBLIC_ENVIRONMENT == 'dev' ||
          process.env.NEXT_PUBLIC_ENVIRONMENT == 'stage') && (
          <Box
            display="flex"
            sx={{
              position: { xs: 'fixed', sm: 'initial' },
              top: { xs: 0, sm: 'auto' },
              left: { xs: 8, sm: 'auto' },
              backgroundColor: 'red',
              color: 'white',
              px: { xs: 1, md: 5 },
              py: 0.5,
              zIndex: 1300,
              borderRadius: 1,
              transformOrigin: 'top right',
              alignItems: 'center',
            }}
          >
            <Typography
              variant="caption"
              sx={{ fontWeight: 'bold', fontSize: { xs: 17, md: 18 } }}
            >
              {process.env.NEXT_PUBLIC_ENVIRONMENT == 'dev' ? 'TEST' : 'STAGE'}
            </Typography>
          </Box>
        )}

        {/* User menu button on desktop */}
        <Box
          sx={{
            display: { xs: 'none', md: 'flex' },
            flexGrow: 1,
            justifyContent: 'flex-end',
          }}
        >
          <Button
            id="basic-button"
            variant="contained"
            sx={{ textTransform: 'none', gap: 1 }}
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
          >
            {session?.user?.name}
            <ExpandMore height={24} />
          </Button>
        </Box>

        {/* Mobile hamburger */}
        <Box
          sx={{
            display: { xs: 'flex', md: 'none' },
            flexGrow: 1,
            justifyContent: 'flex-end',
          }}
        >
          <IconButton
            ref={burgerButtonRef}
            onClick={() => setDrawerOpen((prev) => !prev)}
          >
            <MenuIcon />
          </IconButton>
        </Box>
      </Stack>

      {/* Desktop Navigation */}
      <Box sx={{ display: { xs: 'none', md: 'flex' } }}>
        <Stack direction="row" spacing={2} alignItems="center" pl={15}>
          <NavLink href="/customers" label="KUNDEN" />
          <NavLink href="/contracts" label="VERTRÄGE" />
          <NavLink href="/reports" label="SCHÄDEN" />
          {session?.roles?.includes('asevo-admin') && (
            <NavLink href="/invoices" label="RECHNUNGEN" />
          )}
          {session?.roles?.includes('asevo-admin') && (
            <NavLink href="/agents" label="MAKLER" />
          )}
          {session?.roles?.includes('asevo-admin') && (
            <NavLink href="/agencies" label="AGENTUREN" />
          )}
        </Stack>
      </Box>

      {/* User Menu */}
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{ 'aria-labelledby': 'basic-button' }}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem
          sx={{ justifyContent: 'flex-end', gap: 1 }}
          onClick={() => {
            handleClose();
            window.location.href = '/settings';
          }}
        >
          Einstellungen <Settings height={24} />
        </MenuItem>
        {session?.roles?.includes('asevo-admin') && (
          <MenuItem
            sx={{ justifyContent: 'flex-end', gap: 1 }}
            onClick={() => {
              handleClose();
              window.location.href = '/admin_settings';
            }}
          >
            Admin-Einstellungen <Build height={24} />
          </MenuItem>
        )}
        <MenuItem
          sx={{ justifyContent: 'flex-end', gap: 1 }}
          onClick={() => {
            handleClose();
            window.location.href = '/info';
          }}
        >
          Informationen <Help height={24} />
        </MenuItem>
        <MenuItem
          sx={{ justifyContent: 'flex-end', gap: 1 }}
          onClick={() => {
            handleClose();
            window.location.href = '/documents';
          }}
        >
          Dokumente <InsertDriveFile height={24} />
        </MenuItem>
        <MenuItem
          sx={{ justifyContent: 'flex-end', gap: 1 }}
          onClick={handleLogout}
        >
          Abmelden <Logout height={24} />
        </MenuItem>
      </Menu>
      <Box
        sx={{
          position: 'absolute',
          top: headerH,
          left: 0,
          right: 0,
          zIndex: 20,
          display: { xs: 'inline', md: 'none' },
        }}
      >
        <Collapse in={drawerOpen}>
          <Box
            ref={drawerHostRef}
            sx={{
              backgroundColor: 'white',
              boxShadow: '0px 6px 6px -2px rgba(0,0,0,0.25)',
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8,
              p: 2,
            }}
          >
            <Stack spacing={2} ml={5}>
              <NavLink
                href="/customers"
                label="Kunden"
                icon={<Group color="primary" />}
              />
              <NavLink
                href="/contracts"
                label="Verträge"
                icon={<Description color="primary" />}
              />
              <NavLink
                href="/reports"
                label="Schäden"
                icon={<GppMaybe color="primary" />}
              />
              {session?.roles?.includes('asevo-admin') && (
                <NavLink
                  href="/invoices"
                  label="Rechungen"
                  icon={<Receipt color="primary" />}
                />
              )}
              {session?.roles?.includes('asevo-admin') && (
                <NavLink
                  href="/agents"
                  label="Makler"
                  icon={<AssignmentInd color="primary" />}
                />
              )}
              {session?.roles?.includes('asevo-admin') && (
                <NavLink
                  href="/agencies"
                  label="AGENTUREN"
                  icon={<GroupWork color="primary" />}
                />
              )}
              <Divider />
              <NavLink
                href="/settings"
                label="Einstellungen"
                icon={<Settings color="primary" />}
              />
              {session?.roles?.includes('asevo-admin') && (
                <NavLink
                  href="/admin_settings"
                  label="Admin-Einstellungen"
                  icon={<Build color="primary" />}
                />
              )}
              <NavLink
                href="/info"
                label="Informationen"
                icon={<Help color="primary" />}
              />
              <NavLink
                href="/documents"
                label="Dokumente"
                icon={<InsertDriveFile color="primary" />}
              />
              <Stack
                direction="row"
                gap={2}
                sx={{
                  transition: 'transform 0.3s',
                  '&:hover': { transform: 'scale(1.05)' },
                  cursor: 'pointer',
                }}
              >
                <Logout color="primary" />
                <Typography
                  onClick={handleLogout}
                  sx={{
                    fontSize: '0.875rem',
                    color: 'primary.main',
                    textDecoration: 'none',
                    alignSelf: 'center',
                  }}
                >
                  Abmelden
                </Typography>
              </Stack>
            </Stack>
          </Box>
        </Collapse>
      </Box>
    </Box>
  );
}

/* Helper component for nav links */
function NavLink({
  href,
  label,
  icon,
}: {
  href: string;
  label: string;
  icon?: React.ReactNode;
}) {
  return (
    <NextLink href={href} style={{ textDecoration: 'none' }} aria-label={label}>
      <Box
        sx={{
          display: 'inline-flex',
          alignItems: 'center',
          transition: 'transform 0.3s',
          '&:hover': { transform: 'scale(1.05)' },
          cursor: 'pointer',
        }}
      >
        {icon}
        <Typography
          ml={2}
          sx={{
            fontSize: '0.875rem',
            color: 'primary.main',
            textDecoration: 'none',
          }}
        >
          {label}
        </Typography>
      </Box>
    </NextLink>
  );
}
