import {
  Button,
  Card,
  CardActions,
  CardContent,
  type SvgIconProps,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import React, { type ReactElement } from 'react';

import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface KvcCardProps {
  icon?: React.ElementType<SvgIconProps>;
  titleTextKey?: string;
  bodyTextKey?: string;
  children?: ReactElement;
  disabled?: boolean;
  reduced?: boolean;
  buttonLabelKey?: string;
  buttonOnClick?: () => void;
  buttonDisabledPlaceholder?: string;
  contentAreaSx?: SxProps<Theme>;
  sx?: SxProps<Theme>;
  onCardClick?: () => void;
}

export const KvcCard: React.FC<KvcCardProps> = ({
  icon: Icon,
  titleTextKey,
  bodyTextKey,
  children,
  disabled,
  reduced,
  button<PERSON><PERSON><PERSON><PERSON>ey,
  buttonOnClick,
  buttonDisabledPlaceholder,
  contentAreaSx,
  sx,
}) => {
  const t = useTranslations();
  return (
    <Card
      className={disabled ? 'disabled' : undefined}
      sx={{
        maxWidth: 300,
        minWidth: 300,
        textAlign: 'center',
        background: '',
        display: 'flex',
        justifyContent: 'center',
        ...sx,
      }}
    >
      <CardContent
        sx={{
          flexGrow: 1,
          paddingLeft: 0,
          paddingRight: 0,
          opacity: disabled ? 0.5 : 1,
        }}
      >
        {Icon && (
          <Icon
            sx={{
              fontSize: (theme) => theme.typography.h2.fontSize,
              fill: disabled ? 'gray' : 'var(--startcard-icon-color)',
            }}
          />
        )}

        {typeof titleTextKey === 'string' && (
          <Typography
            fontWeight={700}
            variant="h5"
            component="h2"
            color={disabled ? 'gray' : 'var(--startcard-title-color)'}
            marginBottom={2}
          >
            {t.rich(titleTextKey, intlTranslationRichHelper)}
          </Typography>
        )}

        {reduced !== true && typeof bodyTextKey === 'string' && (
          <Typography variant="body1" color="var(--startcard-body-color)">
            {t.rich(bodyTextKey, intlTranslationRichHelper)}
          </Typography>
        )}
      </CardContent>

      <CardActions
        sx={{
          display: 'flex',
          justifyContent: 'center',
          opacity: disabled ? 0.5 : 1,
          ...contentAreaSx,
        }}
      >
        {children}
        {disabled !== true &&
          typeof buttonOnClick !== 'undefined' &&
          typeof buttonLabelKey === 'string' && (
            <Button
              variant="contained"
              sx={{
                fontWeight: 800,
              }}
              onClick={() => {
                buttonOnClick!();
              }}
            >
              {t.rich(buttonLabelKey, intlTranslationRichHelper)}
            </Button>
          )}
        {disabled && typeof buttonDisabledPlaceholder === 'string' && (
          <Typography fontWeight={700}>
            {t.rich(buttonDisabledPlaceholder, intlTranslationRichHelper)}
          </Typography>
        )}
      </CardActions>
    </Card>
  );
};
