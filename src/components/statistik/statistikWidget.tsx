'use client';
import { MenuItem, Select, Stack, Typography } from '@mui/material';
import { useFormatter, useTranslations } from 'next-intl';
import { type ReactElement, useState } from 'react';

import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { StatistikCard } from './statistikCard';

export default function StatistikWidget(): ReactElement {
  const t = useTranslations();
  const [year, setYear] = useState<number>(2025);
  const [month, setMonth] = useState<number>();
  const [yearsActive, setYearsActive] = useState<number[]>([2024, 2025]);
  const format = useFormatter();
  const months = Array.from({ length: 12 }, (_, i) =>
    format.dateTime(new Date(2000, i, 1), { month: 'long' })
  );

  return (
    <>
      <div>
        <Stack
          sx={{
            background: 'var(--statistic-widget-background)',
            paddingBottom: '50px',
            paddingTop: '30px',
          }}
          direction="column"
          display="inline-flex"
          flexWrap="wrap"
          justifyContent="center"
          width="100%"
        >
          <Stack
            direction="row"
            display="flex"
            flexWrap="wrap"
            justifyContent="center"
            gap={3}
          >
            <Typography
              variant="h5"
              component="h3"
              fontWeight={700}
              color="var(--teal)"
            >
              {t.rich('statistik.headline', intlTranslationRichHelper)}
            </Typography>

            <Select value={year} onChange={(e) => setYear(e.target.value)}>
              {yearsActive.map((y, i) => (
                <MenuItem key={i} value={i}>
                  {y}
                </MenuItem>
              ))}
            </Select>

            <Select value={month} onChange={(e) => setMonth(e.target.value)}>
              {months.map((m, i) => (
                <MenuItem key={i} value={i + 1}>
                  {m}
                </MenuItem>
              ))}
            </Select>
          </Stack>

          <Stack
            direction="row"
            display="flex"
            flexWrap="wrap"
            justifyContent="center"
            gap={3}
          >
            <StatistikCard
              count={7}
              currencyWhole={602}
              currencyFractional={0}
              line1Text="statistik.card.offene-vorgaenge.line1"
              currencySign="statistik.card.offene-vorgaenge.currency-sign"
              currencyPrefix="statistik.card.offene-vorgaenge.currency-prefix"
              line2Text="statistik.card.offene-vorgaenge.line2"
            ></StatistikCard>

            <StatistikCard
              count={22}
              currencyWhole={2472}
              currencyFractional={0}
              line1Text="statistik.card.policierte-vorgaenge.line1"
              currencySign="statistik.card.policierte-vorgaenge.currency-sign"
              currencyPrefix="statistik.card.policierte-vorgaenge.currency-prefix"
              line2Text="statistik.card.policierte-vorgaenge.line2"
            ></StatistikCard>
          </Stack>
        </Stack>
      </div>
    </>
  );
}
