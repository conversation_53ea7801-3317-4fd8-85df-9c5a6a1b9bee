import { Card, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface Props {
  count: number;
  line1Text: string;
  line2Text: string;
  currencyWhole: number;
  currencyFractional: number;
  currencySign: string;
  currencyPrefix: string;
}

export function StatistikCard(props: Props) {
  const t = useTranslations();
  return (
    <Card>
      <Stack direction="row" display="flex">
        <Typography
          variant="h2"
          lineHeight="60px"
          fontWeight={700}
          paddingRight="10px"
          color="var(--statistic-widget-counter-color)"
        >
          {props.count}
        </Typography>

        <Stack
          direction="column"
          display="flex"
          sx={{
            lineHeight: 26,
          }}
        >
          <Typography
            variant="h6"
            fontWeight={700}
            lineHeight="26px"
            color="var(--statistic-widget-headline-color)"
          >
            {t.rich(props.line1Text, intlTranslationRichHelper)}
          </Typography>

          <Stack direction="row" display="flex">
            <Typography variant="h6" lineHeight="26px" fontWeight={700}>
              {`${t.rich(props.currencyPrefix, intlTranslationRichHelper)} ${props.currencyWhole},${props.currencyFractional} ${t.rich(props.currencySign, intlTranslationRichHelper)} `}
            </Typography>
            <Typography variant="h6" lineHeight="26px">
              &nbsp;{t.rich(props.line2Text, intlTranslationRichHelper)}
            </Typography>
          </Stack>
        </Stack>
      </Stack>
    </Card>
  );
}
