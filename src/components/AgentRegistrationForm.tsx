// src/components/AgentRegistrationForm.tsx
import {
  Box,
  Button,
  Card,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Image from 'next/image';
import Link from 'next/link';
import router from 'next/router';
import { useState } from 'react';

import { apiFetch } from '@/utils/apiFetch';

const CenterBox = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  flexGrow: 1,
  padding: theme.spacing(4),
}));

const StyledCard = styled(Card)(({ theme }) => ({
  maxWidth: 600,
  width: '100%',
  padding: theme.spacing(4),
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: (theme.shape.borderRadius as number) * 1,
}));

export default function AgentRegistrationForm() {
  const [formData, setFormData] = useState({
    salutation: '',
    first_name: '',
    last_name: '',
    street: '',
    house_number: '',
    postal_code: '',
    city: '',
    email: '',
    agency_name: '',
    url: '',
    telephone_number: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  function handleChange(event: React.ChangeEvent<HTMLInputElement>): void;
  function handleChange(
    event: SelectChangeEvent<string>,
    child: React.ReactNode
  ): void;

  // Implementation signature
  function handleChange(event: any) {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await apiFetch('/api/user/registration', {
        method: 'POST',
        raw: true,
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(
          'Fehler beim Senden der Email. Bitte versuchen Sie es erneut.'
        );
      }
      alert(
        'Ihre Registrierung wurde versandt, wir werden uns bei Ihnen zeitnah zurückmelden.'
      );
      setFormData({
        salutation: '',
        first_name: '',
        last_name: '',
        street: '',
        house_number: '',
        postal_code: '',
        city: '',
        email: '',
        agency_name: '',
        url: '',
        telephone_number: '',
      });
      router.push('/');
    } catch (error) {
      console.error(error);
      alert('Fehler beim Senden der Email. Bitte versuchen Sie es erneut.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <CenterBox>
      <StyledCard>
        <Box display="flex" justifyContent="center" mb={2}>
          <Link href="/" passHref>
            <Image src="/images/logo.svg" alt="Logo" width={120} height={120} />
          </Link>
        </Box>
        <Typography variant="h5" textAlign="center" gutterBottom>
          Account anfragen
        </Typography>
        <Typography variant="body2" textAlign="center" mb={2}>
          Hier können sie eine unverbindliche Anfrage stellen
        </Typography>
        <form onSubmit={handleSubmit}>
          <div className="my-4 mx-auto relative">
            <div className="grid grid-cols-1 gap-2 py-2">
              <Tooltip title="Agenturnamen eingeben.">
                <TextField
                  type="text"
                  label="Agenturname"
                  name="agency_name"
                  value={formData.agency_name}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Tooltip title="Wählen Sie die passende Anrede.">
                <FormControl required variant="outlined" className="w-full">
                  <InputLabel>Anrede</InputLabel>
                  <Select
                    name="salutation"
                    label="Anrede"
                    value={formData.salutation}
                    onChange={handleChange}
                  >
                    <MenuItem value="Frau">Frau</MenuItem>
                    <MenuItem value="Herr">Herr</MenuItem>
                    <MenuItem value="Person">Person</MenuItem>
                  </Select>
                </FormControl>
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 py-2">
              <Tooltip title="Vorname eingeben.">
                <TextField
                  type="text"
                  label="Vorname"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="Nachname eingeben.">
                <TextField
                  type="text"
                  label="Nachname"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 py-2">
              <Tooltip title="Straße eingeben.">
                <TextField
                  type="text"
                  label="Straße"
                  name="street"
                  value={formData.street}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="Hausnummer eingeben.">
                <TextField
                  type="text"
                  label="Hausnummer"
                  name="house_number"
                  value={formData.house_number}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="Postleitzahl eingeben.">
                <TextField
                  type="text"
                  label="Postleitzahl"
                  name="postal_code"
                  value={formData.postal_code}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="Ort eingeben.">
                <TextField
                  type="text"
                  label="Ort"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 py-2">
              <Tooltip title="E-Mail-Adresse eingeben.">
                <TextField
                  type="email"
                  label="Emailadresse"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="URL zur eigenen Webseite eingeben. Inklusive Protokoll (https://...)">
                <TextField
                  type="url"
                  label="Webseite"
                  name="url"
                  value={formData.url}
                  onChange={handleChange}
                  className="w-full"
                />
              </Tooltip>
              <Tooltip title="Telefonnummer eingeben.">
                <TextField
                  type="tel"
                  label="Telefonnummer"
                  name="telephone_number"
                  value={formData.telephone_number}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </Tooltip>
            </div>
          </div>
          <Button
            type="submit"
            color="secondary"
            variant="contained"
            disabled={isSubmitting}
            fullWidth
          >
            {isSubmitting ? 'Senden...' : 'Senden'}
          </Button>
        </form>
      </StyledCard>
    </CenterBox>
  );
}
