import FactCheckIcon from '@mui/icons-material/FactCheck';
import TimerIcon from '@mui/icons-material/Timer';
import WarningIcon from '@mui/icons-material/Warning';
import { Link, Stack, Typography } from '@mui/material';
import { type Case, type Customer } from '@prisma/client';
import NextLink from 'next/link';
import { type ReactElement } from 'react';

import { VorgangState } from '@/utils/enums';

interface VorgangListItemProps {
  case: Case & { customer: Customer };
}

export function VorgangListItem(props: VorgangListItemProps) {
  return (
    <>
      <Stack
        direction="row"
        spacing={1}
        alignItems="center"
        sx={{
          backgroundColor: 'var(--gray-background)',
          paddingLeft: '16px',
          borderRadius: '7px',
          display: 'inline-flex',
          flexWrap: 'wrap',
        }}
      >
        <>{GetIconForState(props.case.status)}</>
        <Typography
          variant="body1"
          fontWeight={800}
          paddingTop={'16px'}
          paddingBottom={'16px'}
        >
          {props.case.caseType}:
        </Typography>
        {props.case.customer && (
          <Typography variant="body1" fontWeight={500}>
            {props.case.customer.firstName}&nbsp;{props.case.customer.lastName}
          </Typography>
        )}
        <>&nbsp;/ Vorgang: </>
        <Link
          component={NextLink}
          href={`/case/${props.case.caseNumber}`}
          paddingRight={10}
        >
          {props.case.caseNumber}
        </Link>

        {GetColorIndicatorForState(props.case.status)}
      </Stack>
    </>
  );
}

function GetIconForState(state: string): ReactElement {
  switch (state) {
    case VorgangState.offen:
      return (
        <WarningIcon
          sx={{
            color: 'var(--teal)',
          }}
        />
      );
    case VorgangState.abgelehnt:
      return (
        <WarningIcon
          sx={{
            color: 'var(--teal)',
          }}
        />
      );
    case VorgangState.fertig:
      return (
        <TimerIcon
          sx={{
            color: 'var(--teal)',
          }}
        />
      );
    case VorgangState.policiert:
      return (
        <FactCheckIcon
          sx={{
            color: 'var(--teal)',
          }}
        />
      );
  }
  return <></>;
}

function GetColorIndicatorForState(state: string): ReactElement {
  switch (state) {
    case VorgangState.offen:
      return (
        <Typography
          sx={{
            marginLeft: 'auto !important',
            width: 110,
            display: 'flex',
            justifyContent: 'center',
            background: 'var(--case-status-indicator-background-offen)',
            height: '100%',
            paddingTop: '16px',
            borderRadius: '0 7px 7px 0',
          }}
        >
          offen
        </Typography>
      );
    case VorgangState.abgelehnt:
      return (
        <Typography
          sx={{
            marginLeft: 'auto !important',
            width: 110,
            display: 'flex',
            justifyContent: 'center',
            background: 'var(--case-status-indicator-background-abgelehnt)',
            height: '100%',
            paddingTop: '16px',
            borderRadius: '0 7px 7px 0',
          }}
        >
          abgelehnt
        </Typography>
      );
    case VorgangState.fertig:
      return (
        <Typography
          sx={{
            marginLeft: 'auto !important',
            width: 110,
            display: 'flex',
            justifyContent: 'center',
            background: 'var(--case-status-indicator-background-fertig)',
            height: '100%',
            paddingTop: '16px',
            borderRadius: '0 7px 7px 0',
          }}
        >
          fertig
        </Typography>
      );
    case VorgangState.policiert:
      return (
        <Typography
          sx={{
            marginLeft: 'auto !important',
            width: 110,
            display: 'flex',
            justifyContent: 'center',
            background: 'var(--case-status-indicator-background-policiert)',
            height: '100%',
            paddingTop: '16px',
            borderRadius: '0 7px 7px 0',
          }}
        >
          policiert
        </Typography>
      );
  }
  return <></>;
}
