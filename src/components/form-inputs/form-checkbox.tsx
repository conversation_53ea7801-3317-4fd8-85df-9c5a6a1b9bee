'use client';

import {
  Box,
  Checkbox as MuiCheck<PERSON>,
  type CheckboxProps as MuiCheckboxProps,
  FormControl,
  FormControlLabel,
  type FormControlLabelProps,
  FormHelperText,
  useTheme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { forwardRef, type ReactElement, type Ref } from 'react';
import { Controller, type FieldValues, type Path } from 'react-hook-form';

import { useReactHookFormContext } from '@/hooks/useFormContext';

import { FormQuestionHelperText } from './form-question-helper-text';
import { FormQuestionLabel } from './form-question-label';

type CheckboxProps<T extends FieldValues> = Omit<
  MuiCheckboxProps,
  'name' | 'checked' | 'defaultChecked'
> & {
  name: Path<T>;
  label?: string;
  labelPlacement?: FormControlLabelProps['labelPlacement'];
  helperText?: string;
  checkboxProps?: Omit<MuiCheckboxProps, 'name' | 'checked' | 'defaultChecked'>;
  formControlLabelProps?: Omit<FormControlLabelProps, 'control' | 'label'>;
  questionLabel?: string; // Translation key for question label above the input
  questionNumber?: number; // Question number to display before the question label
  questionHelperText?: string; // Translation key for helper text below the question label
};

// eslint-disable-next-line react/display-name
const FormCheckbox = forwardRef(
  <T extends FieldValues>(
    {
      name,
      label,
      labelPlacement = 'end',
      helperText: _helperText,
      checkboxProps,
      formControlLabelProps,
      questionLabel,
      questionNumber,
      questionHelperText,
      ...restProps
    }: CheckboxProps<T>,
    ref: Ref<HTMLInputElement>
  ) => {
    const { control, readOnly } = useReactHookFormContext<T>();
    const theme = useTheme();
    const t = useTranslations();

    return (
      <Controller
        name={name}
        control={control}
        render={({
          field: { value, onChange, ...field },
          fieldState: { error },
        }) => {
          const checkbox = (
            <MuiCheckbox
              {...restProps}
              {...checkboxProps}
              {...field}
              checked={!!value}
              onChange={(event) => onChange(event.target.checked)}
              slotProps={{
                input: {
                  ref,
                },
              }}
              sx={{
                color: error ? 'error.main' : undefined,
                '&.Mui-checked': {
                  color: error ? 'error.main' : undefined,
                },
                ...checkboxProps?.sx,
              }}
            />
          );

          return (
            <Box>
              <FormQuestionLabel
                questionLabel={questionLabel}
                questionNumber={questionNumber}
                required={restProps.required}
              />

              <FormControl error={!!error}>
                {label ? (
                  <FormControlLabel
                    {...formControlLabelProps}
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        color: error ? theme.palette.error.main : 'inherit',
                      },
                      ...formControlLabelProps?.sx,
                    }}
                    disabled={readOnly}
                    control={checkbox}
                    label={label}
                    labelPlacement={labelPlacement}
                  />
                ) : (
                  checkbox
                )}

                {error?.message && (
                  <FormHelperText>{error?.message}</FormHelperText>
                )}
              </FormControl>

              <FormQuestionHelperText questionHelperText={questionHelperText} />
            </Box>
          );
        }}
      />
    );
  }
) as <T extends FieldValues>(
  props: CheckboxProps<T> & { ref?: Ref<HTMLInputElement> }
) => ReactElement;

export { FormCheckbox };
