import { Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

interface FormQuestionHelperTextProps {
  questionHelperText?: string;
}
export const FormQuestionHelperText = ({
  questionHelperText,
}: FormQuestionHelperTextProps) => {
  const t = useTranslations();
  if (!questionHelperText) {
    return null;
  }
  return (
    <Typography
      variant="caption"
      color="text.secondary"
      sx={{ display: 'block', mt: 0.5 }}
    >
      {t(questionHelperText)}
    </Typography>
  );
};
