'use client';

import {
  Box,
  type InputBaseComponentProps,
  type SxProps,
  TextField as MuiTextField,
  type TextFieldProps as MuiTextFieldProps,
  type Theme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { forwardRef, type ReactElement, type Ref } from 'react';
import { Controller, type FieldValues, type Path } from 'react-hook-form';
import { NumericFormat, PatternFormat } from 'react-number-format';

import { useReactHookFormContext } from '@/hooks/useFormContext';

import { FormQuestionHelperText } from './form-question-helper-text';
import { FormQuestionLabel } from './form-question-label';

type FormatType =
  | 'number'
  | 'phoneNumber'
  | 'currency'
  | 'socialSecurity'
  | undefined;

type FormInputType =
  | 'text'
  | 'email'
  | 'password'
  | 'number'
  | 'date'
  | 'datetime-local'
  | 'tel'
  | 'url';

type CustomNumberFormatProps = InputBaseComponentProps & {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
};

type TextFieldProps<T extends FieldValues> = Omit<
  MuiTextFieldProps,
  | 'name'
  | 'error'
  | 'helperText'
  | 'label'
  | 'placeholder'
  | 'type'
  | 'onChange'
  | 'required'
> & {
  name: Path<T>;
  label?: string;
  placeholder?: string;
  type?: FormInputType;
  format?: FormatType;
  multiline?: boolean;
  rows?: number;
  asNumber?: boolean;
  onChange?: (value: string | number) => void;
  skipTranslation?: boolean; // If true, label and placeholder are already translated
  isRequired?: boolean; // If true, adds asterisk (*) to label
  questionLabel?: string; // Translation key for question label above the input
  questionNumber?: number; // Question number to display before the question label
  questionHelperText?: string; // Translation key for helper text below the question label
};

const createNumberFormat = (
  formatConfig: {
    format?: string;
    mask?: string;
    thousandSeparator?: boolean;
    allowEmptyFormatting?: boolean;
    preventLeadingZeros?: boolean;
  } = {}
) => {
  return forwardRef<HTMLInputElement, CustomNumberFormatProps>(
    function NumberFormat(props, ref) {
      const { onChange, name, ...other } = props;

      const handleValueChange = (values: { value: string }) => {
        onChange({
          target: {
            name,
            value: values.value,
          },
        });
      };

      // Prevent leading zeros for number inputs
      const handleIsAllowed = (values: {
        value: string;
        floatValue?: number;
      }) => {
        if (!formatConfig.preventLeadingZeros) return true;

        const { value } = values;

        // Allow empty value
        if (!value || value === '') return true;

        // Allow single "0"
        if (value === '0') return true;

        // Allow negative zero
        if (value === '-0') return true;

        // Allow decimals starting with "0." (e.g., "0.5")
        if (value.startsWith('0.') || value.startsWith('-0.')) return true;

        // Prevent leading zeros (e.g., "013", "05")
        if (value.match(/^-?0\d/)) return false;

        return true;
      };

      const Component = formatConfig.format ? PatternFormat : NumericFormat;

      return (
        <Component
          {...other}
          {...formatConfig}
          format={formatConfig.format ?? ''}
          getInputRef={ref}
          onValueChange={handleValueChange}
          isAllowed={handleIsAllowed}
        />
      );
    }
  );
};

const formatComponents = {
  number: createNumberFormat({
    thousandSeparator: true,
    preventLeadingZeros: true,
  }),
  phoneNumber: createNumberFormat({
    format: '(###) ###-####',
    allowEmptyFormatting: true,
    mask: '_',
  }),
  socialSecurity: createNumberFormat({
    format: '### ## ####',
    allowEmptyFormatting: true,
    mask: '_',
  }),
  currency: createNumberFormat({
    thousandSeparator: true,
    preventLeadingZeros: true,
  }),
};

// eslint-disable-next-line react/display-name
export const FormText = forwardRef(
  <T extends FieldValues>(
    {
      name,
      label,
      placeholder,
      type = 'text',
      format,
      multiline,
      rows,
      asNumber,
      onChange,
      skipTranslation = false,
      isRequired = false,
      questionLabel,
      questionNumber,
      questionHelperText,

      sx,
      ...textFieldProps
    }: TextFieldProps<T>,
    ref: Ref<HTMLInputElement>
  ) => {
    const { control } = useReactHookFormContext<T>();
    const t = useTranslations();

    const getInputComponent = (format?: FormatType) => {
      return format ? formatComponents[format] : undefined;
    };

    const isDateLike = type === 'date' || type === 'datetime-local';

    const defaultSx: SxProps<Theme> = {
      width: 1,
      ...sx,
    };

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState: { error } }) => {
          const value = field.value ?? '';

          return (
            <Box>
              <FormQuestionLabel
                questionLabel={questionLabel}
                questionNumber={questionNumber}
                required={isRequired}
              />
              <MuiTextField
                {...textFieldProps}
                {...field}
                value={value}
                onChange={(e) => {
                  const raw = e.target.value;
                  const processedValue = asNumber ? Number(raw) : raw;

                  if (onChange) {
                    onChange(processedValue);
                  } else {
                    field.onChange(processedValue);
                  }
                }}
                inputRef={ref}
                type={type}
                label={label ? (skipTranslation ? label : t(label)) : undefined}
                placeholder={
                  placeholder
                    ? skipTranslation
                      ? placeholder
                      : t(placeholder)
                    : undefined
                }
                multiline={multiline}
                rows={rows}
                error={!!error}
                helperText={error?.message}
                sx={defaultSx}
                slotProps={{
                  ...textFieldProps?.slotProps,
                  inputLabel: isDateLike
                    ? { shrink: true }
                    : textFieldProps?.slotProps?.inputLabel,
                  input: {
                    ...textFieldProps?.slotProps?.input,
                    inputComponent: getInputComponent(format),
                  },
                }}
              />

              <FormQuestionHelperText questionHelperText={questionHelperText} />
            </Box>
          );
        }}
      />
    );
  }
) as <T extends FieldValues>(
  props: TextFieldProps<T> & { ref?: Ref<HTMLInputElement> }
) => ReactElement;
