'use client';

import { Box, type SxProps, type Theme } from '@mui/material';
import {
  DatePicker as MuiDatePicker,
  type DatePickerProps as MuiDatePickerProps,
} from '@mui/x-date-pickers';
import dayjs, { type Dayjs } from 'dayjs';
import { forwardRef, type ReactElement, type Ref } from 'react';
import { Controller, type FieldValues, type Path } from 'react-hook-form';

import { useReactHookFormContext } from '@/hooks/useFormContext';

import { FormQuestionHelperText } from './form-question-helper-text';
import { FormQuestionLabel } from './form-question-label';

type DatePickerProps<T extends FieldValues> = Omit<
  MuiDatePickerProps<false>,
  'name' | 'value' | 'onChange'
> & {
  name: Path<T>;
  questionLabel?: string; // Translation key for question label above the input
  questionNumber?: number; // Question number to display before the question label
  questionHelperText?: string; // Translation key for helper text below the question label
  sx?: SxProps<Theme>;
  isRequired?: boolean; // If true, adds asterisk (*) to label
};

// eslint-disable-next-line react/display-name
const FormDatePicker = forwardRef(
  <T extends FieldValues>(
    {
      name,
      sx,
      questionLabel,
      questionNumber,
      questionHelperText,
      isRequired = false,
      ...datePickerProps
    }: DatePickerProps<T>,
    ref: Ref<HTMLDivElement>
  ) => {
    const { control, readOnly } = useReactHookFormContext<T>();

    const defaultSx: SxProps<Theme> = {
      width: 1,
      ...sx,
    };

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { onChange, value }, fieldState: { error } }) => {
          const isValidDayjs = (val: unknown): val is Dayjs => {
            return dayjs.isDayjs(val) && val.isValid();
          };

          const dateValue = isValidDayjs(value)
            ? value
            : value
              ? dayjs(value as string)
              : null;

          return (
            <Box>
              <FormQuestionLabel
                questionLabel={questionLabel}
                questionNumber={questionNumber}
                required={isRequired}
              />

              <MuiDatePicker
                {...datePickerProps}
                value={dateValue}
                onChange={(newValue) => {
                  const finalValue =
                    newValue && dayjs.isDayjs(newValue) && newValue.isValid()
                      ? newValue.toISOString()
                      : null;
                  onChange(finalValue);
                }}
                ref={ref}
                sx={defaultSx}
                readOnly={readOnly}
                slotProps={{
                  ...datePickerProps.slotProps,
                  textField: {
                    ...datePickerProps.slotProps?.textField,
                    error: !!error,
                    helperText: error?.message,
                    name,
                  },
                }}
              />

              <FormQuestionHelperText questionHelperText={questionHelperText} />
            </Box>
          );
        }}
      />
    );
  }
) as <T extends FieldValues>(
  props: DatePickerProps<T> & { ref?: Ref<HTMLDivElement> }
) => ReactElement;

export { FormDatePicker };
