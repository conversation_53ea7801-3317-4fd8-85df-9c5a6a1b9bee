# Form Input Components

MUI-based form input components integrated with React Hook Form and next-intl, with full TypeScript generic type support.

## Components

- `FormInput` - Basic text input with multiple types support
- `FormSelect` - Select/Autocomplete dropdown
- `FormTextarea` - Multiline text input
- `FormCheckbox` - Checkbox input

## Usage Examples

```tsx
import { useForm, FormProvider } from 'react-hook-form';
import {
  FormInput,
  FormSelect,
  FormTextarea,
  FormCheckbox,
} from '@/components/form-inputs';

type FormData = {
  firstName: string;
  email: string;
  age: number;
  country: string;
  description: string;
  acceptTerms: boolean;
};

export function ExampleForm() {
  const form = useForm<FormData>();

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(console.log)}>
        {/* Text Input with Generic Type */}
        <FormInput<FormData>
          name="firstName"
          label="labels.firstName"
          isRequired
        />

        {/* Email Input */}
        <FormInput<FormData>
          name="email"
          label="labels.email"
          type="email"
          isRequired
        />

        {/* Number Input */}
        <FormInput<FormData>
          name="age"
          label="labels.age"
          type="number"
          asNumber
        />

        {/* Select/Autocomplete */}
        <FormSelect<FormData>
          name="country"
          label="labels.country"
          options={[
            { value: 'de', label: 'Germany' },
            { value: 'at', label: 'Austria' },
            { value: 'ch', label: 'Switzerland' },
          ]}
          autoComplete // Use Autocomplete (default: true)
          isRequired
        />

        {/* Regular Select with custom props */}
        <FormSelect<FormData>
          name="country"
          label="labels.country"
          options={countryOptions}
          autoComplete={false} // Use regular Select
          size="medium"
          textFieldProps={{
            slotProps: {
              inputLabel: { sx: { fontSize: '1.1rem' } },
              select: { sx: { fontSize: '1.1rem' } },
            },
          }}
        />

        {/* Textarea */}
        <FormTextarea<FormData>
          name="description"
          label="labels.description"
          rows={4}
        />

        {/* Checkbox with custom styling */}
        <FormCheckbox<FormData>
          name="acceptTerms"
          label="labels.acceptTerms"
          checkboxProps={{
            sx: {
              color: 'var(--teal)',
              '&.Mui-checked': {
                color: 'var(--teal)',
              },
            },
          }}
          formControlLabelProps={{
            sx: {
              mt: 1,
              '& .MuiFormControlLabel-label': {
                fontSize: '1.1rem',
              },
            },
          }}
        />

        <button type="submit">Submit</button>
      </form>
    </FormProvider>
  );
}
```

## Real-world Example from WelcomeForm

Here's how these components are used in the refactored WelcomeForm:

```tsx
// Generic type-safe form components
<FormSelect<WelcomeFormData>
  name="salutation"
  label="welcome.form.labels.salutation"
  options={[
    { value: 'Herr', label: 'welcome.form.options.mr' },
    { value: 'Frau', label: 'welcome.form.options.ms' },
    { value: 'Divers', label: 'welcome.form.options.diverse' },
  ]}
  autoComplete={false}
  size="medium"
  textFieldProps={{
    slotProps: {
      inputLabel: { sx: { fontSize: '1.1rem' } },
      select: { sx: { fontSize: '1.1rem' } },
    },
  }}
/>

<FormInput<WelcomeFormData>
  name="firstName"
  label="welcome.form.labels.firstName"
  size="medium"
  textFieldProps={{
    slotProps: {
      inputLabel: { sx: { fontSize: '1.1rem' } },
      input: { sx: { fontSize: '1.1rem' } },
    },
  }}
/>

<FormCheckbox<WelcomeFormData>
  name="termsAndConditionsAccepted"
  label="welcome.form.termsCheckbox"
  checkboxProps={{
    sx: {
      color: 'var(--teal)',
      '&.Mui-checked': { color: 'var(--teal)' },
    },
  }}
  formControlLabelProps={{
    sx: {
      mt: 1,
      '& .MuiFormControlLabel-label': { fontSize: '1.1rem' },
    },
  }}
/>
```

## Props

### FormInput

- `name`: Field name (required) - strongly typed with generic
- `label`: Label translation key (required)
- `type`: Input type (`text`, `email`, `password`, `number`, `date`, etc.)
- `isRequired`: Required field
- `asNumber`: Convert value to number
- `placeholder`: Placeholder translation key
- `disabled`: Disable input
- `size`: MUI size (`small` | `medium`)
- `multiline`: Enable multiline (use FormTextarea instead)
- `sx`: MUI sx prop for styling overrides
- `textFieldProps`: Additional TextField props

### FormSelect

- `name`: Field name (required) - strongly typed with generic
- `label`: Label translation key (required)
- `options`: Array of `{ value, label, isDefault? }` objects
- `autoComplete`: Use Autocomplete (true) vs Select (false)
- `isRequired`: Required field
- `placeholder`: Placeholder translation key
- `searchPlaceholder`: Search input placeholder (Autocomplete only)
- `disabled`: Disable select
- `asNumber`: Convert value to number
- `showDefaultOptionsFirst`: Sort default options first
- `size`: MUI size (`small` | `medium`)
- `sx`: MUI sx prop for styling overrides
- `textFieldProps`: Additional props for Autocomplete mode
- `formControlProps`: Additional props for Select mode

### FormTextarea

- `name`: Field name (required) - strongly typed with generic
- `label`: Label translation key (required)
- `rows`: Number of rows (default: 3)
- `minRows`/`maxRows`: Row limits
- `placeholder`: Placeholder translation key
- `disabled`: Disable textarea
- `isRequired`: Required field
- `sx`: MUI sx prop for styling overrides

### FormCheckbox

- `name`: Field name (required) - strongly typed with generic
- `label`: Label translation key (required)
- `disabled`: Disable checkbox
- `sx`: MUI sx prop for styling overrides
- `checkboxProps`: Additional Checkbox MUI props
- `formControlLabelProps`: Additional FormControlLabel MUI props

## Features

- ✅ **React Hook Form integration** - Uses Controller pattern
- ✅ **next-intl translation support** - Automatic label/placeholder translation
- ✅ **Full TypeScript generic support** - `FormInput<MyFormType>`
- ✅ **MUI theming** - Complete integration with MUI theme
- ✅ **Error handling** - Automatic validation error display
- ✅ **Accessibility** - MUI built-in accessibility features
- ✅ **Date input handling** - Automatic label shrink for date inputs
- ✅ **Number conversion** - `asNumber` prop for automatic conversion
- ✅ **Custom onChange handlers** - Override default behavior
- ✅ **Flexible styling** - Pass through sx prop and other MUI props
- ✅ **Select vs Autocomplete** - Choose mode with `autoComplete` prop

All components maintain the same React Hook Form integration while providing better MUI theming and TypeScript support.
