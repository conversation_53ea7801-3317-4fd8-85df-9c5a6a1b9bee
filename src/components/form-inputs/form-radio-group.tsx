'use client';

import {
  Box,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  type SxProps,
  type Theme,
} from '@mui/material';
import { useTranslations } from 'next-intl';
import { Controller, type FieldPath, type FieldValues } from 'react-hook-form';

import { useReactHookFormContext } from '@/hooks/useFormContext';

import { FormQuestionHelperText } from './form-question-helper-text';
import { FormQuestionLabel } from './form-question-label';

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface FormRadioGroupProps<TFieldValues extends FieldValues> {
  name: FieldPath<TFieldValues>;
  label?: string;
  options: RadioOption[];
  row?: boolean;
  disabled?: boolean;
  onChange?: (value: string) => void;
  isRequired?: boolean;
  sx?: SxProps<Theme>;
  questionLabel?: string; // Translation key for question label above the input
  questionNumber?: number; // Question number to display before the question label
  questionHelperText?: string; // Translation key for helper text below the question label
}

export const FormRadioGroup = <TFieldValues extends FieldValues>({
  name,
  label,
  options,
  row = false,
  disabled = false,
  onChange,
  isRequired = false,
  questionLabel,
  questionNumber,
  questionHelperText,
  sx = {
    marginTop: -0.5,
  },
}: FormRadioGroupProps<TFieldValues>) => {
  const { control } = useReactHookFormContext<TFieldValues>();
  const t = useTranslations();

  return (
    <Controller
      name={name as any}
      control={control}
      render={({ field, fieldState }) => {
        const hasError = Boolean(fieldState.error);

        return (
          <Box>
            <FormQuestionLabel
              questionLabel={questionLabel}
              questionNumber={questionNumber}
              required={isRequired}
            />

            <FormControl
              error={hasError}
              sx={sx}
              disabled={disabled}
              required={isRequired}
            >
              {label && <FormLabel>{t(label)}</FormLabel>}
              <RadioGroup
                {...field}
                row={row}
                value={field.value || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  field.onChange(value); // Always update the form value
                  onChange?.(value); // Call custom onChange if provided
                }}
              >
                {options.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    value={option.value}
                    control={<Radio />}
                    label={option.label}
                    disabled={option.disabled || disabled}
                  />
                ))}
              </RadioGroup>
              {hasError && (
                <FormHelperText error>
                  {fieldState.error?.message}
                </FormHelperText>
              )}
            </FormControl>

            <FormQuestionHelperText questionHelperText={questionHelperText} />
          </Box>
        );
      }}
    />
  );
};
