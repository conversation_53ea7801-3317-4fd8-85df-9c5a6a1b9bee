import {
  Autocomplete as MuiAutocomplete,
  type AutocompleteProps as MuiAutocompleteProps,
  type AutocompleteValue,
  TextField as MuiTextField,
} from '@mui/material';
import { Box } from '@mui/material';
import { useTranslations } from 'next-intl';
import { forwardRef, type ReactElement, type Ref, useMemo } from 'react';
import { Controller, type FieldValues, type Path } from 'react-hook-form';

import { useReactHookFormContext } from '@/hooks/useFormContext';

import { FormQuestionHelperText } from './form-question-helper-text';
import { FormQuestionLabel } from './form-question-label';

export type SelectOption = {
  label: string;
  value: string | number;
};

export type AutocompleteProps<
  T extends FieldValues,
  Multiple extends boolean = false,
> = Omit<
  MuiAutocompleteProps<SelectOption, Multiple, false, false>,
  'renderInput' | 'onChange' | 'options' | 'multiple'
> & {
  name: Path<T>;
  label?: string; // Translation key for input label
  textFieldProps?: Omit<
    React.ComponentProps<typeof MuiTextField>,
    'name' | 'error' | 'helperText'
  >;
  options: SelectOption[] | undefined;
  multiple?: Multiple;
  onOptionSelect?: Multiple extends true
    ? (options: SelectOption[]) => void
    : (option: SelectOption | null) => void;
  isRequired?: boolean; // If true, adds asterisk (*) to label
  questionLabel?: string; // Translation key for question label above the input
  questionNumber?: number; // Question number to display before the question label
  questionHelperText?: string; // Translation key for helper text below the question label
  skipTranslation?: boolean; // If true, label and placeholder are already translated
};

// eslint-disable-next-line react/display-name
export const FormSelect = forwardRef(
  <T extends FieldValues, Multiple extends boolean = false>(
    {
      name,
      label,
      options,
      textFieldProps,
      onOptionSelect,
      multiple = false as Multiple,
      isRequired = false,
      questionLabel,
      questionNumber,
      questionHelperText,
      skipTranslation = false,
      ...autocompleteProps
    }: AutocompleteProps<T, Multiple>,
    ref: Ref<HTMLInputElement>
  ) => {
    const { control, readOnly } = useReactHookFormContext<T>();
    const t = useTranslations();

    // Translate option labels
    const translatedOptions = useMemo(() => {
      if (skipTranslation) {
        return options;
      } else {
        return options?.map((opt) => ({ ...opt, label: t(opt.label) }));
      }
    }, [options, t, skipTranslation]);

    return (
      <Controller
        name={name}
        control={control}
        render={({
          field: { onChange, value, ...field },
          fieldState: { error },
        }) => {
          const getValue = (): AutocompleteValue<
            SelectOption,
            Multiple,
            false,
            false
          > => {
            if (multiple) {
              return (translatedOptions ?? []).filter((option) =>
                Array.isArray(value) ? value.includes(option.value) : false
              ) as AutocompleteValue<SelectOption, Multiple, false, false>;
            }
            return ((translatedOptions ?? []).find(
              (option) => option.value === value
            ) || null) as AutocompleteValue<
              SelectOption,
              Multiple,
              false,
              false
            >;
          };

          return (
            <Box>
              <FormQuestionLabel
                questionLabel={questionLabel}
                questionNumber={questionNumber}
                required={isRequired}
              />

              <MuiAutocomplete<SelectOption, Multiple, false, false>
                {...autocompleteProps}
                {...field}
                multiple={multiple}
                options={translatedOptions ?? []}
                value={getValue()}
                readOnly={readOnly}
                id={name}
                onChange={(_, newValue) => {
                  if (multiple) {
                    const values = (newValue as SelectOption[]).map(
                      (option) => option.value
                    );
                    onChange(values);
                    if (onOptionSelect) {
                      (onOptionSelect as (options: SelectOption[]) => void)(
                        newValue as SelectOption[]
                      );
                    }
                  } else {
                    const singleValue = newValue as SelectOption | null;
                    onChange(singleValue?.value ?? '');
                    if (onOptionSelect) {
                      (onOptionSelect as (option: SelectOption | null) => void)(
                        singleValue
                      );
                    }
                  }
                }}
                renderInput={(params) => (
                  <MuiTextField
                    {...textFieldProps}
                    {...params}
                    inputRef={ref}
                    label={
                      label ? (skipTranslation ? label : t(label)) : undefined
                    }
                    error={!!error}
                    helperText={error?.message}
                    placeholder={!options ? t('form.defaultLoading') : ''}
                    slotProps={{
                      input: {
                        ...params.InputProps,
                        readOnly,
                        endAdornment: readOnly
                          ? null
                          : params.InputProps.endAdornment,
                      },
                    }}
                  />
                )}
              />

              <FormQuestionHelperText questionHelperText={questionHelperText} />
            </Box>
          );
        }}
      />
    );
  }
) as <T extends FieldValues, Multiple extends boolean = false>(
  props: AutocompleteProps<T, Multiple> & { ref?: Ref<HTMLInputElement> }
) => ReactElement;
