'use client';

import { Alert, AlertTitle, List, ListItem } from '@mui/material';
import { useFormState } from 'react-hook-form';

import { type ErrorMessage, formatErrors } from '@/utils/formatErrors';
import { humanizeFieldName } from '@/utils/humanizeFieldName';

export interface FormErrorSummaryProps {
  /**
   * Title to display at the top of the error summary
   * @example 'Please correct the following errors'
   * @example 'Bitte korrigieren Sie folgende Fehler'
   */
  title?: string;
  /**
   * Optional function to translate field names
   * If not provided, uses humanizeFieldName as fallback
   * @example (fieldName) => t(`quickCheck.fields.${fieldName}`)
   */
  fieldLabelTranslator?: (fieldName: string) => string;
}

/**
 * FormErrorSummary component displays all form validation errors in a summary format
 *
 * Features:
 * - Only shows after form submission
 * - Groups errors by category (for array fields)
 * - Clickable errors that scroll to and focus the field
 * - Supports nested fields and arrays
 * - Error messages are already translated from Zod schema
 * - Fully reusable across different forms
 *
 * @example
 * // Basic usage (uses humanizeFieldName for labels)
 * <FormErrorSummary title="Please correct the following errors" />
 *
 * @example
 * // With custom field label translation
 * <FormErrorSummary
 *   title={t('quickCheck.validation.errorSummaryTitle')}
 * />
 */
export const FormErrorSummary = ({
  title = 'Please correct the following errors',
  fieldLabelTranslator,
}: FormErrorSummaryProps = {}) => {
  const { errors, isSubmitted } = useFormState();

  // Don't show errors until form is submitted
  if (!isSubmitted || !errors || Object.keys(errors).length === 0) {
    return null;
  }

  // Use provided translator or fallback to humanizeFieldName
  const translateFieldLabel = fieldLabelTranslator || humanizeFieldName;

  // Format errors with optional translation support for field labels
  // Note: Error messages are already translated from Zod schema
  const formattedErrors = formatErrors(errors, translateFieldLabel);

  /**
   * Handles clicking on an error to scroll to and focus the field
   */
  const handleErrorClick = (field: string) => {
    // Remove '.root' suffix if present
    const cleanField = field.endsWith('.root') ? field.slice(0, -5) : field;
    // Convert array notation from [0] to .0 for DOM queries
    const formFieldName = cleanField.replace(/\[(\d+)\]/g, '.$1');

    try {
      // Try to find element by ID first
      const elementById = document.getElementById(formFieldName);
      if (elementById) {
        elementById.scrollIntoView({ behavior: 'smooth', block: 'center' });
        elementById.focus();
        return;
      }

      // Fallback to finding by name attribute
      const elementByName = document.getElementsByName(formFieldName)[0];
      if (elementByName) {
        elementByName.scrollIntoView({ behavior: 'smooth', block: 'center' });
        (elementByName as HTMLElement).focus();
        return;
      }

      console.warn(`No element found for field: ${formFieldName}`);
    } catch (error) {
      console.error('Error focusing field:', formFieldName, error);
    }
  };

  /**
   * Gets display label for an error, handling special cases like 'root'
   */
  const getDisplayLabel = (label: string, field: string) => {
    if (label.toLowerCase() === 'root') {
      const parentKey = field.split('.root')[0].split('.').pop() || field;
      return translateFieldLabel(parentKey);
    }
    return label;
  };

  // Group errors by category (for array fields)
  const groupedErrors = formattedErrors.reduce(
    (acc: Record<string, ErrorMessage[]>, error) => {
      if (error.category) {
        if (!acc[error.category]) {
          acc[error.category] = [];
        }
        acc[error.category].push(error);
      } else {
        if (!acc['general']) {
          acc['general'] = [];
        }
        acc['general'].push(error);
      }
      return acc;
    },
    {}
  );

  return (
    <Alert
      severity="error"
      sx={{
        mb: 2,
        '& .MuiAlert-message': { width: '100%' },
      }}
    >
      <AlertTitle>{title}</AlertTitle>
      <List dense>
        {/* General errors (non-array fields) */}
        {groupedErrors['general']?.map(({ label, message, field }, index) => (
          <ListItem
            key={`general-${index}`}
            sx={{
              px: 0,
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' },
            }}
            onClick={() => handleErrorClick(field)}
          >
            • {getDisplayLabel(label, field)}: {message}
          </ListItem>
        ))}

        {/* Categorized errors (array fields) */}
        {Object.entries(groupedErrors).map(([category, errors]) => {
          if (category === 'general') return null;

          return (
            <div key={category}>
              <ListItem sx={{ px: 0, fontWeight: 'bold' }}>
                {category}:
              </ListItem>
              {errors.map(({ label, message, field, index }, i) => (
                <ListItem
                  key={`${category}-${index}-${i}`}
                  sx={{
                    px: 2,
                    cursor: 'pointer',
                    '&:hover': { textDecoration: 'underline' },
                  }}
                  onClick={() => handleErrorClick(field)}
                >
                  • {index !== undefined ? `#${index + 1} - ` : ''}
                  {getDisplayLabel(label, field)}: {message}
                </ListItem>
              ))}
            </div>
          );
        })}
      </List>
    </Alert>
  );
};
