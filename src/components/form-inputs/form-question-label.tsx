import { Box, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

interface FormQuestionLabelProps {
  questionLabel?: string;
  questionNumber?: number;
  required?: boolean;
}
export const FormQuestionLabel = ({
  questionLabel,
  questionNumber,
  required,
}: FormQuestionLabelProps) => {
  const t = useTranslations();
  if (!questionLabel) {
    return null;
  }
  return (
    <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
      {questionNumber && (
        <Box component="span" sx={{ mr: 2 }}>
          {questionNumber}
        </Box>
      )}
      {t(questionLabel)}
      {required && (
        <Box component="span" sx={{ color: 'var(--error-red)', ml: 0.5 }}>
          *
        </Box>
      )}
    </Typography>
  );
};
