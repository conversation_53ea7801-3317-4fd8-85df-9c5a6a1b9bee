// src/components/AgentForm.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Button, FormControl, TextField, SelectChangeEvent, Tooltip, Typography, Box } from '@mui/material';
import StaticBox from './box/StaticBox';

interface AgentFormProps {
    agent_number?: string;
    edit_mode?: boolean;
}

const AgentForm: React.FC<AgentFormProps> = ({ agent_number, edit_mode = false }) => {
    const [formData, setFormData] = useState({
        documentId: '',
        username: '',
        company_name: '',
        city: '',
        street: '',
        house_number: '',
        postal_code: '',
        email: '',
        url: '',
        telephone_number: '',
        commission: 0,
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const router = useRouter();

    useEffect(() => {
        console.log("[useEffect] edit_mode:", edit_mode);
        console.log("[useEffect] agent_number:", agent_number);
        if (edit_mode && agent_number) {
            fetch(`/api/agent/${agent_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
            })
                .then((res) => {
                    if (!res.ok) throw new Error("Fehler beim Laden des Kunden");
                    return res.json();
                })
                .then((data) => setFormData(data))
                .catch((err) => {
                    console.error(err);
                    alert("Fehler beim Laden der Kundendaten.");
                });
        }
    }, [edit_mode, agent_number]);

    // Implementation signature
    function handleChange(event: any, child?: React.ReactNode) {
        const { name, value } = event.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    }

    const validateFields = () => {
        const validators = {
            postal_code: (val: string) => /^\d{5}$/.test(val),
            email: (val: string) => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(val),
            first_name: (val: string) => /^[A-Za-zÄÖÜäöüß -]+$/.test(val),
            last_name: (val: string) => /^[A-Za-zÄÖÜäöüß -]+$/.test(val),
            city: (val: string) => /^[A-Za-zÄÖÜäöüß .\-]+$/.test(val),
            street: (val: string) => /^[A-Za-zÄÖÜäöüß .\-]+$/.test(val),
        };
        const fieldLabels: { [key: string]: string } = {
            postal_code: 'Postleitzahl',
            email: 'E-Mail-Adresse',
            house_number: 'Hausnummer',
            first_name: 'Vorname',
            last_name: 'Nachname',
            city: 'Ort',
            street: 'Straße',
        };

        const invalidFields = Object.entries(validators)
            .filter(([key, validate]) => !validate((formData as any)[key]))
            .map(([key]) => fieldLabels[key]);

        if (invalidFields.length > 0) {
            alert(`Bitte überprüfen Sie folgende Felder:\n\n${invalidFields.join('\n')}`);
            return false;
        }
        return true;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateFields()) return;

        setIsSubmitting(true);

        try {
            const method = 'PUT';
            const url = `/api/agent/update`;

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify({ agentData: formData }),
            });

            if (response.ok) {
                alert(`Der Makler wurde erfolgreich aktualisiert.`);
                router.push(`/agent/${agent_number}`);
            } else {
                alert(`Fehler beim aktualisieren des Maklers.`);
            }
        } catch (error) {
            console.error('Die Eingaben konnten nicht übergeben werden:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Makler anpassen
            </Typography>
            <form onSubmit={handleSubmit}>
                <div className='space-y-4 mx-auto relative'>
                    <StaticBox title='Maklerangaben'>

                        <div className='grid grid-cols-2 gap-2'>

                            <Tooltip title="Geben Sie den Vor- und Nachname des Maklers an.">
                                <TextField
                                    label="Maklername"
                                    type="text"
                                    name="username"
                                    value={formData.username}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Agenur an für die der Makler arbeitet.">
                                <TextField
                                    label="Agenurname"
                                    type="text"
                                    name="company_name"
                                    value={formData.company_name}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 gap-y-4 pt-8'>
                            <Tooltip title="Geben Sie die Straße der Makleradresse ein.">
                                <TextField
                                    type="text"
                                    label="Straße"
                                    name="street"
                                    value={formData.street}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Hausnummer der Makleradresse ein.">
                                <TextField
                                    type="text"
                                    label="Hausnummer"
                                    name="house_number"
                                    value={formData.house_number}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Postleitzahl (PLZ) der Makleradresse ein.">
                                <TextField
                                    type="text"
                                    name="postal_code"
                                    label="Postleitzahl"
                                    value={formData.postal_code}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie den Ort oder die Stadt der Makleradresse ein.">
                                <TextField
                                    type="text"
                                    label="Ort"
                                    name="city"
                                    value={formData.city}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 gap-y-4 pt-8'>
                            <Tooltip title="Geben Sie die Emailadresse des Maklers ein. Diese ist auch die Emailadresse für den Login.">
                                <TextField
                                    type="email"
                                    label="Emailadresse"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Website des Maklers ein.">
                                <TextField
                                    type="text"
                                    label="Website"
                                    name="url"
                                    value={formData.url}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Telefonnummer des Maklers ein.">
                                <TextField
                                    type="text"
                                    label="Telefonnummer"
                                    name="telephone_number"
                                    value={formData.telephone_number}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                />
                            </Tooltip>
                            <Tooltip title="Setzen Sie den Courtagewert des Maklers.">
                                <TextField
                                    type="number"
                                    label="Courtage"
                                    name="commission"
                                    value={(formData.commission * 100).toFixed(0)}
                                    onChange={(event) => {
                                        const newValue = parseFloat(event.target.value.replace(',', '.')) / 100;
                                        handleChange({
                                            target: {
                                                name: "commission",
                                                value: String(newValue),
                                            },
                                        });
                                    }}
                                    className="w-full p-2"
                                    required
                                    InputLabelProps={{
                                        shrink: true,
                                    }}
                                    InputProps={{
                                        endAdornment: <span>%</span>,
                                    }}
                                />
                            </Tooltip>
                        </div>
                    </StaticBox>
                    <div className='flex gap-4 justify-end'>
                        <Tooltip title="Ohne sichern zurück.">
                            <Button
                                type="button"
                                onClick={() => window.history.back()}
                                variant='contained'
                            >
                                Verwerfen
                            </Button>
                        </Tooltip>
                        <Tooltip title={edit_mode ? 'Makler aktualisieren' : 'Makler anlegen'}>
                            <Button
                                type="submit"
                                variant='contained'
                                color='secondary'
                                disabled={isSubmitting}>
                                {isSubmitting
                                    ? (edit_mode ? 'Aktualisieren...' : 'Anlegen...')
                                    : (edit_mode ? 'Makler aktualisieren' : 'Makler anlegen')}
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </form>
        </Box>
    );
}

export default AgentForm;