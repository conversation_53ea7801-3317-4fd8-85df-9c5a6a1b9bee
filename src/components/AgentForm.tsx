// src/components/AgentForm.tsx
import { Box, Button, TextField, Tooltip, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';

import type { Agent } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';

import StaticBox from './box/StaticBox';

interface AgentFormProps {
  agentNumber?: string;
  editMode?: boolean;
}

type AgentFormState = Partial<Agent>;

const EMPTY: AgentFormState = {
  documentId: '',
  username: '',
  companyName: '',
  street: '',
  houseNumber: '',
  postalCode: '',
  city: '',
  email: '',
  url: '',
  telephoneNumber: '',
  commission: 0,
  agentNumber: '',
  agencyNumber: '',
};

const AgentForm: React.FC<AgentFormProps> = ({
  agentNumber,
  editMode = false,
}) => {
  const router = useRouter();
  const [formData, setFormData] = useState<AgentFormState>(EMPTY);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load existing agent when editing
  useEffect(() => {
    if (!editMode || !agentNumber) return;

    (async () => {
      try {
        const data = await apiFetch<Agent>(
          `/api/agents/${encodeURIComponent(agentNumber)}`
        );

        setFormData({
          documentId: data.documentId ?? '',
          username: data.username ?? '',
          companyName: data.companyName ?? '',
          street: data.street ?? '',
          houseNumber: data.houseNumber ?? '',
          postalCode: data.postalCode ?? '',
          city: data.city ?? '',
          email: data.email ?? '',
          url: data.url ?? '',
          telephoneNumber: data.telephoneNumber ?? '',
          commission: data.commission ?? 0,
          agentNumber: data.agentNumber ?? '',
          agencyNumber: data.agencyNumber ?? '',
        });
      } catch (err) {
        console.error(err);
        alert('Fehler beim Laden der Maklerdaten.');
      }
    })();
  }, [editMode, agentNumber]);

  function handleChange<K extends keyof AgentFormState>(
    name: K,
    value: AgentFormState[K]
  ) {
    setFormData((prev) => ({ ...prev, [name]: value }));
  }

  // Commission as percent in UI
  const commissionPercent = useMemo(
    () => (formData.commission ?? 0) * 100,
    [formData.commission]
  );

  // Light client-side validation (API still authoritative)
  const validate = () => {
    const invalid: string[] = [];

    const isEmail =
      !formData.email || /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(formData.email);

    const isPostal =
      !formData.postalCode || /^\d{5}$/.test(formData.postalCode);

    if (!isEmail) invalid.push('E-Mail-Adresse');
    if (!isPostal) invalid.push('Postleitzahl');

    const alphaSpace = /^[A-Za-zÄÖÜäöüß .-]*$/;
    if (formData.city && !alphaSpace.test(formData.city)) invalid.push('Ort');
    if (formData.street && !alphaSpace.test(formData.street))
      invalid.push('Straße');

    if (invalid.length) {
      alert(`Bitte überprüfen Sie folgende Felder:\n\n${invalid.join('\n')}`);
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;

    setIsSubmitting(true);
    try {
      // convert commission back from % to fraction for API
      const payload: Agent = {
        // cast to Agent — API will ignore/validate non-writable or missing fields
        ...(formData as Agent),
        commission: Number(formData.commission ?? 0),
      };

      const res = await apiFetch('/api/agents', {
        method: 'PUT',
        raw: true,
        body: JSON.stringify({ agent: payload }),
      });

      if (!res.ok) {
        const msg = await res.text();
        console.error('Update failed:', msg);
        alert('Fehler beim Aktualisieren des Maklers.');
        return;
      }

      alert('Der Makler wurde erfolgreich aktualisiert.');
      const targetAgentNumber = formData.agentNumber || agentNumber;
      if (targetAgentNumber) {
        router.push(`/agent/${encodeURIComponent(String(targetAgentNumber))}`);
      } else {
        router.back();
      }
    } catch (err) {
      console.error('Submit error:', err);
      alert('Die Eingaben konnten nicht übergeben werden.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        {editMode ? 'Makler anpassen' : 'Makler anlegen'}
      </Typography>

      <form onSubmit={handleSubmit}>
        <div className="space-y-4 mx-auto relative">
          <StaticBox title="Maklerangaben">
            <div className="grid grid-cols-2 gap-2">
              <Tooltip title="Geben Sie den Vor- und Nachnamen des Maklers an.">
                <TextField
                  label="Maklername"
                  name="username"
                  value={formData.username ?? ''}
                  onChange={(e) => handleChange('username', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>

              <Tooltip title="Geben Sie die Agentur an, für die der Makler arbeitet.">
                <TextField
                  label="Agenturname"
                  name="companyName"
                  value={formData.companyName ?? ''}
                  onChange={(e) => handleChange('companyName', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>
            </div>

            <div className="grid grid-cols-2 gap-2 gap-y-4 pt-8">
              <Tooltip title="Geben Sie die Straße der Makleradresse ein.">
                <TextField
                  label="Straße"
                  name="street"
                  value={formData.street ?? ''}
                  onChange={(e) => handleChange('street', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>

              <Tooltip title="Geben Sie die Hausnummer der Makleradresse ein.">
                <TextField
                  label="Hausnummer"
                  name="houseNumber"
                  value={formData.houseNumber ?? ''}
                  onChange={(e) => handleChange('houseNumber', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>

              <Tooltip title="Geben Sie die Postleitzahl (PLZ) der Makleradresse ein.">
                <TextField
                  label="Postleitzahl"
                  name="postalCode"
                  value={formData.postalCode ?? ''}
                  onChange={(e) => handleChange('postalCode', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>

              <Tooltip title="Geben Sie den Ort oder die Stadt der Makleradresse ein.">
                <TextField
                  label="Ort"
                  name="city"
                  value={formData.city ?? ''}
                  onChange={(e) => handleChange('city', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>
            </div>

            <div className="grid grid-cols-2 gap-2 gap-y-4 pt-8">
              <Tooltip title="Geben Sie die Emailadresse des Maklers ein. Diese ist auch die Emailadresse für den Login.">
                <TextField
                  type="email"
                  label="E-Mail-Adresse"
                  name="email"
                  value={formData.email ?? ''}
                  onChange={(e) => handleChange('email', e.target.value)}
                  fullWidth
                  required
                />
              </Tooltip>

              <Tooltip title="Geben Sie die Website des Maklers ein.">
                <TextField
                  label="Website"
                  name="url"
                  value={formData.url ?? ''}
                  onChange={(e) => handleChange('url', e.target.value)}
                  fullWidth
                />
              </Tooltip>

              <Tooltip title="Geben Sie die Telefonnummer des Maklers ein.">
                <TextField
                  label="Telefonnummer"
                  name="telephoneNumber"
                  value={formData.telephoneNumber ?? ''}
                  onChange={(e) =>
                    handleChange('telephoneNumber', e.target.value)
                  }
                  fullWidth
                />
              </Tooltip>

              <Tooltip title="Setzen Sie den Courtagewert des Maklers.">
                <TextField
                  type="number"
                  label="Courtage"
                  name="commission"
                  value={
                    Number.isFinite(commissionPercent) ? commissionPercent : 0
                  }
                  onChange={(e) => {
                    const pct = parseFloat(e.target.value.replace(',', '.'));
                    const fraction = isNaN(pct) ? 0 : pct / 100;
                    handleChange('commission', fraction);
                  }}
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                  InputProps={{ endAdornment: <span>%</span> }}
                />
              </Tooltip>
            </div>
          </StaticBox>

          <div className="flex gap-4 justify-end">
            <Tooltip title="Ohne sichern zurück.">
              <Button
                type="button"
                onClick={() => window.history.back()}
                variant="contained"
              >
                Verwerfen
              </Button>
            </Tooltip>

            <Tooltip
              title={editMode ? 'Makler aktualisieren' : 'Makler anlegen'}
            >
              <Button
                type="submit"
                variant="contained"
                color="secondary"
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? editMode
                    ? 'Aktualisieren…'
                    : 'Anlegen…'
                  : editMode
                    ? 'Makler aktualisieren'
                    : 'Makler anlegen'}
              </Button>
            </Tooltip>
          </div>
        </div>
      </form>
    </Box>
  );
};

export default AgentForm;
