// src/components/AgentDetails.tsx
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { AgentData, AttachmentData } from '../types';
import AgentInformationBox from './box/AgentInformationBox';
import AgentFilesBox from './box/AgentFilesBox';
import { Button, Tooltip, CircularProgress, Typography, Box, Grid2 as Grid } from '@mui/material';
import {
    Description,
    Group,
    GppMaybe as GppMaybeIcon,
    Receipt as ReceiptIcon,
    FolderOpen as FolderOpenIcon,
    Upload as UploadIcon,
} from "@mui/icons-material";
import RevisionsModal from './modal/RevisionsModal';
import StaticBox from './box/StaticBox';


const AgentDetails = () => {
    const router = useRouter();
    const { agent_number } = router.query;
    const [agent, setAgent] = useState<AgentData | null>(null);
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [loading, setLoading] = useState(true);
    const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || ""
        const fetchData = async () => {
            try {
                const agentResponse = await fetch(`/api/agent/${agent_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                })

                const attachmentsResponse = await fetch(`/api/file/${agent_number}/all`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                })

                if (!agentResponse.ok || !attachmentsResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const agentData = await agentResponse.json()
                const attachmentsData = await attachmentsResponse.json()

                setAttachments(attachmentsData)
                setAgent(agentData)

            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        }
        fetchData()
    }, [agent_number]);

    const handleUploadFile = async () => {
        if (!fileInputRef.current || !fileInputRef.current.files || fileInputRef.current.files.length === 0) {
            alert('Bitte wählen Sie eine Datei aus.');
            return;
        }

        const file = fileInputRef.current.files[0];

        const formData = new FormData();
        formData.append('file', file);
        formData.append('filename', file.name);
        formData.append('agent_number', agent_number as string);

        const response = await fetch(`/api/file/upload`, {
            method: 'POST',
            headers: {
                // 'Content-Type': 'application/json', // REMOVE THIS
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
            body: formData,
        });

        if (response.ok) {
            alert('Datei wurde erfolgreich hochgeladen.');
        } else {
            console.error('Fehler beim Hochladen der Datei.');
            const errorData = await response.json();
            alert(`Fehler beim Hochladen der Datei: ${errorData.error}`);
        }
    };

    const handleDownloadAttachment = async (file_name: string, documentId: string) => {
        const response = await fetch(`/api/file/${agent!.agent_number}/get/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = file_name;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download Attachment');
        }
    };

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (!agent) {
        return <div>Lade Kundendaten...</div>;
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Maklerdetails
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary" >
                {agent.username}, {agent.company_name}
            </Typography>

            {/* agent infos */}
            <AgentInformationBox
                agent_number={agent_number as string}
                visible
                edit_button
            />

            {/* actions */}
            <StaticBox title="Aktionen">
                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
                            <Button
                                component="label"
                                fullWidth
                                variant="contained"
                            >
                                <FolderOpenIcon sx={{ mr: 1 }} />
                                Datei auswählen
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    hidden
                                />
                            </Button>
                        </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
                            <Button
                                onClick={handleUploadFile}
                                fullWidth
                                variant="contained"
                            >
                                <UploadIcon sx={{ mr: 1 }} />
                                Datei hochladen
                            </Button>
                        </Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier geht es zur Liste mit den Kunden, die der Makler sehen darf.`}>
                            <Button
                                onClick={onShowCustomersClicked(agent.agency_number)}
                                fullWidth
                                variant="contained"
                            >
                                <Group sx={{ mr: 1 }} />
                                Kunden anzeigen
                            </Button>
                        </Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier geht es zur Liste mit den Verträgen, die der Makler sehen darf.`}>
                            <Button
                                onClick={onShowContractsClicked(agent.agency_number)}
                                fullWidth
                                variant="contained"
                            >
                                <Description sx={{ mr: 1 }} />
                                Verträge anzeigen
                            </Button>
                        </Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier geht es zur Liste mit den Schäden, die der Makler sehen darf.`}>
                            <Button
                                onClick={onShowReportsClicked(agent.agency_number)}
                                fullWidth
                                variant="contained"
                            >
                                <GppMaybeIcon sx={{ mr: 1 }} />
                                Schäden anzeigen
                            </Button>
                        </Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier geht es zur Liste mit den Rechnungen, die der Makler sehen darf.`}>
                            <Button
                                onClick={onShowInvoicesClicked(agent.agency_number)}
                                fullWidth
                                variant="contained"
                            >
                                <ReceiptIcon sx={{ mr: 1 }} />
                                Rechnungen anzeigen
                            </Button>
                        </Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <RevisionsModal type={'agent'} number={agent_number as string} />
                    </Grid>
                </Grid>
            </StaticBox>

            {/* files */}
            {attachments && attachments.length > 0 && (
                <AgentFilesBox
                    agent_number={agent.agent_number}
                    attachments={attachments}
                    visible
                    handleDownloadAttachment={handleDownloadAttachment}
                />
            )}

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>
                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>

        </Box>
    );

    function onShowCustomersClicked(agency_number: string) {
        return () => router.push(`/customers?page=1&limit=10&sortField=last_name&sortDirection=asc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }

    function onShowContractsClicked(agency_number: string) {
        return () => router.push(`/contracts?page=1&limit=10&sortField=insurance_start_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number, 'active_status': 'ACTIVE' }))}`);
    }

    function onShowReportsClicked(agency_number: string) {
        return () => router.push(`/reports?page=1&limit=10&sortField=damage_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }

    function onShowInvoicesClicked(agency_number: string) {
        return () => router.push(`/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }
};

export default AgentDetails;
