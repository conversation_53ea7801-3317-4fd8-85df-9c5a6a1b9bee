// src/components/AgentDetails.tsx
import {
  Description,
  FolderOpen as FolderOpenIcon,
  GppMaybe as GppMaybeIcon,
  Group,
  Receipt as ReceiptIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useRef, useState } from 'react';

import { type Agent } from '@/generated/prisma-postgres';
import { type AttachmentData } from '@/types';
import { apiFetch } from '@/utils/apiFetch';

import AgentFilesBox from './box/AgentFilesBox';
import AgentInformationBox from './box/AgentInformationBox';
import StaticBox from './box/StaticBox';
import RevisionsModal from './modal/RevisionsModal';

const AgentDetails = () => {
  const router = useRouter();
  const { agentNumber } = router.query;
  const [agent, setAgent] = useState<Agent | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [loading, setLoading] = useState(true);
  const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);
  const { data } = useSession();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const agentData = await apiFetch<Agent>(`/api/agents/${agentNumber}`, {
          method: 'GET',
          accessToken: data?.accessToken,
        });

        const attachmentsData = await apiFetch<any>(
          `/api/agents/${agentNumber}/attachments`,
          {
            method: 'GET',
            accessToken: data?.accessToken,
          }
        );

        setAttachments(attachmentsData);
        setAgent(agentData);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [agentNumber, data?.accessToken]);

  const handleUploadFile = async () => {
    if (
      !fileInputRef.current ||
      !fileInputRef.current.files ||
      fileInputRef.current.files.length === 0
    ) {
      alert('Bitte wählen Sie eine Datei aus.');
      return;
    }

    const file = fileInputRef.current.files[0];

    const formData = new FormData();
    formData.append('file', file);
    formData.append('filename', file.name);
    formData.append('agent_number', agentNumber as string);

    await apiFetch<any>(`/api/file/upload`, {
      method: 'POST',
      body: formData,
    });
  };

  const handleDownloadAttachment = async (
    file_name: string,
    documentId: string
  ) => {
    try {
      const res = await apiFetch(
        `/api/file/${agent!.agentNumber}/get/${documentId}`,
        {
          method: 'GET',
          raw: true,
        }
      );

      const blob = await (res as Response).blob();
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = file_name;
      link.click();

      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download attachment', err);
    }
  };

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (!agent) {
    return <div>Lade Kundendaten...</div>;
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Maklerdetails
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {agent.username}, {agent.companyName}
      </Typography>

      {/* agent infos */}
      <AgentInformationBox
        agentNumber={agentNumber as string}
        visible
        editButton
      />

      {/* actions */}
      <StaticBox title="Aktionen">
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
              <Button component="label" fullWidth variant="contained">
                <FolderOpenIcon sx={{ mr: 1 }} />
                Datei auswählen
                <input type="file" ref={fileInputRef} hidden />
              </Button>
            </Tooltip>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
              <Button onClick={handleUploadFile} fullWidth variant="contained">
                <UploadIcon sx={{ mr: 1 }} />
                Datei hochladen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip
              title={`Hier geht es zur Liste mit den Kunden, die der Makler sehen darf.`}
            >
              <Button
                onClick={onShowCustomersClicked(agent.agencyNumber || '')}
                fullWidth
                variant="contained"
              >
                <Group sx={{ mr: 1 }} />
                Kunden anzeigen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip
              title={`Hier geht es zur Liste mit den Verträgen, die der Makler sehen darf.`}
            >
              <Button
                onClick={onShowContractsClicked(agent.agencyNumber || '')}
                fullWidth
                variant="contained"
              >
                <Description sx={{ mr: 1 }} />
                Verträge anzeigen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip
              title={`Hier geht es zur Liste mit den Schäden, die der Makler sehen darf.`}
            >
              <Button
                onClick={onShowReportsClicked(agent.agencyNumber || '')}
                fullWidth
                variant="contained"
              >
                <GppMaybeIcon sx={{ mr: 1 }} />
                Schäden anzeigen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip
              title={`Hier geht es zur Liste mit den Rechnungen, die der Makler sehen darf.`}
            >
              <Button
                onClick={onShowInvoicesClicked(agent.agencyNumber || '')}
                fullWidth
                variant="contained"
              >
                <ReceiptIcon sx={{ mr: 1 }} />
                Rechnungen anzeigen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <RevisionsModal type={'agents'} number={agentNumber as string} />
          </Grid>
        </Grid>
      </StaticBox>

      {/* files */}
      {attachments && attachments.length > 0 && (
        <AgentFilesBox
          agent_number={agent.agentNumber || ''}
          attachments={attachments}
          visible
          handleDownloadAttachment={handleDownloadAttachment}
        />
      )}

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>
    </Box>
  );

  function onShowCustomersClicked(agency_number: string) {
    return () =>
      router.push(
        `/customers?page=1&limit=10&sortField=last_name&sortDirection=asc&filters=${encodeURIComponent(JSON.stringify({ agency_number: agency_number }))}`
      );
  }

  function onShowContractsClicked(agency_number: string) {
    return () =>
      router.push(
        `/contracts?page=1&limit=10&sortField=insurance_start_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agency_number: agency_number, active_status: 'ACTIVE' }))}`
      );
  }

  function onShowReportsClicked(agency_number: string) {
    return () =>
      router.push(
        `/reports?page=1&limit=10&sortField=damage_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agency_number: agency_number }))}`
      );
  }

  function onShowInvoicesClicked(agency_number: string) {
    return () =>
      router.push(
        `/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agency_number: agency_number }))}`
      );
  }
};

export default AgentDetails;
