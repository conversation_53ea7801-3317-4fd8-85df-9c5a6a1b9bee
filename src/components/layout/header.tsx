'use client';
import { Stack, Typography } from '@mui/material';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

interface HeaderProps {
  children?: React.ReactNode;
}
export default function Header({ children }: HeaderProps) {
  const t = useTranslations();

  return (
    <>
      <header
        style={{
          backgroundColor: 'var(--header-background)',
          minHeight: '105px',
          padding: '16px 32px',
        }}
      >
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
          justifyContent={'space-between'}
          display="flex"
          flexWrap="wrap"
          alignContent="center"
        >
          {/* Left section - Logo and title */}
          <Stack
            direction="row"
            spacing={5}
            alignItems="center"
            display="flex"
            flexWrap="wrap"
            justifyContent="center"
          >
            <Link href="/start">
              <Image
                src="/images/kvlogo.svg"
                alt="KV care logo"
                style={{
                  cursor: 'pointer',
                }}
                width={145}
                height={63}
              />
            </Link>
            <Typography
              display="flex"
              variant="body1"
              sx={{
                marginTop: 'auto',
                paddingTop: '4px',
              }}
            >
              {t('header.title')}
            </Typography>
          </Stack>

          {/* Right section - Controls */}
          {children && children}
        </Stack>
      </header>
    </>
  );
}
