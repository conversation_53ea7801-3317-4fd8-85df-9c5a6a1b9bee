"use client";

import { useState } from "react";
import { Box, Typography, IconButton, Menu, MenuItem, ListItemIcon } from "@mui/material";
import { useTranslations } from "next-intl";
import Image from "next/image";
import MenuIcon from "@mui/icons-material/Menu";
import LogoutIcon from "@mui/icons-material/Logout";
import ListAltIcon from "@mui/icons-material/ListAlt";
import AssignmentIcon from "@mui/icons-material/Assignment";
import { useRouter } from "next/navigation";
import {paths} from "@/paths";

interface HeaderProps {
  showMenu?: boolean;
}

export default function Header({ showMenu = false }: HeaderProps) {
  const t = useTranslations();
  const router = useRouter();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNavigate = (path: string) => {
    handleMenuClose();
    router.push(path);
  };

  const handleLogout = async () => {
    handleMenuClose();
    document.cookie = "referrerSession=; Path=/; Max-Age=0; SameSite=Lax";
    router.push(paths.referrer.unauthorized);
  };

  return (
    <Box
      sx={{
        position: "fixed",
        top: 0,
        zIndex: (theme) => theme.zIndex.appBar,
        width: "100%",
        height: "3rem",
        padding: "0 1rem",
        backgroundColor: "white",
        boxShadow: 1,
        display: "flex",
        alignItems: "center",
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: "1rem", flexGrow: 1 }}>
        <Box sx={{ position: "relative", width: 120, height: 40 }}>
          <Image src="/logo.svg" alt="Logo" fill style={{ objectFit: "contain" }} />
        </Box>
        <Typography variant="h5" color="primary">
          {t("header.title")}
        </Typography>
      </Box>

      {/* only when showMenu is true */}
      {showMenu && (
        <>
          <IconButton edge="end" color="primary" onClick={handleMenuOpen}>
            <MenuIcon />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
            transformOrigin={{ vertical: "top", horizontal: "right" }}
          >
            <MenuItem onClick={() => handleNavigate(paths.referrer.makeReferral)}>
              <ListItemIcon>
                <AssignmentIcon fontSize="small" />
              </ListItemIcon>
              {t("menu.form")}
            </MenuItem>
            <MenuItem onClick={() => handleNavigate(paths.referrer.referralsHistory)}>
              <ListItemIcon>
                <ListAltIcon fontSize="small" />
              </ListItemIcon>
              {t("menu.list")}
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              {t("menu.logout")}
            </MenuItem>
          </Menu>
        </>
      )}
    </Box>
  );
}
