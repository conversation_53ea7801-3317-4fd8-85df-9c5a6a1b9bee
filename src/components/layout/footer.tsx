import { type ReactElement } from 'react';

import TextLinkGroup from '@/components/common/textlinks';

export default function Footer(): ReactElement {
  return (
    <footer
      style={{
        width: '',
        backgroundColor: 'var(--footer-background)',
        color: 'var(--footer-text-color)',
        fontWeight: 400,
        // leadingTrim: '',
        lineHeight: '100%',
        letterSpacing: '0px',
        textAlign: 'center',
        minHeight: '55px',
        display: 'flex',
        flexDirection: 'column',
        paddingTop: '18px',
        paddingBottom: '18px',
        // alignSelf: 'flex-end',
        // minWidth: '100%',
        // position: 'absolute',
        // bottom: 0,
      }}
    >
      <TextLinkGroup
        links={[
          { label: 'Impressum', href: '/imprint' },
          { label: 'Datenschutz', href: '/privacy-policy' },
          { label: 'Nutzungsbedingung', href: '/terms-of-service' },
        ]}
      />
    </footer>
  );
}
