'use client';

import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import { Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import KvcMainMenu from '@/components/menu/KvcMainMenu';
import { KvModal } from '@/components/modal/modal';
import ModalByUrlProvider from '@/components/modal/modalByUrlProvider';
import { UserAvatar } from '@/components/user/useravatar';
import { useUserSuspense } from '@/modules/user/hooks/useUser';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

export function UserProfileNavigation() {
  const t = useTranslations();
  const [showModal, setShowModal] = useState(false);
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const { user, userInitials, userName } = useUserSuspense();

  return (
    <>
      <Stack
        direction="row"
        spacing={4}
        alignItems="right"
        display="flex"
        flexWrap="wrap"
      >
        {/*  !!! This functionality is disabled for now but we assume that it will be required in the future.  */}
        {/* <FormControlLabel
              control={
                <Switch
                  defaultChecked
                  onChange={() => {
                    setShowModal(!showModal);
                  }}
                />
              }
              label={t('header.view_switch_label')}
              labelPlacement="start"
              sx={{
                '& .MuiFormControlLabel-label': {
                  marginRight: 1,
                },
              }}
            /> */}
        <UserAvatar
          userInitials={userInitials}
          userName={userName}
          onClick={() => {
            // setShowModal(!showModal);
            setMenuIsOpen(true);
          }}
        />
      </Stack>

      {showModal && (
        <KvModal
          onClose={() => {
            setShowModal(false);
          }}
        >
          <Stack
            direction="column"
            display="flex"
            flexWrap="wrap"
            justifyContent="center"
          >
            <SentimentNeutralIcon
              sx={{
                fontSize: 180,
                color: 'var(--modal-warning-icon-color)',
                alignContent: 'center',
                marginLeft: 'auto',
                marginRight: 'auto',
              }}
            />

            <Typography
              color="var(--modal-headline-color)"
              aria-labelledby="modal-title"
              variant="h5"
              component="h1"
              fontWeight={700}
              textAlign="center"
            >
              {t.rich(
                'modal.warning_no_offers.headline',
                intlTranslationRichHelper
              )}
            </Typography>

            <Typography textAlign="center">
              {t.rich(
                'modal.warning_no_offers.text',
                intlTranslationRichHelper
              )}
            </Typography>
          </Stack>
        </KvModal>
      )}
      <ModalByUrlProvider />
      <KvcMainMenu
        userName={userName}
        userInitials={userInitials}
        userRole={user.role}
        isOpen={menuIsOpen}
        onClose={() => {
          setMenuIsOpen(false);
        }}
      />
    </>
  );
}
