'use client';
import { AddCircle } from '@mui/icons-material';
import WindowIcon from '@mui/icons-material/Window';
import {
  Button,
  Stack,
  type SxProps,
  type Theme,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { type PropsWithChildren, type ReactElement, useState } from 'react';

import { KvModal } from '@/components/modal/modal';
import { StartWidget } from '@/components/widgets/startWidget';

type Props = PropsWithChildren<{
  showOverviewButton?: boolean | undefined;
  showNewCaseButton?: boolean | undefined;
  /** Content shown right below the navbar, for example progress bar. */
  contentBelow?: ReactElement;
  text?: string;
  textKey?: string;
  textSX?: SxProps<Theme>;
  elementBeforeText?: ReactElement;
}>;

export default function Navbar(props: Props): ReactElement {
  const router = useRouter();
  const t = useTranslations();
  const [showStartModal, setShowStartModal] = useState(false);
  return (
    <>
      <div
        style={{
          backgroundColor: 'var(--navbar-background)',
          width: '100%',
          minHeight: '66px',
          padding: '16px 40px',
        }}
      >
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
          flexWrap={'wrap'}
          justifyContent={'space-between'}
        >
          <Stack direction="row" spacing={1} alignItems="left">
            <Button
              startIcon={<WindowIcon />}
              variant="contained"
              sx={
                props.showOverviewButton
                  ? null
                  : {
                      opacity: 0,
                      maxHeight: 0,
                      pointerEvents: 'none',
                    }
              }
              onClick={() => {
                router.push('/cases');
              }}
            >
              Übersicht
            </Button>
          </Stack>

          <Stack direction="row">
            {typeof props.elementBeforeText !== 'undefined' &&
              props.elementBeforeText}

            {typeof props.textKey === 'string' && (
              <Typography sx={props.textSX ? props.textSX : null}>
                {t(props.textKey)}
              </Typography>
            )}

            {props.children && (
              <Stack
                direction="row"
                spacing={1}
                alignItems="center"
                sx={{ marginTop: '-1px' }}
              >
                {props.children}
              </Stack>
            )}
          </Stack>

          <Stack direction="row" spacing={1} alignItems="right">
            {/* {props.showNewCaseButton && ( */}
            <Button
              sx={
                props.showNewCaseButton
                  ? null
                  : {
                      opacity: 0,
                      maxHeight: 0,
                      pointerEvents: 'none',
                    }
              }
              startIcon={<AddCircle />}
              variant="contained"
              onClick={() => {
                // router.push('/start');
                setShowStartModal(true);
              }}
            >
              Neuer Vorgang
            </Button>
            {/* )} */}
          </Stack>
        </Stack>

        {showStartModal && (
          <KvModal
            onClose={() => {
              setShowStartModal(false);
            }}
            sx={{
              width: '1080px',
            }}
          >
            <StartWidget
              reduced={true}
              schnellCheckDisabled={true}
              antragDisabled={true}
              closeModal={() => {
                setShowStartModal(false);
              }}
            />
          </KvModal>
        )}
      </div>
      {props.contentBelow && <div>{props.contentBelow}</div>}
    </>
  );
}
