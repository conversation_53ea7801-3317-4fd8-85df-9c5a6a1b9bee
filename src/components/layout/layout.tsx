import { Stack } from '@mui/material';

import { HydrateClient } from '@/trpc/hydrate-client';
import { prefetch, trpc } from '@/trpc/server';

import Footer from './footer';
import Header from './header';
import { UserProfileNavigation } from './user-profile-navigation';

export interface AppLayoutProps {
  children: React.ReactNode;
}
export default async function AppLayout({ children }: AppLayoutProps) {
  prefetch(trpc.user.getCurrentUser.queryOptions());
  return (
    <div
      style={{
        display: 'flex',
        width: '100%',
        flexDirection: 'column',
        minHeight: '100vh',
      }}
    >
      <Header>
        <HydrateClient>
          <UserProfileNavigation />
        </HydrateClient>
      </Header>
      <Stack
        sx={{
          display: 'flex',
          flexGrow: 1,
        }}
      >
        {children}
      </Stack>
      <Footer />
    </div>
  );
}
