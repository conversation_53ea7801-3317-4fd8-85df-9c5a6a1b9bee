import React, { ReactElement, ReactNode } from "react";
import Head from "next/head";
import Header from "./header";
import Footer from "./footer";
import { Box } from "@mui/material";

interface LayoutProps {
  htmlTitle: string;
  metaDescription?: string;
  children: ReactNode;
  showMenu?: boolean;
}

export default function Layout({
  htmlTitle,
  metaDescription,
  children,
  showMenu = false,
}: LayoutProps): ReactElement {
  return (
    <>
      <Head>
        <title>{htmlTitle}</title>
        <meta name="language" content="de" />
        <meta name="description" content={metaDescription} />
        <meta property="og:title" content={htmlTitle} />
        <meta property="og:description" content={metaDescription} />
      </Head>
      <Box
        sx={{
          bgcolor: "background.default",
          minHeight: "100vh",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Header showMenu={showMenu} />

        <Box
          component="main"
          sx={{
            flex: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            pt: "5rem",
            pb: "1rem",
          }}
        >
          {children}
        </Box>

        <Footer />
      </Box>
    </>
  );
}
