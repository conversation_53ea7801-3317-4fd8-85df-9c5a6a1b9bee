// src/components/ContractList.tsx
import React, { useEffect, useCallback, useState } from 'react';
import { useRouter } from 'next/router';
import { formatLabel, formatField } from '../utils/keyFormatter';
import {
    CircularProgress,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    SelectChangeEvent,
    Tooltip,
    IconButton,
    Typography,
    Chip,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Box
} from '@mui/material';
import GppMaybeIcon from '@mui/icons-material/GppMaybe';
import DownloadIcon from '@mui/icons-material/Download';

interface Contract {
    contractId: number;
    contract_type: string;
    insurance_start_date: string;
    id: number;
    is_offer: boolean;
    customer_number: string;
    contract_number: string;
    agency_number: string;
}

interface PaginationMeta {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

const predefinedFilterValues: Record<string, string[]> = {
    contract_type: ['hausrat', 'wohngebaeude', 'tierhalterhaftpflicht', 'privathaftpflicht', 'haus_und_grundbesitzerhaftpflicht', 'bauleistung', 'bauherrenhaftpflicht', 'geschaeftsversicherung', 'gebaeudeversicherung', 'betriebshaftpflicht', 'unfallversicherung'],
    show_original_offer: ['true', 'false'],
    active_status: ['ACTIVE', 'INACTIVE', 'CANCELED', 'DELETED']
};

export default function ContractList() {
    const [contracts, setContracts] = useState<Contract[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(10);
    const [meta, setMeta] = useState<PaginationMeta | null>(null);
    const [filters, setFilters] = useState<Record<string, string>>({ active_status: 'ACTIVE' });
    const [currentField, setCurrentField] = useState<string>('contract_number');
    const [currentValue, setCurrentValue] = useState<string>('');
    const [sortField, setSortField] = useState<string>('insurance_start_date');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
    const [initialized, setInitialized] = useState(false);
    const [isExporting, setIsExporting] = useState(false);

    const router = useRouter();

    useEffect(() => {
        const { query } = router;

        const initialPage = query.page ? parseInt(query.page as string, 10) : 1;
        const initialLimit = query.limit ? parseInt(query.limit as string, 10) : 10;

        const initialFilters = query.filters
            ? JSON.parse(query.filters as string)
            : { active_status: 'ACTIVE' };

        const initialSortField = query.sortField as string || 'insurance_start_date';
        const initialSortDirection = (query.sortDirection as 'asc' | 'desc') || 'desc';

        setPage(initialPage);
        setLimit(initialLimit);
        setFilters(initialFilters);
        setSortField(initialSortField);
        setSortDirection(initialSortDirection);

        setInitialized(true);
    }, []);

    useEffect(() => {
        if (!initialized) return;

        const cleanedFilters: Record<string, string> = {};
        for (const [key, value] of Object.entries(filters)) {
            if (value !== "") {
                cleanedFilters[key] = value;
            }
        }

        const query: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
            sortField,
            sortDirection,
        };

        if (Object.keys(cleanedFilters).length > 0) {
            query.filters = JSON.stringify(cleanedFilters);
        }

        router.replace(
            {
                pathname: router.pathname,
                query,
            },
            undefined,
            { shallow: true }
        );
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const fetchContracts = async () => {
        setLoading(true);
        try {
            const offset = (page - 1) * limit;
            const response = await fetch(
                `/api/contracts?limit=${limit}&offset=${offset}&filters=${JSON.stringify(filters)}&sortField=${sortField}&sortDirection=${sortDirection}`,
                {
                    method: 'GET',
                    headers: { Authorization: `Bearer ${localStorage.getItem('jwt') || ''}` },
                }
            );

            if (!response.ok) {
                throw new Error('Failed to fetch contracts');
            }

            const data = await response.json();
            setContracts(data.contracts);
            setMeta(data.meta);
        } catch (error) {
            console.error('Error fetching contracts:', error);
            setError('Fehler beim Laden der Daten');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!initialized) return;
        fetchContracts();
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const handleAddFilter = () => {
        if (currentField && currentValue) {
            setFilters((prev) => ({ ...prev, [currentField]: currentValue }));
            setCurrentValue('');
        }
    };

    const handleRemoveFilter = (field: string) => {
        setFilters((prev) => {
            const updated = { ...prev };
            delete updated[field];
            return updated;
        });
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
        setPage(value);
    };

    const handleLimitChange = (event: SelectChangeEvent<number>) => {
        const newLimit = Number(event.target.value);
        setLimit(newLimit);
        setPage(1);
    };

    const handleDetailsClick = (contract: Contract, event: React.MouseEvent) => {
        const url = `/customer/${contract.customer_number}/${contract.is_offer ? 'offer' : 'contract'}/${contract.contract_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleReportClick = (contract: Contract, event: React.MouseEvent) => {
        const baseUrl = '/report/create/new';

        const queryParams = new URLSearchParams({
            contract_number: contract.contract_number.toString(),
            customer_number: contract.customer_number.toString(),
            agency_number: contract.agency_number.toString(),
        }).toString();

        const fullUrl = `${baseUrl}?${queryParams}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(fullUrl, '_blank');
        } else {
            router.push(fullUrl);
        }
    };

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const handleExportCsv = useCallback(async () => {
        try {
            setIsExporting(true);
            const token = localStorage.getItem('jwt');
            if (!token) {
                alert("Kein Token vorhanden");
                return;
            }

            const query = new URLSearchParams({
                sortField,
                sortDirection,
                filters: JSON.stringify(filters),
            });

            const response = await fetch(`/api/contracts/export-csv?${query.toString()}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                throw new Error("CSV Export fehlgeschlagen");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'vertragsliste.csv';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            console.error('CSV Download Error:', err);
            alert("Export fehlgeschlagen");
        } finally {
            setIsExporting(false);
        }
    }, [filters, sortField, sortDirection]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (error) {
        return (
            <Typography variant="h6" color="error" align="center">
                Fehler: {error}
            </Typography>
        );
    }

    return (
        <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
            <Typography variant="h4" align="center" gutterBottom color="primary">
                Vertragsliste
            </Typography>

            <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                    <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                        <InputLabel>Filterkriterium</InputLabel>
                        <Select
                            value={currentField}
                            onChange={(e) => setCurrentField(e.target.value)}
                            label="Filterkriterium"
                        >
                            <MenuItem value="contract_number">Vertragsnummer</MenuItem>
                            <MenuItem value="customer_number">Kundennummer</MenuItem>
                            {localStorage.getItem("is_admin") == "true" && <MenuItem value="agency_number">Agenturnummer</MenuItem>}
                            <MenuItem value="contract_type">Vertragsart</MenuItem>
                            <MenuItem value="show_original_offer">Zeige verwendete Angebote</MenuItem>
                            <MenuItem value="active_status">Status</MenuItem>
                        </Select>
                    </FormControl>

                    {predefinedFilterValues[currentField] ? (
                        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                            <InputLabel>Filterwert</InputLabel>
                            <Select
                                value={currentValue}
                                onChange={(e) => setCurrentValue(e.target.value as string)}
                                label="Filterwert"
                            >
                                {predefinedFilterValues[currentField].map((value) => (
                                    <MenuItem key={value} value={value}>
                                        {formatLabel(value)}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    ) : (
                        <TextField
                            label="Filterwert"
                            variant="outlined"
                            size="small"
                            value={currentValue}
                            onChange={(e) => setCurrentValue(e.target.value)}
                            sx={{ width: 200 }}
                        />
                    )}

                    <Button variant="contained" onClick={handleAddFilter}>
                        Filtern
                    </Button>
                    <div style={{ flexGrow: 1 }} />
                    <Tooltip title="CSV exportieren">
                        <IconButton
                            onClick={handleExportCsv}
                            color="primary"
                            style={{ marginRight: 15 }}
                            disabled={isExporting}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip>
                </div>

                <div>
                    {Object.entries(filters).map(([field, value]) => (
                        <Chip
                            key={field}
                            label={`${formatField(field)}: ${formatLabel(value)}`}
                            onDelete={() => handleRemoveFilter(field)}
                            color="primary"
                            sx={{ marginRight: '0.5rem', marginBottom: '0.5rem', borderRadius: 0 }}
                        />
                    ))}
                </div>
            </div>

            {loading ? (
                <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                    <CircularProgress />
                </div>
            ) : (

                <TableContainer component={Paper} sx={{ marginTop: 4 }}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('is_offer')}
                                >
                                    Typ {sortField === 'is_offer' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('contract_type')}
                                >
                                    Vertragsart {sortField === 'contract_type' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('contract_number')}
                                >
                                    Vertragsnummer {sortField === 'contract_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('customer_number')}
                                >
                                    Kundennummer {sortField === 'customer_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                {localStorage.getItem("is_admin") == "true" && <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('agency_number')}
                                >
                                    Agenturnummer {sortField === 'agency_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>}
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('insurance_start_date')}
                                >
                                    Versicherungsbeginn {sortField === 'insurance_start_date' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell align="right" sx={{ fontWeight: 'bold' }}>Aktionen</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {contracts.map((contract, index) => (
                                <TableRow
                                    key={index}
                                    hover
                                    sx={{ cursor: 'pointer' }}
                                    onClick={(event) => handleDetailsClick(contract, event)}
                                >
                                    <TableCell>{contract.is_offer ? 'Angebot' : 'Vertrag'}</TableCell>
                                    <TableCell>{formatLabel(contract.contract_type)}</TableCell>
                                    <TableCell>{contract.contract_number}</TableCell>
                                    <TableCell>{contract.customer_number}</TableCell>
                                    {localStorage.getItem("is_admin") == "true" && (
                                        <TableCell>{contract.agency_number}</TableCell>
                                    )}
                                    <TableCell>{contract.insurance_start_date}</TableCell>
                                    <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                                        {!contract.is_offer && (
                                            <Tooltip title='Schaden melden'>
                                                <IconButton
                                                    onClick={(event) => {
                                                        event.stopPropagation();
                                                        handleReportClick(contract, event);
                                                    }}
                                                    color="primary"
                                                >
                                                    <GppMaybeIcon />
                                                </IconButton>
                                            </Tooltip>
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>

                    </Table>
                </TableContainer>
            )}

            {meta && (
                <Pagination
                    count={meta.pageCount}
                    page={page}
                    onChange={handlePageChange}
                    sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
                />
            )}
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                    <InputLabel id="results-per-page-label">Ergebnisse pro Seite</InputLabel>
                    <Select<number>
                        labelId="results-per-page-label"
                        value={limit}
                        onChange={handleLimitChange}
                        label="Ergebnisse pro Seite"
                    >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                    </Select>
                </FormControl>
            </div>
        </Box>
    );
}
