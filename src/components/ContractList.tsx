// src/components/ContractList.tsx
import DownloadIcon from '@mui/icons-material/Download';
import GppMaybeIcon from '@mui/icons-material/GppMaybe';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useIsAdmin } from '@/utils/authUtils';
import { formatLabel } from '@/utils/keyFormatter';
import { useTableQuery } from '@/utils/useTableQuery';

import { contractFieldConfig } from './filters/contractFieldConfig';
import { FilterBuilder } from './filters/FilterBuilder';
import { serializeFilters } from './filters/url';

export default function ContractList() {
  const router = useRouter();

  const [isExporting, setIsExporting] = useState<boolean>(false);

  const session = useSession();
  const isAdmin = useIsAdmin();

  const table = useTableQuery<any>({
    endpoint: '/api/contracts',
    defaultSortField: 'insuranceStartDate',
    accessToken: session.data?.accessToken,
    enabled: session.status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.items ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: contracts,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  // handlers
  const handlePageChange = (_: React.ChangeEvent<unknown>, value: number) =>
    setPage(value);

  const handleShowDetails = (contract: Contract, event: React.MouseEvent) => {
    const url = `/contract/${contract.contractNumber}`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleReportClick = (contract: Contract, event: React.MouseEvent) => {
    event.stopPropagation();
    const baseUrl = '/report/create';
    const queryParams = new URLSearchParams({
      contractNumber: String(contract.contractNumber),
      customerNumber: String(contract.customerNumber),
      agencyNumber: String(contract.agencyNumber ?? ''),
    }).toString();
    const fullUrl = `${baseUrl}?${queryParams}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(fullUrl, '_blank');
    } else {
      router.push(fullUrl);
    }
  };

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);

      const query = new URLSearchParams({
        sortField,
        sortDirection,
        ...serializeFilters(filters),
      });

      const response = await apiFetch(
        `/api/contracts/export-csv?${query.toString()}`,
        {
          raw: true,
        }
      );

      if (!response.ok) {
        throw new Error('CSV Export fehlgeschlagen');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'vertragsliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Vertragsliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        {/* Unified filter builder */}
        <FilterBuilder
          fields={contractFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="contractNumber"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      <TableContainer component={Paper} sx={{ marginTop: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('isOffer')}
                title="Typ (Angebot/Vertrag)"
              >
                Typ{' '}
                {sortField === 'isOffer' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('contractType')}
              >
                Vertragsart{' '}
                {sortField === 'contractType' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('contractNumber')}
              >
                Vertragsnummer{' '}
                {sortField === 'contractNumber' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('customerNumber')}
              >
                Kundennummer{' '}
                {sortField === 'customerNumber' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              {isAdmin && (
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('agencyNumber')}
                >
                  Agenturnummer{' '}
                  {sortField === 'agencyNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
              )}
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('insuranceStartDate')}
              >
                Versicherungsbeginn{' '}
                {sortField === 'insuranceStartDate' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                Aktionen
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {contracts.map((c) => (
              <TableRow
                key={c.id}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={(event) => handleShowDetails(c, event)}
              >
                <TableCell>{c.isOffer ? 'Angebot' : 'Vertrag'}</TableCell>
                <TableCell>{formatLabel(c.contractType || '')}</TableCell>
                <TableCell>{c.contractNumber}</TableCell>
                <TableCell>{c.customerNumber}</TableCell>
                {isAdmin && <TableCell>{c.agencyNumber ?? ''}</TableCell>}
                <TableCell>
                  {/* assumes ISO date string in c.insuranceStartDate */}
                  {c.insuranceStartDate
                    ? new Date(c.insuranceStartDate).toLocaleDateString()
                    : ''}
                </TableCell>
                <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                  {!c.isOffer && (
                    <Tooltip title="Schaden melden">
                      <IconButton
                        color="primary"
                        onClick={(e) => handleReportClick(c, e)}
                      >
                        <GppMaybeIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={handlePageChange}
          sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </Box>
    </Box>
  );
}
