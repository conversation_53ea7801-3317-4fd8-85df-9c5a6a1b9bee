import * as React from "react";
import {Typography, Stack, IconButton} from "@mui/material";
import {FC} from "react";
import ReplayIcon from "@mui/icons-material/Replay";

type Props = {
    text: string
    onRetryClick: () => void
}

export const ErrorMessage: FC<Props> = ({ text, onRetryClick }) => {
    return (
        <Stack sx={{ justifyContent: 'center', gap: '5px', alignItems: 'center' }}>
            <Typography sx={theme => ({ fontSize: '15px', color: theme.palette.error.main })}>
                {text}
            </Typography>
            <IconButton onClick={onRetryClick}>
                <ReplayIcon color='error' />
            </IconButton>
        </Stack>
    );
}
