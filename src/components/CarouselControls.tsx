import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import TodayIcon from '@mui/icons-material/Today';
import { IconButton, Stack, type SxProps } from '@mui/material';
import React, { type FC } from 'react';

type CarouselControlsProps = {
  sx?: SxProps;
  disablePrevious?: boolean;
  disableNext?: boolean;
  goPrevious: () => void;
  goNext: () => void;
  goMostRecent: () => void;
};
const CarouselControls: FC<CarouselControlsProps> = ({
  disablePrevious,
  disableNext,
  goPrevious,
  goMostRecent,
  goNext,
  sx,
}) => {
  return (
    <Stack direction="row" sx={{ ...sx, justifyContent: 'space-between' }}>
      <IconButton disabled={disablePrevious} onClick={goPrevious}>
        <ArrowBackIosNewIcon sx={{ position: 'relative', left: '-1px' }} />
      </IconButton>
      <IconButton onClick={goMostRecent}>
        <TodayIcon sx={{ position: 'relative', bottom: '-1px' }} />
      </IconButton>
      <IconButton disabled={disableNext} onClick={goNext}>
        <ArrowForwardIosIcon sx={{ position: 'relative', right: '-1px' }} />
      </IconButton>
    </Stack>
  );
};

export default CarouselControls;
