import React, {<PERSON>} from "react";
import {Icon<PERSON>utton, Stack, SxProps} from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import TodayIcon from "@mui/icons-material/Today";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";

type CarouselControlsProps = {
    sx?: SxProps
    disablePrevious?: boolean
    disableNext?: boolean
    goPrevious: () => void
    goNext: () => void
    goMostRecent: () => void
}
const CarouselControls: FC<CarouselControlsProps> = ({ disablePrevious, disableNext, goPrevious, goMostRecent, goNext, sx }) => {
    return (
        <Stack direction='row' sx={{ ...sx, justifyContent: 'space-between' }}>
            <IconButton disabled={disablePrevious} onClick={goPrevious}>
                <ArrowBackIosNewIcon sx={{ position: 'relative', left: '-1px' }} />
            </IconButton>
            <IconButton onClick={goMostRecent}>
                <TodayIcon sx={{ position: 'relative', bottom: '-1px' }} />
            </IconButton>
            <IconButton disabled={disableNext} onClick={goNext}>
                <ArrowForwardIosIcon sx={{ position: 'relative', right: '-1px' }}  />
            </IconButton>
        </Stack>
    )
}

export default CarouselControls;