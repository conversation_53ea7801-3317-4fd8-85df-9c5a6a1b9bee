import {
  AssignmentInd,
  Description,
  GppMaybe,
  Group,
  GroupWork,
  Receipt,
} from '@mui/icons-material';
import { Box, Card, CardActionArea, Typography } from '@mui/material';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function DashboardContent() {
  const { data: session } = useSession();

  const tiles = [
    {
      label: 'KUNDEN',
      href: '/customers',
      icon: <Group sx={{ fontSize: 100 }} />,
      admin: false,
    },
    {
      label: 'VERTRÄGE',
      href: '/contracts',
      icon: <Description sx={{ fontSize: 100 }} />,
      admin: false,
    },
    {
      label: 'SCHÄDEN',
      href: '/reports',
      icon: <GppMaybe sx={{ fontSize: 100 }} />,
      admin: false,
    },
    {
      label: 'RECHNUNGEN',
      href: '/invoices',
      icon: <Receipt sx={{ fontSize: 100 }} />,
      admin: false,
    },
    {
      label: 'Makler',
      href: '/agents',
      icon: <AssignmentInd sx={{ fontSize: 100 }} />,
      admin: true,
    },
    {
      label: 'AGENTUREN',
      href: '/agencies',
      icon: <GroupWork sx={{ fontSize: 100 }} />,
      admin: true,
    },
  ];

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      flexWrap="wrap"
      gap={4}
      minHeight="24rem"
      p={2}
    >
      {tiles.map(
        ({ label, href, icon, admin }) =>
          ((admin == true && session?.roles?.includes('asevo-admin')) ||
            admin == false) && (
            <Link key={href} href={href} passHref legacyBehavior>
              <Card
                sx={{
                  width: 180,
                  height: 170,
                  borderRadius: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  backgroundColor: 'secondary.main',
                  transition: 'transform 0.3s, background-color 0.3s',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    backgroundColor: 'primary.dark',
                  },
                  cursor: 'pointer',
                  textDecoration: 'none',
                }}
              >
                <CardActionArea sx={{ p: 2 }}>
                  <Box
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                  >
                    {icon}
                    <Typography variant="h6" textAlign="center" mt={1}>
                      {label}
                    </Typography>
                  </Box>
                </CardActionArea>
              </Card>
            </Link>
          )
      )}
    </Box>
  );
}
