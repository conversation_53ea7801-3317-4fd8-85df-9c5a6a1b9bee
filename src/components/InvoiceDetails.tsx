// src/components/InvoiceDetails.tsx
import React from 'react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { Tooltip, Button, CircularProgress, Typography, Box, Grid2 as Grid } from '@mui/material';
import { Autorenew, Download, Send } from '@mui/icons-material';

import InvoiceInformationBox from './box/InvoiceInformationBox';
import InvoiceStatusBox from './box/InvoiceStatusBox';
import RevisionsModal from './modal/RevisionsModal';
import StaticBox from './box/StaticBox';

import { InvoiceData, InvoiceType } from "@/utils/invoice/types";

const InvoiceDetails = () => {
    const router = useRouter();
    const { invoice_number } = router.query;
    const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || ""
        if (invoice_number) {
            fetch(`/api/invoice/get/${invoice_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })
                .then((response) => {
                    if (!response.ok) throw new Error('Failed to fetch');
                    return response.json();
                })
                .then((data: InvoiceData) => setInvoiceData(data))
                .catch((error) => console.error('Error fetching customer:', error));
        }
    }, [invoice_number]);


    const handleDownloadInvoice = async (invoice_number: string, documentId: string) => {
        const response = await fetch(`/api/invoice/download/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`,
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = invoice_number;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download attachment');
        }
    };

    const handleSendMail = async (type: string, invoice_number: string) => {
        alert('Comming soon')
    };


    if (!invoiceData) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    async function handleRegeneratePDF(invoice_number: string) {
        const response = await fetch(`/api/invoice/regenerate/${invoice_number}`, {
            method: 'GET',
            headers: {
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        });
        if (response.ok) {
            alert('Die Rechnung wurde erfolgreich neugeneriert.');
        } else {
            console.error('Failed to create PDF');
        }
        location.reload();
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Rechnungsdetails
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary" >
                {invoiceData.invoice_number}
            </Typography>

            {/* invoice informations */}
            <InvoiceInformationBox
                invoiceData={invoiceData}
                visible
            />

            {/* actions */}
            <StaticBox title="Aktionen">
                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Mit dieser Aktion wird die Rechnung an den Makler gesendet.`}>
                            <Button
                                onClick={() => handleSendMail('invoice', invoiceData.invoice_number)}
                                fullWidth
                                variant="contained"
                            >
                                <Send sx={{ mr: 1 }} />   Rechnung senden
                            </Button>
                        </ Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier können Sie die Rechnung herunterladen.`}>
                            <Button
                                onClick={() => handleDownloadInvoice(invoiceData.invoice_number, invoiceData.documentId)}
                                fullWidth
                                variant="contained"
                            >
                                <Download sx={{ mr: 1 }} />   Rechnung herunterladen
                            </Button>
                        </ Tooltip>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <RevisionsModal type={'invoice'} number={invoice_number as string} />
                    </Grid>

                    {localStorage.getItem("is_admin") == "true" && invoiceData.automatically_generated &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie die Rechnung generieren.`}>
                                <Button
                                    variant="contained" color="error"
                                    onClick={() => handleRegeneratePDF(invoice_number as string)}
                                    fullWidth
                                >
                                    <Autorenew sx={{ mr: 1 }} /> Rechnung regenerieren
                                </Button>
                            </ Tooltip>
                        </Grid>}

                </Grid>
            </StaticBox>

            {/* invoice status */}
            <InvoiceStatusBox
                invoiceData={invoiceData}
                visible
            />

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>

                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>

        </Box>
    );
};

export default InvoiceDetails;
