// src/components/InvoiceDetails.tsx
import { Autorenew, Download, Send } from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import React from 'react';
import { useEffect, useState } from 'react';

import { type Invoice } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useIsAdmin } from '@/utils/authUtils';

import InvoiceInformationBox from './box/InvoiceInformationBox';
import InvoiceStatusBox from './box/InvoiceStatusBox';
import StaticBox from './box/StaticBox';
import RevisionsModal from './modal/RevisionsModal';

const InvoiceDetails = () => {
  const router = useRouter();
  const { invoiceNumber } = router.query;
  const [invoiceData, setInvoiceData] = useState<Invoice | null>(null);
  const isAdmin = useIsAdmin();

  useEffect(() => {
    if (invoiceNumber) {
      apiFetch<Invoice>(`/api/invoices/${invoiceNumber}`, {
        method: 'GET',
      })
        .then((data: Invoice) => setInvoiceData(data))
        .catch((error) => console.error('Error fetching customer:', error));
    }
  }, [invoiceNumber]);

  const handleDownloadInvoice = async (invoiceNumber: string) => {
    const response = await apiFetch(`/api/invoices/${invoiceNumber}/file`, {
      method: 'GET',
      raw: true,
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = invoiceNumber;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download attachment');
    }
  };

  const handleSendMail = async () => {
    alert('Comming soon');
  };

  if (!invoiceData) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  async function handleRegeneratePDF(invoiceNumber: string) {
    const response = await apiFetch(
      `/api/invoices/${invoiceNumber}/regenerate`,
      {
        method: 'GET',
        raw: true,
      }
    );
    if (response.ok) {
      alert('Die Rechnung wurde erfolgreich neugeneriert.');
    } else {
      console.error('Failed to create PDF');
    }
    location.reload();
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Rechnungsdetails
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {invoiceData.invoiceNumber}
      </Typography>

      {/* invoice informations */}
      <InvoiceInformationBox invoiceData={invoiceData} visible />

      {/* actions */}
      <StaticBox title="Aktionen">
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip
              title={`Mit dieser Aktion wird die Rechnung an den Makler gesendet.`}
            >
              <Button
                onClick={() => handleSendMail()}
                fullWidth
                variant="contained"
              >
                <Send sx={{ mr: 1 }} /> Rechnung senden
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title={`Hier können Sie die Rechnung herunterladen.`}>
              <Button
                onClick={() => handleDownloadInvoice(invoiceData.invoiceNumber)}
                fullWidth
                variant="contained"
              >
                <Download sx={{ mr: 1 }} /> Rechnung herunterladen
              </Button>
            </Tooltip>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <RevisionsModal
              type={'invoices'}
              number={invoiceNumber as string}
            />
          </Grid>

          {isAdmin && invoiceData.automaticallyGenerated && (
            <Grid size={{ xs: 12, sm: 6, md: 6 }}>
              <Tooltip title={`Hier können Sie die Rechnung generieren.`}>
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => handleRegeneratePDF(invoiceNumber as string)}
                  fullWidth
                >
                  <Autorenew sx={{ mr: 1 }} /> Rechnung regenerieren
                </Button>
              </Tooltip>
            </Grid>
          )}
        </Grid>
      </StaticBox>

      {/* invoice status */}
      <InvoiceStatusBox invoiceData={invoiceData} visible />

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>
    </Box>
  );
};

export default InvoiceDetails;
