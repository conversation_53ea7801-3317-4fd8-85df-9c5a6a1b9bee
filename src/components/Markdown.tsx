import { Box, type SxProps } from '@mui/material';
import { type FC } from 'react';
import ReactMarkdown, {
  type Options as ReactMarkdownProps,
} from 'react-markdown';

const generatedHtmlStyles: SxProps = {
  lineHeight: 1.3,
  ['& ul']: {
    margin: '5px 0 10px 15px',
  },
  ['& li::marker']: {
    content: '"- "',
  },
  ['& h1']: {
    fontSize: '19px',
    fontWeight: '700',
    margin: '10px 0',
  },
  ['& h2']: {
    margin: '10px 0',
    fontSize: '18px',
  },
};

const Markdown: FC<ReactMarkdownProps> = (props) => {
  return (
    <Box sx={generatedHtmlStyles}>
      <ReactMarkdown {...props} />
    </Box>
  );
};

export default Markdown;
