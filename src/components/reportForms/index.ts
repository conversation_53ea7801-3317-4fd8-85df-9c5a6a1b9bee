export { FormItemsList } from './FormItemsList'
export type { FormConfig, FormState } from './domain'
export { determineShownFields } from './determineShownFields'

import {liabilityInsuranceForms} from "@/components/reportForms/configs/liability-insurances";
import {FormConfig} from "@/components/reportForms/domain";
import {accidentInsuranceForms} from "@/components/reportForms/configs/accident-insurance";
import {propertyInsurancesForms} from "@/components/reportForms/configs/property-insurances";

export const reportFormConfigs: Record<string, FormConfig[]> = {
    hausrat: propertyInsurancesForms,
    wohngebaeude: propertyInsurancesForms,
    tierhalterhaftpflicht: liabilityInsuranceForms,
    privathaftpflicht: liabilityInsuranceForms,
    haus_und_grundbesitzerhaftpflicht: liabilityInsuranceForms,
    bauleistung: propertyInsurancesForms,
    bauherrenhaftpflicht: liabilityInsuranceForms,
    geschaeftsversicherung: propertyInsurancesForms,
    gebaeudeversicherung: propertyInsurancesForms,
    betriebshaftpflicht: liabilityInsuranceForms,
    unfallversicherung: accidentInsuranceForms
}
