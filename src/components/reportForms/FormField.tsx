// src/components/forms/Input.tsx
import {
    Checkbox,
    FormControl,
    FormControlLabel, InputAdornment,
    InputLabel,
    MenuItem,
    Select,
    TextField,
    TextFieldProps, Tooltip
} from "@mui/material";
import {DateTimePicker} from "@mui/x-date-pickers/DateTimePicker";
import dayjs, {Dayjs} from "dayjs";
import {FormState, FormFieldConfig as T<PERSON><PERSON><PERSON><PERSON> } from "@/components/reportForms/domain";
import {FC} from "react";

type Props = TFormField & {
    state: FormState
    onChange: (newValue: any) => void;
}

const Content: FC<Props> = ({ type, label, id, state, onChange, options }) => {
    if (type === 'checkbox') {
        return (
            <FormControlLabel
                label={label}
                control={
                    <Checkbox
                        name={id}
                        checked={state[id] ?? false}
                        onChange={(e) => onChange(e.target.checked)}
                    />
                }
            />
        )
    }
    if (type === 'boolean-dropdown') {
        const value = id in state ? (state[id] ? 1 : 0) : ''

        return (
            <FormControl variant="outlined">
                <InputLabel>{label}</InputLabel>
                <Select
                    label={label}
                    name={id}
                    value={value}
                    onChange={(e) => onChange(Boolean(e.target.value))}
                >
                    <MenuItem value={1}>Ja</MenuItem>
                    <MenuItem value={0}>Nein</MenuItem>
                </Select>
            </FormControl>
        )
    }
    if (type === 'dropdown') {
        return (
            <FormControl variant="outlined">
                <InputLabel>{label}</InputLabel>
                <Select
                    label={label}
                    name={id}
                    value={state[id] ?? ''}
                    onChange={(e) => onChange(e.target.value)}
                >
                    {options!.map((option) => (
                        <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
                    ))}
                </Select>
            </FormControl>
        )
    }
    if (type === 'datetime') {
        return (
            <DateTimePicker
                label={label}
                disableFuture
                value={state[id] ? dayjs(state[id]) : null}
                onChange={(newValue: Dayjs | null) => onChange(newValue?.toISOString())}
            />
        )
    }

    let additionalProps: Partial<TextFieldProps> = {}
    if (type === 'textarea') {
        additionalProps = {
            multiline: true,
            minRows: 3,
        }
    }
    if (type === 'date') {
        additionalProps = {
            type: "date",
            slotProps: {
                inputLabel: {
                    shrink: true
                }
            }
        }
    }
    if (type === 'number') {
        additionalProps = {
            type: "number",
        }
    }
    if (type === 'price') {
        additionalProps = {
            type: "number",
            slotProps: {
                input: {
                    endAdornment: <InputAdornment position="end">€</InputAdornment>,
                },
            }
        }
    }

    return (
        <TextField
            label={label}
            name={id}
            value={state[id] ?? ''}
            onChange={(e) => onChange(e.target.value)}
            fullWidth
            {...additionalProps}
        />
    );
}

export const FormField: FC<Props> = (props) => {
    if (props.tooltip) {
        return (
            <Tooltip title={props.tooltip} placement='top-start'>
                <Content {...props} />
            </Tooltip>
        )
    }

    return <Content {...props} />
};
