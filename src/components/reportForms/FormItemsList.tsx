import {FormState, FormItemConfig} from "@/components/reportForms/domain";
import {FormField} from "@/components/reportForms/FormField";
import {Divider} from "@mui/material";
import {determineShownFields} from "@/components/reportForms/determineShownFields";

interface Props {
    items: FormItemConfig[];
    state: FormState;
    onChange: (patch: Partial<FormState>, moveToMemory?: string[], takeFromMemory?: string[]) => void;
}

export const FormItemsList: React.FC<Props> = ({ items, state, onChange }) => {
    const shownFields = determineShownFields(items, state)

    return items.map((item, index) => {
        if (item.type === 'divider') {
            return <Divider key={`divider${index}`} />
        }

        if (!shownFields.has(item.id)) {
            return null
        }
        return (
            <FormField key={item.id} {...item} state={state} onChange={(newValue) => onChange({
                [item.id]: newValue
            })} />
        )
    })
};