export const divider = {
    type: 'divider',
} as const

export type FormFieldConfig = {
    type: 'text' | 'textarea' | 'date' | 'datetime' | 'dropdown' | 'boolean-dropdown' | 'checkbox' | 'price' | 'number'
    label: string;
    id: string;
    tooltip?: string;
    options?: { value: string | number, label: string }[]; // only for 'dropdown'
    showIf?: string | Record<string, any>;
}

export type FormItemConfig = FormFieldConfig | typeof divider

export type FormConfig = {
    key: string;
    title: string;
    items: FormItemConfig[];
}

export type FormState = Record<string, any>;
