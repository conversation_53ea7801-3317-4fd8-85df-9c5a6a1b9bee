import {FormConfig} from "@/components/reportForms/domain";

const insuredPerson: FormConfig = {
    key: 'insured_person',
    title: 'Versicherte Person',
    items: [
        {
            type: 'text',
            label: 'Berufliche Tätigkeit der versicherten Person vor dem Unfall:',
            id: 'occupation',
        },
        {
            type: 'date',
            label: 'Seit:',
            id: 'occupied_since',
        },
        {
            type: 'text',
            label: 'Arbeitgeber (Name und Anschrift):',
            id: 'employer',
        },
    ]
}

const accidentCourse: FormConfig = {
    key: 'accident_course',
    title: 'Unfallhergang',
    items: [
        {
            type: 'boolean-dropdown',
            label: 'Ereignete sich der Unfall bei beruflicher Tätigkeit oder auf dem Weg zu oder von der Arbeitsstelle?',
            id: 'is_work_related',
        },
        {
            type: 'boolean-dropdown',
            label: 'Ist der Unfall einer Berufsgenossenschaft oder sonstigen gesetzlichen Unfallversicherung gemeldet worden?',
            id: 'was_reported_to_professional_association_or_other_insurance',
        },
        {
            showIf: 'was_reported_to_professional_association_or_other_insurance',
            type: 'text',
            id: 'professional_association_or_other_insurance_name',
            label: 'Berufsgenossenschaft oder sonstige gesetzliche Unfallversicherung:',
            tooltip: 'ggf. bitte vom Arbeitgeber erfragen',
        },
        {
            type: 'datetime',
            label: 'Wann ereignete sich der Unfall?',
            id: 'datetime',
        },
        {
            type: 'textarea',
            id: 'place',
            label: 'Wo ereignete sich der Unfall?',
            tooltip: 'Bitte genaue Bezeichnung des Landes, des Ortes, der Straße, Haus-Nr., des Lokales, Betriebes usw.'
        },
        {
            type: 'boolean-dropdown',
            id: 'happened_at_secondary_residence',
            label: 'Besteht dort ein weiterer Wohnsitz?',
        },
        {
            type: 'textarea',
            id: 'course',
            label: 'Wie ereignete sich der Unfall?',
        },
        {
            type: 'text',
            id: 'witnesses',
            label: 'Namen und Anschriften der Augenzeugen des Unfalles?',
        },
        {
            type: 'text',
            id: 'cause',
            label: 'Was war die eigentliche Ursache des Unfalles?',
        },
    ]
}

const alcoholConsumption: FormConfig = {
    key: 'alcohol_consumption',
    title: 'Alkoholkonsum',
    items: [
        {
            type: 'boolean-dropdown',
            id: 'had_alcohol',
            label: 'Hatte der Verletzte in den letzten 12 Stunden vor dem Unfall alkoholische Getränke zu sich genommen?',
        },
        {
            showIf: 'had_alcohol',
            type: 'boolean-dropdown',
            id: 'had_blood_test',
            label: 'Ist dem Verletzten zur Feststellung des Blutalkoholgehaltes eine Blutprobe entnommen worden?',
        },
        {
            showIf: 'had_blood_test',
            type: 'number',
            id: 'blood_alcohol_level',
            label: 'Promillesatz ‰:',
        },
        {
            showIf: 'had_alcohol',
            type: 'boolean-dropdown',
            id: 'was_driving',
            label: 'War der Verletzte Lenker eines Fahrzeuges?',
        },
        {
            showIf: 'was_driving',
            type: 'text',
            id: 'vehicle_type',
            label: 'Fahrzeugart:',
        },
        {
            showIf: 'was_driving',
            type: 'boolean-dropdown',
            id: 'had_license_at_the_time',
            label: 'Besaß er zur Zeit des Unfalles einen Führerschein?',
        },
        {
            showIf: 'had_license_at_the_time',
            type: 'text',
            id: 'license_type',
            label: 'Führerscheinklasse:',
        },
    ]
}

const policeReport: FormConfig = {
    key: 'police_report',
    title: 'Polizeiliche Aufnahme',
    items: [
        {
            type: 'boolean-dropdown',
            label: 'Ist der Unfall polizeilich aufgenommen worden?',
            id: 'was_reported_to_police',
        },
        {
            showIf: 'was_reported_to_police',
            type: 'text',
            label: 'Tagebuch-Nr.:',
            id: 'police_record_number',
        },
        {
            showIf: 'was_reported_to_police',
            type: 'text',
            id: 'police_address',
            label: 'Anschrift der Polizei:',
        },
        {
            showIf: 'was_reported_to_police',
            type: 'text',
            label: 'Zuständige Staatsanwaltschaft?',
            id: 'prosecutor',
        },
        {
            showIf: 'was_reported_to_police',
            type: 'text',
            id: 'prosecutor_reference',
            label: 'Aktenzeichen der Staatsanwaltschaft:',
        },
    ]
}

const injuries: FormConfig = {
    key: 'injuries',
    title: 'Verletzungen',
    items: [
        {
            type: 'text',
            label: 'Welche Körperteile wurden verletzt?',
            id: 'injured_parts',
            tooltip: 'z. B. Kopf, re. Daumen, li. Bein usw.'
        },
        {
            type: 'text',
            label: 'Welcher Art sind die Verletzungen?',
            id: 'injury_type',
            tooltip: 'z. B. Bruch, Verstauchung, Quetschung usw.'
        },
        {
            type: 'datetime',
            id: 'treatment_start_datetime',
            label: 'Wann genau wurde die ärztliche Behandlung aufgenommen?',
        },
        {
            type: 'text',
            label: 'Durch welchen Arzt? (Name und Anschrift)',
            id: 'initial_doctor',
        },
        {
            type: 'boolean-dropdown',
            id: 'is_treatment_completed',
            label: 'Ist die ärztliche Behandlung wegen des Unfalles beendet?',
        },
        {
            showIf: 'is_treatment_completed',
            type: 'date',
            id: 'treatment_end_date',
            label: 'Seit wann?',
        },
        {
            showIf: { is_treatment_completed: false },
            type: 'text',
            id: 'estimated_treatment_duration',
            label: 'Wie lange wird sie voraussichtlich noch dauern?',
        },
        {
            type: 'text',
            id: 'current_doctor',
            label: 'Welcher Arzt behandelte den Verletzten zuletzt bzw. behandelt ihn noch?',
            tooltip: 'Name und Anschrift'
        },
        {
            type: 'boolean-dropdown',
            id: 'was_hospitalized',
            label: 'War der Verletzte wegen des Unfalles in stationärer Krankenhausbehandlung?',
        },
        {
            showIf: 'was_hospitalized',
            type: 'date',
            id: 'hospital_stay_start',
            label: 'Vom',
        },
        {
            showIf: 'was_hospitalized',
            type: 'date',
            id: 'hospital_stay_end',
            label: 'Bis',
        },
        {
            showIf: 'was_hospitalized',
            type: 'text',
            id: 'hospital_name_address',
            label: 'Name und Anschrift des Krankenhauses:',
        },
        {
            showIf: 'was_hospitalized',
            type: 'boolean-dropdown',
            id: 'has_hospitalization_ended',
            label: 'Ist der Krankenhausaufenthalt wegen des Unfalles beendet?',
        },
        {
            showIf: 'has_hospitalization_ended',
            type: 'text',
            id: 'hospitalizazion_end_date',
            label: 'Seit wann?',
        },
        {
            showIf: { has_hospitalization_ended: false },
            type: 'text',
            id: 'estimated_hospital_stay',
            label: 'Wie lange wird er voraussichtlich noch dauern?',
        },
    ]
}

const healthState: FormConfig = {
    key: 'health_state',
    title: 'Gesundheitszustand',
    items: [
        {
            type: 'boolean-dropdown',
            label: 'War der Verletzte bis zum Unfall völlig gesund und vollständig erwerbs- und arbeitsfähig?',
            id: 'was_healthy_before',
        },
        {
            showIf: { was_healthy_before: false },
            type: 'textarea',
            label: 'Welche Erwerbsminderung lag genau vor und mit welchem Minderungsgrad in Prozent?',
            id: 'disability_details',
        },
    ]
}

const pension: FormConfig = {
    key: 'pension',
    title: 'Rente',
    items: [
        {
            type: 'boolean-dropdown',
            label: 'Bezog der Verletzte bereits eine Rente?',
            id: 'had_pension',
        },
        {
            showIf: 'had_pension',
            type: 'textarea',
            label: 'Seit wann und in welcher Höhe wurde diese von wem bezogen?',
            id: 'pension_details',
        },
    ]
}

const otherInsurances: FormConfig = {
    key: 'other_insurances',
    title: 'Andere Versicherungen',
    items: [
        {
            type: 'text',
            label: 'Bei welcher gesetzlichen Krankenkasse, Ersatz- oder Privatkasse ist der Verletzte krankenversichert?',
            tooltip: 'Bitte volle Anschrift angeben',
            id: 'health_insurance',
        },
        {
            type: 'text',
            label: 'Mitglieds-Nr. oder Name des versicherten Familienvorstandes?',
            id: 'insured_head_of_family',
        },
        {
            type: 'boolean-dropdown',
            label: 'Bestehen für den Verletzten noch bei anderen Gesellschaften Unfallversicherungen?',
            id: 'has_other_insurances'
        },
        {
            showIf: 'has_other_insurances',
            type: 'date',
            label: 'Seit wann?',
            id: 'other_insurances_start_date',
        },
        {
            showIf: 'has_other_insurances',
            type: 'textarea',
            label: 'Namen, Anschriften und Vers.-Schein-Nummern (Mitgliedsnummern) der Gesellschaften?',
            id: 'other_insurances_details',
        },
    ],
}

const bankAccount: FormConfig = {
    key: 'bank_account',
    title: 'Konto',
    items: [
        {
            type: 'text',
            label: 'Name des Kontoinhabers',
            id: 'account_holder',
        },
        {
            type: 'number',
            label: 'IBAN',
            id: 'iban',
        },
        {
            type: 'textarea',
            label: 'Geldinstitut (Name, Sitz)',
            id: 'bank_name_address',
        },
    ],
}

export const accidentInsuranceForms: FormConfig[] = [
    insuredPerson,
    accidentCourse,
    alcoholConsumption,
    policeReport,
    injuries,
    healthState,
    pension,
    otherInsurances,
    bankAccount,
]