import {divider, FormConfig} from "@/components/reportForms/domain";

const damageLocation: FormConfig = {
    key: "schaden_ort",
    title: "Schadenort und -zeitpunkt",
    items: [
        {
            type: 'datetime',
            label: 'Wann ereignete sich der Schaden?',
            id: 'damage_date_time',
        },
        {
            type: 'datetime',
            label: 'Wann erhi<PERSON>ten Si<PERSON> von dem Schaden Kenntnis?',
            id: 'knowledge_damage_date_time',
        },
        {
            type: 'text',
            label: 'Wo ereignete sich der Schaden?',
            id: 'damage_location',
        },
        {
            type: 'text',
            label: 'In welchem Raum ereignete sich der Schaden?',
            id: 'damage_location_room',
        },
        {
            type: 'dropdown',
            label: 'Gebäudeart und -nutzung',
            id: 'building_type_usage',
            options: [
                { label: 'Einfamilienhaus', value: 'single_family_house' },
                { label: 'Mehrfamilienhaus', value: 'multiple_family_house' },
                { label: 'Gewerbliche Nutzung', value: 'commercial_use' },
            ]
        },
        {
            type: 'dropdown',
            label: 'Wer ist Eigentümer des Gebäudes oder der Wohnung?',
            id: 'building_owner',
            options: [
                { label: 'Versicherungsnehmer', value: 'insured_person' },
                { label: 'Anderer', value: 'someone_else' },
            ]
        },
        {
            showIf: { building_owner: 'someone_else' },
            type: 'text',
            label: 'Name',
            id: 'building_owner_name',
        },
        {
            showIf: { building_owner: 'someone_else' },
            type: 'text',
            label: 'Adresse',
            id: 'building_owner_address',
        },
        {
            type: 'date',
            label: 'Wann wurde der Schaden gemeldet?',
            id: 'date_damage_reported',
        },
        {
            type: 'text',
            label: 'Wem wurde der Schaden gemeldet?',
            id: 'date_damage_reported_person',
        },
        {
            type: 'date',
            label: 'Wann wurde der Polizei Anzeige erstattet?',
            id: 'date_damage_reported_police',
        },
        {
            type: 'text',
            label: 'Polizei Tagebuch-Nr.',
            id: 'police_diary_nr',
        },
        {
            type: 'text',
            label: 'Dienststelle/Adresse',
            id: 'police_address',
        },
    ]
}
const owner: FormConfig = {
    key: "owner",
    title: "Eigentümer",
    items: [
        {
            type: 'dropdown',
            label: 'Wer ist Eigentümer der betroffenen Sachen?',
            id: 'owner',
            options: [
                { label: 'Versicherungsnehmer', value: 'insured_person' },
                { label: 'Anderer', value: 'someone_else' },
            ]
        },
        {
            showIf: { owner: 'someone_else' },
            type: 'text',
            label: 'Name',
            id: 'owner_name',
        },
        {
            showIf: { owner: 'someone_else' },
            type: 'text',
            label: 'Adresse',
            id: 'owner_address',
        },
    ],
}
const otherInsurances: FormConfig = {
    key: "other_insurances",
    title: "Andere Versicherungen",
    items: [
        {
            type: 'boolean-dropdown',
            label: 'Bestehen anderweitige Versicherungen für die vom Schaden betroffenen Sachen?',
            id: 'has_other_insurances',
        },
        {
            showIf: 'has_other_insurances',
            type: 'text',
            label: 'Name der Gesellschaft',
            id: 'company_name',
        },
        {
            showIf: 'has_other_insurances',
            type: 'text',
            label: 'Ort der Gesellschaft',
            id: 'company_address',
        },
        {
            showIf: 'has_other_insurances',
            type: 'text',
            label: 'Aktenzeichen der Gesellschaft',
            id: 'company_file_number',
        },
    ],
}

const damageDetails: FormConfig = {
    key: "damage_details",
    title: "Angaben zum Schaden",
    items: [
        {
            type: 'dropdown',
            label: 'Wer hat den Schaden verschuldet?',
            id: 'person_fault',
            options: [
                { label: 'Versicherungsnehmer', value: 'insured_person' },
                { label: 'Anderer', value: 'someone_else' },
            ]
        },
        {
            showIf: { person_fault: 'someone_else' },
            type: 'datetime',
            label: 'Name',
            id: 'person_fault_name',
        },
        {
            showIf: { person_fault: 'someone_else' },
            type: 'datetime',
            label: 'Adresse',
            id: 'person_fault_address',
        },
        divider,
        {
            type: 'boolean-dropdown',
            label: 'Wurden Sie bereits von Schäden gleicher Art betroffen?',
            id: 'was_same_kind_damage',
        },
        {
            showIf: 'was_same_kind_damage',
            type: 'date',
            label: 'Wann',
            id: 'when_same_kind_damage',
        },
        {
            showIf: 'was_same_kind_damage',
            type: 'price',
            label: 'Schadenhöhe',
            id: 'same_kind_damage_money_amount',
        },
        divider,
        {
            type: 'price',
            label: 'Wie hoch schätzen Sie den Schaden?',
            id: 'damage_amount',
        },
        {
            type: 'price',
            label: 'Wie hoch schätzen Sie den Neuwert der gesamten versicherten Sachen?',
            id: 'insured_things_price',
        },
        {
            type: 'boolean-dropdown',
            label: 'Sind Sie vorsteuerabzugsberechtigt (§15 UstG)?',
            id: 'is_entitled_input_tax_deduction',
        },
    ]
}
const damageCourse: FormConfig = {
    key: "damage_course",
    title: "Schadenhergang",
    items: [
        {
            type: 'textarea',
            label: 'Bitte schildern Sie den Schadenhergang so ausführlich, dass ein möglichst genaues Bild entsteht:',
            id: 'course_of_damage',
        },
        {
            type: 'textarea',
            label: 'Welche Schadenminderungsmaßnahmen wurden ergriffen?',
            id: 'mitigation_measures',
        },
    ]
}
const additionalQuestions: FormConfig = {
    key: "additional_questions",
    title: "Zusatzfragen",
    items: [
        {
            type: 'checkbox',
            label: 'Einbruchdiebstahl',
            id: 'is_burglary_damage',
        },
        {
            showIf: 'is_burglary_damage',
            type: 'boolean-dropdown',
            label: 'Wurden Behältnisse gewaltsam geöffnet?',
            id: 'forced_open',
        },
        {
            showIf: 'forced_open',
            type: 'textarea',
            label: 'Beschreibung',
            id: 'forced_open_description',
        },
        {
            showIf: 'is_burglary_damage',
            type: 'text',
            label: 'Wo befanden sich die Schlüssel?',
            id: 'location_keys',
        },
        {
            showIf: 'is_burglary_damage',
            type: 'boolean-dropdown',
            label: 'Sind Einbruchsmerkmale sichtbar?',
            tooltip: 'Beschädigungen an Wänden, Decken, Fenstern, Türen, Schlössern, Behältnissen usw.',
            id: 'burglary_characteristics',
        },
        {
            showIf: 'burglary_characteristics',
            type: 'textarea',
            label: 'Beschreibung',
            id: 'burglary_characteristics_description',
        },
        {
            showIf: 'is_burglary_damage',
            type: 'dropdown',
            label: 'Welche Mittel haben die Diebe zum Öffnen angewendet?',
            id: 'used_keys',
            options: [
                { label: 'Richtige Schlüssel', value: 'Richtige Schlüssel' },
                { label: 'Nachschlüssel', value: 'Nachschlüssel' },
                { label: 'Gewalt', value: 'Gewalt' },
            ]
        },
        divider,
        {
            type: 'checkbox',
            label: 'Leitungswasserschäden',
            id: 'is_tap_water_damage',
        },
        {
            showIf: 'is_tap_water_damage',
            type: 'dropdown',
            label: 'An welcher Anlage ist der Schaden entstanden?',
            id: 'tap_water_damage_location',
            options: [
                { label: 'Zuleitung', value: 'Zuleitung' },
                { label: 'Ableitung', value: 'Ableitung' },
                { label: 'Heizung', value: 'Heizung' },
            ]
        },
        {
            showIf: 'is_tap_water_damage',
            type: 'dropdown',
            label: 'Wer bewohnt die vom Schaden betroffene Wohnung?',
            id: 'tap_water_person',
            options: [
                { label: 'Versicherungsnehmer', value: 'Versicherungsnehmer' },
                { label: 'Andere', value: 'Andere' },
            ]
        },
        {
            showIf: { tap_water_person: 'Andere' },
            type: 'text',
            label: 'Name',
            id: 'tap_water_person_name',
        },
        {
            showIf: 'is_tap_water_damage',
            type: 'boolean-dropdown',
            label: 'Hat dieser eine Leitungswasser-Versicherung?',
            id: 'is_tap_insured',
        },
        {
            showIf: 'is_tap_water_damage',
            type: 'boolean-dropdown',
            label: 'Haben Sie als Mieter Gebäudeteile auf eigene Rechnung eingebracht?',
            id: 'part_building_own_expense',
        },
        {
            showIf: 'part_building_own_expense',
            type: 'text',
            label: 'Welche Gebäudeteile?',
            id: 'part_building_own_expense_description',
        },
        {
            showIf: 'part_building_own_expense',
            type: 'price',
            label: 'Wert',
            id: 'part_building_own_expense_amount',
        },
        divider,
        {
            type: 'checkbox',
            label: 'Schäden an Fußböden durch Feuer, Leitungswasser oder Sturm',
            id: 'is_fe_lw_st',
        },
        {
            showIf: 'is_fe_lw_st',
            type: 'dropdown',
            label: 'Wer hat den Belag angeschafft?',
            id: 'person_floor_creation',
            options: [
                { label: 'Gebäudeeigentümer', value: 'Gebäudeeigentümer' },
                { label: 'Wohnungseigentümer', value: 'Wohnungseigentümer' },
                { label: 'Mieter', value: 'Mieter' },
            ]
        },
        {
            showIf: 'is_fe_lw_st',
            type: 'dropdown',
            label: 'Wie ist der Fußboden verlegt?',
            id: 'floor_attachment',
            options: [
                { label: 'Lose', value: 'lose' },
                { label: 'Vollflächig verklebt', value: 'vollflächig verklebt' },
                { label: 'An den Rändern mit doppelseitigem Klebeband befestigt', value: 'an den Rändern mit doppelseitigem Klebeband befestigt' },
            ]
        },
        {
            showIf: 'is_fe_lw_st',
            type: 'dropdown',
            label: 'Was befindet sich unter dem Fußbodenbelag?',
            id: 'below_floor_covering',
            options: [
                { label: 'Estrich/Beton', value: 'Estrich/Beton' },
                { label: 'Holzdielen', value: 'Holzdielen' },
                { label: 'Parkett', value: 'Parkett' },
                { label: 'PVC/Linoleum', value: 'NePVC/Lineumin' },
                { label: 'Anderes', value: 'Anderes' },
            ]
        },
        {
            showIf: { below_floor_covering: 'Anderes' },
            type: 'text',
            label: 'Anderes',
            id: 'below_floor_covering_other',
        },
        divider,
        {
            type: 'checkbox',
            label: 'Glasschäden',
            id: 'is_glas_damage',
        },
        {
            showIf: 'is_glas_damage',
            type: 'boolean-dropdown',
            label: 'War die Scheibe vor dem Schadenereignis fertig eingesetzt?',
            id: 'glas_inserted_before_damage',
        },
        {
            showIf: 'is_glas_damage',
            type: 'boolean-dropdown',
            label: 'Sind Mängel an der Umrahmung vorhanden und ist hierauf der Schaden zurückzuführen?',
            id: 'glas_imperfactions',
        },
        {
            showIf: 'glas_imperfactions',
            type: 'textarea',
            label: 'Beschreibung',
            id: 'glas_imperfactions_decription',
        },
        {
            showIf: 'is_glas_damage',
            type: 'boolean-dropdown',
            label: 'Haben Sie den Reparaturauftrag bereits erteilt?',
            id: 'repair_order_booked',
        },
        {
            showIf: 'repair_order_booked',
            type: 'text',
            label: 'Firma',
            id: 'repair_order_company',
        },
        {
            showIf: 'is_glas_damage',
            type: 'dropdown',
            label: 'Die Entschädigung soll gezahlt werden an',
            id: 'glas_compensation_person',
            options: [
                { label: 'Versicherungsnehmer', value: 'insured_person' },
                { label: 'Glaser gemäß Rechnung', value: 'glazier' },
            ]
        },
    ]
}

const damageStatement: FormConfig = {
    key: "schadenaufstelllung",
    title: "Schadenaufstellung",
    items: [
        {
            type: 'date',
            label: 'Schadendatum',
            id: 'damage_date',
        },
        {
            type: 'text',
            label: 'Schadensort',
            id: 'damage_location',
        },
        {
            type: 'text',
            label: 'Schadensnummer der Versicherung',
            id: 'external_report_number',
        },
        {
            type: 'text',
            label: 'Versicherte Gefahr',
            id: 'coverd_risk',
        },
        {
            type: 'textarea',
            label: 'Beschreibung',
            tooltip: 'Geben Sie eine detaillierte Beschreibung des Schadens ein.',
            id: 'text',
        },
    ]
}

const bankDetails: FormConfig = {
    key: "basis",
    title: "Bankangaben",
    items: [
        {
            type: 'text',
            label: 'Bankinstitut',
            id: 'bank_company',
        },
        {
            type: 'text',
            label: 'IBAN',
            id: 'bank_iban',
        },
        {
            type: 'text',
            label: 'BIC',
            id: 'bank_bic',
        },
    ]
}

export const propertyInsurancesForms: FormConfig[] = [
    damageLocation,
    owner,
    otherInsurances,
    damageDetails,
    damageCourse,
    additionalQuestions,
    damageStatement,
    bankDetails,
]