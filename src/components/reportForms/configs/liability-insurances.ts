import {divider, FormConfig} from "@/components/reportForms/domain";

const injuredPerson: FormConfig = {
    key: "injured_person",
    title: "Geschädigte Person",
    items: [
        {
            type: 'boolean-dropdown',
            label: 'Besteht zwischen Ihnen und der geschädigten Person ein Familien- oder Verwandtschaftsverhältnis?',
            id: 'is_victim_a_relative',
        },
        {
            type: 'text',
            label: '<PERSON><PERSON> Verhältnis?',
            id: 'how_is_victim_related',
            showIf: 'is_victim_a_relative',
        },
        {
            type: 'boolean-dropdown',
            label: 'Steht die geschädigte Person in Ihren Diensten?',
            id: 'is_victim_an_employee',
        },
        {
            type: 'text',
            label: 'Als',
            id: 'victim_employed_as',
            showIf: 'is_victim_an_employee',
        },
        {
            type: 'boolean-dropdown',
            label: 'Leben Sie mit der geschädigten Person in einem gemeinsamen Haushalt?',
            id: 'is_victim_from_same_household',
        },
    ]
}
const details: FormConfig = {
    key: "details",
    title: "Angaben zum Schaden",
    items: [
        {
            type: 'text',
            label: 'Wann ereignete sich der Schaden?',
            id: 'datetime',
        },
        {
            type: 'text',
            label: 'An welchem Ort? (PLZ, Ort, Straße, Haus-Nr., Zimmer, Platz etc.)',
            id: 'place',
        },
        {
            type: 'text',
            label: 'Wer hat den Schaden verursacht? (Name, Geburtsdatum, Anschrift, Verwandtschaftsverhältnis zu Ihnen)',
            id: 'culprit',
        },
    ]
}
const damageCourse: FormConfig = {
    key: "damage_course",
    title: "Schadenshergang",
    items: [
        {
            type: 'textarea',
            label: 'Bitte schildern Sie den Schadenhergang möglichst genau.',
            id: 'course',
        },
        {
            type: 'textarea',
            label: 'Welche Personen waren Augenzeugen des Schadenereignisses? (Name, Alter, Beruf, Adresse)',
            id: 'witnesses',
        },
        {
            type: 'boolean-dropdown',
            label: 'Wurde der Schaden protokolliert?',
            id: 'was_documented_by_police',
        },
        {
            type: 'text',
            label: 'Genaue Anschrift und Aktenzeichen der Polizeidienststelle',
            id: 'police_documentation_details',
            showIf: 'was_documented_by_police',
        },
        {
            type: 'text',
            label: 'Gegen wen ist ein Ermittlungs- oder Strafverfahren eingeleitet worden?',
            id: 'proceeding_initiated_against',
        },
        {
            type: 'text',
            label: 'Genaue Anschrift und Aktenzeichen der Behörde',
            id: 'proceeding_details',
        },
        {
            type: 'textarea',
            label: 'Mit welcher Begründung wird Ihnen oder einer nahestehenden Person ein Verschulden beigemessen?',
            id: 'why_guilty',
        },
        {
            type: 'boolean-dropdown',
            label: 'Hat die vom Schaden betroffene Person den Schaden ganz oder teilweise selbst verursacht?',
            id: 'has_victim_caused_damage',
        },
        {
            type: 'text',
            label: 'Warum?',
            id: 'why_victim_caused_damage',
            showIf: 'has_victim_caused_damage',
        },
        {
            type: 'boolean-dropdown',
            label: 'Sind Schadenersatzansprüche gegen Sie erhoben worden?',
            id: 'were_compensation_claims_made',
        },
        {
            type: 'dropdown',
            label: 'Wie wurden die Ansprüche erhoben?',
            id: 'how_were_compensation_claims_made',
            options: [
                { label: 'Mündlich', value: 'verbally' },
                { label: 'Schriftlich', value: 'in_writing' },
            ],
            showIf: 'were_compensation_claims_made',
        },
        {
            type: 'date',
            label: 'Datum der Anspruchserhebung:',
            id: 'when_were_compensation_claims_made',
            showIf: 'were_compensation_claims_made',
        },
        {
            type: 'boolean-dropdown',
            label: 'Sind die Schadenersatzansprüche zu hoch?',
            id: 'are_compensation_claims_too_big',
        },
        {
            type: 'text',
            label: 'Begründung:',
            id: 'why_compensation_claims_too_big',
            showIf: 'are_compensation_claims_too_big',
        },
    ]
}

const additionalQuestions: FormConfig = {
    key: "additional_questions",
    title: "Zusatzfragen",
    items: [
        {
            type: 'checkbox',
            label: 'Personenschäden',
            id: 'is_personal_injury',
        },
        {
            showIf: 'is_personal_injury',
            type: 'text',
            id: 'injury_nature',
            label: 'Worin besteht die Verletzung?',
        },
        {
            showIf: 'is_personal_injury',
            type: 'date',
            id: 'victim_birthday',
            label: 'Geburtsdatum der verletzten Person:',
        },
        {
            showIf: 'is_personal_injury',
            type: 'dropdown',
            id: 'victim_family_status',
            label: 'Familienstand',
            options: [
                { value: 'single', label: 'Ledig' },
                { value: 'married', label: 'Verheiratet' },
                { value: 'widowed', label: 'Verwitwet' },
                { value: 'divorced', label: 'Geschieden' },
            ]
        },
        {
            showIf: 'is_personal_injury',
            type: 'text',
            id: 'victim_kids',
            label: 'Anzahl der Kinder und Alter:',
        },
        divider,
        {
            type: 'checkbox',
            label: 'Beschädigung einer fremden Sache (auch Tiere)',
            id: 'is_property_damage',
        },
        {
            showIf: 'is_property_damage',
            type: 'text',
            id: 'damaged_item',
            label: 'Welche Sache wurde beschädigt?',
        },
        {
            showIf: 'is_property_damage',
            type: 'text',
            id: 'nature_of_damage',
            label: 'Worin besteht die Beschädigung?',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'was_previously_damaged',
            label: 'Wies die beschädigte Sache Vorschäden auf?',
        },
        {
            showIf: 'was_previously_damaged',
            type: 'text',
            id: 'previous_damages',
            label: 'Welche?',
        },
        {
            showIf: 'is_property_damage',
            type: 'date',
            id: 'when_was_item_bought',
            label: 'Wann wurde der Gegenstand angeschafft?',
        },
        {
            showIf: 'is_property_damage',
            type: 'price',
            id: 'item_price',
            label: 'Anschaffungspreis:',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'is_restoration_possible',
            label: 'Halten Sie eine Wiederherstellung für möglich?',
        },
        {
            showIf: 'is_property_damage',
            type: 'price',
            id: 'damage_estimate',
            label: 'Wie hoch schätzen Sie den Schaden?',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'was_wear_and_tear_taken_into_account',
            label: 'Wurde Abnutzung beim Schaden berücksichtigt?',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'are_damaged_items_insured',
            label: 'Sind die beschädigten Gegenstände versichert?',
        },
        {
            showIf: 'are_damaged_items_insured',
            type: 'text',
            id: 'insured_with_company',
            label: 'Bei welcher Gesellschaft?',
        },
        {
            showIf: 'are_damaged_items_insured',
            type: 'text',
            id: 'insurance_file_number',
            label: 'Aktenzeichen',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'was_item_loaned',
            label: 'Wurde die Sache gemietet, gepachtet oder geliehen?',
        },
        {
            showIf: 'is_property_damage',
            type: 'boolean-dropdown',
            id: 'was_damaged_at_work',
            label: 'Ist der Schaden bei einer Tätigkeit entstanden?',
        },
    ]
}

export const liabilityInsuranceForms: FormConfig[] = [
    injuredPerson,
    details,
    damageCourse,
    additionalQuestions
]