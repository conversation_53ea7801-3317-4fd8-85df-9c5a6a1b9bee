import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Button,
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Switch,
  FormControlLabel,
  Box,
  Stack,
} from "@mui/material";
import { useTranslations } from "next-intl";

type ConsentCategories = {
  essential: boolean; // always true
  functional: boolean;
  analytics: boolean;
  marketing: boolean;
};

const STORAGE_KEY = "cookieConsent";
const COOKIE_NAME = "cookie_consent"; // optional server-readable cookie

function readStoredConsent(): ConsentCategories | null {
  try {
    const raw = typeof window !== "undefined" ? localStorage.getItem(STORAGE_KEY) : null;
    if (!raw) return null;
    return JSON.parse(raw) as ConsentCategories;
  } catch {
    return null;
  }
}

function persistConsent(consent: ConsentCategories) {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(consent));
    // write a non-HttpOnly cookie so server-side can read consent on subsequent requests.
    const payload = `e=${consent.essential ? 1 : 0}&f=${consent.functional ? 1 : 0}&a=${consent.analytics ? 1 : 0}&m=${consent.marketing ? 1 : 0}`;
    // cookie expires in 365 days
    document.cookie = `${COOKIE_NAME}=${encodeURIComponent(payload)}; Path=/; Max-Age=${60 * 60 * 24 * 365}; SameSite=Lax`;
  } catch (e) {
    // ignore
    // as fallback, client-only will still work via localStorage
  }
}

/**
 * helper: check if a category is allowed (reads localStorage).
 * frontend code should call this before loading analytics/marketing scripts.
 */
export function isCategoryAllowed(category: keyof Omit<ConsentCategories, "essential">) {
  const stored = readStoredConsent();
  if (!stored) return false;
  return stored[category];
}

/**
 * public: force-read consent (null if none)
 */
export function getStoredConsent(): ConsentCategories | null {
  return readStoredConsent();
}

export default function CookieConsent() {
  const t = useTranslations();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [consent, setConsent] = useState<ConsentCategories>({
    essential: true,
    functional: false,
    analytics: false,
    marketing: false,
  });

  const initialLoad = useRef(true);

  useEffect(() => {
    const stored = readStoredConsent();
    if (!stored) {
      // show banner on first visit
      setOpenSnackbar(true);
    } else {
      setConsent(stored);
    }
    initialLoad.current = false;
  }, []);

  const acceptAll = () => {
    const next = { essential: true, functional: true, analytics: true, marketing: true };
    setConsent(next);
    persistConsent(next);
    setOpenSnackbar(false);
    setOpenDialog(false);
    // run optional hook/event to load third-party services (not included here)
    // e.g. window.dispatchEvent(new CustomEvent('cookie-consent-changed', {detail: next}))
  };

  const rejectNonEssential = () => {
    const next = { essential: true, functional: false, analytics: false, marketing: false };
    setConsent(next);
    persistConsent(next);
    setOpenSnackbar(false);
    setOpenDialog(false);
  };

  const openConfigure = () => {
    setOpenDialog(true);
    setOpenSnackbar(false);
  };

  const savePreferences = () => {
    const next = { ...consent, essential: true };
    setConsent(next);
    persistConsent(next);
    setOpenDialog(false);
  };

  const toggle = (key: keyof Omit<ConsentCategories, "essential">) => {
    setConsent((c) => ({ ...c, [key]: !c[key] }));
  };

  // ensure essential always true
  useEffect(() => {
    if (!consent.essential) setConsent((c) => ({ ...c, essential: true }));
  }, [consent.essential]);

  return (
    <>
      <Snackbar
        open={openSnackbar}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        onClose={() => setOpenSnackbar(false)}
        message={
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1, maxWidth: 760 }}>
            <Typography variant="h6">{t("cookieConsent.title")}</Typography>
            <Typography variant="body2">{t("cookieConsent.description")}</Typography>
          </Box>
        }
        action={
          <Stack direction="row" spacing={1} sx={{ alignItems: "center", ml: 2 }}>
            <Button variant="contained" onClick={acceptAll} size="small">
              {t("cookieConsent.acceptAll")}
            </Button>
            <Button variant="outlined" onClick={rejectNonEssential} size="small">
              {t("cookieConsent.reject")}
            </Button>
            <Button variant="text" onClick={openConfigure} size="small">
              {t("cookieConsent.configure")}
            </Button>
          </Stack>
        }
        sx={{
          "& .MuiSnackbarContent-root": { borderRadius: 2, padding: 2 },
        }}
      />

      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        aria-labelledby="cookie-configure-title"
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle id="cookie-configure-title">{t("cookieConsent.configureTitle")}</DialogTitle>
        <DialogContent dividers>
          <Typography variant="body2" paragraph>
            {t("cookieConsent.configureDescription")}
          </Typography>

          <Box sx={{ mt: 1 }}>
            <FormControlLabel
              control={<Switch checked disabled />}
              label={<Typography variant="subtitle1">{t("cookieConsent.categories.essential")}</Typography>}
            />
            <Typography variant="caption" display="block" sx={{ ml: 6, mb: 1 }}>
              {t("cookieConsent.categories.essentialDesc")}
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={consent.functional}
                  onChange={() => toggle("functional")}
                  inputProps={{ "aria-label": "Toggle functional cookies" }}
                />
              }
              label={<Typography variant="subtitle1">{t("cookieConsent.categories.functional")}</Typography>}
            />
            <Typography variant="caption" display="block" sx={{ ml: 6, mb: 1 }}>
              {t("cookieConsent.categories.functionalDesc")}
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={consent.analytics}
                  onChange={() => toggle("analytics")}
                  inputProps={{ "aria-label": "Toggle analytics cookies" }}
                />
              }
              label={<Typography variant="subtitle1">{t("cookieConsent.categories.analytics")}</Typography>}
            />
            <Typography variant="caption" display="block" sx={{ ml: 6, mb: 1 }}>
              {t("cookieConsent.categories.analyticsDesc")}
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={consent.marketing}
                  onChange={() => toggle("marketing")}
                  inputProps={{ "aria-label": "Toggle marketing cookies" }}
                />
              }
              label={<Typography variant="subtitle1">{t("cookieConsent.categories.marketing")}</Typography>}
            />
            <Typography variant="caption" display="block" sx={{ ml: 6, mb: 0 }}>
              {t("cookieConsent.categories.marketingDesc")}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={rejectNonEssential}>{t("cookieConsent.reject")}</Button>
          <Button onClick={savePreferences}>{t("cookieConsent.save")}</Button>
          <Button onClick={acceptAll} variant="contained">
            {t("cookieConsent.acceptAll")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
