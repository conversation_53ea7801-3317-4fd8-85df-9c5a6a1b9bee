// src/components/box/CustomerBox.tsx
import router from "next/router";
import { useState } from "react";

import { CustomerData } from "@/types";
import CollapsibleBox from "./CollapsibleBox";

import { TableContainer, Paper, Table, TableBody, TableRow, TableCell, IconButton, styled, Tooltip, Box } from "@mui/material";
import { Edit as EditIcon, Info as InfoIcon } from "@mui/icons-material";


interface CustomerInformationBoxProps {
    customerData: CustomerData;
    customer_number: string;
    edit_button?: boolean;
    details_button?: boolean;
    visible?: boolean;
    simplified?: boolean;
}

const CustomerInformationBox: React.FC<CustomerInformationBoxProps> = ({ customerData, customer_number, edit_button, details_button, visible, simplified }) => {
    const [customerInformationIsVisible, setCustomerInformationIsVisible] = useState(visible || false);

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0
        },
    }));

    const handleDetailsClick = (customer_number: string, event: React.MouseEvent) => {
        const url = `/customer/${customer_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleEditClick = (customer_number: string, event: React.MouseEvent) => {
        const url = `/customer/edit/${customer_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    return (
        <CollapsibleBox
            title="Kundeninformationen"
            isVisible={customerInformationIsVisible}
            setIsVisible={setCustomerInformationIsVisible}
        >
            <Box>
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            <StyledTableRow>
                                <TableCell><strong>Kundennummer:</strong></TableCell>
                                <TableCell>{customerData.customer_number}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Name:</strong></TableCell>
                                <TableCell>
                                    {customerData.salutation} {customerData.name_prefix && `${customerData.name_prefix} `}{customerData.first_name} {customerData.last_name}
                                </TableCell>
                            </StyledTableRow>

                            {customerData.care_of && <StyledTableRow>
                                <TableCell><strong>c/o:</strong></TableCell>
                                <TableCell>{customerData.care_of}</TableCell>
                            </StyledTableRow>}

                            {!simplified && <StyledTableRow>
                                <TableCell><strong>Adresse/ Hausnummer:</strong></TableCell>
                                <TableCell>
                                    {customerData.street} {customerData.house_number}
                                </TableCell>
                            </StyledTableRow>}

                            {!simplified && <StyledTableRow>
                                <TableCell><strong>Postleitzahl/ Ort:</strong></TableCell>
                                <TableCell>
                                    {customerData.postal_code} {customerData.city}
                                </TableCell>
                            </StyledTableRow>}

                            <StyledTableRow>
                                <TableCell><strong>Email:</strong></TableCell>
                                <TableCell>{customerData.email}</TableCell>
                            </StyledTableRow>
                        </TableBody>
                    </Table>
                </TableContainer>

                {details_button && <Box justifySelf='flex-end'>
                    <Tooltip title={`Kundendetails für ${customerData.first_name} ${customerData.last_name} öffnen`} placement="top">
                        <IconButton
                            onClick={(event) => handleDetailsClick(customerData.customer_number, event)}
                        >
                            <InfoIcon />
                        </IconButton>
                    </ Tooltip>
                </Box>}

                {edit_button && <Box justifySelf='flex-end'>
                    <Tooltip title={`Kundendetails für ${customerData.first_name} ${customerData.last_name} anpassen.`} placement="top">
                        <IconButton
                            onClick={(event) => handleEditClick(customerData.customer_number, event)}
                        >
                            <EditIcon />
                        </IconButton>
                    </Tooltip>
                </Box>}
            </Box>
        </CollapsibleBox>
    )
}

export default CustomerInformationBox;