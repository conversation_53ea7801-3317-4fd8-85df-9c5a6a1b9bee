// src/components/box/CustomerBox.tsx
import { Edit as EditIcon, Info as InfoIcon } from '@mui/icons-material';
import {
  Box,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import router from 'next/router';
import { useState } from 'react';

import { type Customer } from '@/generated/prisma-postgres';

import CollapsibleBox from './CollapsibleBox';

interface CustomerInformationBoxProps {
  customerData: Customer;
  customerNumber: string;
  editButton?: boolean;
  detailsButton?: boolean;
  visible?: boolean;
  simplified?: boolean;
}

const CustomerInformationBox: React.FC<CustomerInformationBoxProps> = ({
  customerData,
  editButton,
  detailsButton,
  visible,
  simplified,
}) => {
  const [customerInformationIsVisible, setCustomerInformationIsVisible] =
    useState(visible || false);

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  const handleDetailsClick = (
    customerNumber: string,
    event: React.MouseEvent
  ) => {
    const url = `/customer/${customerNumber}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleEditClick = (customerNumber: string, event: React.MouseEvent) => {
    const url = `/customer/edit/${customerNumber}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title="Kundeninformationen"
      isVisible={customerInformationIsVisible}
      setIsVisible={setCustomerInformationIsVisible}
    >
      <Box>
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              <StyledTableRow>
                <TableCell>
                  <strong>Kundennummer:</strong>
                </TableCell>
                <TableCell>{customerData.customerNumber}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Name:</strong>
                </TableCell>
                <TableCell>
                  {customerData.salutation}{' '}
                  {customerData.namePrefix && `${customerData.namePrefix} `}
                  {customerData.firstName} {customerData.lastName}
                </TableCell>
              </StyledTableRow>

              {customerData.careOf && (
                <StyledTableRow>
                  <TableCell>
                    <strong>c/o:</strong>
                  </TableCell>
                  <TableCell>{customerData.careOf}</TableCell>
                </StyledTableRow>
              )}

              {!simplified && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Adresse/ Hausnummer:</strong>
                  </TableCell>
                  <TableCell>
                    {customerData.street} {customerData.houseNumber}
                  </TableCell>
                </StyledTableRow>
              )}

              {!simplified && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Postleitzahl/ Ort:</strong>
                  </TableCell>
                  <TableCell>
                    {customerData.postalCode} {customerData.city}
                  </TableCell>
                </StyledTableRow>
              )}

              <StyledTableRow>
                <TableCell>
                  <strong>Email:</strong>
                </TableCell>
                <TableCell>{customerData.email}</TableCell>
              </StyledTableRow>
            </TableBody>
          </Table>
        </TableContainer>

        {detailsButton && (
          <Box justifySelf="flex-end">
            <Tooltip
              title={`Kundendetails für ${customerData.firstName} ${customerData.lastName} öffnen`}
              placement="top"
            >
              <IconButton
                onClick={(event) =>
                  handleDetailsClick(customerData.customerNumber, event)
                }
              >
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {editButton && (
          <Box justifySelf="flex-end">
            <Tooltip
              title={`Kundendetails für ${customerData.firstName} ${customerData.lastName} anpassen.`}
              placement="top"
            >
              <IconButton
                onClick={(event) =>
                  handleEditClick(customerData.customerNumber, event)
                }
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    </CollapsibleBox>
  );
};

export default CustomerInformationBox;
