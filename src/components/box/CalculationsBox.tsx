import RefreshIcon from '@mui/icons-material/Refresh';
import {
  Box,
  Button,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';

import { type Contract } from '@/generated/prisma-postgres';
import {
  calculateInsuranceDaysCurrentYear,
  calculateInsuranceRuntime,
} from '@/utils/calculationUtil';

import CollapsibleBox from './CollapsibleBox';

interface CalculationsBoxProps {
  calculationPreviewData: Contract;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
  handlePreviewCalculation: () => void;
  isFormDataChanged: boolean;
}

const CalculationsBox: React.FC<CalculationsBoxProps> = ({
  calculationPreviewData,
  isVisible,
  setIsVisible,
  handlePreviewCalculation,
  isFormDataChanged,
}) => {
  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  const formatEuro = (value: number) =>
    (Math.round(value * 100) / 100).toLocaleString('de', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }) + ' EUR';

  return (
    <CollapsibleBox
      title="Beitragsvorschau"
      isVisible={isVisible}
      setIsVisible={setIsVisible}
    >
      <Box width="100%">
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              {calculationPreviewData ? (
                <>
                  <Typography pl={1}>Gesamtlaufzeit</Typography>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Netto:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.premie! / 365) *
                          calculateInsuranceRuntime(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Versicherungssteuer:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.tax! / 365) *
                          calculateInsuranceRuntime(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                  {calculationPreviewData.contractType === 'wohngebaeude' &&
                    calculationPreviewData.premieGlassInsurance && (
                      <>
                        <StyledTableRow>
                          <TableCell>
                            <strong>Netto (Glas):</strong>
                          </TableCell>
                          <TableCell>
                            {formatEuro(
                              (calculationPreviewData.premieGlassInsurance /
                                365) *
                                calculateInsuranceRuntime(
                                  calculationPreviewData.insuranceStartDate!,
                                  calculationPreviewData.insuranceEndDate!
                                )
                            )}
                          </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                          <TableCell>
                            <strong>Versicherungssteuer (Glas):</strong>
                          </TableCell>
                          <TableCell>
                            {formatEuro(
                              (calculationPreviewData.glassTax! / 365) *
                                calculateInsuranceRuntime(
                                  calculationPreviewData.insuranceStartDate!,
                                  calculationPreviewData.insuranceEndDate!
                                )
                            )}
                          </TableCell>
                        </StyledTableRow>
                      </>
                    )}
                  <StyledTableRow>
                    <TableCell>
                      <strong>Brutto:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.invoiceAmount! / 365) *
                          calculateInsuranceRuntime(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                  <br />
                  <Typography pl={1}>
                    Beitrag{' '}
                    {new Date(
                      calculationPreviewData.insuranceStartDate!
                    ).getFullYear()}
                  </Typography>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Netto:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.premie! / 365) *
                          calculateInsuranceDaysCurrentYear(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Versicherungssteuer:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.tax! / 365) *
                          calculateInsuranceDaysCurrentYear(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                  {calculationPreviewData.contractType === 'wohngebaeude' &&
                    calculationPreviewData.premieGlassInsurance && (
                      <>
                        <StyledTableRow>
                          <TableCell>
                            <strong>Netto (Glas):</strong>
                          </TableCell>
                          <TableCell>
                            {formatEuro(
                              (calculationPreviewData.premieGlassInsurance /
                                365) *
                                calculateInsuranceDaysCurrentYear(
                                  calculationPreviewData.insuranceStartDate!,
                                  calculationPreviewData.insuranceEndDate!
                                )
                            )}
                          </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                          <TableCell>
                            <strong>Versicherungssteuer (Glas):</strong>
                          </TableCell>
                          <TableCell>
                            {formatEuro(
                              (calculationPreviewData.glassTax! / 365) *
                                calculateInsuranceDaysCurrentYear(
                                  calculationPreviewData.insuranceStartDate!,
                                  calculationPreviewData.insuranceEndDate!
                                )
                            )}
                          </TableCell>
                        </StyledTableRow>
                      </>
                    )}
                  <StyledTableRow>
                    <TableCell>
                      <strong>Brutto:</strong>
                    </TableCell>
                    <TableCell>
                      {formatEuro(
                        (calculationPreviewData.invoiceAmount! / 365) *
                          calculateInsuranceDaysCurrentYear(
                            calculationPreviewData.insuranceStartDate!,
                            calculationPreviewData.insuranceEndDate!
                          )
                      )}
                    </TableCell>
                  </StyledTableRow>
                </>
              ) : (
                <StyledTableRow>
                  <TableCell colSpan={2}>
                    Keine Berechnung durchgeführt
                  </TableCell>
                </StyledTableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <Box mt={4}>
          <Tooltip title="Beitragsvorschau neu berechnen">
            <Button
              type="button"
              variant="contained"
              color="secondary"
              onClick={handlePreviewCalculation}
              startIcon={<RefreshIcon />}
            >
              Berechnen
            </Button>
          </Tooltip>
        </Box>

        {isFormDataChanged && (
          <Box
            mt={4}
            mb={4}
            p={2}
            textAlign="center"
            sx={{
              backgroundColor: 'yellow.100',
              color: 'warning.main',
              borderRadius: 1,
            }}
          >
            <Typography variant="body2" bgcolor="lightyellow" p={2}>
              Das Formular wurde angepasst. Bitte neu berechnen.
            </Typography>
          </Box>
        )}
      </Box>
    </CollapsibleBox>
  );
};

export default CalculationsBox;
