import {
    TableContainer,
    Table,
    TableBody,
    TableCell,
    TableRow,
    Paper,
    Tooltip,
    IconButton,
    styled,
    Button,
    Box,
    Typography,
} from "@mui/material";
import RefreshIcon from '@mui/icons-material/Refresh';
import CollapsibleBox from "./CollapsibleBox";
import { calculateInsuranceDaysCurrentYear, calculateInsuranceRuntime } from "@/utils/calculationUtil";

interface CalculationsBoxProps {
    calculationPreviewData: any;
    isVisible: boolean;
    setIsVisible: (visible: boolean) => void;
    handlePreviewCalculation: () => void;
    isFormDataChanged: boolean;
}

const CalculationsBox: React.FC<CalculationsBoxProps> = ({
    calculationPreviewData,
    isVisible,
    setIsVisible,
    handlePreviewCalculation,
    isFormDataChanged,
}) => {
    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0,
        },
    }));

    const formatEuro = (value: number) =>
        (Math.round(value * 100) / 100).toLocaleString('de', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }) + ' EUR';

    return (
        <CollapsibleBox
            title="Beitragsvorschau"
            isVisible={isVisible}
            setIsVisible={setIsVisible}
        >
            <Box width="100%">
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            {calculationPreviewData ? (
                                <>
                                    <Typography pl={1}>Gesamtlaufzeit</Typography>
                                    <StyledTableRow>
                                        <TableCell><strong>Netto:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.premie / 365 * calculateInsuranceRuntime(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                    <StyledTableRow>
                                        <TableCell><strong>Versicherungssteuer:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.tax / 365 * calculateInsuranceRuntime(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                    {calculationPreviewData.contract_type === "wohngebaeude" && calculationPreviewData.premie_glass_insurance && (
                                        <>
                                            <StyledTableRow>
                                                <TableCell><strong>Netto (Glas):</strong></TableCell>
                                                <TableCell>{formatEuro(calculationPreviewData.premie_glass_insurance / 365 * calculateInsuranceRuntime(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                            </StyledTableRow>
                                            <StyledTableRow>
                                                <TableCell><strong>Versicherungssteuer (Glas):</strong></TableCell>
                                                <TableCell>{formatEuro(calculationPreviewData.glass_tax / 365 * calculateInsuranceRuntime(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                            </StyledTableRow>
                                        </>
                                    )}
                                    <StyledTableRow>
                                        <TableCell><strong>Brutto:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.invoice_amount / 365 * calculateInsuranceRuntime(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                    <br/>
                                    <Typography pl={1}>Beitrag {new Date(calculationPreviewData.insurance_start_date).getFullYear()}</Typography>
                                    <StyledTableRow>
                                        <TableCell><strong>Netto:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.premie / 365 * calculateInsuranceDaysCurrentYear(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                    <StyledTableRow>
                                        <TableCell><strong>Versicherungssteuer:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.tax / 365 * calculateInsuranceDaysCurrentYear(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                    {calculationPreviewData.contract_type === "wohngebaeude" && calculationPreviewData.premie_glass_insurance && (
                                        <>
                                            <StyledTableRow>
                                                <TableCell><strong>Netto (Glas):</strong></TableCell>
                                                <TableCell>{formatEuro(calculationPreviewData.premie_glass_insurance / 365 * calculateInsuranceDaysCurrentYear(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                            </StyledTableRow>
                                            <StyledTableRow>
                                                <TableCell><strong>Versicherungssteuer (Glas):</strong></TableCell>
                                                <TableCell>{formatEuro(calculationPreviewData.glass_tax / 365 * calculateInsuranceDaysCurrentYear(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                            </StyledTableRow>
                                        </>
                                    )}
                                    <StyledTableRow>
                                        <TableCell><strong>Brutto:</strong></TableCell>
                                        <TableCell>{formatEuro(calculationPreviewData.invoice_amount / 365 * calculateInsuranceDaysCurrentYear(calculationPreviewData.insurance_start_date, calculationPreviewData.insurance_end_date))}</TableCell>
                                    </StyledTableRow>
                                </>
                            ) : (
                                <StyledTableRow>
                                    <TableCell colSpan={2}>Keine Berechnung durchgeführt</TableCell>
                                </StyledTableRow>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                <Box mt={4}>
                    <Tooltip title="Beitragsvorschau neu berechnen">
                        <Button
                            type="button"
                            variant="contained"
                            color="secondary"
                            onClick={handlePreviewCalculation}
                            startIcon={<RefreshIcon />}
                        >
                            Berechnen
                        </Button>
                    </Tooltip>
                </Box>

                {isFormDataChanged && (
                    <Box
                        mt={4}
                        mb={4}
                        p={2}
                        textAlign="center"
                        sx={{
                            backgroundColor: 'yellow.100',
                            color: 'warning.main',
                            borderRadius: 1,
                        }}
                    >
                        <Typography variant="body2" bgcolor='lightyellow' p={2}>
                            Das Formular wurde angepasst. Bitte neu berechnen.
                        </Typography>
                    </Box>
                )}
            </Box>
        </CollapsibleBox>
    );
};

export default CalculationsBox;
