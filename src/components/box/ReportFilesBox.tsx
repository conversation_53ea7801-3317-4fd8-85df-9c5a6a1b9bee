// src/components/box/ReportInformationBox.tsx
import { useState } from "react";
import { ReportData } from "@/types";
import CollapsibleBox from "./CollapsibleBox";
import { TableContainer, Paper, Table, TableBody, TableRow, TableCell, TableHead, styled, IconButton } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";

interface ReportFilesBoxProps {
    reportData: ReportData;
    attachments: any[];
}

const ReportFilesBox: React.FC<ReportFilesBoxProps> = ({ reportData, attachments }) => {
    const [customerDetailsIsVisible, setCustomerDetailsIsVisible] = useState(true);

    const handleDownloadAttachment = async (file_name: string, id: string) => {
        const response = await fetch(`/api/file/${reportData.contract_number}/get/${id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`,
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = file_name;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download attachment');
        }
    };

    return (
        <CollapsibleBox
            title="Dokumente"
            isVisible={customerDetailsIsVisible}
            setIsVisible={setCustomerDetailsIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Dateiname</TableCell>
                            <TableCell>Erstellt am</TableCell>
                            <TableCell align="right">Aktion</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {attachments.map((file, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    {file.bucket_path.replace(`report_files/${reportData.report_number}_`, "").length > 50
                                        ? `${file.bucket_path.replace(`report_files/${reportData.report_number}_`, "").slice(0, 50)}...`
                                        : file.bucket_path.replace(`report_files/${reportData.report_number}_`, "")}
                                </TableCell>
                                <TableCell>
                                    {new Date(file.createdAt).toLocaleDateString()}
                                </TableCell>
                                <TableCell align="right">
                                    <IconButton
                                        onClick={() => handleDownloadAttachment(file.bucket_path, file.documentId)}
                                        color="primary"
                                    >
                                        <DownloadIcon />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default ReportFilesBox;
