// src/components/box/ReportInformationBox.tsx
import DownloadIcon from '@mui/icons-material/Download';
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { useState } from 'react';

import { type Report } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';

import CollapsibleBox from './CollapsibleBox';

interface ReportFilesBoxProps {
  reportData: Report;
  attachments: any[];
}

const ReportFilesBox: React.FC<ReportFilesBoxProps> = ({
  reportData,
  attachments,
}) => {
  const [customerDetailsIsVisible, setCustomerDetailsIsVisible] =
    useState(true);

  const handleDownloadAttachment = async (id: string) => {
    const response = await apiFetch(
      `/api/reports/${reportData.reportNumber}/files/${id}`,
      {
        method: 'GET',
        raw: true,
      }
    );
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = id;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download attachment');
    }
  };

  return (
    <CollapsibleBox
      title="Dokumente"
      isVisible={customerDetailsIsVisible}
      setIsVisible={setCustomerDetailsIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Dateiname</TableCell>
              <TableCell>Erstellt am</TableCell>
              <TableCell align="right">Aktion</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {attachments.map((file, index) => (
              <TableRow key={index}>
                <TableCell>
                  {file.bucket_path.replace(
                    `report_files/${reportData.reportNumber}_`,
                    ''
                  ).length > 50
                    ? `${file.bucket_path.replace(`report_files/${reportData.reportNumber}_`, '').slice(0, 50)}...`
                    : file.bucket_path.replace(
                        `report_files/${reportData.reportNumber}_`,
                        ''
                      )}
                </TableCell>
                <TableCell>
                  {new Date(file.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    onClick={() => handleDownloadAttachment(file.bucket_path)}
                    color="primary"
                  >
                    <DownloadIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default ReportFilesBox;
