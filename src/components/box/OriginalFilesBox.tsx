// src/components/box/InvoiceBox.tsx
import { Download as DownloadIcon } from '@mui/icons-material';
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useState } from 'react';

import { formatDateString } from '@/utils/dateUtils';

import CollapsibleBox from './CollapsibleBox';

interface OriginalFilesBoxProps {
  originalAttachments: any[];
  contractNumber: string;
  fromOffer: string;
  visible?: boolean;
  handleDownloadAttachment: (
    path: string,
    documentId: string,
    contractNumber: string
  ) => void;
}

const OriginalFilesBox: React.FC<OriginalFilesBoxProps> = ({
  originalAttachments,
  contractNumber,
  fromOffer,
  visible,
  handleDownloadAttachment,
}) => {
  const [originalFilesIsVisible, setOriginalFilesIsVisible] = useState(
    visible || false
  );

  return (
    <CollapsibleBox
      title="Dokumente vom original Angebot"
      isVisible={originalFilesIsVisible}
      setIsVisible={setOriginalFilesIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Dateiname</TableCell>
              <TableCell>Erstellt am</TableCell>
              <TableCell>Zuletzt aktualisiert</TableCell>
              <TableCell align="right">Aktion</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {originalAttachments
              .filter((file) => file.type == 'contractAttachment')
              .map((attachment, index) => (
                <TableRow key={index}>
                  <TableCell>
                    {attachment.bucket_path.replace(`${contractNumber}_`, '')
                      .length > 50
                      ? `${attachment.bucket_path.replace(`${contractNumber}_`, '').slice(0, 50)}...`
                      : attachment.bucket_path.replace(
                          `${contractNumber}_`,
                          ''
                        )}
                  </TableCell>
                  <TableCell>
                    {formatDateString(attachment.createdAt)}
                  </TableCell>
                  <TableCell>
                    {formatDateString(attachment.updatedAt)}
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title={`Datei herunterladen`} placement="top">
                      <IconButton
                        onClick={() =>
                          handleDownloadAttachment(
                            attachment.bucket_path,
                            attachment.documentId,
                            fromOffer
                          )
                        }
                        color="primary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default OriginalFilesBox;
