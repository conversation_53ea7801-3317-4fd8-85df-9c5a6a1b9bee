// src/components/box/InvoiceBox.tsx
import { useState } from "react";

import { TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper, IconButton, Tooltip } from "@mui/material";
import { Download as DownloadIcon, Send as SendIcon, Autorenew as AutorenewIcon } from "@mui/icons-material";
import CollapsibleBox from "./CollapsibleBox";
import { formatDate } from "@/utils/dateUtils";

interface OriginalFilesBoxProps {
    originalAttachments: any[];
    contract_number: string;
    from_offer: string;
    visible?: boolean;
    handleDownloadAttachment: (path: string, documentId: string, contract_number: string) => void;
}

const OriginalFilesBox: React.FC<OriginalFilesBoxProps> = ({
    originalAttachments,
    contract_number,
    from_offer,
    visible,
    handleDownloadAttachment,
}) => {
    const [originalFilesIsVisible, setOriginalFilesIsVisible] = useState(visible || false);

    return (
        <CollapsibleBox
            title="Dokumente vom original Angebot"
            isVisible={originalFilesIsVisible}
            setIsVisible={setOriginalFilesIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Dateiname</TableCell>
                            <TableCell>Erstellt am</TableCell>
                            <TableCell>Zuletzt aktualisiert</TableCell>
                            <TableCell align="right">Aktion</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {originalAttachments.filter((file) => file.type == "contract_attachment").map((attachment, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    {attachment.bucket_path.replace(`${contract_number}_`, "").length > 50
                                        ? `${attachment.bucket_path.replace(`${contract_number}_`, "").slice(0, 50)}...`
                                        : attachment.bucket_path.replace(`${contract_number}_`, "")}
                                </TableCell>
                                <TableCell>{formatDate(attachment.createdAt)}</TableCell>
                                <TableCell>{formatDate(attachment.updatedAt)}</TableCell>
                                <TableCell align="right">

                                    <Tooltip title={`Datei herunterladen`} placement="top">
                                        <IconButton
                                            onClick={() => handleDownloadAttachment(attachment.bucket_path, attachment.documentId, from_offer)}
                                            color="primary"
                                        >
                                            <DownloadIcon />
                                        </IconButton>
                                    </Tooltip>

                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default OriginalFilesBox;
