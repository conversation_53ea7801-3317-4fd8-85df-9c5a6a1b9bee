// src/components/box/StaticBox.tsx
import { Box, Typography } from '@mui/material';
import { type ReactNode } from 'react';

interface StaticBoxProps {
  title: string;
  children: ReactNode;
}

const StaticBox: React.FC<StaticBoxProps> = ({ title, children }) => {
  return (
    <Box
      sx={{
        p: 3,
        border: 1,
        borderColor: 'divider',
        borderRadius: 1,
        backgroundColor: 'background.paper',
      }}
    >
      <Typography variant="h6" pb={1} color="primary">
        {title}
      </Typography>
      <Box display="flex" flexDirection="column" fontSize="0.875rem" pt={3}>
        {children}
      </Box>
    </Box>
  );
};

export default StaticBox;
