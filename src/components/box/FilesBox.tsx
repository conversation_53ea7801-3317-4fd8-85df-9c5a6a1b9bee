// src/components/box/FilesBox.tsx
import { useState } from "react";

import { TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper, IconButton, Tooltip } from "@mui/material";
import { Download as DownloadIcon, Send as SendIcon, Autorenew as AutorenewIcon } from "@mui/icons-material";
import CollapsibleBox from "./CollapsibleBox";
import { formatDate } from "@/utils/dateUtils";

interface FilesBoxProps {
    attachments: any[];
    contract_number: string;
    visible?: boolean;
    handleDownloadAttachment: (path: string, documentId: string, contract_number: string) => void;
}

const FilesBox: React.FC<FilesBoxProps> = ({
    attachments,
    contract_number,
    visible,
    handleDownloadAttachment,
}) => {
    const [filesIsVisible, setFilesIsVisible] = useState(visible || false);

    return (
        <CollapsibleBox
            title="Dokumente"
            isVisible={filesIsVisible}
            setIsVisible={setFilesIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Dateiname</TableCell>
                            <TableCell>Erstellt am</TableCell>
                            <TableCell>Zuletzt aktualisiert</TableCell>
                            <TableCell align="center">Aktion</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {attachments.filter((file) => file.type == "contract_attachment").map((attachment, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    {attachment.bucket_path.replace(`${contract_number}_`, "").length > 50
                                        ? `${attachment.bucket_path.replace(`${contract_number}_`, "").slice(0, 50)}...`
                                        : attachment.bucket_path.replace(`${contract_number}_`, "")}
                                </TableCell>
                                <TableCell>{formatDate(attachment.createdAt)}</TableCell>
                                <TableCell>{formatDate(attachment.updatedAt)}</TableCell>
                                <TableCell align="center">

                                    <Tooltip title="Datei herunterladen">
                                        <IconButton
                                            onClick={() => handleDownloadAttachment(attachment.bucket_path, attachment.documentId, contract_number)}
                                            color="primary"
                                        >
                                            <DownloadIcon />
                                        </IconButton>
                                    </Tooltip>

                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default FilesBox;
