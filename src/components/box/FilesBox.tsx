// src/components/box/FilesBox.tsx
import { Download as DownloadIcon } from '@mui/icons-material';
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useState } from 'react';

import { formatDateString } from '@/utils/dateUtils';

import CollapsibleBox from './CollapsibleBox';

interface FilesBoxProps {
  attachments: any[];
  contractNumber: string;
  visible?: boolean;
  handleDownloadAttachment: (
    path: string,
    documentId: string,
    contractNumber: string
  ) => void;
}

const FilesBox: React.FC<FilesBoxProps> = ({
  attachments,
  contractNumber,
  visible,
  handleDownloadAttachment,
}) => {
  const [filesIsVisible, setFilesIsVisible] = useState(visible || false);

  return (
    <CollapsibleBox
      title="Dokumente"
      isVisible={filesIsVisible}
      setIsVisible={setFilesIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Dateiname</TableCell>
              <TableCell>Erstellt am</TableCell>
              <TableCell>Zuletzt aktualisiert</TableCell>
              <TableCell align="center">Aktion</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {attachments
              .filter((file) => file.type == 'contractAttachment')
              .map((attachment, index) => (
                <TableRow key={index}>
                  <TableCell>
                    {attachment.bucket_path.replace(`${contractNumber}_`, '')
                      .length > 50
                      ? `${attachment.bucket_path.replace(`${contractNumber}_`, '').slice(0, 50)}...`
                      : attachment.bucket_path.replace(
                          `${contractNumber}_`,
                          ''
                        )}
                  </TableCell>
                  <TableCell>
                    {formatDateString(attachment.createdAt)}
                  </TableCell>
                  <TableCell>
                    {formatDateString(attachment.updatedAt)}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="Datei herunterladen">
                      <IconButton
                        onClick={() =>
                          handleDownloadAttachment(
                            attachment.bucket_path,
                            attachment.documentId,
                            contractNumber
                          )
                        }
                        color="primary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default FilesBox;
