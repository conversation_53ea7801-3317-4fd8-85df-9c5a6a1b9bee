// src/components/box/CollapsibleBox.tsx
import { ReactNode } from "react";
import { Collapse, IconButton, Tooltip, Box, Typography } from "@mui/material";
import { ChevronRight as ChevronRightIcon, Close as CloseIcon } from "@mui/icons-material";

interface CollapsibleBoxProps {
    title: string;
    children: ReactNode;
    isVisible: boolean;
    setIsVisible: (visible: boolean) => void;
}

const CollapsibleBox: React.FC<CollapsibleBoxProps> = ({ title, children, isVisible, setIsVisible }) => {
    const handleToggle = () => setIsVisible(!isVisible);

    return (
        <Box
            sx={{
                p: 3,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                position: 'relative',
                cursor: !isVisible ? 'pointer' : 'default',
                transition: 'background-color 0.3s',
                '&:hover': {
                    backgroundColor: !isVisible ? 'action.hover' : 'inherit',
                },
            }}
            onClick={!isVisible ? handleToggle : undefined}
        >
            <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                onClick={isVisible ? (e) => {
                    e.stopPropagation();
                    handleToggle();
                } : undefined}
                sx={{ cursor: 'pointer' }}
            >
                <Typography variant="h6" pb={1} color="primary" >
                    {title}
                </Typography>
                <Tooltip title={isVisible ? `${title} ausblenden` : `${title} einblenden`} placement="top">
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            handleToggle();
                        }}
                        color="primary"
                        size="small"
                    >
                        {isVisible ? <CloseIcon /> : <ChevronRightIcon />}
                    </IconButton>
                </Tooltip>
            </Box>
            <Collapse in={isVisible} timeout="auto">
                <Box
                    display="flex"
                    flexDirection="column"
                    fontSize="0.875rem"
                    pt={1}
                    onClick={(e) => e.stopPropagation()}
                >
                    {children}
                </Box>
            </Collapse>
        </Box>
    );
};

export default CollapsibleBox;
