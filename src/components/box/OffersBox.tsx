import React from "react";
import { useState } from "react";

import CollapsibleBox from "./CollapsibleBox";
import { formatLabel } from '../../utils/keyFormatter';
import { ContractData, CustomerData } from "../../types";
import { Button, Tooltip, Box, Grid2 as Grid } from "@mui/material";

interface OffersBoxProps {
    offers: ContractData[];
    customer: CustomerData;
    visible?: boolean;
    router: any;
}

const OffersBox: React.FC<OffersBoxProps> = ({
    offers,
    customer,
    visible,
    router
}) => {
    const [offerssIsVisible, setOfferssIsVisible] = useState(visible || false);

    const handleOfferClick = (contract_number: string, event: React.MouseEvent) => {
        const url = `/customer/${customer.customer_number}/offer/${contract_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    return (
        <CollapsibleBox
            title={"Angebote"}
            isVisible={offerssIsVisible}
            setIsVisible={setOfferssIsVisible}
        >
            <Box width="100%" pt={2}>
                <Grid container spacing={2} sx={{ direction: 'rtl' }}>
                    {offers.map((offer, index) => (
                        <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                            <Tooltip key={index} title={`${formatLabel(offer.contract_type)} Angebot öffnen`}>
                                <Button
                                    key={index}
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        handleOfferClick(offer.contract_number, event);
                                    }}
                                    variant='contained'
                                    sx={{ textTransform: 'none', fontSize: '0.875rem', height: '100%', width: '100%'  }}
                                >
                                    {formatLabel(offer.contract_type)}
                                    <br />
                                    {offer.contract_number}
                                </Button>
                            </Tooltip>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </CollapsibleBox>
    );
};

export default OffersBox;
