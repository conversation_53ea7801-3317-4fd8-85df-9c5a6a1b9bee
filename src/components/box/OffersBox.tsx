import { Box, Button, Grid, Tooltip } from '@mui/material';
import React from 'react';
import { useState } from 'react';

import { type Contract, type Customer } from '@/generated/prisma-postgres';
import { formatLabel } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface OffersBoxProps {
  offers: Contract[];
  customer: Customer;
  visible?: boolean;
  router: any;
}

const OffersBox: React.FC<OffersBoxProps> = ({
  offers,
  customer,
  visible,
  router,
}) => {
  const [offerssIsVisible, setOfferssIsVisible] = useState(visible || false);

  const handleOfferClick = (
    contract_number: string,
    event: React.MouseEvent
  ) => {
    const url = `/customer/${customer.customerNumber}/offer/${contract_number}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title={'Angebote'}
      isVisible={offerssIsVisible}
      setIsVisible={setOfferssIsVisible}
    >
      <Box width="100%" pt={2}>
        <Grid container spacing={2} sx={{ direction: 'rtl' }}>
          {offers.map((offer, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
              <Tooltip
                key={index}
                title={`${formatLabel(offer.contractType || '')} Angebot öffnen`}
              >
                <Button
                  key={index}
                  onClick={(event) => {
                    event.stopPropagation();
                    handleOfferClick(offer.contractNumber, event);
                  }}
                  variant="contained"
                  sx={{
                    textTransform: 'none',
                    fontSize: '0.875rem',
                    height: '100%',
                    width: '100%',
                  }}
                >
                  {formatLabel(offer.contractType || '')}
                  <br />
                  {offer.contractNumber}
                </Button>
              </Tooltip>
            </Grid>
          ))}
        </Grid>
      </Box>
    </CollapsibleBox>
  );
};

export default OffersBox;
