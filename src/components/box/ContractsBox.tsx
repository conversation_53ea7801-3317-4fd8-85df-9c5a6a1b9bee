import React, { useState } from "react";
import { useRouter } from 'next/router';
import { Button, Tooltip, Box, Grid2 as Grid } from "@mui/material";

import CollapsibleBox from "./CollapsibleBox";
import { formatLabel } from '../../utils/keyFormatter';
import { ContractData, CustomerData } from "../../types";

interface ContractsBoxProps {
    contracts: ContractData[];
    customer: CustomerData;
    visible?: boolean;
    router: any;
}

const ContractsBox: React.FC<ContractsBoxProps> = ({
    contracts,
    customer,
    visible,
    router
}) => {
    const [contractsIsVisible, setContractsIsVisible] = useState(visible || false);

    const handleContractClick = (contract_number: string, event: React.MouseEvent) => {
        const url = `/customer/${customer.customer_number}/contract/${contract_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    return (
        <CollapsibleBox
            title="Verträge"
            isVisible={contractsIsVisible}
            setIsVisible={setContractsIsVisible}
        >
            <Box width="100%" pt={2}>
                <Grid container spacing={2} sx={{ direction: 'rtl' }}>
                    {contracts.map((contract, index) => (
                        <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                            <Tooltip title={`${formatLabel(contract.contract_type)} Vertrag öffnen`}>
                                <Button
                                    fullWidth
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        handleContractClick(contract.contract_number, event);
                                    }}
                                    variant="contained"
                                    sx={{ textTransform: 'none', fontSize: '0.875rem', height: '100%', width: '100%'  }}
                                >
                                    {formatLabel(contract.contract_type)}
                                    <br />
                                    {contract.contract_number}
                                </Button>
                            </Tooltip>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </CollapsibleBox>
    );
};

export default ContractsBox;
