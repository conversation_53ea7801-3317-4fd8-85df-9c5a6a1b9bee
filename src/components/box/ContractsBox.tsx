import { Box, Button, Grid, Tooltip } from '@mui/material';
import React, { useState } from 'react';

import { type Contract, type Customer } from '@/generated/prisma-postgres';
import { formatLabel } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface ContractsBoxProps {
  contracts: Contract[];
  customer: Customer;
  visible?: boolean;
  router: any;
}

const ContractsBox: React.FC<ContractsBoxProps> = ({
  contracts,
  customer,
  visible,
  router,
}) => {
  const [contractsIsVisible, setContractsIsVisible] = useState(
    visible || false
  );

  const handleContractClick = (
    contract_number: string,
    event: React.MouseEvent
  ) => {
    const url = `/customer/${customer.customerNumber}/contract/${contract_number}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title="Verträge"
      isVisible={contractsIsVisible}
      setIsVisible={setContractsIsVisible}
    >
      <Box width="100%" pt={2}>
        <Grid container spacing={2} sx={{ direction: 'rtl' }}>
          {contracts.map((contract, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
              <Tooltip
                title={`${formatLabel(contract.contractType || '')} Vertrag öffnen`}
              >
                <Button
                  fullWidth
                  onClick={(event) => {
                    event.stopPropagation();
                    handleContractClick(contract.contractNumber, event);
                  }}
                  variant="contained"
                  sx={{
                    textTransform: 'none',
                    fontSize: '0.875rem',
                    height: '100%',
                    width: '100%',
                  }}
                >
                  {formatLabel(contract.contractType || '')}
                  <br />
                  {contract.contractNumber}
                </Button>
              </Tooltip>
            </Grid>
          ))}
        </Grid>
      </Box>
    </CollapsibleBox>
  );
};

export default ContractsBox;
