// src/components/box/MultiFormBox.tsx
import {Box, MenuItem, Select, IconButton, Tooltip, Stack} from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {useState} from "react";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";
import 'dayjs/locale/de'
import {FormConfig, FormState, FormItemsList} from "@/components/reportForms";

interface MultiFormBoxProps {
    formConfigs: FormConfig[];
    state: Record<string, FormState>;
    onChange: (formKey: string, patch: Partial<FormState>) => void;
    dirtyFlags: Record<string, boolean>;
}

const MultiFormBox: React.FC<MultiFormBoxProps> = ({ formConfigs, state, onChange, dirtyFlags }) => {
    const [activeIndex, setActiveIndex] = useState(0);
    const activeConfig = formConfigs[activeIndex];
    const activeKey = activeConfig.key
    const activeState = state[activeKey] || {}

    const handleStateChange = (patch: Partial<FormState>) => {
        onChange(activeKey, patch);
    }

    const switchToForm = (index: number) => {
        if (index < 0 || index >= formConfigs.length) {
            return console.error(`Cannot switch to the form at index ${index} because it is out of bounds`);
        }
        setActiveIndex(index)
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale='de'>
            <Box
                sx={{
                    p: 3,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    backgroundColor: 'background.paper',
                }}
            >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Select
                        value={activeConfig.key}
                        onChange={(e) =>
                            switchToForm(formConfigs.findIndex(f => f.key === e.target.value))
                        }
                        variant="standard"
                        size="small"
                        disableUnderline
                        IconComponent={ExpandMoreIcon} // <--- Hier wird das Icon ersetzt
                        sx={{
                            fontSize: '1.25rem',
                            fontWeight: 500,
                            color: 'primary.main',
                            paddingBottom: 0,
                            minWidth: 200,
                        }}
                    >
                        {formConfigs.map((form) => (
                            <MenuItem key={form.key} value={form.key}>
                                {form.title}
                            </MenuItem>
                        ))}
                    </Select>


                    {/* dots as navigator */}
                    <Box display="flex" gap={1}>
                        {formConfigs.map((form, i) => (
                            <Tooltip title={form.title} key={form.key}>
                                <Box
                                    onClick={() => switchToForm(i)}
                                    sx={(theme) => ({
                                        width: 18,
                                        height: 18,
                                        borderRadius: "50%",
                                        border: activeIndex === i
                                            ? `1px solid ${theme.palette.primary.main}`
                                            : `1px solid ${theme.palette.common.white}`,
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        cursor: "pointer",
                                    })}
                                >
                                    <Box
                                        sx={(theme) => ({
                                            width: 10,
                                            height: 10,
                                            borderRadius: "50%",
                                            backgroundColor: dirtyFlags[form.key]
                                                ? theme.palette.primary.main
                                                : theme.palette.grey[400],
                                            pointerEvents: "none",
                                        })}
                                    />
                                </Box>


                            </Tooltip>
                        ))}
                    </Box>
                </Box>

                <Box display="flex" alignItems="center" mt={3}>
                    <Box flex={1}>
                        <Stack gap={2}>
                            <FormItemsList items={activeConfig.items ?? []} state={activeState} onChange={handleStateChange} />
                        </Stack>
                    </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, alignItems: 'center', marginTop: 3 }}>
                    <IconButton disabled={activeIndex === 0} onClick={() => switchToForm(activeIndex - 1)}>
                        <ArrowBackIosNewIcon sx={{ position: 'relative', left: '-1px' }} />
                    </IconButton>

                    <IconButton disabled={activeIndex === formConfigs.length - 1} onClick={() => switchToForm(activeIndex + 1)}>
                        <ArrowForwardIosIcon sx={{ position: 'relative', right: '-1px' }} />
                    </IconButton>
                </Box>

            </Box>
        </LocalizationProvider>
    );
};

export default MultiFormBox;
