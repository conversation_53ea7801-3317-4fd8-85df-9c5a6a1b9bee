// src/components/box/InvoiceBox.tsx
import { useState } from "react";
import router from "next/router";

import { TableContainer, Table, TableRow, TableCell, TableBody, Paper, styled, TableHead, IconButton, Tooltip, Box } from "@mui/material";
import { Edit } from "@mui/icons-material";

import CollapsibleBox from "./CollapsibleBox";

import { InvoiceData } from "@/utils/invoice/types";
import { formatDate } from "@/utils/dateUtils";

interface InvoiceInformationBoxProps {
    invoiceData: InvoiceData;
    visible?: boolean;
}

const InvoiceInformationBox: React.FC<InvoiceInformationBoxProps> = ({
    invoiceData,
    visible
}) => {
    const [invoiceInformationIsVisible, setInvoiceInformationIsVisible] = useState(visible || false);

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0
        },
    }));

    function handleEditClick(invoice_number: string, event: React.MouseEvent): void {
        const url = `/invoice/edit/${invoice_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    }

    return (
        <CollapsibleBox
            title="Rechnungsinformationen"
            isVisible={invoiceInformationIsVisible}
            setIsVisible={setInvoiceInformationIsVisible}
        >
            <Box>
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            <StyledTableRow>
                                <TableCell><strong>Rechnungsnummer:</strong></TableCell>
                                <TableCell>{invoiceData.invoice_number}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Fälligkeitsdatum:</strong></TableCell>
                                <TableCell>{formatDate(invoiceData.due_date).split(',')[0]}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Rechnungsbetrag Netto:</strong></TableCell>
                                <TableCell>{invoiceData.total_net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} €</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Rechnungsbetrag Brutto:</strong></TableCell>
                                <TableCell>{invoiceData.total_gross.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} €</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Rechnugnsname (Kunde):</strong></TableCell>
                                <TableCell>
                                    {invoiceData.name_prefix && `${invoiceData.name_prefix} `}{invoiceData.first_name} {invoiceData.last_name}
                                </TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Rechnungsadresse/-Hausnummer:</strong></TableCell>
                                <TableCell>
                                    {invoiceData.billing_street} {invoiceData.billing_house_number}
                                </TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Rechnungspostleitzahl/-Ort:</strong></TableCell>
                                <TableCell>
                                    {invoiceData.billing_postal_code} {invoiceData.billing_city}
                                </TableCell>
                            </StyledTableRow>
                        </TableBody>
                    </Table>
                    <Box ml={2} mt={2}>
                        <h1><strong>Positionen:</strong></h1>
                        <Table>
                            <TableHead>
                                <StyledTableRow>
                                    <TableCell>
                                        Sparte
                                    </TableCell>
                                    <TableCell>
                                        Netto
                                    </TableCell>
                                    <TableCell>
                                        Steuer
                                    </TableCell>
                                    <TableCell>
                                        Brutto
                                    </TableCell>
                                </StyledTableRow>
                            </TableHead>
                            <TableBody>
                                {invoiceData.positions.map((position, index) =>
                                    <StyledTableRow key={index}>
                                        <TableCell>
                                            {position.name}
                                        </TableCell>
                                        <TableCell>
                                            {position.net.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} €
                                        </TableCell>
                                        <TableCell>
                                            {(position.tax * 100).toLocaleString('de')} %
                                        </TableCell>
                                        <TableCell>
                                            {(position.tax_amount + position.net).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} €
                                        </TableCell>
                                    </StyledTableRow>
                                )}
                            </TableBody>
                        </Table>
                    </Box>


                </TableContainer>
                <Box justifySelf='flex-end'>
                    <Tooltip title={`Rechnung bearbeiten`} placement="top">
                        <IconButton
                            onClick={(event) => handleEditClick(invoiceData.invoice_number, event)}
                        >
                            <Edit />
                        </IconButton>
                    </Tooltip>
                </Box>
            </Box>
        </CollapsibleBox>
    );
};

export default InvoiceInformationBox;
