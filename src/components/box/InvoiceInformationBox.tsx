// src/components/box/InvoiceBox.tsx
import { Edit } from '@mui/icons-material';
import {
  Box,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import router from 'next/router';
import { useState } from 'react';

import { type Invoice } from '@/generated/prisma-postgres';
import { formatDateString } from '@/utils/dateUtils';
import { type InvoicePositionData } from '@/utils/invoice/types';

import CollapsibleBox from './CollapsibleBox';

interface InvoiceInformationBoxProps {
  invoiceData: Invoice;
  visible?: boolean;
}

const InvoiceInformationBox: React.FC<InvoiceInformationBoxProps> = ({
  invoiceData,
  visible,
}) => {
  const [invoiceInformationIsVisible, setInvoiceInformationIsVisible] =
    useState(visible || false);

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  function handleEditClick(
    invoiceNumber: string,
    event: React.MouseEvent
  ): void {
    const url = `/invoice/${invoiceNumber}/edit`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  }

  return (
    <CollapsibleBox
      title="Rechnungsinformationen"
      isVisible={invoiceInformationIsVisible}
      setIsVisible={setInvoiceInformationIsVisible}
    >
      <Box>
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungsnummer:</strong>
                </TableCell>
                <TableCell>{invoiceData.invoiceNumber}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Fälligkeitsdatum:</strong>
                </TableCell>
                <TableCell>
                  {formatDateString(invoiceData.dueDate!).split(',')[0]}
                </TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungsbetrag Netto:</strong>
                </TableCell>
                <TableCell>
                  {invoiceData.totalNet!.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}{' '}
                  €
                </TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungsbetrag Brutto:</strong>
                </TableCell>
                <TableCell>
                  {invoiceData.totalGross!.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}{' '}
                  €
                </TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Rechnugnsname (Kunde):</strong>
                </TableCell>
                <TableCell>
                  {invoiceData.namePrefix && `${invoiceData.namePrefix} `}
                  {invoiceData.firstName} {invoiceData.lastName}
                </TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungsadresse/-Hausnummer:</strong>
                </TableCell>
                <TableCell>
                  {invoiceData.billingStreet} {invoiceData.billingHouseNumber}
                </TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungspostleitzahl/-Ort:</strong>
                </TableCell>
                <TableCell>
                  {invoiceData.billingPostalCode} {invoiceData.billingCity}
                </TableCell>
              </StyledTableRow>
            </TableBody>
          </Table>
          <Box ml={2} mt={2}>
            <h1>
              <strong>Positionen:</strong>
            </h1>
            <Table>
              <TableHead>
                <StyledTableRow>
                  <TableCell>Sparte</TableCell>
                  <TableCell>Netto</TableCell>
                  <TableCell>Steuer</TableCell>
                  <TableCell>Brutto</TableCell>
                </StyledTableRow>
              </TableHead>
              <TableBody>
                {(invoiceData.positions as InvoicePositionData[]).map(
                  (position, index) => (
                    <StyledTableRow key={index}>
                      <TableCell>{position.name}</TableCell>
                      <TableCell>
                        {position.net.toLocaleString('de', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}{' '}
                        €
                      </TableCell>
                      <TableCell>
                        {(position.tax * 100).toLocaleString('de')} %
                      </TableCell>
                      <TableCell>
                        {(position.tax_amount + position.net).toLocaleString(
                          'de',
                          { minimumFractionDigits: 2, maximumFractionDigits: 2 }
                        )}{' '}
                        €
                      </TableCell>
                    </StyledTableRow>
                  )
                )}
              </TableBody>
            </Table>
          </Box>
        </TableContainer>
        <Box justifySelf="flex-end">
          <Tooltip title={`Rechnung bearbeiten`} placement="top">
            <IconButton
              onClick={(event) =>
                handleEditClick(invoiceData.invoiceNumber, event)
              }
            >
              <Edit />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </CollapsibleBox>
  );
};

export default InvoiceInformationBox;
