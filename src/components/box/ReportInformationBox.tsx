// src/components/box/ReportInformationBox.tsx
import { Fragment, useState } from "react";
import { useRouter } from "next/router";
import { ReportData } from "@/types";
import CollapsibleBox from "./CollapsibleBox";
import { TableContainer, Paper, Table, TableBody, TableRow, TableCell, styled, Tooltip, Button, IconButton, Box, Typography } from "@mui/material";
import { Edit } from "@mui/icons-material";
import { formatReportLabel } from "@/utils/keyFormatter";


interface ReportInformationBoxProps {
    reportData: ReportData;
}

const ReportInformationBox: React.FC<ReportInformationBoxProps> = ({ reportData }) => {
    const [customerDetailsIsVisible, setCustomerDetailsIsVisible] = useState(true);
    const router = useRouter();

    const StyledTableRow = styled(TableRow)(() => ({
        td: {
            border: 0
        }
    }));

    const formatValue = (val: any) => {
        if (val == null || val === "") return "–";
        if (typeof val === "string" && /^\d{4}-\d{2}-\d{2}T/.test(val)) {
            return new Date(val).toLocaleString("de-DE");
        }
        if (typeof val === "boolean") {
            return (val as boolean)? "Ja" : "Nein"
        }
        return val.toString();
    };

    function handleEditClick(report_number: string, event: React.MouseEvent) {
        const url = `/report/edit/${report_number}`;
        if (event.ctrlKey || event.metaKey) {
            window.open(url, "_blank");
        } else {
            router.push(url);
        }
    }

    return (
        <CollapsibleBox
            title="Schadensinformationen"
            isVisible={customerDetailsIsVisible}
            setIsVisible={setCustomerDetailsIsVisible}
        >
            <Box>
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            {/* — your existing static fields — */}
                            {reportData.report_number && (
                                <StyledTableRow>
                                    <TableCell><strong>Schadennummer:</strong></TableCell>
                                    <TableCell>{reportData.report_number}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.external_report_number && (
                                <StyledTableRow>
                                    <TableCell><strong>Schadensnummer der Versicherung:</strong></TableCell>
                                    <TableCell>{reportData.external_report_number}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.damage_date && (
                                <StyledTableRow>
                                    <TableCell><strong>Schadendatum:</strong></TableCell>
                                    <TableCell>{reportData.damage_date}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.damage_location && (
                                <StyledTableRow>
                                    <TableCell><strong>Schadenort:</strong></TableCell>
                                    <TableCell>{reportData.damage_location}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.iban && (
                                <StyledTableRow>
                                    <TableCell><strong>IBAN:</strong></TableCell>
                                    <TableCell>{reportData.iban}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.coverd_risk && (
                                <StyledTableRow>
                                    <TableCell><strong>Versicherte Gefahr:</strong></TableCell>
                                    <TableCell>{reportData.coverd_risk}</TableCell>
                                </StyledTableRow>
                            )}
                            {reportData.text && (
                                <StyledTableRow>
                                    <TableCell><strong>Beschreibung:</strong></TableCell>
                                    <TableCell>{reportData.text}</TableCell>
                                </StyledTableRow>
                            )}
                            <StyledTableRow>
                                <TableCell><strong>Vertragsnummer:</strong></TableCell>
                                <TableCell>
                                    <Tooltip title="Vertrag anzeigen">
                                        <Button
                                            color="primary"
                                            variant="contained"
                                            onClick={() =>
                                                router.push(
                                                    `/customer/${reportData.customer_number}/contract/${reportData.contract_number}`
                                                )
                                            }
                                        >
                                            {reportData.contract_number}
                                        </Button>
                                    </Tooltip>
                                </TableCell>
                            </StyledTableRow>

                            {/* — dynamic data_raw sections — */}
                            {reportData.data_raw &&
                                Object.entries(reportData.data_raw).map(([sectionKey, sectionValue]) => {
                                    if (typeof sectionValue !== 'object' || sectionValue === null) return null;
                                    return (
                                        // Fragment avoids injecting a div
                                        <Fragment key={sectionKey}>
                                            {/* FormFieldsList header */}
                                            <StyledTableRow>
                                                <TableCell
                                                    colSpan={2}
                                                    sx={{
                                                        fontWeight: 'bold',
                                                        backgroundColor: (theme) => theme.palette.action.hover,
                                                        textTransform: 'capitalize',
                                                    }}
                                                >
                                                    {sectionKey.replace(/_/g, ' ')}
                                                </TableCell>
                                            </StyledTableRow>

                                            {/* FormFieldsList fields */}
                                            {Object.entries(sectionValue as Record<string, any>).map(
                                                ([fieldKey, fieldVal]) => (
                                                    <StyledTableRow key={fieldKey}>
                                                        <TableCell sx={{ fontWeight: 'bold' }}>
                                                            {formatReportLabel(fieldKey)}
                                                        </TableCell>
                                                        <TableCell>{formatValue(fieldVal)}</TableCell>
                                                    </StyledTableRow>
                                                )
                                            )}
                                        </Fragment>
                                    );
                                })}
                        </TableBody>
                    </Table>
                </TableContainer>

                {/* — edit button for admins — */}
                {localStorage.getItem("is_admin") === "true" && (
                    <Box display="flex" justifyContent="end" mt={1}>
                        <Tooltip title="Schaden bearbeiten." placement="top">
                            <IconButton
                                onClick={(event) =>
                                    handleEditClick(reportData.report_number, event)
                                }
                            >
                                <Edit />
                            </IconButton>
                        </Tooltip>
                    </Box>
                )}
            </Box>
        </CollapsibleBox>
    );
};

export default ReportInformationBox;
