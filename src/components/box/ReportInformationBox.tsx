// src/components/box/ReportInformationBox.tsx
import { Edit } from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useRouter } from 'next/router';
import { Fragment, useState } from 'react';

import { type Report } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';
import { formatReportLabel } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface ReportInformationBoxProps {
  reportData: Report;
}

const ReportInformationBox: React.FC<ReportInformationBoxProps> = ({
  reportData,
}) => {
  const [customerDetailsIsVisible, setCustomerDetailsIsVisible] =
    useState(true);
  const router = useRouter();
  const isAdmin = useIsAdmin();

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  const formatValue = (val: any) => {
    if (val == null || val === '') return '–';
    if (typeof val === 'string' && /^\d{4}-\d{2}-\d{2}T/.test(val)) {
      return new Date(val).toLocaleString('de-DE');
    }
    if (typeof val === 'boolean') {
      return val ? 'Ja' : 'Nein';
    }
    return val.toString();
  };

  function handleEditClick(reportNumber: string, event: React.MouseEvent) {
    const url = `/report/${reportNumber}/edit`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  }

  return (
    <CollapsibleBox
      title="Schadensinformationen"
      isVisible={customerDetailsIsVisible}
      setIsVisible={setCustomerDetailsIsVisible}
    >
      <Box>
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              {/* — static fields — */}
              {reportData.reportNumber && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Schadennummer:</strong>
                  </TableCell>
                  <TableCell>{reportData.reportNumber}</TableCell>
                </StyledTableRow>
              )}
              {reportData.externalReportNumber && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Schadensnummer der Versicherung:</strong>
                  </TableCell>
                  <TableCell>{reportData.externalReportNumber}</TableCell>
                </StyledTableRow>
              )}
              {reportData.damageDate && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Schadendatum:</strong>
                  </TableCell>
                  <TableCell>{reportData.damageDate}</TableCell>
                </StyledTableRow>
              )}
              {reportData.damageLocation && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Schadenort:</strong>
                  </TableCell>
                  <TableCell>{reportData.damageLocation}</TableCell>
                </StyledTableRow>
              )}
              {reportData.iban && (
                <StyledTableRow>
                  <TableCell>
                    <strong>IBAN:</strong>
                  </TableCell>
                  <TableCell>{reportData.iban}</TableCell>
                </StyledTableRow>
              )}
              {reportData.coveredRisk && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Versicherte Gefahr:</strong>
                  </TableCell>
                  <TableCell>{reportData.coveredRisk}</TableCell>
                </StyledTableRow>
              )}
              {reportData.text && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Beschreibung:</strong>
                  </TableCell>
                  <TableCell>{reportData.text}</TableCell>
                </StyledTableRow>
              )}
              <StyledTableRow>
                <TableCell>
                  <strong>Vertragsnummer:</strong>
                </TableCell>
                <TableCell>
                  <Tooltip title="Vertrag anzeigen">
                    <Button
                      color="primary"
                      variant="contained"
                      onClick={() =>
                        router.push(
                          `/customer/${reportData.customerNumber}/contract/${reportData.contractNumber}`
                        )
                      }
                    >
                      {reportData.contractNumber}
                    </Button>
                  </Tooltip>
                </TableCell>
              </StyledTableRow>

              {/* — dynamic dataRaw sections — */}
              {reportData.dataRaw &&
                Object.entries(reportData.dataRaw).map(
                  ([sectionKey, sectionValue]) => {
                    if (
                      typeof sectionValue !== 'object' ||
                      sectionValue === null
                    )
                      return null;
                    return (
                      <Fragment key={sectionKey}>
                        {/* Section header */}
                        <StyledTableRow>
                          <TableCell
                            colSpan={2}
                            sx={{
                              fontWeight: 'bold',
                              backgroundColor: (theme) =>
                                theme.palette.action.hover,
                              textTransform: 'capitalize',
                            }}
                          >
                            {sectionKey.replace(/_/g, ' ')}
                          </TableCell>
                        </StyledTableRow>

                        {/* Section fields */}
                        {Object.entries(
                          sectionValue as Record<string, any>
                        ).map(([fieldKey, fieldVal]) => (
                          <StyledTableRow key={fieldKey}>
                            <TableCell sx={{ fontWeight: 'bold' }}>
                              {formatReportLabel(fieldKey)}
                            </TableCell>
                            <TableCell>{formatValue(fieldVal)}</TableCell>
                          </StyledTableRow>
                        ))}
                      </Fragment>
                    );
                  }
                )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* — edit button for admins — */}
        {isAdmin && (
          <Box display="flex" justifyContent="end" mt={1}>
            <Tooltip title="Schaden bearbeiten." placement="top">
              <IconButton
                onClick={(event) =>
                  handleEditClick(reportData.reportNumber, event)
                }
              >
                <Edit />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    </CollapsibleBox>
  );
};

export default ReportInformationBox;
