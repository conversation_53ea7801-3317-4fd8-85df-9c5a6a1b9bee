// src/components/box/StatusBox.tsx
import { useRouter } from "next/router";
import { useState } from "react";

import { TableContainer, Table, TableBody, TableCell, TableRow, styled, Paper, Button, Tooltip } from "@mui/material";
import CollapsibleBox from "./CollapsibleBox";

import { InvoiceData, InvoiceStatusType } from "@/utils/invoice/types";
import { formatInvoiceAgentStatus, formatInvoiceCustomerStatus, formatInvoiceInsuranceStatus, formatInvoiceStatus } from "@/utils/keyFormatter";
import { formatDate } from "@/utils/dateUtils";

interface InvoiceStatusBoxProps {
    invoiceData: InvoiceData;
    visible?: boolean;
}

const InvoiceStatusBox: React.FC<InvoiceStatusBoxProps> = ({ invoiceData, visible }) => {
    const [invoiceStatusIsVisible, setInvoiceStatusIsVisible] = useState(visible || false);

    const router = useRouter();
    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0
        },
    }));

    return (
        <CollapsibleBox
            title={"Rechnungsstatus"}
            isVisible={invoiceStatusIsVisible}
            setIsVisible={setInvoiceStatusIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableBody>
                        <StyledTableRow>
                            <TableCell><strong>Allgemeiner Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceStatus(invoiceData.invoice_status)}
                            </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                            <TableCell><strong>Kunden Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceCustomerStatus(invoiceData.customer_status)}
                            </TableCell>
                        </StyledTableRow>
                        {/*<StyledTableRow>
                            <TableCell><strong>Makler Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceAgentStatus(invoiceData.agent_status)}
                            </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                            <TableCell><strong> Versicherungs Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceInsuranceStatus(invoiceData.insurance_status)}
                            </TableCell>
                        </StyledTableRow> */}
                        {Object.entries(invoiceData.invoice_detail_status)
                            .filter(([key, value]) =>
                                key !== "status" &&
                                key !== "customer_status" &&
                                key !== "agent_status" &&
                                key !== "insurance_status" &&
                                key !== "partially_payment_amount" &&
                                value !== "")
                            .map(([key, value]) => (
                                <StyledTableRow key={key}>
                                    <TableCell>{key}:</TableCell>
                                    <TableCell>{formatDate(value)}</TableCell>
                                </StyledTableRow>
                            ))
                        }

                        <StyledTableRow>
                            <TableCell><strong>Erstellungsdatum:</strong></TableCell>
                            <TableCell>
                                {formatDate(invoiceData.createdAt)}
                            </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                            <TableCell><strong>Aktuallisierungsdatum:</strong></TableCell>
                            <TableCell>
                                {formatDate(invoiceData.updatedAt)}
                            </TableCell>
                        </StyledTableRow>

                        <StyledTableRow>
                            <TableCell><strong>Vertrag:</strong></TableCell>
                            <TableCell>
                                <Tooltip title="Vertrag anzeigen" placement="top">
                                    <Button
                                        color="primary"
                                        onClick={() => router.push(`/customer/${invoiceData.customer_number}/contract/${invoiceData.contract_number}`)}
                                        variant="contained">
                                        {invoiceData.contract_number}
                                    </Button>
                                </Tooltip>
                            </TableCell>
                        </StyledTableRow>

                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default InvoiceStatusBox;
