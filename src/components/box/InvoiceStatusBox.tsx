// src/components/box/StatusBox.tsx
import {
  Button,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useState } from 'react';

import { type Invoice } from '@/generated/prisma-postgres';
import { formatDate, formatDateString } from '@/utils/dateUtils';
import { type InvoiceDetailStatusData } from '@/utils/invoice/types';
import {
  formatInvoiceCustomerStatus,
  formatInvoiceStatus,
} from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface InvoiceStatusBoxProps {
  invoiceData: Invoice;
  visible?: boolean;
}

const InvoiceStatusBox: React.FC<InvoiceStatusBoxProps> = ({
  invoiceData,
  visible,
}) => {
  const [invoiceStatusIsVisible, setInvoiceStatusIsVisible] = useState(
    visible || false
  );

  const router = useRouter();
  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  return (
    <CollapsibleBox
      title={'Rechnungsstatus'}
      isVisible={invoiceStatusIsVisible}
      setIsVisible={setInvoiceStatusIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableBody>
            <StyledTableRow>
              <TableCell>
                <strong>Allgemeiner Status:</strong>
              </TableCell>
              <TableCell>
                {formatInvoiceStatus(invoiceData.invoiceStatus!)}
              </TableCell>
            </StyledTableRow>
            <StyledTableRow>
              <TableCell>
                <strong>Kunden Status:</strong>
              </TableCell>
              <TableCell>
                {formatInvoiceCustomerStatus(invoiceData.customerStatus!)}
              </TableCell>
            </StyledTableRow>
            {/*<StyledTableRow>
                            <TableCell><strong>Makler Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceAgentStatus(invoiceData.agentStatus)}
                            </TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                            <TableCell><strong> Versicherungs Status:</strong></TableCell>
                            <TableCell>
                                {formatInvoiceInsuranceStatus(invoiceData.insuranceStatus)}
                            </TableCell>
                        </StyledTableRow> */}
            {Object.entries(
              invoiceData.invoiceDetailStatus! as InvoiceDetailStatusData
            )
              .filter(
                ([key, value]) =>
                  key !== 'status' &&
                  key !== 'customer_status' &&
                  key !== 'agent_status' &&
                  key !== 'insurance_status' &&
                  key !== 'partially_payment_amount' &&
                  value !== ''
              )
              .map(([key, value]) => (
                <StyledTableRow key={key}>
                  <TableCell>{key}:</TableCell>
                  <TableCell>{formatDateString(value as string)}</TableCell>
                </StyledTableRow>
              ))}

            <StyledTableRow>
              <TableCell>
                <strong>Erstellungsdatum:</strong>
              </TableCell>
              <TableCell>{formatDate(invoiceData.createdAt!)}</TableCell>
            </StyledTableRow>
            <StyledTableRow>
              <TableCell>
                <strong>Aktuallisierungsdatum:</strong>
              </TableCell>
              <TableCell>{formatDate(invoiceData.updatedAt!)}</TableCell>
            </StyledTableRow>

            <StyledTableRow>
              <TableCell>
                <strong>Vertrag:</strong>
              </TableCell>
              <TableCell>
                <Tooltip title="Vertrag anzeigen" placement="top">
                  <Button
                    color="primary"
                    onClick={() =>
                      router.push(`/contract/${invoiceData.contractNumber}`)
                    }
                    variant="contained"
                  >
                    {invoiceData.contractNumber}
                  </Button>
                </Tooltip>
              </TableCell>
            </StyledTableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default InvoiceStatusBox;
