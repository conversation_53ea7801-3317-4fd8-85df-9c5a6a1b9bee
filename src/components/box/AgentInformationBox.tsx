import { AgentData } from "@/types";
import router from "next/router";
import { useState, useEffect } from "react";
import {
    TableContainer,
    Paper,
    Table,
    TableBody,
    TableRow,
    TableCell,
    IconButton,
    styled,
    Tooltip,
    CircularProgress,
    Box,
} from "@mui/material";
import { Edit as EditIcon, Info as InfoIcon } from "@mui/icons-material";
import CollapsibleBox from "./CollapsibleBox";

interface AgentInformationBoxProps {
    agent_number: string;
    edit_button?: boolean;
    details_button?: boolean;
    visible?: boolean;
    simplified?: boolean;
}

const AgentInformationBox: React.FC<AgentInformationBoxProps> = ({
    agent_number,
    edit_button,
    details_button,
    visible,
    simplified,
}) => {
    const [agent, setAgent] = useState<AgentData | null>(null);
    const [loading, setLoading] = useState(true);
    const [agentInformationIsVisible, setAgentInformationIsVisible] = useState(visible || false);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || "";
        const fetchData = async () => {
            try {
                const agentResponse = await fetch(`/api/agent/${agent_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                if (!agentResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const agentData = await agentResponse.json();
                setAgent(agentData);
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [agent_number]);

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0,
        },
    }));

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" mt={4}>
                <CircularProgress />
            </Box>
        );
    }

    if (!agent) {
        return <Box>Fehler beim Laden der Maklerdetails.</Box>;
    }

    const handleEditClick = (event: React.MouseEvent) => {
        const url = `/agent/edit/${agent.agent_number}`;
        if (event.ctrlKey || event.metaKey) {
            window.open(url, "_blank");
        } else {
            router.push(url);
        }
    };

    const handleDetailsClick = (event: React.MouseEvent) => {
        const url = `/agent/${agent.agent_number}`;
        if (event.ctrlKey || event.metaKey) {
            window.open(url, "_blank");
        } else {
            router.push(url);
        }
    };

    return (
        <CollapsibleBox
            title="Maklerinformationen"
            isVisible={agentInformationIsVisible}
            setIsVisible={setAgentInformationIsVisible}
        >
            <Box width="100%">
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            <StyledTableRow>
                                <TableCell><strong>Maklernummer:</strong></TableCell>
                                <TableCell>{agent.agent_number}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Agenturnummer:</strong></TableCell>
                                <TableCell>{agent.agency_number}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Benutzername:</strong></TableCell>
                                <TableCell>{agent.username}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Firmenname:</strong></TableCell>
                                <TableCell>{agent.company_name}</TableCell>
                            </StyledTableRow>

                            {!simplified && <>
                                <StyledTableRow>
                                    <TableCell><strong>Adresse/ Hausnummer:</strong></TableCell>
                                    <TableCell>{agent.street} {agent.house_number}</TableCell>
                                </StyledTableRow>

                                <StyledTableRow>
                                    <TableCell><strong>Postleitzahl/ Ort:</strong></TableCell>
                                    <TableCell>{agent.postal_code} {agent.city}</TableCell>
                                </StyledTableRow>

                                <StyledTableRow>
                                    <TableCell><strong>Email:</strong></TableCell>
                                    <TableCell>{agent.email}</TableCell>
                                </StyledTableRow>

                                <StyledTableRow>
                                    <TableCell><strong>Webseite:</strong></TableCell>
                                    <TableCell>{agent.url}</TableCell>
                                </StyledTableRow>

                                <StyledTableRow>
                                    <TableCell><strong>Telefonnummer:</strong></TableCell>
                                    <TableCell>{agent.telephone_number}</TableCell>
                                </StyledTableRow>

                                <StyledTableRow>
                                    <TableCell><strong>Courtage:</strong></TableCell>
                                    <TableCell>{agent.commission * 100}%</TableCell>
                                </StyledTableRow>
                            </>}
                        </TableBody>
                    </Table>
                </TableContainer>

                {details_button && (
                    <Box display="flex" justifyContent="flex-end" mt={1}>
                        <Tooltip title={`Maklerdetails für ${agent.username} öffnen`} placement="top">
                            <IconButton onClick={handleDetailsClick}>
                                <InfoIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                )}

                {edit_button && (
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                        <Tooltip title={`Maklerdetails für ${agent.username} anpassen`} placement="top">
                            <IconButton onClick={handleEditClick}>
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                )}
            </Box>
        </CollapsibleBox>
    );
};

export default AgentInformationBox;
