import { Edit as EditIcon, Info as InfoIcon } from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  IconButton,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import router from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { type Agent } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';

import CollapsibleBox from './CollapsibleBox';

interface AgentInformationBoxProps {
  agentNumber: string;
  editButton?: boolean;
  detailsButton?: boolean;
  visible?: boolean;
  simplified?: boolean;
}

const AgentInformationBox: React.FC<AgentInformationBoxProps> = ({
  agentNumber,
  editButton,
  detailsButton,
  visible,
  simplified,
}) => {
  const [agent, setAgent] = useState<Agent | null>(null);
  const [loading, setLoading] = useState(true);
  const [agentInformationIsVisible, setAgentInformationIsVisible] = useState(
    visible || false
  );

  const { data: session } = useSession();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const agentData = await apiFetch<Agent>(`/api/agents/${agentNumber}`, {
          accessToken: session?.accessToken,
        });
        setAgent(agentData);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [agentNumber, session?.accessToken]);

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (!agent) {
    return <Box>Fehler beim Laden der Maklerdetails.</Box>;
  }

  const handleEditClick = (event: React.MouseEvent) => {
    const url = `/agent/edit/${agent.agentNumber}`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleDetailsClick = (event: React.MouseEvent) => {
    const url = `/agent/${agent.agentNumber}`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title="Maklerinformationen"
      isVisible={agentInformationIsVisible}
      setIsVisible={setAgentInformationIsVisible}
    >
      <Box width="100%">
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              <StyledTableRow>
                <TableCell>
                  <strong>Maklernummer:</strong>
                </TableCell>
                <TableCell>{agent.agentNumber}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Agenturnummer:</strong>
                </TableCell>
                <TableCell>{agent.agencyNumber}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Benutzername:</strong>
                </TableCell>
                <TableCell>{agent.username}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Firmenname:</strong>
                </TableCell>
                <TableCell>{agent.companyName}</TableCell>
              </StyledTableRow>

              {!simplified && (
                <>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Adresse/ Hausnummer:</strong>
                    </TableCell>
                    <TableCell>
                      {agent.street} {agent.houseNumber}
                    </TableCell>
                  </StyledTableRow>

                  <StyledTableRow>
                    <TableCell>
                      <strong>Postleitzahl/ Ort:</strong>
                    </TableCell>
                    <TableCell>
                      {agent.postalCode} {agent.city}
                    </TableCell>
                  </StyledTableRow>

                  <StyledTableRow>
                    <TableCell>
                      <strong>Email:</strong>
                    </TableCell>
                    <TableCell>{agent.email}</TableCell>
                  </StyledTableRow>

                  <StyledTableRow>
                    <TableCell>
                      <strong>Webseite:</strong>
                    </TableCell>
                    <TableCell>{agent.url}</TableCell>
                  </StyledTableRow>

                  <StyledTableRow>
                    <TableCell>
                      <strong>Telefonnummer:</strong>
                    </TableCell>
                    <TableCell>{agent.telephoneNumber}</TableCell>
                  </StyledTableRow>

                  <StyledTableRow>
                    <TableCell>
                      <strong>Courtage:</strong>
                    </TableCell>
                    <TableCell>{(agent.commission || 0) * 100}%</TableCell>
                  </StyledTableRow>
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {detailsButton && (
          <Box display="flex" justifyContent="flex-end" mt={1}>
            <Tooltip
              title={`Maklerdetails für ${agent.username} öffnen`}
              placement="top"
            >
              <IconButton onClick={handleDetailsClick}>
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {editButton && (
          <Box display="flex" justifyContent="flex-end" mt={2}>
            <Tooltip
              title={`Maklerdetails für ${agent.username} anpassen`}
              placement="top"
            >
              <IconButton onClick={handleEditClick}>
                <EditIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    </CollapsibleBox>
  );
};

export default AgentInformationBox;
