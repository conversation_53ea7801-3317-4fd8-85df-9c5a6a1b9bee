// src/components/box/ContractInformationBox.tsx
import React, { ReactNode, useState } from "react";
import {
    TableContainer,
    Paper,
    Table,
    TableBody,
    TableRow,
    TableCell,
    IconButton,
    Tooltip,
    styled,
    Box,
    Grid2 as Grid,
    Typography,
    Stack
} from "@mui/material";
import { Edit as EditIcon, Info as InfoIcon } from "@mui/icons-material";

import CollapsibleBox from "./CollapsibleBox";
import { ContractData, RiskAddressData, AccidentInsuranceFormData } from "../../types";
import { formatLabel } from '../../utils/keyFormatter';
import { calculateInsuranceDaysCurrentYear } from "@/utils/calculationUtil";

interface ContractInformationBoxProps {
    contract: ContractData;
    isOffer: boolean;
    router: any;
    details_button?: boolean;
    visible?: boolean;
    simplified?: boolean;
}

const StyledTableRow = styled(TableRow)(() => ({
    '& td': {
        borderBottom: 0,
    },
}));

const renderTableRow = (label: string, value?: ReactNode) =>
    value !== undefined && value !== "" ? (
        <StyledTableRow>
            <TableCell sx={{ verticalAlign: 'top' }}><strong>{label}:</strong></TableCell>
            <TableCell>{typeof value === "boolean" ? (value ? "Ja" : "Nein") : value}</TableCell>
        </StyledTableRow>
    ) : null;

function generateRiskAddressFields(risks: RiskAddressData[]): ReactNode {
    return risks.map((r, idx) => (
        <Box key={idx} mb={2}>
            <Stack direction="row" gap={2}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Straße:</strong>
                <p>{r.street}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Hausnummer:</strong>
                <p>{r.house_number}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>PLZ:</strong>
                <p>{r.postal_code}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Ort:</strong>
                <p>{r.city}</p>
            </Stack>
            {(r.unit !== "" && r.unit !== undefined) && (
                <Stack direction="row" gap={2} mt={1}>
                    <strong style={{ minWidth: '80px', display: 'inline-block' }}>Einheit:</strong>
                    <p>{r.unit}</p>
                </Stack>
            )}
        </Box>
    ));
}

function generateInsuredPersonFields(persons: AccidentInsuranceFormData[]): ReactNode {
    return persons.map((r, idx) => (
        <Box key={idx} mb={2}>
            <Stack direction="row" gap={2}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Vorname:</strong>
                <p>{r.first_name}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Nachname:</strong>
                <p>{r.last_name}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Geburtstag:</strong>
                <p>{r.birth_date}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Typ:</strong>
                <p>{r.type}</p>
            </Stack>
        </Box>
    ));
}

function generateAnimalFields(animals: { animal_type: string; animal_name: string; race?: string }[]): ReactNode {
    return animals.map((a, idx) => (
        <Box key={idx} mb={2}>
            <Stack direction="row" gap={2}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Tierart:</strong>
                <p>{a.animal_type}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Name:</strong>
                <p>{a.animal_name}</p>
            </Stack>
            {a.animal_type === "Hund" && (
                <Stack direction="row" gap={2} mt={1}>
                    <strong style={{ minWidth: '80px', display: 'inline-block' }}>Rasse:</strong>
                    <p>{a.race}</p>
                </Stack>
            )}
        </Box>
    ));
}

function generateShareFields(shares: { share_type: string; percentage: number; insurance?: string }[]): ReactNode {
    return shares.map((s, idx) => (
        <Box key={idx} mb={2}>
            <Stack direction="row" gap={2}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Teilhaber:</strong>
                <p>{s.share_type}</p>
            </Stack>
            <Stack direction="row" gap={2} mt={1}>
                <strong style={{ minWidth: '80px', display: 'inline-block' }}>Wert:</strong>
                <p>{s.percentage}</p>
            </Stack>
            {s.share_type === "Makler" && s.insurance && (
                <Stack direction="row" gap={2} mt={1}>
                    <strong style={{ minWidth: '80px', display: 'inline-block' }}>Versicherung:</strong>
                    <p>{s.insurance}</p>
                </Stack>
            )}
        </Box>
    ));
}

const ContractInformationBox: React.FC<ContractInformationBoxProps> = ({
    contract,
    isOffer,
    router,
    details_button,
    visible,
    simplified
}) => {
    const [contractInformationIsVisible, setContractInformationIsVisible] = useState(visible || false);

    const handleEditClick = (event: React.MouseEvent) => {
        const url = `/customer/${contract.customer_number}/${contract.is_offer ? "editOffer" : "editContract"}/${contract.contract_number}`;
        if (event.ctrlKey || event.metaKey) {
            window.open(url, "_blank")
        } else {
            router.push(url);
        }
    };

    const handleDetailsClick = (event: React.MouseEvent) => {
        const url = `/customer/${contract.customer_number}/contract/${contract.contract_number}`;
        if (event.ctrlKey || event.metaKey) {
            window.open(url, "_blank")
        } else {
            router.push(url);
        }
    };

    return (
        <CollapsibleBox
            title={isOffer ? "Angebotsinformationen" : "Vertragsinformationen"}
            isVisible={contractInformationIsVisible}
            setIsVisible={setContractInformationIsVisible}
        >
            <Box width="100%">
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            {renderTableRow(isOffer ? "Angebotsnummer" : "Vertragsnummer", contract.contract_number)}
                            {renderTableRow("Vertragsart", formatLabel(contract.contract_type))}
                            {renderTableRow("Versicherungsbeginn", new Date(contract.insurance_start_date).toLocaleDateString())}
                            {renderTableRow("Versicherungsablauf", new Date(contract.insurance_end_date).toLocaleDateString())}

                            {!simplified && (
                                <>
                                    {renderTableRow("Zahlungsweise", contract.payment_mode)}
                                    {renderTableRow("IBAN", contract.iban)}
                                    {renderTableRow("BIC", contract.bic)}
                                    {renderTableRow("Vorversicherung", contract.previous_insurance)}
                                    {renderTableRow("Nr. Vorversicherer", contract.previous_insurance_number)}

                                    {(contract.contract_type === 'hausrat' || contract.contract_type === 'wohngebaeude') && (
                                        <>
                                            {renderTableRow("Bauart", contract.building_type)}
                                            {renderTableRow("Ständig bewohnt", contract.is_permanently_occupied)}
                                        </>
                                    )}

                                    {renderTableRow("Versicherungssumme", contract.insurance_sum ? `${contract.insurance_sum} EUR` : undefined)}

                                    {["hausrat", "wohngebaeude", "gebaeudeversicherung", "geschaeftsversicherung"].includes(contract.contract_type) && (
                                        <>
                                            {renderTableRow("Elementar", contract.is_elementar)}
                                            {renderTableRow("Zürs Zone", contract.zuers_zone)}
                                        </>
                                    )}

                                    {contract.risk_addresses[0].city && renderTableRow("Risikoadresse", generateRiskAddressFields(contract.risk_addresses))}
                                    {contract.insured_persons && renderTableRow("Versicherte Personen", generateInsuredPersonFields(contract.insured_persons))}
                                    {contract.animal_data && contract.contract_type === "tierhalterhaftpflicht" && renderTableRow("Tiere", generateAnimalFields(contract.animal_data))}
                                    {contract.share_data && renderTableRow("Anteile", generateShareFields(contract.share_data))}

                                    {renderTableRow("Deckungssumme", contract.coverage_amount ? `EUR ${parseInt(contract.coverage_amount).toLocaleString('de')}` : undefined)}
                                    {renderTableRow("Objektart", contract.object_type)}
                                    {renderTableRow("Tarifgruppe", contract.tariff_group)}
                                    {renderTableRow("Betriebsart", contract.business_type)}
                                    {renderTableRow("Wohnfläche", contract.living_area ? `${contract.living_area} m²` : undefined)}

                                    {(contract.contract_type === 'wohngebaeude' || contract.contract_type === 'gebaeudeversicherung') && (
                                        <>
                                            {renderTableRow("Baujahr", contract.construction_year)}
                                            {renderTableRow("Baujahr bekannt?", !contract.is_construction_year_unknown)}
                                        </>
                                    )}

                                    {contract.contract_type === "privathaftpflicht" && renderTableRow("Familien-Deckung", contract.family_coverage)}
                                    {renderTableRow("Individuelle Berechnung", contract.is_individually_calculated)}
                                    {renderTableRow("Nettobetrag", contract.premie ? `${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR` : undefined)}
                                    {renderTableRow("Versicherungssteuer", contract.tax ? `${(Math.round(contract.tax! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR` : undefined)}
                                    {renderTableRow("Bruttobetrag", contract.invoice_amount ? `${(Math.round(contract.invoice_amount! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR` : undefined)}
                                    {renderTableRow("Bausumme", contract.building_sum ? `${contract.building_sum} EUR` : undefined)}
                                    {renderTableRow("Zusatzvereinbarungen", contract.additional_agreements)}
                                </>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                {details_button && (
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                        <Tooltip title={`Details für ${isOffer ? "Angebot" : "Police"} ${contract.contract_number} öffnen`} placement="top">
                            <IconButton onClick={handleDetailsClick}>
                                <InfoIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                )}

                {(isOffer || localStorage.getItem("is_admin") === "true") && !simplified && (
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                        <Tooltip title={`${isOffer ? "Angebot" : "Police"} anpassen`} placement="top">
                            <IconButton onClick={handleEditClick}>
                                <EditIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                )}
            </Box>
        </CollapsibleBox>
    );
};

export default ContractInformationBox;
