// src/components/box/ContractInformationBox.tsx
import { Edit as EditIcon, Info as InfoIcon } from '@mui/icons-material';
import {
  Box,
  IconButton,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import React, { type ReactNode, useState } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import {
  type AccidentInsuranceFormData,
  type AnimalData,
  type RiskAddressData,
} from '@/types';
import { useIsAdmin } from '@/utils/authUtils';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { formatLabel } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface ContractInformationBoxProps {
  contract: Contract;
  isOffer: boolean;
  router: any;
  detailsButton?: boolean;
  visible?: boolean;
  simplified?: boolean;
}

const StyledTableRow = styled(TableRow)(() => ({
  '& td': {
    borderBottom: 0,
  },
}));

const renderTableRow = (label: string, value?: ReactNode) =>
  value !== undefined && value !== '' ? (
    <StyledTableRow>
      <TableCell sx={{ verticalAlign: 'top' }}>
        <strong>{label}:</strong>
      </TableCell>
      <TableCell>
        {typeof value === 'boolean' ? (value ? 'Ja' : 'Nein') : value}
      </TableCell>
    </StyledTableRow>
  ) : null;

function generateRiskAddressFields(risks: RiskAddressData[]): ReactNode {
  return risks.map((r, idx) => (
    <Box key={idx} mb={2}>
      <Stack direction="row" gap={2}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Straße:
        </strong>
        <p>{r.street}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Hausnummer:
        </strong>
        <p>{r.house_number}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          PLZ:
        </strong>
        <p>{r.postal_code}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Ort:
        </strong>
        <p>{r.city}</p>
      </Stack>
      {r.unit !== '' && r.unit !== undefined && (
        <Stack direction="row" gap={2} mt={1}>
          <strong style={{ minWidth: '80px', display: 'inline-block' }}>
            Einheit:
          </strong>
          <p>{r.unit}</p>
        </Stack>
      )}
    </Box>
  ));
}

function generateInsuredPersonFields(
  persons: AccidentInsuranceFormData[]
): ReactNode {
  return persons.map((r, idx) => (
    <Box key={idx} mb={2}>
      <Stack direction="row" gap={2}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Vorname:
        </strong>
        <p>{r.first_name}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Nachname:
        </strong>
        <p>{r.last_name}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Geburtstag:
        </strong>
        <p>{r.birth_date}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Typ:
        </strong>
        <p>{r.type}</p>
      </Stack>
    </Box>
  ));
}

function generateAnimalFields(
  animals: { animal_type: string; animal_name: string; race?: string }[]
): ReactNode {
  return animals.map((a, idx) => (
    <Box key={idx} mb={2}>
      <Stack direction="row" gap={2}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Tierart:
        </strong>
        <p>{a.animal_type}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Name:
        </strong>
        <p>{a.animal_name}</p>
      </Stack>
      {a.animal_type === 'Hund' && (
        <Stack direction="row" gap={2} mt={1}>
          <strong style={{ minWidth: '80px', display: 'inline-block' }}>
            Rasse:
          </strong>
          <p>{a.race}</p>
        </Stack>
      )}
    </Box>
  ));
}

export type ShareData = {
  share_type: string;
  percentage: number;
  insurance?: string;
};

function generateShareFields(
  shares: { share_type: string; percentage: number; insurance?: string }[]
): ReactNode {
  return shares.map((s, idx) => (
    <Box key={idx} mb={2}>
      <Stack direction="row" gap={2}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Teilhaber:
        </strong>
        <p>{s.share_type}</p>
      </Stack>
      <Stack direction="row" gap={2} mt={1}>
        <strong style={{ minWidth: '80px', display: 'inline-block' }}>
          Wert:
        </strong>
        <p>{s.percentage}</p>
      </Stack>
      {s.share_type === 'Makler' && s.insurance && (
        <Stack direction="row" gap={2} mt={1}>
          <strong style={{ minWidth: '80px', display: 'inline-block' }}>
            Versicherung:
          </strong>
          <p>{s.insurance}</p>
        </Stack>
      )}
    </Box>
  ));
}

const ContractInformationBox: React.FC<ContractInformationBoxProps> = ({
  contract,
  isOffer,
  router,
  detailsButton,
  visible,
  simplified,
}) => {
  const [contractInformationIsVisible, setContractInformationIsVisible] =
    useState(visible || false);
  const isAdmin = useIsAdmin();

  const handleEditClick = (event: React.MouseEvent) => {
    const url = `/contract/${contract.contractNumber}/${contract.isOffer ? 'editOffer' : 'editContract'}`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleDetailsClick = (event: React.MouseEvent) => {
    const url = `/customer/${contract.customerNumber}/contract/${contract.contractNumber}`;
    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title={isOffer ? 'Angebotsinformationen' : 'Vertragsinformationen'}
      isVisible={contractInformationIsVisible}
      setIsVisible={setContractInformationIsVisible}
    >
      <Box width="100%">
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              {renderTableRow(
                isOffer ? 'Angebotsnummer' : 'Vertragsnummer',
                contract.contractNumber
              )}
              {renderTableRow(
                'Vertragsart',
                formatLabel(contract.contractType!)
              )}
              {renderTableRow(
                'Versicherungsbeginn',
                new Date(contract.insuranceStartDate!).toLocaleDateString()
              )}
              {renderTableRow(
                'Versicherungsablauf',
                new Date(contract.insuranceEndDate!).toLocaleDateString()
              )}

              {!simplified && (
                <>
                  {renderTableRow('Zahlungsweise', contract.paymentMode)}
                  {renderTableRow('IBAN', contract.iban)}
                  {renderTableRow('BIC', contract.bic)}
                  {renderTableRow(
                    'Vorversicherung',
                    contract.previousInsurance
                  )}
                  {renderTableRow(
                    'Nr. Vorversicherer',
                    contract.previousInsuranceNumber
                  )}

                  {(contract.contractType === 'hausrat' ||
                    contract.contractType === 'wohngebaeude') && (
                    <>
                      {renderTableRow('Bauart', contract.buildingType)}
                      {renderTableRow(
                        'Ständig bewohnt',
                        contract.isPermanentlyOccupied
                      )}
                    </>
                  )}

                  {renderTableRow(
                    'Versicherungssumme',
                    contract.insuranceSum
                      ? `${contract.insuranceSum} EUR`
                      : undefined
                  )}

                  {[
                    'hausrat',
                    'wohngebaeude',
                    'gebaeudeversicherung',
                    'geschaeftsversicherung',
                  ].includes(contract.contractType!) && (
                    <>
                      {renderTableRow('Elementar', contract.isElementar)}
                      {renderTableRow('Zürs Zone', contract.zuersZone)}
                    </>
                  )}

                  {(contract.riskAddresses as RiskAddressData[])[0]!.city &&
                    renderTableRow(
                      'Risikoadresse',
                      generateRiskAddressFields(
                        contract.riskAddresses as RiskAddressData[]
                      )
                    )}
                  {(contract.insuredPersons as AccidentInsuranceFormData[]) &&
                    renderTableRow(
                      'Versicherte Personen',
                      generateInsuredPersonFields(
                        contract.insuredPersons as AccidentInsuranceFormData[]
                      )
                    )}
                  {contract.animalData &&
                    contract.contractType === 'tierhalterhaftpflicht' &&
                    renderTableRow(
                      'Tiere',
                      generateAnimalFields(
                        contract.animalData as unknown as AnimalData[]
                      )
                    )}
                  {contract.shareData &&
                    renderTableRow(
                      'Anteile',
                      generateShareFields(
                        contract.shareData as unknown as ShareData[]
                      )
                    )}

                  {renderTableRow(
                    'Deckungssumme',
                    contract.coverageAmount
                      ? `EUR ${parseInt(contract.coverageAmount).toLocaleString('de')}`
                      : undefined
                  )}
                  {renderTableRow('Objektart', contract.objectType)}
                  {renderTableRow('Tarifgruppe', contract.tariffGroup)}
                  {renderTableRow('Betriebsart', contract.businessType)}
                  {renderTableRow(
                    'Wohnfläche',
                    contract.livingArea
                      ? `${contract.livingArea} m²`
                      : undefined
                  )}

                  {(contract.contractType === 'wohngebaeude' ||
                    contract.contractType === 'gebaeudeversicherung') && (
                    <>
                      {renderTableRow('Baujahr', contract.constructionYear)}
                      {renderTableRow(
                        'Baujahr bekannt?',
                        !contract.isConstructionYearUnknown
                      )}
                    </>
                  )}

                  {contract.contractType === 'privathaftpflicht' &&
                    renderTableRow('Familien-Deckung', contract.familyCoverage)}
                  {renderTableRow(
                    'Individuelle Berechnung',
                    contract.isIndividuallyCalculated
                  )}
                  {renderTableRow(
                    'Nettobetrag',
                    contract.premie
                      ? `${(Math.round((contract.premie! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
                      : undefined
                  )}
                  {renderTableRow(
                    'Versicherungssteuer',
                    contract.tax
                      ? `${(Math.round((contract.tax! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
                      : undefined
                  )}
                  {renderTableRow(
                    'Bruttobetrag',
                    contract.invoiceAmount!
                      ? `${(Math.round((contract.invoiceAmount! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
                      : undefined
                  )}
                  {renderTableRow(
                    'Bausumme',
                    contract.buildingSum
                      ? `${contract.buildingSum} EUR`
                      : undefined
                  )}
                  {renderTableRow(
                    'Zusatzvereinbarungen',
                    contract.additionalAgreements
                  )}
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {detailsButton && (
          <Box display="flex" justifyContent="flex-end" mt={2}>
            <Tooltip
              title={`Details für ${isOffer ? 'Angebot' : 'Police'} ${contract.contractNumber} öffnen`}
              placement="top"
            >
              <IconButton onClick={handleDetailsClick}>
                <InfoIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {(isOffer || isAdmin) && !simplified && (
          <Box display="flex" justifyContent="flex-end" mt={2}>
            <Tooltip
              title={`${isOffer ? 'Angebot' : 'Police'} anpassen`}
              placement="top"
            >
              <IconButton onClick={handleEditClick}>
                <EditIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    </CollapsibleBox>
  );
};

export default ContractInformationBox;
