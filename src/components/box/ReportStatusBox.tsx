import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import { Stack, Tooltip, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { type FC, type ReactNode, useEffect, useState } from 'react';

import { type ReportPrecheck, ReportPrecheckStatus } from '@/types';
import { apiFetch } from '@/utils/apiFetch';

import CollapsibleBox from './CollapsibleBox';

interface ReportStatusBoxProps {
  visible?: boolean;
}

const ReportStatusBox: FC<ReportStatusBoxProps> = ({
  visible: isVisibleByDefault = false,
}) => {
  const router = useRouter();
  const { reportNumber } = router.query;

  const [isVisible, setIsVisible] = useState<boolean>(isVisibleByDefault);
  const [prechecks, setPrechecks] = useState<ReportPrecheck[]>([]);

  useEffect(() => {
    if (!reportNumber) return;

    const fetchData = async () => {
      try {
        const reportResponse = await apiFetch(
          `/api/report/${reportNumber}/prechecks`,
          {
            method: 'GET',
            raw: true,
          }
        );

        if (!reportResponse.ok) throw new Error('Failed to fetch precheck');

        const loadedPrechecks: ReportPrecheck[] = await reportResponse.json();
        setPrechecks(loadedPrechecks);
      } catch (error) {
        console.error(error);
      }
    };

    fetchData();
  }, [reportNumber]);

  const createItemClickHandler = (index: number) => async () => {
    const currentPrecheck = prechecks[index];
    const newPrecheck: ReportPrecheck = {
      ...currentPrecheck,
      status: resolveStatusAfterClick(currentPrecheck.status),
    };

    try {
      const response = await apiFetch(
        `/api/report/${reportNumber}/prechecks/${currentPrecheck.id}`,
        {
          method: 'PUT',
          raw: true,
          body: JSON.stringify(newPrecheck),
        }
      );

      if (!response.ok) throw new Error('Failed to toggle precheck status');

      const updatedPrecheck: ReportPrecheck = await response.json();
      setPrechecks((prevState) =>
        prevState.toSpliced(index, 1, updatedPrecheck)
      );
    } catch (error) {
      console.error(error);
    }
  };

  const sortedPrechecks = prechecks.sort((a, b) => a.index - b.index);

  return (
    <CollapsibleBox
      title="Vorprüfung"
      isVisible={isVisible}
      setIsVisible={setIsVisible}
    >
      {sortedPrechecks.length === 0 && (
        <Typography
          sx={{ marginTop: '20px', fontSize: '16px', fontWeight: '700' }}
        >
          Noch keine Vorprüfungen vorhanden
        </Typography>
      )}
      {sortedPrechecks.length > 0 && (
        <Stack spacing={2} sx={{ padding: '15px 0' }}>
          {sortedPrechecks.map((itemProps, index) => {
            return (
              <Item
                key={itemProps.id}
                {...itemProps}
                onClick={createItemClickHandler(index)}
              />
            );
          })}
        </Stack>
      )}
    </CollapsibleBox>
  );
};

export default ReportStatusBox;

type ItemProps = ReportPrecheck & {
  onClick?: () => Promise<void>;
};
const Item: FC<ItemProps> = ({ title, status, tooltip, onClick }) => {
  return (
    <Tooltip title={tooltip} placement="top-start">
      <Stack
        direction="row"
        spacing={1}
        onClick={onClick}
        sx={{
          '&:hover': {
            cursor: 'pointer',
          },
        }}
      >
        {iconMap[status]}
        <Typography sx={{ fontWeight: 'bold' }}>{title}</Typography>
      </Stack>
    </Tooltip>
  );
};

const iconMap: Record<ReportPrecheckStatus, ReactNode> = {
  [ReportPrecheckStatus.EMPTY]: <RadioButtonUncheckedIcon color="primary" />,
  [ReportPrecheckStatus.CHECKED]: <CheckCircleOutlineIcon color="primary" />,
  [ReportPrecheckStatus.DECLINED]: 'Not implemented',
};

const resolveStatusAfterClick = (current: ReportPrecheckStatus) => {
  if (current === ReportPrecheckStatus.CHECKED)
    return ReportPrecheckStatus.EMPTY;
  if (current === ReportPrecheckStatus.EMPTY)
    return ReportPrecheckStatus.CHECKED;

  return ReportPrecheckStatus.DECLINED;
};
