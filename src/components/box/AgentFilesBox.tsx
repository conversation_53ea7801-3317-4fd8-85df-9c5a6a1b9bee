// src/components/box/AgentFilesBox.tsx
import DownloadIcon from '@mui/icons-material/Download';
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { useState } from 'react';

import { type AttachmentData } from '@/types';
import { formatDateString } from '@/utils/dateUtils';

import CollapsibleBox from './CollapsibleBox';

interface AgentFilesBoxProps {
  attachments: AttachmentData[];
  agent_number: string;
  visible?: boolean;
  handleDownloadAttachment: (path: string, documentId: string) => void;
}

const AgentFilesBox: React.FC<AgentFilesBoxProps> = ({
  agent_number,
  attachments,
  visible,
  handleDownloadAttachment,
}) => {
  const [agentFilesIsVisible, setAgentFilesIsVisible] = useState(
    visible || false
  );

  return (
    <CollapsibleBox
      title="Dokumente"
      isVisible={agentFilesIsVisible}
      setIsVisible={setAgentFilesIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Dateiname</TableCell>
              <TableCell>Erstellt am</TableCell>
              <TableCell>Zuletzt aktualisiert</TableCell>
              <TableCell align="center">Aktion</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {attachments.map((attachment, index) => (
              <TableRow key={index}>
                <TableCell>
                  {attachment.bucket_path.replace(
                    `agent_files/${agent_number}_`,
                    ''
                  ).length > 50
                    ? `${attachment.bucket_path.replace(`agent_files/${agent_number}_`, '').slice(0, 50)}...`
                    : attachment.bucket_path.replace(
                        `agent_files/${agent_number}_`,
                        ''
                      )}
                </TableCell>
                <TableCell>{formatDateString(attachment.createdAt)}</TableCell>
                <TableCell>{formatDateString(attachment.updatedAt)}</TableCell>
                <TableCell align="center">
                  <IconButton
                    onClick={() =>
                      handleDownloadAttachment(
                        attachment.bucket_path,
                        attachment.documentId
                      )
                    }
                    color="primary"
                  >
                    <DownloadIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default AgentFilesBox;
