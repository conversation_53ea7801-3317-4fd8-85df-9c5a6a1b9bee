// src/components/box/AgentFilesBox.tsx
import { useState } from "react";

import { TableContainer, Paper, Table, TableBody, TableRow, TableCell, TableHead, IconButton } from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import CollapsibleBox from "./CollapsibleBox";
import { formatDate } from '@/utils/dateUtils';
import { AttachmentData } from '../../types';

interface AgentFilesBoxProps {
    attachments: AttachmentData[];
    agent_number: string;
    visible?: boolean;
    handleDownloadAttachment: (path: string, documentId: string) => void;
}

const AgentFilesBox: React.FC<AgentFilesBoxProps> = ({
    agent_number,
    attachments,
    visible,
    handleDownloadAttachment,
}) => {
    const [agentFilesIsVisible, setAgentFilesIsVisible] = useState(visible || false);

    return (
        <CollapsibleBox
            title="Dokumente"
            isVisible={agentFilesIsVisible}
            setIsVisible={setAgentFilesIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Dateiname</TableCell>
                            <TableCell>Erstellt am</TableCell>
                            <TableCell>Zuletzt aktualisiert</TableCell>
                            <TableCell align="center">Aktion</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {attachments.map((attachment, index) => (
                            <TableRow key={index}>
                                <TableCell>
                                    {attachment.bucket_path.replace(`agent_files/${agent_number}_`, "").length > 50
                                        ? `${attachment.bucket_path.replace(`agent_files/${agent_number}_`, "").slice(0, 50)}...`
                                        : attachment.bucket_path.replace(`agent_files/${agent_number}_`, "")}
                                </TableCell>
                                <TableCell>{formatDate(attachment.createdAt)}</TableCell>
                                <TableCell>{formatDate(attachment.updatedAt)}</TableCell>
                                <TableCell align="center">

                                    <IconButton
                                        onClick={() => handleDownloadAttachment(attachment.bucket_path, attachment.documentId)}
                                        color="primary"
                                    >
                                        <DownloadIcon />
                                    </IconButton>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default AgentFilesBox;
