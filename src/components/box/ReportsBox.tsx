// src/components/box/ReportsBox.tsx
import { Edit as EditIcon } from '@mui/icons-material';
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useState } from 'react';

import { formatDateString } from '@/utils/dateUtils';

import CollapsibleBox from './CollapsibleBox';

interface ReportsBoxProps {
  reports: any[];
  attachments: any[];
  contract_number: string;
  visible?: boolean;
}
interface Report {
  id: number;
  report_number: string;
  customer_number: string;
  contract_number: string;
  agency_number: string;
  damage_date: string;
}

const ReportsBox: React.FC<ReportsBoxProps> = ({ reports, visible }) => {
  const [reportsIsVisible, setReportsIsVisible] = useState(visible || false);

  const router = useRouter();

  const handleDetailsClick = (report: Report, event: React.MouseEvent) => {
    const url = `/report/${report.report_number}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleEditClick = (report_number: string, event: React.MouseEvent) => {
    const url = `/report/edit/${report_number}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  return (
    <CollapsibleBox
      title="Schadensmeldungen"
      isVisible={reportsIsVisible}
      setIsVisible={setReportsIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Berichtnummer</TableCell>
              <TableCell>Erstellt am</TableCell>
              <TableCell align="right">Aktion</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {reports.map((report, index) => (
              <TableRow
                key={index}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={(event) => handleDetailsClick(report, event)}
              >
                <TableCell>{report.report_number}</TableCell>
                <TableCell>{formatDateString(report.createdAt)}</TableCell>
                <TableCell align="right">
                  <Tooltip title="Bearbeiten">
                    <IconButton
                      onClick={(event) => {
                        event.stopPropagation();
                        handleEditClick(report.report_number, event);
                      }}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default ReportsBox;
