// src/components/box/InvoiceBox.tsx
import {
  Download as DownloadIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from '@mui/material';
import router from 'next/router';
import { useState } from 'react';

import { type Invoice } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { formatDate } from '@/utils/dateUtils';
import { type InvoiceStatusType } from '@/utils/invoice/types';
import { formatInvoiceStatus } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface InvoicesBoxProps {
  contractNumber: string;
  invoices: Invoice[];
  visible?: boolean;
  handleSendMail: (type: string, path: string) => void;
}

const InvoicesBox: React.FC<InvoicesBoxProps> = ({
  contractNumber,
  invoices,
  visible,
  handleSendMail,
}) => {
  const [invoicesIsVisible, setInvoicesIsVisible] = useState(visible || false);

  const handleDownloadInvoice = async (invoiceNumber: string) => {
    const response = await apiFetch(`/api/invoices/${invoiceNumber}/file`, {
      method: 'GET',
      raw: true,
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = invoiceNumber;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download attachment');
    }
  };

  const handleDetailsClick = (
    invoiceNumber: string,
    event: React.MouseEvent
  ) => {
    const url = `/invoice/${invoiceNumber}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  function handleInvoiceCreation(): void {
    router.push(`/invoice/${contractNumber}/create`);
  }

  return (
    <CollapsibleBox
      title="Rechnungen"
      isVisible={invoicesIsVisible}
      setIsVisible={setInvoicesIsVisible}
    >
      <Box>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Rechnungsnummer</TableCell>
                <TableCell>Fälligkeitsdatum</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Aktion</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoice, index) => (
                <TableRow
                  key={index}
                  hover
                  sx={{ cursor: 'pointer' }}
                  onClick={(event) =>
                    handleDetailsClick(invoice.invoiceNumber, event)
                  }
                >
                  <TableCell>{invoice.invoiceNumber}</TableCell>
                  <TableCell>
                    {formatDate(invoice.updatedAt).split(',')[0]}
                  </TableCell>
                  <TableCell>
                    {formatInvoiceStatus(
                      invoice.invoiceStatus as InvoiceStatusType
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="Herunterladen" placement="top">
                      <IconButton
                        onClick={(event) => {
                          event.stopPropagation();
                          handleDownloadInvoice(invoice.invoiceNumber);
                        }}
                        color="primary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Versenden" placement="top">
                      <IconButton
                        onClick={(event) => {
                          event.stopPropagation();
                          handleSendMail('invoice', invoice.invoiceNumber);
                        }}
                        color="primary"
                      >
                        <SendIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box display="flex" justifyContent="end" marginTop={2}>
          <Button
            type="submit"
            color="secondary"
            variant="contained"
            onClick={() => handleInvoiceCreation()}
          >
            Rechnung erstellen
          </Button>
        </Box>
      </Box>
    </CollapsibleBox>
  );
};

export default InvoicesBox;
