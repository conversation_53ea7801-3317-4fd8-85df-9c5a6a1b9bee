// src/components/box/InvoiceBox.tsx
import { useState } from "react";
import router from "next/router";

import { TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper, IconButton, Tooltip, Button, Box } from "@mui/material";
import { Download as DownloadIcon, Send as SendIcon } from "@mui/icons-material";

import CollapsibleBox from "./CollapsibleBox";
import { InvoiceData } from "@/utils/invoice/types";
import { formatInvoiceStatus } from "@/utils/keyFormatter";
import { formatDate } from "@/utils/dateUtils";

interface InvoicesBoxProps {
    contractNumber: string;
    invoices: InvoiceData[];
    visible?: boolean;
    handleSendMail: (type: string, path: string) => void;
}

const InvoicesBox: React.FC<InvoicesBoxProps> = ({
    contractNumber,
    invoices,
    visible,
    handleSendMail,
}) => {
    const [invoicesIsVisible, setInvoicesIsVisible] = useState(visible || false);

    const handleDownloadInvoice = async (file_name: string, documentId: string) => {
        const response = await fetch(`/api/invoice/download/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`,
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = file_name;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download attachment');
        }
    };

    const handleDetailsClick = (invoiceNumber: string, event: React.MouseEvent) => {
        const url = `/invoice/${invoiceNumber}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    function handleInvoiceCreation(): void {
        router.push(`/invoice/create/${contractNumber}`)
    }

    return (
        <CollapsibleBox
            title="Rechnungen"
            isVisible={invoicesIsVisible}
            setIsVisible={setInvoicesIsVisible}
        >
            <Box>
                <TableContainer component={Paper}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Rechnungsnummer</TableCell>
                                <TableCell>Fälligkeitsdatum</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell align="right" >Aktion</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {invoices.map((invoice, index) => (
                                <TableRow
                                    key={index}
                                    hover
                                    sx={{ cursor: 'pointer' }}
                                    onClick={(event) => handleDetailsClick(invoice.invoice_number, event)}
                                >
                                    <TableCell>{invoice.invoice_number}</TableCell>
                                    <TableCell>{formatDate(invoice.updatedAt).split(",")[0]}</TableCell>
                                    <TableCell>{formatInvoiceStatus(invoice.invoice_status)}</TableCell>
                                    <TableCell align="right">
                                        <Tooltip title="Herunterladen" placement="top">
                                            <IconButton
                                                onClick={(event) => {
                                                    event.stopPropagation();
                                                    handleDownloadInvoice(invoice.invoice_number, invoice.documentId);
                                                }}
                                                color="primary">
                                                <DownloadIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Versenden" placement="top">
                                            <IconButton
                                                onClick={(event) => {
                                                    event.stopPropagation();
                                                    handleSendMail("invoice", invoice.invoice_number);
                                                }}
                                                color="primary">
                                                <SendIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
                <Box display='flex' justifyContent='end' marginTop={2}>
                    <Button
                        type="submit"
                        color="secondary"
                        variant="contained"
                        onClick={() => handleInvoiceCreation()}>
                        Rechnung erstellen
                    </Button>
                </Box>
            </Box>
        </CollapsibleBox>
    );
};

export default InvoicesBox;
