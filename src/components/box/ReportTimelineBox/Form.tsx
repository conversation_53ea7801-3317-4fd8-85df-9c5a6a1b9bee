import AttachFileIcon from '@mui/icons-material/AttachFile';
import CloseIcon from '@mui/icons-material/Close';
import SendOutlinedIcon from '@mui/icons-material/SendOutlined';
import {
  Button,
  List,
  ListItem,
  ListItemIcon,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import React, { type ChangeEvent, type FC, useState } from 'react';

import { VisuallyHiddenInput } from '@/components/common';
import {
  PrecheckAuthorType,
  type ReportTimelineEntry,
  ReportTimelineEntryType,
} from '@/generated/prisma-postgres';

const transformToMarkdown = (text: string) => {
  return text.replaceAll('\n', '\n\n');
};

interface FormProps {
  onSuccessfulSubmit: (newEntry: ReportTimelineEntry, files?: File[]) => void;
}
export const Form: FC<FormProps> = ({ onSuccessfulSubmit }) => {
  const router = useRouter();
  const reportNumber = router.query.reportNumber as string;

  const [text, setText] = useState<string>('');
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const handleFileAttached = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      // Convert FileList to a real array of File
      const newFiles = Array.from(event.target.files);
      // TODO: should we validate anything about the files, like size or type or the count?
      setAttachedFiles((prev) => [...prev, ...newFiles]);
    }
  };
  const handleRemoveFileClick = (index: number) => () => {
    setAttachedFiles((prev) => prev.toSpliced(index, 1));
  };

  const handleSubmit = async () => {
    const [rawTitle, ...contentLines] = text.trim().split('\n');
    const title = transformToMarkdown(rawTitle);
    const content = transformToMarkdown(contentLines.join('\n'));

    if (attachedFiles.length) {
      onSuccessfulSubmit(
        {
          id: 0,
          entryType: ReportTimelineEntryType.DOCUMENTS,
          reportNumber,
          authorType: PrecheckAuthorType.AGENT,
          authorId: '',
          timestamp: new Date().toISOString(),
          title,
          content,
          files: null,
          documentId: '',
          metadata: null,
          relatedEntries: null,
          reportId: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        attachedFiles
      );
    } else {
      onSuccessfulSubmit({
        id: 0,
        entryType: ReportTimelineEntryType.COMMENT,
        reportNumber,
        authorType: PrecheckAuthorType.AGENT,
        authorId: '',
        timestamp: new Date().toISOString(),
        title,
        content,
        documentId: '',
        files: null,
        metadata: null,
        relatedEntries: null,
        reportId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    setText('');
    setAttachedFiles([]);
  };

  const isSubmitButtonDisabled = !text && !attachedFiles.length;
  const buttons = (
    <>
      <Button
        component="label"
        sx={{ padding: '8px', minWidth: 'unset', alignSelf: 'end' }}
      >
        <AttachFileIcon color="primary" sx={{ fontSize: '25px' }} />
        <VisuallyHiddenInput
          type="file"
          multiple
          onChange={handleFileAttached}
        />
      </Button>
      <Button
        disabled={isSubmitButtonDisabled}
        onClick={handleSubmit}
        sx={{ padding: '8px', minWidth: 'unset', alignSelf: 'end' }}
      >
        <SendOutlinedIcon
          color={isSubmitButtonDisabled ? 'disabled' : 'primary'}
          sx={{ fontSize: '25px' }}
        />
      </Button>
    </>
  );

  return (
    <Stack>
      <TextField
        multiline
        placeholder="Neuer Eintrag"
        variant="outlined"
        sx={{ margin: '20px 5px 0 0' }}
        value={text}
        onChange={(event) => setText(event.target.value)}
        slotProps={{
          input: {
            endAdornment: buttons,
            sx: {
              padding: '5px 5px 5px 20px',
            },
          },
        }}
      />
      {attachedFiles.length > 0 ? (
        <List>
          {attachedFiles.map((file, index) => {
            return (
              <ListItem key={file.name + index}>
                <ListItemIcon sx={{ minWidth: '25px' }}>
                  <AttachFileIcon fontSize="small" />
                </ListItemIcon>
                <Typography sx={{ fontSize: '15px' }}>{file.name}</Typography>
                <button
                  style={{ marginLeft: '5px', marginTop: '-2px' }}
                  onClick={handleRemoveFileClick(index)}
                >
                  <CloseIcon fontSize={'15px' as any} color="error" />
                </button>
              </ListItem>
            );
          })}
        </List>
      ) : null}
    </Stack>
  );
};
