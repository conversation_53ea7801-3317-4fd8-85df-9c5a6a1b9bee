import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import TimelineContent from '@mui/lab/TimelineContent';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  accordionSummaryClasses,
  Stack,
  Typography,
} from '@mui/material';
import React, { type FC, type MouseEvent } from 'react';

import { reportTimelineCommon } from '@/components/common';
import Markdown from '@/components/Markdown';
import { type ReportTimelineEntry } from '@/generated/prisma-postgres';
import { formatDate } from '@/utils/dateUtils';
import { truncateWithEllipsis } from '@/utils/truncateWithEllipsis';

interface TimelineItemContentProps {
  item: ReportTimelineEntry;
  onInfoButtonClick: () => void;
}
const TimelineItemContent: FC<TimelineItemContentProps> = ({
  item,
  onInfoButtonClick,
}) => {
  const handleInfoButtonClick = (event: MouseEvent) => {
    event.stopPropagation();
    onInfoButtonClick();
  };

  return (
    <TimelineContent
      sx={{
        alignSelf: 'start',
        justifySelf: 'start',
        paddingRight: 0,
        paddingLeft: '5px',
      }}
    >
      <Accordion>
        <AccordionSummary
          sx={accordionSummaryStyles}
          expandIcon={
            item.content ? (
              <ExpandMoreIcon
                color="primary"
                sx={{
                  fontSize: '40px',
                  position: 'relative',
                  top: '-5px',
                }}
              />
            ) : null
          }
        >
          <Stack>
            <Stack
              direction="row"
              spacing={1}
              alignItems="center"
              sx={{ paddingTop: '5px' }}
            >
              <Typography sx={{ fontWeight: '700', fontSize: '18px' }}>
                {reportTimelineCommon.getAuthorLabel(item.authorType!)}
              </Typography>
              {reportTimelineCommon.getEntryTypeMarker(item.entryType!)}
            </Stack>
            <Typography sx={{ fontSize: '14px', lineHeight: '1' }}>
              {formatDate(item.timestamp!)}
            </Typography>
            <Typography
              sx={{ fontWeight: '600', fontSize: '16px', marginTop: '10px' }}
            >
              <Markdown>{item.title}</Markdown>
            </Typography>
          </Stack>
          <button
            onClick={handleInfoButtonClick}
            style={{
              position: 'absolute',
              right: 43,
              top: 2,
            }}
          >
            <InfoOutlinedIcon color="primary" />
          </button>
        </AccordionSummary>
        {item.content && (
          <AccordionDetails>
            <Typography>
              <Markdown>{truncateWithEllipsis(item.content, 300)}</Markdown>
            </Typography>
          </AccordionDetails>
        )}
      </Accordion>
    </TimelineContent>
  );
};

export default TimelineItemContent;

const accordionSummaryStyles = {
  ['&']: {
    paddingRight: 0,
  },
  [`& .${accordionSummaryClasses.content}`]: {
    margin: '0 0 20px !important',
  },
  [`& .${accordionSummaryClasses.expandIconWrapper}`]: {
    alignSelf: 'start',
  },
  [`& .${accordionSummaryClasses.expandIconWrapper}.${accordionSummaryClasses.expanded} > svg`]:
    {
      // when expanded, the icon is rotated along with its direction axis
      // and 5px in this case actually works as -5px
      top: '5px',
    },
};
