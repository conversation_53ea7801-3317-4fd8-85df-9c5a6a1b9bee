import {TimelineEntry} from "@/types";
import React, {FC} from "react";
import Timeline from "@mui/lab/Timeline";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import TimelineDot from "@mui/lab/TimelineDot";
import {reportTimelineCommon} from "@/components/common";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineItemContent from "./TimelineItemContent";

interface TimelineCmpProps {
    entries: TimelineEntry[]
    onInfoButtonClick: (index: number) => void
}
const TimelineCmp: FC<TimelineCmpProps> = ({ entries, onInfoButtonClick }) => {
    return (
        <Timeline sx={{ padding: '0 10px 0 5px', marginTop: '50px' }}>
            {entries.map((item, index) => {
                const renderConnector = index < entries.length - 1

                return (
                    <TimelineItem key={item.id} sx={{
                        [`&:before`]: {
                            flex: 0,
                            padding: 0,
                        }
                    }}>
                        <TimelineSeparator>
                            <TimelineDot>{reportTimelineCommon.getAuthorIcon(item.author_type)}</TimelineDot>
                            {renderConnector ? <TimelineConnector /> : null}
                        </TimelineSeparator>
                        <TimelineItemContent item={item} onInfoButtonClick={() => onInfoButtonClick(index)} />
                    </TimelineItem>
                )
            })}
        </Timeline>
    )
}

export default TimelineCmp;
