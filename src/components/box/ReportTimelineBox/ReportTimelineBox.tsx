// src/components/box/ReportTimelineBox.tsx
import React, {FC, useEffect, useState} from "react";
import {v4 as uuidv4} from 'uuid';

import {
    JobResponse,
    AuthorType, TimelineEntryAnalysisJobResult,
    TimelineEntry,
    TimelineEntryType
} from "@/types";
import CollapsibleBox from "../CollapsibleBox";
import {Form} from "./Form";
import ReportTimelineDetailsModal from "@/components/modal/ReportTimelineDetailsModal";
import Timeline from "./Timeline";
import {AI_MARKER} from "@/components/common";
import {useRouter} from "next/router";
import {orderEntries} from "@/components/box/ReportTimelineBox/orderEntries";

interface ReportTimelineBoxProps {
    visible?: boolean;
}
export const ReportTimelineBox: FC<ReportTimelineBoxProps> = ({ visible: isVisibleByDefault = false }) => {
    const router = useRouter();
    const reportNumber = router.query.report_number as string;

    const [isVisible, setIsVisible] = useState<boolean>(isVisibleByDefault);
    const [entryIndexInModal, setEntryIndexInModal] =  useState<number | null>(null);
    const [analysisJobId, setAnalysisJobId] = useState<string | null>(null);
    const [temporaryAnalysisEntry, setTemporaryAnalysisEntry] = useState<TimelineEntry | null>(null);

    const [entries, setEntries] =  useState<TimelineEntry[]>([]);
    const orderedEntries = orderEntries(entries)

    useEffect(() => {
        const loadEntries = async () => {
            const token = localStorage.getItem("jwt") || "";
            const response = await fetch(`/api/report/${reportNumber}/timeline-entries`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
            });
            if (!response.ok) {
                console.error('Failed to fetch timeline entries');
            }

            const loadedEntries = await response.json();
            setEntries(loadedEntries);
        };

        loadEntries()
    }, [reportNumber]);

    useEffect(() => {
        if (!analysisJobId) return
        if (!temporaryAnalysisEntry) return

        const interval = setInterval(async () => {
            const response = await fetch(`http://localhost:8000/jobs/${analysisJobId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
                },
            });
            if (!response.ok) {
                console.error('Failed to fetch job status');
                return
            }

            const analysis: TimelineEntryAnalysisJobResult = await response.json()
            if (analysis.status === 'FINISHED') {
                const analysisEntry = await postTimelineEntry({
                    ...temporaryAnalysisEntry,
                    title: `Zusammenfassung des Eintrags`,
                    content: analysis.aiSummary,
                }, reportNumber as string);

                // TODO: update files in the original entry
                setEntries(
                    prevState => prevState
                        .filter(item => temporaryAnalysisEntry!.id !== item.id)
                        .concat(analysisEntry)
                )

                setTemporaryAnalysisEntry(null)
                setAnalysisJobId(null)
                clearInterval(interval)
            }
            if (analysis.status === 'FAILED') {
                console.error('Timeline entry analysis failed', analysis);

                alert('Die Analyse der Dokumente ist fehlgeschlagen. Bitte versuchen Sie es später erneut.');
                setEntries(prevState => prevState.filter(item => temporaryAnalysisEntry.id !== item.id))

                setTemporaryAnalysisEntry(null)
                setAnalysisJobId(null)
                clearInterval(interval)
            }
        }, 1000); // TODO: should we apply another strategy or another interval?
        const timeout = setTimeout(() => {
            clearInterval(interval)
        }, 60000);

        return () => {
            clearInterval(interval)
            clearTimeout(timeout)
        }
    }, [analysisJobId])

    const handleDocumentEntrySubmit = async (entryToCreate: TimelineEntry, files?: File[]) => {
        /* TODO: move this logic to the server and use polling on the frontend */

        const entry = await postTimelineEntry(entryToCreate, reportNumber as string, files);

        const temporaryAnalysisEntry: TimelineEntry = {
            id: uuidv4(),
            entry_type: TimelineEntryType.ACTION,
            report_number: entry.report_number,
            author_type: AuthorType.AI,
            author_id: AI_MARKER,
            timestamp: new Date().toISOString(),
            title: '_KI analysiert die Dokumente..._',
            content: 'Dies kann ein paar Sekunden dauern',
        }
        setTemporaryAnalysisEntry(temporaryAnalysisEntry)
        setEntries(prevState => [...prevState, entry, temporaryAnalysisEntry])

        const analysisResponse = await fetch(`/api/report/${reportNumber}/timeline-entries/${entry.id}/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
            },
        })
        if (!analysisResponse.ok) {
            setEntries(prevState => prevState.filter(item => temporaryAnalysisEntry.id !== item.id))
            setTemporaryAnalysisEntry(null)

            alert ('Die Analyse der Dokumente ist fehlgeschlagen. Bitte versuchen Sie es später erneut.')
            throw new Error(`Failed to analyze timeline entry: ${await analysisResponse.text()}`);
        }

        const { job_id }: JobResponse = await analysisResponse.json()
        setAnalysisJobId(job_id)
    }

    const handleFormSubmit = async (entry: TimelineEntry, files?: File[]) => {
        try {
            if (entry.entry_type === TimelineEntryType.DOCUMENTS) {
                await handleDocumentEntrySubmit(entry, files)
                return
            }
            if (entry.entry_type === TimelineEntryType.COMMENT) {
                await postTimelineEntry(entry, reportNumber as string);
                setEntries(prevState => [...prevState, entry])
            }
        } catch (error) {
            // TODO: show error message
            console.error(error);
        }
    }

    const goToEntryInModal = ({ index, delta }: { index?: number, delta?: number }) => {
        if (index !== undefined) {
            return setEntryIndexInModal(index)
        }
        if (delta !== undefined ) {
            setEntryIndexInModal(prevState => {
                if (prevState === null) {
                    console.error('cannot goToEntry({ delta }) because there is no active entry')
                    return prevState
                }
                if (prevState + delta < 0 || prevState + delta >= entries.length) {
                    console.error('cannot goToEntry({ delta }) because it would go out of bounds')
                    return prevState
                }
                return prevState + delta
            })
        }
    }

    return (
        <CollapsibleBox
            title="Verlauf"
            isVisible={isVisible}
            setIsVisible={setIsVisible}
        >
            <Form onSuccessfulSubmit={handleFormSubmit} />
            {orderedEntries.length > 0 && <Timeline entries={orderedEntries} onInfoButtonClick={(index) => setEntryIndexInModal(index)} />}
            {entryIndexInModal !== null && (
                <ReportTimelineDetailsModal
                    reportNumber={reportNumber as string}
                    entry={orderedEntries[entryIndexInModal]}
                    disablePreviousEntryButton={entryIndexInModal === 0}
                    disableNextEntryButton={entryIndexInModal === orderedEntries.length - 1}
                    onClose={() => setEntryIndexInModal(null)}
                    goToEntry={goToEntryInModal}
                />
            )}

        </CollapsibleBox>
    );
};

export default ReportTimelineBox;

const postTimelineEntry = async (entry: TimelineEntry, report_number: string, files?: File[]): Promise<TimelineEntry> => {
    const formData = new FormData();
    Object.entries(entry).forEach(([key, value]) => {
        formData.append(key, value);
    });
    files?.forEach(file => {
        formData.append('files', file);
    });

    const response = await fetch(`/api/report/${report_number}/timeline-entries`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
        },
        body: formData,
    })
    if (!response.ok) throw new Error(`Failed to post timeline entry: ${await response.text()}`);

    return response.json()
}