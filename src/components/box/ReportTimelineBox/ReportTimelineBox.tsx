// src/components/box/ReportTimelineBox.tsx
import { useRouter } from 'next/router';
import React, { type FC, useEffect, useState } from 'react';

import CollapsibleBox from '@/components/box/CollapsibleBox';
import { orderEntries } from '@/components/box/ReportTimelineBox/orderEntries';
import { AI_MARKER } from '@/components/common';
import ReportTimelineDetailsModal from '@/components/modal/ReportTimelineDetailsModal';
import {
  PrecheckAuthorType,
  type ReportTimelineEntry,
  ReportTimelineEntryType,
} from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { getToken } from '@/utils/getToken';
import { HttpClient } from '@/utils/HttpClient';

import { Form } from './Form';
import Timeline from './Timeline';

interface ReportTimelineBoxProps {
  visible?: boolean;
}
export const ReportTimelineBox: FC<ReportTimelineBoxProps> = ({
  visible: isVisibleByDefault = false,
}) => {
  const router = useRouter();
  const reportNumber = router.query.reportNumber as string;

  const [isVisible, setIsVisible] = useState<boolean>(isVisibleByDefault);
  const [entryIndexInModal, setEntryIndexInModal] = useState<number | null>(
    null
  );

  const [entries, setEntries] = useState<ReportTimelineEntry[]>([]);
  const orderedEntries = orderEntries(entries);

  const httpClient = new HttpClient(getToken.onClient());

  useEffect(() => {
    const loadEntries = async () => {
      const response = await apiFetch(
        `/api/reports/${reportNumber}/timeline-entries`,
        {
          method: 'GET',
          raw: true,
        }
      );
      if (!response.ok) {
        console.error('Failed to fetch timeline entries');
      }

      const loadedEntries = await response.json();
      setEntries(loadedEntries.entries);
    };

    loadEntries();
  }, [reportNumber]);

  const runEntryAnalysis = async (
    entry: ReportTimelineEntry,
    temporaryAnalysisEntry: ReportTimelineEntry
  ) => {
    try {
      const analysis = await httpClient.request(
        `/api/reports/${reportNumber}/timeline-entries/${entry.id}/analyze`,
        {
          method: 'POST',
        }
      );
      const analysisEntry = await postTimelineEntry(
        {
          ...temporaryAnalysisEntry,
          title: `Analyse des Eintrags`,
          content: analysis.aiSummary,
        },
        reportNumber
      );

      setEntries((prevState) =>
        prevState
          .filter((item) => temporaryAnalysisEntry.id !== item.id)
          .concat(analysisEntry)
      );
    } catch (error) {
      alert(
        'Die Analyse der Dokumente ist fehlgeschlagen. Bitte versuchen Sie es später erneut.'
      );
      console.error('AI analysis flow failed for a timeline entry', error, {
        entry,
      });
      setEntries((prevState) =>
        prevState.filter((item) => temporaryAnalysisEntry.id !== item.id)
      );
    }
  };
  const handleDocumentEntrySubmit = async (
    entryToCreate: ReportTimelineEntry,
    files: File[]
  ) => {
    const createdEntry = await postTimelineEntry(
      entryToCreate,
      reportNumber as string,
      files
    );
    const temporaryAnalysisEntry: ReportTimelineEntry = {
      entryType: ReportTimelineEntryType.ACTION,
      reportNumber: createdEntry.reportNumber,
      authorType: PrecheckAuthorType.AI,
      authorId: AI_MARKER,
      timestamp: new Date().toISOString(),
      title: '_Die KI analysiert den Eintrag..._',
      content: 'Dies kann ein paar Sekunden dauern.',
      id: 0,
      documentId: '',
      files: null,
      metadata: null,
      relatedEntries: null,
      reportId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setEntries((prevState) => [
      ...prevState,
      createdEntry,
      temporaryAnalysisEntry,
    ]);

    await runEntryAnalysis(createdEntry, temporaryAnalysisEntry);
  };
  const handleFormSubmit = async (
    entryToCreate: ReportTimelineEntry,
    files?: File[]
  ) => {
    try {
      if (files && files.length) {
        return await handleDocumentEntrySubmit(entryToCreate, files);
      }

      const createdEntry = await postTimelineEntry(entryToCreate, reportNumber);
      setEntries((prevState) => [...prevState, createdEntry]);
    } catch (error) {
      // TODO: show error notification
      console.error(error);
    }
  };

  const goToEntryInModal = ({
    index,
    delta,
  }: {
    index?: number;
    delta?: number;
  }) => {
    if (index !== undefined) {
      return setEntryIndexInModal(index);
    }
    if (delta !== undefined) {
      setEntryIndexInModal((prevState) => {
        if (prevState === null) {
          console.error(
            'cannot goToEntry({ delta }) because there is no active entry'
          );
          return prevState;
        }
        if (prevState + delta < 0 || prevState + delta >= entries.length) {
          console.error(
            'cannot goToEntry({ delta }) because it would go out of bounds'
          );
          return prevState;
        }
        return prevState + delta;
      });
    }
  };

  return (
    <CollapsibleBox
      title="Verlauf"
      isVisible={isVisible}
      setIsVisible={setIsVisible}
    >
      <Form onSuccessfulSubmit={handleFormSubmit} />
      {orderedEntries.length > 0 && (
        <Timeline
          entries={orderedEntries}
          onInfoButtonClick={(index) => setEntryIndexInModal(index)}
        />
      )}
      {entryIndexInModal !== null && (
        <ReportTimelineDetailsModal
          reportNumber={reportNumber}
          entry={orderedEntries[entryIndexInModal]}
          disablePreviousEntryButton={entryIndexInModal === 0}
          disableNextEntryButton={
            entryIndexInModal === orderedEntries.length - 1
          }
          onClose={() => setEntryIndexInModal(null)}
          goToEntry={goToEntryInModal}
        />
      )}
    </CollapsibleBox>
  );
};

export default ReportTimelineBox;

const postTimelineEntry = async (
  entry: ReportTimelineEntry,
  reportNumber: string,
  files?: File[]
): Promise<ReportTimelineEntry> => {
  const formData = new FormData();
  Object.entries(entry).forEach(([key, value]) => {
    formData.append(key, value as any);
  });
  files?.forEach((file) => {
    formData.append('files', file);
  });

  const response = await apiFetch(
    `/api/report/${reportNumber}/timeline-entries`,
    {
      method: 'POST',
      raw: true,
      body: formData,
    }
  );
  if (!response.ok)
    throw new Error(`Failed to post timeline entry: ${await response.text()}`);

  return response.json();
};
