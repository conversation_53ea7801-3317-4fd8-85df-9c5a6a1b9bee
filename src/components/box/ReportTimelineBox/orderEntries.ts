import { type ReportTimelineEntry } from '@/generated/prisma-postgres';

export const orderEntries = (entries: ReportTimelineEntry[]) => {
  const relatedEntryIds = new Set<number>();
  for (const entry of entries) {
    if (entry.relatedEntries) {
      for (const relatedEntry of entry.relatedEntries as any) {
        relatedEntryIds.add(relatedEntry.id);
      }
    }
  }

  const sortedEntriesWithoutRelated = entries
    .filter((entry) => !relatedEntryIds.has(entry.id))
    .sort(reverseChronologicalOrder);
  const sortedEntries = sortedEntriesWithoutRelated.flatMap((mainEntry) => {
    const relatedEntries = entries.filter((entry) =>
      (mainEntry.relatedEntries as any[])?.some(
        (relatedEntry) => relatedEntry.id === entry.id
      )
    );
    const sortedRelatedEntries = relatedEntries.sort(reverseChronologicalOrder);

    return [...sortedRelatedEntries, mainEntry];
  });

  return sortedEntries;
};

const reverseChronologicalOrder = (
  a: ReportTimelineEntry,
  b: ReportTimelineEntry
) => (a < b ? 1 : -1);
