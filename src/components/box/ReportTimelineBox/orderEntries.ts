import {TimelineEntry} from "@/types";

export const orderEntries = (entries: TimelineEntry[]) => {
    const relatedEntryIds = new Set<string>()
    for (const entry of entries) {
        if (entry.related_entries) {
            for (const relatedEntry of entry.related_entries) {
                relatedEntryIds.add(relatedEntry.id)
            }
        }
    }

    const sortedEntriesWithoutRelated = entries
        .filter(entry => !relatedEntryIds.has(entry.id))
        .sort(reverseChronologicalOrder)
    const sortedEntries = sortedEntriesWithoutRelated.flatMap(mainEntry => {
        const relatedEntries = entries.filter(
            entry => mainEntry.related_entries?.some(relatedEntry => relatedEntry.id === entry.id)
        )
        const sortedRelatedEntries = relatedEntries.sort(reverseChronologicalOrder)

        return [...sortedRelatedEntries, mainEntry]
    })

    return sortedEntries
}

const reverseChronologicalOrder = (a: TimelineEntry, b: TimelineEntry) => new Date(a.timestamp) < new Date(b.timestamp) ? 1 : -1
