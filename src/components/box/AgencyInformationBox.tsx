import {
  Box,
  CircularProgress,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material';
import { useState } from 'react';

import { type Agency } from '@/generated/prisma-postgres';

import CollapsibleBox from './CollapsibleBox';
interface AgencyInformationBoxProps {
  agencyData: Agency;
  visible?: boolean;
  simplified?: boolean;
}

const AgencyInformationBox: React.FC<AgencyInformationBoxProps> = ({
  agencyData,
  visible,
  simplified,
}) => {
  const [agencyInformationIsVisible, setAgencyInformationIsVisible] = useState(
    visible || false
  );

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  if (!agencyData) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  return (
    <CollapsibleBox
      title="Maklerinformationen"
      isVisible={agencyInformationIsVisible}
      setIsVisible={setAgencyInformationIsVisible}
    >
      <Box width="100%">
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              <StyledTableRow>
                <TableCell>
                  <strong>Agenturname:</strong>
                </TableCell>
                <TableCell>{agencyData.agencyName}</TableCell>
              </StyledTableRow>

              <StyledTableRow>
                <TableCell>
                  <strong>Agenturnummer:</strong>
                </TableCell>
                <TableCell>{agencyData.agencyNumber}</TableCell>
              </StyledTableRow>

              {!simplified && (
                <>
                  <StyledTableRow>
                    <TableCell>
                      <strong>Administrator</strong>
                    </TableCell>
                    <TableCell>{agencyData.isAdmin ? 'Ja' : 'Nein'}</TableCell>
                  </StyledTableRow>
                </>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </CollapsibleBox>
  );
};

export default AgencyInformationBox;
