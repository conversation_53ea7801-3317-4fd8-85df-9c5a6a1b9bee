import { AgencyData } from "@/types";
import { useState } from "react";
import {
    TableContainer,
    Paper,
    Table,
    TableBody,
    TableRow,
    TableCell,
    styled,
    Box,
    CircularProgress
} from "@mui/material";
import CollapsibleBox from "./CollapsibleBox";

interface AgencyInformationBoxProps {
    agencyData: AgencyData;
    visible?: boolean;
    simplified?: boolean;
}

const AgencyInformationBox: React.FC<AgencyInformationBoxProps> = ({
    agencyData,
    visible,
    simplified,
}) => {
    const [agencyInformationIsVisible, setAgencyInformationIsVisible] = useState(visible || false);

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0,
        },
    }));

    if (!agencyData) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    return (
        <CollapsibleBox
            title="Maklerinformationen"
            isVisible={agencyInformationIsVisible}
            setIsVisible={setAgencyInformationIsVisible}
        >
            <Box width="100%">
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            <StyledTableRow>
                                <TableCell><strong>Agenturname:</strong></TableCell>
                                <TableCell>{agencyData.agency_name}</TableCell>
                            </StyledTableRow>

                            <StyledTableRow>
                                <TableCell><strong>Agenturnummer:</strong></TableCell>
                                <TableCell>{agencyData.agency_number}</TableCell>
                            </StyledTableRow>

                            {!simplified && <>
                                <StyledTableRow>
                                    <TableCell><strong>Administrator</strong></TableCell>
                                    <TableCell>{agencyData.is_admin ? 'Ja' : 'Nein'}</TableCell>
                                </StyledTableRow>
                            </>}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </CollapsibleBox>
    );
};

export default AgencyInformationBox;
