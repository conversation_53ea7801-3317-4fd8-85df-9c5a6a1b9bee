import {
  Box,
  Button,
  CircularProgress,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material';
import router from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { type Agency } from '@/generated/prisma-postgres';
import { type AgencyStatisticsData } from '@/types';
import { apiFetch } from '@/utils/apiFetch';
import { InvoiceCustomerStatusType } from '@/utils/invoice/types';

import CollapsibleBox from './CollapsibleBox';

interface AgencyStatisticsBoxProps {
  agencyData: Agency;
  visible?: boolean;
}

const AgencyStatisticsBox: React.FC<AgencyStatisticsBoxProps> = ({
  agencyData,
  visible,
}) => {
  const [agencyInformationIsVisible, setAgencyInformationIsVisible] = useState(
    visible || false
  );
  const [statistiscData, setStatistiscData] = useState<AgencyStatisticsData>();

  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  const { data: session } = useSession();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const statistiscsData = await apiFetch<AgencyStatisticsData>(
          `/api/agencies/${agencyData.agencyNumber}/statistics`,
          {
            accessToken: session?.accessToken,
          }
        );

        if (!statistiscsData) {
          setStatistiscData({
            openInvoices: 0,
            paiedInvoices: 0,
            openAmount: 0,
            paiedAmount: 0,
          });
          return;
        }

        setStatistiscData(statistiscsData);
      } catch (error) {
        console.error(error);
      }
    };
    fetchData();
  }, [agencyData, session?.accessToken]);

  function onShowInvoicesClicked(
    agencyNumber: string,
    customer_status: InvoiceCustomerStatusType
  ) {
    return () =>
      router.push(
        `/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agencyNumber: agencyNumber, customer_status: customer_status }))}`
      );
  }

  if (!statistiscData) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  return (
    <CollapsibleBox
      title="Agentur Statistiken"
      isVisible={agencyInformationIsVisible}
      setIsVisible={setAgencyInformationIsVisible}
    >
      <Box width="100%">
        <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
          <Table>
            <TableBody>
              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungen bezahlt:</strong>
                </TableCell>
                <TableCell>{statistiscData.paiedInvoices}</TableCell>
                <TableCell>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={onShowInvoicesClicked(
                      agencyData.agencyNumber,
                      InvoiceCustomerStatusType.PAID
                    )}
                  >
                    anzeigen
                  </Button>
                </TableCell>
              </StyledTableRow>
              <StyledTableRow>
                <TableCell>
                  <strong>Summe der bezahlten Rechnungen:</strong>
                </TableCell>
                <TableCell>
                  {Math.round(statistiscData.paiedAmount * 100) / 100} €
                </TableCell>
              </StyledTableRow>
              <StyledTableRow>
                <TableCell>
                  <strong>Rechnungen offen:</strong>
                </TableCell>
                <TableCell>{statistiscData.openInvoices}</TableCell>
                <TableCell>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={onShowInvoicesClicked(
                      agencyData.agencyNumber,
                      InvoiceCustomerStatusType.OPEN
                    )}
                  >
                    anzeigen
                  </Button>
                </TableCell>
              </StyledTableRow>
              <StyledTableRow>
                <TableCell>
                  <strong>Summe der offenen Rechnungen:</strong>
                </TableCell>
                <TableCell>
                  {Math.round(statistiscData.openAmount * 100) / 100} €
                </TableCell>
              </StyledTableRow>
              {/* <StyledTableRow>
                                <TableCell><strong>Rechnungen in erster Mahnung:</strong></TableCell>
                                <TableCell>{0}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.REMINDER_1)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der offenen Rechnungen:</strong></TableCell>
                                <TableCell>{0} €</TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Rechnungen in zweiter Mahnung:</strong></TableCell>
                                <TableCell>{0}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.REMINDER_2)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der offenen Rechnungen:</strong></TableCell>
                                <TableCell>{0} €</TableCell>
                            </StyledTableRow> */}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    </CollapsibleBox>
  );
};

export default AgencyStatisticsBox;
