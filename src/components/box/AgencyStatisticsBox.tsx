import { AgencyData, AgencyStatisticsData } from "@/types";
import { useEffect, useState } from "react";
import {
    TableContainer,
    Paper,
    Table,
    TableBody,
    TableRow,
    TableCell,
    styled,
    Box,
    Button,
    CircularProgress
} from "@mui/material";
import CollapsibleBox from "./CollapsibleBox";
import router from "next/router";
import { InvoiceCustomerStatusType } from "@/utils/invoice/types";

interface AgencyStatisticsBoxProps {
    agencyData: AgencyData;
    visible?: boolean;
}

const AgencyStatisticsBox: React.FC<AgencyStatisticsBoxProps> = ({
    agencyData,
    visible,
}) => {
    const [agencyInformationIsVisible, setAgencyInformationIsVisible] = useState(visible || false);
    const [statistiscData, setStatistiscData] = useState<AgencyStatisticsData>();

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0,
        },
    }));

    useEffect(() => {
        const token = localStorage.getItem("jwt") || "";
        const fetchData = async () => {
            try {
                const statistiscsResponse = await fetch(`/api/agencies/${agencyData.agency_number}/statistics`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                if (!statistiscsResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const statistiscData = await statistiscsResponse.json();
                setStatistiscData(statistiscData);
            } catch (error) {
                console.error(error);
            }
        }
        fetchData();
    }, [agencyData])

    function onShowInvoicesClicked(agency_number: string, customer_status: InvoiceCustomerStatusType) {
        return () => router.push(`/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number , 'customer_status' : customer_status}))}`);
    }

    if (!statistiscData) {
            return (
                <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                    <CircularProgress />
                </div>
            );
        }

    return (
        <CollapsibleBox
            title="Agentur Statistiken"
            isVisible={agencyInformationIsVisible}
            setIsVisible={setAgencyInformationIsVisible}
        >
            <Box width="100%">
                <TableContainer component={Paper} sx={{ mt: 0, border: 0 }}>
                    <Table>
                        <TableBody>
                            <StyledTableRow>
                                <TableCell><strong>Rechnungen bezahlt:</strong></TableCell>
                                <TableCell>{statistiscData.paiedInvoices}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.PAID)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der bezahlten Rechnungen:</strong></TableCell>
                                <TableCell>{Math.round(statistiscData.paiedAmount * 100) / 100} €</TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Rechnungen offen:</strong></TableCell>
                                <TableCell>{statistiscData.openInvoices}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.OPEN)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der offenen Rechnungen:</strong></TableCell>
                                <TableCell>{Math.round(statistiscData.openAmount * 100) / 100} €</TableCell>
                            </StyledTableRow>
                            {/* <StyledTableRow>
                                <TableCell><strong>Rechnungen in erster Mahnung:</strong></TableCell>
                                <TableCell>{0}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.REMINDER_1)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der offenen Rechnungen:</strong></TableCell>
                                <TableCell>{0} €</TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Rechnungen in zweiter Mahnung:</strong></TableCell>
                                <TableCell>{0}</TableCell>
                                <TableCell>
                                    <Button color="primary" variant="contained"
                                        onClick={onShowInvoicesClicked(agencyData.agency_number, InvoiceCustomerStatusType.REMINDER_2)}>
                                        anzeigen
                                    </Button>
                                </TableCell>
                            </StyledTableRow>
                            <StyledTableRow>
                                <TableCell><strong>Summe der offenen Rechnungen:</strong></TableCell>
                                <TableCell>{0} €</TableCell>
                            </StyledTableRow> */}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </CollapsibleBox>
    );
};

export default AgencyStatisticsBox;
