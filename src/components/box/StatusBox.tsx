// src/components/box/StatusBox.tsx
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useState } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { type JsonObject } from '@/generated/prisma-postgres/runtime/library';
import { formatLabel } from '@/utils/keyFormatter';

import CollapsibleBox from './CollapsibleBox';

interface StatusBoxProps {
  contract: Contract;
  visible?: boolean;
  handleChange: (event: any) => void;
  isOffer: boolean;
}

const StatusBox: React.FC<StatusBoxProps> = ({
  contract,
  visible,
  handleChange,
  isOffer,
}) => {
  const [statusIsVisible, setStatusIsVisible] = useState(visible || false);
  const session = useSession();

  const router = useRouter();
  const StyledTableRow = styled(TableRow)(() => ({
    td: {
      border: 0,
    },
  }));

  return (
    <CollapsibleBox
      title={isOffer ? 'Angebotsstatus' : 'Vertragsstatus'}
      isVisible={statusIsVisible}
      setIsVisible={setStatusIsVisible}
    >
      <TableContainer component={Paper}>
        <Table>
          <TableBody>
            {!session.data?.roles.includes('asevo-admin') &&
              !contract.isOffer && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Status:</strong>
                  </TableCell>
                  <TableCell>
                    {formatLabel(contract.activeStatus || '')}
                  </TableCell>
                </StyledTableRow>
              )}
            {session.data?.roles.includes('asevo-admin') &&
              !contract.isOffer && (
                <StyledTableRow>
                  <TableCell>
                    <strong>Aktiv:</strong>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Vertragstatus ändern" placement="top">
                      <FormControl
                        variant="outlined"
                        sx={{ width: 200, marginTop: 1 }}
                      >
                        <InputLabel>Status</InputLabel>
                        <Select
                          name="status"
                          label="Status"
                          value={contract.activeStatus || 'INACTIVE'}
                          onChange={handleChange}
                        >
                          <MenuItem value="ACTIVE">Aktiv</MenuItem>
                          <MenuItem value="INACTIVE">Inaktiv</MenuItem>
                          <MenuItem value="CANCELED">Storniert</MenuItem>
                          <MenuItem value="DELETED">Gelöscht</MenuItem>
                        </Select>
                      </FormControl>
                    </Tooltip>
                  </TableCell>
                </StyledTableRow>
              )}
            <StyledTableRow>
              <TableCell>
                <strong>{isOffer ? 'Angebot' : 'Vertrag'} erstellt:</strong>
              </TableCell>
              <TableCell>
                {(contract.contractStatus as JsonObject).create_police
                  ? 'Ja'
                  : 'Nein'}
              </TableCell>
            </StyledTableRow>
            <StyledTableRow>
              <TableCell>
                <strong>{isOffer ? 'Angebot' : 'Vertrag'} gesendet:</strong>
              </TableCell>
              <TableCell>
                {(contract.contractStatus as JsonObject).send_email
                  ? 'Ja'
                  : 'Nein'}
              </TableCell>
            </StyledTableRow>
            {!contract.isOffer && (
              <>
                <StyledTableRow>
                  <TableCell>
                    <strong>Rechnung erstellt:</strong>
                  </TableCell>
                  <TableCell>
                    {(contract.contractStatus as JsonObject).create_invoice
                      ? 'Ja'
                      : 'Nein'}
                  </TableCell>
                </StyledTableRow>
                <StyledTableRow>
                  <TableCell>
                    <strong>Rechnung gesendet:</strong>
                  </TableCell>
                  <TableCell>
                    {(contract.contractStatus as JsonObject).send_invoice
                      ? 'Ja'
                      : 'Nein'}
                  </TableCell>
                </StyledTableRow>
              </>
            )}
            {contract.isOffer && contract.toContract && (
              <StyledTableRow>
                <TableCell>
                  <strong>Erstellter Vertrag:</strong>
                </TableCell>
                <TableCell>
                  <Tooltip title="Erstellten Vertrag anzeigen" placement="top">
                    <Button
                      color="primary"
                      onClick={() =>
                        router.push(`/contract/${contract.toContract}`)
                      }
                      variant="contained"
                    >
                      {contract.toContract}
                    </Button>
                  </Tooltip>
                </TableCell>
              </StyledTableRow>
            )}
            {!contract.isOffer && contract.fromOffer && (
              <StyledTableRow>
                <TableCell>
                  <strong>Ursprügliches Angebot:</strong>
                </TableCell>
                <TableCell>
                  <Tooltip
                    title="Ursprügliches Angebot anzeigen"
                    placement="top"
                  >
                    <Button
                      color="primary"
                      onClick={() =>
                        router.push(`/contract/${contract.fromOffer}`)
                      }
                      variant="contained"
                    >
                      {contract.fromOffer}
                    </Button>
                  </Tooltip>
                </TableCell>
              </StyledTableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </CollapsibleBox>
  );
};

export default StatusBox;
