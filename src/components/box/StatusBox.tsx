// src/components/box/StatusBox.tsx
import { useState } from "react";
import { useRouter } from "next/router";

import { TableContainer, Table, TableBody, TableCell, TableRow, styled, Paper, FormControl, InputLabel, Select, MenuItem, Button, Tooltip } from "@mui/material";

import CollapsibleBox from "./CollapsibleBox";
import { ContractData } from "@/types";
import { formatLabel } from "@/utils/keyFormatter";

interface StatusBoxProps {
    contract: ContractData;
    visible?: boolean;
    handleChange: (event: any) => void;
    isOffer: boolean;
    customer_number: string;
}

const StatusBox: React.FC<StatusBoxProps> = ({
    contract,
    visible,
    handleChange,
    isOffer,
    customer_number
}) => {
    const [statusIsVisible, setStatusIsVisible] = useState(visible || false);

    const router = useRouter();
    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        'td': {
            border: 0
        },
    }));

    return (
        <CollapsibleBox
            title={isOffer ? "Angebotsstatus" : "Vertragsstatus"}
            isVisible={statusIsVisible}
            setIsVisible={setStatusIsVisible}
        >
            <TableContainer component={Paper}>
                <Table>
                    <TableBody>
                        {(localStorage.getItem('is_admin') == 'false' && !contract.is_offer) && (
                            <StyledTableRow>
                                <TableCell><strong>Status:</strong></TableCell>
                                <TableCell>{formatLabel(contract.active_status)}</TableCell>
                            </StyledTableRow>
                        )}
                        {localStorage.getItem('is_admin') == 'true' && !contract.is_offer && (
                            <StyledTableRow>
                                <TableCell><strong>Aktiv:</strong></TableCell>
                                <TableCell>
                                    <Tooltip title="Vertragstatus ändern" placement="top">
                                        <FormControl variant="outlined" sx={{ width: 200, marginTop: 1 }}>
                                            <InputLabel>Status</InputLabel>
                                            <Select
                                                name="status"
                                                label="Status"
                                                value={contract.active_status || "INACTIVE"}
                                                onChange={handleChange}
                                            >
                                                <MenuItem value="ACTIVE">Aktiv</MenuItem>
                                                <MenuItem value="INACTIVE">Inaktiv</MenuItem>
                                                <MenuItem value="CANCELED">Storniert</MenuItem>
                                                <MenuItem value="DELETED">Gelöscht</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Tooltip>
                                </TableCell>
                            </StyledTableRow>
                        )}
                        <StyledTableRow>
                            <TableCell><strong>{isOffer ? "Angebot" : "Vertrag"} erstellt:</strong></TableCell>
                            <TableCell>{contract.contract_status.create_police ? "Ja" : "Nein"}</TableCell>
                        </StyledTableRow>
                        <StyledTableRow>
                            <TableCell><strong>{isOffer ? "Angebot" : "Vertrag"} gesendet:</strong></TableCell>
                            <TableCell>{contract.contract_status.send_email ? "Ja" : "Nein"}</TableCell>
                        </StyledTableRow>
                        {!contract.is_offer && (
                            <>
                                <StyledTableRow>
                                    <TableCell><strong>Rechnung erstellt:</strong></TableCell>
                                    <TableCell>{contract.contract_status.create_invoice ? "Ja" : "Nein"}</TableCell>
                                </StyledTableRow>
                                <StyledTableRow>
                                    <TableCell><strong>Rechnung gesendet:</strong></TableCell>
                                    <TableCell>{contract.contract_status.send_invoice ? "Ja" : "Nein"}</TableCell>
                                </StyledTableRow>
                            </>
                        )}
                        {contract.is_offer && contract.to_contract && (
                            <StyledTableRow>
                                <TableCell><strong>Erstellter Vertrag:</strong></TableCell>
                                <TableCell>
                                    <Tooltip title="Erstellten Vertrag anzeigen" placement="top">
                                        <Button
                                            color="primary"
                                            onClick={() => router.push(`/customer/${customer_number}/contract/${contract.to_contract}`)}
                                            variant="contained">
                                            {contract.to_contract}
                                        </Button>
                                    </Tooltip>
                                </TableCell>
                            </StyledTableRow>
                        )}
                        {!contract.is_offer && contract.from_offer && (
                            <StyledTableRow>
                                <TableCell><strong>Ursprügliches Angebot:</strong></TableCell>
                                <TableCell>
                                    <Tooltip title="Ursprügliches Angebot anzeigen" placement="top">
                                        <Button
                                            color="primary"
                                            onClick={() => router.push(`/customer/${customer_number}/contract/${contract.from_offer}`)}
                                            variant="contained">
                                            {contract.from_offer}
                                        </Button>
                                    </Tooltip>
                                </TableCell>
                            </StyledTableRow>
                        )}
                    </TableBody>
                </Table>
            </TableContainer>
        </CollapsibleBox>
    );
};

export default StatusBox;
