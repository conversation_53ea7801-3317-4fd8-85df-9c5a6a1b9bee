import React, {<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState} from "react";
import {v4 as uuidv4} from 'uuid';

import {IconButton, Tooltip, Typography} from "@mui/material";
import CollapsibleBox from "./CollapsibleBox";
import {AuthorType, JobResponse, ReportSummary, ReportSummaryJobResult} from "@/types";
import {useRouter} from "next/router";
import CarouselControls from "@/components/CarouselControls";
import {formatDate} from "@/utils/dateUtils";
import Markdown from "@/components/Markdown";
import {reportSummaryMapper} from "@/utils/mappers";
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';

interface ReportSummaryBoxProps {
    visible?: boolean;
}

const ReportSummaryBox: FC<ReportSummaryBoxProps> = ({ visible: isVisibleByDefault = false }) => {
    const router = useRouter();
    const { report_number } = router.query;

    const [isVisible, setIsVisible] = useState<boolean>(isVisibleByDefault);
    const [currentSummaryIndex, setCurrentSummaryIndex] = useState<number>(0);

    const [analysisJobId, setAnalysisJobId] = useState<string | null>(null);
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false)

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [summaries, setSummaries] = useState<ReportSummary[]>([]);
    const sortedSummaries = summaries.sort(reverseChronologicalOrder)

    useEffect(() => {
        const loadSummaries = async () => {
            setIsLoading(true);

            try {
                const token = localStorage.getItem("jwt") || "";
                const response = await fetch(`/api/report/${report_number}/summaries`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                });
                if (!response.ok) {
                    throw new Error(`Failed to fetch summaries: ${response.status} ${response.statusText}`);
                }

                const loadedSummaries = await response.json();
                const domainSummaries = loadedSummaries.map(reportSummaryMapper.toDomain)

                setSummaries(domainSummaries);
            } catch (error) {
                // TODO: show error message
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        }

        loadSummaries()
    }, [report_number])

    // TODO: extract into a hook
    useEffect(() => {
        if (!analysisJobId) return

        const stopPolling = () => {
            clearInterval(interval)
            setIsRefreshing(false)
            setAnalysisJobId(null)
        }

        const interval = setInterval(async () => {
            const response = await fetch(`http://localhost:8001/timeline-jobs/${analysisJobId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
                },
            });
            if (!response.ok) {
                console.error('Failed to fetch job status');
                return
            }

            const jobResult: ReportSummaryJobResult = await response.json()

            if (jobResult.status === 'FINISHED') {
                const summary: ReportSummary = {
                    id: uuidv4(),
                    report_number: report_number as string,
                    author_type: AuthorType.AI,
                    text: jobResult.timelineSummary || '',
                    timestamp: new Date().toISOString(),
                }
                const createResponse = await fetch(`/api/report/${report_number}/summaries`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
                    },
                    body: JSON.stringify(reportSummaryMapper.toStorage(summary))
                });
                if (createResponse.ok) {
                    const createdSummary = await createResponse.json()
                    const domainSummary = reportSummaryMapper.toDomain(createdSummary)

                    setSummaries(prevState => [domainSummary, ...prevState])
                    setCurrentSummaryIndex(0)
                } else {
                    console.error('Failed to POST summary');
                }

                stopPolling()
            }
            if (jobResult.status === 'FAILED') {
                stopPolling()
            }
        }, 1000); // TODO: should we apply another strategy or another interval?
        const timeout = setTimeout(() => {
            stopPolling()
        }, 60000);

        return () => {
            clearInterval(interval)
            clearTimeout(timeout)
        }
    }, [analysisJobId])


    const handleRefreshClick: MouseEventHandler = async (event) => {
        event.stopPropagation()

        try {
            setIsRefreshing(true)
            const token = localStorage.getItem("jwt") || "";
            const response = await fetch(`/api/report/${report_number}/create/summary`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
            });
            if (!response.ok) throw new Error('Failed to create summary');

            const { job_id, status }: JobResponse = await response.json()
            // TODO: handle FAILED status
            setAnalysisJobId(job_id)

        } catch (error) {
            // TODO: show error message
            console.error(error);
            setIsRefreshing(false)
        }
    }

    const resolveBoxContent = () => {
        if (isLoading) {
            return <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                Lade Zusammenfassung...
            </Typography>
        }
        if (sortedSummaries.length === 0) {
            return <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                Noch keine Zusammenfassung vorhanden
            </Typography>
        }

        const summary = sortedSummaries[currentSummaryIndex]
        return (
            <>
                <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                    {formatDate(summary.timestamp)}
                </Typography>
                <Typography sx={{ fontSize: '17px', marginTop: '15px' }}>
                    <Markdown>{summary.text}</Markdown>
                </Typography>
                <CarouselControls
                    sx={{ marginTop: '40px' }}
                    disablePrevious={currentSummaryIndex === 0}
                    disableNext={currentSummaryIndex === summaries.length - 1}
                    goPrevious={() => setCurrentSummaryIndex(prev => prev - 1)}
                    goNext={() => setCurrentSummaryIndex(prev => prev + 1)}
                    goMostRecent={() => setCurrentSummaryIndex(0)}
                />
            </>
        )
    }

    return (
        <CollapsibleBox title="KI - Zusammenfassung" isVisible={isVisible} setIsVisible={setIsVisible}>
            {resolveBoxContent()}
            <Tooltip title='Aktualisieren' placement="top">
                <IconButton
                    onClick={handleRefreshClick}
                    color="primary"
                    size="small"
                    sx={{ position: 'absolute', top: '26px', right: '62px' }}
                    disabled={isRefreshing}
                >
                    <AutorenewOutlinedIcon />
                </IconButton>
            </Tooltip>
            {isRefreshing && (
                <Typography sx={{ color: 'primary', fontSize: '17px', fontStyle: 'italic', position: 'absolute', top: '32px', right: '105px' }}>
                    Aktualisieren...
                </Typography>
            )}
        </CollapsibleBox>
    );
};

export default ReportSummaryBox;

const reverseChronologicalOrder = (a: ReportSummary, b: ReportSummary) => new Date(a.timestamp) < new Date(b.timestamp) ? 1 : -1
