import React, {<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState} from "react";

import {Icon<PERSON>utton, Tooltip, Typography} from "@mui/material";
import CollapsibleBox from "./CollapsibleBox";
import {ReportSummary} from "@/types";
import {useRouter} from "next/router";
import CarouselControls from "@/components/CarouselControls";
import {formatDate} from "@/utils/dateUtils";
import Markdown from "@/components/Markdown";
import {reportSummaryMapper} from "@/utils/mappers";
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import {HttpClient} from "@/utils/HttpClient";
import {getToken} from "@/utils/getToken";

interface ReportSummaryBoxProps {
    visible?: boolean;
}

const ReportSummaryBox: FC<ReportSummaryBoxProps> = ({ visible: isVisibleByDefault = false }) => {
    const router = useRouter();
    const { report_number } = router.query;

    const [isVisible, setIsVisible] = useState<boolean>(isVisibleByDefault);
    const [currentSummaryIndex, setCurrentSummaryIndex] = useState<number>(0);

    const [isRefreshing, setIsRefreshing] = useState<boolean>(false)

    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [summaries, setSummaries] = useState<ReportSummary[]>([]);
    const sortedSummaries = summaries.sort(reverseChronologicalOrder)

    const httpClient = new HttpClient(getToken.onClient())

    useEffect(() => {
        const loadSummaries = async () => {
            setIsLoading(true);

            try {
                const token = localStorage.getItem("jwt") || "";
                const response = await fetch(`/api/report/${report_number}/summaries`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                });
                if (!response.ok) {
                    throw new Error(`Failed to fetch summaries: ${response.status} ${response.statusText}`);
                }

                const loadedSummaries = await response.json();
                const domainSummaries = loadedSummaries.map(reportSummaryMapper.toDomain)

                setSummaries(domainSummaries);
            } catch (error) {
                // TODO: show error message
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        }

        loadSummaries()
    }, [report_number])

    const handleRefreshClick: MouseEventHandler = async (event) => {
        event.stopPropagation()
        setIsRefreshing(true)

        try {
            const summary = await httpClient.request(`/api/report/${report_number}/create/summary`, {
                method: 'POST',
            });

            setSummaries(prevState => [summary, ...prevState])
            setCurrentSummaryIndex(0)
        } catch (error) {
            alert('Die Zusammenfassung kann nicht erstellt werden. Versuchen Sie es erneut später.')
            console.error(error);
        } finally {
            setIsRefreshing(false)
        }
    }

    const resolveBoxContent = () => {
        if (isLoading) {
            return <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                Lade Zusammenfassung...
            </Typography>
        }
        if (sortedSummaries.length === 0) {
            return <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                Noch keine Zusammenfassung vorhanden
            </Typography>
        }

        const summary = sortedSummaries[currentSummaryIndex]
        return (
            <>
                <Typography sx={{ fontSize: '16px', fontWeight: '700', marginTop: '20px' }}>
                    {formatDate(summary.timestamp)}
                </Typography>
                <Typography sx={{ fontSize: '17px', marginTop: '15px' }}>
                    <Markdown>{summary.text}</Markdown>
                </Typography>
                <CarouselControls
                    sx={{ marginTop: '40px' }}
                    disablePrevious={currentSummaryIndex === 0}
                    disableNext={currentSummaryIndex === summaries.length - 1}
                    goPrevious={() => setCurrentSummaryIndex(prev => prev - 1)}
                    goNext={() => setCurrentSummaryIndex(prev => prev + 1)}
                    goMostRecent={() => setCurrentSummaryIndex(0)}
                />
            </>
        )
    }

    return (
        <CollapsibleBox title="KI - Zusammenfassung" isVisible={isVisible} setIsVisible={setIsVisible}>
            {resolveBoxContent()}
            <Tooltip title='Aktualisieren' placement="top">
                <IconButton
                    onClick={handleRefreshClick}
                    color="primary"
                    size="small"
                    sx={{ position: 'absolute', top: '26px', right: '62px' }}
                    disabled={isRefreshing}
                >
                    <AutorenewOutlinedIcon />
                </IconButton>
            </Tooltip>
            {isRefreshing && (
                <Typography sx={{ color: 'primary', fontSize: '17px', fontStyle: 'italic', position: 'absolute', top: '32px', right: '105px' }}>
                    Aktualisieren...
                </Typography>
            )}
        </CollapsibleBox>
    );
};

export default ReportSummaryBox;

const reverseChronologicalOrder = (a: ReportSummary, b: ReportSummary) => new Date(a.timestamp) < new Date(b.timestamp) ? 1 : -1
