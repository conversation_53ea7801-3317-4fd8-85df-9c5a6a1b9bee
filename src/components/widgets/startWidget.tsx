'use client';

import AddModeratorIcon from '@mui/icons-material/AddModerator';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import TimerIcon from '@mui/icons-material/Timer';
import { Stack, type SxProps, type Theme, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { KvcCard } from '@/components/cards/kvcCard';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface StartWidgetProps {
  schnellCheckDisabled?: boolean;
  risikoVorabfrageDisabled?: boolean;
  antragDisabled?: boolean;
  reduced?: boolean;
  showHeadline?: boolean;
  sx?: SxProps<Theme>;
  closeModal?: () => void;
}

export function StartWidget(props: StartWidgetProps) {
  const t = useTranslations();

  const router = useRouter();
  return (
    <Stack direction="column" display="flex" flexWrap="wrap" sx={props.sx}>
      {props.showHeadline && (
        <Typography
          variant="h5"
          component="h1"
          textAlign="center"
          color="var(--teal)"
          fontWeight={700}
          marginBottom={2}
        >
          {t.rich('widget.new-case.headline', intlTranslationRichHelper)}
        </Typography>
      )}

      <Stack
        direction="row"
        display="flex"
        flexWrap="wrap"
        gap={4}
        alignContent="center"
        justifyContent="center"
      >
        <KvcCard
          reduced={props.reduced}
          disabled={props.schnellCheckDisabled}
          icon={TimerIcon}
          titleTextKey="start-cards.schnellcheck.title"
          bodyTextKey="start-cards.schnellcheck.body"
          buttonLabelKey="start-cards.schnellcheck.button"
          buttonOnClick={() => {
            if (props.closeModal) {
              props.closeModal();
            }
            router.push('/risikocheck');
          }}
          buttonDisabledPlaceholder="start-cards.schnellcheck.button-disabled-placeholder"
        />

        <KvcCard
          reduced={props.reduced}
          disabled={props.risikoVorabfrageDisabled}
          //  This shows up as an error but it works, we can figure out a solution later:
          icon={AddModeratorIcon}
          titleTextKey="start-cards.risiko-anfrage.title"
          bodyTextKey="start-cards.risiko-anfrage.body"
          buttonLabelKey="start-cards.risiko-anfrage.button"
          buttonOnClick={() => {
            if (props.closeModal) {
              props.closeModal();
            }

            router.push('/newcase');
          }}
          buttonDisabledPlaceholder="start-cards.risiko-anfrage.button-disabled-placeholder"
        />

        <KvcCard
          reduced={props.reduced}
          disabled={props.antragDisabled}
          icon={FactCheckIcon}
          titleTextKey="start-cards.antrag.title"
          bodyTextKey="start-cards.antrag.body"
          buttonLabelKey="start-cards.antrag.button"
          buttonOnClick={() => {
            if (props.closeModal) {
              props.closeModal();
            }
            router.push('/');
          }}
          buttonDisabledPlaceholder="start-cards.antrag.button-disabled-placeholder"
        />
      </Stack>
    </Stack>
  );
}
