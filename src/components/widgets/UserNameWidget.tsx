'use client';
import { type SxProps, type Theme } from '@mui/material';
import { useTranslations } from 'next-intl';

import { useUserSuspense } from '@/modules/user/hooks/useUser';

interface Props {
  textBefore?: string;
  textAfter?: string;
  sx?: SxProps<Theme>;
  includeSalutation?: boolean;
  prefix?: string;
  suffix?: string;
  prefixKey?: string;
  suffixKey?: string;
}

export function UserNameWidget(props: Props) {
  const { user } = useUserSuspense();
  const t = useTranslations();
  return (
    <>
      {typeof props.prefixKey === 'string' && `${t(props.prefixKey)} `}
      {typeof props.prefix === 'string' && `${props.prefix} `}
      {props.includeSalutation === true &&
        user.salutation !== null &&
        user.salutation}
      {`${user.firstName} ${user.lastName}`}
      {typeof props.suffix === 'string' && ` ${props.suffix}`}
      {typeof props.suffixKey === 'string' && ` ${t(props.suffixKey)}`}
    </>
  );
}
