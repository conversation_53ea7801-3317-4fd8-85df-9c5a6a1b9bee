import {AuthorType, TimelineEntryType} from "@/types";
import {ReactNode} from "react";
import SmartToyOutlinedIcon from "@mui/icons-material/SmartToyOutlined";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import {styled} from "@mui/material";

export const VisuallyHiddenInput = styled('input')({
    clip: 'rect(0 0 0 0)',
    clipPath: 'inset(50%)',
    height: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 0,
    left: 0,
    whiteSpace: 'nowrap',
    width: 1,
})

export const AI_MARKER = 'AI'

const getAuthorLabel = (authorType: AuthorType): string => {
    const map = {
        [AuthorType.AI]: 'Virtueller Assistent',
        [AuthorType.AGENT]: 'Makler',
        [AuthorType.CUSTOMER]: 'not implemented',
    }
    return map[authorType]
}
const getAuthorIcon = (authorType: AuthorType): ReactNode => {
    const map = {
        [AuthorType.AI]: <SmartToyOutlinedIcon fontSize='large' sx={{ color: 'white' }} />,
        [AuthorType.AGENT]: <PersonOutlineIcon fontSize='large' sx={{ color: 'white' }} />,
        [AuthorType.CUSTOMER]: null,
    }
    return map[authorType]
}
const getEntryTypeMarker = (entryType: TimelineEntryType): ReactNode => {
    const map = {
        [TimelineEntryType.ACTION]: <CheckCircleOutlineIcon fontSize={'1.15rem' as any} />,
        [TimelineEntryType.DOCUMENTS]: <AttachFileIcon fontSize={'1.25rem' as any} />,
        [TimelineEntryType.COMMENT]: null,
        [TimelineEntryType.SUMMARY]: null,
    }
    return map[entryType]
}
export const reportTimelineCommon = {
    getAuthorIcon,
    getAuthorLabel,
    getEntryTypeMarker,

}
