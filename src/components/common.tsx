import AttachFileIcon from '@mui/icons-material/AttachFile';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import SmartToyOutlinedIcon from '@mui/icons-material/SmartToyOutlined';
import { styled } from '@mui/material';
import { type ReactNode } from 'react';

import {
  ReportTimelineAuthorType,
  ReportTimelineEntryType,
} from '@/generated/prisma-postgres';

export const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export const AI_MARKER = 'AI';

const getAuthorLabel = (authorType: ReportTimelineAuthorType): string => {
  const map = {
    [ReportTimelineAuthorType.AI]: 'Virtueller Assistent',
    [ReportTimelineAuthorType.AGENT]: 'Makler',
    [ReportTimelineAuthorType.CUSTOMER]: 'not implemented',
  };
  return map[authorType];
};
const getAuthorIcon = (authorType: ReportTimelineAuthorType): ReactNode => {
  const map = {
    [ReportTimelineAuthorType.AI]: (
      <SmartToyOutlinedIcon fontSize="large" sx={{ color: 'white' }} />
    ),
    [ReportTimelineAuthorType.AGENT]: (
      <PersonOutlineIcon fontSize="large" sx={{ color: 'white' }} />
    ),
    [ReportTimelineAuthorType.CUSTOMER]: null,
  };
  return map[authorType];
};
const getEntryTypeMarker = (entryType: ReportTimelineEntryType): ReactNode => {
  const map = {
    [ReportTimelineEntryType.ACTION]: (
      <CheckCircleOutlineIcon fontSize={'1.15rem' as any} />
    ),
    [ReportTimelineEntryType.DOCUMENTS]: (
      <AttachFileIcon fontSize={'1.25rem' as any} />
    ),
    [ReportTimelineEntryType.COMMENT]: null,
    [ReportTimelineEntryType.SUMMARY]: null,
  };
  return map[entryType];
};
export const reportTimelineCommon = {
  getAuthorIcon,
  getAuthorLabel,
  getEntryTypeMarker,
};
