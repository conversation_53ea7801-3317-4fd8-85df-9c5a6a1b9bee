// src/components/AgentList.tsx
import {
  Download as DownloadIcon,
  Edit as EditIcon,
  PersonAddAlt1 as PersonAddAlt1Icon,
} from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { apiFetch } from '@/utils/apiFetch';
import { useTableQuery } from '@/utils/useTableQuery';

import { agentFieldConfig } from './filters/agentFieldConfig';
import { FilterBuilder } from './filters/FilterBuilder';
import { serializeFilters } from './filters/url';

export default function AgentList() {
  const [isExporting, setIsExporting] = useState(false);

  const { data: session, status } = useSession();

  const router = useRouter();

  const table = useTableQuery<any>({
    endpoint: '/api/agents',
    defaultSortField: 'username',
    accessToken: session?.accessToken,
    enabled: status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.agents ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: agents,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleShowDetails = (agentNumber: string, event: React.MouseEvent) => {
    const url = `/agent/${agentNumber}`;
    if (event.ctrlKey || event.metaKey) window.open(url, '_blank');
    else router.push(url);
  };

  const handleAddCustomer = (
    agentNumber: string,
    agencyNumber: string,
    event: React.MouseEvent
  ) => {
    const baseUrl = '/customer/new';
    const queryParams = new URLSearchParams({
      agent_number: String(agentNumber), // keep query keys if consumers expect snake_case
      agency_number: String(agencyNumber),
    }).toString();
    const fullUrl = `${baseUrl}?${queryParams}`;
    if (event.ctrlKey || event.metaKey) window.open(fullUrl, '_blank');
    else router.push(fullUrl);
  };

  const handleEditClick = (agentNumber: string, event: React.MouseEvent) => {
    const url = `/agent/edit/${agentNumber}`;
    if (event.ctrlKey || event.metaKey) window.open(url, '_blank');
    else router.push(url);
  };

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);
      const query = new URLSearchParams({
        sortField,
        sortDirection,
        ...serializeFilters(filters),
      });
      const response = await apiFetch(
        `/api/agents/export-csv?${query.toString()}`,
        {
          raw: true,
        }
      );
      if (!response.ok) throw new Error('CSV Export fehlgeschlagen');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'maklerliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  // --- UI ---
  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }
  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Maklerliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        {/* Unified filter builder */}
        <FilterBuilder
          fields={agentFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="username"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      {/* Table */}
      <TableContainer component={Paper} sx={{ mt: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('username')}
              >
                Benutzername{' '}
                {sortField === 'username' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('agentNumber')}
              >
                Maklernummer{' '}
                {sortField === 'agentNumber' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('companyName')}
              >
                Agenturname{' '}
                {sortField === 'companyName' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('agencyNumber')}
              >
                Agenturnummer{' '}
                {sortField === 'agencyNumber' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('postalCode')}
              >
                Adresse{' '}
                {sortField === 'postalCode' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('email')}
              >
                E-Mail{' '}
                {sortField === 'email' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>

              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                Aktionen
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {agents.map((agent, index) => (
              <TableRow
                key={index}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={(event) => handleShowDetails(agent.agentNumber, event)}
              >
                <TableCell>{agent.username}</TableCell>
                <TableCell>{agent.agentNumber}</TableCell>
                <TableCell>{agent.companyName}</TableCell>
                <TableCell>{agent.agencyNumber}</TableCell>
                <TableCell>
                  {agent.street} {agent.houseNumber}, {agent.postalCode}{' '}
                  {agent.city}
                </TableCell>
                <TableCell>{agent.email}</TableCell>
                <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                  <Tooltip title="Kunde anlegen">
                    <IconButton
                      onClick={(event) =>
                        handleAddCustomer(
                          agent.agentNumber,
                          agent.agencyNumber,
                          event
                        )
                      }
                      color="primary"
                    >
                      <PersonAddAlt1Icon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Makler bearbeiten">
                    <IconButton
                      onClick={(event) =>
                        handleEditClick(agent.agentNumber, event)
                      }
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={handlePageChange}
          sx={{ mt: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}

      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '1rem',
        }}
      >
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </div>
    </Box>
  );
}
