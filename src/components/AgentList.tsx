// src/components/AgentList.tsx
import React, { useEffect, useCallback, useState } from 'react';
import { useRouter } from 'next/router';
import { formatField } from '../utils/keyFormatter';
import { AgentData } from '@/types';
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Typography,
    CircularProgress,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Chip,
    SelectChangeEvent,
    Tooltip,
    Box
} from '@mui/material';
import IconButton from '@mui/material/IconButton';
import {
    Edit as EditIcon,
    PersonAddAlt1 as PersonAddAlt1Icon,
    Download as DownloadIcon,
} from "@mui/icons-material";

interface PaginationMeta {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

const predefinedFilterValues: Record<string, string[]> = {}; //if needed (used in ContractsList.tsx)

export default function AgentList() {
    const [agents, setAgents] = useState<AgentData[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(10);
    const [meta, setMeta] = useState<PaginationMeta | null>(null);
    const [filters, setFilters] = useState<Record<string, string>>({});
    const [currentField, setCurrentField] = useState<string>('username');
    const [currentValue, setCurrentValue] = useState<string>('');
    const [sortField, setSortField] = useState<string>('username');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const [initialized, setInitialized] = useState(false);
    const [isExporting, setIsExporting] = useState(false);

    const router = useRouter();

    useEffect(() => {
        const { query } = router;

        const initialPage = query.page ? parseInt(query.page as string, 10) : 1;
        const initialLimit = query.limit ? parseInt(query.limit as string, 10) : 10;

        const initialFilters = query.filters
            ? JSON.parse(query.filters as string)
            : ("");
        const initialSortField = query.sortField as string || 'username';
        const initialSortDirection = (query.sortDirection as 'asc' | 'desc') || 'asc';

        setPage(initialPage);
        setLimit(initialLimit);
        setFilters(initialFilters);
        setSortField(initialSortField);
        setSortDirection(initialSortDirection);

        setInitialized(true);
    }, []);

    useEffect(() => {
        if (!initialized) return;

        const cleanedFilters: Record<string, string> = {};
        for (const [key, value] of Object.entries(filters)) {
            if (value !== "") {
                cleanedFilters[key] = value;
            }
        }

        const query: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
            sortField,
            sortDirection,
        };

        if (Object.keys(cleanedFilters).length > 0) {
            query.filters = JSON.stringify(cleanedFilters);
        }

        router.replace(
            {
                pathname: router.pathname,
                query,
            },
            undefined,
            { shallow: true }
        );
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const fetchAgents = async () => {
        setLoading(true);
        try {
            const offset = (page - 1) * limit;
            const response = await fetch(
                `/api/agent?limit=${limit}&offset=${offset}&filters=${JSON.stringify(filters)}&sortField=${sortField}&sortDirection=${sortDirection}`,
                {
                    method: 'GET',
                    headers: { Authorization: `Bearer ${localStorage.getItem('jwt') || ''}` },
                }
            );

            if (!response.ok) {
                throw new Error('Failed to fetch agents');
            }

            const data = await response.json();
            setAgents(data.agents);
            setMeta(data.meta);
        } catch (error) {
            console.error('Error fetching agents:', error);
            setError('Fehler beim Laden der Daten');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!initialized) return;
        fetchAgents();
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const handleAddFilter = () => {
        if (currentField && currentValue) {
            setFilters((prev) => ({ ...prev, [currentField]: currentValue }));
            setCurrentValue('');
        }
    };

    const handleRemoveFilter = (field: string) => {
        setFilters((prev) => {
            const updated = { ...prev };
            delete updated[field];
            return updated;
        });
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
        setPage(value);
    };

    const handleLimitChange = (event: SelectChangeEvent<number>) => {
        const newLimit = Number(event.target.value);
        setLimit(newLimit);
        setPage(1);
    };

    const handleShowDetails = (agent_number: string, event: React.MouseEvent) => {
        const url = `/agent/${agent_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleAddCustomer = (agent_number: string, agency_number: string, event: React.MouseEvent) => {

        const baseUrl = '/customer/new';

        const queryParams = new URLSearchParams({
            agent_number: agent_number.toString(),
            agency_number: agency_number.toString(),
        }).toString();

        const fullUrl = `${baseUrl}?${queryParams}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(fullUrl, '_blank');
        } else {
            router.push(fullUrl);
        }
    };

    const handleEditClick = (agent_number: string, event: React.MouseEvent) => {
        const url = `/agent/edit/${agent_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const handleExportCsv = useCallback(async () => {
        try {
            setIsExporting(true);
            const token = localStorage.getItem('jwt');
            if (!token) {
                alert("Kein Token vorhanden");
                return;
            }

            const query = new URLSearchParams({
                sortField,
                sortDirection,
                filters: JSON.stringify(filters),
            });

            const response = await fetch(`/api/agent/export-csv?${query.toString()}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                throw new Error("CSV Export fehlgeschlagen");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'maklerliste.csv';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            console.error('CSV Download Error:', err);
            alert("Export fehlgeschlagen");
        } finally {
            setIsExporting(false);
        }
    }, [filters, sortField, sortDirection]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (error) {
        return (
            <Typography variant="h6" color="error" align="center">
                Fehler: {error}
            </Typography>
        );
    }

    return (
        <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
            <Typography variant="h4" align="center" gutterBottom color="primary">
                Maklerliste
            </Typography>

            <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                    <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                        <InputLabel>Filterkriterium</InputLabel>
                        <Select
                            value={currentField}
                            onChange={(e) => setCurrentField(e.target.value)}
                            label="Filterkriterium"
                        >
                            <MenuItem value="username">Benutzername</MenuItem>
                            <MenuItem value="email">E-Mail</MenuItem>
                            <MenuItem value="company_name">Agenturname</MenuItem>
                            <MenuItem value="agency_number">Agenturnummer</MenuItem>
                            <MenuItem value="agent_number">Maklernummer</MenuItem>
                            <MenuItem value="street">Straße</MenuItem>
                            <MenuItem value="postal_code">Postleitzahl</MenuItem>
                            <MenuItem value="city">Ort</MenuItem>
                        </Select>
                    </FormControl>

                    {predefinedFilterValues[currentField] ? (
                        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                            <InputLabel>Filterwert</InputLabel>
                            <Select
                                value={currentValue}
                                onChange={(e) => setCurrentValue(e.target.value as string)}
                                label="Filterwert"
                            >
                                {predefinedFilterValues[currentField].map((value) => (
                                    <MenuItem key={value} value={value}>
                                        {value}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    ) : (
                        <TextField
                            label="Filterwert"
                            variant="outlined"
                            size="small"
                            value={currentValue}
                            onChange={(e) => setCurrentValue(e.target.value)}
                            sx={{ width: 200 }}
                        />
                    )}

                    <Button variant="contained" color="primary" onClick={handleAddFilter}>
                        Filtern
                    </Button>
                    <div style={{ flexGrow: 1 }} />
                    <Tooltip title="CSV exportieren">
                        <IconButton
                            onClick={handleExportCsv}
                            color="primary"
                            style={{ marginRight: 15 }}
                            disabled={isExporting}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip>
                </div>

                <div>
                    {Object.entries(filters).map(([field, value]) => (
                        <Chip
                            key={field}
                            label={`${formatField(field)}: ${value}`}
                            onDelete={() => handleRemoveFilter(field)}
                            color="primary"
                            sx={{ marginRight: '0.5rem', marginBottom: '0.5rem', borderRadius: 0 }}
                        />
                    ))}
                </div>
            </div>

            <TableContainer component={Paper} sx={{ marginTop: 4 }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('username')}
                            >
                                Benutzername {sortField === 'username' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('agent_number')}
                            >
                                Maklernummer {sortField === 'agent_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('company_name')}
                            >
                                Agenturname {sortField === 'company_name' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('agency_number')}
                            >
                                Agenturnummer {sortField === 'agency_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('postal_code')}
                            >
                                Adresse {sortField === 'postal_code' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('email')}
                            >
                                E-Mail {sortField === 'email' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>Aktionen</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {agents.map((agent, index) => (
                            <TableRow
                                key={index}
                                hover
                                sx={{ cursor: 'pointer' }}
                                onClick={(event) => handleShowDetails(agent.agent_number, event)}
                            >
                                <TableCell>
                                    {agent.username}
                                </TableCell>
                                <TableCell>{agent.agent_number}</TableCell>
                                <TableCell>{agent.company_name}</TableCell>
                                <TableCell>{agent.agency_number}</TableCell>
                                <TableCell>
                                    {agent.street} {agent.house_number}, {agent.postal_code} {agent.city}
                                </TableCell>
                                <TableCell>{agent.email}</TableCell>
                                <TableCell align="right">
                                    <Tooltip title='Kunde anlegen'>
                                        <IconButton
                                            onClick={(event) => {
                                                event.stopPropagation();
                                                handleAddCustomer(agent.agent_number, agent.agency_number, event);
                                            }}
                                            color="primary"
                                        >
                                            <PersonAddAlt1Icon />
                                        </IconButton>
                                    </Tooltip>
                                    <Tooltip title='Makler bearbeiten'>
                                        <IconButton
                                            onClick={(event) => {
                                                event.stopPropagation();
                                                handleEditClick(agent.agent_number, event);
                                            }}
                                            color="primary"
                                        >
                                            <EditIcon />
                                        </IconButton>
                                    </Tooltip>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>

            {meta && (
                <Pagination
                    count={meta.pageCount}
                    page={page}
                    onChange={handlePageChange}
                    sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
                />
            )}
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                    <InputLabel id="results-per-page-label">Ergebnisse pro Seite</InputLabel>
                    <Select<number>
                        labelId="results-per-page-label"
                        value={limit}
                        onChange={handleLimitChange}
                        label="Ergebnisse pro Seite"
                    >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                    </Select>
                </FormControl>
            </div>
        </Box>
    );
}
