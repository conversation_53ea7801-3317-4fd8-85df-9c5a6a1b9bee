import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';

export const KvcSecondaryButton = styled(Button)(({ theme }) => ({
  backgroundColor: 'transparent',
  textTransform: 'none',
  fontWeight: '700',
  border: 3,
  borderStyle: 'solid',
  borderColor: theme.palette.primary.main,
  color: theme.palette.primary.main,

  '&:hover': {
    backgroundColor: 'transparent',
    // textDecoration: 'underline',
    color: theme.palette.buttons.primary.hover,
    borderColor: theme.palette.buttons.primary.hover,
  },

  '& .MuiButton-startIcon': {
    marginRight: theme.spacing(0.5),
  },
}));
