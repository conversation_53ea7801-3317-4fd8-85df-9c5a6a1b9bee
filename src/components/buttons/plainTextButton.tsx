import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';

export const KvcPlainTextButton = styled(Button)(({ theme }) => ({
  backgroundColor: 'transparent',
  color: 'black',
  textTransform: 'none',
  fontWeight: '700',

  '&:hover': {
    backgroundColor: 'transparent',
    textDecoration: 'underline',
  },

  '& .MuiButton-startIcon': {
    marginRight: theme.spacing(0.5),
  },
}));
