import { useEffect, useState } from "react";
import { CalculationParametersData } from "@/types";
import { Button, TextField, Tooltip, Typography, Box, Grid2 as Grid } from '@mui/material';
import StaticBox from "./box/StaticBox";

export default function AdminSettings() {

    const [calculationParametersData, setCalculationParametersData] = useState<CalculationParametersData>()

    useEffect(() => {
        const year = new Date().toISOString().split('T')[0]
        fetch(`/api/calculation/get?valid_from=${year}`, {
            method: 'GET',
            headers: {
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        })
            .then(res => res.json())
            .then(data => setCalculationParametersData(data))
    }, [])

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Administrationseinstellungen
            </Typography>

            <form>
                <StaticBox title='Berechnungsparameter'>
                    <Grid container spacing={2}>
                        {calculationParametersData ? (
                            Object.entries(calculationParametersData)
                                .filter((value => value[0] != "id" && value[0] != "documentId" && value[0] != "createdAt" && value[0] != "publishedAt" && value[0] != "updatedAt"))
                                .map(([key, value], index) => (
                                    <Grid size={{ xs: 12, sm: 6, md: 6 }} key={index}>
                                        <Tooltip title={`Gebe einen Wert für "${key.replace(/_/g, " ")}" ein`}>
                                            <TextField
                                                type="string"
                                                name={key}
                                                label={key.replace(/_/g, " ").replace(/\b\w/g, char => char.toUpperCase())}
                                                fullWidth
                                                value={value}
                                                disabled
                                                InputLabelProps={{
                                                    shrink: true,
                                                }}
                                            />
                                        </Tooltip>
                                    </Grid>
                                ))
                        ) : (
                            <p>Loading...</p>
                        )}
                    </Grid>
                </StaticBox>

                <Box display="flex" justifyContent="flex-end" sx={{ paddingTop: 2 }}>
                    <Grid container spacing={2}>

                        <Tooltip title="Einstellungen sichern.">
                            <Button
                                type="submit"
                                color="secondary"
                                variant="contained"
                                onClick={handleSubmit}
                                disabled>
                                Sichern
                            </Button>
                        </Tooltip>

                    </Grid>
                </Box>
            </form>
        </Box>
    );
}
