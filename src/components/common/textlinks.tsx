import { Box, Divider, ListItem } from '@mui/material';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import NextLink from 'next/link';
import React, { Fragment } from 'react';

interface TextLinkGroupProps {
  links: { label: string; href: string }[];
}

const TextLinkGroup: React.FC<TextLinkGroupProps> = ({ links }) => {
  return (
    <Box width="100%" component="nav" display="flex" justifyContent="center">
      <Stack
        direction="row"
        component="ul"
        spacing={1}
        alignItems="center"
        display="flex"
        flexWrap="wrap"
      >
        {links.map((link, index) => (
          <Fragment key={link.href}>
            <ListItem component="li">
              <Link
                component={NextLink}
                href={link.href}
                underline="hover"
                color="var(--footer-text-color)"
                variant="body1"
              >
                {link.label}
              </Link>
            </ListItem>
            {index < links.length - 1 && (
              <Divider
                orientation="vertical"
                variant="middle"
                flexItem
                key={link.href + 's'}
              />
            )}
          </Fragment>
        ))}
      </Stack>
    </Box>
  );
};

export default TextLinkGroup;
