// src/components/Tooltip.tsx
import React, { useState, useEffect } from 'react';

interface TooltipProps {
    text: string;
    children: React.ReactNode;
    delay?: number; 
}

const Tooltip: React.FC<TooltipProps> = ({ text, children, delay = 500 }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isHovered, setIsHovered] = useState(false);

    useEffect(() => {
        let timer: NodeJS.Timeout | null = null;

        if (isHovered) {
            timer = setTimeout(() => {
                setIsVisible(true);
            }, delay);
        } else {
            if (timer) {
                clearTimeout(timer);
            }
            setIsVisible(false);
        }

        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [isHovered, delay]);

    return (
        <div
            className="relative inline-block"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {children}
            {isVisible && (
                <div className="absolute bottom-full mb-2 p-2 z-10 text-sm text-white bg-alpha_gray/50 backdrop-blur-md rounded shadow-lg">
                    {text.split("<br>").map(line => <div key={line}>{line}</div>)}
                </div>
            )}
        </div>
    );
};

export default Tooltip;