import { But<PERSON>, Card, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { TypeOfDocument, type UiDocument } from './documentsList';

interface Props {
  document: UiDocument;
}

export function DocumentsTile(props: Props) {
  const t = useTranslations();
  return (
    <Card
      sx={{
        display: 'flex',
        flexShrink: 0,
      }}
    >
      <Stack
        direction="row"
        display="flex"
        flexWrap="wrap"
        justifyContent="center"
        alignContent="center"
      >
        <Stack
          direction="column"
          display="flex"
          flexWrap="wrap"
          justifyContent="center"
          alignContent="center"
        >
          <Typography fontWeight={700}>{props.document.name}</Typography>
        </Stack>

        {props.document.type === TypeOfDocument.Attachment && (
          <Button sx={{ marginLeft: 'auto' }}>
            {t('document_list.buttons.download')}
          </Button>
        )}
        {props.document.type === TypeOfDocument.Questionnaire && (
          <Button sx={{ marginLeft: 'auto' }}>
            {t('document_list.buttons.open')}
          </Button>
        )}
      </Stack>
    </Card>
  );
}
