'use client';
import { Button, Stack, Typography } from '@mui/material';
import { type Attachment, type Questionnaire } from '@prisma/client';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { DocumentsTile } from './documentTile';

interface Props {
  documents: UiDocument[];
}

export function DocumentsList(props: Props) {
  const t = useTranslations();
  return (
    <Stack
      direction="column"
      sx={{ width: '600px', maxHeight: '100%' }}
      gap={3}
    >
      <Typography variant="h5" fontWeight={700} color="var(--teal)">
        {t('document_list.documents_single_or_plural', {
          count: props.documents.length,
        })}
      </Typography>
      <Stack
        display="flex"
        direction="column"
        gap={1}
        sx={{
          overflowY: 'auto',
        }}
      >
        {props.documents.map((doc, i) => (
          <DocumentsTile document={doc} key={`${i}`} />
        ))}
      </Stack>
    </Stack>
  );
}

export enum TypeOfDocument {
  Attachment = 'Attachment',
  Questionnaire = 'Questionnaire',
}

export interface UiDocument {
  type: TypeOfDocument;
  document: Questionnaire | Attachment;
  name: string;
}

function CreateDocument(): UiDocument {
  const type =
    Math.random() < 0.5
      ? TypeOfDocument.Attachment
      : TypeOfDocument.Questionnaire;
  if (type === TypeOfDocument.Attachment) {
    const attachment: Attachment = {
      attachmentId: 0,
      caseId: null,
      messageId: null,
      fileName: 'Zahnbehandlungen',
      filePath: crypto.randomUUID(),
      uploadedAt: new Date(),
      uploadedBy: 0,
      riskPreRequestId: null,
      status: null,
      applicationId: null,
      linkedAttachmentId: null,
      documentType: null,
    };
    return { type: type, document: attachment, name: 'Zahnbehandlungen' };
  }
  const questionnaire: Questionnaire = {
    type: 'Zusatzfragen: Psychotherapeutische Behandlungen',
    questionnaireId: 0,
    formId: 'dummy-form-id',
    createdAt: new Date(),
    status: '',
    answersJson: null,
    caseCaseId: null,
  };
  return {
    type: type,
    document: questionnaire,
    name: 'Zusatzfragen: Psychotherapeutische Behandlungen:',
  };
}

export function DocumentsListTest() {
  const [documents, setDocuments] = useState<UiDocument[]>([
    CreateDocument(),
    CreateDocument(),
    CreateDocument(),
    CreateDocument(),
  ]);
  return (
    <Stack
      direction="column"
      sx={{ maxHeight: 'calc(100% - 40px)', padding: 5 }}
      gap={1}
    >
      <Stack direction="row" gap={1}>
        <Button
          onClick={() => {
            setDocuments((prev) => [...prev, CreateDocument()]);
          }}
        >
          Add document
        </Button>
        <Button
          onClick={() => {
            setDocuments((prev) =>
              prev.filter((_, index) => index !== prev.length - 1)
            );
          }}
        >
          remove document
        </Button>
      </Stack>
      <DocumentsList documents={documents}></DocumentsList>
    </Stack>
  );
}
