// src/components/CustomerList.tsx
import React, { useEffect, useCallback, useState } from 'react';
import { useRouter } from 'next/router';

import ContractSelectionModal from './modal/ContractSelectionModal';

import { formatField } from '../utils/keyFormatter';
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Typography,
    CircularProgress,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Chip,
    SelectChangeEvent,
    Tooltip,
    IconButton,
    Box
} from '@mui/material';

import {
    Edit as EditIcon,
    PersonAddAlt1 as PersonAddAlt1Icon,
    NoteAdd as NoteAddIcon,
    Download as DownloadIcon
} from "@mui/icons-material";

interface Customer {
    documentId: string;
    salutation: string;
    name_prefix: string;
    first_name: string;
    last_name: string;
    street: string;
    house_number: string;
    postal_code: string;
    city: string;
    email: string;
    id: number;
    customer_number: string;
    agency_number: string;
}

interface PaginationMeta {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

const predefinedFilterValues: Record<string, string[]> = {};

export default function CustomerList() {
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(10);
    const [meta, setMeta] = useState<PaginationMeta | null>(null);
    const [filters, setFilters] = useState<Record<string, string>>({});
    const [currentField, setCurrentField] = useState<string>('customer_number');
    const [currentValue, setCurrentValue] = useState<string>('');
    const [sortField, setSortField] = useState<string>('last_name');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const [initialized, setInitialized] = useState(false);
    const [contractPickerIsVisible, setContractPickerIsVisible] = useState(false);
    const [selectedCustomerNumber, setSelectedCustomerNumber] = useState<string | null>(null);
    const [showButtons, setShowButtons] = useState<'contract' | 'offer' | null>(null);
    const [isExporting, setIsExporting] = useState(false);

    const router = useRouter();

    useEffect(() => {
        const { query } = router;

        const initialPage = query.page ? parseInt(query.page as string, 10) : 1;
        const initialLimit = query.limit ? parseInt(query.limit as string, 10) : 10;

        const initialFilters = query.filters
            ? JSON.parse(query.filters as string)
            : {};

        const initialSortField = query.sortField as string || 'last_name';
        const initialSortDirection = (query.sortDirection as 'asc' | 'desc') || 'asc';

        setPage(initialPage);
        setLimit(initialLimit);
        setFilters(initialFilters);
        setSortField(initialSortField);
        setSortDirection(initialSortDirection);

        setInitialized(true);
    }, []);

    useEffect(() => {
        if (!initialized) return;

        const cleanedFilters: Record<string, string> = {};
        for (const [key, value] of Object.entries(filters)) {
            if (value !== "") {
                cleanedFilters[key] = value;
            }
        }

        const query: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
            sortField,
            sortDirection,
        };

        if (Object.keys(cleanedFilters).length > 0) {
            query.filters = JSON.stringify(cleanedFilters);
        }

        router.replace(
            {
                pathname: router.pathname,
                query,
            },
            undefined,
            { shallow: true }
        );
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const fetchCustomers = async () => {
        setLoading(true);
        try {
            const offset = (page - 1) * limit;
            const response = await fetch(
                `/api/customer?limit=${limit}&offset=${offset}&filters=${JSON.stringify(filters)}&sortField=${sortField}&sortDirection=${sortDirection}`,
                {
                    method: 'GET',
                    headers: { Authorization: `Bearer ${localStorage.getItem('jwt') || ''}` },
                }
            );

            if (!response.ok) {
                throw new Error('Failed to fetch customers');
            }

            const data = await response.json();
            setCustomers(data.customers);
            setMeta(data.meta);
        } catch (error) {
            console.error('Error fetching customers:', error);
            setError('Fehler beim Laden der Daten');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!initialized) return;
        fetchCustomers();
    }, [page, limit, filters, sortField, sortDirection, initialized]);


    const handleAddFilter = () => {
        if (currentField && currentValue) {
            setFilters((prev) => ({ ...prev, [currentField]: currentValue }));
            setCurrentValue('');
        }
    };

    const handleRemoveFilter = (field: string) => {
        setFilters((prev) => {
            const updated = { ...prev };
            delete updated[field];
            return updated;
        });
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
        setPage(value);
    };

    const handleLimitChange = (event: SelectChangeEvent<number>) => {
        const newLimit = Number(event.target.value);
        setLimit(newLimit);
        setPage(1);
    };

    const handleDetailsClick = (customerNumber: string, event: React.MouseEvent) => {
        const url = `/customer/${customerNumber}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleEditClick = (customerNumber: string, event: React.MouseEvent) => {
        const url = `/customer/edit/${customerNumber}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleAddClick = (event: React.MouseEvent) => {
        const url = `/customer/new`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const handleExportCsv = useCallback(async () => {
        try {
            setIsExporting(true);
            const token = localStorage.getItem('jwt');
            if (!token) {
                alert("Kein Token vorhanden");
                return;
            }

            const query = new URLSearchParams({
                sortField,
                sortDirection,
                filters: JSON.stringify(filters),
            });

            const response = await fetch(`/api/customer/export-csv?${query.toString()}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                throw new Error("CSV Export fehlgeschlagen");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'kundenliste.csv';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            console.error('CSV Download Error:', err);
            alert("Export fehlgeschlagen");
        } finally {
            setIsExporting(false);
        }
    }, [filters, sortField, sortDirection]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (error) {
        return (
            <Typography variant="h6" color="error" align="center">
                Fehler: {error}
            </Typography>
        );
    }

    return (
        <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
            <Typography variant="h4" align="center" gutterBottom color="primary">
                Kundenliste
            </Typography>

            <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                    <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                        <InputLabel>Filterkriterium</InputLabel>
                        <Select
                            value={currentField}
                            onChange={(e) => setCurrentField(e.target.value)}
                            label="Filterkriterium"
                        >
                            <MenuItem value="first_name">Vorname</MenuItem>
                            <MenuItem value="last_name">Nachname</MenuItem>
                            <MenuItem value="street">Straße</MenuItem>
                            <MenuItem value="postal_code">Postleitzahl</MenuItem>
                            <MenuItem value="city">Ort</MenuItem>
                            <MenuItem value="customer_number">Kundennummer</MenuItem>
                            {localStorage.getItem("is_admin") == "true" && <MenuItem value="agency_number">Agenturnummer</MenuItem>}
                        </Select>
                    </FormControl>

                    {predefinedFilterValues[currentField] ? (
                        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                            <InputLabel>Filterwert</InputLabel>
                            <Select
                                value={currentValue}
                                onChange={(e) => setCurrentValue(e.target.value as string)}
                                label="Filterwert"
                            >
                                {predefinedFilterValues[currentField].map((value) => (
                                    <MenuItem key={value} value={value}>
                                        {value}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    ) : (
                        <TextField
                            label="Filterwert"
                            variant="outlined"
                            size="small"
                            value={currentValue}
                            onChange={(e) => setCurrentValue(e.target.value)}
                            sx={{ width: 200 }}
                        />
                    )}

                    <Button variant="contained" color="primary" onClick={handleAddFilter}>
                        Filtern
                    </Button>
                    <div style={{ flexGrow: 1 }} />
                    <Tooltip title="CSV exportieren">
                        <IconButton
                            onClick={handleExportCsv}
                            color="primary"
                            style={{ }}
                            disabled={isExporting}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title="Kunde anlegen">
                        <IconButton
                            onClick={(event) => handleAddClick(event)}
                            color="primary"
                            style={{ marginRight: 15 }}
                        >
                            <PersonAddAlt1Icon />
                        </IconButton>
                    </Tooltip>

                </div>

                <div>
                    {Object.entries(filters).map(([field, value]) => (
                        <Chip
                            key={field}
                            label={`${formatField(field)}: ${value}`}
                            onDelete={() => handleRemoveFilter(field)}
                            color="primary"
                            sx={{ marginRight: '0.5rem', marginBottom: '0.5rem', borderRadius: 0 }}
                        />
                    ))}
                </div>
            </div>

            <TableContainer component={Paper} sx={{ marginTop: 4 }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('last_name')}
                            >
                                Name {sortField === 'last_name' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('postal_code')}
                            >
                                Adresse {sortField === 'postal_code' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('customer_number')}
                            >
                                Kundennummer {sortField === 'customer_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>
                            {localStorage.getItem("is_admin") == "true" && <TableCell
                                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                onClick={() => handleSort('agency_number')}
                            >
                                Agenturnummer {sortField === 'agency_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                            </TableCell>}
                            <TableCell align="right" sx={{ fontWeight: 'bold' }}>Aktionen</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {customers.map((customer) => (
                            <TableRow
                                key={customer.documentId}
                                hover
                                sx={{ cursor: 'pointer' }}
                                onClick={(event) => handleDetailsClick(customer.customer_number, event)}
                            >
                                <TableCell>
                                    {customer.salutation} {customer.name_prefix && `${customer.name_prefix} `}{customer.first_name} {customer.last_name}
                                </TableCell>
                                <TableCell>
                                    {customer.street} {customer.house_number}, {customer.postal_code} {customer.city}
                                </TableCell>
                                <TableCell>{customer.customer_number}</TableCell>
                                {localStorage.getItem("is_admin") === "true" && (
                                    <TableCell>{customer.agency_number}</TableCell>
                                )}
                                <TableCell align="right">
                                    <Tooltip title="Vertrag anlegen">
                                        <IconButton
                                            onClick={(event) => {
                                                event.stopPropagation();
                                                setShowButtons('contract');
                                                setSelectedCustomerNumber(customer.customer_number);
                                                setContractPickerIsVisible(true);
                                            }}
                                            color="primary"
                                        >
                                            <NoteAddIcon />
                                        </IconButton>
                                    </Tooltip>
                                    <Tooltip title="Kunde bearbeiten">
                                        <IconButton
                                            onClick={(event) => {
                                                event.stopPropagation();
                                                handleEditClick(customer.customer_number, event);
                                            }}
                                            color="secondary"
                                        >
                                            <EditIcon />
                                        </IconButton>
                                    </Tooltip>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>

                </Table>
            </TableContainer>

            {meta && (
                <Pagination
                    count={meta.pageCount}
                    page={page}
                    onChange={handlePageChange}
                    sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
                />
            )}
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                    <InputLabel id="results-per-page-label">Ergebnisse pro Seite</InputLabel>
                    <Select<number>
                        labelId="results-per-page-label"
                        value={limit}
                        onChange={handleLimitChange}
                        label="Ergebnisse pro Seite"
                    >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                    </Select>
                </FormControl>
            </div>
            <ContractSelectionModal
                open={contractPickerIsVisible}
                onClose={() => setContractPickerIsVisible(false)}
                customerNumber={selectedCustomerNumber || ''}
                mode={showButtons}
            />
        </Box>
    );
}
