// src/components/CustomerList.tsx
import {
  Download as DownloadIcon,
  Edit as EditIcon,
  NoteAdd as NoteAddIcon,
  PersonAddAlt1 as PersonAddAlt1Icon,
} from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { apiFetch } from '@/utils/apiFetch';
import { useTableQuery } from '@/utils/useTableQuery';

import { customerFieldConfig } from './filters/customerFieldConfig';
import { FilterBuilder } from './filters/FilterBuilder';
import { serializeFilters } from './filters/url';
import ContractSelectionModal from './modal/ContractSelectionModal';

export default function CustomerList() {
  const [isExporting, setIsExporting] = useState(false);

  const [contractPickerIsVisible, setContractPickerIsVisible] = useState(false);
  const [selectedCustomerNumber, setSelectedCustomerNumber] = useState<
    string | null
  >(null);
  const [mode, setMode] = useState<'contract' | 'offer' | null>(null);

  const session = useSession();

  const router = useRouter();

  const table = useTableQuery<any>({
    endpoint: '/api/customers',
    defaultSortField: 'lastName',
    accessToken: session.data?.accessToken,
    enabled: session.status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.items ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: customers,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  // ---- UI handlers ----
  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => setPage(value);

  const handleShowDetails = (
    customerNumber: string,
    event: React.MouseEvent
  ) => {
    const url = `/customer/${customerNumber}`;
    if (event.ctrlKey || event.metaKey) window.open(url, '_blank');
    else router.push(url);
  };

  const handleEditClick = (customerNumber: string, event: React.MouseEvent) => {
    const url = `/customer/edit/${customerNumber}`;
    if (event.ctrlKey || event.metaKey) window.open(url, '_blank');
    else router.push(url);
  };

  const handleAddCustomer = (event: React.MouseEvent) => {
    const url = `/customer/new`;
    if (event.ctrlKey || event.metaKey) window.open(url, '_blank');
    else router.push(url);
  };

  const handleCreateContract = (
    customerNumber: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    setMode('contract');
    setSelectedCustomerNumber(customerNumber);
    setContractPickerIsVisible(true);
  };

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);

      const query = new URLSearchParams({
        sortField,
        sortDirection,
        ...serializeFilters(filters),
      });

      const response = await apiFetch(
        `/api/customers/export-csv?${query.toString()}`,
        {
          raw: true,
        }
      );

      if (!response.ok) throw new Error('CSV Export fehlgeschlagen');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'kundenliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  // ---- UI ----
  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  const isAdmin =
    typeof window !== 'undefined' &&
    session.data?.roles.includes('asevo-admin');

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Kundenliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        <FilterBuilder
          fields={customerFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="username"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Kunde anlegen">
            <IconButton
              onClick={handleAddCustomer}
              color="primary"
              sx={{ ml: 1 }}
            >
              <PersonAddAlt1Icon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      {/* Table */}
      <TableContainer component={Paper} sx={{ mt: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('lastName')}
              >
                Name{' '}
                {sortField === 'lastName' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('postalCode')}
              >
                Adresse{' '}
                {sortField === 'postalCode' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('customerNumber')}
              >
                Kundennummer{' '}
                {sortField === 'customerNumber' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              {isAdmin && (
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('agencyNumber')}
                >
                  Agenturnummer{' '}
                  {sortField === 'agencyNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
              )}
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('email')}
              >
                E-Mail{' '}
                {sortField === 'email' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                Aktionen
              </TableCell>
            </TableRow>
          </TableHead>

          <TableBody>
            {customers.map((c) => (
              <TableRow
                key={c.documentId}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={(event) => handleShowDetails(c.customerNumber!, event)}
              >
                <TableCell>
                  {c.salutation ? c.salutation + ' ' : ''}
                  {c.namePrefix ? c.namePrefix + ' ' : ''}
                  {c.firstName} {c.lastName}
                </TableCell>
                <TableCell>
                  {[c.street, c.houseNumber].filter(Boolean).join(' ')}
                  {', '}
                  {[c.postalCode, c.city].filter(Boolean).join(' ')}
                </TableCell>
                <TableCell>{c.customerNumber}</TableCell>
                {isAdmin && <TableCell>{c.agencyNumber}</TableCell>}
                <TableCell>{c.email}</TableCell>

                <TableCell align="right" onClick={(e) => e.stopPropagation()}>
                  <Tooltip title="Vertrag anlegen">
                    <IconButton
                      onClick={(e) =>
                        handleCreateContract(c.customerNumber!, e)
                      }
                      color="primary"
                    >
                      <NoteAddIcon />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Kunde bearbeiten">
                    <IconButton
                      onClick={(e) => handleEditClick(c.customerNumber!, e)}
                      color="secondary"
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={(_e, v) => handlePageChange(_e as any, v)}
          sx={{ mt: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <ContractSelectionModal
        open={contractPickerIsVisible}
        onClose={() => setContractPickerIsVisible(false)}
        customerNumber={selectedCustomerNumber || ''}
        mode={mode}
      />
    </Box>
  );
}
