// src/components/LoginForm.tsx
'use client';

import {
  <PERSON>,
  Button,
  CardContent,
  Link as MuiLink,
  Typography,
} from '@mui/material';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { signIn } from 'next-auth/react'; // v5 client
// If you're fully on the App Router v5 helpers you can also import from "@/auth" and call signIn(),
// but next-auth/react is fine for client components.

export default function LoginForm() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';

  const handleKeycloak = async () => {
    // Start Keycloak OAuth; come back to /dashboard when done
    await signIn('keycloak', { callbackUrl: callbackUrl });
  };

  return (
    <>
      <Box display="flex" justifyContent="center" mb={2}>
        <Link href="/" passHref>
          <Image src="/images/logo.svg" alt="Logo" width={120} height={120} />
        </Link>
      </Box>

      <Typography variant="h5" textAlign="center" gutterBottom>
        Willkommen zurück
      </Typography>
      <Typography variant="body2" textAlign="center" mb={2}>
        Melden Sie sich über Keycloak an
      </Typography>

      <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Button onClick={handleKeycloak} variant="contained" fullWidth>
          Mit Keycloak einloggen
        </Button>

        <MuiLink
          component={Link}
          href="/register"
          underline="hover"
          textAlign="center"
          mt={1}
        >
          Noch kein Konto? Jetzt registrieren
        </MuiLink>
      </CardContent>
    </>
  );
}
