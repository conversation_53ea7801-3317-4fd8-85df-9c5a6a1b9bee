// src/components/LoginForm.tsx
import React, { useState } from 'react';
import {
  Box, Button, TextField, Typography, CardContent,
  FormControlLabel, Checkbox, Link as MuiLink
} from '@mui/material';
import Link from 'next/link';
import { useRouter } from 'next/router';
import Image from 'next/image';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ identifier: email, password }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || 'Login fehlgeschlagen');
      }

      const data = await response.json();
      localStorage.setItem('jwt', data.jwt);
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.message);
    }
  };

  return (
    <>
      <Box display="flex" justifyContent="center" mb={2}>
        <Link href="/" passHref>
          <Image src="/images/logo.svg" alt="Logo" width={120} height={120} />
        </Link>
      </Box>
      <Typography variant="h5" textAlign="center" gutterBottom>
        Willkommen zurück
      </Typography>
      <Typography variant="body2" textAlign="center" mb={2}>
        Melden Sie sich mit Ihren Zugangsdaten an
      </Typography>
      <form onSubmit={handleLogin} noValidate>
        <CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            label="Emailadresse"
            type="email"
            fullWidth
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <TextField
            label="Passwort"
            type="password"
            fullWidth
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          {error && (
            <Typography color="error" variant="body2">
              {error}
            </Typography>
          )}
          <FormControlLabel
            control={<Checkbox defaultChecked color="primary" />}
            label="Angemeldet bleiben"
          />
          <Button type="submit" variant="contained" fullWidth>
            Einloggen
          </Button>
          <MuiLink
            component={Link}
            href="/register"
            underline="hover"
            textAlign="center"
            mt={1}
          >
            Noch kein Konto? Jetzt registrieren
          </MuiLink>
        </CardContent>
      </form>
    </>
  );
}
