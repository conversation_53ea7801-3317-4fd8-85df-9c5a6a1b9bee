// src/components/filters/url.ts
import { type FieldConfig, type FilterRow, type Op } from './types';

const BETWEEN_DELIM = ',';

/** field__op=value (always strings for the URL) */
export function serializeFilters(rows: FilterRow[]): Record<string, string> {
  const out: Record<string, string> = {};
  for (const { field, op, value } of rows) {
    if (op !== 'null' && op !== 'notNull') {
      if (value == null || String(value).trim() === '') continue;
    }
    const key = `${field}__${op}`;
    out[key] = op === 'null' || op === 'notNull' ? 'true' : String(value);
  }
  return out;
}

/** Auto-coerce select values back to number when the field’s options are numeric.
 *  Pass the fields config so we can detect how to coerce.
 */
export function deserializeFilters(
  query: Record<string, any>,
  fields?: Record<string, FieldConfig>
): FilterRow[] {
  const rows: FilterRow[] = [];
  for (const [k, v] of Object.entries(query)) {
    if (['page', 'limit', 'sortField', 'sortDirection'].includes(k)) continue;
    const [field, op = 'eq'] = k.split('__');
    const raw = Array.isArray(v) ? v[0] : String(v ?? '');

    let value: string | number = raw;

    const cfg = fields?.[field];

    if (cfg?.valueKind === 'select') {
      const coerceMode = cfg.coerceValueAs ?? 'auto';
      const options = cfg.options ?? [];

      const allNumericOptions =
        options.length > 0 && options.every((o) => typeof o.value === 'number');

      const shouldNumber =
        coerceMode === 'number' || (coerceMode === 'auto' && allNumericOptions);

      if (op === 'between' && raw.includes(BETWEEN_DELIM)) {
        // for between we keep the raw joined string for the URL model,
        // but FilterBuilder will split & coerce when displaying chips.
        // If you prefer, you could return two rows; here we keep one row.
        value = raw; // leave joined; the UI will parse on render
      } else if (shouldNumber && raw !== '') {
        const num = Number(raw);
        value = Number.isFinite(num) ? num : raw;
      }
    }

    rows.push({ field, op: op as Op, value });
  }
  return rows;
}
