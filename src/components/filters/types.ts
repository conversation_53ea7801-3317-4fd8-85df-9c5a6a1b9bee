// src/components/filters/types.ts
export type Op =
    | 'eq'
    | 'not'
    | 'contains_i'
    | 'startsWith_i'
    | 'endsWith_i'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'in'
    | 'between'
    | 'null'
    | 'notNull';

export type FilterRow = { field: string; op: Op; value?: string | number | boolean };

export type ValueKind = 'text' | 'number' | 'select' | 'boolean' | 'date';

export type FieldConfig = {
    /** Label for UI */
    label: string;
    /** Which operators are allowed for this field */
    ops: Op[];
    /** How to render the value input */
    valueKind: ValueKind;
    /** Optional: predefined options for select dropdown */
    options?: { label: string; value: string | number }[];
    /** Optional: placeholder for the value input */
    placeholder?: string;
    /** optional: force the value type for this field */
    coerceValueAs?: 'auto' | 'string' | 'number' | 'boolean';
};

// A small operator label map for the UI
export const OpLabels: Record<Op, string> = {
    eq: '=',
    not: '≠',
    contains_i: 'enthält',
    startsWith_i: 'beginnt mit',
    endsWith_i: 'endet mit',
    gt: '>',
    gte: '≥',
    lt: '<',
    lte: '≤',
    in: 'in',
    between: 'zwischen',
    null: 'ist leer',
    notNull: 'nicht leer',
};

export interface PaginationMeta {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}
