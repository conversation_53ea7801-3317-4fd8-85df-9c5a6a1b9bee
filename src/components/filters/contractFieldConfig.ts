// src/components/filters/contractFilterConfig.ts
import { type FieldConfig } from './types';

export const contractFieldConfig: Record<string, FieldConfig> = {
    contractNumber: {
        label: 'Vertragsnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    customerNumber: {
        label: 'Kundennummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    contractType: {
        label: 'Vertragsart',
        ops: ['eq', 'not', 'in'],
        valueKind: 'select',
        options: [
            { label: 'Hausrat', value: 'hausrat' },
            { label: 'Wohngebäude', value: 'wohngebaeude' },
            { label: 'Tierhalterhaftpflicht', value: 'tierhalterhaftpflicht' },
            { label: 'Privathaftpflicht', value: 'privathaftpflicht' },
            {
                label: 'Haus- und Grundbesitzerhaftpflicht',
                value: 'haus_und_grundbesitzerhaftpflicht',
            },
            { label: 'Bauleistung', value: 'bauleistung' },
            { label: 'Bauherrenhaftpflicht', value: 'bauherrenhaftpflicht' },
            { label: 'Geschäftsversicherung', value: 'geschaeftsversicherung' },
            { label: 'Gebäudeversicherung', value: 'gebaeudeversicherung' },
            { label: 'Betriebshaftpflicht', value: 'betriebshaftpflicht' },
            { label: 'Unfallversicherung', value: 'unfallversicherung' },
        ],
    },
    isOffer: {
        label: 'Typ',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Vertrag', value: 'false' },
            { label: 'Angebot', value: 'true' },
        ],
        coerceValueAs: 'boolean'
    },
    activeStatus: {
        label: 'Status',
        ops: ['eq', 'not', 'in'],
        valueKind: 'select',
        options: [
            { label: 'Aktiv', value: 'ACTIVE' },
            { label: 'Inaktiv', value: 'INACTIVE' },
            { label: 'Gekündigt', value: 'CANCELED' },
            { label: 'Gelöscht', value: 'DELETED' },
        ],
    },
    insuranceStartDate: {
        label: 'Versicherungsbeginn',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
    createdAt: {
        label: 'Erstellt am',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
    updatedAt: {
        label: 'Aktualisiert am',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
};
