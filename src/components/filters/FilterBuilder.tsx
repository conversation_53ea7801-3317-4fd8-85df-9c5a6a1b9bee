// src/components/filters/FilterBuilder.tsx
import {
  Box,
  Button,
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  TextField,
} from '@mui/material';
import React, { useMemo, useState } from 'react';

import { type FieldConfig, type FilterRow, type Op, OpLabels } from './types';

type Props = {
  fields: Record<string, FieldConfig>;
  filters: FilterRow[];
  onChange: (rows: FilterRow[]) => void;
  initialField?: string;
};

const BETWEEN_DELIM = ',';

export function FilterBuilder({
  fields,
  filters,
  onChange,
  initialField,
}: Props) {
  const fieldKeys = useMemo(() => Object.keys(fields), [fields]);
  const defaultField = initialField ?? fieldKeys[0] ?? '';

  const [currentField, setCurrentField] = useState<string>(defaultField);
  const currentFieldCfg = fields[currentField];

  const [currentOp, setCurrentOp] = useState<Op>(
    currentFieldCfg?.ops?.[0] ?? 'contains_i'
  );
  const [currentValue, setCurrentValue] = useState<string | number>('');
  const [currentValueTo, setCurrentValueTo] = useState<string | number>('');

  React.useEffect(() => {
    if (!currentFieldCfg) return;
    if (!currentFieldCfg.ops.includes(currentOp))
      setCurrentOp(currentFieldCfg.ops[0]);
    setCurrentValue('');
    setCurrentValueTo('');
  }, [currentField]); // eslint-disable-line

  React.useEffect(() => {
    if (currentOp !== 'between' && currentValueTo !== '') setCurrentValueTo('');
  }, [currentOp, currentValueTo]);

  function addFilter() {
    if (!currentField) return;

    if (currentOp === 'null' || currentOp === 'notNull') {
      // ok without a value
    } else if (currentOp === 'between') {
      if (
        String(currentValue).trim() === '' ||
        String(currentValueTo).trim() === ''
      )
        return;
    } else {
      if (String(currentValue).trim() === '') return;
    }

    const next = filters.filter(
      (r) => !(r.field === currentField && r.op === currentOp)
    );

    let value: string | number | boolean;
    if (currentOp === 'between') {
      // keep as strings for UI; between doesn't make sense for booleans anyway
      value = `${String(currentValue).trim()}${BETWEEN_DELIM}${String(currentValueTo).trim()}`;
    } else {
      // coerce here (boolean/number/string as requested by field config)
      value = coerceForField(currentValue, currentFieldCfg);
    }

    next.push({ field: currentField, op: currentOp, value });
    onChange(next);
    setCurrentValue('');
    setCurrentValueTo('');
  }

  function removeFilter(field: string, op?: Op) {
    onChange(
      filters.filter((r) => !(r.field === field && (op ? r.op === op : true)))
    );
  }

  // helper: for selects, show human label on chips
  function formatValueForChip(f: FilterRow) {
    const cfg = fields[f.field];
    if (f.op === 'null' || f.op === 'notNull') return '';

    if (f.op === 'between') {
      const [fromRaw = '', toRaw = ''] = String(f.value ?? '').split(
        BETWEEN_DELIM
      );
      const display = (raw: string) => {
        if (cfg?.valueKind === 'select' && cfg.options?.length) {
          const opt = cfg.options.find((o) => String(o.value) === raw);
          return opt ? opt.label : raw;
        }
        return raw;
      };
      return `${display(fromRaw)} – ${display(toRaw)}`;
    }

    if (cfg?.valueKind === 'select' && cfg.options?.length) {
      const stored = f.value; // could be boolean now
      const opt = cfg.options.find((o) => String(o.value) === String(stored));
      if (opt) return opt.label;
    }

    return String(f.value ?? '');
  }

  function coerceForField(raw: string | number, cfg?: FieldConfig) {
    const mode = cfg?.coerceValueAs ?? 'auto';
    if (mode === 'boolean') {
      // UI gives us 'true' | 'false' strings; turn into boolean
      if (raw === 'true') return true;
      if (raw === 'false') return false;
      // if somehow not a string, leave as-is
      return raw;
    }
    if (mode === 'number') {
      const n = Number(raw);
      return Number.isFinite(n) ? n : raw;
    }
    if (mode === 'string') {
      return String(raw);
    }

    // auto: if all options numeric, coerce numeric (existing behavior)
    if (
      cfg?.options?.length &&
      cfg.options.every((o) => typeof o.value === 'number')
    ) {
      const n = Number(raw);
      return Number.isFinite(n) ? n : raw;
    }
    return raw;
  }

  function renderValueInput() {
    if (!currentFieldCfg) return null;
    if (currentOp === 'null' || currentOp === 'notNull') return null;

    const kind = currentFieldCfg.valueKind;
    const type =
      kind === 'number' ? 'number' : kind === 'date' ? 'date' : 'text';

    if (currentOp === 'between') {
      if (kind === 'select' && currentFieldCfg.options) {
        return (
          <>
            <FormControl size="small" sx={{ width: 180 }}>
              <InputLabel>Von</InputLabel>
              <Select
                label="Von"
                value={currentValue}
                onChange={(e) => setCurrentValue(e.target.value)} // ← no string cast
              >
                {currentFieldCfg.options.map((opt) => (
                  <MenuItem key={String(opt.value)} value={opt.value}>
                    {opt.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ width: 180 }}>
              <InputLabel>Bis</InputLabel>
              <Select
                label="Bis"
                value={currentValueTo}
                onChange={(e) => setCurrentValueTo(e.target.value)} // ← no string cast
              >
                {currentFieldCfg.options.map((opt) => (
                  <MenuItem key={String(opt.value)} value={opt.value}>
                    {opt.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </>
        );
      }

      return (
        <>
          <TextField
            label="Von"
            variant="outlined"
            size="small"
            sx={{ width: 180 }}
            type={type}
            value={currentValue}
            onChange={(e) => setCurrentValue(e.target.value)}
            InputLabelProps={kind === 'date' ? { shrink: true } : undefined}
            placeholder={currentFieldCfg.placeholder}
          />
          <TextField
            label="Bis"
            variant="outlined"
            size="small"
            sx={{ width: 180 }}
            type={type}
            value={currentValueTo}
            onChange={(e) => setCurrentValueTo(e.target.value)}
            InputLabelProps={kind === 'date' ? { shrink: true } : undefined}
            placeholder={currentFieldCfg.placeholder}
          />
        </>
      );
    }

    if (kind === 'select' && currentFieldCfg.options) {
      return (
        <FormControl size="small" sx={{ width: 220 }}>
          <InputLabel>Filterwert</InputLabel>
          <Select
            label="Filterwert"
            value={currentValue}
            onChange={(e) => setCurrentValue(e.target.value)} // ← no string cast
          >
            {currentFieldCfg.options.map((opt) => (
              <MenuItem key={String(opt.value)} value={opt.value}>
                {opt.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      );
    }

    return (
      <TextField
        label="Filterwert"
        variant="outlined"
        size="small"
        sx={{ width: 220 }}
        type={type}
        value={currentValue}
        onChange={(e) => setCurrentValue(e.target.value)}
        InputLabelProps={kind === 'date' ? { shrink: true } : undefined}
        placeholder={currentFieldCfg.placeholder}
      />
    );
  }

  return (
    <Box>
      {/* Add filter row */}
      <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
        <FormControl variant="outlined" size="small" sx={{ width: 220 }}>
          <InputLabel>Filterkriterium</InputLabel>
          <Select
            value={currentField}
            label="Filterkriterium"
            onChange={(e: SelectChangeEvent<string>) =>
              setCurrentField(e.target.value)
            }
          >
            {fieldKeys.map((f) => (
              <MenuItem key={f} value={f}>
                {fields[f].label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel>Operator</InputLabel>
          <Select<Op>
            value={currentOp}
            label="Operator"
            onChange={(e) => setCurrentOp(e.target.value as Op)}
          >
            {currentFieldCfg?.ops.map((op) => (
              <MenuItem key={op} value={op}>
                {OpLabels[op]}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {renderValueInput()}

        <Button variant="contained" color="primary" onClick={addFilter}>
          Filtern
        </Button>
      </Box>

      {/* Chips */}
      <Box>
        {filters.map((f, idx) => (
          <Chip
            key={`${f.field}-${f.op}-${idx}`}
            label={`${fields[f.field]?.label ?? f.field} ${OpLabels[f.op]} ${formatValueForChip(f)}`}
            onDelete={() => removeFilter(f.field, f.op)}
            color="primary"
            sx={{ mr: 1, mb: 1 }}
          />
        ))}
      </Box>
    </Box>
  );
}
