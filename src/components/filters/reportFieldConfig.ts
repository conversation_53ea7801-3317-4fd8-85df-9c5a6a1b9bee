// src/components/filters/reportFieldConfig.ts
import { type FieldConfig } from './types';

export const reportFieldConfig: Record<string, FieldConfig> = {
    reportNumber: {
        label: 'Schadennummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    externalReportNumber: {
        label: 'Externe Schadennummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    contractNumber: {
        label: 'Vertragsnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    customerNumber: {
        label: 'Kundennummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    damageDate: {
        label: 'Schadendatum',
        ops: ['eq', 'gt', 'lt', 'between', 'not', 'null', 'notNull'],
        valueKind: 'date',
    },
};
