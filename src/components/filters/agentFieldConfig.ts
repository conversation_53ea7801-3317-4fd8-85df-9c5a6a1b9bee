// src/components/filters/agentFieldConfig.ts
import { type FieldConfig } from './types';

export const agentFieldConfig: Record<string, FieldConfig> = {
    username: {
        label: 'Benutzername',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    email: {
        label: 'E-Mail',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    companyName: {
        label: 'Agenturname',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agentNumber: {
        label: 'Maklernummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    postalCode: {
        label: 'PLZ',
        ops: ['eq', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    }
};
