// src/components/filters/agencyFieldConfig.ts
import { type FieldConfig } from './types';

export const agencyFieldConfig: Record<string, FieldConfig> = {
    agencyName: {
        label: 'Agenturname',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    isAdmin: {
        label: 'Adminaccount',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Ja', value: 'true' },
            { label: 'Nein', value: 'false' },
        ],
        coerceValueAs: 'boolean'
    },
};
