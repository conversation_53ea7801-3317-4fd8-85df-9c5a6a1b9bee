import {
    InvoiceCustomerStatusType,
    InvoiceStatusType,
    InvoiceType,
} from '@/utils/invoice/types';

import { type FieldConfig } from './types';

export const invoiceFieldConfig: Record<string, FieldConfig> = {
    invoiceNumber: {
        label: 'Rechnungsnummer',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    type: {
        label: 'Rechnungsart',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Erstprämie', value: InvoiceType.FRIST_INVOICE },
            { label: 'Nachtrag', value: InvoiceType.ADDENDUM },
            { label: 'Rechnung', value: InvoiceType.BILL },
            { label: 'Gutschrift', value: InvoiceType.CREDIT },
        ],
        coerceValueAs: 'number',
    },
    contractNumber: {
        label: 'Vertragsnummer',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    customerNumber: {
        label: 'Kundennummer',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agentNumber: {
        label: 'Maklernummer',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    dueDate: {
        label: 'Fälligkeitsdatum',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
    invoiceStatus: {
        label: 'Status',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Offen', value: InvoiceStatusType.UNBOOKED },
            { label: 'Gebucht', value: InvoiceStatusType.BOOKED },
            { label: 'Abgeschlossen', value: InvoiceStatusType.COMPLETED },
        ],
    },
    customerStatus: {
        label: 'Kundenstatus',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Offen', value: InvoiceCustomerStatusType.OPEN },
            { label: 'Errinering 1', value: InvoiceCustomerStatusType.REMINDER_1 },
            { label: 'Errinering 2', value: InvoiceCustomerStatusType.REMINDER_2 },
            { label: 'Mahnung', value: InvoiceCustomerStatusType.DEMAND },
            { label: 'Bezahlt', value: InvoiceCustomerStatusType.PAID },
            { label: 'Storniert', value: InvoiceCustomerStatusType.CANCELED },
            { label: 'Zurückerstattet', value: InvoiceCustomerStatusType.REFUNDED },
        ],
    },
};
