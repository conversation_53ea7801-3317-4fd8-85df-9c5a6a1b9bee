import { type FieldConfig } from './types';

export const customerFieldConfig: Record<string, FieldConfig> = {
    firstName: {
        label: 'Vorname',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    lastName: {
        label: 'Nachname',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    email: {
        label: 'E-Mail',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    street: {
        label: 'Straße',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    houseNumber: {
        label: 'Hausnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    postalCode: {
        label: 'PLZ',
        ops: ['eq', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    city: {
        label: 'Ort',
        ops: ['contains_i', 'startsWith_i', 'eq', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    customerNumber: {
        label: 'Kundennummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    agencyNumber: {
        label: 'Agenturnummer',
        ops: ['eq', 'contains_i', 'startsWith_i', 'not', 'null', 'notNull'],
        valueKind: 'text',
    },
    active: {
        label: 'Aktiv',
        ops: ['eq', 'not'],
        valueKind: 'select',
        options: [
            { label: 'Ja', value: 'true' },
            { label: 'Nein', value: 'false' },
        ],
    },
    createdAt: {
        label: 'Erstellt am',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
    updatedAt: {
        label: 'Aktualisiert am',
        ops: ['gte', 'lte', 'between'],
        valueKind: 'date',
    },
};
