// src/components/AgentDocuments.tsx
import DownloadIcon from '@mui/icons-material/Download';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { type Attachment } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { formatDate } from '@/utils/dateUtils';

import StaticBox from './box/StaticBox';

const AgentDocuments = () => {
  const [attachments, setAttachments] = useState<Attachment[] | null>(null);
  const session = useSession();

  useEffect(() => {
    const fetchData = async () => {
      const attachmentsData = await apiFetch<Attachment[]>(
        `/api/agents/${session.data?.agentNumber}/attachemnts`
      );

      setAttachments(attachmentsData);
    };
    fetchData();
  }, [session.data?.agentNumber]);

  const handleDownloadAttachment = async (
    fileName: string,
    documentId: string
  ) => {
    try {
      const res = await apiFetch(
        `/api/agents/${session.data?.agentNumber}/attachemnts/${documentId}`,
        {
          method: 'GET',
          raw: true,
        }
      );

      const blob = await res.blob();
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.click();

      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download Attachment', err);
    }
  };

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Persönliche Dokumente
      </Typography>
      {/* files */}
      {attachments && attachments.length > 0 && (
        <StaticBox title="Dokumente">
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Dateiname</TableCell>
                  <TableCell>Erstellt am</TableCell>
                  <TableCell>Zuletzt aktualisiert</TableCell>
                  <TableCell align="center">Aktion</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {attachments.map((attachment, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      {attachment.bucketPath!.replace(
                        `agent_files/${session.data?.agentNumber}_`,
                        ''
                      ).length > 50
                        ? `${attachment.bucketPath!.replace(`agent_files/${session.data?.agentNumber}_`, '').slice(0, 30)}...`
                        : attachment.bucketPath!.replace(
                            `agent_files/${session.data?.agentNumber}_`,
                            ''
                          )}
                    </TableCell>
                    <TableCell>{formatDate(attachment.createdAt)}</TableCell>
                    <TableCell>{formatDate(attachment.updatedAt)}</TableCell>
                    <TableCell align="center">
                      <IconButton
                        onClick={() =>
                          handleDownloadAttachment(
                            attachment.bucketPath!,
                            attachment.documentId
                          )
                        }
                        color="primary"
                      >
                        <DownloadIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </StaticBox>
      )}

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>
    </Box>
  );
};

export default AgentDocuments;
