// src/components/AgentDocuments.tsx
import { useEffect, useState } from 'react';
import { AttachmentData } from '../types';
import { ArrowDownOnSquareIcon } from '@heroicons/react/24/outline';
import { TableContainer, Paper, Table, TableHead, TableRow, TableCell, TableBody, IconButton, Button, Typography, Box, Grid2 as Grid } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import { formatDate } from '@/utils/dateUtils';
import StaticBox from './box/StaticBox';

const AgentDocuments = () => {
    const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);
    const [me, setMe] = useState<any>(null);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || ""
        const fetchData = async () => {

            const meResponse = await fetch(`/api/user/me`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })

            if (!meResponse.ok) {
                throw new Error('Failed to fetch self-data');
            }

            const meData = await meResponse.json()

            const attachmentsResponse = await fetch(`/api/file/${meData["agent_number"]}/all`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })

            if (!attachmentsResponse.ok) {
                throw new Error('Failed to fetch data');
            }

            const attachmentsData = await attachmentsResponse.json()

            setAttachments(attachmentsData)
            setMe(meData)
        }
        fetchData()
    }, []);

    if (!me) {
        return <div>Lade Daten...</div>;
    }

    const handleDownloadAttachment = async (file_name: string, documentId: string) => {
        const response = await fetch(`/api/file/${me["agent_number"]}/get/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = file_name;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download Attachment');
        }
    };

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 }, 
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Persönliche Dokumente
            </Typography>
            {/* files */}
            {attachments && attachments.length > 0 && (
                <StaticBox title='Dokumente'>
                    <TableContainer component={Paper}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>Dateiname</TableCell>
                                    <TableCell>Erstellt am</TableCell>
                                    <TableCell>Zuletzt aktualisiert</TableCell>
                                    <TableCell align="center">Aktion</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {attachments.map((attachment, index) => (
                                    <TableRow key={index}>
                                        <TableCell>
                                            {attachment.bucket_path.replace(`agent_files/${me["agent_number"]}_`, "").length > 50
                                                ? `${attachment.bucket_path.replace(`agent_files/${me["agent_number"]}_`, "").slice(0, 30)}...`
                                                : attachment.bucket_path.replace(`agent_files/${me["agent_number"]}_`, "")}
                                        </TableCell>
                                        <TableCell>{formatDate(attachment.createdAt)}</TableCell>
                                        <TableCell>{formatDate(attachment.updatedAt)}</TableCell>
                                        <TableCell align="center">

                                            <IconButton
                                                onClick={() => handleDownloadAttachment(attachment.bucket_path, attachment.documentId)}
                                                color="primary"
                                            >
                                                <DownloadIcon />
                                            </IconButton>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </StaticBox>
            )}

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>

                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>
        </Box>
    );
};

export default AgentDocuments;
