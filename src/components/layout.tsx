// src/components/layout.tsx
import React, { ReactElement, ReactNode, useState, useEffect } from 'react';
import Head from 'next/head';
import Header from './header';
import Footer from './footer'
import router from 'next/router';

interface LayoutProps {
    title: string;
    description: string;
    children: ReactNode;
}

export default function Layout({ title, description, children }: LayoutProps): ReactElement {

    const [authChecked, setAuthChecked] = useState<boolean>(false)

    useEffect(() => {

        fetch(`/api/user/me`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        })
            .then(res => {
                if (res.ok)
                    res.json().then(user => {
                        localStorage.setItem("is_admin", user["agency"]["is_admin"])
                        localStorage.setItem("userName", user['username'])
                        localStorage.setItem("agencyNumber", user['agency_number'])
                        localStorage.setItem("agent_number", user['agent_number'])
                        setAuthChecked(true)
                    }
                    )
                else {
                    localStorage.removeItem("jwt")
                    setAuthChecked(false)
                    router.push('/login');
                }
            })
    })

    return (
        authChecked == true ?
            <>
                <Head>
                    <title>{title}</title>
                    <meta name="language" content="de" />
                    <meta name="description" content={description} />
                    <meta property="og:title" content={title} />
                    <meta property="og:description" content={description} />
                </Head>
                <div>
                    <Header />
                    {children}
                    <Footer />
                </div>
            </>
            : <></>

    );
}

