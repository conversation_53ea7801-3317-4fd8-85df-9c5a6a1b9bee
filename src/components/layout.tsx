// src/components/layout.tsx
import Head from 'next/head';
import { SessionProvider } from 'next-auth/react';
import { type ReactElement, type ReactNode } from 'react';

import Footer from './footer';
import Header from './header';

interface LayoutProps {
  title: string;
  description: string;
  children: ReactNode;
}

export default function Layout({
  title,
  description,
  children,
}: LayoutProps): ReactElement {
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="language" content="de" />
        <meta name="description" content={description} />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
      </Head>
      <div>
        <SessionProvider>
          <Header />
          {children}
          <Footer />
        </SessionProvider>
      </div>
    </>
  );
}
