"use client";

import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Avatar,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import PriceChangeIcon from "@mui/icons-material/PriceChange";
import AutorenewIcon from "@mui/icons-material/Autorenew";
import StarsIcon from "@mui/icons-material/Stars";
import DescriptionIcon from "@mui/icons-material/Description";
import CheckIcon from "@mui/icons-material/Check";

const features = [
  {
    icon: <PriceChangeIcon />,
    key: "customerLanding.dashboard.feature.differenz",
  },
  {
    icon: <AutorenewIcon />,
    key: "customerLanding.dashboard.feature.update",
  },
  {
    icon: <StarsIcon />,
    key: "customerLanding.dashboard.feature.bestleistung",
  },
  {
    icon: <DescriptionIcon />,
    key: "customerLanding.dashboard.feature.besserstellung",
  },
  {
    icon: <CheckIcon />,
    key: "customerLanding.dashboard.feature.transparenz",
  },
];

export default function DashboardPreview() {
  const router = useRouter();
  const t = useTranslations();

  return (
    <Box component="section" sx={{ mb: { xs: 6, md: 10 } }}>
      <Card>
        <CardContent>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            <Box textAlign="center">
              <Avatar
                variant="rounded"
                sx={{
                  width: 72,
                  height: 72,
                  mx: "auto",
                  bgcolor: "primary.light",
                  color: "primary.contrastText",
                  mb: 2,
                }}
              >
                <StarsIcon fontSize="large" />
              </Avatar>
              <Typography variant="h2" gutterBottom>
                {t("customerLanding.dashboard.sectionTitle")}
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ maxWidth: 720, mx: "auto" }}
              >
                {t("customerLanding.dashboard.sectionDesc")}
              </Typography>
            </Box>

            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
                gap: 2,
              }}
            >
              {features.map((f) => (
                <Card
                  key={f.key}
                  variant="outlined"
                  sx={{
                    p: 2,
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    height: "100%",
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 32 }}>{f.icon}</ListItemIcon>
                  <Typography variant="body1">{t(f.key)}</Typography>
                </Card>
              ))}
            </Box>

            <Box textAlign="center" mt={2}>
              <Button
                variant="contained"
                size="large"
                onClick={() => router.push("/customer/form")}
              >
                {t("customerLanding.dashboard.cta")}
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{ ml: 2 }}
                onClick={() =>
                  window.open(
                    "/static/Highlightblatt_CIF4ALL_VGV_ba_08-2025.pdf",
                    "_blank",
                    "noopener,noreferrer"
                  )
                }
              >
                {t("customerLanding.dashboard.ctaSecondary")}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}
