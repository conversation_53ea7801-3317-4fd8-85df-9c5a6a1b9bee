"use client";

import React from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Chip,
  Divider,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import GavelIcon from "@mui/icons-material/Gavel";
import HomeRepairServiceIcon from "@mui/icons-material/HomeRepairService";
import WaterDamageIcon from "@mui/icons-material/WaterDrop";
import SecurityIcon from "@mui/icons-material/Security";
import PolicyIcon from "@mui/icons-material/Policy";

const highlights = [
  {
    icon: <CheckCircleIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit1",
    descKey: "customerLanding.product.benefit1Desc",
  },
  {
    icon: <GavelIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit2",
    descKey: "customerLanding.product.benefit2Desc",
  },
  {
    icon: <HomeRepairServiceIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit3",
    descKey: "customerLanding.product.benefit3Desc",
  },
  {
    icon: <WaterDamageIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit4",
    descKey: "customerLanding.product.benefit4Desc",
  },
  {
    icon: <SecurityIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit5",
    descKey: "customerLanding.product.benefit5Desc",
  },
  {
    icon: <PolicyIcon fontSize="large" />,
    titleKey: "customerLanding.product.benefit6",
    descKey: "customerLanding.product.benefit6Desc",
  },
];

export default function ProductOverview() {
  const t = useTranslations();
  const router = useRouter();

  return (
    <Box component="section" sx={{ mb: { xs: 6, md: 10 } }}>
      <Typography variant="h2" align="center" gutterBottom>
        {t("customerLanding.product.sleepEasyTitle")}
      </Typography>
      <Typography
        variant="body1"
        color="text.secondary"
        align="center"
        sx={{ maxWidth: 920, mx: "auto", mb: 4 }}
      >
        {t("customerLanding.product.sectionIntro")}
      </Typography>

      <Box
        sx={{
          display: "grid",
          gap: 3,
          gridTemplateColumns: { xs: "1fr", md: "1fr 1fr" },
        }}
      >
        {/* Linke Karte */}
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {t("customerLanding.product.highlightsTitle")}
            </Typography>

            <List>
              {highlights.slice(0, 3).map((h) => (
                <ListItem key={h.titleKey} disableGutters sx={{ py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 44 }}>{h.icon}</ListItemIcon>
                  <ListItemText
                    primary={t(h.titleKey)}
                    secondary={t(h.descKey)}
                    primaryTypographyProps={{ fontWeight: 600 }}
                  />
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle1" gutterBottom>
              {t("customerLanding.product.optionalTitle")}
            </Typography>
            <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
              <Chip label={t("customerLanding.product.elementar.label")} />
              <Chip label={t("customerLanding.product.glas.label")} />
              <Chip label={t("customerLanding.product.updateGuarantee.label")} />
            </Box>
          </CardContent>
        </Card>

        {/* Rechte Karte */}
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom>
              {t("customerLanding.product.detailedTitle")}
            </Typography>

            <List>
              {highlights.slice(3).map((h) => (
                <ListItem key={h.titleKey} disableGutters sx={{ py: 1 }}>
                  <ListItemIcon sx={{ minWidth: 44 }}>{h.icon}</ListItemIcon>
                  <ListItemText
                    primary={t(h.titleKey)}
                    secondary={t(h.descKey)}
                    primaryTypographyProps={{ fontWeight: 600 }}
                  />
                </ListItem>
              ))}
            </List>

            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {t("customerLanding.product.footnote")}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>

      <Box textAlign="center" mt={4}>
        <Button
          variant="contained"
          size="large"
          onClick={() => router.push("/customer/form")}
        >
          {t("customerLanding.product.cta")}
        </Button>
      </Box>
    </Box>
  );
}
