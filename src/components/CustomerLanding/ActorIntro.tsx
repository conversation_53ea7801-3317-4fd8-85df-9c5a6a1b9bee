"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Box, Card, CardContent, Typography } from "@mui/material";
import { useTranslations } from "next-intl";

const partners = [
  {
    logo: "/assets/logos/partner-primary.svg",
    key: "customerLanding.partners.primary.title",
    descKey: "customerLanding.partners.primary.desc",
    alt: "Primary Partner Logo",
    url: "https://advania.de/",
  },
  {
    logo: "/assets/logos/partner-secondary.svg",
    key: "customerLanding.partners.secondary.title",
    descKey: "customerLanding.partners.secondary.desc",
    alt: "Secondary Partner Logo",
    url: "https://www.wecoya.com/",
  },
];

export default function ActorIntro() {
  const t = useTranslations();

  return (
    <Box component="section" sx={{ mb: { xs: 6, md: 10 } }}>
      <Typography variant="h2" align="center" gutterBottom>
        {t("customerLanding.partners.sectionTitle")}
      </Typography>

      <Box
        sx={{
          display: "grid",
          gap: 4,
          gridTemplateColumns: { xs: "1fr", md: "repeat(2, 1fr)" },
          mt: 3,
        }}
      >
        {partners.map((partner) => (
          <Link
            key={partner.key}
            href={partner.url}
            target="_blank"
            rel="noopener noreferrer"
            style={{ textDecoration: "none" }}
          >
            <Card
              sx={{
                height: "100%",
                textAlign: "center",
                p: { xs: 3, md: 4 },
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                transition: "transform 0.2s, box-shadow 0.2s",
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: 4,
                },
              }}
            >
              <CardContent sx={{ p: 0 }}>
                <Box
                  sx={{
                    width: "250px",
                    height: "auto",
                    mx: "auto",
                    mt: 2,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    src={partner.logo}
                    alt={partner.alt}
                    width={250}
                    height={80}
                    style={{
                      objectFit: "contain",
                      filter: "grayscale(0%)",
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Link>
        ))}
      </Box>
    </Box>
  );
}
