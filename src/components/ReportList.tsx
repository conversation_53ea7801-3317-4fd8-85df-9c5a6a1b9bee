// src/components/ReportList.tsx
import React, { useEffect, useCallback, useState } from 'react';
import { useRouter } from 'next/router';
import { formatLabel, formatField } from '../utils/keyFormatter';
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Typography,
    CircularProgress,
    Pagination,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField,
    Button,
    Chip,
    SelectChangeEvent,
    Tooltip,
    Box,
    IconButton
} from '@mui/material';
import {
    Edit as EditIcon,
    Download as DownloadIcon
} from '@mui/icons-material';

interface Report {
    id: number;
    report_number: string;
    external_report_number: string;
    customer_number: string;
    contract_number: string;
    agency_number: string;
    damage_date: string;
}

interface PaginationMeta {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

const predefinedFilterValues: Record<string, string[]> = {
};

export default function ReportList() {
    const [reports, setReports] = useState<Report[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(10);
    const [meta, setMeta] = useState<PaginationMeta | null>(null);
    const [filters, setFilters] = useState<Record<string, string>>({});
    const [currentField, setCurrentField] = useState<string>('report_number');
    const [currentValue, setCurrentValue] = useState<string>('');
    const [sortField, setSortField] = useState<string>('damage_date');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
    const [initialized, setInitialized] = useState(false);
    const [isExporting, setIsExporting] = useState(false);


    const router = useRouter();

    useEffect(() => {
        const { query } = router;

        const initialPage = query.page ? parseInt(query.page as string, 10) : 1;
        const initialLimit = query.limit ? parseInt(query.limit as string, 10) : 10;

        const initialFilters = query.filters
            ? JSON.parse(query.filters as string)
            : {};

        const initialSortField = query.sortField as string || 'damage_date';
        const initialSortDirection = (query.sortDirection as 'asc' | 'desc') || 'asc';

        setPage(initialPage);
        setLimit(initialLimit);
        setFilters(initialFilters);
        setSortField(initialSortField);
        setSortDirection(initialSortDirection);

        setInitialized(true);
    }, []);

    useEffect(() => {
        if (!initialized) return;

        const cleanedFilters: Record<string, string> = {};
        for (const [key, value] of Object.entries(filters)) {
            if (value !== "") {
                cleanedFilters[key] = value;
            }
        }

        const query: Record<string, string> = {
            page: page.toString(),
            limit: limit.toString(),
            sortField,
            sortDirection,
        };

        if (Object.keys(cleanedFilters).length > 0) {
            query.filters = JSON.stringify(cleanedFilters);
        }

        router.replace(
            {
                pathname: router.pathname,
                query,
            },
            undefined,
            { shallow: true }
        );
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const fetchReports = async () => {
        setLoading(true);
        try {
            const offset = (page - 1) * limit;
            const response = await fetch(
                `/api/report?limit=${limit}&offset=${offset}&filters=${JSON.stringify(filters)}&sortField=${sortField}&sortDirection=${sortDirection}`,
                {
                    method: 'GET',
                    headers: { Authorization: `Bearer ${localStorage.getItem('jwt') || ''}` },
                }
            );

            if (!response.ok) {
                throw new Error('Failed to fetch reports');
            }

            const data = await response.json();
            setReports(data.reports);
            setMeta(data.meta);
        } catch (error) {
            console.error('Error fetching reports:', error);
            setError('Fehler beim Laden der Daten');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!initialized) return;
        fetchReports();
    }, [page, limit, filters, sortField, sortDirection, initialized]);

    const handleAddFilter = () => {
        if (currentField && currentValue) {
            setFilters((prev) => ({ ...prev, [currentField]: currentValue }));
            setCurrentValue('');
        }
    };

    const handleRemoveFilter = (field: string) => {
        setFilters((prev) => {
            const updated = { ...prev };
            delete updated[field];
            return updated;
        });
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
        setPage(value);
    };

    const handleLimitChange = (event: SelectChangeEvent<number>) => {
        const newLimit = Number(event.target.value);
        setLimit(newLimit);
        setPage(1);
    };

    const handleDetailsClick = (report: Report, event: React.MouseEvent) => {
        const url = `/report/${report.report_number}`;

        if (event.ctrlKey || event.metaKey) {
            window.open(url, '_blank');
        } else {
            router.push(url);
        }
    };

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const handleExportCsv = useCallback(async () => {
        try {
            setIsExporting(true);
            const token = localStorage.getItem('jwt');
            if (!token) {
                alert("Kein Token vorhanden");
                return;
            }

            const query = new URLSearchParams({
                sortField,
                sortDirection,
                filters: JSON.stringify(filters),
            });

            const response = await fetch(`/api/report/export-csv?${query.toString()}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) {
                throw new Error("CSV Export fehlgeschlagen");
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'schadenliste.csv';
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        } catch (err) {
            console.error('CSV Download Error:', err);
            alert("Export fehlgeschlagen");
        } finally {
            setIsExporting(false);
        }
    }, [filters, sortField, sortDirection]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (error) {
        return (
            <Typography variant="h6" color="error" align="center">
                Fehler: {error}
            </Typography>
        );
    }

    function handleEditClick(report_number: string): void {
        router.push(`/report/edit/${report_number}`);
    }

    return (
        <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
            <Typography variant="h4" align="center" gutterBottom color="primary">
                Schadensliste
            </Typography>

            <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
                    <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                        <InputLabel>Filterkriterium</InputLabel>
                        <Select
                            value={currentField}
                            onChange={(e) => setCurrentField(e.target.value)}
                            label="Filterkriterium"
                        >
                            <MenuItem value="report_number">Schadennummer</MenuItem>
                            <MenuItem value="external_report_number">Externe Schadennummer</MenuItem>
                            <MenuItem value="contract_number">Vertragnummer</MenuItem>
                            <MenuItem value="customer_number">Kundennummer</MenuItem>
                            {localStorage.getItem("is_admin") == "true" && <MenuItem value="agency_number">Agenturnummer</MenuItem>}
                        </Select>
                    </FormControl>

                    {predefinedFilterValues[currentField] ? (
                        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                            <InputLabel>Filterwert</InputLabel>
                            <Select
                                value={currentValue}
                                onChange={(e) => setCurrentValue(e.target.value as string)}
                                label="Filterwert"
                            >
                                {predefinedFilterValues[currentField].map((value) => (
                                    <MenuItem key={value} value={value}>
                                        {formatLabel(value)}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    ) : (
                        <TextField
                            label="Filterwert"
                            variant="outlined"
                            size="small"
                            value={currentValue}
                            onChange={(e) => setCurrentValue(e.target.value)}
                            sx={{ width: 200 }}
                        />
                    )}

                    <Button variant="contained" onClick={handleAddFilter}>
                        Filtern
                    </Button>
                    <div style={{ flexGrow: 1 }} />
                    <Tooltip title="CSV exportieren">
                        <IconButton
                            onClick={handleExportCsv}
                            color="primary"
                            style={{ marginRight: 15 }}
                            disabled={isExporting}
                        >
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip>
                </div>

                <div>
                    {Object.entries(filters).map(([field, value]) => (
                        <Chip
                            key={field}
                            label={`${formatField(field)}: ${formatLabel(value)}`}
                            onDelete={() => handleRemoveFilter(field)}
                            color="primary"
                            sx={{ marginRight: '0.5rem', marginBottom: '0.5rem', borderRadius: 0 }}
                        />
                    ))}
                </div>
            </div>

            {loading ? (
                <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                    <CircularProgress />
                </div>
            ) : (

                <TableContainer component={Paper} sx={{ marginTop: 4 }}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('report_number')}
                                >
                                    Schadennummer {sortField === 'report_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('external_report_number')}
                                >
                                    Externe Schadennummer {sortField === 'external_report_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('contract_number')}
                                >
                                    Vertragsnummer {sortField === 'contract_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('customer_number')}
                                >
                                    Kundennummer {sortField === 'customer_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                {localStorage.getItem("is_admin") == "true" && <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('agency_number')}
                                >
                                    Agenturnummer {sortField === 'agency_number' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>}
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('damage_date')}
                                >
                                    Schadendatum {sortField === 'damage_date' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell align="right" sx={{ fontWeight: 'bold' }}>Aktionen</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {Array.isArray(reports) && reports.length > 0 ? (
                                reports.map((report, index) => (
                                    <TableRow
                                        key={index}
                                        hover
                                        sx={{ cursor: 'pointer' }}
                                        onClick={(event) => handleDetailsClick(report, event)}
                                    >
                                        <TableCell>{report.report_number}</TableCell>
                                        <TableCell>{report.external_report_number}</TableCell>
                                        <TableCell>{report.contract_number}</TableCell>
                                        <TableCell>{report.customer_number}</TableCell>
                                        {localStorage.getItem("is_admin") == "true" && <TableCell>{report.agency_number}</TableCell>}
                                        <TableCell>{report.damage_date}</TableCell>
                                        <TableCell align="right">
                                            {localStorage.getItem('is_admin') == "true" && <Tooltip title='Schaden bearbeiten'>
                                                <IconButton
                                                    onClick={(event) => {
                                                        event.stopPropagation();
                                                        handleEditClick(report.report_number);
                                                    }}
                                                    color="primary"
                                                >
                                                    <EditIcon />
                                                </IconButton>
                                            </Tooltip>}
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={6} align="center">
                                        Keine Berichte gefunden.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>

                    </Table>
                </TableContainer>
            )}

            {meta && (
                <Pagination
                    count={meta.pageCount}
                    page={page}
                    onChange={handlePageChange}
                    sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
                />
            )}
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
                <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
                    <InputLabel id="results-per-page-label">Ergebnisse pro Seite</InputLabel>
                    <Select<number>
                        labelId="results-per-page-label"
                        value={limit}
                        onChange={handleLimitChange}
                        label="Ergebnisse pro Seite"
                    >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                    </Select>
                </FormControl>
            </div>
        </Box>
    );
}
