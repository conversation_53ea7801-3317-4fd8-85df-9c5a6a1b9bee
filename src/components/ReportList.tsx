// src/components/ReportList.tsx
import {
  Download as DownloadIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { serializeFilters } from '@/components/filters/url';
import { type Report } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useTableQuery } from '@/utils/useTableQuery';

import { FilterBuilder } from './filters/FilterBuilder';
import { reportFieldConfig } from './filters/reportFieldConfig';

export default function ReportList() {
  const [isExporting, setIsExporting] = useState(false);

  const router = useRouter();
  const session = useSession();

  const table = useTableQuery<any>({
    endpoint: '/api/reports',
    defaultSortField: 'reportNumber',
    accessToken: session.data?.accessToken,
    enabled: session.status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.items ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: reports,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleDetailsClick = (report: Report, event: React.MouseEvent) => {
    const url = `/report/${report.reportNumber}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);

      const query = new URLSearchParams({
        sortField,
        sortDirection,
        ...serializeFilters(filters),
      });

      const response = await apiFetch(
        `/api/reports/export-csv?${query.toString()}`,
        {
          raw: true,
        }
      );

      if (!response.ok) {
        throw new Error('CSV Export fehlgeschlagen');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'schadenliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  function handleEditClick(reportNumber: string): void {
    router.push(`/report/${reportNumber}/edit`);
  }

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Schadensliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        {/* Unified filter builder */}
        <FilterBuilder
          fields={reportFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="damageDate"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      {loading ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: '2rem',
          }}
        >
          <CircularProgress />
        </div>
      ) : (
        <TableContainer component={Paper} sx={{ marginTop: 4 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('reportNumber')}
                >
                  Schadennummer{' '}
                  {sortField === 'reportNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('externalReportNumber')}
                >
                  Externe Schadennummer{' '}
                  {sortField === 'externalReportNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('contractNumber')}
                >
                  Vertragsnummer{' '}
                  {sortField === 'contractNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('customerNumber')}
                >
                  Kundennummer{' '}
                  {sortField === 'customerNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                {session.data?.roles.includes('asevo-admin') && (
                  <TableCell
                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                    onClick={() => handleSort('agencyNumber')}
                  >
                    Agenturnummer{' '}
                    {sortField === 'agencyNumber' &&
                      (sortDirection === 'asc' ? '⬆' : '⬇')}
                  </TableCell>
                )}
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('damageDate')}
                >
                  Schadendatum{' '}
                  {sortField === 'damageDate' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  Aktionen
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(reports) && reports.length > 0 ? (
                reports.map((report, index) => (
                  <TableRow
                    key={index}
                    hover
                    sx={{ cursor: 'pointer' }}
                    onClick={(event) => handleDetailsClick(report, event)}
                  >
                    <TableCell>{report.reportNumber}</TableCell>
                    <TableCell>{report.externalReportNumber}</TableCell>
                    <TableCell>{report.contractNumber}</TableCell>
                    <TableCell>{report.customerNumber}</TableCell>
                    {session.data?.roles.includes('asevo-admin') && (
                      <TableCell>{report.agencyNumber}</TableCell>
                    )}
                    <TableCell>{report.damageDate}</TableCell>
                    <TableCell align="right">
                      {session.data?.roles.includes('asevo-admin') && (
                        <Tooltip title="Schaden bearbeiten">
                          <IconButton
                            onClick={(event) => {
                              event.stopPropagation();
                              handleEditClick(report.reportNumber);
                            }}
                            color="primary"
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    Keine Berichte gefunden.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={handlePageChange}
          sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '1rem',
        }}
      >
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </div>
    </Box>
  );
}
