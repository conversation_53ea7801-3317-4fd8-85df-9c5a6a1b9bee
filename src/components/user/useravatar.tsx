import { Ava<PERSON>, IconButton, Stack, Tooltip, Typography } from '@mui/material';

interface Props {
  userInitials: string;
  userName?: string;
  onClick?: () => void;
  small?: boolean;
}

export function UserAvatar({ userInitials, userName, onClick, small }: Props) {
  return (
    <Stack direction="row" spacing={1} alignItems="center">
      {userName && (
        <Typography
          sx={{
            cursor: onClick ? 'pointer' : 'default',
          }}
          variant="body1"
          onClick={onClick}
        >
          {userName}
        </Typography>
      )}

      {onClick ? (
        <Tooltip title="Profile">
          <IconButton
            onClick={onClick}
            sx={{
              padding: small ? 0.3 : 0.5, // kleineres Padding, wenn small
            }}
          >
            <Avatar
              sx={{
                bgcolor: 'var(--avatar-circle-color)',
                width: small
                  ? 'calc(var(--avatar-size) - 4px)'
                  : 'var(--avatar-size)',
                height: small
                  ? 'calc(var(--avatar-size) - 4px)'
                  : 'var(--avatar-size)',
                fontSize: small
                  ? 'calc(var(--avatar-font-size) - 2px)'
                  : 'var(--avatar-font-size)',
              }}
            >
              {userInitials}
            </Avatar>
          </IconButton>
        </Tooltip>
      ) : (
        <Avatar
          sx={{
            bgcolor: 'var(--avatar-circle-color)',
            width: small
              ? 'calc(var(--avatar-size) - 16px)'
              : 'var(--avatar-size)',
            height: small
              ? 'calc(var(--avatar-size) - 16px)'
              : 'var(--avatar-size)',
            fontSize: small
              ? 'calc(var(--avatar-font-size) - 8px)'
              : 'var(--avatar-font-size)',
          }}
        >
          {userInitials}
        </Avatar>
      )}
    </Stack>
  );
}
