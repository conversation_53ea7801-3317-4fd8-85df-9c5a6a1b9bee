// src/components/AgentDetails.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { Button, CircularProgress, Typography, Box, Grid2 as Grid } from '@mui/material';
import { AgencyData } from '@/types';
import AgencyInformationBox from './box/AgencyInformationBox';
import AgencyStatisticsBox from './box/AgencyStatisticsBox';


const AgencyDetails = () => {
    const router = useRouter();
    const { agency_number } = router.query;
    const [agency, setAgency] = useState<AgencyData | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || ""
        const fetchData = async () => {
            try {
                const agencyResponse = await fetch(`/api/agencies/${agency_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                })

                if (!agencyResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const agencyData = await agencyResponse.json();
                setAgency(agencyData);

            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        }
        fetchData()
    }, [agency_number]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (!agency) {
        return <div>Lade Agenturdaten...</div>;
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Maklerdetails
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary" >
                {agency.agency_name}
            </Typography>

            {/* agent infos */}
            <AgencyInformationBox
                agencyData={agency}
                visible
            />

            {/* agent stats */}
            <AgencyStatisticsBox
                agencyData={agency}
                visible
            />

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>
                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>

        </Box>
    );

    function onShowCustomersClicked(agency_number: string) {
        return () => router.push(`/customers?page=1&limit=10&sortField=last_name&sortDirection=asc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }

    function onShowContractsClicked(agency_number: string) {
        return () => router.push(`/contracts?page=1&limit=10&sortField=insurance_start_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number, 'active_status': 'ACTIVE' }))}`);
    }

    function onShowReportsClicked(agency_number: string) {
        return () => router.push(`/reports?page=1&limit=10&sortField=damage_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }

    function onShowInvoicesClicked(agency_number: string) {
        return () => router.push(`/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ 'agency_number': agency_number }))}`);
    }
};

export default AgencyDetails;
