// src/components/AgentDetails.tsx
import { Box, Button, CircularProgress, Grid, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { type Agency } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';

import AgencyInformationBox from './box/AgencyInformationBox';
import AgencyStatisticsBox from './box/AgencyStatisticsBox';

const AgencyDetails = () => {
  const router = useRouter();
  const { agencyNumber: agencyNumber } = router.query;
  const [agency, setAgency] = useState<Agency | null>(null);
  const [loading, setLoading] = useState(true);
  const { data: session, status } = useSession();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const agencyData = await apiFetch<Agency>(
          `/api/agencies/${agencyNumber}`,
          {
            method: 'GET',
            accessToken: session?.accessToken,
          }
        );
        setAgency(agencyData);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    if (status !== 'authenticated') return;
    fetchData();
  }, [agencyNumber, session, session?.accessToken, status]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (!agency) {
    return <div>Lade Agenturdaten...</div>;
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Maklerdetails
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {agency.agencyName}
      </Typography>

      {/* agent infos */}
      <AgencyInformationBox agencyData={agency} visible />

      {/* agent stats */}
      <AgencyStatisticsBox agencyData={agency} visible />

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>
    </Box>
  );

  //   function onShowCustomersClicked(agencyNumber: string) {
  //     return () =>
  //       router.push(
  //         `/customers?page=1&limit=10&sortField=last_name&sortDirection=asc&filters=${encodeURIComponent(JSON.stringify({ agencyNumber: agencyNumber }))}`
  //       );
  //   }

  //   function onShowContractsClicked(agencyNumber: string) {
  //     return () =>
  //       router.push(
  //         `/contracts?page=1&limit=10&sortField=insurance_start_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agencyNumber: agencyNumber, active_status: 'ACTIVE' }))}`
  //       );
  //   }

  //   function onShowReportsClicked(agencyNumber: string) {
  //     return () =>
  //       router.push(
  //         `/reports?page=1&limit=10&sortField=damage_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agencyNumber: agencyNumber }))}`
  //       );
  //   }

  //   function onShowInvoicesClicked(agencyNumber: string) {
  //     return () =>
  //       router.push(
  //         `/invoices?page=1&limit=10&sortField=due_date&sortDirection=desc&filters=${encodeURIComponent(JSON.stringify({ agencyNumber: agencyNumber }))}`
  //       );
  //   }
};

export default AgencyDetails;
