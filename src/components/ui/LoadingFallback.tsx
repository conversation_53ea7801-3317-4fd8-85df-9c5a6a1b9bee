'use client';

import {
  Box,
  Card,
  CircularProgress,
  Skeleton,
  Typography,
} from '@mui/material';

/**
 * A reusable loading fallback component for Suspense boundaries
 * Provides different loading states based on the context
 */
export function LoadingFallback({
  message = 'Loading...',
}: {
  message?: string;
}) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 4,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CircularProgress size={20} />
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      </Box>
    </Box>
  );
}

/**
 * A skeleton loading fallback for content areas
 */
export function SkeletonFallback() {
  return (
    <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Skeleton variant="rectangular" height={32} sx={{ width: '75%' }} />
      <Skeleton variant="text" height={16} sx={{ width: '100%' }} />
      <Skeleton variant="text" height={16} sx={{ width: '83%' }} />
      <Skeleton variant="text" height={16} sx={{ width: '80%' }} />
    </Box>
  );
}

/**
 * A minimal loading spinner for inline use
 */
export function SpinnerFallback() {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 1,
      }}
    >
      <CircularProgress size={16} color="inherit" />
    </Box>
  );
}

/**
 * A card-style loading fallback
 */
export function CardLoadingFallback() {
  return (
    <Card sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Skeleton variant="rectangular" height={24} sx={{ width: '33%' }} />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Skeleton variant="text" height={16} sx={{ width: '100%' }} />
          <Skeleton variant="text" height={16} sx={{ width: '80%' }} />
          <Skeleton variant="text" height={16} sx={{ width: '60%' }} />
        </Box>
      </Box>
    </Card>
  );
}
