'use client';

import AddIcon from '@mui/icons-material/Add';
import InboxIcon from '@mui/icons-material/Inbox';
import SearchOffIcon from '@mui/icons-material/SearchOff';
import { Box, Button, Typography } from '@mui/material';
import { type ReactNode } from 'react';

interface EmptyStateProps {
  title?: string;
  description?: string;
  icon?: ReactNode;
  actionLabel?: string;
  onAction?: () => void;
  variant?: 'default' | 'search' | 'minimal';
}

/**
 * A reusable empty state component for displaying when no data is available
 * Follows the project's design system and provides a consistent empty state UI
 */
export function EmptyState({
  title = 'No data available',
  description = 'There are no items to display at the moment.',
  icon,
  actionLabel,
  onAction,
  variant = 'default',
}: EmptyStateProps) {
  const getDefaultIcon = () => {
    switch (variant) {
      case 'search':
        return <SearchOffIcon sx={{ fontSize: 48, color: 'text.secondary' }} />;
      case 'minimal':
        return <InboxIcon sx={{ fontSize: 24, color: 'text.secondary' }} />;
      default:
        return <InboxIcon sx={{ fontSize: 48, color: 'text.secondary' }} />;
    }
  };

  const getMinHeight = () => {
    switch (variant) {
      case 'minimal':
        return '120px';
      default:
        return '200px';
    }
  };

  const getTypographyVariant = () => {
    switch (variant) {
      case 'minimal':
        return 'body2' as const;
      default:
        return 'h6' as const;
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: getMinHeight(),
        p: variant === 'minimal' ? 2 : 3,
        textAlign: 'center',
      }}
    >
      <Box sx={{ mb: variant === 'minimal' ? 1 : 2 }}>
        {icon || getDefaultIcon()}
      </Box>

      <Typography
        variant={getTypographyVariant()}
        color="text.primary"
        sx={{ mb: variant === 'minimal' ? 0.5 : 1, fontWeight: 500 }}
      >
        {title}
      </Typography>

      <Typography
        variant="body2"
        color="text.secondary"
        sx={{
          mb: variant === 'minimal' ? 1 : 2,
          maxWidth: '400px',
          fontSize: variant === 'minimal' ? '0.875rem' : '0.95rem',
        }}
      >
        {description}
      </Typography>

      {actionLabel && onAction && (
        <Button
          onClick={onAction}
          variant="outlined"
          size={variant === 'minimal' ? 'small' : 'medium'}
          startIcon={<AddIcon />}
          sx={{
            textTransform: 'none',
            fontWeight: 500,
          }}
        >
          {actionLabel}
        </Button>
      )}
    </Box>
  );
}

/**
 * A minimal empty state for inline use in tables or lists
 */
export function MinimalEmptyState({
  title = 'No items',
  description = 'No data to display',
  actionLabel,
  onAction,
}: Omit<EmptyStateProps, 'variant' | 'icon'>) {
  return (
    <EmptyState
      title={title}
      description={description}
      actionLabel={actionLabel}
      onAction={onAction}
      variant="minimal"
    />
  );
}

/**
 * A search-specific empty state for when search/filter results are empty
 */
export function SearchEmptyState({
  title = 'No results found',
  description = 'Try adjusting your search criteria or filters.',
  actionLabel,
  onAction,
}: Omit<EmptyStateProps, 'variant' | 'icon'>) {
  return (
    <EmptyState
      title={title}
      description={description}
      actionLabel={actionLabel}
      onAction={onAction}
      variant="search"
    />
  );
}

// Default export for flexibility
export default EmptyState;
