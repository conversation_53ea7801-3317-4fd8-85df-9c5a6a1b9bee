'use client';

import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RefreshIcon from '@mui/icons-material/Refresh';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { Alert, AlertTitle, Box, Button, Typography } from '@mui/material';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

/**
 * A reusable error fallback component that can be used with ErrorBoundary
 * Follows the project's design system and provides a consistent error UI
 */
export function ErrorFallback({
  error,
  resetErrorBoundary,
}: ErrorFallbackProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        p: 3,
      }}
    >
      <Box sx={{ width: '100%', maxWidth: '400px' }}>
        <Alert severity="error" icon={<ErrorOutlineIcon />} sx={{ mb: 2 }}>
          <AlertTitle>Something went wrong</AlertTitle>
          <Typography variant="body2" sx={{ mt: 1 }}>
            {error.message || 'An unexpected error occurred'}
          </Typography>
        </Alert>

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Button
            onClick={resetErrorBoundary}
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
          >
            Try again
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

/**
 * A minimal error fallback for inline use
 */
export function MinimalErrorFallback({
  error,
  resetErrorBoundary,
}: ErrorFallbackProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        p: 1,
        color: 'error.main',
      }}
    >
      <WarningAmberIcon sx={{ fontSize: 16 }} />
      <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
        Error: {error.message}
      </Typography>
      <Button
        onClick={resetErrorBoundary}
        variant="text"
        size="small"
        sx={{
          minHeight: 'auto',
          p: 0.5,
          fontSize: '0.75rem',
          textTransform: 'none',
        }}
      >
        Retry
      </Button>
    </Box>
  );
}
