import { Stack, type StackProps } from '@mui/material';
import { styled } from '@mui/material/styles';
import React from 'react';

/**
 * FormStack - A specialized Stack component for form layouts
 *
 * This component provides consistent styling for form elements with:
 * - Horizontal layout (row direction)
 * - Flexible wrapping
 * - Consistent spacing between form fields
 * - Responsive behavior for form controls
 */
const StyledFormStack = styled(Stack)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '0 20px',
  '& > div': {
    flex: 1,
    minWidth: 300,
    marginTop: '20px',
  },
}));

// Define the props interface, excluding variant since we don't need it
export interface FormStackProps extends Omit<StackProps, 'variant'> {
  children?: React.ReactNode;
}

/**
 * FormStack Component
 *
 * A pre-styled Stack component optimized for form layouts.
 * Automatically applies form-specific styling and sets direction to "row".
 *
 * @example
 * ```tsx
 * <FormStack>
 *   <TextField label="First Name" />
 *   <TextField label="Last Name" />
 * </FormStack>
 * ```
 */
const FormStack = React.forwardRef<HTMLDivElement, FormStackProps>(
  (props, ref) => {
    return <StyledFormStack ref={ref} direction="row" {...props} />;
  }
);

FormStack.displayName = 'FormStack';

export default FormStack;
