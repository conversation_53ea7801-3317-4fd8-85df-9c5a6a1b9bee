'use client';

import {
  FormControl,
  IconButton,
  Menu,
  MenuItem,
  <PERSON><PERSON><PERSON>,
  Typography,
} from '@mui/material';
import { useLocale, useTranslations } from 'next-intl';
import { useState, useTransition } from 'react';

import { setUserLocale } from '@/i18n/actions';

type Locale = 'de' | 'en';

interface LocaleOption {
  value: Locale;
  label: string;
  flag: string;
}

const localeOptions: LocaleOption[] = [
  { value: 'de', label: 'Deutsch', flag: '🇩🇪' },
  { value: 'en', label: 'English', flag: '🇺🇸' },
];

export default function LanguageSwitcher() {
  const currentLocale = useLocale() as Locale;
  const t = useTranslations('header');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isPending, startTransition] = useTransition();
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLocaleChange = (locale: Locale) => {
    if (locale === currentLocale) {
      handleClose();
      return;
    }

    startTransition(async () => {
      try {
        await setUserLocale(locale);
      } catch (error) {
        console.error('Failed to change locale:', error);
      }
    });
    handleClose();
  };

  const currentOption = localeOptions.find(
    (option) => option.value === currentLocale
  );

  return (
    <FormControl size="small">
      <Tooltip title={t('language_switcher_tooltip') || 'Change language'}>
        <IconButton
          onClick={handleClick}
          disabled={isPending}
          aria-controls={open ? 'language-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          aria-label="Change language"
          sx={{
            color: 'inherit',
            padding: '6px',
            minWidth: 'auto',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          }}
        >
          {currentOption && (
            <Typography
              variant="h6"
              component="span"
              sx={{
                fontSize: '1.2rem',
                lineHeight: 1,
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {currentOption.flag}
            </Typography>
          )}
        </IconButton>
      </Tooltip>

      <Menu
        id="language-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            'aria-labelledby': 'language-button',
          },
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {localeOptions.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => handleLocaleChange(option.value)}
            selected={option.value === currentLocale}
            disabled={isPending}
            sx={{
              minHeight: 'auto',
              py: 1,
              px: 2,
              justifyContent: 'center',
            }}
          >
            <Typography
              variant="h6"
              component="span"
              sx={{
                fontSize: '1.2rem',
                lineHeight: 1,
              }}
            >
              {option.flag}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </FormControl>
  );
}
