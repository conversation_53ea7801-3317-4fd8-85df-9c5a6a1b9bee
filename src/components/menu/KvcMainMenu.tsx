import { AddCircle } from '@mui/icons-material';
import CancelIcon from '@mui/icons-material/Cancel';
import WindowIcon from '@mui/icons-material/Window';
import { IconButton, Link, Stack, Typography } from '@mui/material';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import NextLink from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { KvModal } from '@/components/modal/modal';
import LanguageSwitcher from '@/components/ui/language-switcher';
import { UserAvatar } from '@/components/user/useravatar';
import { StartWidget } from '@/components/widgets/startWidget';
import { LogoutIconButton } from '@/modules/auth/components/LogoutButton';
import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  userName: string;
  userInitials: string;
  userRole?: string;
}

export default function KvcMainMenu(props: Props) {
  const [showStartModal, setShowStartModal] = useState(false);
  const t = useTranslations();
  const isAdmin = props.userRole === 'admin';
  return (
    <>
      <Drawer
        anchor="right"
        open={props.isOpen}
        onClose={() => {
          props.onClose();
        }}
        sx={{
          minWidth: '300px',
          width: '400px',
          maxWidth: '100vw',
        }}
      >
        <Box
          sx={{
            minWidth: '300px',
            width: '400px',

            maxWidth: '100vw',
          }}
          role="presentation"
          onClick={() => {
            // props.onClose();
          }}
        >
          <Stack direction="column">
            <Stack
              direction="row"
              display="flex"
              justifyContent="space-between"
            >
              <Stack
                direction="row"
                display="flex"
                gap={1}
                sx={{ paddingLeft: 5, marginTop: 5 }}
              >
                <UserAvatar userInitials={props.userInitials} small />
                <Typography
                  sx={{
                    // padding: 2,
                    // paddingLeft: 3,
                    fontWeight: 700,
                    marginTop: 'auto',
                    marginBottom: 'auto',
                  }}
                >
                  {props.userName}
                </Typography>
              </Stack>

              <IconButton
                onClick={props.onClose}
                sx={{
                  width: 40,
                  height: 40,
                  aspectRatio: '1 / 1',
                }}
              >
                <CancelIcon
                  sx={{
                    color: 'var(--teal)',
                    fontSize: (theme) => theme.typography.h4.fontSize,
                  }}
                ></CancelIcon>
              </IconButton>
            </Stack>
            <Stack
              direction="column"
              justifyContent="space-between"
              sx={{ padding: 6 }}
            >
              <Stack direction="column" gap={3} sx={{}}>
                <Link
                  component={NextLink}
                  href="/cases"
                  underline="none"
                  onClick={() => props.onClose()}
                >
                  <Stack direction="row">
                    <WindowIcon
                      sx={{
                        //   marginTop: -5,
                        marginRight: 1,
                      }}
                    />
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 500,
                        color: 'inherit',
                        textDecoration: 'none',
                      }}
                    >
                      {t('menu.cases')}
                    </Typography>
                  </Stack>
                </Link>
                {/* <Link href="/customers">
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 500,
                      color: 'inherit',
                      textDecoration: 'none',
                    }}
                  >
                    {t('menu.customers')}
                  </Typography>
                </Link> */}
                <Link
                  component="button"
                  underline="none"
                  onClick={() => setShowStartModal(true)}
                >
                  <Stack direction="row">
                    <AddCircle sx={{ marginTop: 0, marginRight: 1 }} />
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 500,
                        color: 'inherit',
                        textDecoration: 'none',
                      }}
                    >
                      {t('menu.create_new_case')}
                    </Typography>
                  </Stack>
                </Link>
                {isAdmin && (
                  <Link
                    component={NextLink}
                    href="/admin/customers"
                    underline="none"
                    onClick={() => props.onClose()}
                  >
                    <Typography
                      variant="body1"
                      sx={{
                        fontWeight: 500,
                        color: 'inherit',
                        textDecoration: 'none',
                      }}
                    >
                      Admin - Kunden
                    </Typography>
                  </Link>
                )}
              </Stack>

              <Stack direction="column">
                {process.env.NODE_ENV !== 'production' && (
                  <Stack direction="row">
                    <Typography>
                      {t.rich('menu.language', intlTranslationRichHelper)}:
                    </Typography>
                    <LanguageSwitcher />
                  </Stack>
                )}
                <Stack
                  direction="row"
                  display="flex"
                  justifyContent="center"
                  sx={{ marginTop: 15 }}
                >
                  <LogoutIconButton />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Box>
      </Drawer>

      {showStartModal && (
        <KvModal
          onClose={() => {
            setShowStartModal(false);
          }}
          sx={{
            width: '1080px',
          }}
        >
          <StartWidget
            reduced={true}
            schnellCheckDisabled={true}
            antragDisabled={true}
            closeModal={() => {
              setShowStartModal(false);
            }}
          />
        </KvModal>
      )}
    </>
  );
}
