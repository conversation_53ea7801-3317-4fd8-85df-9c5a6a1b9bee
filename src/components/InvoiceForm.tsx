// src/components/InvoiceForm.tsx
import { Add, Delete } from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import router from 'next/router';
import { type FormEvent, useEffect, useRef, useState } from 'react';
import React from 'react';

import {
  type Contract,
  type Customer,
  type Invoice,
} from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useIsAdmin } from '@/utils/authUtils';
import {
  InvoiceAgentStatusType,
  InvoiceCustomerStatusType,
  type InvoiceDetailStatusData,
  InvoiceInsuranceStatusType,
  InvoiceStatusType,
  InvoiceType,
} from '@/utils/invoice/types';

import StaticBox from './box/StaticBox';

type InvoicePositionData = {
  net?: number;
  tax?: number;
  name: string;
  tax_amount?: number;
};

interface InvoiceFormProps {
  editMode?: boolean;
}

const defaultInvoiceDetailStatus: InvoiceDetailStatusData = {
  send_date: '',
  overdue_date: '',
  dunning_date: '',
  partially_paid_date: '',
  partially_payment_amount: 0,
  payment_date: '',
  refund_date: '',
  cancel_date: '',
  fail_date: '',
  proccessing_date: '',
};

const defaultPosition: InvoicePositionData = {
  net: undefined,
  tax: 0,
  name: '',
  tax_amount: undefined,
};

const defaultInvoiceData: Invoice = {
  type: InvoiceType.BILL,
  contractNumber: '',
  versionNumber: 0,
  invoiceDetailStatus: defaultInvoiceDetailStatus,
  agentStatus: InvoiceAgentStatusType.OPEN,
  insuranceStatus: InvoiceInsuranceStatusType.OPEN,
  customerStatus: InvoiceCustomerStatusType.OPEN,
  invoiceStatus: InvoiceStatusType.BOOKED,
  dueDate: '',
  totalNet: null,
  totalGross: null,
  positions: [defaultPosition as any],
  billingStreet: '',
  billingHouseNumber: '',
  billingCity: '',
  billingPostalCode: '',
  billingCareOf: '',
  firstName: '',
  lastName: '',
  namePrefix: '',
  customerNumber: '',
  id: 0,
  documentId: '',
  invoiceNumber: '',
  insuranceStartDate: null,
  insuranceEndDate: null,
  iban: null,
  bic: null,
  agentCompanyName: null,
  agentStreet: null,
  agentHouseNumber: null,
  agentPostalCode: null,
  agentCity: null,
  paymentMode: null,
  agentNumber: null,
  subject: null,
  automaticallyGenerated: false,
  agencyNumber: null,
  contractId: null,
  customerId: null,
  agencyId: null,
  agentId: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const InvoiceForm: React.FC<InvoiceFormProps> = ({ editMode = false }) => {
  const [invoiceData, setInvoiceData] = useState<Invoice>(defaultInvoiceData);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [countPositions, setCountPositions] = useState<number>(
    (invoiceData.positions as InvoicePositionData[])!.length || 1
  );
  const [openPreview, setOpenPreview] = useState(false);
  const [url, setUrl] = useState<string>();
  const formRef = useRef<HTMLFormElement>(null);
  const isAdmin = useIsAdmin();

  const handlePreviewClose = () => {
    if (url) URL.revokeObjectURL(url);
    setOpenPreview(false);
  };

  useEffect(() => {
    async function loadInvoice() {
      const invoiceNumber: string =
        (router.query.invoiceNumber as string) || '';
      const invoiceResponse = await apiFetch(`/api/invoices/${invoiceNumber}`, {
        method: 'GET',
        raw: true,
      });
      const invoiceDataJson: Invoice = await invoiceResponse.json();
      setInvoiceData((prev) => ({
        ...defaultInvoiceData, // guarantees all keys exist
        ...prev, // keeps anything you already typed
        ...invoiceDataJson, // overrides with data from the server
        positions: (invoiceDataJson.positions as InvoicePositionData[])?.length
          ? invoiceDataJson.positions
          : [defaultPosition], // never leave positions empty
      }));
    }

    if (router.isReady) {
      if (editMode) {
        loadInvoice();
      } else {
        const { invoiceNumber } = router.query;
        const contract_number_string: string = (invoiceNumber as string) || '';
        setInvoiceData((prevInvoiceData) => ({
          ...prevInvoiceData,
          contractNumber: contract_number_string,
        }));
        fetchData(contract_number_string);
      }
    }
  }, [editMode]);

  const fetchData = async (contractNumber: string) => {
    try {
      const contractData = await apiFetch<Contract>(
        `/api/contracts/${contractNumber}`,
        {
          method: 'GET',
        }
      );

      const customerData = await apiFetch<Customer>(
        `/api/customers/${contractData.customerNumber}`,
        {
          method: 'GET',
        }
      );

      setInvoiceData((prevInvoiceData) => ({
        ...prevInvoiceData,
        billingCity: customerData.city,
        billingHouseNumber: customerData.houseNumber,
        billingPostalCode: customerData.postalCode,
        billingStreet: customerData.street,
        billingCareOf: customerData.careOf,
        customerNumber: customerData.customerNumber,
        firstName: customerData.firstName,
        lastName: customerData.lastName,
        namePrefix: customerData.namePrefix,
      }));
    } catch (error) {
      console.error(error);
    } finally {
      //setLoading(false);
    }
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    setIsSubmitting(true);

    let endpoint = `/api/invoices`;
    let method = 'POST';

    if (editMode) {
      endpoint = `/api/invoices`;
      method = 'PUT';
    }

    const body = { invoice: invoiceData };

    try {
      const response = await apiFetch(endpoint, {
        method: method,
        raw: true,
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const invoiceDataResponse = await response.json();
        alert('Die Rechnung wurde erfolgreich angelegt.');
        setIsSubmitting(false);
        router.push(`/invoice/${invoiceDataResponse.invoiceNumber}`);
      } else {
        alert('Fehler beim Anlegen der Rechnung.');
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error('Die Eingaben konnten nicht übergeben werden:', error);
      setIsSubmitting(false);
    }
  };

  function handleChange(
    event:
      | React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      | SelectChangeEvent<InvoiceType>
  ): void {
    const { name, value } = event.target as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;

    const updatedInvoiceData = {
      ...invoiceData,
      [name]: value,
    };

    setInvoiceData(updatedInvoiceData);
  }

  const handlePositionsChange = (
    index: number,
    field: keyof InvoicePositionData,
    value: InvoicePositionData[keyof InvoicePositionData]
  ) => {
    setInvoiceData((prev) => {
      /* ── 1. copy current positions ─────────────────────────────── */
      const positions = [...((prev.positions as InvoicePositionData[]) ?? [])];

      /* ── 2. update the selected field ───────────────────────────── */
      positions[index] = { ...positions[index], [field]: value };

      /* ── 3. keep tax_amount up-to-date when net or tax changes ──── */
      if (field === 'net' || field === 'tax') {
        const { net = 0, tax = 0 } = positions[index];
        positions[index] = { ...positions[index], tax_amount: net * tax };
      }

      /* ── 4. (re)calculate invoice-level totals ──────────────────── */
      const totals = positions.reduce(
        (acc, p) => {
          const net = p.net ?? 0;
          const taxAmount = p.tax_amount ?? (p.net ?? 0) * (p.tax ?? 0); // fallback if not yet stored
          acc.totalNet += net;
          acc.totalGross += net + taxAmount; // gross = net + tax
          return acc;
        },
        { totalNet: 0, totalGross: 0 }
      );

      /* ── 5. return the fully updated InvoiceData object ─────────── */
      return {
        ...prev,
        positions,
        totalNet: totals.totalNet,
        totalGross: totals.totalGross,
      };
    });
  };

  const addPosition = () => {
    setCountPositions((prevCount) => prevCount + 1);
    setInvoiceData((prevInvoiceData) => ({
      ...prevInvoiceData,
      positions: [
        ...((prevInvoiceData.positions as InvoicePositionData[]) || []),
        defaultPosition,
      ],
    }));
  };

  const removePosition = (index: number) => {
    if (countPositions > 1) {
      setCountPositions((prevCount) => prevCount - 1);
      setInvoiceData((prevInvoiceData) => {
        const updatedPositions = (
          (prevInvoiceData.positions as InvoicePositionData[]) || []
        ).filter((_, i) => i !== index);
        return {
          ...prevInvoiceData,
          positions: updatedPositions,
        };
      });
    }
  };

  async function handleOpenPreview() {
    if (formRef.current) {
      const isValid = formRef.current.reportValidity();
      if (!isValid) {
        return; // Don't proceed if validation fails
      }
    }
    const previewResponse = await apiFetch(`/api/invoice/get/preview`, {
      method: 'POST',
      raw: true,
      body: JSON.stringify({ invoiceData }),
    });

    if (!previewResponse.ok) {
      alert(
        'Die Vorschau konnte nicht generiert werden. Bitte stellen Sie sicher, dass sie alle notwendigen Felder ausgefüllt haben.'
      );
      return;
    }
    const blob = await previewResponse.blob();
    const localUrl = URL.createObjectURL(blob);
    setUrl(localUrl);
    setOpenPreview(true);
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        {editMode ? 'Rechnung bearbeiten' : 'Rechnung anlegen'}
      </Typography>

      <form ref={formRef} onSubmit={handleSubmit}>
        <Box sx={{ mt: 4 }}>
          <StaticBox title="Rechnungsangaben">
            <Grid container spacing={2}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Hier können Sie die Rechnungsart festlegen.">
                  <FormControl required fullWidth>
                    <InputLabel>Rechnungsart</InputLabel>
                    <Select
                      name="type"
                      label="Rechnungsart"
                      value={invoiceData.type || 0}
                      onChange={handleChange}
                      disabled={
                        editMode && (invoiceData.invoiceStatus || 0) > 0
                      }
                    >
                      {editMode && (
                        <MenuItem value={InvoiceType.FRIST_INVOICE}>
                          Erstprämie
                        </MenuItem>
                      )}
                      <MenuItem value={InvoiceType.ADDENDUM}>Nachtrag</MenuItem>
                      <MenuItem value={InvoiceType.BILL}>Rechnung</MenuItem>
                      <MenuItem value={InvoiceType.CREDIT}>Gutschrift</MenuItem>
                    </Select>
                  </FormControl>
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Hier können Sie einen Titel für die Rechnung definieren.">
                  <TextField
                    label="Rechnungstitel"
                    name="subject"
                    value={invoiceData.subject ?? ''}
                    onChange={handleChange}
                    fullWidth
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>
            </Grid>
            <Grid container spacing={2} mt={4}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie den Titel des Kunden ein (falls vorhanden).">
                  <TextField
                    label="Titel"
                    name="namePrefix"
                    value={invoiceData.namePrefix ?? ''}
                    onChange={handleChange}
                    fullWidth
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Falls die Post an eine andere Person oder Firma weitergeleitet werden soll, geben Sie hier den Zusatz ein, z. B. 'Max Mustermann'.">
                  <TextField
                    label="c/o"
                    name="billingCareOf"
                    value={invoiceData.billingCareOf ?? ''}
                    onChange={handleChange}
                    fullWidth
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie den Vornamen des Kunden ein.">
                  <TextField
                    label="Vorname"
                    name="firstName"
                    value={invoiceData.firstName ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie den Nachnamen des Kunden ein.">
                  <TextField
                    label="Nachname"
                    name="lastName"
                    value={invoiceData.lastName ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>
            </Grid>
            <Grid container spacing={2} mt={4}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie das Fälligkeitsdatum der Rechnung ein.">
                  <TextField
                    type="date"
                    label="Fälligkeitsdatum"
                    name="dueDate"
                    value={invoiceData.dueDate}
                    onChange={handleChange}
                    fullWidth
                    required
                    slotProps={{
                      inputLabel: { shrink: true },
                      htmlInput: isAdmin
                        ? undefined
                        : {
                            min: new Date().toISOString().split('T')[0],
                          },
                    }}
                    disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                  />
                </Tooltip>
              </Grid>

              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie die Straße der Kundenadresse ein.">
                  <TextField
                    label="Straße"
                    name="billingStreet"
                    value={invoiceData.billingStreet ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie die Hausnummer der Kundenadresse ein.">
                  <TextField
                    label="Hausnummer"
                    name="billingHouseNumber"
                    value={invoiceData.billingHouseNumber ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie die Postleitzahl der Kundenadresse ein.">
                  <TextField
                    label="Postleitzahl"
                    name="billingPostalCode"
                    value={invoiceData.billingPostalCode ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Geben Sie den Ort oder die Stadt der Kundenadresse ein.">
                  <TextField
                    label="Ort"
                    name="billingCity"
                    value={invoiceData.billingCity ?? ''}
                    onChange={handleChange}
                    fullWidth
                    required
                  />
                </Tooltip>
              </Grid>
            </Grid>

            <Typography sx={{ mt: 4 }} variant="subtitle1">
              Positionen:
            </Typography>
            <Stack gap={4} mt={2}>
              {Array.from({ length: countPositions }, (_, index) => (
                <Stack direction="row" alignItems="center" key={index} gap={2}>
                  <Grid
                    container
                    spacing={2}
                    alignItems="center"
                    key={index + '-grid'}
                  >
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Tooltip title="Bezeichnung der Position">
                        <TextField
                          fullWidth
                          label="Name"
                          value={
                            (invoiceData.positions as InvoicePositionData[])![
                              index
                            ]?.name ?? ''
                          }
                          onChange={(e) =>
                            handlePositionsChange(index, 'name', e.target.value)
                          }
                          disabled={
                            editMode && (invoiceData.invoiceStatus || 0) > 0
                          }
                          required
                        />
                      </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Tooltip title="Jährlicher Nettobetrag">
                        <TextField
                          type="number"
                          fullWidth
                          label="Netto"
                          value={
                            (invoiceData.positions as InvoicePositionData[])![
                              index
                            ]?.net ?? ''
                          }
                          onChange={(e) =>
                            handlePositionsChange(
                              index,
                              'net',
                              Number(e.target.value)
                            )
                          }
                          disabled={
                            editMode && (invoiceData.invoiceStatus || 0) > 0
                          }
                          slotProps={{
                            input: {
                              endAdornment: (
                                <InputAdornment position="end">
                                  €
                                </InputAdornment>
                              ),
                            },
                          }}
                          name="net"
                          required
                        />
                      </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Tooltip title="Versicherungssteuer in Prozent">
                        <TextField
                          type="number"
                          fullWidth
                          label="Versicherungssteuer"
                          slotProps={{
                            input: {
                              endAdornment: (
                                <InputAdornment position="end">
                                  %
                                </InputAdornment>
                              ),
                            },
                          }}
                          name="tax"
                          value={
                            ((invoiceData.positions as InvoicePositionData[])![
                              index
                            ]?.tax ?? 0) * 100
                          }
                          onChange={(e) =>
                            handlePositionsChange(
                              index,
                              'tax',
                              Number(e.target.value) / 100
                            )
                          }
                          disabled={
                            editMode && (invoiceData.invoiceStatus || 0) > 0
                          }
                          required
                        />
                      </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6 }}>
                      <Tooltip title="Berechneter Bruttowert">
                        <TextField
                          fullWidth
                          label="Brutto"
                          value={
                            ((invoiceData.positions as InvoicePositionData[])![
                              index
                            ]?.net ?? 0) +
                            ((invoiceData.positions as InvoicePositionData[])![
                              index
                            ]?.tax_amount ?? 0)
                          }
                          slotProps={{
                            input: {
                              endAdornment: (
                                <InputAdornment position="end">
                                  €
                                </InputAdornment>
                              ),
                            },
                          }}
                          disabled
                        />
                      </Tooltip>
                    </Grid>
                  </Grid>
                  <Box>
                    <Tooltip title="Position entfernen">
                      <IconButton
                        onClick={() => removePosition(index)}
                        disabled={
                          countPositions === 1 ||
                          (editMode && (invoiceData.invoiceStatus || 0) > 0)
                        }
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Stack>
              ))}

              <Tooltip title="Weitere Position hinzufügen">
                <Button
                  sx={{ mt: 2 }}
                  variant="contained"
                  startIcon={<Add />}
                  onClick={addPosition}
                  disabled={editMode && (invoiceData.invoiceStatus || 0) > 0}
                >
                  Position hinzufügen
                </Button>
              </Tooltip>
            </Stack>

            <Grid container spacing={2} sx={{ mt: 4 }}>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Gesamtnettobetrag">
                  <TextField
                    label="Gesamt Netto"
                    type="number"
                    fullWidth
                    value={invoiceData.totalNet || ''}
                    slotProps={{
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">€</InputAdornment>
                        ),
                      },
                    }}
                    disabled
                  />
                </Tooltip>
              </Grid>
              <Grid size={{ xs: 12, sm: 6 }}>
                <Tooltip title="Gesamtbruttobetrag">
                  <TextField
                    label="Gesamt Brutto"
                    type="number"
                    fullWidth
                    value={invoiceData.totalGross || ''}
                    slotProps={{
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">€</InputAdornment>
                        ),
                      },
                    }}
                    disabled
                  />
                </Tooltip>
              </Grid>
            </Grid>
          </StaticBox>

          <Box
            sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}
          >
            <Tooltip title="Vorschau PDF anzeigen">
              <Button variant="contained" onClick={handleOpenPreview}>
                PDF Vorschau
              </Button>
            </Tooltip>
            <Tooltip title="Ohne sichern zurück.">
              <Button variant="contained" onClick={() => window.history.back()}>
                Verwerfen
              </Button>
            </Tooltip>
            <Tooltip
              title={
                editMode ? 'Rechnung aktualisieren' : 'Neue Rechnung buchen'
              }
            >
              <Button
                type="submit"
                variant="contained"
                color="secondary"
                disabled={isSubmitting}
              >
                {editMode
                  ? 'Rechnung aktualisieren'
                  : isSubmitting
                    ? 'Rechnung buchen...'
                    : 'Rechnung buchen'}
              </Button>
            </Tooltip>
          </Box>
        </Box>
      </form>

      <Dialog
        open={openPreview}
        onClose={handlePreviewClose}
        maxWidth="xl"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          {url && (
            <iframe
              src={url}
              style={{ width: '100%', height: '80vh', border: 'none' }}
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default InvoiceForm;
