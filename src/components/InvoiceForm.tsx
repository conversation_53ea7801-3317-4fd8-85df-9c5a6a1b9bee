// src/components/InvoiceForm.tsx
import { FormEvent, useEffect, useRef, useState } from 'react';
import router from 'next/router';
import { <PERSON><PERSON>, TextField, Tooltip, IconButton, InputAdornment, Dialog, DialogContent, Select, MenuItem, FormControl, InputLabel, SelectChangeEvent, Typography, Box, Grid2 as Grid, Divider, Stack } from '@mui/material';
import { CustomerData } from '@/types';
import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceInsuranceStatusType, InvoiceDetailStatusData, InvoiceStatusType, InvoiceType } from "@/utils/invoice/types";
import { Add, Delete } from '@mui/icons-material';
import StaticBox from './box/StaticBox';
import React from 'react';

interface InvoiceData {
    type: InvoiceType;
    contract_number?: string;
    version_number?: number;
    invoice_detail_status: InvoiceDetailStatusData;
    invoice_status: InvoiceStatusType;
    agent_status: InvoiceAgentStatusType;
    insurance_status: InvoiceInsuranceStatusType;
    customer_status: InvoiceCustomerStatusType;
    due_date?: string;
    total_net?: number;
    total_gross?: number;
    positions?: InvoicePositionData[];
    billing_street?: string;
    billing_house_number?: string;
    billing_city?: string;
    billing_postal_code?: string;
    billing_care_of?: string;
    first_name?: string;
    last_name?: string;
    name_prefix?: string;
    customer_number?: string;
    subject?: string;
}

interface InvoicePositionData {
    net?: number,
    tax?: number,
    name: string,
    tax_amount?: number
}

interface InvoiceFormProps {
    editMode?: boolean;
}


const InvoiceForm: React.FC<InvoiceFormProps> = ({ editMode = false }) => {

    const defaultInvoiceDetailStatus: InvoiceDetailStatusData = {
        send_date: '',
        overdue_date: '',
        dunning_date: '',
        partially_paid_date: '',
        partially_payment_amount: 0,
        payment_date: '',
        refund_date: '',
        cancel_date: '',
        fail_date: '',
        proccessing_date: ''
    }

    const defaultPosition: InvoicePositionData = {
        net: undefined,
        tax: 0,
        name: '',
        tax_amount: undefined
    }

    const defaultInvoiceData: InvoiceData =
    {
        type: InvoiceType.BILL,
        contract_number: '',
        version_number: 0,
        invoice_detail_status: defaultInvoiceDetailStatus,
        agent_status: InvoiceAgentStatusType.OPEN,
        insurance_status: InvoiceInsuranceStatusType.OPEN,
        customer_status: InvoiceCustomerStatusType.OPEN,
        invoice_status: InvoiceStatusType.BOOKED,
        due_date: '',
        total_net: undefined,
        total_gross: undefined,
        positions: [defaultPosition],
        billing_street: '',
        billing_house_number: '',
        billing_city: '',
        billing_postal_code: '',
        billing_care_of: '',
        first_name: '',
        last_name: '',
        name_prefix: '',
        customer_number: '',
    }

    const [invoiceData, setInvoiceData] = useState<InvoiceData>(defaultInvoiceData)
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
    const [countPositions, setCountPositions] = useState<number>(invoiceData.positions!.length || 1);
    const [openPreview, setOpenPreview] = useState(false);
    const [url, setUrl] = useState<string>();
    const formRef = useRef<HTMLFormElement>(null);

    const handlePreviewClose = () => {
        if (url) URL.revokeObjectURL(url);
        setOpenPreview(false);
    };

    useEffect(() => {
        if (router.isReady) {
            if (editMode) {
                loadInvoice()
            } else {
                const { contract_number } = router.query;
                const contract_number_string: string = contract_number as string || '';
                setInvoiceData(prevInvoiceData => ({
                    ...prevInvoiceData,
                    contract_number: contract_number_string,
                }));
                fetchData(contract_number_string);
            }
        }
    }, [router.isReady, router.query]);

    const fetchData = async (contractNumber: string) => {
        const token = localStorage.getItem("jwt") || "";
        try {
            const contractResponse = await fetch(`/api/contracts/${contractNumber}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            });

            if (!contractResponse.ok) {
                throw new Error('Failed to fetch data');
            }

            const contractData = await contractResponse.json();

            const customerResponse = await fetch(`/api/customer/${contractData.customer_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            });

            if (!customerResponse.ok) {
                throw new Error('Failed to fetch data');
            }

            const customerData: CustomerData = await customerResponse.json();
            setInvoiceData(prevInvoiceData => ({
                ...prevInvoiceData,
                billing_city: customerData.city,
                billing_house_number: customerData.house_number,
                billing_postal_code: customerData.postal_code,
                billing_street: customerData.street,
                billing_care_of: customerData.care_of,
                customer_number: customerData.customer_number,
                first_name: customerData.first_name,
                last_name: customerData.last_name,
                name_prefix: customerData.name_prefix,
            }));


        } catch (error) {
            console.error(error);
        } finally {
            //setLoading(false);
        }
    };

    async function loadInvoice() {
        const { invoice_number } = router.query;
        const invoiceNumber: string = invoice_number as string || '';
        const token = localStorage.getItem("jwt") || "";
        const invoiceResponse = await fetch(`/api/invoice/get/${invoiceNumber}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${token}`
            },
        });
        const invoiceDataJson: InvoiceData = await invoiceResponse.json()
        setInvoiceData(prev => ({
            ...defaultInvoiceData,          // guarantees all keys exist
            ...prev,                        // keeps anything you already typed
            ...invoiceDataJson,             // overrides with data from the server
            positions:
                invoiceDataJson.positions?.length
                    ? invoiceDataJson.positions
                    : [defaultPosition],        // never leave positions empty
        }));
    }

    const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
        event.preventDefault();

        let endpoint = `/api/invoice/create`;
        let method = 'POST'

        if (editMode) {
            endpoint = `/api/invoice/update`;
            method = 'PUT';
        }

        const body = { 'invoiceData': invoiceData }

        try {
            const response = await fetch(endpoint, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify(body),
            });

            if (response.ok) {
                const invoiceDataResponse = await response.json()
                alert('Die Rechnung wurde erfolgreich angelegt.');
                router.push(`/invoice/${invoiceDataResponse.invoice_number}`);
            } else {
                alert('Fehler beim Anlegen der Rechnung.');
            }
        } catch (error) {
            console.error('Die Eingaben konnten nicht übergeben werden:', error);
        }
    }

    function handleChange(event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> | SelectChangeEvent<InvoiceType>): void {
        const { name, value, type } = event.target as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;

        const updatedInvoiceData = {
            ...invoiceData,
            [name]: value,
        };

        setInvoiceData(updatedInvoiceData);
    };

    const handlePositionsChange = (
        index: number,
        field: keyof InvoicePositionData,
        value: InvoicePositionData[keyof InvoicePositionData]
    ) => {
        setInvoiceData(prev => {
            /* ── 1. copy current positions ─────────────────────────────── */
            const positions = [...(prev.positions ?? [])];

            /* ── 2. update the selected field ───────────────────────────── */
            positions[index] = { ...positions[index], [field]: value };

            /* ── 3. keep tax_amount up-to-date when net or tax changes ──── */
            if (field === 'net' || field === 'tax') {
                const { net = 0, tax = 0 } = positions[index];
                positions[index] = { ...positions[index], tax_amount: net * tax };
            }

            /* ── 4. (re)calculate invoice-level totals ──────────────────── */
            const totals = positions.reduce(
                (acc, p) => {
                    const net = p.net ?? 0;
                    const taxAmount =
                        p.tax_amount ?? (p.net ?? 0) * (p.tax ?? 0); // fallback if not yet stored
                    acc.totalNet += net;
                    acc.totalGross += net + taxAmount;               // gross = net + tax
                    return acc;
                },
                { totalNet: 0, totalGross: 0 }
            );

            /* ── 5. return the fully updated InvoiceData object ─────────── */
            return {
                ...prev,
                positions,
                total_net: totals.totalNet,
                total_gross: totals.totalGross
            };
        });
    };


    const addPosition = () => {
        setCountPositions((prevCount) => prevCount + 1);
        setInvoiceData(prevInvoiceData => ({
            ...prevInvoiceData,
            positions: [
                ...(prevInvoiceData.positions || []),
                defaultPosition,
            ],
        }));
    };

    const removePosition = (index: number) => {
        if (countPositions > 1) {
            setCountPositions((prevCount) => prevCount - 1);
            setInvoiceData(prevInvoiceData => {
                const updatedPositions = (prevInvoiceData.positions || []).filter((_, i) => i !== index);
                return {
                    ...prevInvoiceData,
                    positions: updatedPositions,
                };
            });
        }
    };

    async function handleOpenPreview() {
        if (formRef.current) {
            const isValid = formRef.current.reportValidity();
            if (!isValid) {
                return; // Don't proceed if validation fails
            }
        }
        const token = localStorage.getItem("jwt") || "";
        const previewResponse = await fetch(`/api/invoice/get/preview`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${token}`
            },
            body: JSON.stringify({ invoiceData })
        });

        if (!previewResponse.ok) {
            alert('Die Vorschau konnte nicht generiert werden. Bitte stellen Sie sicher, dass sie alle notwendigen Felder ausgefüllt haben.');
            return;
        }
        const blob = await previewResponse.blob();
        const localUrl = URL.createObjectURL(blob);
        setUrl(localUrl);
        setOpenPreview(true);
    }


    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary">
                {editMode ? 'Rechnung bearbeiten' : 'Rechnung anlegen'}
            </Typography>

            <form ref={formRef} onSubmit={handleSubmit}>
                <Box sx={{ mt: 4 }}>
                    <StaticBox title="Rechnungsangaben">
                        <Grid container spacing={2}>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Hier können Sie die Rechnungsart festlegen.">
                                    <FormControl required fullWidth>
                                        <InputLabel>Rechnungsart</InputLabel>
                                        <Select
                                            name="type"
                                            label="Rechnungsart"
                                            value={invoiceData.type}
                                            onChange={handleChange}
                                            disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                        >
                                            {editMode && <MenuItem value={InvoiceType.FRIST_INVOICE}>Erstprämie</MenuItem>}
                                            <MenuItem value={InvoiceType.ADDENDUM}>Nachtrag</MenuItem>
                                            <MenuItem value={InvoiceType.BILL}>Rechnung</MenuItem>
                                            <MenuItem value={InvoiceType.CREDIT}>Gutschrift</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Hier können Sie einen Titel für die Rechnung definieren.">
                                    <TextField
                                        label="Rechnungstitel"
                                        name="subject"
                                        value={invoiceData.subject ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} mt={4}>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie den Titel des Kunden ein (falls vorhanden).">
                                    <TextField
                                        label="Titel"
                                        name="name_prefix"
                                        value={invoiceData.name_prefix ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Falls die Post an eine andere Person oder Firma weitergeleitet werden soll, geben Sie hier den Zusatz ein, z. B. 'Max Mustermann'.">
                                    <TextField
                                        label="c/o"
                                        name="billing_care_of"
                                        value={invoiceData.billing_care_of ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>

                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie den Vornamen des Kunden ein.">
                                    <TextField
                                        label="Vorname"
                                        name="first_name"
                                        value={invoiceData.first_name ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie den Nachnamen des Kunden ein.">
                                    <TextField
                                        label="Nachname"
                                        name="last_name"
                                        value={invoiceData.last_name ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} mt={4}>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie das Fälligkeitsdatum der Rechnung ein.">
                                    <TextField
                                        type="date"
                                        label="Fälligkeitsdatum"
                                        name="due_date"
                                        value={invoiceData.due_date}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                        slotProps={{
                                            inputLabel: { shrink: true },
                                            htmlInput: localStorage.getItem("is_admin") == "true"
                                                ? undefined
                                                : {
                                                    min: new Date().toISOString().split("T")[0],
                                                }
                                        }}
                                        disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                    />
                                </Tooltip>
                            </Grid>

                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie die Straße der Kundenadresse ein.">
                                    <TextField
                                        label="Straße"
                                        name="billing_street"
                                        value={invoiceData.billing_street ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie die Hausnummer der Kundenadresse ein.">
                                    <TextField
                                        label="Hausnummer"
                                        name="billing_house_number"
                                        value={invoiceData.billing_house_number ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie die Postleitzahl der Kundenadresse ein.">
                                    <TextField
                                        label="Postleitzahl"
                                        name="billing_postal_code"
                                        value={invoiceData.billing_postal_code ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Geben Sie den Ort oder die Stadt der Kundenadresse ein.">
                                    <TextField
                                        label="Ort"
                                        name="billing_city"
                                        value={invoiceData.billing_city ?? ''}
                                        onChange={handleChange}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                            </Grid>
                        </Grid>

                        <Typography sx={{ mt: 4 }} variant="subtitle1">Positionen:</Typography>
                        <Stack gap={4} mt={2}>
                            {Array.from({ length: countPositions }, (_, index) => (
                                <Stack direction='row' alignItems='center' key={index} gap={2}>
                                    <Grid container spacing={2} alignItems="center" key={index + '-grid'}>
                                        <Grid size={{ xs: 12, sm: 6 }}>
                                            <Tooltip title="Bezeichnung der Position">
                                                <TextField
                                                    fullWidth
                                                    label="Name"
                                                    value={invoiceData.positions![index]?.name ?? ''}
                                                    onChange={(e) => handlePositionsChange(index, 'name', e.target.value)}
                                                    disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                                    required
                                                />
                                            </Tooltip>
                                        </Grid>
                                        <Grid size={{ xs: 12, sm: 6 }}>
                                            <Tooltip title="Jährlicher Nettobetrag">
                                                <TextField
                                                    type="number"
                                                    fullWidth
                                                    label="Netto"
                                                    value={invoiceData.positions![index]?.net ?? ''}
                                                    onChange={(e) => handlePositionsChange(index, 'net', Number(e.target.value))}
                                                    disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                                    slotProps={{
                                                        input: {
                                                            endAdornment: <InputAdornment position="end">€</InputAdornment>,
                                                        },
                                                    }}
                                                    name="net"
                                                    required
                                                />
                                            </Tooltip>
                                        </Grid>
                                        <Grid size={{ xs: 12, sm: 6 }}>
                                            <Tooltip title="Versicherungssteuer in Prozent">
                                                <TextField
                                                    type="number"
                                                    fullWidth
                                                    label="Versicherungssteuer"
                                                    slotProps={{
                                                        input: {
                                                            endAdornment: <InputAdornment position="end">%</InputAdornment>,
                                                        },
                                                    }}
                                                    name="tax"
                                                    value={(invoiceData.positions![index]?.tax ?? 0) * 100}
                                                    onChange={(e) => handlePositionsChange(index, 'tax', Number(e.target.value) / 100)}
                                                    disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                                    required
                                                />
                                            </Tooltip>
                                        </Grid>
                                        <Grid size={{ xs: 12, sm: 6 }}>
                                            <Tooltip title="Berechneter Bruttowert">
                                                <TextField
                                                    fullWidth
                                                    label="Brutto"
                                                    value={(invoiceData.positions![index]?.net ?? 0) + (invoiceData.positions![index]?.tax_amount ?? 0)}
                                                    slotProps={{
                                                        input: {
                                                            endAdornment: <InputAdornment position="end">€</InputAdornment>,
                                                        },
                                                    }}
                                                    disabled
                                                />
                                            </Tooltip>
                                        </Grid>
                                    </Grid>
                                    <Box>
                                        <Tooltip title="Position entfernen">
                                            <IconButton
                                                onClick={() => removePosition(index)}
                                                disabled={countPositions === 1 || (editMode && (invoiceData.invoice_status || 0) > 0)}
                                            >
                                                <Delete />
                                            </IconButton>
                                        </Tooltip>
                                    </Box>
                                </Stack>
                            ))}

                            <Tooltip title="Weitere Position hinzufügen">
                                <Button
                                    sx={{ mt: 2 }}
                                    variant="contained"
                                    startIcon={<Add />}
                                    onClick={addPosition}
                                    disabled={editMode && (invoiceData.invoice_status || 0) > 0}
                                >
                                    Position hinzufügen
                                </Button>
                            </Tooltip>
                        </Stack>

                        <Grid container spacing={2} sx={{ mt: 4 }}>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Gesamtnettobetrag">
                                    <TextField
                                        label="Gesamt Netto"
                                        type="number"
                                        fullWidth
                                        value={invoiceData.total_net || ''}
                                        slotProps={{
                                            input: {
                                                endAdornment: <InputAdornment position="end">€</InputAdornment>,
                                            },
                                        }}
                                        disabled
                                    />
                                </Tooltip>
                            </Grid>
                            <Grid size={{ xs: 12, sm: 6 }}>
                                <Tooltip title="Gesamtbruttobetrag">
                                    <TextField
                                        label="Gesamt Brutto"
                                        type="number"
                                        fullWidth
                                        value={invoiceData.total_gross || ''}
                                        slotProps={{
                                            input: {
                                                endAdornment: <InputAdornment position="end">€</InputAdornment>,
                                            },
                                        }}
                                        disabled
                                    />
                                </Tooltip>
                            </Grid>
                        </Grid>
                    </StaticBox>

                    <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <Tooltip title="Vorschau PDF anzeigen">
                            <Button variant="contained" onClick={handleOpenPreview}>
                                PDF Vorschau
                            </Button>
                        </Tooltip>
                        <Tooltip title="Ohne sichern zurück.">
                            <Button variant="contained" onClick={() => window.history.back()}>
                                Verwerfen
                            </Button>
                        </Tooltip>
                        <Tooltip title={editMode ? 'Rechnung aktualisieren' : 'Neue Rechnung buchen'}>
                            <Button
                                type="submit"
                                variant="contained"
                                color="secondary"
                                disabled={isSubmitting}
                            >
                                {editMode ? 'Rechnung aktualisieren' : isSubmitting ? 'Rechnung buchen...' : 'Rechnung buchen'}
                            </Button>
                        </Tooltip>
                    </Box>
                </Box>
            </form>

            <Dialog open={openPreview} onClose={handlePreviewClose} maxWidth="xl" fullWidth>
                <DialogContent sx={{ p: 0 }}>
                    {url && (
                        <iframe
                            src={url}
                            style={{ width: '100%', height: '80vh', border: 'none' }}
                        />
                    )}
                </DialogContent>
            </Dialog>
        </Box>
    );
}

export default InvoiceForm;
