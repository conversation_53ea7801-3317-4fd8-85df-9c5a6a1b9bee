// src/components/ReportForm.tsx
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { CustomerData, ContractData } from '@/types';
import { Button, IconButton, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip, CircularProgress, Typography, Box } from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { TrashIcon } from '@heroicons/react/24/outline';
import CustomerInformationBox from './box/CustomerInformationBox';
import ContractInformationBox from './box/ContractInformationBox';
import StaticBox from './box/StaticBox';
import MultiFormBox from './box/MultiFormBox';
import {reportFormConfigs, FormState, determineShownFields, FormConfig} from '@/components/reportForms';
import { VisuallyHiddenInput } from "@/components/common";

interface ReportFormProps {
    customer_number?: string;
    contract_number?: string;
    agency_number?: string;
    report_number?: string;
}


const ReportForm: React.FC<ReportFormProps> = ({ customer_number, contract_number, agency_number, report_number }) => {
    const [formData, setFormData] = useState({
        customer_number: customer_number || "",
        contract_number: contract_number || "",
        agency_number: agency_number || "",
        external_report_number: '',
        damage_date: '',
        damage_location: '',
        iban: '',
        coverd_risk: '',
        text: ''
    });

    const router = useRouter();
    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [contract, setContract] = useState<ContractData | null>(null);
    const [loading, setLoading] = useState(true);
    const [editMode, setEditMode] = useState<boolean>(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || "";
        const fetchData = async () => {
            try {

                if (report_number != undefined) {
                    setEditMode(true)
                    const reportResponse = await fetch(`/api/report/${report_number}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Baerer ${token}`
                        },
                    })

                    if (!reportResponse.ok) throw new Error('Failed to fetch');
                    const reportData = await reportResponse.json();
                    setFormData(reportData)
                    setFormsMemory(reportData.data_raw)
                    customer_number = reportData.customer_number;
                    contract_number = reportData.contract_number;
                }

                if (contract_number) {
                    const customerResponse = await fetch(`/api/customer/${customer_number}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Baerer ${token}`
                        },
                    })

                    const contractResponse = await fetch(`/api/contracts/${contract_number}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Baerer ${token}`
                        },
                    });

                    if (!customerResponse.ok || !contractResponse.ok) throw new Error('Failed to fetch data');


                    const customerData = await customerResponse.json();
                    const contractData = await contractResponse.json();

                    setCustomer(customerData);
                    setContract(contractData);
                }

            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        }
        fetchData()
    }, [customer_number, editMode]);

    const [formsMemory, setFormsMemory] = useState<Record<string, FormState>>({}); // the last value of every field regardless if it's shown or not
    const [dirtyFlags, setDirtyFlags] = useState<Record<string, boolean>>({});

    const handleFormChange = (formKey: string, patch: Partial<FormState>) => {
        setFormsMemory((prev) => ({
            ...prev,
            [formKey]: {
                ...prev[formKey],
                ...patch
            }
        }));

        setDirtyFlags((prev) => ({ ...prev, [formKey]: true }));
    };

    const [files, setFiles] = useState<File[]>([]);

    // Remove file at the given index
    const handleRemoveFile = (fileIndex: number) => {
        setFiles((prevFiles) => prevFiles.filter((_, i) => i !== fileIndex));
    };
    // If user selects files
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            // Convert FileList to a real array of File
            const newFiles = Array.from(e.target.files);
            setFiles((prev) => [...prev, ...newFiles])
        }
    };

    if (!contract) {
        return <div>Vertragsdaten nicht gefunden.</div>;
    }
    const formConfigs = reportFormConfigs[contract.contract_type] || []

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        const formStates = determineFormStates(formsMemory, formConfigs);

        const url = editMode ? '/api/report/update' : '/api/report/create';
        const headers: Record<string, string> = {
            'Authorization': `Bearer ${localStorage.getItem('jwt')}`
        };

        let body: FormData | string;

        if (editMode) {
            headers['Content-Type'] = 'application/json';
            const updated = {
                ...formData,
                data_raw: formStates,
            };
            body = JSON.stringify({ reportData: updated });
        } else {
            // build real FormData
            const formDataToSend = new FormData();
            const payload = { ...formData, formulars: formStates };

            Object.entries(payload).forEach(([key, value]) => {
                // if you have nested objects/arrays, JSON.stringify them
                formDataToSend.append(key, typeof value === 'object'
                    ? JSON.stringify(value)
                    : String(value));
            });
            files.forEach(file => formDataToSend.append('file', file));

            body = formDataToSend;
            // **do not** set Content-Type here
        }

        try {
            const response = await fetch(url, {
                method: editMode ? 'PUT' : 'POST',
                headers,
                body
            });

            if (response.ok) {
                const createdReport = await response.json();
                alert(`Der Schadensbericht wurde erfolgreich ${editMode ? 'aktualisiert' : 'erstellt'}.`);
                router.push(`/report/${editMode ? createdReport.report_number : createdReport.data.report_number}`);
            } else {
                alert(`Fehler beim ${editMode ? 'Aktualisieren' : 'Erstellen'} des Schadensberichts.`);
            }
        } catch (error) {
            console.error('Fehler beim Senden des Berichts:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }
    if (!customer) return <div>Kundendaten nicht gefunden.</div>;

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Schaden melden
            </Typography>
            <form onSubmit={handleSubmit}>
                <div className='space-y-4 mx-auto relative'>
                    <div className='pt-4'>
                        <CustomerInformationBox
                            customerData={customer}
                            customer_number={customer_number!}
                            simplified
                            visible
                        />
                    </div>
                    <div>
                        <ContractInformationBox
                            contract={contract}
                            isOffer={false}
                            router={router}
                            simplified
                            visible
                        />
                    </div>
                    <MultiFormBox
                        formConfigs={formConfigs}
                        state={formsMemory}
                        dirtyFlags={dirtyFlags}
                        onChange={handleFormChange}
                    />

                    {/* files */}
                    {!editMode &&
                        <StaticBox title="Anhänge">
                            {files && files.length > 0 && <div className='flex text-sm'>
                                <TableContainer component={Paper}>
                                    <Table>
                                        <TableHead>
                                            <TableRow>
                                                <TableCell>Dateiname</TableCell>
                                                <TableCell align="center">Entfernen</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {files.map((file, index) => (
                                                <TableRow key={index}>
                                                    <TableCell>
                                                        {file.name}
                                                    </TableCell>
                                                    <TableCell align="center">
                                                        <IconButton
                                                            onClick={() => handleRemoveFile(index)}
                                                            color="primary"
                                                        >
                                                            <TrashIcon className='h-6 w-6' />
                                                        </IconButton>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                            </div>}

                            <div className="pt-4 justify-end flex">
                                <Tooltip title="Hier können weitere Dokumente angehängt werden">
                                    <Button
                                        component="label"
                                        variant="contained"
                                        startIcon={<CloudUploadIcon />}
                                    >
                                        Dokumente anhängen
                                        <VisuallyHiddenInput
                                            type="file"
                                            multiple
                                            onChange={handleFileChange}
                                        />
                                    </Button>
                                </Tooltip>
                            </div>
                        </StaticBox>
                    }

                    <div className='flex gap-4 justify-end'>
                        <Tooltip title="Ohne sichern zurück.">
                            <Button onClick={() => window.history.back()} variant="contained">
                                Verwerfen
                            </Button>
                        </Tooltip>
                        <Tooltip title={editMode ? 'Schaden aktulaiseren' : "Neue Schaden anlegen"}>
                            <Button
                                type="submit"
                                color="secondary"
                                variant="contained"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (editMode ? "Aktualisiere..." : 'Melden...') : (editMode ? 'Schaden aktualisieren' : 'Schaden melden')}
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </form>
        </Box>
    );
}

export default ReportForm;

// returns only the values from formsMemory that are present on the screen
const determineFormStates = (formsMemory: Record<string, FormState>, formConfigs: FormConfig[]) => {
    return Object.entries(formsMemory).reduce((acc, [formKey, formMemory]) => {
        const formConfig = formConfigs.find(form => form.key === formKey);
        if (!formConfig) {
            console.error('No form found with form.key=' + formKey);
        }

        const { items = [] } = formConfig ?? {};
        const shownFields = determineShownFields(items, formMemory);

        const formState = Object.entries(formMemory).reduce((acc, [key, value]) => {
            if (shownFields.has(key)) {
                return { ...acc, [key]: value };
            }
            return acc;
        }, {})

        return {
            ...acc,
            [formKey]: formState
        }
    }, {} as Record<string, FormState>);
}