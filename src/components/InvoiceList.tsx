// src/components/InvoiceList.tsx
import {
  Download as DownloadIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { serializeFilters } from '@/components/filters/url';
import { type Invoice } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { formatDateString } from '@/utils/dateUtils';
import {
  formatInvoiceCustomerStatus,
  formatInvoiceStatus,
  formatInvoiceType,
} from '@/utils/keyFormatter';
import { useTableQuery } from '@/utils/useTableQuery';

import { FilterBuilder } from './filters/FilterBuilder';
import { invoiceFieldConfig } from './filters/invoiceFieldConfig';

export default function InvoiceList() {
  const [isExporting, setIsExporting] = useState(false);
  const router = useRouter();
  const session = useSession();

  const table = useTableQuery<any>({
    endpoint: '/api/invoices',
    defaultSortField: 'insuranceStartDate',
    accessToken: session.data?.accessToken,
    enabled: session.status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.items ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: invoices,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleDetailsClick = (
    invoiceData: Invoice,
    event: React.MouseEvent
  ) => {
    const url = `/invoice/${invoiceData.invoiceNumber}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  function handleEditClick(
    invoiceNumber: string,
    event: React.MouseEvent
  ): void {
    const url = `/invoice/${invoiceNumber}/edit`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  }

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);

      const query = new URLSearchParams({
        sortField,
        sortDirection,
        ...serializeFilters(filters),
      });

      const response = await apiFetch(
        `/api/invoices/export-csv?${query.toString()}`,
        {
          raw: true,
        }
      );

      if (!response.ok) {
        throw new Error('CSV Export fehlgeschlagen');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'rechnungsliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Rechnungsliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        {/* Unified filter builder */}
        <FilterBuilder
          fields={invoiceFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="dueDate"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      {loading ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: '2rem',
          }}
        >
          <CircularProgress />
        </div>
      ) : (
        <TableContainer component={Paper} sx={{ marginTop: 4 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('invoiceNumber')}
                >
                  Rechungsnummer{' '}
                  {sortField === 'invoiceNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('type')}
                >
                  Rechungsart{' '}
                  {sortField === 'type' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('contractNumber')}
                >
                  Vertragsnummer{' '}
                  {sortField === 'contractNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('customerNumber')}
                >
                  Kundennummer{' '}
                  {sortField === 'customerNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                {session.data?.roles.includes('asevo-admin') && (
                  <TableCell
                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                    onClick={() => handleSort('agentNumber')}
                  >
                    Maklernummer{' '}
                    {sortField === 'agentNumber' &&
                      (sortDirection === 'asc' ? '⬆' : '⬇')}
                  </TableCell>
                )}
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('agencyNumber')}
                >
                  Agenturnummer{' '}
                  {sortField === 'agencyNumber' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('dueDate')}
                >
                  Fälligkeitsdatum{' '}
                  {sortField === 'dueDate' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('invoiceStatus')}
                >
                  Status{' '}
                  {sortField === 'invoiceStatus' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                  onClick={() => handleSort('customerStatus')}
                >
                  Kunden Status{' '}
                  {sortField === 'customerStatus' &&
                    (sortDirection === 'asc' ? '⬆' : '⬇')}
                </TableCell>
                {/* <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('agent_status')}
                                >
                                    Makler Status {sortField === 'agent_status' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell>
                                <TableCell
                                    sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                                    onClick={() => handleSort('insurance_status')}
                                >
                                    Versicherung Status {sortField === 'insurance_status' && (sortDirection === 'asc' ? '⬆' : '⬇')}
                                </TableCell> */}
                <TableCell
                  align="right"
                  sx={{ fontWeight: 'bold', width: 120 }}
                >
                  Aktionen
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoiceData, index) => (
                <TableRow
                  key={index}
                  hover
                  sx={{ cursor: 'pointer' }}
                  onClick={(event) => handleDetailsClick(invoiceData, event)}
                >
                  <TableCell>{invoiceData.invoiceNumber}</TableCell>
                  <TableCell>{formatInvoiceType(invoiceData.type!)}</TableCell>
                  <TableCell>{invoiceData.contractNumber}</TableCell>
                  <TableCell>{invoiceData.customerNumber}</TableCell>
                  <TableCell>{invoiceData.agentNumber}</TableCell>
                  {session.data?.roles.includes('asevo-admin') && (
                    <TableCell>{invoiceData.agencyNumber}</TableCell>
                  )}
                  <TableCell>
                    {formatDateString(invoiceData.dueDate!).split(',')[0]}
                  </TableCell>
                  <TableCell>
                    {formatInvoiceStatus(invoiceData.invoiceStatus!)}
                  </TableCell>
                  <TableCell>
                    {formatInvoiceCustomerStatus(invoiceData.customerStatus!)}
                  </TableCell>
                  {/* <TableCell>{formatInvoiceAgentStatus(invoiceData.agent_status)}</TableCell>
                                    <TableCell>{formatInvoiceInsuranceStatus(invoiceData.insurance_status)}</TableCell> */}
                  <TableCell align="right">
                    <Tooltip title={`Rechnung bearbeiten`} placement="top">
                      <IconButton
                        onClick={(event) => {
                          event.stopPropagation();
                          handleEditClick(invoiceData.invoiceNumber, event);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={handlePageChange}
          sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '1rem',
        }}
      >
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </div>
    </Box>
  );
}
