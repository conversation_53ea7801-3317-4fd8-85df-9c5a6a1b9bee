import { type Theme } from '@emotion/react';
import { Stack, type SxProps } from '@mui/material';
import { type PropsWithChildren, useEffect, useRef, useState } from 'react';

type Props = PropsWithChildren<{
  sx?: SxProps<Theme>;
}>;

export default function StickyBottomBar(props: Props) {
  const [fixed, setFixed] = useState(false);
  const sentinelRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const el = sentinelRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      (entries) => setFixed(!entries[0].isIntersecting),
      { threshold: 0 }
    );
    observer.observe(el);
    return () => observer.disconnect();
  }, []);

  return (
    <>
      <div
        ref={sentinelRef}
        style={{
          height: 1,
          minWidth: '100%',
          marginTop: 'auto',
          marginBottom: 0,
        }}
      />
      {fixed && (
        <Stack
          direction="column"
          position={fixed ? 'fixed' : 'static'}
          sx={{
            // top: 'auto',
            bottom: 0,
            backgroundColor: 'primary.light',
            minWidth: '100%',
            minHeight: '85px',
            // padding: 2,
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          {props.children}
        </Stack>
      )}

      <Stack
        direction="column"
        sx={{
          backgroundColor: 'primary.light',
          minWidth: '100%',
          minHeight: '85px',
          //   padding: 2,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        {props.children}
      </Stack>
    </>
  );
}
