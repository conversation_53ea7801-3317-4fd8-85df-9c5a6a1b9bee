import { Chip } from "@mui/material";
import { FC } from "react";
import EventIcon from "@mui/icons-material/Event";
import {useTranslations} from "next-intl";
import {calculateDaysLeft} from "@/utils/calculateDaysLeft";

type Props = {
    dueDate: string
}
export const RemainingDaysChip: FC<Props> = ({ dueDate }) => {
    const t = useTranslations()
    const daysLeft = calculateDaysLeft(dueDate)

    let color: "success" | "warning" | "error" = "error";
    let label = t('remainingDaysChip.expired');

    if (daysLeft >= 0) {
        color = daysLeft <= 7 ? "warning" : "success"
        label = t('remainingDaysChip.daysLeft', { daysLeft });
    }

    return (
        <Chip
            icon={<EventIcon />}
            label={label}
            color={color}
            size="small"
            sx={{ fontWeight: 500 }}
        />
    );
};
