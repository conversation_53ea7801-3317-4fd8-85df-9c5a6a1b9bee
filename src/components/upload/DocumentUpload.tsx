import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import { Alert } from '@mui/material';
import {
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import React, { useRef, useState } from 'react';

import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import {
  DOCUMENT_TYPE_VALUES,
  type DocumentType,
  getDocumentTypeTranslationKey,
} from '@/modules/files/types/document-types';
import { useTRPC } from '@/trpc/client';

interface Props {
  caseId: number;
  insurerName?: string;
  onUploadSuccess?: () => void | Promise<void>;
}

// TODO: actual upload logic frontend + backend.
export function DocumentUpload(props: Props) {
  const t = useTranslations();
  const uploadAllowedFileExtensionsConfiguration = t.raw(
    'document_upload.limit_uploaded_file_types_with_html_accept'
  );
  const uploadAllowedFileExtensions =
    uploadAllowedFileExtensionsConfiguration !==
    'document_upload.limit_uploaded_file_types_with_html_accept'
      ? uploadAllowedFileExtensionsConfiguration
      : '';
  const [fileName, setFileName] = useState<string>('');
  const [documentType, setDocumentType] = useState<DocumentType | ''>('');
  const [optionalMessage, setOptionalMessage] = useState<string | undefined>(
    undefined
  );
  const [highlightPickFileButton, setHighlightPickFileButton] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const trpc = useTRPC();

  const {
    mutateAsync: uploadPdf,
    isPending: uploadPdfPending,
    error: uploadPdfError,
  } = useMutation(
    trpc.files.uploadPdf.mutationOptions({
      onSuccess: async (result) => {
        console.log('UPLOAD RESULT:', result);

        // Reset form fields
        setFileName('');
        setDocumentType('');
        setOptionalMessage(undefined);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }

        // Call the onUploadSuccess callback if provided
        if (props.onUploadSuccess) {
          await props.onUploadSuccess();
        }
      },
      onError: (error) => {
        console.log('UPLOAD ERROR:', error);
      },
    })
  );

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (fileName.trim().length <= 0) {
      setHighlightPickFileButton(true);
      setTimeout(() => {
        setHighlightPickFileButton(false);
      }, 1000);
    } else if (!documentType) {
      // Document type is required - form validation should prevent this
      return;
    } else {
      //   getUploadUrl({ caseId: props.caseId, fileName: fileName });
      //   alert('submit!');
      const file = fileInputRef.current?.files?.[0] || null;
      if (file !== null) {
        const arrayBuffer = await file.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        uploadPdf({
          caseId: props.caseId,
          fileName: fileName,
          fileBuffer: uint8Array,
          optionalMessage: optionalMessage,
          documentType: documentType,
        });
      }
    }
  };

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e?.target.files?.[0];
    setFileName(file ? file.name : '');
  };

  return (
    <form
      onSubmit={onSubmit}
      style={{
        maxHeight: '100%',
      }}
    >
      <Stack
        direction="column"
        display="flex"
        sx={{
          width: '600px',
          maxWidth: '100%',
          maxHeight: '100%',
          overflow: 'auto',
        }}
        gap={4}
      >
        <Typography variant="h5" fontWeight={700} color="var(--teal)">
          {t('document_upload.headline_upload_documents')}
        </Typography>

        {/* Error Display */}
        {uploadPdfError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t('document_upload.error_upload_failed')}
          </Alert>
        )}

        <Stack
          direction="row"
          display="flex"
          flexGrow={1}
          gap={1}
          flexWrap="wrap"
        >
          {fileName.trim().length <= 0 && (
            <KvcPlainTextButton
              className={highlightPickFileButton ? 'blink-red' : ''}
              onClick={() => {
                fileInputRef.current?.click();
              }}
              sx={{
                background: 'var(--gray-background)',
                display: 'flex',
                flexGrow: 1,
                flexShrink: 0,
                minWidth: 250,
                width: 'calc(50% -1rem)',
                paddingLeft: 0,
                paddingRight: 0,
              }}
              startIcon={
                <AddCircleOutlineIcon
                  sx={{
                    color: 'var(--teal)',
                    marginRight: 1,
                  }}
                />
              }
            >
              {t('document_upload.pick_document_button_label')}
            </KvcPlainTextButton>
          )}

          {fileName.trim().length > 0 && (
            <KvcPlainTextButton
              className={highlightPickFileButton ? 'blink-red' : ''}
              onClick={() => {
                setFileName('');
              }}
              sx={{
                background: 'var(--gray-background)',
                display: 'flex',
                flexGrow: 1,
                flexShrink: 0,
                minWidth: 250,
                width: 'calc(50% -1rem)',
                paddingLeft: 0,
                paddingRight: 0,
                fontWeight: 400,
                maxWidth: '100%',
              }}
              startIcon={
                <RemoveCircleOutlineIcon
                  sx={{
                    color: 'var(--teal)',
                    marginRight: 1,
                  }}
                />
              }
            >
              <Typography
                sx={{
                  maxWidth: 'calc(100% - 60px)',
                  wordWrap: 'break-word',
                }}
              >
                {fileName}
              </Typography>
            </KvcPlainTextButton>
          )}

          <FormControl fullWidth>
            <InputLabel id="upload-document-type-label">
              {t('document_upload.dropdown_document_type.label')}
            </InputLabel>
            <Select
              labelId="upload-document-type-label"
              id="upload-document-type-select"
              value={documentType}
              label={t('document_upload.dropdown_document_type.label')}
              onChange={(e) => {
                setDocumentType(e.target.value as DocumentType);
              }}
            >
              {DOCUMENT_TYPE_VALUES.map((type) => (
                <MenuItem key={type} value={type}>
                  {t(getDocumentTypeTranslationKey(type))}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Stack>

        <Stack direction="column" gap={1}>
          <Typography fontWeight={700} color="var(--teal)">
            {t('document_upload.optional_message')}
          </Typography>
          <TextField
            multiline
            placeholder={t(
              'document_upload.optional_message_input_placeholder'
            )}
            value={optionalMessage}
            minRows={6}
            onChange={(e) => {
              setOptionalMessage(e.target.value);
            }}
            sx={{
              minHeight: '160px',
              '& .MuiInputBase-root': {
                minHeight: '160px',
              },
            }}
          />
        </Stack>

        <input
          accept={uploadAllowedFileExtensions}
          style={{
            display: 'none',
          }}
          ref={fileInputRef}
          type="file"
          onChange={onFileChange}
        />
        <Stack direction="row" display="flex">
          <Button type="submit" sx={{ fontWeight: 700, marginLeft: 'auto' }}>
            {t('document_upload.button_upload_document')}
          </Button>
        </Stack>
      </Stack>
    </form>
  );
}
