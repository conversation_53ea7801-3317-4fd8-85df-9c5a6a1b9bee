interface Props {
  fillPercent: number;
}

export default function KvcProgressBar(props: Props) {
  return (
    <div
      style={{
        width: '100%',
        backgroundColor: 'var(--progressbar-background)',
      }}
    >
      <div
        style={{
          height: '10px',
          width: `${Math.min(100, Math.max(0, Math.ceil(props.fillPercent)))}%`,
          backgroundColor: 'var(--progressbar-fill)',
        }}
      ></div>
    </div>
  );
}
