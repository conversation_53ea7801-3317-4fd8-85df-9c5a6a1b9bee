// src/components/insuranceForms/BusinessLiabilityInsuranceForm.tsx
// Betriebshaftpflicht

import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { type Dispatch, type SetStateAction, useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface BusinessLiabilityInsuranceFormProps {
  formData: Contract;
  handleChange: (event: any, child?: React.ReactNode) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const BusinessLiabilityInsuranceForm: React.FC<
  BusinessLiabilityInsuranceFormProps
> = ({ formData, setFormData, handleChange }) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    setFormData({
      ...formData,
      coverageAmount: '20000000',
      ...(formData.employeeCount === 0 && { employeeCount: 6 }),
    });
  }, [formData, setFormData]);

  return (
    <div className="flex flex-col gap-4">
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
          <TextField
            type="text"
            label="Betriebsart"
            name="businessType"
            value={formData.businessType}
            onChange={handleChange}
            placeholder="Betriebsart"
            className="w-full p-2"
            required
          />
        </Tooltip>
        <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
          <FormControl required variant="outlined" className="w-full p-2">
            <InputLabel>Tarifgruppe</InputLabel>
            <Select
              name="tariffGroup"
              label="Tarifgruppe"
              value={formData.tariffGroup}
              onChange={handleChange}
              required
            >
              <MenuItem value="Handel">Handel</MenuItem>
              <MenuItem value="Handwerk">Handwerk</MenuItem>
              <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
              <MenuItem value="Gastronomie">Gastronomie</MenuItem>
            </Select>
          </FormControl>
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2">
        {isAdmin && (
          <Tooltip title="Geben Sie hier eine abweichende maximal Anzahl der Mitarbeiter an (entfällt bei leerem Feld).">
            <TextField
              type="number"
              label="Maximale Mitarbeiteranzahl"
              name="employeeCount"
              value={formData.employeeCount}
              onChange={handleChange}
              className="w-full p-2"
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default BusinessLiabilityInsuranceForm;
