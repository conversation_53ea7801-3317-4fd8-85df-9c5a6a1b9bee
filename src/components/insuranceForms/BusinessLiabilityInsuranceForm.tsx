// src/components/insuranceForms/BusinessLiabilityInsuranceForm.tsx
// Betriebshaftpflicht

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { FormControl, InputLabel, MenuItem, Select, TextField, Tooltip } from '@mui/material';

interface BusinessLiabilityInsuranceFormProps {
    formData: ContractData;
    handleChange: (event: any, child?: React.ReactNode) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const BusinessLiabilityInsuranceForm: React.FC<BusinessLiabilityInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        setFormData({
            ...formData,
            "coverage_amount": "20000000",
            ...(formData.employee_count == 0 && { "employee_count": 6 }),
        })
    }, [])

    return (
        <div className="flex flex-col gap-4">
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
                    <TextField
                        type="text"
                        label="Betriebsart"
                        name="business_type"
                        value={formData.business_type}
                        onChange={handleChange}
                        placeholder="Betriebsart"
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
                <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
                    <FormControl required variant="outlined" className="w-full p-2">
                        <InputLabel>Tarifgruppe</InputLabel>
                        <Select
                            name="tariff_group"
                            label="Tarifgruppe"
                            value={formData.tariff_group}
                            onChange={handleChange}
                            required
                        >
                            <MenuItem value="Handel">Handel</MenuItem>
                            <MenuItem value="Handwerk">Handwerk</MenuItem>
                            <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
                            <MenuItem value="Gastronomie">Gastronomie</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Geben Sie hier eine abweichende maximal Anzahl der Mitarbeiter an (entfällt bei leerem Feld).">
                        <TextField
                            type="number"
                            label="Maximale Mitarbeiteranzahl"
                            name="employee_count"
                            value={formData.employee_count}
                            onChange={handleChange}
                            className="w-full p-2"
                        />
                    </Tooltip>}
            </div>
        </div>
    );
};

export default BusinessLiabilityInsuranceForm;
