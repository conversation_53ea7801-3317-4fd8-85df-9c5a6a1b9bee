// src/components/insuranceForms/ConstructionOwnerLiabilityForm.tsx
// Bauherrenhaftpflicht

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import { TextField, FormControlLabel, Checkbox, Tooltip } from "@mui/material";
import RiskAddressForm from './RiskAddressForm';

interface ConstructionOwnerLiabilityFormProps {
    formData: ContractData;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const ConstructionOwnerLiabilityForm: React.FC<ConstructionOwnerLiabilityFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        setFormData({
            ...formData,
            "payment_mode": "Einmalzahlung",
            "coverage_amount": "60000000",
            "object_type": formData.object_type || "Neubau eines Einfamilienhauses"
        })
    }, [])

    useEffect(() => {
        if (formData.building_sum !== undefined) {
            const maxBuildingSum = 2000000
            if ((formData.building_sum) > maxBuildingSum) {
                setFormData({
                    ...formData,
                    building_sum: maxBuildingSum,
                });
            }
        }
    }, [formData.building_sum, setFormData]);

    return (
        <div>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-1 gap-2 mt-8'>
                <Tooltip title="Aktivieren Sie diese Option, wenn es sich um ein privates Wohngebäude handelt. Dies kann Einfluss auf die Versicherungsbedingungen und -prämien haben.">
                    <FormControlLabel
                        control={<Checkbox
                            name="private_building"
                            onChange={handleChange}
                            required />}
                        label="Privates Wohngebäude" />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Geben Sie die Objektart an.">
                        <TextField
                            type="text"
                            label="Objektart:"
                            name="object_type"
                            value={formData.object_type}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                            InputLabelProps={{
                                shrink: true,
                            }} />
                    </Tooltip>
                }
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Die Zahlungsweise gibt an, wie die Versicherungsprämie für die Bauherrenhaftpflichtversicherung beglichen wird. Dieses Feld ist in diesem Fall festgelegt und kann nicht bearbeitet werden.">
                    <TextField
                        type="text"
                        label="Zahlungsweise"
                        name="payment_mode"
                        value={formData.payment_mode}
                        onChange={handleChange}
                        className="w-full p-2"
                        disabled
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie die gesamte Bausumme an, die für den Bau des Gebäudes erforderlich war. Diese Summe dient als Grundlage für die Berechnung der Versicherungssumme.">
                    <TextField
                        type="number"
                        label="Bausumme in €"
                        name="building_sum"
                        value={formData.building_sum || ""}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-1 gap-2 mt-8'>
                <Tooltip title="Die Deckungssumme gibt an, bis zu welchem Betrag die Versicherung im Schadensfall leistet. Diese Summe ist für dieses Feld festgelegt und kann nicht geändert werden.">
                    <TextField
                        type="text"
                        label="Deckungssumme"
                        name="coverage_amount"
                        value={formData.coverage_amount}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                        disabled={localStorage.getItem("is_admin") == "false"}
                    />
                </Tooltip>
            </div>
        </div>
    );
};

export default ConstructionOwnerLiabilityForm;