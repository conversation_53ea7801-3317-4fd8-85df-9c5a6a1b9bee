// src/components/insuranceForms/ConstructionOwnerLiabilityForm.tsx
// Bauherrenhaftpflicht

import { Checkbox, FormControlLabel, TextField, Tooltip } from '@mui/material';
import React, { type Dispatch, type SetStateAction, useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface ConstructionOwnerLiabilityFormProps {
  formData: Contract;
  handleChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const ConstructionOwnerLiabilityForm: React.FC<
  ConstructionOwnerLiabilityFormProps
> = ({ formData, setFormData, handleChange }) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    setFormData({
      ...formData,
      paymentMode: 'Einmalzahlung',
      coverageAmount: '60000000',
      objectType: formData.objectType || 'Neubau eines Einfamilienhauses',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (formData.buildingSum !== undefined) {
      const maxBuildingSum = 2000000;
      if ((formData.buildingSum || 0) > maxBuildingSum) {
        setFormData({
          ...formData,
          buildingSum: maxBuildingSum,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.buildingSum, setFormData]);

  return (
    <div>
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-1 gap-2 mt-8">
        <Tooltip title="Aktivieren Sie diese Option, wenn es sich um ein privates Wohngebäude handelt. Dies kann Einfluss auf die Versicherungsbedingungen und -prämien haben.">
          <FormControlLabel
            control={
              <Checkbox
                name="privateBuilding"
                onChange={handleChange}
                required
              />
            }
            label="Privates Wohngebäude"
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        {isAdmin && (
          <Tooltip title="Geben Sie die Objektart an.">
            <TextField
              type="text"
              label="Objektart:"
              name="objectType"
              value={formData.objectType}
              onChange={handleChange}
              className="w-full p-2"
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Die Zahlungsweise gibt an, wie die Versicherungsprämie für die Bauherrenhaftpflichtversicherung beglichen wird. Dieses Feld ist in diesem Fall festgelegt und kann nicht bearbeitet werden.">
          <TextField
            type="text"
            label="Zahlungsweise"
            name="paymentMode"
            value={formData.paymentMode}
            onChange={handleChange}
            className="w-full p-2"
            disabled
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie die gesamte Bausumme an, die für den Bau des Gebäudes erforderlich war. Diese Summe dient als Grundlage für die Berechnung der Versicherungssumme.">
          <TextField
            type="number"
            label="Bausumme in €"
            name="buildingSum"
            value={formData.buildingSum || ''}
            onChange={handleChange}
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-1 gap-2 mt-8">
        <Tooltip title="Die Deckungssumme gibt an, bis zu welchem Betrag die Versicherung im Schadensfall leistet. Diese Summe ist für dieses Feld festgelegt und kann nicht geändert werden.">
          <TextField
            type="text"
            label="Deckungssumme"
            name="coverageAmount"
            value={formData.coverageAmount}
            onChange={handleChange}
            className="w-full p-2"
            required
            disabled={!isAdmin}
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default ConstructionOwnerLiabilityForm;
