// src/components/insuranceForms/PersonalLiabilityInsuranceForm.tsx
// Privathaftpflicht

import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  TextField,
  Tooltip,
} from '@mui/material';
import React, {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useState,
} from 'react';

import { type Contract } from '@/generated/prisma-postgres';

interface PersonalLiabilityInsuranceFormProps {
  formData: Contract;
  handleChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const PersonalLiabilityInsuranceForm: React.FC<
  PersonalLiabilityInsuranceFormProps
> = ({ formData, setFormData, handleChange }) => {
  const [familyCoverage, setFamilyCoverage] = useState<string>();

  useEffect(() => {
    setFamilyCoverage(formData.familyCoverage ? 'family' : 'single');
    setFormData({
      ...formData,
      coverageAmount: '60000000',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSelectChange = (
    e: SelectChangeEvent<string>,
    // eslint-disable-next-line unused-imports/no-unused-vars
    child: React.ReactNode
  ) => {
    const { value } = e.target;
    setFamilyCoverage(value);
    if (value === 'family') {
      setFormData({
        ...formData,
        familyCoverage: true,
      });
    } else {
      setFormData({
        ...formData,
        familyCoverage: false,
      });
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-2">
        <Tooltip title="Aktivieren Sie dieses Feld, wenn die Privathaftpflichtversicherung auch Ihre Familienangehörigen mit abdecken soll. Dies erweitert den Versicherungsschutz auf weitere Mitglieder Ihres Haushalts.">
          <FormControl required variant="outlined" className="w-full p-2">
            <InputLabel>Risikobezeichnung</InputLabel>
            <Select
              name="familyCoverage"
              label="Risikobezeichnung"
              value={familyCoverage}
              onChange={handleSelectChange}
              required
            >
              <MenuItem value="single">Single/Alleinerziehend</MenuItem>
              <MenuItem value="family">Familiendeckung</MenuItem>
            </Select>
          </FormControl>
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        {!formData.familyCoverage && (
          <Tooltip title="Geben Sie hier den Vornamen des versicherten Familienmitglieds an, wenn die Familien-Deckung aktiviert ist. Dieses Feld ist nur sichtbar, wenn Familienmitglieder versichert werden.">
            <TextField
              label="Vorname"
              type="text"
              name="privateFirstName"
              value={formData.privateFirstName || ''}
              onChange={handleChange}
              className="w-full p-2"
              required
            />
          </Tooltip>
        )}
        <Tooltip title="Geben Sie den vollständigen Nachnamen der versicherten Familie bzw. des versicherten Familiennmitglieds ein.">
          <TextField
            label="Nachname"
            type="text"
            name="privateName"
            value={formData.privateName}
            onChange={handleChange}
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>
      <label hidden>Deckungssumme</label>
      <input
        type="text"
        name="coverageAmount"
        value={formData.coverageAmount || ''}
        onChange={handleChange}
        placeholder="Deckungssumme"
        className="w-full p-2"
        hidden
        required
      />
    </div>
  );
};

export default PersonalLiabilityInsuranceForm;
