// src/components/insuranceForms/PersonalLiabilityInsuranceForm.tsx
// Privathaftpflicht

import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { ContractData } from '../../types';
import { FormControl, TextField, InputLabel, MenuItem, Select, SelectChangeEvent, Tooltip } from '@mui/material';

interface PersonalLiabilityInsuranceFormProps {
    formData: ContractData;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const PersonalLiabilityInsuranceForm: React.FC<PersonalLiabilityInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {

    const [familyCoverage, setFamilyCoverage] = useState<string>()

    useEffect(() => {
        setFamilyCoverage(formData.family_coverage ? "family" : "single")
        setFormData({
            ...formData,
            "coverage_amount": "60000000"
        })
    }, [])

    const handleSelectChange = (e: SelectChangeEvent<string>, child: React.ReactNode) => {
        const { name, value } = e.target;
        setFamilyCoverage(value)
        if (value == "family") {
            setFormData({
                ...formData,
                "family_coverage": true
            })
        } else {
            setFormData({
                ...formData,
                "family_coverage": false
            })
        }
    }

    return (
        <div>
            <div className='grid grid-cols-2 gap-2'>
                <Tooltip title="Aktivieren Sie dieses Feld, wenn die Privathaftpflichtversicherung auch Ihre Familienangehörigen mit abdecken soll. Dies erweitert den Versicherungsschutz auf weitere Mitglieder Ihres Haushalts.">
                    <FormControl required variant="outlined" className="w-full p-2">
                        <InputLabel>Risikobezeichnung</InputLabel>
                        <Select
                            name="family_coverage"
                            label="Risikobezeichnung"
                            value={familyCoverage}
                            onChange={handleSelectChange}
                            required
                        >
                            <MenuItem value="single">Single/Alleinerziehend</MenuItem>
                            <MenuItem value="family">Familiendeckung</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {!formData.family_coverage &&
                    <Tooltip title="Geben Sie hier den Vornamen des versicherten Familienmitglieds an, wenn die Familien-Deckung aktiviert ist. Dieses Feld ist nur sichtbar, wenn Familienmitglieder versichert werden.">
                        <TextField
                            label="Vorname"
                            type="text"
                            name="private_first_name"
                            value={formData.private_first_name || ''}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                        />
                    </Tooltip>
                }
                <Tooltip title="Geben Sie den vollständigen Nachnamen der versicherten Familie bzw. des versicherten Familiennmitglieds ein.">
                    <TextField
                        label="Nachname"
                        type="text"
                        name="private_name"
                        value={formData.private_name}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>
            <label hidden>Deckungssumme</label>
            <input
                type="text"
                name="coverage_amount"
                value={formData.coverage_amount}
                onChange={handleChange}
                placeholder="Deckungssumme"
                className="w-full p-2"
                hidden
                required
            />
        </div>
    );
};

export default PersonalLiabilityInsuranceForm;