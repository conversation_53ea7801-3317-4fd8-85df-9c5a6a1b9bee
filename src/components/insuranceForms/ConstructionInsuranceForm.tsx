// src/components/insuranceForms/ConstructionInsuranceForm.tsx
// Bauleistung

import { Checkbox, FormControlLabel, TextField, Tooltip } from '@mui/material';
import React, { type Dispatch, type SetStateAction, useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface ConstructionInsuranceFormProps {
  formData: Contract;
  handleChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const ConstructionInsuranceForm: React.FC<ConstructionInsuranceFormProps> = ({
  formData,
  setFormData,
  handleChange,
}) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    setFormData({
      ...formData,
      paymentMode: 'Einmalzahlung',
      premiumRate: 0.0008, // TODO: Why hardcoded value?
      objectType: formData.objectType || 'Neubau eines Einfamilienhauses',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (formData.insuranceSum !== undefined && !isAdmin) {
      const maxInsuranceSum = 2000000;
      if ((formData.insuranceSum || 0) > maxInsuranceSum) {
        setFormData({
          ...formData,
          insuranceSum: maxInsuranceSum,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.insuranceSum, setFormData]);

  return (
    <div>
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2 mt-8">
        {isAdmin && (
          <Tooltip title="Geben Sie die Objektart an.">
            <TextField
              type="text"
              label="Objektart:"
              name="objectType"
              value={formData.objectType}
              onChange={handleChange}
              className="w-full p-2"
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-2 gap-2 pt-8">
        <Tooltip title="Die Zahlungsweise gibt an, wie die Versicherungsprämie für die Bauleistungsversicherung beglichen wird. Dieses Feld ist in diesem Fall festgelegt und kann nicht bearbeitet werden.">
          <TextField
            type="text"
            label="Zahlungsweise"
            name="paymentMode"
            value={formData.paymentMode}
            className="w-full p-2"
            disabled
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 pt-4">
        <Tooltip title="Aktivieren Sie diese Option, wenn das versicherte Bauvorhaben ein privates Wohngebäude betrifft. Diese Auswahl beeinflusst möglicherweise den Versicherungsschutz und die Prämienberechnung.">
          <FormControlLabel
            control={
              <Checkbox
                name="privateBuilding"
                onChange={handleChange}
                required
              />
            }
            label="Privates Wohngebäude"
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
          <TextField
            type="number"
            label="Versicherungssumme in €"
            name="insuranceSum"
            value={formData.insuranceSum || ''}
            onChange={handleChange}
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default ConstructionInsuranceForm;
