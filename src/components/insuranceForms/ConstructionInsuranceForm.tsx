// src/components/insuranceForms/ConstructionInsuranceForm.tsx
// Bauleistung

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import { TextField, FormControlLabel, Checkbox, Tooltip } from "@mui/material";
import RiskAddressForm from './RiskAddressForm';

interface ConstructionInsuranceFormProps {
    formData: ContractData;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const ConstructionInsuranceForm: React.FC<ConstructionInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        setFormData({
            ...formData,
            "payment_mode": "Einmalzahlung",
            "premium_rate": 0.0008,
            "object_type": formData.object_type || "Neubau eines Einfamilienhauses"
        })
    }, [])

    useEffect(() => {
        if (formData.insurance_sum !== undefined && localStorage.getItem("is_admin") == "false") {
            const maxInsureanceSum = 2000000
            if ((formData.insurance_sum) > maxInsureanceSum) {
                setFormData({
                    ...formData,
                    insurance_sum: maxInsureanceSum,
                });
            }
        }
    }, [formData.insurance_sum, setFormData]);

    return (
        <div>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Geben Sie die Objektart an.">
                        <TextField
                            type="text"
                            label="Objektart:"
                            name="object_type"
                            value={formData.object_type}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                            InputLabelProps={{
                                shrink: true,
                            }} />
                    </Tooltip>
                }
            </div>
            <div className='grid grid-cols-2 gap-2 pt-8'>
                <Tooltip title="Die Zahlungsweise gibt an, wie die Versicherungsprämie für die Bauleistungsversicherung beglichen wird. Dieses Feld ist in diesem Fall festgelegt und kann nicht bearbeitet werden.">
                    <TextField
                        type="text"
                        label="Zahlungsweise"
                        name="payment_mode"
                        value={formData.payment_mode}
                        className="w-full p-2"
                        disabled
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 pt-4'>
                <Tooltip title="Aktivieren Sie diese Option, wenn das versicherte Bauvorhaben ein privates Wohngebäude betrifft. Diese Auswahl beeinflusst möglicherweise den Versicherungsschutz und die Prämienberechnung.">
                    <FormControlLabel
                        control={<Checkbox
                            name="private_building"
                            onChange={handleChange}
                            required />}
                        label="Privates Wohngebäude" />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
                    <TextField
                        type="number"
                        label="Versicherungssumme in €"
                        name="insurance_sum"
                        value={formData.insurance_sum || ""}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>
        </div>
    );
};

export default ConstructionInsuranceForm;