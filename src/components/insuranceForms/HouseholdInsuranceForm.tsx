// src/components/insuranceForms/HouseholdInsuranceForm.tsx
// Hausrat

import React, { useEffect } from 'react';
import { ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { FormControlLabel, Checkbox, TextField, FormControl, InputLabel, MenuItem, Select, InputAdornment, Tooltip } from '@mui/material';

interface HouseholdInsuranceFormProps {
    formData: ContractData;
    setFormData: React.Dispatch<React.SetStateAction<ContractData>>;
    handleChange: (event: any, child?: React.ReactNode) => void;
    errors: { [key: string]: string };
}

const HouseholdInsuranceForm: React.FC<HouseholdInsuranceFormProps> = ({ formData, setFormData, handleChange, errors }) => {

    useEffect(() => {
        setFormData({
            ...formData,
            "object_type": formData.object_type || "Wohnung in einem Mehrfamilienhaus"
        })
    }, [])

    useEffect(() => {
        setFormData({
            ...formData,
            living_area: 0
        })
    }, [formData.insurance_start_date])

    return (
        <div>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Geben Sie die Objektart an.">
                        <TextField
                            type="text"
                            label="Objektart:"
                            name="object_type"
                            value={formData.object_type}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                            InputLabelProps={{
                                shrink: true,
                            }} />
                    </Tooltip>
                }
            </div>
            <div className='grid grid-cols-1 gap-2 mt-8'>
                <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude nach Bauartklasse 1 (BAK1) gebaut wurde. Diese Klasse umfasst massiv gebaute Gebäude, die als besonders robust gelten.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_bak1"
                            checked={formData.building_type === "BAK1"}
                            onChange={handleChange}
                            required />}
                        label="Bauart BAK1" />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, wenn das Objekt ganzjährig bewohnt ist, da dies das Risiko für Versicherungsschäden beeinflussen kann.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_permanently_occupied"
                            checked={formData.is_permanently_occupied}
                            onChange={handleChange}
                            required />}
                        label="ständig bewohnt" />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie die gesamte Wohnfläche in Quadratmetern an. Dies ist relevant für die Berechnung des Versicherungsschutzes.">
                    <TextField
                        type="number"
                        label="Wohnfläche"
                        name="living_area"
                        value={formData.living_area !== undefined ? formData.living_area : ""}
                        onChange={handleChange}
                        placeholder="Wohnfläche"
                        className="w-full p-2"
                        required
                        error={!!errors.living_area}
                        helperText={errors.living_area}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">m²</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
                <Tooltip title="Geben Sie die gewünschte Versicherungssumme in Euro ein. Der Höchstwert ist auf 500.000 Euro begrenzt.">
                    <TextField
                        type="number"
                        label="Versicherungssumme"
                        name="insurance_sum"
                        value={formData.insurance_sum}
                        onChange={handleChange}
                        placeholder="Versicherungssumme"
                        className="w-full p-2"
                        required
                        error={!!errors.insurance_sum}
                        helperText={errors.insurance_sum}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">€</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>

            </div>
            <div className='grid grid-cols-1 gap-2 mt-8'>
                <Tooltip title="Versicherten Gefahren gemäß Alpha Expert:
                - Feuer: Schutz gegen Schäden durch Feuer und Brand.
                - ED/V: Versicherung gegen Einbruchdiebstahl und daraus resultierenden Vandalismus.
                - LW: Absicherung gegen Schäden durch austretendes Leitungswasser.
                - ST/H: Schutz vor Schäden durch Sturm und Hagel.">
                    <FormControlLabel
                        control={<Checkbox
                            name="covered_risks"
                            checked={formData.covered_risks}
                            onChange={handleChange} />}
                        label="Abdeckung gemäß Alpha Expert" />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_elementar"
                            checked={formData.is_elementar || false}
                            onChange={handleChange} />}
                        label="Elementar" />
                </Tooltip>

                {formData.is_elementar && <Tooltip title="Wählen Sie die Zürs Zone aus.">
                    <FormControl
                        required variant="outlined"
                        className="w-full p-2"
                    >
                        <InputLabel hidden={!formData.is_elementar}>Zürs Zone</InputLabel>
                        <Select
                            name="zuers_zone"
                            value={formData.zuers_zone}
                            label="Zürs Zone"
                            onChange={handleChange}
                            hidden={!formData.is_elementar}
                            required={formData.is_elementar}
                        >
                            <MenuItem value="1">1</MenuItem>
                            <MenuItem value="2">2</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>}
            </div>
        </div>
    );
};

export default HouseholdInsuranceForm;
