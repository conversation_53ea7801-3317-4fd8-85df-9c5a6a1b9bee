// src/components/insuranceForms/HouseholdInsuranceForm.tsx
// Hausrat

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface HouseholdInsuranceFormProps {
  formData: Contract;
  setFormData: React.Dispatch<React.SetStateAction<Contract>>;
  handleChange: (event: any, child?: React.ReactNode) => void;
  errors: { [key: string]: string };
}

const HouseholdInsuranceForm: React.FC<HouseholdInsuranceFormProps> = ({
  formData,
  setFormData,
  handleChange,
  errors,
}) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    setFormData({
      ...formData,
      objectType: formData.objectType || 'Wohnung in einem Mehrfamilienhaus',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setFormData({
      ...formData,
      livingArea: 0,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.insuranceStartDate]);

  return (
    <div>
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2 mt-8">
        {isAdmin && (
          <Tooltip title="Geben Sie die Objektart an.">
            <TextField
              type="text"
              label="Objektart:"
              name="objectType"
              value={formData.objectType}
              onChange={handleChange}
              className="w-full p-2"
              required
              InputLabelProps={{
                shrink: true,
              }}
            />
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-1 gap-2 mt-8">
        <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude nach Bauartklasse 1 (BAK1) gebaut wurde. Diese Klasse umfasst massiv gebaute Gebäude, die als besonders robust gelten.">
          <FormControlLabel
            control={
              <Checkbox
                name="isBak1"
                checked={formData.buildingType === 'BAK1'}
                onChange={handleChange}
                required
              />
            }
            label="Bauart BAK1"
          />
        </Tooltip>
        <Tooltip title="Aktivieren Sie diese Option, wenn das Objekt ganzjährig bewohnt ist, da dies das Risiko für Versicherungsschäden beeinflussen kann.">
          <FormControlLabel
            control={
              <Checkbox
                name="isPermanentlyOccupied"
                checked={formData.isPermanentlyOccupied}
                onChange={handleChange}
                required
              />
            }
            label="ständig bewohnt"
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie die gesamte Wohnfläche in Quadratmetern an. Dies ist relevant für die Berechnung des Versicherungsschutzes.">
          <TextField
            type="number"
            label="Wohnfläche"
            name="livingArea"
            value={formData.livingArea !== undefined ? formData.livingArea : ''}
            onChange={handleChange}
            placeholder="Wohnfläche"
            className="w-full p-2"
            required
            error={!!errors.livingArea}
            helperText={errors.livingArea}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">m²</InputAdornment>
                ),
              },
            }}
          />
        </Tooltip>
        <Tooltip title="Geben Sie die gewünschte Versicherungssumme in Euro ein. Der Höchstwert ist auf 500.000 Euro begrenzt.">
          <TextField
            type="number"
            label="Versicherungssumme"
            name="insuranceSum"
            value={formData.insuranceSum}
            onChange={handleChange}
            placeholder="Versicherungssumme"
            className="w-full p-2"
            required
            error={!!errors.insuranceSum}
            helperText={errors.insuranceSum}
            slotProps={{
              input: {
                endAdornment: <InputAdornment position="end">€</InputAdornment>,
              },
            }}
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-1 gap-2 mt-8">
        <Tooltip
          title="Versicherten Gefahren gemäß Alpha Expert:
                - Feuer: Schutz gegen Schäden durch Feuer und Brand.
                - ED/V: Versicherung gegen Einbruchdiebstahl und daraus resultierenden Vandalismus.
                - LW: Absicherung gegen Schäden durch austretendes Leitungswasser.
                - ST/H: Schutz vor Schäden durch Sturm und Hagel."
        >
          <FormControlLabel
            control={
              <Checkbox
                name="coveredRisks"
                checked={formData.coveredRisks}
                onChange={handleChange}
              />
            }
            label="Abdeckung gemäß Alpha Expert"
          />
        </Tooltip>
        <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
          <FormControlLabel
            control={
              <Checkbox
                name="isElementar"
                checked={formData.isElementar || false}
                onChange={handleChange}
              />
            }
            label="Elementar"
          />
        </Tooltip>

        {formData.isElementar && (
          <Tooltip title="Wählen Sie die Zürs Zone aus.">
            <FormControl required variant="outlined" className="w-full p-2">
              <InputLabel hidden={!formData.isElementar}>Zürs Zone</InputLabel>
              <Select
                name="zuersZone"
                value={formData.zuersZone}
                label="Zürs Zone"
                onChange={handleChange}
                hidden={!formData.isElementar}
                required={formData.isElementar}
              >
                <MenuItem value="1">1</MenuItem>
                <MenuItem value="2">2</MenuItem>
              </Select>
            </FormControl>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default HouseholdInsuranceForm;
