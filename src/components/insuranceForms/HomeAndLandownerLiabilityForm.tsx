// src/components/insuranceForms/HomeAndLandownerLiabilityForm.tsx
// Haus- und Grundbesitzerhaftpflicht

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { FormControl, InputLabel, MenuItem, Select, TextField, Tooltip } from '@mui/material';

interface HomeAndLandownerLiabilityFormProps {
    formData: ContractData;
    handleChange: (event: any, child?: React.ReactNode) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const HomeAndLandownerLiabilityForm: React.FC<HomeAndLandownerLiabilityFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        setFormData({
            ...formData,
            "coverage_amount": "60000000",
            "object_type": formData.object_type || "Einfamilienhaus"
        })
    }, [])

    return (
        <div className="flex flex-col gap-4">
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
           
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie die Art des versicherten Gebäudes an (nur Ein- oder Zweifamilienhäuser).">
                        <TextField
                            type="text"
                            label="Objektart"
                            name="object_type"
                            value={formData.object_type}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                            InputLabelProps={{
                                shrink: true,
                            }} />
                    </Tooltip>
            </div>
            <label hidden>Deckungssumme</label>
            <input
                type="text"
                name="coverage_amount"
                value={formData.coverage_amount}
                onChange={handleChange}
                placeholder="Deckungssumme"
                className="w-full p-2"
                hidden
                required
            />
        </div>
    );
};

export default HomeAndLandownerLiabilityForm;