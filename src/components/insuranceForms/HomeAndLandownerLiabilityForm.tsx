// src/components/insuranceForms/HomeAndLandownerLiabilityForm.tsx
// Haus- und Grundbesitzerhaftpflicht

import { TextField, Tooltip } from '@mui/material';
import React, { type Dispatch, type SetStateAction, useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';

import RiskAddressForm from './RiskAddressForm';

interface HomeAndLandownerLiabilityFormProps {
  formData: Contract;
  handleChange: (event: any, child?: React.ReactNode) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const HomeAndLandownerLiabilityForm: React.FC<
  HomeAndLandownerLiabilityFormProps
> = ({ formData, setFormData, handleChange }) => {
  useEffect(() => {
    setFormData({
      ...formData,
      coverageAmount: '60000000',
      objectType: formData.objectType || 'Einfamilienhaus',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col gap-4">
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />

      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie die Art des versicherten Gebäudes an (nur Ein- oder Zweifamilienhäuser).">
          <TextField
            type="text"
            label="Objektart"
            name="objectType"
            value={formData.objectType}
            onChange={handleChange}
            className="w-full p-2"
            required
            InputLabelProps={{
              shrink: true,
            }}
          />
        </Tooltip>
      </div>
      <label hidden>Deckungssumme</label>
      <input
        type="text"
        name="coverageAmount"
        value={formData.coverageAmount || ''}
        onChange={handleChange}
        placeholder="Deckungssumme"
        className="w-full p-2"
        hidden
        required
      />
    </div>
  );
};

export default HomeAndLandownerLiabilityForm;
