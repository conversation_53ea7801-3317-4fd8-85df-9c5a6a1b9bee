// src/components/insuranceForms/AccidentInsuranceContactForm.tsx
// Unfallversicherung Kontaktformular

import React, { useEffect, useState } from 'react';
import Tooltip from '../Tooltip';
import { CustomerData } from '@/types';
import CustomerInformationBox from '../box/CustomerInformationBox';
import { Button, TextField } from '@mui/material';

interface AccidentInsuranceContactFormProps {
    customer_number: string;
}

const AccidentInsuranceContactForm: React.FC<AccidentInsuranceContactFormProps> = ({ customer_number }) => {

    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [loading, setLoading] = useState(true);
    const [formData, setFormData] = useState({
        text: '',
        contract_type: 'unfallversicherung',
        customer_data: {}
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const token = localStorage.getItem("jwt") || "";

    useEffect(() => {
        const fetchData = async () => {
            try {
                const customerResponse = await fetch(`/api/customer/${customer_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                if (!customerResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const customerData = await customerResponse.json();
                setFormData({
                    ...formData,
                    customer_data: customerData
                })
                setCustomer(customerData);
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [customer_number]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const response = await fetch('/api/contracts/special', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
                body: JSON.stringify(formData),
            });

            if (!response.ok) {
                throw new Error('Email sending failed');
            }
            alert('Ihre Anfrage zur Unfallversicherung wurde verschickt.');
            setFormData({
                text: '',
                contract_type: 'unfallversicherung',
                customer_data: {}
            });
        } catch (error) {
            alert('Fehler beim Senden der Anfrage. Bitte versuchen Sie es erneut.' + error);
        } finally {
            setIsSubmitting(false);
        }
    };

    if (!customer) {
        return <div>Fehler beim Laden der Kundendetails.</div>;
    }

    if (loading) {
        return <div>Lade Kundendetails... </div>;
    }

    return (
        <div className='p-10 space-y-4 mx-auto max-w-3xl'>
            <h1 className="text-3xl text-center text-alpha_blue my-5">Unfallversicherung anfragen</h1>
            <CustomerInformationBox customerData={customer} customer_number={customer_number} />
            <form onSubmit={handleSubmit} className='space-y-4 mx-auto mt-5 p-5 border'>
                <div className='grid grid-cols-1 gap-2'>
                    <Tooltip text="Beschreiben Sie die Anforderungen.">
                        <TextField
                            name="text"
                            label="Anfrage"
                            multiline
                            minRows={4}
                            value={formData.text}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                        />
                    </Tooltip>
                </div>
            </form>
            <div className='flex justify-end'>
                <Button
                    type="submit"
                    color="secondary"
                    variant="contained"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? 'Senden...' : 'Senden'}
                </Button>
            </div>
        </div>
    );
};

export default AccidentInsuranceContactForm;