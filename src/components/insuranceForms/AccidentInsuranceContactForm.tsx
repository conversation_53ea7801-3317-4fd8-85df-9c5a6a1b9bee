// src/components/insuranceForms/AccidentInsuranceContactForm.tsx
// Unfallversicherung Kontaktformular

import { Button, TextField } from '@mui/material';
import React, { useEffect, useState } from 'react';

import CustomerInformationBox from '@/components/box/CustomerInformationBox';
import Tooltip from '@/components/Tooltip';
import { type Customer } from '@/generated/prisma-postgres';
import { type CustomerData } from '@/types';
import { apiFetch } from '@/utils/apiFetch';

interface AccidentInsuranceContactFormProps {
  customerNumber: string; // camelCase
}

const AccidentInsuranceContactForm: React.FC<
  AccidentInsuranceContactFormProps
> = ({ customerNumber }) => {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    text: '',
    contractType: 'unfallversicherung', // camelCase
    customerData: {} as Partial<Customer>, // camelCase
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const customerResponse = await apiFetch(
          `/api/customer/${customerNumber}`,
          {
            method: 'GET',
            raw: true,
          }
        );

        if (!customerResponse.ok) {
          throw new Error('Failed to fetch data');
        }

        const customerData: CustomerData = await customerResponse.json();
        setFormData((prev) => ({
          ...prev,
          customerData: customerData,
        }));
        setCustomer(customerData as any);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [customerNumber]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Convert back to API expected keys (snake_case) in the request body
      const payload = {
        text: formData.text,
        contract_type: formData.contractType,
        customer_data: formData.customerData,
      };

      const response = await apiFetch('/api/contracts/special', {
        method: 'POST',
        raw: true,
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Email sending failed');
      }
      alert('Ihre Anfrage zur Unfallversicherung wurde verschickt.');
      setFormData({
        text: '',
        contractType: 'unfallversicherung',
        customerData: {},
      });
    } catch (error) {
      alert(
        'Fehler beim Senden der Anfrage. Bitte versuchen Sie es erneut.' + error
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!customer) {
    return <div>Fehler beim Laden der Kundendetails.</div>;
  }

  if (loading) {
    return <div>Lade Kundendetails... </div>;
  }

  return (
    <div className="p-10 space-y-4 mx-auto max-w-3xl">
      <h1 className="text-3xl text-center text-alpha_blue my-5">
        Unfallversicherung anfragen
      </h1>
      <CustomerInformationBox
        customerData={customer}
        customerNumber={customerNumber}
      />
      <form
        onSubmit={handleSubmit}
        className="space-y-4 mx-auto mt-5 p-5 border"
      >
        <div className="grid grid-cols-1 gap-2">
          <Tooltip text="Beschreiben Sie die Anforderungen.">
            <TextField
              name="text"
              label="Anfrage"
              multiline
              minRows={4}
              value={formData.text}
              onChange={handleChange}
              className="w-full p-2"
              required
            />
          </Tooltip>
        </div>
        <div className="flex justify-end">
          <Button
            type="submit"
            color="secondary"
            variant="contained"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Senden...' : 'Senden'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AccidentInsuranceContactForm;
