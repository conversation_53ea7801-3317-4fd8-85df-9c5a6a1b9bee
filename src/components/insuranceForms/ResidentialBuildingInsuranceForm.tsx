// src/components/insuranceForms/ResidentialBuildingInsuranceForm.tsx
// Wohngebäude

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface ResidentialBuildingInsuranceFormProps {
  formData: Contract;
  setFormData: React.Dispatch<React.SetStateAction<Contract>>;
  handleChange: (event: any, child?: React.ReactNode) => void;
  errors: { [key: string]: string };
}

const ResidentialBuildingInsuranceForm: React.FC<
  ResidentialBuildingInsuranceFormProps
> = ({ formData, setFormData, handleChange, errors }) => {
  const [calculationParametersData, setCalculationParametersData] =
    useState<CalculationParameter>();
  const [lastEdited, setLastEdited] = useState<
    'insuranceSum' | 'insuranceSum1914' | null
  >(null);
  const isAdmin = useIsAdmin();

  useEffect(() => {
    if (formData.insuranceStartDate !== '') {
      apiFetch(
        `/api/calculation/get?valid_from=${formData.insuranceStartDate}`,
        {
          method: 'GET',
          raw: true,
        }
      )
        .then((res) => res.json())
        .then((data) => setCalculationParametersData(data));
    }
  }, [formData.insuranceStartDate]);

  // if 1914 value changed
  useEffect(() => {
    if (
      lastEdited === 'insuranceSum1914' &&
      formData.insuranceSum1914 &&
      calculationParametersData?.wohngebaudeSummenfaktor
    ) {
      const summe =
        Math.round(
          formData.insuranceSum1914 *
            calculationParametersData.wohngebaudeSummenfaktor *
            100
        ) / 100;
      if (formData.insuranceSum !== summe) {
        setFormData((prev) => ({ ...prev, insuranceSum: summe }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.insuranceSum1914, calculationParametersData, lastEdited]);

  // if Neuwert value changed
  useEffect(() => {
    if (
      lastEdited === 'insuranceSum' &&
      formData.insuranceSum &&
      calculationParametersData?.wohngebaudeSummenfaktor
    ) {
      const mark1914 =
        Math.round(
          (formData.insuranceSum /
            calculationParametersData.wohngebaudeSummenfaktor) *
            100
        ) / 100;
      if (formData.insuranceSum1914 !== mark1914) {
        setFormData((prev) => ({ ...prev, insuranceSum1914: mark1914 }));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.insuranceSum, calculationParametersData, lastEdited]);

  useEffect(() => {
    setFormData({
      ...formData,
      objectType: formData.objectType || 'Einfamilienhaus',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2 mt-8">
        {isAdmin && (
          <Tooltip title="Geben Sie die Objektart an.">
            <TextField
              type="text"
              label="Objektart"
              name="objectType"
              value={formData.objectType}
              onChange={handleChange}
              className="w-full p-2"
              required
              InputLabelProps={{ shrink: true }}
            />
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-1 gap-2 mt-8">
        {!isAdmin && (
          <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude zu 100 % für Wohnzwecke genutzt wird. Dies kann die Versicherungskosten beeinflussen.">
            <FormControlLabel
              control={
                <Checkbox name="coverageUsage" onChange={handleChange} />
              }
              label="100% Wohnzwecke?"
            />
          </Tooltip>
        )}
        <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude nach Bauartklasse 1 (BAK1) gebaut wurde.">
          <FormControlLabel
            control={
              <Checkbox
                name="isBak1"
                checked={formData.buildingType === 'BAK1'}
                onChange={handleChange}
              />
            }
            required
            label="Bauart BAK1?"
          />
        </Tooltip>
        <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude ganzjährig bewohnt ist.">
          <FormControlLabel
            control={
              <Checkbox
                name="isPermanentlyOccupied"
                checked={formData.isPermanentlyOccupied || false}
                onChange={handleChange}
              />
            }
            required
            label="ständig bewohnt?"
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        {isAdmin && (
          <Tooltip title="Aktivieren Sie diese Option, wenn das Baujahr nicht bekannt ist.">
            <FormControlLabel
              control={
                <Checkbox
                  name="isConstructionYearUnknown"
                  checked={formData.isConstructionYearUnknown || false}
                  onChange={handleChange}
                  className="p-2"
                />
              }
              label="Baujahr unbekannt"
            />
          </Tooltip>
        )}
        {(isAdmin ? !formData.isConstructionYearUnknown : true) && (
          <Tooltip title="Geben Sie das Baujahr der Immobilie an.">
            <TextField
              type="number"
              label="Baujahr"
              name="constructionYear"
              value={formData.constructionYear || ''}
              onChange={handleChange}
              className="w-full p-2"
              required
            />
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Versicherungssumme 1914">
          <TextField
            type="number"
            label="Versicherungssumme 1914"
            name="insuranceSum1914"
            value={formData.insuranceSum1914 || ''}
            onChange={(e) => {
              setLastEdited('insuranceSum1914');
              handleChange(e);
            }}
            placeholder="Versicherungssumme 1914"
            className="w-full p-2"
            error={!!errors.insuranceSum1914}
            helperText={errors.insuranceSum1914}
            required={!isAdmin}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">Mark</InputAdornment>
                ),
              },
            }}
          />
        </Tooltip>
        <Tooltip title="Neuwert zum Versicherungsbeginn in €">
          <TextField
            type="number"
            label="Neuwert"
            name="insuranceSum"
            value={formData.insuranceSum || ''}
            onChange={(e) => {
              setLastEdited('insuranceSum');
              handleChange(e);
            }}
            placeholder="Versicherungssumme aktuell"
            className="w-full p-2"
            required
            error={!!errors.insuranceSum}
            helperText={errors.insuranceSum}
            slotProps={{
              input: {
                endAdornment: <InputAdornment position="end">€</InputAdornment>,
              },
            }}
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-1 gap-2 mt-8">
        <Tooltip title="Versicherte Gefahren gemäß Alpha Expert">
          <FormControlLabel
            control={
              <Checkbox
                name="coveredRisks"
                checked={formData.coveredRisks}
                onChange={handleChange}
                className="p-2"
              />
            }
            label="Abdeckung gemäß Alpha Expert"
          />
        </Tooltip>
        <Tooltip title="Elementarversicherung">
          <FormControlLabel
            control={
              <Checkbox
                name="isElementar"
                checked={formData.isElementar || false}
                onChange={handleChange}
                className="p-2"
              />
            }
            label="Elementar?"
          />
        </Tooltip>

        {formData.isElementar && (
          <Tooltip title="Wählen Sie die Zürs Zone aus.">
            <FormControl required variant="outlined" className="p-2">
              <InputLabel hidden={!formData.isElementar}>Zürs Zone</InputLabel>
              <Select
                name="zuersZone"
                value={formData.zuersZone}
                label="Zürs Zone"
                onChange={handleChange}
                hidden={!formData.isElementar}
                required={formData.isElementar}
              >
                <MenuItem value="1">1</MenuItem>
                <MenuItem value="2">2</MenuItem>
              </Select>
            </FormControl>
          </Tooltip>
        )}

        <FormControlLabel
          control={
            <Checkbox
              name="householdTech"
              checked={formData.householdTech || false}
              onChange={handleChange}
              className="p-2"
            />
          }
          label="Haustechnik Expert"
        />
        <FormControlLabel
          control={
            <Checkbox
              name="pvSystem"
              checked={formData.pvSystem || false}
              onChange={handleChange}
              className="p-2"
            />
          }
          label="Photovoltaik Expert"
        />
        <FormControlLabel
          control={
            <Checkbox
              name="glassInsurance"
              checked={formData.glassInsurance || false}
              onChange={handleChange}
              className="p-2"
            />
          }
          label="Glasversicherung Expert"
        />
      </div>
      {isAdmin && (
        <>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-red-600">
              Dieses Feld überschreibt die Haustechnik Netto Prämie!
            </div>
            <div />
            <Tooltip title="Hier können Sie die Prämie überschreiben.">
              <TextField
                name="premieHouseholdTech"
                type="number"
                label="Haustechnik Prämie"
                value={formData.premieHouseholdTech}
                onChange={handleChange}
                className="w-full p-2"
                variant="outlined"
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  },
                }}
              />
            </Tooltip>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-red-600">
              Dieses Feld überschreibt die Photovoltaik Netto Prämie!
            </div>
            <div />
            <Tooltip title="Hier können Sie die Prämie überschreiben.">
              <TextField
                name="premiePvSystem"
                type="number"
                label="Photovoltaik Prämie"
                value={formData.premiePvSystem}
                onChange={handleChange}
                className="w-full p-2"
                variant="outlined"
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  },
                }}
              />
            </Tooltip>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-red-600">
              Dieses Feld überschreibt die Glasversicherung Netto Prämie!
            </div>
            <div />
            <Tooltip title="Hier können Sie die Prämie überschreiben.">
              <TextField
                name="premieGlassInsurance"
                type="number"
                label="Glasversicherung Netto Prämie"
                value={formData.premieGlassInsurance}
                onChange={handleChange}
                className="w-full p-2"
                variant="outlined"
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">€</InputAdornment>
                    ),
                  },
                }}
              />
            </Tooltip>
          </div>
        </>
      )}
    </div>
  );
};

export default ResidentialBuildingInsuranceForm;
