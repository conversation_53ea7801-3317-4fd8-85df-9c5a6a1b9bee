// src/components/insuranceForms/ResidentialBuildingInsuranceForm.tsx
// Wohngebäude

import React, { useEffect, useState } from 'react';
import { CalculationParametersData, ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { Checkbox, FormControlLabel, InputAdornment, TextField, FormControl, InputLabel, Select, MenuItem, Tooltip } from '@mui/material';


interface ResidentialBuildingInsuranceFormProps {
    formData: ContractData;
    setFormData: React.Dispatch<React.SetStateAction<ContractData>>;
    handleChange: (event: any, child?: React.ReactNode) => void;
    errors: { [key: string]: string };
}

const ResidentialBuildingInsuranceForm: React.FC<ResidentialBuildingInsuranceFormProps> = ({ formData, setFormData, handleChange, errors }) => {

    const [calculationParametersData, setCalculationParametersData] = useState<CalculationParametersData>()
    const [lastEdited, setLastEdited] = useState<'insurance_sum' | 'insurance_sum_1914' | null>(null);


    useEffect(() => {
        if (formData.insurance_start_date != "")
            fetch(`/api/calculation/get?valid_from=${formData.insurance_start_date}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
            })
                .then(res => res.json())
                .then(data => setCalculationParametersData(data))
    }, [formData.insurance_start_date])

    // if 1914 value changed
    useEffect(() => {
        if (lastEdited === 'insurance_sum_1914' && formData.insurance_sum_1914 && calculationParametersData?.wohngebaude_summenfaktor) {
            const summe = Math.round(formData.insurance_sum_1914 * calculationParametersData.wohngebaude_summenfaktor * 100) / 100;
            if (formData.insurance_sum !== summe) {
                setFormData(prev => ({ ...prev, insurance_sum: summe }));
            }
        }
    }, [formData.insurance_sum_1914, calculationParametersData, lastEdited]);

    // if Neuwert value changed
    useEffect(() => {
        if (lastEdited === 'insurance_sum' && formData.insurance_sum && calculationParametersData?.wohngebaude_summenfaktor) {
            const mark1914 = Math.round((formData.insurance_sum / calculationParametersData.wohngebaude_summenfaktor) * 100) / 100;
            if (formData.insurance_sum_1914 !== mark1914) {
                setFormData(prev => ({ ...prev, insurance_sum_1914: mark1914 }));
            }
        }
    }, [formData.insurance_sum, calculationParametersData, lastEdited]);

    useEffect(() => {
        setFormData({
            ...formData,
            "object_type": formData.object_type || "Einfamilienhaus"
        })
    }, [])

    return (
        <div>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Geben Sie die Objektart an.">
                        <TextField
                            type="text"
                            label="Objektart"
                            name="object_type"
                            value={formData.object_type}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                            InputLabelProps={{
                                shrink: true,
                            }} />
                    </Tooltip>
                }
            </div>
            <div className='grid grid-cols-1 gap-2 mt-8'>
                {localStorage.getItem("is_admin") === "false" && (
                    <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude zu 100 % für Wohnzwecke genutzt wird. Dies kann die Versicherungskosten beeinflussen.">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="coverage_usage"
                                    onChange={handleChange}
                                />
                            }
                            label={"100% Wohnzwecke?"}
                        />
                    </Tooltip>
                )}
                <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude nach Bauartklasse 1 (BAK1) gebaut wurde. Diese Klasse umfasst massiv gebaute Gebäude, die als besonders robust gelten.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_bak1"
                            checked={formData.building_type === "BAK1"}
                            onChange={handleChange} />}
                        required
                        label="Bauart BAK1?" />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, wenn das Gebäude ganzjährig bewohnt ist, um das Risiko für bestimmte Schadensarten besser einzuschätzen.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_permanently_occupied"
                            checked={formData.is_permanently_occupied || false}
                            onChange={handleChange} />}
                        required
                        label="ständig bewohnt?" />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                {localStorage.getItem("is_admin") == "true" &&
                    <Tooltip title="Aktivieren Sie diese Option, wenn das Baujahr nicht bekannt ist.">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="is_construction_year_unknown"
                                    checked={formData.is_construction_year_unknown || false}
                                    onChange={handleChange}
                                    className="p-2"
                                />
                            }
                            label="Baujahr unbekannt"
                        />
                    </Tooltip>}
                {(localStorage.getItem("is_admin") == "true" ? !formData.is_construction_year_unknown : true) &&
                    <Tooltip title="Geben Sie das Baujahr der Immobilie an.">
                        <TextField
                            type="number"
                            label="Baujahr"
                            name="construction_year"
                            value={formData.construction_year || ""}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                        />
                    </Tooltip>
                }
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Versicherungssumme in Mark aus dem Jahr 1914.  Bei Angabe der Versicherungssumme in Mark wird der Neuwert zum Versicherungsbeginn in € berechnet.">
                    <TextField
                        type="number"
                        label="Versicherungssumme 1914"
                        name="insurance_sum_1914"
                        value={formData.insurance_sum_1914 || ""}
                        onChange={(e) => {
                            setLastEdited('insurance_sum_1914');
                            handleChange(e);
                        }}
                        placeholder="Versicherungssumme 1914"
                        className="w-full p-2"
                        error={!!errors.insurance_sum_1914}
                        helperText={errors.insurance_sum_1914}
                        required={!(localStorage.getItem("is_admin") == "true")}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">Mark</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
                <Tooltip title="Neuwert zum Versicherungsbeginn in €. Bei Angabe des Neuwerts wird die Versicherungssumme in Mark aus dem Jahr 1914 berechnet.">
                    <TextField
                        type="number"
                        label="Neuwert"
                        name="insurance_sum"
                        value={formData.insurance_sum || ""}
                        onChange={(e) => {
                            setLastEdited('insurance_sum');
                            handleChange(e);
                        }}
                        placeholder="Versicherungssumme aktuell"
                        className="w-full p-2"
                        required
                        disabled={false}
                        error={!!errors.insurance_sum}
                        helperText={errors.insurance_sum}
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">€</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-1 gap-2 mt-8'>
                <Tooltip title="Versicherten Gefahren gemäß Alpha Expert:
                - Feuer: Schutz gegen Schäden durch Feuer und Brand.
                - LW: Absicherung gegen Schäden durch austretendes Leitungswasser.
                - ST/H: Schutz vor Schäden durch Sturm und Hagel.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="covered_risks"
                                checked={formData.covered_risks}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Abdeckung gemäß Alpha Expert"
                    />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="is_elementar"
                                checked={formData.is_elementar || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Elementar?"
                    />
                </Tooltip>

                {formData.is_elementar &&
                    <Tooltip title="Wählen Sie die Zürs Zone aus.">
                    <FormControl
                        required variant="outlined"
                        className="p-2"
                    >
                        <InputLabel hidden={!formData.is_elementar}>Zürs Zone</InputLabel>
                        <Select name="zuers_zone"
                            value={formData.zuers_zone}
                            label="Zürs Zone"
                            onChange={handleChange}
                            hidden={!formData.is_elementar}
                            required={formData.is_elementar}
                        >
                            <MenuItem value="1">1</MenuItem>
                            <MenuItem value="2">2</MenuItem>
                        </Select>
                    </FormControl>
                    </Tooltip>}

                <Tooltip title="Aktivieren Sie diese Option, wenn Sie zusätzlichen Schutz für technische Anlagen im Gebäude wünschen, wie z. B. Heizungs- oder Belüftungssysteme.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="household_tech"
                                checked={formData.household_tech || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Haustechnik Expert"
                    />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, wenn Sie Ihre Photovoltaikanlage umfassend gegen Schäden versichern möchten.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="pv_system"
                                checked={formData.pv_system || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Photovoltaik Expert"
                    />
                </Tooltip>
                <Tooltip title="Aktivieren Sie diese Option, um Glasflächen wie Fenster, Türen oder Glaselemente im Gebäude gegen Bruch zu versichern.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="glass_insurance"
                                checked={formData.glass_insurance || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Glasversicherung Expert"
                    />
                </Tooltip>
            </div>
            {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-2'>
                <div className='text-red-600'>Dieses Feld überschreibt die Haustechnik Netto Prämie!</div>
                <div />
                <Tooltip title="Hier können Sie die Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                    <TextField
                        name="premie_household_tech"
                        type='number'
                        label="Haustechnik Prämie"
                        value={formData.premie_household_tech}
                        onChange={handleChange}
                        className="w-full p-2"
                        variant="outlined"
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">€</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
            </div>}
            {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-2'>
                <div className='text-red-600'>Dieses Feld überschreibt die Photovoltaik Netto Prämie!</div>
                <div />
                <Tooltip title="Hier können Sie die Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                    <TextField
                        name="premie_pv_system"
                        type='number'
                        label="Photovoltaik Prämie"
                        value={formData.premie_pv_system}
                        onChange={handleChange}
                        className="w-full p-2"
                        variant="outlined"
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">€</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
            </div>}
            {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-2'>
                <div className='text-red-600'>Dieses Feld überschreibt die Glasversicherung Netto Prämie!</div>
                <div />
                <Tooltip title="Hier können Sie die Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                    <TextField
                        name="premie_glass_insurance"
                        type='number'
                        label="Glasversicherung Netto Prämie"
                        value={formData.premie_glass_insurance}
                        onChange={handleChange}
                        className="w-full p-2"
                        variant="outlined"
                        slotProps={{
                            input: {
                                endAdornment: (
                                    <InputAdornment position="end">€</InputAdornment>
                                )
                            }
                        }}
                    />
                </Tooltip>
            </div>}
        </div>
    );
};

export default ResidentialBuildingInsuranceForm;