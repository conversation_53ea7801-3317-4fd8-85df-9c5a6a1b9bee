// src/components/insuranceForms/AnimalLiabilityInsuranceForm.tsx
// Tierhalterhaftpflicht

import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { AnimalData, ContractData } from '../../types';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { Button, IconButton, TextField, FormControl, InputLabel, Select, MenuItem, Tooltip } from '@mui/material';

interface AnimalLiabilityInsuranceFormProps {
    formData: ContractData;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const AnimalLiabilityInsuranceForm: React.FC<AnimalLiabilityInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {
    const [countAnimals, setCountPets] = useState<number>(formData.animal_data.length || 1);

    const handleAnimalChange = (index: number, field: keyof AnimalData, value: string) => {
        const updatedAnimalData = [...formData.animal_data];
        updatedAnimalData[index] = {
            ...updatedAnimalData[index],
            [field]: value,
        };
        setFormData({
            ...formData,
            animal_data: updatedAnimalData
        });
    };

    const addPet = () => {
        setCountPets((prevCount) => prevCount + 1);
        setFormData({
            ...formData,
            animal_data: [
                ...formData.animal_data,
                { animal_type: '', race: '', animal_name: '' },
            ],
        });
    };

    const removePet = (index: number) => {
        if (countAnimals > 1) {
            const updatedAnimalData = formData.animal_data.filter((_, i) => i !== index);
            setCountPets((prevCount) => prevCount - 1);
            setFormData({
                ...formData,
                animal_data: updatedAnimalData
            });
        }
    };

    useEffect(() => {
        if (formData.animal_data.length === 0) {
            setFormData({
                ...formData,
                animal_data: [{ animal_type: '', race: '', animal_name: '' }]
            });
        }
        setFormData({
            ...formData,
            "coverage_amount": "60000000"
        });
    }, []);

    return (
        <div className='pb-5'>
            <div className="flex flex-col gap-2" >
                {Array.from({ length: countAnimals }, (_, index) => (
                    <div className='flex flex-row gap-2 items-center' key={index}>
                        <Tooltip title="Bitte wählen Sie die Tierart, für die die Versicherung gelten soll.">
                            <FormControl
                                required variant="outlined"
                                className="w-full"
                            >
                                <InputLabel>Tierart</InputLabel>
                                <Select
                                    name="animal_type"
                                    label="Tierart"
                                    value={formData.animal_data[index]?.animal_type || ''}
                                    onChange={(e) => handleAnimalChange(index, 'animal_type', e.target.value)}
                                    className="w-full"
                                    required
                                >
                                    <MenuItem value="Hund">Hund</MenuItem>
                                    <MenuItem value="Pferd">Pferd</MenuItem>
                                </Select>
                            </FormControl>
                        </Tooltip>
                        {formData.animal_data[index]?.animal_type === "Hund" &&
                            <Tooltip title="Falls das Tier ein Hund ist, geben Sie hier die Rasse an.">
                                <TextField
                                    type="text"
                                    label="Rasse"
                                    name="race"
                                    value={formData.animal_data[index]?.race || ''}
                                    onChange={(e) => handleAnimalChange(index, 'race', e.target.value)}
                                    placeholder="Rasse"
                                    className="w-full p-2"
                                    required={formData.animal_data[index]?.animal_type === "Hund"}
                                />
                            </Tooltip>
                        }
                        <Tooltip title="Geben Sie den Namen Ihres Tieres an.">
                            <TextField
                                type="text"
                                label="Tiername"
                                name="animal_name"
                                value={formData.animal_data[index]?.animal_name || ''}
                                onChange={(e) => handleAnimalChange(index, 'animal_name', e.target.value)}
                                placeholder="Tiername"
                                className="w-full p-2"
                                required
                            />
                        </Tooltip>
                        <Tooltip title="Tier entfernen.">
                            <IconButton
                                type="button"
                                onClick={() => removePet(index)}
                                //className={`text-alpha_blue p-2 hover:text-white hover:bg-alpha_blue duration-300 flex items-center ${countAnimals === 1 ? 'opacity-30 cursor-not-allowed' : ''}`}
                                disabled={countAnimals === 1}
                            >
                                <DeleteIcon className="w-6" />
                            </IconButton>
                        </Tooltip>
                    </div>
                ))}
            </div>
            <div className='mt-4'>
                <Tooltip title="Hier können witere Tiere hinzugefügt werden.">
                    <Button
                        type="button"
                        variant='contained'
                        color='secondary'
                        onClick={addPet}
                    >
                        <AddIcon className='pr-2' />
                        <p>Tier hinzufügen</p>
                    </Button>
                </Tooltip>
            </div>
        </div>
    );
};

export default AnimalLiabilityInsuranceForm;
