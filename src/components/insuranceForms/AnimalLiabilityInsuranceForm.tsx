// src/components/insuranceForms/AnimalLiabilityInsuranceForm.tsx
// Tierhalterhaftpflicht

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Button,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useState,
} from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { type JsonArray } from '@/generated/prisma-postgres/runtime/library';
import { type AnimalData } from '@/types';

interface AnimalLiabilityInsuranceFormProps {
  formData: Contract;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const AnimalLiabilityInsuranceForm: React.FC<
  AnimalLiabilityInsuranceFormProps
> = ({ formData, setFormData }) => {
  const [countAnimals, setCountPets] = useState<number>(
    (formData.animalData as JsonArray).length || 1
  );

  const handleAnimalChange = (
    index: number,
    field: keyof AnimalData,
    value: string
  ) => {
    const updatedAnimalData = [...(formData.animalData as any)];
    updatedAnimalData[index] = {
      ...updatedAnimalData[index],
      [field]: value,
    };
    setFormData({
      ...formData,
      animalData: updatedAnimalData,
    });
  };

  const addPet = () => {
    setCountPets((prevCount) => prevCount + 1);
    setFormData({
      ...formData,
      animalData: [
        ...(formData.animalData as JsonArray),
        { animalType: '', race: '', animalName: '' },
      ],
    });
  };

  const removePet = (index: number) => {
    if (countAnimals > 1) {
      const updatedAnimalData = (formData.animalData as JsonArray).filter(
        (_, i) => i !== index
      );
      setCountPets((prevCount) => prevCount - 1);
      setFormData({
        ...formData,
        animalData: updatedAnimalData,
      });
    }
  };

  useEffect(() => {
    if ((formData.animalData as JsonArray).length === 0) {
      setFormData({
        ...formData,
        animalData: [{ animal_type: '', race: '', animal_name: '' }],
      });
    }
    setFormData({
      ...formData,
      coverageAmount: '60000000',
    });
  }, [formData, setFormData]);

  return (
    <div className="pb-5">
      <div className="flex flex-col gap-2">
        {Array.from({ length: countAnimals }, (_, index) => (
          <div className="flex flex-row gap-2 items-center" key={index}>
            <Tooltip title="Bitte wählen Sie die Tierart, für die die Versicherung gelten soll.">
              <FormControl required variant="outlined" className="w-full">
                <InputLabel>Tierart</InputLabel>
                <Select
                  name="animal_type"
                  label="Tierart"
                  value={(formData.animalData as any)[index]?.animal_type || ''}
                  onChange={(e) =>
                    handleAnimalChange(index, 'animal_type', e.target.value)
                  }
                  className="w-full"
                  required
                >
                  <MenuItem value="Hund">Hund</MenuItem>
                  <MenuItem value="Pferd">Pferd</MenuItem>
                </Select>
              </FormControl>
            </Tooltip>
            {(formData.animalData as any)[index]?.animal_type === 'Hund' && (
              <Tooltip title="Falls das Tier ein Hund ist, geben Sie hier die Rasse an.">
                <TextField
                  type="text"
                  label="Rasse"
                  name="race"
                  value={(formData.animalData as any)[index]?.race || ''}
                  onChange={(e) =>
                    handleAnimalChange(index, 'race', e.target.value)
                  }
                  placeholder="Rasse"
                  className="w-full p-2"
                  required={
                    (formData.animalData as any)[index]?.animal_type === 'Hund'
                  }
                />
              </Tooltip>
            )}
            <Tooltip title="Geben Sie den Namen Ihres Tieres an.">
              <TextField
                type="text"
                label="Tiername"
                name="animal_name"
                value={(formData.animalData as any)[index]?.animal_name || ''}
                onChange={(e) =>
                  handleAnimalChange(index, 'animal_name', e.target.value)
                }
                placeholder="Tiername"
                className="w-full p-2"
                required
              />
            </Tooltip>
            <Tooltip title="Tier entfernen.">
              <IconButton
                type="button"
                onClick={() => removePet(index)}
                //className={`text-alpha_blue p-2 hover:text-white hover:bg-alpha_blue duration-300 flex items-center ${countAnimals === 1 ? 'opacity-30 cursor-not-allowed' : ''}`}
                disabled={countAnimals === 1}
              >
                <DeleteIcon className="w-6" />
              </IconButton>
            </Tooltip>
          </div>
        ))}
      </div>
      <div className="mt-4">
        <Tooltip title="Hier können witere Tiere hinzugefügt werden.">
          <Button
            type="button"
            variant="contained"
            color="secondary"
            onClick={addPet}
          >
            <AddIcon className="pr-2" />
            <p>Tier hinzufügen</p>
          </Button>
        </Tooltip>
      </div>
    </div>
  );
};

export default AnimalLiabilityInsuranceForm;
