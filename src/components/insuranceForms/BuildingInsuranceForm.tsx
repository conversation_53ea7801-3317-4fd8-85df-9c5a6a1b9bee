// src/components/insuranceForms/BuildingInsuranceForm.tsx
// Gewerbe – Gebäude-Versicherung

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface BuildingInsuranceFormProps {
  formData: Contract;
  setFormData: React.Dispatch<React.SetStateAction<Contract>>;
  handleChange: (event: any, child?: React.ReactNode) => void;
}

const BuildingInsuranceForm: React.FC<BuildingInsuranceFormProps> = ({
  formData,
  setFormData,
  handleChange,
}) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    if (formData.insuranceSum !== undefined && !isAdmin) {
      const maxInsuranceSum = 7500000;
      if ((formData.insuranceSum || 0) > maxInsuranceSum) {
        setFormData({
          ...formData,
          insuranceSum: maxInsuranceSum,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData, formData.insuranceSum, setFormData]);

  return (
    <div className="flex flex-col gap-4">
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2">
        <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
          <TextField
            type="number"
            label="Versicherungssumme in €"
            name="insuranceSum"
            value={formData.insuranceSum || ''}
            onChange={handleChange}
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
          <FormControl required variant="outlined" className="w-full p-2">
            <InputLabel>Tarifgruppe</InputLabel>
            <Select
              name="tariffGroup"
              label="Tarifgruppe"
              value={formData.tariffGroup}
              onChange={handleChange}
              required
            >
              <MenuItem value="Handel">Handel</MenuItem>
              <MenuItem value="Handwerk">Handwerk</MenuItem>
              <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
              <MenuItem value="Gastronomie">Gastronomie</MenuItem>
            </Select>
          </FormControl>
        </Tooltip>
        <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
          <TextField
            type="text"
            label="Betriebsart"
            name="businessType"
            value={formData.businessType}
            onChange={handleChange}
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-4">
        <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
          <FormControlLabel
            control={
              <Checkbox
                name="isElementar"
                checked={formData.isElementar || false}
                onChange={handleChange}
                className="p-2"
              />
            }
            label="Elementar"
          />
        </Tooltip>
        {formData.isElementar && (
          <Tooltip title="Wählen Sie die Zürs Zone aus.">
            <FormControl required variant="outlined" className="w-full p-2">
              <InputLabel hidden={!formData.isElementar}>Zürs Zone</InputLabel>
              <Select
                name="zuersZone"
                value={formData.zuersZone}
                label="Zürs Zone"
                onChange={handleChange}
                required={formData.isElementar}
              >
                <MenuItem value="1">1</MenuItem>
                <MenuItem value="2">2</MenuItem>
              </Select>
            </FormControl>
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-2 gap-2">
        <Tooltip title="Aktivieren Sie diese Option, wenn das Baujahr nicht bekannt ist.">
          <FormControlLabel
            control={
              <Checkbox
                name="isConstructionYearUnknown"
                checked={formData.isConstructionYearUnknown || false}
                onChange={handleChange}
                className="p-2"
              />
            }
            label="Baujahr unbekannt"
          />
        </Tooltip>
        {!formData.isConstructionYearUnknown && (
          <Tooltip title="Geben Sie das Baujahr der Immobilie an.">
            <TextField
              type="number"
              label="Baujahr"
              name="constructionYear"
              value={formData.constructionYear || ''}
              onChange={handleChange}
              className="w-full p-2"
              required
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default BuildingInsuranceForm;
