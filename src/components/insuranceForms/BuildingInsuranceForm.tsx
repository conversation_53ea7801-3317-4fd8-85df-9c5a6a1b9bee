// src/components/insuranceForms/BuildingInsuranceForm.tsx
// Gewerbe – Gebäude-Versicherung

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { Checkbox, FormControl, FormControlLabel, InputLabel, MenuItem, Select, SelectChangeEvent, TextField, Tooltip } from '@mui/material';

interface BuildingInsuranceFormProps {
    formData: ContractData;
    setFormData: React.Dispatch<React.SetStateAction<ContractData>>;
    handleChange: (event: any, child?: React.ReactNode) => void;
}

const BuildingInsuranceForm: React.FC<BuildingInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        if (formData.insurance_sum !== undefined && localStorage.getItem("is_admin") == "false") {
            const maxInsureanceSum = 7500000
            if ((formData.insurance_sum) > maxInsureanceSum) {
                setFormData({
                    ...formData,
                    insurance_sum: maxInsureanceSum,
                });
            }
        }
    }, [formData.insurance_sum, setFormData]);

    return (
        <div className='flex flex-col gap-4'>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2'>
                <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
                    <TextField
                        type="number"
                        label="Versicherungssumme in €"
                        name="insurance_sum"
                        value={formData.insurance_sum || ""}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
                    <FormControl required variant="outlined" className="w-full p-2">
                        <InputLabel>Tarifgruppe</InputLabel>
                        <Select
                            name="tariff_group"
                            label="Tarifgruppe"
                            value={formData.tariff_group}
                            onChange={handleChange}
                            required
                        >
                            <MenuItem value="Handel">Handel</MenuItem>
                            <MenuItem value="Handwerk">Handwerk</MenuItem>
                            <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
                            <MenuItem value="Gastronomie">Gastronomie</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>
                <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
                    <TextField
                        type="text"
                        label="Betriebsart"
                        name="business_type"
                        value={formData.business_type}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>
            <div className='grid grid-cols-2 gap-2 mt-4'>
                <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="is_elementar"
                                checked={formData.is_elementar || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Elementar"
                    />
                </Tooltip>
                {formData.is_elementar &&
                    <Tooltip title="Wählen Sie die Zürs Zone aus.">
                        <FormControl
                            required variant="outlined"
                            className="w-full p-2"
                        >
                            <InputLabel hidden={!formData.is_elementar}>Zürs Zone</InputLabel>
                            <Select name="zuers_zone"
                                value={formData.zuers_zone}
                                label="Zürs Zone"
                                onChange={handleChange}
                                required={formData.is_elementar}
                            >
                                <MenuItem value="1">1</MenuItem>
                                <MenuItem value="2">2</MenuItem>
                            </Select>
                        </FormControl>
                    </Tooltip>}
            </div>
            <div className='grid grid-cols-2 gap-2'>
                <Tooltip title="Aktivieren Sie diese Option, wenn das Baujahr nicht bekannt ist.">
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="is_construction_year_unknown"
                                checked={formData.is_construction_year_unknown || false}
                                onChange={handleChange}
                                className="p-2"
                            />
                        }
                        label="Baujahr unbekannt"
                    />
                </Tooltip>
                {!formData.is_construction_year_unknown &&
                    <Tooltip title="Geben Sie das Baujahr der Immobilie an.">
                        <TextField
                            type="number"
                            label="Baujahr"
                            name="construction_year"
                            value={formData.construction_year || ""}
                            onChange={handleChange}
                            className="w-full p-2"
                            required
                        />
                    </Tooltip>
                }
            </div>
        </div>
    );
};

export default BuildingInsuranceForm;