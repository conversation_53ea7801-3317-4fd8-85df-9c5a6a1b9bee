// src/components/insuranceForms/RiskAddressForm.tsx
// Risikoaddresse

import React, { useState } from 'react';
import { ContractData, RiskAddressData } from '../../types';
import { Button, IconButton, TextField, Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

interface RiskAddressFormProps {
    formData: ContractData;
    handleChange: (event: any, child?: React.ReactNode) => void;
    setFormData: React.Dispatch<React.SetStateAction<ContractData>>;
}

const RiskAddressForm: React.FC<RiskAddressFormProps> = ({ formData, handleChange, setFormData }) => {

    const [countRiskAddresses, setCountRiskAddresses] = useState<number>(formData.risk_addresses!.length || 1);

    const handleRiskAddressChange = (index: number, field: keyof RiskAddressData, value: string) => {
        const addressesData = [...formData.risk_addresses!];
        addressesData[index] = {
            ...addressesData[index],
            [field]: value,
        };
        setFormData({
            ...formData,
            risk_addresses: addressesData
        });
    };

    const addAddress = () => {
        setCountRiskAddresses((prevCount) => prevCount + 1);
        const riskAddress: RiskAddressData = {
            street: '',
            house_number: '',
            postal_code: '',
            city: '',
            unit: '',
        }
        setFormData({
            ...formData,
            risk_addresses: [
                ...formData.risk_addresses!,
                riskAddress,
            ],
        });
    };

    const removeAddress = (index: number) => {
        if (countRiskAddresses > 1) {
            const addressesData = formData.risk_addresses!.filter((_, i) => i !== index);
            setCountRiskAddresses((prevCount) => prevCount - 1);
            setFormData({
                ...formData,
                risk_addresses: addressesData
            });
        }
    };


    return (
        <div >
            <p className='pb-2'>Risikoadressen:</p>
            <div className='flex flex-col gap-4 pb-5'>
                {Array.from({ length: countRiskAddresses }, (_, index) => (
                    <div className='flex flex-row gap-4 items-center' key={index}>
                        <div className='flex flex-col gap-2'>
                            <div className='grid grid-cols-2 gap-2'>
                                <Tooltip title="Geben Sie die Straße der Immobilie oder des Standorts an, die/der versichert werden soll.">
                                    <TextField
                                        type="text"
                                        label="Straße"
                                        name="street"
                                        value={formData.risk_addresses![index]?.street}
                                        onChange={(e) => handleRiskAddressChange(index, 'street', e.target.value)}
                                        className="w-full p-2"
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Geben Sie die Hausnummer der zu versichernden Immobilie oder des Standorts an.">
                                    <TextField
                                        type="text"
                                        label="Hausnummer"
                                        name="house_number"
                                        value={formData.risk_addresses![index]?.house_number}
                                        onChange={(e) => handleRiskAddressChange(index, 'house_number', e.target.value)}
                                        className="w-full p-2"
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Geben Sie die Postleitzahl der zu versichernden Immobilie oder des Standorts an.">
                                    <TextField
                                        type="text"
                                        label="Postleitzahl"
                                        name="postal_code"
                                        value={formData.risk_addresses![index]?.postal_code}
                                        onChange={(e) => handleRiskAddressChange(index, 'postal_code', e.target.value)}
                                        className="w-full p-2"
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Geben Sie die Stadt oder den Ort der zu versichernden Immobilie oder des Standorts an.">
                                    <TextField
                                        type="text"
                                        label="Ort"
                                        name="city"
                                        value={formData.risk_addresses![index]?.city}
                                        onChange={(e) => handleRiskAddressChange(index, 'city', e.target.value)}
                                        className="w-full p-2"
                                        required
                                    />
                                </Tooltip>
                            </div>
                            <div className='grid grid-cols-1 gap-2'>
                                <Tooltip title="Geben Sie die genaue Lage der Wohneinheit an (z. B. Wohnung 3 im 2. Obergeschoss).">
                                    <TextField
                                        type="text"
                                        label="Genaue Lage der Wohneinheit"
                                        name="unit"
                                        value={formData.risk_addresses![index]?.unit}
                                        onChange={(e) => handleRiskAddressChange(index, 'unit', e.target.value)}
                                        className="w-full p-2"
                                    />
                                </Tooltip>
                            </div>
                        </div>
                        <Tooltip title="Addresse entfernen.">
                            <IconButton
                                type="button"
                                onClick={() => removeAddress(index)}
                                //className={`text-alpha_blue p-2 hover:text-white hover:bg-alpha_blue duration-300 flex items-center ${countRiskAddresses === 1 ? 'opacity-30 cursor-not-allowed' : ''}`}
                                disabled={countRiskAddresses === 1}
                            >
                                <DeleteIcon />
                            </IconButton>
                        </Tooltip>

                    </div>
                ))}
            </div>
            <Tooltip title="Hier können witere Adressen hinzugefügt werden.">
                <Button
                    type="button"
                    variant='contained'
                    color='secondary'
                    onClick={addAddress}

                >
                    <AddIcon className='pr-2' />
                    <p>Risikoadresse hinzufügen</p>
                </Button>
            </Tooltip>
        </div>
    );
};

export default RiskAddressForm;