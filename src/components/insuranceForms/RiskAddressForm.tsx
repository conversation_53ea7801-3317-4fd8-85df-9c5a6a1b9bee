// src/components/insuranceForms/RiskAddressForm.tsx
// Risikoaddresse

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Button, IconButton, TextField, Tooltip } from '@mui/material';
import React from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { type RiskAddressData } from '@/types';

// Tiny runtime guard to safely narrow unknown JSON to RiskAddressData[]
function isRiskAddressArray(val: unknown): val is RiskAddressData[] {
  return (
    Array.isArray(val) &&
    val.every(
      (item) =>
        item &&
        typeof item === 'object' &&
        'street' in item &&
        'house_number' in item &&
        'postal_code' in item &&
        'city' in item
    )
  );
}

interface RiskAddressFormProps {
  formData: Contract;
  handleChange: (event: any, child?: React.ReactNode) => void;
  setFormData: React.Dispatch<React.SetStateAction<Contract>>;
}

const RiskAddressForm: React.FC<RiskAddressFormProps> = ({
  formData,
  setFormData,
}) => {
  // Safely normalize to an array (never undefined)
  const addresses: RiskAddressData[] = isRiskAddressArray(
    formData.riskAddresses
  )
    ? formData.riskAddresses
    : [];

  const setAddresses = (next: RiskAddressData[]) =>
    setFormData((prev) => ({ ...prev, riskAddresses: next }));

  const handleRiskAddressChange = (
    index: number,
    field: keyof RiskAddressData,
    value: string
  ) => {
    setAddresses(
      addresses.map((addr, i) =>
        i === index ? { ...addr, [field]: value } : addr
      )
    );
  };

  const addAddress = () => {
    const empty: RiskAddressData = {
      street: '',
      house_number: '',
      postal_code: '',
      city: '',
      unit: '',
    };
    setAddresses([...addresses, empty]);
  };

  const removeAddress = (index: number) => {
    if (addresses.length <= 1) return; // keep at least one row
    setAddresses(addresses.filter((_, i) => i !== index));
  };

  return (
    <div>
      <p className="pb-2">Risikoadressen:</p>
      <div className="flex flex-col gap-4 pb-5">
        {addresses.map((addr, index) => (
          <div className="flex flex-row gap-4 items-center" key={index}>
            <div className="flex flex-col gap-2">
              <div className="grid grid-cols-2 gap-2">
                <Tooltip title="Geben Sie die Straße der Immobilie oder des Standorts an, die/der versichert werden soll.">
                  <TextField
                    type="text"
                    label="Straße"
                    name="street"
                    value={addr.street}
                    onChange={(e) =>
                      handleRiskAddressChange(index, 'street', e.target.value)
                    }
                    className="w-full p-2"
                    required
                  />
                </Tooltip>
                <Tooltip title="Geben Sie die Hausnummer der zu versichernden Immobilie oder des Standorts an.">
                  <TextField
                    type="text"
                    label="Hausnummer"
                    name="house_number"
                    value={addr.house_number}
                    onChange={(e) =>
                      handleRiskAddressChange(
                        index,
                        'house_number',
                        e.target.value
                      )
                    }
                    className="w-full p-2"
                    required
                  />
                </Tooltip>
                <Tooltip title="Geben Sie die Postleitzahl der zu versichernden Immobilie oder des Standorts an.">
                  <TextField
                    type="text"
                    label="Postleitzahl"
                    name="postal_code"
                    value={addr.postal_code}
                    onChange={(e) =>
                      handleRiskAddressChange(
                        index,
                        'postal_code',
                        e.target.value
                      )
                    }
                    className="w-full p-2"
                    required
                  />
                </Tooltip>
                <Tooltip title="Geben Sie die Stadt oder den Ort der zu versichernden Immobilie oder des Standorts an.">
                  <TextField
                    type="text"
                    label="Ort"
                    name="city"
                    value={addr.city}
                    onChange={(e) =>
                      handleRiskAddressChange(index, 'city', e.target.value)
                    }
                    className="w-full p-2"
                    required
                  />
                </Tooltip>
              </div>
              <div className="grid grid-cols-1 gap-2">
                <Tooltip title="Geben Sie die genaue Lage der Wohneinheit an (z. B. Wohnung 3 im 2. Obergeschoss).">
                  <TextField
                    type="text"
                    label="Genaue Lage der Wohneinheit"
                    name="unit"
                    value={addr.unit ?? ''}
                    onChange={(e) =>
                      handleRiskAddressChange(index, 'unit', e.target.value)
                    }
                    className="w-full p-2"
                  />
                </Tooltip>
              </div>
            </div>
            <Tooltip title="Adresse entfernen.">
              <span>
                <IconButton
                  type="button"
                  onClick={() => removeAddress(index)}
                  disabled={addresses.length === 1}
                >
                  <DeleteIcon />
                </IconButton>
              </span>
            </Tooltip>
          </div>
        ))}
      </div>

      <Tooltip title="Hier können weitere Adressen hinzugefügt werden.">
        <Button
          type="button"
          variant="contained"
          color="secondary"
          onClick={addAddress}
        >
          <AddIcon className="pr-2" />
          <p>Risikoadresse hinzufügen</p>
        </Button>
      </Tooltip>
    </div>
  );
};

export default RiskAddressForm;
