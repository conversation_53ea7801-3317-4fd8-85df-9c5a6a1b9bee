// src/components/insuranceForms/BusinessInsuranceForm.tsx
// Geschäftsversicherung

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, { type Dispatch, type SetStateAction, useEffect } from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { useIsAdmin } from '@/utils/authUtils';

import RiskAddressForm from './RiskAddressForm';

interface BusinessInsuranceFormProps {
  formData: Contract;
  handleChange: (event: any, child?: React.ReactNode) => void;
  setFormData: Dispatch<SetStateAction<Contract>>;
}

const BusinessInsuranceForm: React.FC<BusinessInsuranceFormProps> = ({
  formData,
  setFormData,
  handleChange,
}) => {
  const isAdmin = useIsAdmin();

  useEffect(() => {
    if (formData.insuranceSum !== undefined && !isAdmin) {
      const maxInsuranceSum = 5000000;
      if ((formData.insuranceSum || 0) > maxInsuranceSum) {
        setFormData({
          ...formData,
          insuranceSum: maxInsuranceSum,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData, formData.insuranceSum, setFormData]);

  return (
    <div>
      <RiskAddressForm
        formData={formData}
        handleChange={handleChange}
        setFormData={setFormData}
      />
      <div className="grid grid-cols-2 gap-2 mt-8">
        <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
          <TextField
            type="number"
            label="Versicherungssumme in €"
            name="insuranceSum"
            value={formData.insuranceSum || ''}
            onChange={handleChange}
            placeholder="Versicherungssumme"
            className="w-full p-2"
            required
          />
        </Tooltip>
        <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
          <FormControl required variant="outlined" className="w-full p-2">
            <InputLabel>Tarifgruppe</InputLabel>
            <Select
              name="tariffGroup"
              label="Tarifgruppe"
              value={formData.tariffGroup}
              onChange={handleChange}
              required
            >
              <MenuItem value="Handel">Handel</MenuItem>
              <MenuItem value="Handwerk">Handwerk</MenuItem>
              <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
              <MenuItem value="Gastronomie">Gastronomie</MenuItem>
            </Select>
          </FormControl>
        </Tooltip>
        <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
          <TextField
            type="text"
            label="Betriebsart"
            name="businessType"
            value={formData.businessType}
            onChange={handleChange}
            placeholder="Betriebsart"
            className="w-full p-2"
            required
          />
        </Tooltip>
      </div>

      <div className="grid grid-cols-2 gap-2 mt-4">
        <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
          <FormControlLabel
            control={
              <Checkbox
                name="isElementar"
                checked={formData.isElementar || false}
                onChange={handleChange}
              />
            }
            label="Elementar"
          />
        </Tooltip>

        {formData.isElementar && (
          <Tooltip title="Wählen Sie die Zürs Zone aus.">
            <FormControl required variant="outlined" className="w-full p-2">
              <InputLabel hidden={!formData.isElementar}>Zürs Zone</InputLabel>
              <Select
                name="zuersZone"
                value={formData.zuersZone}
                label="Zürs Zone"
                onChange={handleChange}
                required={formData.isElementar}
              >
                <MenuItem value="1">1</MenuItem>
                <MenuItem value="2">2</MenuItem>
              </Select>
            </FormControl>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default BusinessInsuranceForm;
