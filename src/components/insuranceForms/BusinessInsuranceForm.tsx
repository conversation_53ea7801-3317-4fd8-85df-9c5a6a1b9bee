// src/components/insuranceForms/BusinessInsuranceForm.tsx
// Geschäftsversicherung

import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData } from '../../types';
import RiskAddressForm from './RiskAddressForm';
import { Checkbox, FormControl, FormControlLabel, InputLabel, MenuItem, Select, TextField, Tooltip } from '@mui/material';

interface BusinessInsuranceFormProps {
    formData: ContractData;
    handleChange: (event: any, child?: React.ReactNode) => void;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const BusinessInsuranceForm: React.FC<BusinessInsuranceFormProps> = ({ formData, setFormData, handleChange }) => {

    useEffect(() => {
        if (formData.insurance_sum !== undefined && localStorage.getItem("is_admin") == "false") {
            const maxInsureanceSum = 5000000
            if ((formData.insurance_sum) > maxInsureanceSum) {
                setFormData({
                    ...formData,
                    insurance_sum: maxInsureanceSum,
                });
            }
        }
    }, [formData.insurance_sum, setFormData]);

    return (
        <div>
            <RiskAddressForm formData={formData} handleChange={handleChange} setFormData={setFormData} />
            <div className='grid grid-cols-2 gap-2 mt-8'>
                <Tooltip title="Geben Sie den Betrag an, bis zu dem die Versicherung im Schadensfall abdeckt.">
                    <TextField
                        type="number"
                        label="Versicherungssumme in €"
                        name="insurance_sum"
                        value={formData.insurance_sum || ""}
                        onChange={handleChange}
                        placeholder="Versicherungssumme"
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
                <Tooltip title="Wählen Sie die Tarifgruppe des Betriebs, um die Versicherungsbedingungen und -kosten an Ihre Bedürfnisse anzupassen.">
                    <FormControl required variant="outlined" className="w-full p-2">
                        <InputLabel>Tarifgruppe</InputLabel>
                        <Select
                            name="tariff_group"
                            label="Tarifgruppe"
                            value={formData.tariff_group}
                            onChange={handleChange}
                            required
                        >
                            <MenuItem value="Handel">Handel</MenuItem>
                            <MenuItem value="Handwerk">Handwerk</MenuItem>
                            <MenuItem value="Dienstleistungen">Dienstleistungen</MenuItem>
                            <MenuItem value="Gastronomie">Gastronomie</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>
                <Tooltip title="Geben Sie hier die Betriebsart an. (z.B.: Friseur, Restaurant, Büro ...)">
                    <TextField
                        type="text"
                        label="Betriebsart"
                        name="business_type"
                        value={formData.business_type}
                        onChange={handleChange}
                        placeholder="Betriebsart"
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
            </div>

            <div className='grid grid-cols-2 gap-2 mt-4'>
                <Tooltip title="Aktivieren Sie diese Option, um die Deckung für Schäden durch Naturereignisse wie Überschwemmungen oder Erdbeben einzuschließen.">
                    <FormControlLabel
                        control={<Checkbox
                            name="is_elementar"
                            checked={formData.is_elementar || false}
                            onChange={handleChange} />}
                        label="Elementar" />
                </Tooltip>

                {formData.is_elementar &&
                    <Tooltip title="Wählen Sie die Zürs Zone aus.">
                        <FormControl
                            required variant="outlined"
                            className="w-full p-2"
                        >
                            <InputLabel hidden={!formData.is_elementar}>Zürs Zone</InputLabel>
                            <Select name="zuers_zone"
                                value={formData.zuers_zone}
                                label="Zürs Zone"
                                onChange={handleChange}
                                required={formData.is_elementar}
                            >
                                <MenuItem value="1">1</MenuItem>
                                <MenuItem value="2">2</MenuItem>
                            </Select>
                        </FormControl>
                    </Tooltip>}
            </div>
        </div>
    );
};

export default BusinessInsuranceForm;