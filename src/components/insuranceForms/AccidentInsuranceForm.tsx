// src/components/insuranceForms/AccidentInsuranceForm.tsx
// Unfallversicherung

import React, { useEffect, Dispatch, SetStateAction, useState } from 'react';
import occupations from '../../utils/occupations.json';
import { Checkbox, FormControl, FormControlLabel, InputLabel, MenuItem, Select, TextField, Tooltip, Button, IconButton, InputAdornment, Autocomplete } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { ContractData, AccidentInsuranceFormData } from '../../types';

interface AccidentInsuranceFormProps {
    formData: ContractData;
    setFormData: Dispatch<SetStateAction<ContractData>>;
    handleChange: (event: any, child?: React.ReactNode) => void;
}

const AccidentInsuranceForm: React.FC<AccidentInsuranceFormProps> = ({ formData, setFormData }) => {
    const [isAgeEligible, setIsAgeEligible] = useState<boolean[]>([]);

    const defaultPerson: AccidentInsuranceFormData = {
        first_name: '',
        last_name: '',
        birth_date: '',
        type: '',
        occupation: '',
        occupation_group: '',
        disability_coverage: '',
        basic_sum: 0,
        increased_benefit_clause: 0,
        accident_pension: 0,
        accidental_death: 0,
        daily_sickness_allowance: 0,
        hospital_daily_allowance: 0,
        transitional_benefit: 0,
        first_aid_module: false,
    };

    useEffect(() => {
        const isAdmin = localStorage.getItem("is_admin") === "true";
        const updatedPersons = formData.insured_persons.map((person) => {
            const updatedPerson = { ...person };

            // Limit of increased_benefit_clause
            if (!isAdmin && Number(updatedPerson.increased_benefit_clause) > 500000) {
                updatedPerson.increased_benefit_clause = 500000;
            }

            // Limit of accidental_death
            const maxAccidentalDeath = updatedPerson.type === "Kind" ? 10000 : 500000;
            if (!isAdmin && Number(updatedPerson.accidental_death) > maxAccidentalDeath) {
                updatedPerson.accidental_death = maxAccidentalDeath;
            }

            // Limit of hospital_daily_allowance
            const maxHospitalDailyAllowance = updatedPerson.type === "Kind" ? 30 : 75;
            if (!isAdmin && Number(updatedPerson.hospital_daily_allowance) > maxHospitalDailyAllowance) {
                updatedPerson.hospital_daily_allowance = maxHospitalDailyAllowance;
            }

            // Limit of transitional_benefit
            const maxTransitionalBenefit = updatedPerson.type === "Kind" ? 10000 : 20000;
            if (!isAdmin && Number(updatedPerson.transitional_benefit) > maxTransitionalBenefit) {
                updatedPerson.transitional_benefit = maxTransitionalBenefit;
            }

            // Limit of accident_pension
            if (!isAdmin && Number(updatedPerson.accident_pension) > 1500) {
                updatedPerson.accident_pension = 1500;
            }

            // Limit of daily_sickness_allowance
            if (!isAdmin && Number(updatedPerson.daily_sickness_allowance) > 1000) { //TODO Ask for Value
                updatedPerson.daily_sickness_allowance = 1000;
            }

            // Limit of basic_sum depending on disability_coverage
            let maxBasicSum = 100000; // Default
            switch (updatedPerson.disability_coverage) {
                case "0":
                    maxBasicSum = 0;
                    break;
                case "225":
                    maxBasicSum = 440000;
                    break;
                case "350":
                    maxBasicSum = 280000;
                    break;
                case "500":
                    maxBasicSum = 200000;
                    break;
                default:
                    maxBasicSum = 100000;
            }
            if (!isAdmin && Number(updatedPerson.basic_sum) > maxBasicSum) {
                updatedPerson.basic_sum = maxBasicSum;
            }

            return updatedPerson;
        });

        setFormData((prev) => ({
            ...prev,
            insured_persons: updatedPersons,
        }));
    }, [
        formData.insured_persons.map(p => `${p.increased_benefit_clause}-${p.accidental_death}-${p.hospital_daily_allowance}-${p.accident_pension}-${p.transitional_benefit}-${p.daily_sickness_allowance}-${p.basic_sum}-${p.disability_coverage}-${p.type}`).join(','),
        setFormData
    ]);


    useEffect(() => {
        if (!formData.insured_persons || formData.insured_persons.length === 0) {
            setFormData(prev => ({
                ...prev,
                insured_persons: [defaultPerson]
            }));
        }
    }, []);

    useEffect(() => {
        const updatedPersons = formData.insured_persons?.map(person => {
            if (!person.birth_date) return person;

            const birthDate = new Date(person.birth_date);
            const insuranceStartDate = formData.insurance_start_date ? new Date(formData.insurance_start_date) : new Date();
            let age = insuranceStartDate.getFullYear() - birthDate.getFullYear();
            const m = insuranceStartDate.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && insuranceStartDate.getDate() < birthDate.getDate())) {
                age--;
            }

            let type = '';
            if (age < 18) {
                type = 'Kind';
            } else if (age > 65) {
                type = 'Senior';
            } else {
                type = 'Berufstätig';
            }

            return {
                ...person,
                type
            };
        }) || [];

        // Update state
        setFormData(prev => ({
            ...prev,
            insured_persons: updatedPersons
        }));

        const eligibility = updatedPersons.map(person => {
            if (!person.birth_date) return false;
            const birthDate = new Date(person.birth_date);
            const insuranceStartDate = formData.insurance_start_date ? new Date(formData.insurance_start_date) : new Date();

            let age = insuranceStartDate.getFullYear() - birthDate.getFullYear();
            const m = insuranceStartDate.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && insuranceStartDate.getDate() < birthDate.getDate())) {
                age--;
            }

            return age >= 18 && age <= 65;
        });

        setIsAgeEligible(eligibility);
    }, [formData.insured_persons?.map(p => p.birth_date).join(','), formData.insurance_start_date]);


    const handlePersonChange = (index: number, field: keyof AccidentInsuranceFormData, value: any) => {
        const updatedPersons = [...formData.insured_persons];

        if (field == 'occupation') {
            updatedPersons[index] = {
                ...updatedPersons[index],
                'occupation': value.name,
                'occupation_group': value.gefahrengruppe
            };
        } else {
            updatedPersons[index] = {
                ...updatedPersons[index],
                [field]: value,
            };
        }

        setFormData({
            ...formData,
            insured_persons: updatedPersons,
        });
    };

    const addPerson = () => {
        setFormData(prev => ({
            ...prev,
            insured_persons: [...prev.insured_persons, defaultPerson]
        }));
    };

    const removePerson = (index: number) => {
        const updated = formData.insured_persons.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            insured_persons: updated
        });
    };

    useEffect(() => {
        formData.insured_persons?.forEach((person, index) => {
            const occupation = occupations.find(o => o.name === person.occupation);
            if (occupation && person.occupation !== '') {
                handlePersonChange(index, 'occupation_group', occupation.gefahrengruppe || '');
            }
        });
    }, [formData.insured_persons?.map(person => person.occupation).join(',')]);

    return (
        <div className='flex flex-col gap-4'>
            {formData.insured_persons?.map((person, index) => (
                <div key={index}>
                    <p className='pb-2 font-semibold text-alpha_gray'>{`Person ${index + 1}`}</p>
                    <div key={index} className='flex items-center'>
                        <div>
                            <div className='grid grid-cols-2 gap-2'>
                                <Tooltip title="Vorname der versicherten Person">
                                    <TextField
                                        label="Vorname"
                                        value={person.first_name}
                                        onChange={(e) => handlePersonChange(index, 'first_name', e.target.value)}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Nachname der versicherten Person">
                                    <TextField
                                        label="Nachname"
                                        value={person.last_name}
                                        onChange={(e) => handlePersonChange(index, 'last_name', e.target.value)}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Geburtsdatum">
                                    <TextField
                                        label="Geburtsdatum"
                                        type="date"
                                        value={person.birth_date}
                                        onChange={(e) => handlePersonChange(index, 'birth_date', e.target.value)}
                                        InputLabelProps={{ shrink: true }}
                                        fullWidth
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Der Typ wird anhand des Alters ermittelt">
                                    <TextField
                                        label="Typ"
                                        value={person.type}
                                        fullWidth
                                        disabled
                                    />
                                </Tooltip>
                                {isAgeEligible[index] && (
                                    <>
                                        <Tooltip title="Die Prämie hängt wesentlich von dem Risiko der beruflichen Tätigkeit ab. Bitte geben Sie Ihre aktuell ausgeübte berufliche Tätigkeit ein. Falls diese in der Liste nicht vorhanden ist, wählen Sie bitte eine ähnliche Tätigkeit oder schreiben Sie Ihre Berufstätigkeit aus.">
                                            <Autocomplete
                                                options={occupations}
                                                getOptionLabel={(option) => option.name}
                                                value={occupations.find((o) => o.name === person.occupation) || null}
                                                onChange={(event, newValue) => {
                                                    handlePersonChange(index, 'occupation', newValue || '');
                                                }}
                                                isOptionEqualToValue={(option, value) =>
                                                    option.name?.toLowerCase() === value?.name?.toLowerCase()
                                                }
                                                renderInput={(params) => <TextField {...params} label="Beruf" fullWidth required={true} />}
                                            />
                                        </Tooltip>
                                        <Tooltip title="Die Gefahrengruppe wird durch die Angabe der beruflichen Tätigkeit ermittelt.">
                                            <TextField
                                                label="Gefahrengruppe"
                                                value={person.occupation_group}
                                                fullWidth
                                                disabled
                                            />
                                        </Tooltip>
                                    </>
                                )}
                            </div>

                            {person.birth_date &&
                                <div className='grid grid-cols-2 gap-2 mt-4'>
                                    <Tooltip title="Die Progression erhöht bei einer schweren Invalidität den Betrag, der an Sie ausgezahlt wird. Die maximale Erhöhung können Sie durch den entsprechenden Progressionswert festlegen.">
                                        <FormControl fullWidth>
                                            <InputLabel>Invalidität</InputLabel>
                                            <Select
                                                value={person.disability_coverage}
                                                onChange={(e) => handlePersonChange(index, 'disability_coverage', e.target.value)}
                                                label="Progression"
                                                required
                                            >
                                                <MenuItem value="0">keine</MenuItem>
                                                <MenuItem value="225">225%</MenuItem>
                                                <MenuItem value="350">350%</MenuItem>
                                                <MenuItem value="500">500%</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Tooltip>
                                    <Tooltip title="Die Grundsumme dient im Versicherungsfall als Berechnungsgrundlage für die Auszahlungssumme. Im Vorfeld die angemessene Versicherungssumme für die private Unfallversicherung festzulegen ist schwierig, da hier unterschiedliche Parameter zusammenkommen. Als Faustformel gilt: Die Grundsumme sollte mindestens das zwei- bis dreifache des Bruttojahresverdienstes betragen.">
                                        <TextField
                                            label="Grundsumme Invalidität"
                                            type="number"
                                            value={person.basic_sum}
                                            onChange={(e) => handlePersonChange(index, 'basic_sum', e.target.value)}
                                            fullWidth
                                            required
                                            inputProps={{
                                                max:
                                                    localStorage.getItem("is_admin") === "true"
                                                        ? undefined
                                                        : person.disability_coverage === "0"
                                                            ? 0
                                                            : person.disability_coverage === "225"
                                                                ? 440000
                                                                : person.disability_coverage === "350"
                                                                    ? 280000
                                                                    : person.disability_coverage === "500"
                                                                        ? 200000
                                                                        : 100000, // Default
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>

                                    <Tooltip title="Mehrleistungsklausel 90%.">
                                        <TextField
                                            label="Mehrleistungsklausel"
                                            type="number"
                                            value={person.increased_benefit_clause}
                                            onChange={(e) => handlePersonChange(index, 'increased_benefit_clause', e.target.value)}
                                            fullWidth
                                            inputProps={{
                                                max: localStorage.getItem("is_admin") === "true" ? undefined : 500000,
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>
                                    <Tooltip title="Die Unfallrente wird lebenslang monatlich gezahlt, zusätzlich zum Auszahlungsbetrag.">
                                        <TextField
                                            label="Unfallrente"
                                            type="number"
                                            value={person.accident_pension}
                                            onChange={(e) => handlePersonChange(index, 'accident_pension', e.target.value)}
                                            fullWidth
                                            inputProps={{
                                                max: localStorage.getItem("is_admin") === "true" ? undefined : 1500,
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>
                                    <Tooltip title="Die Todesfallsumme wird an die Hinterbliebenen ausbezahlt. Voraussetzung: Die versicherte Person ist infolge des Unfalles innerhalb eines Jahres gestorben.">
                                        <TextField
                                            label="Unfalltod"
                                            type="number"
                                            value={person.accidental_death}
                                            onChange={(e) => handlePersonChange(index, 'accidental_death', e.target.value)}
                                            fullWidth
                                            inputProps={{
                                                max: localStorage.getItem("is_admin") === "true"
                                                    ? undefined
                                                    : person.type === "Kind"
                                                        ? 10000
                                                        : 500000,
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>
                                    {isAgeEligible[index] && (
                                        <Tooltip title="Das Tagegeld wird nach der vereinbarten Versicherungssumme berechnet und gezahlt. Es wird nach dem festgestellten Grad der Beeinträchtigung der Berufstätigkeit oder Beschäftigung abgestuft.">
                                            <TextField
                                                label="Krankentagegeld"
                                                type="number"
                                                value={person.daily_sickness_allowance}
                                                onChange={(e) => handlePersonChange(index, 'daily_sickness_allowance', e.target.value)}
                                                fullWidth
                                                inputProps={{
                                                    max: localStorage.getItem("is_admin") === "true" ? undefined : 1000,
                                                }}
                                                slotProps={{
                                                    input: {
                                                        endAdornment: (
                                                            <InputAdornment position="end">€</InputAdornment>
                                                        )
                                                    }
                                                }}
                                            />
                                        </Tooltip>
                                    )}
                                    <Tooltip title="Dieses wird für jeden Tag gezahlt, den man unfallbedingt im Krankenhaus verbringt. Das Genesungsgeld wird zusätzlich zum Krankenhaustagegeld für jeden Tag bezahlt, der im Krankenhaus verbracht wurde.">
                                        <TextField
                                            label="Krankenhaustagegeld mit Genesungsgeld"
                                            value={person.hospital_daily_allowance}
                                            onChange={(e) => handlePersonChange(index, 'hospital_daily_allowance', e.target.value)}
                                            fullWidth
                                            inputProps={{
                                                max: localStorage.getItem("is_admin") === "true"
                                                    ? undefined
                                                    : person.type === "Kind"
                                                        ? 30
                                                        : 75,
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>
                                    <Tooltip title="Ist die normale körperliche oder geistige Leistungsfähigkeit der versicherten Person im beruflichen oder außerberuflichen Bereich unfallbedingt beeinträchtigt, so greift die Übergangsleistung.">
                                        <TextField
                                            label="Übergangsleistung"
                                            type="number"
                                            value={person.transitional_benefit}
                                            onChange={(e) => handlePersonChange(index, 'transitional_benefit', e.target.value)}
                                            fullWidth
                                            inputProps={{
                                                max: localStorage.getItem("is_admin") === "true"
                                                    ? undefined
                                                    : person.type === "Kind"
                                                        ? 10000
                                                        : 20000,
                                            }}
                                            slotProps={{
                                                input: {
                                                    endAdornment: (
                                                        <InputAdornment position="end">€</InputAdornment>
                                                    )
                                                }
                                            }}
                                        />
                                    </Tooltip>
                                    <Tooltip title="Nach Nachweis der Hilfsbedürftigkeit durch ärztliche Unterlagen: Erstgespräch sowie folgende Hilfestellungen für die Dauer von 6 Monaten: Hauswirtschaftliche Leistungen, personenbezogene Leistungen, sonstige Hilfeleistungen. Für 21 Tage: Kinderbetreuung für Kinder und Enkelkinder unter 18 Jahren.">
                                        <FormControlLabel
                                            control={<Checkbox
                                                name="first_aid_module"
                                                checked={person.first_aid_module}
                                                onChange={(e) => handlePersonChange(index, 'first_aid_module', e.target.checked)}
                                            />}
                                            label="Erste-Hilfe-Baustein" />
                                    </Tooltip>
                                </div>
                            }
                        </div>
                        <div className=" top-2 right-2">
                            <IconButton
                                type="button"
                                onClick={() => removePerson(index)}
                                disabled={formData.insured_persons.length === 1}
                            >
                                <DeleteIcon />
                            </IconButton>
                        </div>
                    </div>
                </div>
            ))}
            <div>
                <Button
                    variant="contained"
                    color="secondary"
                    onClick={addPerson}
                    startIcon={<AddIcon />}
                >
                    Person hinzufügen
                </Button>
            </div>
        </div>
    );
};

export default AccidentInsuranceForm;
