// src/components/insuranceForms/AccidentInsuranceForm.tsx
// Unfallversicherung

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import {
  Autocomplete,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import React, {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { type Contract } from '@/generated/prisma-postgres';
import { type AccidentInsuranceFormData } from '@/types';
import { useIsAdmin } from '@/utils/authUtils';
import occupations from '@/utils/occupations.json';

interface AccidentInsuranceFormProps {
  formData: Contract;
  setFormData: Dispatch<SetStateAction<Contract>>;
  handleChange: (event: any, child?: React.ReactNode) => void;
}

// --- Type guard: does the JSON look like AccidentInsuranceFormData[]?
function isAccidentPersons(val: unknown): val is AccidentInsuranceFormData[] {
  return (
    Array.isArray(val) &&
    val.every(
      (p) =>
        p &&
        typeof p === 'object' &&
        'first_name' in p &&
        'last_name' in p &&
        'birth_date' in p
    )
  );
}

// keep snake_case keys here (AccidentInsuranceFormData)
const DEFAULT_PERSON: AccidentInsuranceFormData = {
  first_name: '',
  last_name: '',
  birth_date: '',
  type: '',
  occupation: '',
  occupation_group: '',
  disability_coverage: '',
  basic_sum: 0,
  increased_benefit_clause: 0,
  accident_pension: 0,
  accidental_death: 0,
  daily_sickness_allowance: 0,
  hospital_daily_allowance: 0,
  transitional_benefit: 0,
  first_aid_module: false,
};

const AccidentInsuranceForm: React.FC<AccidentInsuranceFormProps> = ({
  formData,
  setFormData,
}) => {
  const [isAgeEligible, setIsAgeEligible] = useState<boolean[]>([]);
  const isAdmin = useIsAdmin();

  // persons derived from formData
  const persons: AccidentInsuranceFormData[] = useMemo(() => {
    const raw = (formData as any).insuredPersons;
    if (isAccidentPersons(raw) && raw.length > 0) return raw;
    return [DEFAULT_PERSON];
  }, [formData]);

  // stable setter
  const setPersons = useCallback(
    (next: AccidentInsuranceFormData[]) =>
      setFormData((prev) => ({ ...prev, insuredPersons: next as any })),
    [setFormData]
  );

  // ---- compute dependency keys *before* any effect that uses them
  const personsDependencyString = useMemo(
    () =>
      persons
        .map(
          (p) =>
            `${p.increased_benefit_clause}-${p.accidental_death}-${p.hospital_daily_allowance}-${p.accident_pension}-${p.transitional_benefit}-${p.daily_sickness_allowance}-${p.basic_sum}-${p.disability_coverage}-${p.type}`
        )
        .join(','),
    [persons]
  );

  const birthDatesKey = useMemo(
    () => persons.map((p) => p.birth_date).join(','),
    [persons]
  );

  const occupationsKey = useMemo(
    () => persons.map((p) => p.occupation).join(','),
    [persons]
  );

  // ---- clamp values when inputs change
  useEffect(() => {
    const updated = persons.map((person) => {
      const p = { ...person };
      const _isAdmin: boolean = isAdmin || false;

      // Limits (respect admin)
      if (!_isAdmin && Number(p.increased_benefit_clause) > 500000)
        p.increased_benefit_clause = 500000;

      const maxAccidentalDeath = p.type === 'Kind' ? 10000 : 500000;
      if (!_isAdmin && Number(p.accidental_death) > maxAccidentalDeath)
        p.accidental_death = maxAccidentalDeath;

      const maxHospitalDaily = p.type === 'Kind' ? 30 : 75;
      if (!_isAdmin && Number(p.hospital_daily_allowance) > maxHospitalDaily)
        p.hospital_daily_allowance = maxHospitalDaily;

      const maxTransitional = p.type === 'Kind' ? 10000 : 20000;
      if (!_isAdmin && Number(p.transitional_benefit) > maxTransitional)
        p.transitional_benefit = maxTransitional;

      if (!_isAdmin && Number(p.accident_pension) > 1500)
        p.accident_pension = 1500;

      if (!_isAdmin && Number(p.daily_sickness_allowance) > 1000)
        p.daily_sickness_allowance = 1000;

      // basic_sum limit depends on disability_coverage
      let maxBasic = 100000;
      switch (p.disability_coverage) {
        case '0':
          maxBasic = 0;
          break;
        case '225':
          maxBasic = 440000;
          break;
        case '350':
          maxBasic = 280000;
          break;
        case '500':
          maxBasic = 200000;
          break;
      }
      if (!_isAdmin && Number(p.basic_sum) > maxBasic) p.basic_sum = maxBasic;

      return p;
    });

    setPersons(updated);
    // Only depend on the *key* and the stable setter
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [persons, personsDependencyString, setPersons]);

  // Ensure at least one person row on mount
  useEffect(() => {
    if (
      !isAccidentPersons(formData.insuredPersons) ||
      formData.insuredPersons?.length === 0
    ) {
      setPersons([DEFAULT_PERSON]);
    }
    // setPersons is stable
  }, [formData.insuredPersons, setPersons]);

  // Derive type + eligibility from birth dates & start date
  useEffect(() => {
    const updated = persons.map((person) => {
      if (!person.birth_date) return person;

      const birthDate = new Date(person.birth_date);
      const insuranceStartDate = formData.insuranceStartDate
        ? new Date(formData.insuranceStartDate)
        : new Date();
      let age = insuranceStartDate.getFullYear() - birthDate.getFullYear();
      const m = insuranceStartDate.getMonth() - birthDate.getMonth();
      if (
        m < 0 ||
        (m === 0 && insuranceStartDate.getDate() < birthDate.getDate())
      )
        age--;

      let type = '';
      if (age < 18) type = 'Kind';
      else if (age > 65) type = 'Senior';
      else type = 'Berufstätig';

      return { ...person, type };
    });

    setPersons(updated);

    const eligibility = updated.map((person) => {
      if (!person.birth_date) return false;
      const birthDate = new Date(person.birth_date);
      const insuranceStartDate = formData.insuranceStartDate
        ? new Date(formData.insuranceStartDate)
        : new Date();
      let age = insuranceStartDate.getFullYear() - birthDate.getFullYear();
      const m = insuranceStartDate.getMonth() - birthDate.getMonth();
      if (
        m < 0 ||
        (m === 0 && insuranceStartDate.getDate() < birthDate.getDate())
      )
        age--;
      return age >= 18 && age <= 65;
    });
    setIsAgeEligible(eligibility);
  }, [birthDatesKey, formData.insuranceStartDate, persons, setPersons]);

  // Update occupation_group when occupation changes
  useEffect(() => {
    persons.forEach((person, index) => {
      const occupation = occupations.find((o) => o.name === person.occupation);
      if (occupation && person.occupation !== '') {
        handlePersonChange(
          index,
          'occupation_group',
          occupation.gefahrengruppe || ''
        );
      }
    });
    // only run when the occupation list "key" changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [occupationsKey]);

  const handlePersonChange = (
    index: number,
    field: keyof AccidentInsuranceFormData,
    value: any
  ) => {
    const updated = [...persons];

    if (field === 'occupation') {
      updated[index] = {
        ...updated[index],
        occupation: value?.name ?? '',
        occupation_group: value?.gefahrengruppe ?? '',
      };
    } else {
      updated[index] = { ...updated[index], [field]: value };
    }

    setPersons(updated);
  };

  const addPerson = () => setPersons([...persons, DEFAULT_PERSON]);

  const removePerson = (index: number) => {
    const updated = persons.filter((_, i) => i !== index);
    setPersons(updated.length ? updated : [DEFAULT_PERSON]); // keep at least one row
  };

  return (
    <div className="flex flex-col gap-4">
      {persons.map((person, index) => (
        <div key={index}>
          <p className="pb-2 font-semibold text-alpha_gray">{`Person ${index + 1}`}</p>
          <div className="flex items-center">
            <div>
              <div className="grid grid-cols-2 gap-2">
                <Tooltip title="Vorname der versicherten Person">
                  <TextField
                    label="Vorname"
                    value={person.first_name}
                    onChange={(e) =>
                      handlePersonChange(index, 'first_name', e.target.value)
                    }
                    fullWidth
                    required
                  />
                </Tooltip>
                <Tooltip title="Nachname der versicherten Person">
                  <TextField
                    label="Nachname"
                    value={person.last_name}
                    onChange={(e) =>
                      handlePersonChange(index, 'last_name', e.target.value)
                    }
                    fullWidth
                    required
                  />
                </Tooltip>
                <Tooltip title="Geburtsdatum">
                  <TextField
                    label="Geburtsdatum"
                    type="date"
                    value={person.birth_date}
                    onChange={(e) =>
                      handlePersonChange(index, 'birth_date', e.target.value)
                    }
                    InputLabelProps={{ shrink: true }}
                    fullWidth
                    required
                  />
                </Tooltip>
                <Tooltip title="Der Typ wird anhand des Alters ermittelt">
                  <TextField
                    label="Typ"
                    value={person.type}
                    fullWidth
                    disabled
                  />
                </Tooltip>

                {isAgeEligible[index] && (
                  <>
                    <Tooltip title="Die Prämie hängt wesentlich von der beruflichen Tätigkeit ab.">
                      <Autocomplete
                        options={occupations}
                        getOptionLabel={(option) => option.name}
                        value={
                          occupations.find(
                            (o) => o.name === person.occupation
                          ) || null
                        }
                        onChange={(_, newValue) =>
                          handlePersonChange(
                            index,
                            'occupation',
                            newValue || ''
                          )
                        }
                        isOptionEqualToValue={(option, value) =>
                          option.name?.toLowerCase() ===
                          value?.name?.toLowerCase()
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Beruf"
                            fullWidth
                            required
                          />
                        )}
                      />
                    </Tooltip>
                    <Tooltip title="Die Gefahrengruppe wird aus der Tätigkeit abgeleitet.">
                      <TextField
                        label="Gefahrengruppe"
                        value={person.occupation_group}
                        fullWidth
                        disabled
                      />
                    </Tooltip>
                  </>
                )}
              </div>

              {person.birth_date && (
                <div className="grid grid-cols-2 gap-2 mt-4">
                  <Tooltip title="Progression bei Invalidität.">
                    <FormControl fullWidth>
                      <InputLabel>Invalidität</InputLabel>
                      <Select
                        value={person.disability_coverage}
                        onChange={(e) =>
                          handlePersonChange(
                            index,
                            'disability_coverage',
                            e.target.value
                          )
                        }
                        label="Progression"
                        required
                      >
                        <MenuItem value="0">keine</MenuItem>
                        <MenuItem value="225">225%</MenuItem>
                        <MenuItem value="350">350%</MenuItem>
                        <MenuItem value="500">500%</MenuItem>
                      </Select>
                    </FormControl>
                  </Tooltip>

                  <Tooltip title="Grundsumme Invalidität.">
                    <TextField
                      label="Grundsumme Invalidität"
                      type="number"
                      value={person.basic_sum}
                      onChange={(e) =>
                        handlePersonChange(index, 'basic_sum', e.target.value)
                      }
                      fullWidth
                      required
                      inputProps={{
                        max: isAdmin
                          ? undefined
                          : person.disability_coverage === '0'
                            ? 0
                            : person.disability_coverage === '225'
                              ? 440000
                              : person.disability_coverage === '350'
                                ? 280000
                                : person.disability_coverage === '500'
                                  ? 200000
                                  : 100000,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  <Tooltip title="Mehrleistungsklausel 90%.">
                    <TextField
                      label="Mehrleistungsklausel"
                      type="number"
                      value={person.increased_benefit_clause}
                      onChange={(e) =>
                        handlePersonChange(
                          index,
                          'increased_benefit_clause',
                          e.target.value
                        )
                      }
                      fullWidth
                      inputProps={{
                        max: isAdmin ? undefined : 500000,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  <Tooltip title="Unfallrente (monatlich).">
                    <TextField
                      label="Unfallrente"
                      type="number"
                      value={person.accident_pension}
                      onChange={(e) =>
                        handlePersonChange(
                          index,
                          'accident_pension',
                          e.target.value
                        )
                      }
                      fullWidth
                      inputProps={{
                        max: isAdmin ? undefined : 1500,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  <Tooltip title="Todesfallsumme.">
                    <TextField
                      label="Unfalltod"
                      type="number"
                      value={person.accidental_death}
                      onChange={(e) =>
                        handlePersonChange(
                          index,
                          'accidental_death',
                          e.target.value
                        )
                      }
                      fullWidth
                      inputProps={{
                        max: isAdmin
                          ? undefined
                          : person.type === 'Kind'
                            ? 10000
                            : 500000,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  {isAgeEligible[index] && (
                    <Tooltip title="Krankentagegeld.">
                      <TextField
                        label="Krankentagegeld"
                        type="number"
                        value={person.daily_sickness_allowance}
                        onChange={(e) =>
                          handlePersonChange(
                            index,
                            'daily_sickness_allowance',
                            e.target.value
                          )
                        }
                        fullWidth
                        inputProps={{
                          max: isAdmin ? undefined : 1000,
                        }}
                        slotProps={{
                          input: {
                            endAdornment: (
                              <InputAdornment position="end">€</InputAdornment>
                            ),
                          },
                        }}
                      />
                    </Tooltip>
                  )}

                  <Tooltip title="Krankenhaustagegeld mit Genesungsgeld.">
                    <TextField
                      label="Krankenhaustagegeld mit Genesungsgeld"
                      value={person.hospital_daily_allowance}
                      onChange={(e) =>
                        handlePersonChange(
                          index,
                          'hospital_daily_allowance',
                          e.target.value
                        )
                      }
                      fullWidth
                      inputProps={{
                        max: isAdmin
                          ? undefined
                          : person.type === 'Kind'
                            ? 30
                            : 75,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  <Tooltip title="Übergangsleistung.">
                    <TextField
                      label="Übergangsleistung"
                      type="number"
                      value={person.transitional_benefit}
                      onChange={(e) =>
                        handlePersonChange(
                          index,
                          'transitional_benefit',
                          e.target.value
                        )
                      }
                      fullWidth
                      inputProps={{
                        max: isAdmin
                          ? undefined
                          : person.type === 'Kind'
                            ? 10000
                            : 20000,
                      }}
                      slotProps={{
                        input: {
                          endAdornment: (
                            <InputAdornment position="end">€</InputAdornment>
                          ),
                        },
                      }}
                    />
                  </Tooltip>

                  <Tooltip title="Erste-Hilfe-Baustein.">
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="first_aid_module"
                          checked={person.first_aid_module}
                          onChange={(e) =>
                            handlePersonChange(
                              index,
                              'first_aid_module',
                              e.target.checked
                            )
                          }
                        />
                      }
                      label="Erste-Hilfe-Baustein"
                    />
                  </Tooltip>
                </div>
              )}
            </div>

            <div className="top-2 right-2">
              <IconButton
                type="button"
                onClick={() => removePerson(index)}
                disabled={persons.length === 1}
              >
                <DeleteIcon />
              </IconButton>
            </div>
          </div>
        </div>
      ))}
      <div>
        <Button
          variant="contained"
          color="secondary"
          onClick={addPerson}
          startIcon={<AddIcon />}
        >
          Person hinzufügen
        </Button>
      </div>
    </div>
  );
};

export default AccidentInsuranceForm;
