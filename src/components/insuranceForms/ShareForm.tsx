import React, { Dispatch, SetStateAction, useEffect } from 'react';
import { ContractData, ShareData } from '../../types';
import { But<PERSON>, IconButton, TextField, Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

interface ShareFormProps {
    formData: ContractData;
    setFormData: Dispatch<SetStateAction<ContractData>>;
}

const alphaData: ShareData = {
    share_type: 'Alpha',
    percentage: 100,
    insurance: ''
};

const ShareForm: React.FC<ShareFormProps> = ({ formData, setFormData }) => {

    // Helper function to calculate how much Alpha (index 0) should remain.
    const calculateRemainingAlphaValue = (shareData: ShareData[]) => {
        const totalValue = shareData.reduce(
            (sum, share) => sum + (share.share_type === 'Alpha' ? 0 : share.percentage || 0),
            0
        );
        return Math.max(100 - totalValue, 0);
    };

    const handleShareChange = (index: number, field: keyof ShareData, value: any) => {
        if (!formData.share_data || formData.share_data.length === 0) return;

        const updatedShareData = [...formData.share_data];
        updatedShareData[index] = {
            ...updatedShareData[index],
            [field]: value,
        };

        // Recompute alpha's percentage and update it
        const newAlphaValue = calculateRemainingAlphaValue(updatedShareData);
        updatedShareData[0] = {
            ...updatedShareData[0],
            percentage: newAlphaValue,
        };

        setFormData({ ...formData, share_data: updatedShareData });
    };

    const addShare = () => {
        if (!formData.share_data) {
            setFormData({ ...formData, share_data: [alphaData] });
            return;
        }

        const newShare: ShareData = { share_type: 'Makler', percentage: 0, insurance: '' };
        const updatedShareData = [...formData.share_data, newShare];
        setFormData({ ...formData, share_data: updatedShareData });
    };

    const removeShare = (index: number) => {
        if (!formData.share_data || formData.share_data.length === 0) return;

        // Remove the share at the given index
        const updatedShareData = formData.share_data.filter((_, i) => i !== index);
        setFormData({ ...formData, share_data: updatedShareData });
    };

    if (!formData.share_data || formData.share_data.length === 0) {
        return null;
    }

    return (
        <div className='pb-5'>
            <div className="flex flex-col gap-2">
                {formData.share_data.map((share, index) => (
                    <div className="flex flex-row gap-4 items-center" key={index}>
                        <div className='grid grid-cols-3 gap-2'>
                            <TextField type='text' label="Teilhaber" value={share.share_type} disabled className="w-full p-2 bg-gray-100 text-gray-500" />

                            <Tooltip title="Angabe des Vertragsanteils in %">
                                <TextField
                                    type="number"
                                    label="Wert in %"
                                    value={share.percentage || ''}
                                    onChange={(e) =>
                                        handleShareChange(index, 'percentage', parseFloat(e.target.value) || 0)
                                    }
                                    className="w-full p-2"
                                    disabled={index === 0}
                                />
                            </Tooltip>

                            {index !== 0 && (<Tooltip title="Name der Versicherung">
                                <TextField
                                    type="text"
                                    label="Versicherung"
                                    value={share.insurance || ''}
                                    onChange={(e) => handleShareChange(index, 'insurance', e.target.value)}
                                    className="w-full p-2"
                                    disabled={index === 0}
                                />
                            </Tooltip>)}
                        </div>
                        {index !== 0 && (
                            <div className="">
                                <IconButton
                                    type="button"
                                    onClick={() => removeShare(index)}
                                    color='primary'
                                >
                                    <DeleteIcon />
                                </IconButton>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            <div className='mt-4'>
                <Tooltip title="Hier können weitere Anteile hinzugefügt werden.">
                    <Button
                        type="button"
                        onClick={addShare}
                        color='secondary'
                        variant="contained"
                    >
                        <AddIcon className="pr-2" />
                        <p>Anteil hinzufügen</p>
                    </Button>
                </Tooltip>
            </div>

            {calculateRemainingAlphaValue(formData.share_data) < 0 && (
                <p className="text-red-500">Die Summe der Werte überschreitet 100!</p>
            )}
        </div>
    );
};

export default ShareForm;
