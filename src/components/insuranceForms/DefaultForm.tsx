// src/components/insuranceForms/DefaultForm.tsx
// Allgemeine Felder

import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
} from '@mui/material';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { type Contract } from '@/generated/prisma-postgres';

interface DefaultFormProps {
  formData: Contract;
  handleChange: (event: any, child?: React.ReactNode) => void;
}

export default function DefaultForm({
  formData,
  handleChange,
}: DefaultFormProps) {
  const session = useSession();
  const [hasPreviousInsurance, setHasPreviousInsurance] =
    useState<boolean>(false);
  useEffect(() => {
    setHasPreviousInsurance((formData.previousInsurance || '').trim() !== '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    handleChange({
      target: {
        name: 'insuranceMainDueDate',
        value: formData.insuranceStartDate,
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.insuranceStartDate]);

  return (
    <div className="mb-10 flex flex-col gap-4">
      {session.data?.roles.includes('asevo-admin') && (
        <div className="grid grid-cols-2 gap-4">
          <Tooltip title="Setzen Sie einen abweichenden Courtagewert, falls ein individueller Wert gefordert ist.">
            <TextField
              type="number"
              label="Courtage"
              name="commission"
              value={((formData.commission || 0) * 100).toFixed(0)}
              onChange={(event) => {
                const newValue =
                  parseFloat(event.target.value.replace(',', '.')) / 100;
                handleChange({
                  target: {
                    name: 'commission',
                    value: newValue,
                  },
                });
              }}
              className="w-full p-2"
              required
              slotProps={{
                inputLabel: {
                  shrink: true,
                },
                input: {
                  endAdornment: (
                    <InputAdornment position="end">%</InputAdornment>
                  ),
                },
              }}
            />
          </Tooltip>
        </div>
      )}
      <div className="grid grid-cols-2 gap-2">
        <Tooltip title="Wählen Sie das Datum, an dem die Versicherung in Kraft treten soll.">
          <TextField
            type="date"
            label="Versicherungsbeginn"
            name="insuranceStartDate"
            value={formData.insuranceStartDate}
            onChange={handleChange}
            className="w-full p-2"
            required
            slotProps={{
              inputLabel: {
                shrink: true,
              },
            }}
          />
        </Tooltip>
        <Tooltip title="Das Datum an dem die Versicherung endet.">
          <TextField
            type="date"
            label="Versicherungsablauf"
            name="insuranceEndDate"
            value={formData.insuranceEndDate}
            onChange={handleChange}
            className="w-full p-2"
            disabled={!session.data?.roles.includes('asevo-admin')}
            required
            slotProps={{
              inputLabel: {
                shrink: true,
              },
            }}
          />
        </Tooltip>
        <Tooltip title="Das Datum der Hauptfälligkeit.">
          <TextField
            type="date"
            label="Hauptfälligkeit"
            name="insuranceMainDueDate"
            value={formData.insuranceMainDueDate}
            onChange={handleChange}
            className="w-full p-2"
            disabled={!session.data?.roles.includes('asevo-admin')}
            required
            slotProps={{
              inputLabel: {
                shrink: true,
              },
            }}
          />
        </Tooltip>
        {!['bauleistung', 'bauherrenhaftpflicht'].includes(
          formData.contractType || ''
        ) && (
          <Tooltip title="Wählen Sie, wie Versicherungsbeiträge gezahlt werden. Bei bestimmten Vertragsarten wie 'Bauleistung' wird die Zahlungsweise automatisch festgelegt.">
            <FormControl required variant="outlined" className="w-full p-2">
              <InputLabel>Zahlungsweise</InputLabel>
              <Select
                value={formData.paymentMode}
                onChange={handleChange}
                label="Zahlungsweise"
                name="paymentMode"
              >
                {session.data?.roles.includes('asevo-admin') &&
                  ['monatlich', 'vierteljährlich'].map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                <MenuItem value="halbjährlich">halbjährlich</MenuItem>
                <MenuItem value="jährlich">jährlich</MenuItem>
              </Select>
            </FormControl>
          </Tooltip>
        )}
      </div>
      <div className="grid grid-cols-2 gap-2">
        <Tooltip title="Geben Sie die IBAN ein, um die Kontoverbindung für die Beitragszahlung anzugeben.">
          <TextField
            type="text"
            label="IBAN"
            name="iban"
            value={formData.iban}
            onChange={handleChange}
            className="w-full p-2"
          />
        </Tooltip>
        <Tooltip title="Geben Sie die BIC ein, wenn internationale Überweisungen genommen werden sollen oder das Konto bei einer Bank im Ausland ist.">
          <TextField
            type="text"
            label="BIC"
            name="bic"
            value={formData.bic}
            onChange={handleChange}
            className="w-full p-2"
          />
        </Tooltip>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-4">
        <FormControlLabel
          control={
            <Checkbox
              checked={!hasPreviousInsurance}
              onChange={(_, _checked) => {
                setHasPreviousInsurance(!hasPreviousInsurance);
              }}
            />
          }
          label="Keine Vorversicherung"
        />
      </div>

      {hasPreviousInsurance && (
        <div className="grid grid-cols-2 gap-2">
          <Tooltip title="Nennen Sie den Vorversicherer.">
            <TextField
              type="text"
              label="Vorversicherer"
              name="previousInsurance"
              value={formData.previousInsurance ?? ''}
              onChange={handleChange}
              className="w-full p-2"
              required
            />
          </Tooltip>

          <Tooltip title="Versicherungsnummer des Vorvertrags (optional).">
            <TextField
              type="text"
              label="Nr. Vorversicherer"
              name="previousInsuranceNumber"
              value={formData.previousInsuranceNumber ?? ''}
              onChange={handleChange}
              className="w-full p-2"
            />
          </Tooltip>
        </div>
      )}

      <Tooltip title="Falls bereits Schäden oder Ansprüche geltend gemacht wurden, die von einer Versicherung abgedeckt wurden, geben Sie sie hier an.">
        <FormControlLabel
          control={
            <Checkbox
              name="previousClaims"
              checked={formData.previousClaims}
              onChange={handleChange}
            />
          }
          label="Vorschadensfrei"
          required={!session.data?.roles.includes('asevo-admin')}
        />
      </Tooltip>
    </div>
  );
}
