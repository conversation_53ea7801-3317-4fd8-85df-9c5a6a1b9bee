// src/components/insuranceForms/DefaultForm.tsx
// Allgemeine Felder

import { ContractData } from "@/types";
import { TextField, FormControlLabel, Checkbox, InputLabel, Select, FormControl, MenuItem, Tooltip, InputAdornment } from "@mui/material";
import { useEffect } from "react";

interface DefaultFormProps {
    formData: ContractData & { noPreviousInsurance: boolean };
    handleChange: (event: any, child?: React.ReactNode) => void;
}

export default function DefaultForm({ formData, handleChange }: DefaultFormProps) {

    useEffect(() => {
        handleChange({
            target: {
                name: "insurance_main_due_date",
                value: formData.insurance_start_date,
            },
        });
    }, [formData.insurance_start_date])

    return <div className='mb-10 flex flex-col gap-4'>
        {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-4'>
            <Tooltip title="Setzen Sie einen abweichenden Courtagewert, falls ein individueller Wert gefordert ist.">
                <TextField
                    type="number"
                    label="Courtage"
                    name="commission"
                    value={(formData.commission * 100).toFixed(0)}
                    onChange={(event) => {
                        const newValue = parseFloat(event.target.value.replace(',', '.')) / 100;
                        handleChange({
                            target: {
                                name: "commission",
                                value: newValue,
                            },
                        });
                    }}
                    className="w-full p-2"
                    required
                    slotProps={{
                        inputLabel: {
                            shrink: true
                        },
                        input: {
                            endAdornment: (
                                <InputAdornment position="end">%</InputAdornment>
                            )
                        }
                    }}
                />
            </Tooltip>

        </div>}
        <div className='grid grid-cols-2 gap-2'>
            <Tooltip title="Wählen Sie das Datum, an dem die Versicherung in Kraft treten soll.">
                <TextField
                    type="date"
                    label="Versicherungsbeginn"
                    name="insurance_start_date"
                    value={formData.insurance_start_date}
                    onChange={handleChange}
                    className="w-full p-2"
                    required
                    slotProps={{
                        inputLabel: {
                            shrink: true
                        }
                    }} />
            </Tooltip>
            <Tooltip title="Das Datum an dem die Versicherung endet.">
                <TextField
                    type="date"
                    label="Versicherungsablauf"
                    name="insurance_end_date"
                    value={formData.insurance_end_date}
                    onChange={handleChange}
                    className="w-full p-2"
                    disabled={localStorage.getItem("is_admin") == "false"}
                    required
                    slotProps={{
                        inputLabel: {
                            shrink: true
                        }
                    }} />
            </Tooltip>
            <Tooltip title="Das Datum der Hauptfälligkeit.">
                <TextField
                    type="date"
                    label="Hauptfälligkeit"
                    name="insurance_main_due_date"
                    value={formData.insurance_main_due_date}
                    onChange={handleChange}
                    className="w-full p-2"
                    disabled={localStorage.getItem("is_admin") == "false"}
                    required
                    slotProps={{
                        inputLabel: {
                            shrink: true
                        }
                    }} />
            </Tooltip>
            {!["bauleistung", "bauherrenhaftpflicht"].includes(formData.contract_type) &&
                <Tooltip title="Wählen Sie, wie Versicherungsbeiträge gezahlt werden. Bei bestimmten Vertragsarten wie 'Bauleistung' wird die Zahlungsweise automatisch festgelegt.">
                    <FormControl required variant="outlined" className="w-full p-2">
                        <InputLabel>Zahlungsweise</InputLabel>
                        <Select
                            value={formData.payment_mode}
                            onChange={handleChange}
                            label="Zahlungsweise"
                            name="payment_mode">
                            {localStorage.getItem("is_admin") == "true" &&
                                ["monatlich", "vierteljährlich"].map(option => (
                                    <MenuItem key={option} value={option}>{option}</MenuItem>
                                ))}
                            <MenuItem value="halbjährlich">halbjährlich</MenuItem>
                            <MenuItem value="jährlich">jährlich</MenuItem>
                        </Select>
                    </FormControl>
                </Tooltip>
            }
        </div>
        <div className='grid grid-cols-2 gap-2'>
            <Tooltip title="Geben Sie die IBAN ein, um die Kontoverbindung für die Beitragszahlung anzugeben.">
                <TextField
                    type="text"
                    label="IBAN"
                    name="iban"
                    value={formData.iban}
                    onChange={handleChange}
                    className="w-full p-2" />
            </Tooltip>
            <Tooltip title="Geben Sie die BIC ein, wenn internationale Überweisungen genommen werden sollen oder das Konto bei einer Bank im Ausland ist.">
                <TextField
                    type="text"
                    label="BIC"
                    name="bic"
                    value={formData.bic}
                    onChange={handleChange}
                    className="w-full p-2" />
            </Tooltip>
        </div>
        <div className='grid grid-cols-2 gap-2 mt-4'>
            <FormControlLabel
                control={
                    <Checkbox
                        name="noPreviousInsurance"
                        checked={formData.noPreviousInsurance}
                        onChange={handleChange}
                    />
                }
                label="Keine Vorversicherung"
            />
        </div>
        {!formData.noPreviousInsurance && (
            <div className='grid grid-cols-2 gap-2'>
                <Tooltip title="Nennen Sie den Versicherer, bei dem der Kunde zuvor eine ähnliche Versicherung hatten.">
                    <TextField
                        type="text"
                        label="Vorversicherer"
                        name="previous_insurance"
                        value={formData.previous_insurance}
                        onChange={handleChange}
                        className="w-full p-2"
                        required
                    />
                </Tooltip>
                <Tooltip title="Geben Sie die Versicherungsnummer des Vorvertrags ein, falls vorhanden.">
                    <TextField
                        type="text"
                        label="Nr. Vorversicherer"
                        name="previous_insurance_number"
                        value={formData.previous_insurance_number}
                        onChange={handleChange}
                        className="w-full p-2"
                    />
                </Tooltip>
            </div>
        )}

        <Tooltip title="Falls bereits Schäden oder Ansprüche geltend gemacht wurden, die von einer Versicherung abgedeckt wurden, geben Sie sie hier an.">
            <FormControlLabel
                control={<Checkbox
                    name="previous_claims"
                    checked={formData.previous_claims}
                    onChange={handleChange} />}
                label="Vorschadensfrei"
                required={localStorage.getItem("is_admin") == "false"} />
        </Tooltip>
    </div>;
}