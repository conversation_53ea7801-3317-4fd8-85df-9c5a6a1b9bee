"use client";

import * as React from "react";
import {<PERSON>po<PERSON>, TextField, MenuItem, Box, CircularProgress, Tooltip, IconButton} from "@mui/material";
import {Order, Referral, tipStatuses} from "@/domain";
import {getOfferStatusLabel} from "@/common";
import {useCallback, useEffect, useState} from "react";
import httpClient from "@/utils/HttpClient";
import {useTranslations} from "next-intl";
import {ColumnKey} from "./domain";
import {ReferralsTable} from "@/components/ReferralsHistory/ReferralsTable";
import {ErrorMessage} from "@/components/ErrorMessage";
import DownloadIcon from "@mui/icons-material/Download";
import downloadFile from "@/utils/downloadFile";

type Props = {
  isAdmin?: boolean
}
export default function ReferralsHistory({ isAdmin }: Props) {
  const t = useTranslations('referralsHistory')
  const unscopedT = useTranslations()

  const [sortBy, setSortBy] = React.useState<ColumnKey>("createdAt");
  const [order, setOrder] = React.useState<Order>("desc");
  const [search, setSearch] = React.useState("");
  const [statusFilter, setStatusFilter] = React.useState<string>("");

  const [isLoading, setIsLoading] = React.useState(false)
  const [isError, setIsError] = React.useState(false)
  const [referrals, setReferrals] = useState<Referral[]>([])

  const createSearchParams = useCallback(() => {
    let value = `sortBy=${sortBy}&order=${order}`
    if (search) {
      value += `&search=${search}`
    }
    if (statusFilter) {
      value += `&status=${statusFilter}`
    }

    return value
  }, [order, sortBy, search, statusFilter])

  const loadReferrals = useCallback(async () => {
    setIsLoading(true)

    try {
      const response = await httpClient.request(`/api/referrer/referrals?${createSearchParams()}`)
      setReferrals(response.referrals)
    } catch (error) {
      console.error('Failed to fetch referrals', error)
      setIsError(true)
    } finally {
       setIsLoading(false)
    }
  }, [createSearchParams])

  useEffect(() => {
    loadReferrals()
  }, [loadReferrals]);

  const handleSort = (property: ColumnKey) => {
    setOrder((prev) => (sortBy === property && prev === "asc" ? "desc" : "asc"));
    setSortBy(property);
  };

  const downloadCsv = async () => {
    const timestamp = new Date().toISOString().slice(0, 10)
    const fileName = `referrals_${timestamp}.csv`

    try {
      await downloadFile(fileName, `/api/referrer/referrals/csv?${createSearchParams()}`)
    } catch (error) {
      console.error('Failed to download the referrals CSV:', error)
      alert(t('notification.downloadCsvFailed'))
    }
  }

  const resolveMainContent = () => {
    if (isError) {
      const retry = () => {
        setIsError(false)
        loadReferrals()
      }
      return (
          <Box sx={{ paddingTop: '100px' }}>
            <ErrorMessage text={t('loadingFailed')} onRetryClick={retry} />
          </Box>
      )
    }
    if (isLoading) {
      return (
          <Box sx={{ display: 'flex', justifyContent: 'center', paddingTop: '100px' }}>
            <CircularProgress />
          </Box>
      )
    }
    if (!referrals.length) {
      return (
          <Box sx={{ display: 'flex', justifyContent: 'center', paddingTop: '100px' }}>
            <Typography>
              {t('noReferrals')}
            </Typography>
          </Box>
      )
    }

    return (
        <ReferralsTable referrals={referrals} sortBy={sortBy} order={order} onHeadCellClick={handleSort} />
    )
  }

  return (
    <Box sx={{ minHeight: '100vh', width: '95vw' }}>
      <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2, gap: 2, flexWrap: "wrap" }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography variant="h4" sx={{ paddingBottom: '4px' }}>
            {t('title')}
          </Typography>
          {isAdmin && (
            <Tooltip title={t('downloadCsvTooltip')}>
              <IconButton
                  onClick={downloadCsv}
                  color="primary"
                  size="large"
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap", width: "100%", maxWidth: 900, justifyContent: "flex-end" }}>
          <TextField
            label={t('searchHint')}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            fullWidth
            sx={{ flex: "1 1 320px", minWidth: 260 }}
          />
          <TextField
            select
            label={t('statusFilter.hint')}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            sx={{ flex: "0 1 220px", minWidth: 200 }}
          >
            <MenuItem value="">{t('statusFilter.showAll')}</MenuItem>
            {tipStatuses.map(status => {
              return (
                  <MenuItem key={status} value={status}>
                    {getOfferStatusLabel(status, unscopedT)}
                  </MenuItem>
              )
            })}
          </TextField>
        </Box>
      </Box>

      {resolveMainContent()}
    </Box>
  );
}
