import * as React from "react";
import {
    Box, CircularProgress,
    IconButton, Stack,
    TableCell,
    TableRow,
    Tooltip
} from "@mui/material";
import {getOfferStatusChip} from "@/common";
import {RemainingDaysChip} from "@/components/RemainingDaysChip";
import {FC, useState} from "react";
import {Referral} from "@/domain";
import {useTranslations} from "next-intl";
import DownloadIcon from "@mui/icons-material/Download";
import SendOutlinedIcon from "@mui/icons-material/SendOutlined";
import httpClient from "@/utils/HttpClient";
import {formatDate} from "@/utils/format";
import downloadFile from "@/utils/downloadFile";

type Props = {
    referral: Referral
}
export const ReferralsTableRow: FC<Props> = ({ referral }) => {
    const t = useTranslations('referralsHistory')
    const unscopedT = useTranslations()

    const createdAt = formatDate(referral.createdAt)
    const extensions = referral.productExtensions?.join(', ') || '-'

    const statusChip = getOfferStatusChip(referral.status, unscopedT)
    const statusCellContent = referral.status === 'open' && referral.expiresAt ? (
        <Tooltip
            placement='top'
            title={<RemainingDaysChip dueDate={referral.expiresAt} />}
            slotProps={{
                popper: {
                    sx: { '.MuiTooltip-tooltip': { backgroundColor: 'transparent' } }
                }
            }}
        >
            {statusChip}
        </Tooltip>
    ) : statusChip

    const [isResending, setIsResending] = useState(false)

    const downloadAgreement  = async () => {
        const fileName = `${t('referrerAgreementFileName')}.pdf`

        try {
            await downloadFile(fileName, `/api/referrer/agreement?tipId=${referral.tipId}`)
        } catch (error) {
            console.error('Failed to download the referrer agreement:', error)
            alert(t('notification.downloadAgreementFailed'))
        }
    }
    const resendInvitation = async () => {
        setIsResending(true)

        try {
            await httpClient.request('/api/referrer/resend-offer', {
                method: 'POST',
                body: {
                    tipId: referral.tipId
                }
            })
            alert(t('notification.invitationResent'))
        } catch (error) {
            console.error('Failed to resend the invitation:', error)
            alert(t('notification.invitationResendingFailed'))
        } finally {
            setIsResending(false)
        }
    }

    return (
        <TableRow key={referral.id} hover>
            <TableCell>{createdAt}</TableCell>
            <TableCell>{referral.customerName}</TableCell>
            <TableCell>{statusCellContent}</TableCell>
            <TableCell>{referral.premium ?? '-'}</TableCell>
            <TableCell>{referral.commission ?? '-'}</TableCell>
            <TableCell>{referral.previousContractNumber || '-'}</TableCell>
            <TableCell>{referral.productName}</TableCell>
            <TableCell>
                <Box sx={{ whiteSpace: 'nowrap' }}>
                    {extensions}
                </Box>
            </TableCell>
            <TableCell align="right">
                <Stack direction='row' justifyContent='end'>
                    <Tooltip title="Tippgebervereinbarung herunterladen">
                        <IconButton color="primary" onClick={downloadAgreement}>
                            <DownloadIcon />
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={isResending ? "Wird gesendet..." : "Einladung erneut senden"}>
                        <IconButton color="secondary" onClick={resendInvitation} disabled={isResending}>
                            {isResending ? <CircularProgress size='24px' /> : <SendOutlinedIcon />}
                        </IconButton>
                    </Tooltip>
                </Stack>
            </TableCell>
        </TableRow>
    )
}