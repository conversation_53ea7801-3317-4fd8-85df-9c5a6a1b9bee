import * as React from "react";
import {
    Paper,
    TableBody,
    TableCell, TableContainer,
    TableHead,
    TableRow,
    TableSortLabel,
    Table
} from "@mui/material";
import {Order, Referral} from "@/domain";
import {FC} from "react";
import {ColumnKey} from "@/components/ReferralsHistory/domain";
import {ReferralsTableRow} from "@/components/ReferralsHistory/ReferralsTableRow";

const columns: { id: ColumnKey; label: string }[] = [
    { id: "createdAt", label: "Datum" },
    { id: "customerName", label: "Kunde" },
    { id: "status", label: "Status" },
    { id: "premium", label: "Prämie" },
    { id: "commission", label: "Vergütung" },
    { id: "previousContractNumber", label: "Vorherige Vertrag-Nr." },
    { id: "productName", label: "Produktname" },
    { id: "productExtensions", label: "Erweiterungen" },
];

type Props = {
    referrals: Referral[]
    sortBy: ColumnKey
    order: Order
    onHeadCellClick: (column: ColumnKey) => void
}
export const ReferralsTable: FC<Props> = ({ referrals, sortBy, order, onHeadCellClick }) => {
    return (
        <TableContainer component={Paper} sx={{ width: "100%", overflowX: "auto" }}>
            <Table>
                <TableHead>
                    <TableRow>
                        {columns.map((col) => (
                            <TableCell key={col.id}>
                                <TableSortLabel
                                    active={sortBy === col.id}
                                    direction={sortBy === col.id ? order : "asc"}
                                    onClick={() => onHeadCellClick(col.id)}
                                >
                                    {col.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="right">Aktionen</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {referrals.map((referral) => {
                        return <ReferralsTableRow key={referral.id} referral={referral} />
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    )
}