import { FormItem, FormState } from "@/inline-dependencies/FancyForm";
import { Subtitle } from "@/components/ReferralFlow/Subtitle";
import { FormCard as TFormCard } from "@/components/ReferralFlow/cards";

/**
 * -----------------------------
 *   Page 1: Bedarf
 * -----------------------------
 */
const needFields: FormItem[] = [
  {
    id: "buildingType",
    required: true,
    label: "insuranceForm.form.fieldLabel.buildingType",
    element: "dropdown",
    options: [
      { label: "Einfamilienhaus", value: "Einfamilienhaus" },
      { label: "Reihenhaus", value: "Reihenhaus" },
      { label: "Doppelhaushälfte", value: "Doppelhaushälfte" },
      { label: "Mehrfamilienhaus", value: "Mehrfamilienhaus" },
      { label: "Sonstiges", value: "Sonstiges" },
    ],
  },

  {
    id: "numUnits",
    element: "number",
    required: true,
    label: "insuranceForm.form.fieldLabel.numUnits",
  },
  {
    element: (
      <Subtitle
        key="subtitle.address"
        translation<PERSON>ey="insuranceForm.form.subtitle.propertyAddress"
      />
    ),
  },
  {
    id: "postalCode",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.postalCode",
  },
  {
    id: "city",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.city",
  },
  {
    id: "street",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.street",
  },
  {
    id: "houseNumber",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.houseNumber",
  },
  {
    id: "stormZone",
    required: true,
    label: "insuranceForm.form.fieldLabel.stormZone",
    element: "dropdown",
    options: [
      { label: "Zone 1", value: 1 },
      { label: "Zone 2", value: 2 },
    ],
  },
    {
    id: "waterZone",
    required: true,
    label: "insuranceForm.form.fieldLabel.waterZone",
    element: "dropdown",
    options: [
      { label: "Zone 1", value: 1 },
      { label: "Zone 2", value: 2 },
      { label: "Zone 3", value: 3 },
      { label: "Zone 4", value: 4 },
    ],
  },
    {
    id: "zuersZone",
    required: true,
    label: "insuranceForm.form.fieldLabel.zuersZone",
    element: "dropdown",
    options: [
      { label: "Zone 1", value: 1 },
      { label: "Zone 2", value: 2 },
      { label: "Zone 3", value: 3 },
      { label: "Zone 4", value: 4 },
    ],
  },
];

/**
 * -----------------------------
 *   Page 2: Gebäudedaten
 * -----------------------------
 */
const buildingDataFields: FormItem[] = [
  {
    id: "constructionYear",
    element: "number",
    required: true,
    label: "insuranceForm.form.fieldLabel.constructionYear",
  },
  {
    id: "newBuilding",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.newBuilding",
  },
  {
    element: (
      <Subtitle
        key="subtitle.renovations"
        translationKey="insuranceForm.form.subtitle.renovations"
      />
    ),
  },
  {
    id: "waterPipes",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.waterPipes",
  },
  {
    id: "heating",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.heating",
  },
  {
    id: "roof",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.roof",
  },
  {
    id: "electricity",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.electricity",
  },
  {
    element: (
      <Subtitle
        key="subtitle.riskLocation"
        translationKey="insuranceForm.form.subtitle.riskLocation"
      />
    ),
  },
  {
    element: "boolean-radio-group",
    id: "partialUse",
    label: "insuranceForm.form.fieldLabel.partialUse",
    required: true,
  },
  {
    element: "boolean-radio-group",
    id: "nearbyBusiness",
    label: "insuranceForm.form.fieldLabel.nearbyBusiness",
    required: true,
  },
  {
    id: "businessType",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.businessType",
  },
  {
    element: "boolean-radio-group",
    id: "nearbyForests",
    label: "insuranceForm.form.fieldLabel.nearbyForests",
    required: true,
  },
  {
    element: "boolean-radio-group",
    id: "readyForOccupancy",
    label: "insuranceForm.form.fieldLabel.readyForOccupancy",
    required: true,
  },
  {
    element: "boolean-radio-group",
    id: "permanentOccupancy",
    label: "insuranceForm.form.fieldLabel.permanentOccupancy",
    required: true,
  },
  {
    element: "boolean-radio-group",
    id: "vacancy",
    label: "insuranceForm.form.fieldLabel.vacancy",
    required: true,
  },
  {
    id: "vacancyPercent",
    element: "number",
    required: true,
    label: "insuranceForm.form.fieldLabel.vacancyPercent",
  },
  {
    element: (
      <Subtitle
        key="subtitle.damageDetails"
        translationKey="insuranceForm.form.subtitle.previousClaims"
      />
    ),
  },
  {
    element: "boolean-radio-group",
    id: "prevDamageFree",
    label: "insuranceForm.form.fieldLabel.prevDamageFree",
    required: true,
  },
  {
    id: "prevDamageCount",
    required: true,
    label: "insuranceForm.form.fieldLabel.prevDamageCount",
    element: "dropdown",
    options: [
      { label: "Ein Schaden", value: "Ein Schaden" },
      { label: "Zwei Schäden", value: "Zwei Schäden" },
      { label: "Drei Schäden", value: "Drei Schäden" },
      { label: "Mehrfamilienhaus", value: "Mehrfamilienhaus" },
      { label: "Mehr als drei Schäden", value: "Mehr als drei Schäden" },
    ],
  },
  {
    element: (
      <Subtitle
        key="subtitle.damageDetails"
        translationKey="insuranceForm.form.subtitle.damageDetails"
      />
    ),
  },
  {
    id: "damageCause",
    required: true,
    label: "insuranceForm.form.fieldLabel.damageCause",
    element: "dropdown",
    options: [
      {
        label: "Brand, Blitzschlag, Explosion, Implosion",
        value: "Brand, Blitzschlag, Explosion, Implosion",
      },
      { label: "Leitungswasser", value: "Leitungswassern" },
      { label: "Sturm/Hagel", value: "Sturm/Hagel" },
      { label: "Sonstiges", value: "Sonstiges" },
    ],
  },
  {
    id: "damageYear",
    required: true,
    label: "insuranceForm.form.fieldLabel.damageYear",
    element: "dropdown",
    options: [
      { label: "2025", value: "2025" },
      { label: "2024", value: "2024" },
      { label: "2023", value: "2023" },
      { label: "2022", value: "2022" },
      { label: "2021", value: "2021" },
      { label: "2020", value: "2020" },
    ],
  },
  {
    id: "damageAmount",
    required: true,
    label: "insuranceForm.form.fieldLabel.damageAmount",
    element: "dropdown",
    options: [
      { label: "bis 3.000 €", value: "bis 3.000 €" },
      { label: "über 3.000 €", value: "über 3.000 €" },
    ],
  },
];

/**
 * -----------------------------
 *   Page 3: Bauweise und Anlagen
 * -----------------------------
 */
const constructionFields: FormItem[] = [
  {
    element: (
      <Subtitle
        key="subtitle.construction"
        translationKey="insuranceForm.form.subtitle.construction"
      />
    ),
  },
  {
    id: "houseConstruction",
    required: true,
    label: "insuranceForm.form.fieldLabel.houseConstruction",
    element: "dropdown",
    options: [
      { label: "Massiv", value: "Massiv" },
      {
        label: "Stahl-, Stahlbeton-, Holzkonstruktion",
        value: "Stahl-, Stahlbeton-, Holzkonstruktion",
      },
      { label: "Fachwerk", value: "Fachwerk" },
    ],
  },
  {
    element: "boolean-radio-group",
    id: "softRoof",
    label: "insuranceForm.form.fieldLabel.softRoof",
    required: true,
  },
  {
    element: (
      <Subtitle
        key="subtitle.floors"
        translationKey="insuranceForm.form.subtitle.floors"
      />
    ),
  },
  {
    id: "roofType",
    required: true,
    label: "insuranceForm.form.fieldLabel.roofType",
    element: "dropdown",
    options: [
      { label: "Schrägdach", value: "Schrägdach" },
      { label: "Flachdach", value: "Flachdach" },
    ],
  },
  {
    element: "boolean-radio-group",
    id: "atticFinished",
    label: "insuranceForm.form.fieldLabel.atticFinished",
    required: true,
  },
  {
    id: "numFloors",
    required: true,
    label: "insuranceForm.form.fieldLabel.numFloors",
    element: "number",
  },
  {
    element: "boolean-radio-group",
    id: "hasBasement",
    label: "insuranceForm.form.fieldLabel.hasBasement",
    required: true,
  },
  {
    element: (
      <Subtitle
        key="subtitle.livingArea"
        translationKey="insuranceForm.form.subtitle.livingArea"
      />
    ),
  },
  {
    id: "livingAreaMain",
    element: "number",
    required: true,
    label: "insuranceForm.form.fieldLabel.livingAreaMain",
  },
  {
    // TODO: show if hasBasement=true (?)
    id: "livingAreaBasement",
    element: "number",
    label: "insuranceForm.form.fieldLabel.livingAreaBasement",
    tooltip: "insuranceForm.form.tooltip.livingAreaBasement",
  },
  {
    id: "garageParkingSpacesNumber",
    element: "number",
    label: "insuranceForm.form.fieldLabel.garageParkingSpacesNumber",
  },
  {
    id: "carportParkingSpacesNumber",
    element: "number",
    label: "insuranceForm.form.fieldLabel.carportParkingSpacesNumber",
  },
  {
    id: "undergroundParkingSpacesNumber",
    element: "number",
    label: "insuranceForm.form.fieldLabel.undergroundParkingSpacesNumber",
  },
  {
    element: (
        <Subtitle
            key="subtitle.specialFeatures"
            translationKey="insuranceForm.form.subtitle.specialFeatures"
        />
    ),
  },
  {
    id: "specialFeatures",
    element: "text",
    tooltip: "insuranceForm.form.tooltip.specialFeaturesList",
    label: "insuranceForm.form.fieldLabel.specialFeatures.list",
  },
  {
    id: "specialFeaturesRebuildingPrice",
    element: "price",
    label: "insuranceForm.form.fieldLabel.specialFeatures.rebuildingPrice",
  },
  {
    element: (
        <Subtitle
            key="subtitle.outbuilding"
            translationKey="insuranceForm.form.subtitle.outbuilding"
        />
    ),
  },
  {
    id: "outbuildingHowUsed",
    element: "text",
    label: "insuranceForm.form.fieldLabel.outbuilding.howUsed",
  },
  {
    id: "outbuildingSquareFootage",
    element: "number",
    label: "insuranceForm.form.fieldLabel.outbuilding.squareFootage",
  },
  {
    id: "outbuildingWallType",
    element: "text",
    label: "insuranceForm.form.fieldLabel.outbuilding.wallType",
  },
  {
    id: "outbuildingRoofType",
    element: "text",
    label: "insuranceForm.form.fieldLabel.outbuilding.roofType",
  },
  {
    id: "outbuildingRebuildingPrice",
    element: "price",
    label: "insuranceForm.form.fieldLabel.outbuilding.rebuildingPrice",
  },
  {
    element: (
      <Subtitle
        key="subtitle.renewableEnergy"
        translationKey="insuranceForm.form.subtitle.renewableEnergy"
      />
    ),
  },
  {
    id: "renewableEnergyInstallations",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.renewableEnergyInstallations",
  },
  {
    id: "maxPowerKWp",
    element: "number",
    required: true,
    label: "insuranceForm.form.fieldLabel.maxPowerKWp",
  },
  {
    id: "solarThermie",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.solarThermie",
  },
  {
    id: "geothermie",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.geothermie",
  },
  {
    id: "heatPumpAC",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.heatPumpAC",
  },
  {
    element: (
      <Subtitle
        key="subtitle.buildingFeatures"
        translationKey="insuranceForm.form.subtitle.buildingFeatures"
      />
    ),
  },
  {
    id: "naturalStoneCopperRoof",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.naturalStoneCopperRoof",
  },
  {
    id: "stoneCeramicCladding",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.stoneCeramicCladding",
  },
  {
    id: "stuccoWood",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.stuccoWood",
  },
  {
    id: "premiumFlooring",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.premiumFlooring",
  },
  {
    id: "metalWoodWindows",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.metalWoodWindows",
  },
  {
    id: "premiumDoors",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.premiumDoors",
  },
  {
    id: "premiumSanitary",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.premiumSanitary",
  },
  {
    id: "heatingRenewableSystems",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.heatingRenewableSystems",
  },
  {
    id: "premiumKitchen",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.premiumKitchen",
  },
];

/**
 * -----------------------------
 *   Page 4: Angebot
 * -----------------------------
 */
const offerFields: FormItem[] = [
  {
    element: (
      <Subtitle
        key="subtitle.deductible"
        translationKey="insuranceForm.form.subtitle.deductible"
      />
    ),
  },
  {
    id: "deductible",
    required: true,
    label: "insuranceForm.form.fieldLabel.deductible",
    element: "dropdown",
    options: [
      { label: "keine", value: "keine" },
      { label: "150 €", value: "150 €" },
    ],
  },
  {
    element: (
      <Subtitle
        key="subtitle.coverage"
        translationKey="insuranceForm.form.subtitle.coverage"
      />
    ),
  },
  {
    id: "coverageFire",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.coverageFire",
  },
  {
    id: "coverageWater",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.coverageWater",
  },
  {
    id: "coverageStorm",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.coverageStorm",
  },
  {
    id: "coverageBestAdvice",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.coverageBestAdvice",
  },
  {
    id: "paymentInterval",
    required: true,
    label: "insuranceForm.form.fieldLabel.paymentInterval",
    element: "dropdown",
    options: [
      { label: "jährlich", value: "jährlich" },
      { label: "halbjährlich", value: "halbjährlich" },
      { label: "vierteljährlich", value: "vierteljährlich" },
    ],
  },
  {
    element: (
      <Subtitle
        key="subtitle.additionalOptions"
        translationKey="insuranceForm.form.subtitle.additionalOptions"
      />
    ),
  },
  {
    id: "glassInsurance",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.glassInsurance",
  },
  {
    id: "unnamedRisks",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.unnamedRisks",
  },
  {
    id: "extendedPipeCoverage",
    element: "checkbox",
    label: "insuranceForm.form.fieldLabel.extendedPipeCoverage",
  },
  {
    element: (
      <Subtitle
        key="subtitle.policyholder"
        translationKey="insuranceForm.form.subtitle.policyholder"
      />
    ),
  },
  {
    id: "salutation",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.salutation",
  },
  {
    id: "firstName",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.firstName",
  },
  {
    id: "lastName",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.lastName",
  },
  {
    id: "postalCode",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.postalCode",
  },
  {
    id: "city",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.city",
  },
  {
    id: "street",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.street",
  },
  {
    id: "street",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.street",
  },
  {
    id: "houseNumber",
    element: "text",
    required: true,
    label: "insuranceForm.form.fieldLabel.houseNumber",
  },
  {
    element: (
      <Subtitle
        key="subtitle.policyStart"
        translationKey="insuranceForm.form.subtitle.policyStart"
      />
    ),
  },
  {
    id: "policyStartDate",
    element: "date",
    required: true,
    label: "insuranceForm.form.fieldLabel.policyStartDate",
  },
];

/**
 * Cards on Page
 */
export const newResidentialBuildingCards: TFormCard[] = [
  {
    id: "need",
    title: "insuranceForm.form.title.need",
    items: needFields,
  },
  {
    id: "buildingData",
    title: "insuranceForm.form.title.buildingData",
    items: buildingDataFields,
  },
  {
    id: "basicData",
    title: "insuranceForm.form.title.construction",
    items: constructionFields,
  },
  {
    id: "offer",
    title: "insuranceForm.form.title.offer",
    items: offerFields,
  },
];
