import { useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Step,
  StepButton,
  Button,
} from "@mui/material";
import { useTranslations } from "next-intl";
import { Form } from "@/components/InsuranceForm/Form";
import { newResidentialBuildingCards } from "@/components/InsuranceForm/WohngebaeudeversicherungFormCards";
import { FormState } from "@/inline-dependencies/FancyForm";
import { InsuranceInfoCard } from "@/components/InsuranceForm/WohngebaeudeversicherungInfoCards";
import { calculatePremium } from "@/utils/calculatePremium";

// TODO: use config.defaultValue once FancyForm supports it
const initialState = {
  basicData: {
    numFloors: 0,
    garageParkingSpacesNumber: 0,
    carportParkingSpacesNumber: 0,
    undergroundParkingSpacesNumber: 0,
    livingAreaBasement: 0,
  }
}

export const NewResidentialBuildingForm = () => {
  const t = useTranslations();
  const [cardStates, setCardStates] = useState<Record<string, FormState>>(initialState);
  const [activeStep, setActiveStep] = useState(2);
  const [premiumResult, setPremiumResult] = useState<null | {
    total: number;
    details: any;
  }>(null);

  const steps = newResidentialBuildingCards.map((c) => t(c.title));

  const handleCardChange = (cardId: string, patch: Partial<FormState>) => {
    setCardStates((prev) => ({
      ...prev,
      [cardId]: { ...prev[cardId], ...patch },
    }));
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) setActiveStep((s) => s + 1);
    else {
      alert("Formular abgeschickt! Daten siehe Konsole.");
    }
  };

  const handleBack = () => setActiveStep((s) => Math.max(0, s - 1));
  const currentCard = newResidentialBuildingCards[activeStep];

  const handleCalculate = () => {
    const allData = Object.values(cardStates).reduce(
      (acc, s) => ({ ...acc, ...s.values }),
      {}
    );
    const result = calculatePremium(allData as any);
    setPremiumResult({ total: result.totalPremium, details: result.details });
  };

  return (
    <Box p={{ xs: 2, md: 4 }}>
      <Typography variant="h4" textAlign="center" mb={3}>
        {t("insuranceForm.form.mainTitle")}
      </Typography>

      <Stepper
        nonLinear
        activeStep={activeStep}
        alternativeLabel
        sx={{ mb: 5 }}
      >
        {steps.map((label, index) => (
          <Step key={label}>
            <StepButton color="inherit" onClick={() => setActiveStep(index)}>
              {label}
            </StepButton>
          </Step>
        ))}
      </Stepper>

      <Box display="flex" flexDirection={{ xs: "column", md: "row" }} gap={3}>
        <Box flex={2}>
          <Form
            cards={[currentCard]}
            cardStates={cardStates}
            onChange={handleCardChange}
            onButtonClick={handleNext}
          />
          <Box display="flex" justifyContent="space-between" mt={3} mx={10}>
            <Button
              variant="outlined"
              disabled={activeStep === 0}
              onClick={handleBack}
            >
              {t("insuranceForm.form.actions.back")}
            </Button>
            <Button variant="contained" onClick={handleNext}>
              {activeStep === steps.length - 1
                ? t("insuranceForm.form.actions.submit")
                : t("insuranceForm.form.actions.next")}
            </Button>
          </Box>
        </Box>
        <Box flex={1} flexDirection={"row"}>
          <InsuranceInfoCard stepId={currentCard.id} />
                  {activeStep === steps.length - 1 && (
          <Box mt={4}>
            <Button variant="contained" onClick={handleCalculate}>
              Prämie berechnen
            </Button>

            {premiumResult && (
              <Box mt={3} p={2} border="1px solid #ccc" borderRadius={2}>
                <Typography variant="h6">Berechnete Nettoprämie:</Typography>
                <Typography variant="h5" color="primary">
                  {premiumResult.total.toFixed(2)} €
                </Typography>
              </Box>
            )}
          </Box>
        )}
        </Box>

      </Box>
    </Box>
  );
};
