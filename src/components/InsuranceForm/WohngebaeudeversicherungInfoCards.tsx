"use client";

import { Card, CardContent, Typography, Stack } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import LockIcon from "@mui/icons-material/Lock";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import { useTranslations } from "next-intl";

type InsuranceInfoCardProps = {
  stepId: string;
};

export const InsuranceInfoCard = ({ stepId }: InsuranceInfoCardProps) => {
  const t = useTranslations();

  const stepDisclaimer = t(
    `insuranceForm.infoCard.steps.${stepId}.disclaimer`,
    {
      default: t("insuranceForm.infoCard.disclaimer"),
    }
  );

  const stepTipsTitle = t(`insuranceForm.infoCard.steps.${stepId}.tipsTitle`, {
    default: t("insuranceForm.infoCard.tipsTitle"),
  });

  const rawTips = t.raw(`insuranceForm.infoCard.steps.${stepId}.tips`);
  const stepTips = Array.isArray(rawTips) ? rawTips : [];

  return (
    <Card
      sx={{ borderRadius: 3, boxShadow: "0 4px 12px rgba(0,0,0,0.08)", p: 2 }}
    >
      <CardContent>
        <Stack spacing={2}>
          {/* Header */}
          <Stack direction="row" spacing={1} alignItems="center">
            <InfoIcon color="primary" />
            <Typography variant="h6">
              {stepTipsTitle}
            </Typography>
          </Stack>

          {/* Disclaimer */}
          <Stack direction="row" spacing={1} alignItems="center">
            <TipsAndUpdatesIcon color="primary" fontSize="small" />
            <Typography variant="body2" color="text.secondary">
              {stepDisclaimer}
            </Typography>
          </Stack>

          {/* Tipps */}

          {stepTips.map((tip, index) => (
            <Stack key={index} spacing={0.5} sx={{ pl: 1 }}>
              <Typography
                variant="body1"
                fontWeight="bold"
                color="primary.main"
              >
                {tip.title}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                dangerouslySetInnerHTML={{ __html: tip.text }}
              />
            </Stack>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
};
