import { Box, Button, TextField, Typography } from "@mui/material";
import { AdditionalAreas, SpecialFeature, Outbuilding } from "@/utils/insurance1914";
import { useState } from "react";

type Props = {
  additional: AdditionalAreas | null;
  setAdditional: (v: AdditionalAreas) => void;
  special: SpecialFeature[];
  setSpecial: (v: SpecialFeature[]) => void;
  outbuildings: Outbuilding[];
  setOutbuildings: (v: Outbuilding[]) => void;
  onBack: () => void;
  onFinish: () => void;
};

export default function Step3({
  additional,
  setAdditional,
  special,
  setSpecial,
  outbuildings,
  setOutbuildings,
  onBack,
  onFinish,
}: Props) {
  const [livingArea, setLivingArea] = useState(additional?.livingArea ?? 0);
  const [basement, setBasement] = useState(additional?.basementLiving ?? 0);
  const [garage, setGarage] = useState(additional?.garageSpaces ?? 0);
  const [carport, setCarport] = useState(additional?.carportSpaces ?? 0);
  const [underground, setUnderground] = useState(additional?.undergroundSpaces ?? 0);

  const handleNext = () => {
    setAdditional({
      livingArea: livingArea,
      basementLiving: basement,
      garageSpaces: garage,
      carportSpaces: carport,
      undergroundSpaces: underground,
    });
    onFinish();
  };

  return (
    <Box>
      <Typography variant="h6">3. Schritt: Flächen & Extras</Typography>
      <TextField
        label="Wohnfläche (m²)"
        type="number"
        value={livingArea}
        onChange={(e) => setLivingArea(Number(e.target.value))}
        fullWidth
        margin="normal"
      />
      <TextField
        label="Kellergeschoss, sofern zu Wohn- o. Hobbyzwecken genutzt (m²)"
        type="number"
        value={basement}
        onChange={(e) => setBasement(Number(e.target.value))}
        fullWidth
        margin="normal"
      />
      <TextField
        label="Garage, (Anzahl der Stellplätze)"
        type="number"
        value={garage}
        onChange={(e) => setGarage(Number(e.target.value))}
        fullWidth
        margin="normal"
      />
      <TextField
        label="Carport, (Anzahl der Stellplätze)"
        type="number"
        value={carport}
        onChange={(e) => setCarport(Number(e.target.value))}
        fullWidth
        margin="normal"
      />
      <TextField
        label="Tiefgaragenstellplätze, Anzahl"
        type="number"
        value={underground}
        onChange={(e) => setUnderground(Number(e.target.value))}
        fullWidth
        margin="normal"
      />

      {/* TODO: Add repeatable forms for SpecialFeature and Outbuilding arrays */}

      <Box mt={2}>
        <Button onClick={onBack}>Zurück</Button>
        <Button variant="contained" onClick={handleNext} sx={{ ml: 2 }}>
          Berechnen
        </Button>
      </Box>
    </Box>
  );
}
