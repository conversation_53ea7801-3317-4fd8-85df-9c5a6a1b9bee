"use client";

import { useState } from "react";
import { Box, Typography, IconButton, Snackbar, Stack, Button } from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ReplayIcon from "@mui/icons-material/Replay";
import { formatEuro } from "@/utils/format";
import { copyToClipboard } from "@/utils/clipboard";

type Props = {
  versicherungssumme1914: number;
  buildingValue2025: number;
  onReset?: () => void;
};

export default function Step4({
  versicherungssumme1914,
  buildingValue2025,
  onReset,
}: Props) {
  const [snackbarMsg, setSnackbarMsg] = useState("");

  const handleCopy = async (label: string, value: number) => {
    const text = formatEuro(value);
    const ok = await copyToClipboard(text);
    if (ok) setSnackbarMsg(`${label} kopiert: ${text}`);
  };

  return (
    <>
      <Box sx={{ mt: 4 }}>
        <Typography variant="h4" mb={3}>
          Ergebnis der Berechnung
        </Typography>

        <Stack direction="row" spacing={1} alignItems="center" mb={2}>
          <Typography variant="h6">
            Versicherungssumme 1914: {formatEuro(versicherungssumme1914)} Mark
          </Typography>
          <IconButton
            aria-label="Versicherungssumme 1914 kopieren"
            onClick={() =>
              handleCopy("Versicherungssumme 1914", versicherungssumme1914)
            }
            size="small"
          >
            <ContentCopyIcon fontSize="small" />
          </IconButton>
        </Stack>

        <Stack direction="row" spacing={1} alignItems="center" mb={4}>
          <Typography variant="h6">
            Gebäudewert 2025: {formatEuro(buildingValue2025)} €
          </Typography>
          <IconButton
            aria-label="Gebäudewert 2025 kopieren"
            onClick={() =>
              handleCopy("Gebäudewert 2025", buildingValue2025)
            }
            size="small"
          >
            <ContentCopyIcon fontSize="small" />
          </IconButton>
        </Stack>

        {onReset && (
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ReplayIcon />}
            onClick={onReset}
          >
            Erneut eingeben
          </Button>
        )}
      </Box>

      <Snackbar
        open={!!snackbarMsg}
        autoHideDuration={2500}
        message={snackbarMsg}
        onClose={() => setSnackbarMsg("")}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      />
    </>
  );
}
