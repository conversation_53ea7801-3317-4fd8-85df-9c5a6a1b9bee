import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  Typo<PERSON>,
} from "@mui/material";

type Props = {
  value: Record<string, number>;
  onChange: (v: Record<string, number>) => void;
  onNext: () => void;
  onBack: () => void;
};

const SURCHARGE_OPTIONS = [
  { id: "1", label: "Naturstein, Kupferdach", factor: 4 },
  { id: "2", label: "Naturstein-, Keramik-, Kunststeinverkleidung, Handstrichklinker", factor: 5,},
  { id: "3", label: "Stuckarbeiten, Edelholzverkleidung", factor: 6 },
  { id: "4", label: "Natursteinböden, Parkett- oder Teppichböden in hochwertiger Qualität", factor: 4 },
  { id: "5", label: "Leichtmetall- oder Holzsprossenfenster", factor: 4 },
  { id: "6", label: "Edelholztüren", factor: 3 },
  { id: "7", label: "hochwertige Sanitäreinrichtungen", factor: 6 },
  { id: "8", label: "<PERSON><PERSON><PERSON><PERSON>ump<PERSON>, Solaranlagen, Fußboden- und Deckenheizungen", factor: 6 },
  { id: "9", label: "hochwertige Einbauküchen", factor: 4 },
];

export default function Step2({ value, onChange, onNext, onBack }: Props) {
  const toggle = (id: string, factor: number) => {
    const newVal = { ...value };
    if (newVal[id]) delete newVal[id];
    else newVal[id] = factor;
    onChange(newVal);
  };

  const total = Object.values(value).reduce((a, b) => a + b, 0);

  return (
    <Box>
      <Typography variant="h6">
        2. Schritt: Zuschläge für gehobene Bauausführungen-/ausstattungen des Gebäudes
      </Typography>
      {SURCHARGE_OPTIONS.map((o) => (
        <FormControlLabel
          key={o.id}
          control={
            <Checkbox
              checked={!!value[o.id]}
              onChange={() => toggle(o.id, o.factor)}
            />
          }
          label={`${o.label} (+${o.factor})`}
        />
      ))}
      <Typography mt={2}>Summe der Zuschläge Mark 1914: {total} M</Typography>
      <Box mt={2}>
        <Button onClick={onBack}>Zurück</Button>
        <Button variant="contained" onClick={onNext} sx={{ ml: 2 }}>
          Weiter
        </Button>
      </Box>
    </Box>
  );
}
