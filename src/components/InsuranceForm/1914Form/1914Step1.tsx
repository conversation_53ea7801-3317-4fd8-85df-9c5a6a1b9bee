import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, MenuItem, <PERSON><PERSON>ield, Typo<PERSON> } from "@mui/material";
import { Building } from "@/utils/insurance1914";

type Props = {
  value: Building | null;
  onChange: (v: Building) => void;
  onNext: () => void;
};

const floorLists: Record<string, { label: string; factor: number }[]> = {
  // Flachdach
  flat_true: [
    { label: "1-2 Geschosse", factor: 190 },
    { label: "3-4 Geschosse", factor: 150 },
    { label: "5 Geschosse", factor: 135 },
    { label: "6-7 Geschosse", factor: 130 },
  ],
  flat_false: [
    { label: "1 Geschoss", factor: 160 },
    { label: "2 Geschosse", factor: 160 },
    { label: "3-7 Geschosse", factor: 135 },
  ],

  // Dachgeschoss nicht ausgebaut
  attic_plain_true: [
    { label: "1 Geschoss", factor: 190 },
    { label: "2 Geschosse", factor: 165 },
    { label: "3-4 Geschosse", factor: 150 },
    { label: "5-7 Geschosse", factor: 130 },
  ],
  attic_plain_false: [
    { label: "1 Geschoss", factor: 160 },
    { label: "2 Geschosse", factor: 140 },
    { label: "3-7 Geschosse", factor: 135 },
  ],

  // Dachgeschoss ausgebaut
  attic_built_true: [
    { label: "1 Geschoss", factor: 165 },
    { label: "2 Geschosse", factor: 150 },
    { label: "3 Geschosse", factor: 140 },
    { label: "4 Geschosse", factor: 135 },
    { label: "5-7 Geschosse", factor: 130 },
  ],
  attic_built_false: [
    { label: "1 Geschoss", factor: 140 },
    { label: "2 Geschosse", factor: 130 },
    { label: "3-7 Geschosse", factor: 125 },
  ],
};

export default function Step1({ value, onChange, onNext }: Props) {
  const [roof, setRoof] = useState(value?.roofType || "");
  const [basement, setBasement] = useState(value?.hasBasement ? "yes" : "");
  const [floorFactor, setFloorFactor] = useState<number | null>(
    value?.floorsFactor ?? null
  );

  const handleNext = () => {
    if (roof && basement && floorFactor != null) {
      onChange({
        roofType: roof,
        hasBasement: basement === "yes",
        floorsFactor: floorFactor,
      });
      onNext();
    }
  };

  const floorsKey = roof && basement ? `${roof}_${basement === "yes"}` : "";
  const floorOptions = floorsKey ? floorLists[floorsKey] : [];

  return (
    <Box>
      <Typography variant="h6">1. Schritt: Gebäudetyp ermitteln</Typography>
      <TextField
        select
        label="Dach"
        value={roof}
        onChange={(e) => setRoof(e.target.value)}
        fullWidth
        margin="normal"
      >
        <MenuItem value="flat">Flachdach</MenuItem>
        <MenuItem value="attic_plain">Dachgeschoss nicht ausgebaut</MenuItem>
        <MenuItem value="attic_built">Dachgeschoss ausgebaut</MenuItem>
      </TextField>

      {roof && (
        <TextField
          select
          label="Kellergeschoss"
          value={basement}
          onChange={(e) => setBasement(e.target.value)}
          fullWidth
          margin="normal"
        >
          <MenuItem value="yes">Ja</MenuItem>
          <MenuItem value="no">Nein</MenuItem>
        </TextField>
      )}

      {roof && basement && (
        <TextField
          select
          label="Anzahl der Geschosse"
          value={floorFactor ?? ""}
          onChange={(e) => setFloorFactor(Number(e.target.value))}
          fullWidth
          margin="normal"
        >
          {floorOptions.map((o) => (
            <MenuItem key={o.label} value={o.factor}>
              {o.label}
            </MenuItem>
          ))}
        </TextField>
      )}

      {floorFactor != null && (
        <Typography mt={2}>Wert in Mark 1914: {floorFactor} M</Typography>
      )}

      <Button
        variant="contained"
        onClick={handleNext}
        disabled={floorFactor == null}
        sx={{ mt: 2 }}
      >
        Weiter
      </Button>
    </Box>
  );
}
