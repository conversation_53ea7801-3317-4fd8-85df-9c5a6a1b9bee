import { useState } from "react";
import { Card, CardContent } from "@mui/material";
import Step1 from "./1914Step1";
import Step2 from "./1914Step2";
import Step3 from "./1914Step3";
import Step4 from "./1914Step4";
import {
  calculateInsurance1914,
  BuildingSelection,
  SurchargeSelection,
  AdditionalAreas,
  SpecialFeature,
  Outbuilding,
} from "@/utils/insurance1914";

export const Insurance1914Form = () => {
  const [step, setStep] = useState(1);

  const [building, setBuilding] = useState<BuildingSelection | null>(null);
  const [surcharges, setSurcharges] = useState<Record<string, number>>({});
  const [additional, setAdditional] = useState<AdditionalAreas | null>(null);
  const [special, setSpecial] = useState<SpecialFeature[]>([]);
  const [outbuildings, setOutbuildings] = useState<Outbuilding[]>([]);

  const [results, setResults] = useState<any>(null);

  const next = () => setStep((s) => s + 1);
  const back = () => setStep((s) => s - 1);

  const handleFinish = () => {
    if (!building || !additional) return;
    const res = calculateInsurance1914(
      building,
      surcharges,
      additional,
      special,
      outbuildings
    );
    setResults(res);
    setStep(4);
  };

  return (
    <Card sx={{ borderRadius: 3, boxShadow: "0 4px 12px rgba(0,0,0,0.08)" }}>
      <CardContent>
        {step === 1 && (
          <Step1 value={building} onChange={setBuilding} onNext={next} />
        )}
        {step === 2 && (
          <Step2
            value={surcharges}
            onChange={setSurcharges}
            onNext={next}
            onBack={back}
          />
        )}
        {step === 3 && (
          <Step3
            additional={additional}
            setAdditional={setAdditional}
            special={special}
            setSpecial={setSpecial}
            outbuildings={outbuildings}
            setOutbuildings={setOutbuildings}
            onBack={back}
            onFinish={handleFinish}
          />
        )}
        {step === 4 && results && (
          <Step4
            versicherungssumme1914={results.versicherungssumme1914}
            buildingValue2025={results.buildingValue2025}
          />
        )}
      </CardContent>
    </Card>
  );
};
