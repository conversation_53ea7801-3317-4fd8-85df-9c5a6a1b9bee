import {<PERSON><PERSON>, <PERSON>ack, Typography} from "@mui/material";
import {FancyForm, FancyFormProps, FormItem, FormState} from "@/inline-dependencies/FancyForm";
import {FC, ReactNode} from "react";
import {FormCard as TFormCard, formCards} from "@/components/ReferralFlow/cards";
import {FormCard} from "@/components/ReferralFlow/FormCard";

type Props = {
    cards: TFormCard[]
    cardStates: Record<string, FormState>
    onChange: (pageId: string, patch: Partial<FormState>) => void
    onButtonClick: () => void
}
export const Form: FC<Props> = ({ cards, cardStates, onChange, onButtonClick}) => {

    return (
        <Stack sx={{ maxWidth: '800px', margin: '0 auto', gap: 5 }}>
            {cards.map(card => {
                if (card.displayIf && !card.displayIf(cardStates)) {
                    return null
                }
                return <FormCard
                    key={card.id}
                    state={cardStates[card.id] ?? {}}
                    onChange={patch => onChange(card.id, patch)}
                    {...card}
                />
            })}
        </Stack>
    );
}
