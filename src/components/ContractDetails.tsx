// src/components/ContractDetails.tsx
import {
  AutoFixHigh as AutoFixHighIcon,
  Autorenew as AutorenewIcon,
  Download as DownloadIcon,
  FolderOpen as FolderOpenIcon,
  GppMaybe as GppMaybeIcon,
  Send as SendIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  type SelectChangeEvent,
  Tooltip,
  Typography,
} from '@mui/material';
import router from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useRef, useState } from 'react';

import {
  type Contract,
  type Customer,
  type Invoice,
} from '@/generated/prisma-postgres';
import { type JsonObject } from '@/generated/prisma-postgres/runtime/library';
import {
  type AttachmentData,
  type ContractStatusData,
  type ReportData,
} from '@/types';
import { apiFetch } from '@/utils/apiFetch';
import { formatLabel } from '@/utils/keyFormatter';

import AgentInformationBox from './box/AgentInformationBox';
import ContractInformationBox from './box/ContractInformationBox';
import CustomerInformationBox from './box/CustomerInformationBox';
import FilesBox from './box/FilesBox';
import InvoicesBox from './box/InvoicesBox';
import OriginalFilesBox from './box/OriginalFilesBox';
import ReportsBox from './box/ReportsBox';
import StaticBox from './box/StaticBox';
import StatusBox from './box/StatusBox';
import RevisionsModal from './modal/RevisionsModal';

interface ContractDetailsProps {
  contractNumber: string;
}

const ContractDetails = ({ contractNumber }: ContractDetailsProps) => {
  const [contract, setContract] = useState<Contract | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [reports, setReports] = useState<ReportData[] | null>(null);
  const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);
  const [originalAttachments, setOriginalAttachments] = useState<
    AttachmentData[] | null
  >(null);
  const [invoicesData, setInvoicesData] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const session = useSession();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const contractData = await apiFetch<Contract>(
          `/api/contracts/${contractNumber}`,
          {
            method: 'GET',
          }
        );
        console.log(contractData);

        const customerData = await apiFetch<Customer>(
          `/api/customers/${contractData.customerNumber}`,
          {
            method: 'GET',
          }
        );
        console.log(customerData);

        const reportsData = await apiFetch<any>(
          `/api/contracts/${contractNumber}/reports`,
          {
            method: 'GET',
          }
        );
        console.log(reportsData);

        const attachmentsData = await apiFetch<any>(
          `/api/contracts/${contractNumber}/attachments`,
          {
            method: 'GET',
          }
        );
        console.log(attachmentsData);

        const invoicesData = await apiFetch<any>(
          `/api/contracts/${contractNumber}/invoices`,
          {
            method: 'GET',
          }
        );
        console.log(invoicesData);

        setContract(contractData);
        setCustomer(customerData);
        setReports(reportsData.reports || []);
        setAttachments(attachmentsData.attachments);
        setInvoicesData(invoicesData.invoices);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [contractNumber]);

  // useEffect for originalAttachments
  useEffect(() => {
    if (contract?.fromOffer) {
      const fetchOriginalAttachments = async () => {
        try {
          const originalAttachmentsData = await apiFetch<any>(
            `/api/contracts/${contract.fromOffer}/attachments`,
            {
              method: 'GET',
            }
          );
          setOriginalAttachments(originalAttachmentsData.attachments);
        } catch (error) {
          console.error(error);
        }
      };

      fetchOriginalAttachments();
    }
  }, [contract]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (!customer || !contract || !reports || !attachments) {
    return <div>Fehler beim Laden der Vertragsdetails.</div>;
  }

  const handleDownloadPDF = async () => {
    const response = await apiFetch(
      `/api/contracts/${contract.contractNumber}/file`,
      {
        method: 'GET',
        raw: true,
      }
    );
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${customer.firstName}_${customer.lastName}_-_${contract.contractType}_${contract.contractNumber}.pdf`;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download PDF');
    }
  };

  const handleDownloadAttachment = async (
    file_name: string,
    documentId: string,
    number: string
  ) => {
    const response = await apiFetch(`/api/file/${number}/get/${documentId}`, {
      method: 'GET',
      raw: true,
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = file_name;
      link.click();
      URL.revokeObjectURL(url);
    } else {
      console.error('Failed to download attachment');
    }
  };

  const handleConvertOfferToContract = async () => {
    if (!contract.contractNumber || !contract.isOffer) {
      alert('Nur Angebote können in Verträge umgewandelt werden.');
      return;
    }

    // update status
    const newStatus: ContractStatusData =
      contract.contractStatus as unknown as ContractStatusData;
    newStatus.create_invoice = true;
    const updatedContract = { ...contract, newStatus: newStatus } as Contract;
    setContract(updatedContract);

    try {
      contract.isOffer = false;
      const response = await apiFetch('/api/contracts/create', {
        method: 'POST',
        raw: true,
        body: JSON.stringify({ formData: contract, customer }),
      });

      if (response.ok) {
        const data = await response.json();
        alert('Das Angebot wurde erfolgreich in einen Vertrag umgewandelt.');
        router.push(`/contract/${data.contractNumber}`);
      } else {
        const errorData = await response.json();
        alert(`Fehler beim Umwandeln des Angebots: ${errorData.error}`);
      }
    } catch (error) {
      console.error(
        'Fehler beim Umwandeln des Angebots in einen Vertrag:',
        error
      );
      alert('Ein Fehler ist aufgetreten. Bitte versuche es später erneut.');
    }
  };

  const handleUploadFile = async () => {
    if (
      !fileInputRef.current ||
      !fileInputRef.current.files ||
      fileInputRef.current.files.length === 0
    ) {
      alert('Bitte wählen Sie eine Datei aus.');
      return;
    }

    const file = fileInputRef.current.files[0];

    const formData = new FormData();
    formData.append('file', file);
    formData.append('filename', file.name);
    formData.append('contractNumber', contractNumber);

    const response = await apiFetch(`/api/file/upload`, {
      method: 'POST',
      raw: true,
      body: formData,
    });

    if (response.ok) {
      alert('Datei wurde erfolgreich hochgeladen.');
    } else {
      console.error('Fehler beim Hochladen der Datei.');
      const errorData = await response.json();
      alert(`Fehler: ${errorData.error}`);
    }
  };

  const handleSendMail = async (type: string, invoice_number: string) => {
    const contractStatus = contract.contractStatus;
    const documentId = contract.documentId;
    const agentNumber = contract.agentNumber;
    const response = await apiFetch(
      `/api/contracts/${contract.contractNumber}/mail`,
      {
        method: 'POST',
        raw: true,
        body: JSON.stringify({
          customer,
          contractStatus,
          documentId,
          type,
          invoice_number,
          agentNumber,
        }),
      }
    );
    if (response.ok) {
      alert('Die Email wird in kürze versandt.');
    } else {
      console.error('Failed to send Email');
      alert('Beim versenden der Email ist ein Fehler aufgetreten.');
    }
  };

  async function handleGeneratePDF() {
    if (contract) {
      const response = await apiFetch(
        `/api/contracts/${contract!.contractNumber}/regenerate`,
        {
          method: 'GET',
          raw: true,
        }
      );
      if (response.ok) {
        alert('Die PDF wurde erfolgreich erstellt.');
      } else {
        console.error('Failed to create PDF');
      }
    }
    location.reload();
  }

  async function handleChange(event: SelectChangeEvent<string>): Promise<void> {
    const updatedContract = {
      ...contract,
      activeStatus: event.target.value,
    } as Contract;
    setContract(updatedContract);

    const endpoint = `/api/contracts/${contractNumber}/updateStatus`;
    try {
      const response = await apiFetch(endpoint, {
        method: 'PUT',
        raw: true,
        body: JSON.stringify({
          activeStatus: updatedContract.activeStatus,
          documentId: updatedContract.documentId,
        }),
      });

      if (response.ok) {
        alert('Der Status wurde erfolgreich geändert!');
      } else {
        alert('Fehler beim Ändern des Status!');
      }
    } catch (error) {
      console.error('Es ist ein Fehler aufgetreten:', error);
    }
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        {contract.isOffer ? 'Angebotsdetails' : 'Vertragsdetails'}
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {formatLabel(contract.contractType || '')}, {customer.firstName}{' '}
        {customer.lastName}
      </Typography>

      {/* customer informations */}
      <CustomerInformationBox
        customerData={customer}
        customerNumber={customer.customerNumber}
        detailsButton
        simplified
      />

      {/* agent informations */}
      {session.data?.roles.includes('asevo-admin') && (
        <div>
          <AgentInformationBox
            agentNumber={customer?.agentNumber as string}
            detailsButton
            simplified
          />
        </div>
      )}

      {/* actions */}
      <StaticBox title="Aktionen">
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title="Hier können weitere Dokumente angehängt werden.">
              <Button component="label" variant="contained" fullWidth>
                <FolderOpenIcon sx={{ mr: 1 }} />
                Datei auswählen
                <input type="file" ref={fileInputRef} hidden />
              </Button>
            </Tooltip>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
              <Button onClick={handleUploadFile} variant="contained" fullWidth>
                <UploadIcon sx={{ mr: 1 }} />
                Datei hochladen
              </Button>
            </Tooltip>
          </Grid>

          {(contract.contractStatus as JsonObject).create_police && (
            <Grid size={{ xs: 12, sm: 6, md: 6 }}>
              <Tooltip
                title={`Mit dieser Aktion wird eine Email mit ${contract.isOffer ? 'dem Angebot' : 'der Versicherungspolice'} an den Kunden gesendet.`}
              >
                <Button
                  onClick={() =>
                    handleSendMail(contract.isOffer ? 'offer' : 'police', '')
                  }
                  fullWidth
                  variant="contained"
                >
                  <SendIcon sx={{ mr: 1 }} />{' '}
                  {contract.isOffer ? 'Angebot' : 'Police'} senden
                </Button>
              </Tooltip>
            </Grid>
          )}

          {(contract.contractStatus as JsonObject).create_police && (
            <Grid size={{ xs: 12, sm: 6, md: 6 }}>
              <Tooltip
                title={`Hier können Sie ${contract.isOffer ? 'das Angebot' : 'die Versicherungspolice'} herunterladen.`}
              >
                <Button
                  onClick={handleDownloadPDF}
                  fullWidth
                  variant="contained"
                >
                  <DownloadIcon sx={{ mr: 1 }} />{' '}
                  {contract.isOffer ? 'Angebot' : 'Versicherungspolice'}
                </Button>
              </Tooltip>
            </Grid>
          )}

          {session.data?.roles.includes('asevo-admin') &&
            !(contract.contractStatus as JsonObject).create_police && (
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Tooltip
                  title={`Hier können Sie ${contract.isOffer ? 'das Angebot' : 'die Versicherungspolice'} generieren.`}
                >
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleGeneratePDF()}
                    fullWidth
                  >
                    <AutorenewIcon sx={{ mr: 1 }} />{' '}
                    {contract.isOffer ? 'Angebot' : 'Versicherungspolice'}{' '}
                    generieren
                  </Button>
                </Tooltip>
              </Grid>
            )}

          {/*                     {localStorage.getItem("is_admin") == "true" && !contract.contractStatus.create_invoice && !contract.isOffer &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie die Rechnung generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("invoice")}
                                    fullWidth
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} /> Rechnung generieren
                                </Button>
                            </ Tooltip>
                        </Grid>} */}

          {session.data?.roles.includes('asevo-admin') &&
            (contract.contractStatus as JsonObject).create_police && (
              <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                <Tooltip
                  title={`Hier können Sie ${contract.isOffer ? 'das Angebot' : 'die Versicherungspolice'} erneut generieren.`}
                >
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleGeneratePDF()}
                    fullWidth
                    sx={{
                      backgroundColor: 'red',
                    }}
                  >
                    <AutorenewIcon sx={{ mr: 1 }} />{' '}
                    {contract.isOffer ? 'Angebot' : 'Versicherungspolice'} neu
                    generieren
                  </Button>
                </Tooltip>
              </Grid>
            )}

          {/* {localStorage.getItem("is_admin") == "true" && contract.contractStatus.create_invoice && !contract.isOffer &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie die Erstrechnung erneut generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("invoice")}
                                    fullWidth
                                    sx={{
                                        backgroundColor: "red"
                                    }}
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} />   Erstrechnung neu generieren
                                </Button>
                            </ Tooltip>
                        </Grid>} */}

          {!contract.isOffer && (
            <Grid size={{ xs: 12, sm: 6, md: 6 }}>
              <Tooltip
                title={`Hier kann direkt ein Schaden zu diesem Vertrag gemeldet werden`}
              >
                <Button
                  onClick={() =>
                    router.push({
                      pathname: '/report/create',
                      query: {
                        contractNumber: contract.contractNumber,
                        customerNumber: contract.customerNumber,
                        agencyNumber: contract.agencyNumber,
                      },
                    })
                  }
                  fullWidth
                  variant="contained"
                >
                  <GppMaybeIcon sx={{ mr: 1 }} /> Schaden melden
                </Button>
              </Tooltip>
            </Grid>
          )}
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <RevisionsModal type={'contracts'} number={contractNumber} />
          </Grid>
        </Grid>
      </StaticBox>

      {/* contract informations */}
      <ContractInformationBox
        contract={contract}
        isOffer={contract.isOffer}
        visible
        router={router}
      />

      {/* reports list */}
      {reports.length > 0 && (
        <ReportsBox
          reports={reports}
          attachments={attachments}
          contract_number={contract.contractNumber}
        />
      )}

      {/* files list */}
      {attachments.filter((file) => file.type == 'contractAttachment').length >
        0 && (
        <FilesBox
          attachments={attachments}
          contractNumber={contract.contractNumber}
          handleDownloadAttachment={handleDownloadAttachment}
        />
      )}

      {/* origialfiles list */}
      {originalAttachments &&
        originalAttachments.filter((file) => file.type == 'contractAttachment')
          .length > 0 && (
          <OriginalFilesBox
            originalAttachments={originalAttachments}
            contractNumber={contract.contractNumber}
            fromOffer={contract.fromOffer || ''}
            handleDownloadAttachment={handleDownloadAttachment}
          />
        )}

      {/* invoice list */}
      {invoicesData && (
        <InvoicesBox
          contractNumber={contract.contractNumber}
          invoices={invoicesData}
          handleSendMail={handleSendMail}
        />
      )}

      {/* contract status */}
      <StatusBox
        contract={contract}
        handleChange={handleChange}
        isOffer={contract.isOffer}
      />

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* button for convert offer to contract */}
          {contract.isOffer && contract.toContract == '' && (
            <Tooltip title="Angebot in einen Vertrag umwandeln.">
              <Button
                onClick={handleConvertOfferToContract}
                color="secondary"
                variant="contained"
              >
                <AutoFixHighIcon sx={{ mr: 1 }} />
                Umwandeln
              </Button>
            </Tooltip>
          )}

          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>
    </Box>
  );
};

export default ContractDetails;
