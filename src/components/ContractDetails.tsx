// src/components/ContractDetails.tsx
import React, { useEffect, useRef, useState } from 'react';
import router from 'next/router';

import CustomerInformationBox from './box/CustomerInformationBox';
import AgentInformationBox from './box/AgentInformationBox';
import ContractInformationBox from './box/ContractInformationBox';
import ReportsBox from './box/ReportsBox';
import FilesBox from './box/FilesBox';
import OriginalFilesBox from './box/OriginalFilesBox';
import InvoicesBox from "./box/InvoicesBox";
import StatusBox from "./box/StatusBox";
import StaticBox from './box/StaticBox';

import { formatLabel } from '@/utils/keyFormatter';

import { CustomerData, ContractData, ReportData, AttachmentData, ContractStatusData } from '../types';
import { InvoiceData } from "@/utils/invoice/types";

import {
    Button,
    SelectChangeEvent,
    CircularProgress,
    Tooltip,
    Typography,
    Box,
    Grid2 as Grid
} from '@mui/material';

import {
    FolderOpen as FolderOpenIcon,
    Upload as UploadIcon,
    Download as DownloadIcon,
    Autorenew as AutorenewIcon,
    Send as SendIcon,
    AutoFixHigh as AutoFixHighIcon,
    GppMaybe as GppMaybeIcon,
} from "@mui/icons-material";
import RevisionsModal from './modal/RevisionsModal';


interface ContractDetailsProps {
    customer_number: string;
    contract_number: string;
    isOffer?: boolean;
}

const ContractDetails = ({ contract_number: contract_number, customer_number: customer_number, isOffer = false }: ContractDetailsProps) => {
    const [contract, setContract] = useState<ContractData | null>(null);
    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [reports, setReports] = useState<ReportData[] | null>(null);
    const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);
    const [originalAttachments, setOriginalAttachments] = useState<AttachmentData[] | null>(null);
    const [invoicesData, setInvoicesData] = useState<InvoiceData[]>([])
    const [loading, setLoading] = useState(true);
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || "";
        const fetchData = async () => {
            try {
                const contractResponse = await fetch(`/api/contracts/${contract_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                const customerResponse = await fetch(`/api/customer/${customer_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                const reportResponse = await fetch(`/api/contracts/reports/${contract_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                const attachmentsResponse = await fetch(`/api/file/${contract_number}/all`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                const invoicesResponse = await fetch(`/api/invoice/get/contract/${contract_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                if (!contractResponse.ok || !customerResponse.ok || !reportResponse.ok || !attachmentsResponse.ok || !invoicesResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const contractData = await contractResponse.json();
                const customerData = await customerResponse.json();
                const reportsData = await reportResponse.json();
                const attachmentsData = await attachmentsResponse.json();
                const invoicesData = await invoicesResponse.json();

                setContract(contractData);
                setCustomer(customerData);
                setReports(reportsData);
                setAttachments(attachmentsData);
                setInvoicesData(invoicesData);
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [contract_number, customer_number]);

    //useEffect for originalAttachments
    useEffect(() => {
        if (contract?.from_offer) {
            const token = localStorage.getItem("jwt") || "";
            const fetchOriginalAttachments = async () => {
                try {
                    const originalAttachmentsResponse = await fetch(`/api/file/${contract.from_offer}/all`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Baerer ${token}`
                        },
                    });

                    if (!originalAttachmentsResponse.ok) {
                        throw new Error('Failed to fetch original attachments');
                    }

                    const originalAttachmentsData = await originalAttachmentsResponse.json();
                    setOriginalAttachments(originalAttachmentsData);
                } catch (error) {
                    console.error(error);
                }
            };

            fetchOriginalAttachments();
        }
    }, [contract]);

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    if (!customer || !contract || !reports || !attachments) {
        return <div>Fehler beim Laden der Vertragsdetails.</div>;
    }

    const handleDownloadPDF = async () => {
        const documentId = attachments.filter((attachment) => attachment.type == "contract")[0].documentId
        const response = await fetch(`/api/file/${contract.contract_number}/get/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${customer.first_name}_${customer.last_name}_-_${contract.contract_type}_${contract.contract_number}.pdf`;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download PDF');
        }
    };

    const handleDownloadAttachment = async (file_name: string, documentId: string, number: string) => {
        const response = await fetch(`/api/file/${number}/get/${documentId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`,
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = file_name;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            console.error('Failed to download attachment');
        }
    };

    const handleConvertOfferToContract = async () => {
        if (!contract.contract_number || !isOffer) {
            alert('Nur Angebote können in Verträge umgewandelt werden.');
            return;
        }

        // update status
        const newStatus: ContractStatusData = contract.contract_status
        newStatus.create_invoice = true
        const updatedContract = { ...contract, newStatus: newStatus } as ContractData;
        setContract(updatedContract);

        try {
            contract.is_offer = false
            const response = await fetch('/api/contracts/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify({ "formData": contract, customer }),
            });

            if (response.ok) {
                const data = await response.json();
                alert('Das Angebot wurde erfolgreich in einen Vertrag umgewandelt.');
                router.push(`/customer/${contract.customer_number}/contract/${data.contract_number}`);
            } else {
                const errorData = await response.json();
                alert(`Fehler beim Umwandeln des Angebots: ${errorData.error}`);
            }
        } catch (error) {
            console.error('Fehler beim Umwandeln des Angebots in einen Vertrag:', error);
            alert('Ein Fehler ist aufgetreten. Bitte versuche es später erneut.');
        }
    };

    const handleUploadFile = async () => {
        if (!fileInputRef.current || !fileInputRef.current.files || fileInputRef.current.files.length === 0) {
            alert('Bitte wählen Sie eine Datei aus.');
            return;
        }

        const file = fileInputRef.current.files[0];

        const formData = new FormData();
        formData.append('file', file);
        formData.append('filename', file.name);
        formData.append('contract_number', contract_number);

        const response = await fetch(`/api/file/upload`, {
            method: 'POST',
            headers: {
                // 'Content-Type': 'application/json', // REMOVE THIS
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
            body: formData,
        });

        if (response.ok) {
            alert('Datei wurde erfolgreich hochgeladen.');
        } else {
            console.error('Fehler beim Hochladen der Datei.');
            const errorData = await response.json();
            alert(`Fehler: ${errorData.error}`);
        }
    };


    const handleSendMail = async (type: string, invoice_number: string) => {
        const contract_status = contract.contract_status;
        const documentId = contract.documentId;
        const agent_number = contract.agent_number;
        const response = await fetch(`/api/contracts/${contract.contract_number}/mail`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
            },
            body: JSON.stringify({ customer, contract_status, documentId, type, invoice_number, agent_number }),
        });
        if (response.ok) {
            alert('Die Email wird in kürze versandt.');
        } else {
            console.error('Failed to send Email');
            alert('Beim versenden der Email ist ein Fehler aufgetreten.');
        }
    };

    async function handleGeneratePDF(type: string, customDueDate?: string) {
        if (contract) {
            const contract_status = contract.contract_status;
            const due_date = customDueDate || contract.insurance_start_date;
            const documentId = contract.documentId;
            const response = await fetch(`/api/contracts/${contract!.contract_number}/${type}/generate_pdf`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify({
                    contract_status,
                    documentId,
                    customer,
                    due_date
                }),
            });
            if (response.ok) {
                alert('Die PDF wurde erfolgreich erstellt.');
            } else {
                console.error('Failed to create PDF');
            }
        }
        location.reload();
    }

    async function handleChange(event: SelectChangeEvent<string>): Promise<void> {

        const updatedContract = { ...contract, active_status: event.target.value } as ContractData;
        setContract(updatedContract);

        const endpoint = `/api/contracts/${contract_number}/update_status`;
        try {
            const response = await fetch(endpoint, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify({
                    active_status: updatedContract.active_status,
                    documentId: updatedContract.documentId
                }),
            });

            if (response.ok) {
                alert('Der Status wurde erfolgreich geändert!');
            } else {
                alert('Fehler beim Ändern des Status!');
            }
        } catch (error) {
            console.error('Es ist ein Fehler aufgetreten:', error);
        }
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 }, 
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                {isOffer ? 'Angebotsdetails' : 'Vertragsdetails'}
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary" >
                {formatLabel(contract.contract_type)}, {customer.first_name} {customer.last_name}
            </Typography>

            {/* customer informations */}
            <CustomerInformationBox
                customerData={customer}
                customer_number={customer_number}
                details_button
                simplified />

            {/* agent informations */}
            {localStorage.getItem("is_admin") == "true" && <div>
                <AgentInformationBox
                    agent_number={customer?.agent_number as string}
                    details_button
                    simplified
                />
            </div>}

            {/* actions */}
            <StaticBox title="Aktionen">
                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title="Hier können weitere Dokumente angehängt werden.">
                            <Button
                                component="label"
                                variant="contained"
                                fullWidth
                            >
                                <FolderOpenIcon sx={{ mr: 1 }} />
                                Datei auswählen
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    hidden
                                />
                            </Button>
                        </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title={`Hier können weitere Dokumente anghängt werden.`}>
                            <Button
                                onClick={handleUploadFile}
                                variant="contained"
                                fullWidth
                            >
                                <UploadIcon sx={{ mr: 1 }} />
                                Datei hochladen
                            </Button>
                        </Tooltip>
                    </Grid>

                    {contract.contract_status.create_police &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Mit dieser Aktion wird eine Email mit ${isOffer ? 'dem Angebot' : 'der Versicherungspolice'} an den Kunden gesendet.`}>
                                <Button
                                    onClick={() => handleSendMail(isOffer ? "offer" : "police", "")}
                                    fullWidth
                                    variant="contained"
                                >
                                    <SendIcon sx={{ mr: 1 }} />   {isOffer ? 'Angebot' : 'Police'} senden
                                </Button>
                            </ Tooltip>
                        </Grid>}

                    {contract.contract_status.create_police &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie ${isOffer ? 'das Angebot' : 'die Versicherungspolice'} herunterladen.`}>
                                <Button
                                    onClick={handleDownloadPDF}
                                    fullWidth
                                    variant="contained"
                                >
                                    <DownloadIcon sx={{ mr: 1 }} />   {isOffer ? 'Angebot' : 'Versicherungspolice'}
                                </Button>
                            </ Tooltip>
                        </Grid>}

                    {localStorage.getItem("is_admin") == "true" && !contract.contract_status.create_police &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie ${isOffer ? 'das Angebot' : 'die Versicherungspolice'} generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("police")}
                                    fullWidth
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} />   {isOffer ? 'Angebot' : 'Versicherungspolice'} generieren
                                </Button>
                            </ Tooltip>
                        </Grid>}

                    {/*                     {localStorage.getItem("is_admin") == "true" && !contract.contract_status.create_invoice && !isOffer &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie die Rechnung generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("invoice")}
                                    fullWidth
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} /> Rechnung generieren
                                </Button>
                            </ Tooltip>
                        </Grid>} */}

                    {localStorage.getItem("is_admin") == "true" && contract.contract_status.create_police &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie ${isOffer ? 'das Angebot' : 'die Versicherungspolice'} erneut generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("police")}
                                    fullWidth
                                    sx={{
                                        backgroundColor: "red"
                                    }}
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} />    {isOffer ? 'Angebot' : 'Versicherungspolice'} neu generieren
                                </Button>
                            </ Tooltip>
                        </Grid>}

                    {/* {localStorage.getItem("is_admin") == "true" && contract.contract_status.create_invoice && !isOffer &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier können Sie die Erstrechnung erneut generieren.`}>
                                <Button
                                    variant="contained" color="primary"
                                    onClick={() => handleGeneratePDF("invoice")}
                                    fullWidth
                                    sx={{
                                        backgroundColor: "red"
                                    }}
                                >
                                    <AutorenewIcon sx={{ mr: 1 }} />   Erstrechnung neu generieren
                                </Button>
                            </ Tooltip>
                        </Grid>} */}

                    {!isOffer &&
                        <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                            <Tooltip title={`Hier kann direkt ein Schaden zu diesem Vertrag gemeldet werden`}>
                                <Button
                                    onClick={() =>
                                        router.push({
                                            pathname: '/report/create/new',
                                            query: {
                                                contract_number: contract.contract_number,
                                                customer_number: contract.customer_number,
                                                agency_number: contract.agency_number,
                                            },
                                        })
                                    }
                                    fullWidth
                                    variant="contained"
                                >
                                    <GppMaybeIcon sx={{ mr: 1 }} />  Schaden melden
                                </Button>
                            </Tooltip>
                        </Grid>
                    }
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <RevisionsModal type={'contracts'} number={contract_number} />
                    </Grid>
                </Grid>
            </StaticBox>

            {/* contract informations */}
            <ContractInformationBox
                contract={contract}
                isOffer={isOffer}
                visible
                router={router}
            />

            {/* reports list */}
            {reports.length > 0 && (
                <ReportsBox
                    reports={reports}
                    attachments={attachments}
                    contract_number={contract.contract_number}
                />
            )}

            {/* files list */}
            {attachments.filter((file) => file.type == "contract_attachment").length > 0 && (
                <FilesBox
                    attachments={attachments}
                    contract_number={contract.contract_number}
                    handleDownloadAttachment={handleDownloadAttachment}
                />
            )}

            {/* origialfiles list */}
            {originalAttachments && originalAttachments.filter((file) => file.type == "contract_attachment").length > 0 && (
                <OriginalFilesBox
                    originalAttachments={originalAttachments}
                    contract_number={contract.contract_number}
                    from_offer={contract.from_offer}
                    handleDownloadAttachment={handleDownloadAttachment}
                />
            )}

            {/* invoice list */}
            {invoicesData && (
                <InvoicesBox
                    contractNumber={contract.contract_number}
                    invoices={invoicesData}
                    handleSendMail={handleSendMail}
                />
            )}

            {/* contract status */}
            <StatusBox
                contract={contract}
                handleChange={handleChange}
                isOffer={isOffer}
                customer_number={customer_number}
            />

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>

                    {/* button for convert offer to contract */}
                    {isOffer && contract.to_contract == '' && (
                        <Tooltip title='Angebot in einen Vertrag umwandeln.'>
                            <Button
                                onClick={handleConvertOfferToContract}
                                color="secondary"
                                variant="contained">
                                <AutoFixHighIcon sx={{ mr: 1 }} />
                                Umwandeln
                            </Button>
                        </Tooltip>
                    )}

                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>

        </Box>
    );
};

export default ContractDetails;
