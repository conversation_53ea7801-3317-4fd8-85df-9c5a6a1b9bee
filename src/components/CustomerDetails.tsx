// src/components/CustomerDetails.tsx
import { Add as PlusIcon } from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { type Contract, type Customer } from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';
import { useIsAdmin } from '@/utils/authUtils';

import AgentInformationBox from './box/AgentInformationBox';
import ContractsBox from './box/ContractsBox';
import CustomerInformationBox from './box/CustomerInformationBox';
import OffersBox from './box/OffersBox';
import StaticBox from './box/StaticBox';
import ContractSelectionModal from './modal/ContractSelectionModal';
import RevisionsModal from './modal/RevisionsModal';

const CustomerDetails = () => {
  const router = useRouter();
  const { customerNumber } = router.query;
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [showButtons, setShowButtons] = useState<'contract' | 'offer' | null>(
    null
  );
  const [contractOverviewList, setContractOverviewList] = useState<Contract[]>(
    []
  );
  const [contractPickerIsVisible, setContractPickerIsVisible] = useState(false);
  const isAdmin = useIsAdmin();

  useEffect(() => {
    if (customerNumber) {
      apiFetch(`/api/customers/${customerNumber}`, {
        method: 'GET',
        raw: true,
      })
        .then((response) => {
          if (!response.ok) throw new Error('Failed to fetch');
          return response.json();
        })
        .then((data: Customer) => setCustomer(data))
        .catch((error) => console.error('Error fetching customer:', error));

      apiFetch(`/api/customers/${customerNumber}/contracts`, {
        method: 'GET',
        raw: true,
      })
        .then((response) => {
          if (!response.ok) throw new Error('Failed to fetch');
          return response.json();
        })
        .then((json) => {
          setContractOverviewList(json.items);
        })
        .catch((error) => console.error('Error fetching contracts:', error));
    }
  }, [customerNumber]);

  if (!customer) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  const contracts = contractOverviewList.filter(
    (contract) => !contract.isOffer && contract.activeStatus != 'DELETED'
  );
  const offers = contractOverviewList.filter(
    (contract) => contract.isOffer && contract.activeStatus != 'DELETED'
  );

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Kundendetails
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {customer.firstName} {customer.lastName}
      </Typography>

      <CustomerInformationBox
        customerData={customer}
        customerNumber={customerNumber as string}
        visible
        editButton
      />

      {isAdmin && (
        <AgentInformationBox
          agentNumber={customer?.agentNumber as string}
          detailsButton
          simplified
        />
      )}

      <StaticBox title="Aktionen">
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title="Verfügbare Vertragsoptionen einblenden">
              <Button
                onClick={() => {
                  setShowButtons('contract');
                  setContractPickerIsVisible(true);
                }}
                variant="contained"
                color="secondary"
                fullWidth
              >
                <PlusIcon sx={{ mr: 1 }} />
                Vertrag erstellen
              </Button>
            </Tooltip>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <Tooltip title="Verfügbare Angebotsoptionen einblenden">
              <Button
                onClick={() => {
                  setShowButtons('offer');
                  setContractPickerIsVisible(true);
                }}
                variant="contained"
                color="secondary"
                fullWidth
              >
                <PlusIcon sx={{ mr: 1 }} />
                Angebot erstellen
              </Button>
            </Tooltip>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 6 }}>
            <RevisionsModal
              type={'customers'}
              number={customerNumber as string}
            />
          </Grid>
        </Grid>
      </StaticBox>

      {contracts.length > 0 && (
        <ContractsBox
          contracts={contracts}
          customer={customer}
          visible
          router={router}
        />
      )}

      {offers.length > 0 && (
        <OffersBox
          offers={offers}
          customer={customer}
          visible
          router={router}
        />
      )}

      <Box display="flex" justifyContent="flex-end">
        <Grid container spacing={2}>
          {/* back button */}
          <Button onClick={() => window.history.back()} variant="contained">
            Zurück
          </Button>
        </Grid>
      </Box>

      <ContractSelectionModal
        open={contractPickerIsVisible}
        onClose={() => setContractPickerIsVisible(false)}
        customerNumber={customer.customerNumber}
        mode={showButtons}
      />
    </Box>
  );
};

export default CustomerDetails;
