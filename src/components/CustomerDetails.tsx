// src/components/CustomerDetails.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ContractData, CustomerData, AgentData } from '../types';

import CustomerInformationBox from './box/CustomerInformationBox';
import AgentInformationBox from './box/AgentInformationBox';
import ContractsBox from './box/ContractsBox';
import OffersBox from './box/OffersBox';
import ContractSelectionModal from './modal/ContractSelectionModal';

import { Button, Tooltip, CircularProgress, Box, Typography, Grid2 as Grid } from '@mui/material';
import { CenterFocusStrong, Add as PlusIcon } from "@mui/icons-material";
import RevisionsModal from './modal/RevisionsModal';
import StaticBox from './box/StaticBox';

const CustomerDetails = () => {
    const router = useRouter();
    const { customer_number } = router.query;
    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [showButtons, setShowButtons] = useState<'contract' | 'offer' | null>(null);
    const [contractOverviewList, setContractOverviewList] = useState<ContractData[]>([]);
    const [contractPickerIsVisible, setContractPickerIsVisible] = useState(false);

    useEffect(() => {
        const token = localStorage.getItem("jwt") || ""
        if (customer_number) {
            fetch(`/api/customer/${customer_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })
                .then((response) => {
                    if (!response.ok) throw new Error('Failed to fetch');
                    return response.json();
                })
                .then((data: CustomerData) => setCustomer(data))
                .catch((error) => console.error('Error fetching customer:', error));

            fetch(`/api/contracts/customer/${customer_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })
                .then((response) => {
                    if (!response.ok) throw new Error('Failed to fetch');
                    return response.json();
                })
                .then((data: ContractData[]) => setContractOverviewList(data))
                .catch((error) => console.error('Error fetching contracts:', error));
        }
    }, [customer_number]);

    if (!customer) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    const contracts = contractOverviewList.filter((contract) => !contract.is_offer && contract.active_status != 'DELETED');
    const offers = contractOverviewList.filter((contract) => contract.is_offer && contract.active_status != 'DELETED');

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 }, 
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}
        >
            <Typography variant="h4" textAlign="center" color="primary">
                Kundendetails
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary">
                {customer.first_name} {customer.last_name}
            </Typography>

            <CustomerInformationBox
                customerData={customer}
                customer_number={customer_number as string}
                visible
                edit_button
            />

            {localStorage.getItem("is_admin") === "true" && (
                <AgentInformationBox
                    agent_number={customer?.agent_number as string}
                    details_button
                    simplified
                />
            )}

            <StaticBox title="Aktionen">
                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title="Verfügbare Vertragsoptionen einblenden">
                            <Button
                                onClick={() => {
                                    setShowButtons('contract');
                                    setContractPickerIsVisible(true);
                                }}
                                variant="contained"
                                color="secondary"
                                fullWidth
                            >
                                <PlusIcon sx={{ mr: 1 }} />
                                Vertrag erstellen
                            </Button>
                        </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <Tooltip title="Verfügbare Angebotsoptionen einblenden">
                            <Button
                                onClick={() => {
                                    setShowButtons('offer');
                                    setContractPickerIsVisible(true);
                                }}
                                variant="contained"
                                color="secondary"
                                fullWidth
                            >
                                <PlusIcon sx={{ mr: 1 }} />
                                Angebot erstellen
                            </Button>
                        </Tooltip>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 6, md: 6 }}>
                        <RevisionsModal
                            type={'customer'}
                            number={customer_number as string}
                        />
                    </Grid>
                </Grid>
            </StaticBox>

            {contracts.length > 0 && (
                <ContractsBox
                    contracts={contracts}
                    customer={customer}
                    visible
                    router={router}
                />
            )}

            {offers.length > 0 && (
                <OffersBox
                    offers={offers}
                    customer={customer}
                    visible
                    router={router}
                />
            )}

            <Box display="flex" justifyContent="flex-end">
                <Grid container spacing={2}>

                    {/* back button */}
                    <Button
                        onClick={() => window.history.back()}
                        variant='contained'>
                        Zurück
                    </Button>

                </Grid>
            </Box>

            <ContractSelectionModal
                open={contractPickerIsVisible}
                onClose={() => setContractPickerIsVisible(false)}
                customerNumber={customer.customer_number}
                mode={showButtons}
            />
        </Box>

    );
};

export default CustomerDetails;
