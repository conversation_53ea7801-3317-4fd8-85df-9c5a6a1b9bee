// src/components/AgentList.tsx
import { Download as DownloadIcon } from '@mui/icons-material';
import {
  Box,
  CircularProgress,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import React, { useCallback, useState } from 'react';

import { apiFetch } from '@/utils/apiFetch';
import { useTableQuery } from '@/utils/useTableQuery';

import { agencyFieldConfig } from './filters/agencyFilterConfig';
import { FilterBuilder } from './filters/FilterBuilder';

export default function AgencyList() {
  const [isExporting, setIsExporting] = useState(false);

  const router = useRouter();
  const { data: session, status } = useSession();

  const table = useTableQuery<any>({
    endpoint: '/api/agencies',
    defaultSortField: 'agencyName',
    accessToken: session?.accessToken,
    enabled: status === 'authenticated',
    debounceMs: 300,
    mapData: (json) => ({
      items: json.items ?? [],
      meta: json.meta ?? null,
    }),
  });

  const {
    items: agencies,
    meta,
    loading,
    error,
    page,
    setPage,
    limit,
    filters,
    setFilters,
    sortField,
    sortDirection,
    handleSort,
    handleLimitChange,
  } = table;

  const handlePageChange = (
    _event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleShowDetails = (agent_number: string, event: React.MouseEvent) => {
    const url = `/agency/${agent_number}`;

    if (event.ctrlKey || event.metaKey) {
      window.open(url, '_blank');
    } else {
      router.push(url);
    }
  };

  const handleExportCsv = useCallback(async () => {
    try {
      setIsExporting(true);

      const query = new URLSearchParams({
        sortField,
        sortDirection,
        filters: JSON.stringify(filters),
      });

      const res = await apiFetch(`/api/agent/export-csv?${query.toString()}`, {
        method: 'GET',
        raw: true,
      });

      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = 'maklerliste.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();

      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('CSV Download Error:', err);
      alert('Export fehlgeschlagen');
    } finally {
      setIsExporting(false);
    }
  }, [filters, sortField, sortDirection]);

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  if (error) {
    return (
      <Typography variant="h6" color="error" align="center">
        Fehler: {error}
      </Typography>
    );
  }

  return (
    <Box sx={{ px: { xs: 2, md: 5 }, py: 5 }}>
      <Typography variant="h4" align="center" gutterBottom color="primary">
        Agenturliste
      </Typography>

      <Stack direction="row" justifyContent="space-between">
        {/* Unified filter builder */}
        <FilterBuilder
          fields={agencyFieldConfig}
          filters={filters}
          onChange={setFilters}
          initialField="username"
        />
        <Box>
          <Tooltip title="CSV exportieren">
            <IconButton
              onClick={handleExportCsv}
              color="primary"
              disabled={isExporting}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>

      <TableContainer component={Paper} sx={{ marginTop: 4 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('agency_name')}
              >
                Agenturname{' '}
                {sortField === 'agency_name' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('agency_number')}
              >
                Agenturnummer{' '}
                {sortField === 'agency_number' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
              <TableCell
                sx={{ fontWeight: 'bold', cursor: 'pointer' }}
                onClick={() => handleSort('is_admin')}
              >
                Administrator{' '}
                {sortField === 'is_admin' &&
                  (sortDirection === 'asc' ? '⬆' : '⬇')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {agencies.map((agency, index) => (
              <TableRow
                key={index}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={(event) =>
                  handleShowDetails(agency.agencyNumber, event)
                }
              >
                <TableCell>{agency.agencyName}</TableCell>
                <TableCell>{agency.agencyNumber}</TableCell>
                <TableCell>{agency.isAdmin ? 'Ja' : 'Nein'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {meta && (
        <Pagination
          count={meta.pageCount}
          page={page}
          onChange={handlePageChange}
          sx={{ marginTop: '1rem', display: 'flex', justifyContent: 'center' }}
        />
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '1rem',
        }}
      >
        <FormControl variant="outlined" size="small" sx={{ width: 200 }}>
          <InputLabel id="results-per-page-label">
            Ergebnisse pro Seite
          </InputLabel>
          <Select<number>
            labelId="results-per-page-label"
            value={limit}
            onChange={handleLimitChange}
            label="Ergebnisse pro Seite"
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={20}>20</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </FormControl>
      </div>
    </Box>
  );
}
