// src/components/ReportDetails.tsx
import { AutoAwesome } from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  Stack,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import ReportStatusBox from '@/components/box/ReportStatusBox';
import ReportSummaryBox from '@/components/box/ReportSummaryBox';
import ReportTimelineBox from '@/components/box/ReportTimelineBox';
import {
  type Attachment,
  type Contract,
  type Customer,
  type Report,
  type ReportCoverageAssessment,
} from '@/generated/prisma-postgres';
import { apiFetch } from '@/utils/apiFetch';

import ContractInformationBox from './box/ContractInformationBox';
import CustomerInformationBox from './box/CustomerInformationBox';
import ReportFilesBox from './box/ReportFilesBox';
import ReportInformationBox from './box/ReportInformationBox';
import StaticBox from './box/StaticBox';
import { CoverageAssessmentModal } from './modal/CoverageAssessmentModal';
import RevisionsModal from './modal/RevisionsModal';

const ReportDetails = () => {
  const router = useRouter();
  const { reportNumber } = router.query;
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [contract, setContract] = useState<Contract | null>(null);
  const [report, setReport] = useState<Report | null>(null);
  const [attachments, setAttachments] = useState<Attachment[] | null>(null);
  const [loading, setLoading] = useState(true);

  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // TODO: merge `loading` and `isLoading` into one state?
  const [data, setData] = useState<ReportCoverageAssessment | null>(null);

  useEffect(() => {
    if (!reportNumber) return;

    const initReport = async () => {
      const reportData = await apiFetch<Report>(
          `/api/reports/${reportNumber}`,
          {
            method: 'GET',
          }
      );
      setReport(reportData)

      return reportData
    }
    const initCustomer = async (report: Report) => {
      const {customerNumber} = report;
      if (!customerNumber)
        throw new Error('Customer number not found in report');

      const customerData = await apiFetch<Customer>(
        `/api/customers/${customerNumber}`,
        {
          method: 'GET',
        }
      );
      setCustomer(customerData)
    }
    const initContract = async (report: Report) => {
      const {contractNumber} = report;
      if (!contractNumber)
        throw new Error('Customer number not found in report');

      const contractData = await apiFetch<Contract>(
          `/api/contracts/${contractNumber}`,
          {
            method: 'GET',
          }
      );
      setContract(contractData)
    }
    const initAttachments = async () => {
      console.error('TODO: fetch attachments')
    }

    const fetchData = async () => {
      try {
        const loadedReport = await initReport()

        await Promise.all([
            initCustomer(loadedReport),
            initContract(loadedReport),
            initAttachments()
        ])
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [reportNumber]);

  const fetchCoverageAssessment = async () => {
    setIsLoading(true);
    try {
      const response = await apiFetch(
        `/api/reports/${reportNumber}/coverage-assessment`,
        {
          method: 'GET',
          raw: true,
        }
      );

      if (!response.ok) throw new Error('Fehler beim Abruf');

      const result = await response.json();
      setData(result);
    } catch (e) {
      console.error(e);
      setData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCoverageAssessmentClick = async () => {
    await fetchCoverageAssessment();
    setOpen(true);
  };

  const handleCreateAssessment = async () => {
    setIsLoading(true);
    try {
      const response = await apiFetch(
        `/api/reports/${reportNumber}/coverage-assessment/`,
        {
          method: 'POST',
          raw: true,
        }
      );

      if (!response.ok) throw new Error('Fehler beim Erstellen');

      const result = await response.json();
      setData(result);
      setOpen(false);
      await new Promise((res) => setTimeout(res, 100));
      await fetchCoverageAssessment();
      setOpen(true);
    } catch {
      alert('Fehler beim Anfordern der Deckungsprüfung.');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }
  if (!customer) return <div>Kundendaten nicht gefunden.</div>;
  if (!contract) return <div>Vertragsdaten nicht gefunden.</div>;

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: 'lg',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        Schadensdetails
      </Typography>
      <Typography variant="h6" textAlign="center" color="primary">
        {report?.reportNumber}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={7}>
          <Stack spacing={2}>
            <ReportSummaryBox />

            {report && <ReportInformationBox reportData={report} />}

            <CustomerInformationBox
              customerData={customer}
              customerNumber={customer.customerNumber}
              detailsButton
              simplified
            />

            <ContractInformationBox
              contract={contract}
              isOffer={false}
              router={router}
              detailsButton
              simplified
            />
          </Stack>
        </Grid>

        <Grid size={5}>
          <Stack spacing={2}>
            <ReportStatusBox />

            {/* actions */}
            <StaticBox title="Aktionen">
              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <RevisionsModal
                    type={'reports'}
                    number={reportNumber as string}
                  />
                </Grid>

                <Grid size={{ xs: 12 }}>
                  <Tooltip title="KI Deckungsprüfung">
                    <Button
                      onClick={handleCoverageAssessmentClick}
                      variant="contained"
                      disabled={isLoading}
                      fullWidth
                    >
                      <AutoAwesome sx={{ mr: 1 }} />
                      {isLoading ? 'Laden...' : 'KI - Deckungsprüfung'}
                    </Button>
                  </Tooltip>
                </Grid>
              </Grid>
            </StaticBox>

            <CoverageAssessmentModal
              open={open}
              onClose={() => setOpen(false)}
              data={data}
              isLoading={isLoading}
              createAssessment={handleCreateAssessment}
            />

            <ReportTimelineBox visible />

            {report &&
              attachments &&
              attachments.filter(
                (file) => file.reportNumber == report.reportNumber
              ).length > 0 && (
                <ReportFilesBox
                  reportData={report}
                  attachments={attachments.filter(
                    (file) => file.reportNumber == report.reportNumber
                  )}
                />
              )}
          </Stack>
        </Grid>
        <Grid size={12}>
          <Box display="flex" justifyContent="flex-end">
            {/* back button */}
            <Button onClick={() => window.history.back()} variant="contained">
              Zurück
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportDetails;
