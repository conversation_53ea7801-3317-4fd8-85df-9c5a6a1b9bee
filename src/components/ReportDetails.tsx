// src/components/ReportDetails.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { CustomerData, ContractData, ReportData, AttachmentData } from '../types';

import CustomerInformationBox from './box/CustomerInformationBox';
import ContractInformationBox from './box/ContractInformationBox';
import ReportInformationBox from './box/ReportInformationBox';
import ReportFilesBox from './box/ReportFilesBox';
import StaticBox from './box/StaticBox';

import { CircularProgress, Button, Typography, Tooltip, Stack, Box, Grid2 as Grid } from '@mui/material';
import { AutoAwesome } from "@mui/icons-material";
import { CoverageAssessmentModal } from './modal/CoverageAssessmentModal';
import RevisionsModal from './modal/RevisionsModal';
import ReportStatusBox from "@/components/box/ReportStatusBox";
import ReportTimelineBox from "@/components/box/ReportTimelineBox";
import ReportSummaryBox from "@/components/box/ReportSummaryBox";

type Reference = {
    section: string;
    subsection: string;
    text: string;
};

type CoverageAssessmentData = {
    coverage_assessment: string;
    coverage_assessment_confidence: number;
    coverage_assessment_indication: string;
    fraud_assessment: string;
    fraud_assessment_confidence: number;
    fraud_assessment_indication: string;
    relevant_references: Reference[];
};

const ReportDetails = () => {
    const router = useRouter();
    const { report_number } = router.query;
    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [contract, setContract] = useState<ContractData | null>(null);
    const [report, setReport] = useState<ReportData | null>(null);
    const [attachments, setAttachments] = useState<AttachmentData[] | null>(null);
    const [loading, setLoading] = useState(true);

    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [data, setData] = useState<CoverageAssessmentData | null>(null);

    useEffect(() => {
        if (!report_number) return;

        const token = localStorage.getItem("jwt") || "";

        const fetchData = async () => {
            try {
                const reportResponse = await fetch(`/api/report/${report_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                });

                if (!reportResponse.ok) throw new Error('Failed to fetch report');

                const reportData: ReportData = await reportResponse.json();
                setReport(reportData);

                const customer_number = reportData.customer_number;
                if (!customer_number) throw new Error('Customer number not found in report');

                const customerResponse = await fetch(`/api/customer/${customer_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                });

                const contract_number = reportData.contract_number;
                const contractResponse = await fetch(`/api/contracts/${contract_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                const attachmentsResponse = await fetch(`/api/file/${contract_number}/all`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                });

                if (!reportResponse.ok || !customerResponse.ok || !contractResponse.ok || !attachmentsResponse.ok) throw new Error('Failed to fetch data');

                const customerData: CustomerData = await customerResponse.json();
                const contractData = await contractResponse.json();
                const attachmentsData = await attachmentsResponse.json();

                setCustomer(customerData);
                setContract(contractData);
                setAttachments(attachmentsData);
            } catch (error) {
                console.error(error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [report_number]);

    const fetchCoverageAssessment = async () => {
        setIsLoading(true);
        try {
            const token = localStorage.getItem("jwt") || "";
            const response = await fetch(`/api/report/get/coverage-assessment/${report_number}`, {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) throw new Error("Fehler beim Abruf");

            const result = await response.json();
            setData(result);
        } catch (e) {
            console.error(e);
            setData(null);

        } finally {
            setIsLoading(false);
        }
    };

    const handleCoverageAssessmentClick = async () => {
        await fetchCoverageAssessment();
        setOpen(true);
    };

    const handleCreateAssessment = async () => {
        setIsLoading(true);
        try {
            const token = localStorage.getItem("jwt") || "";
            const response = await fetch(`/api/report/${report_number}/create/coverage-assessment/`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                },
            });

            if (!response.ok) throw new Error("Fehler beim Erstellen");

            const result = await response.json();
            setData(result);
            setOpen(false) 
            await new Promise((res) => setTimeout(res, 100));
            await fetchCoverageAssessment();
            setOpen(true)
        } catch (e) {
            alert("Fehler beim Anfordern der Deckungsprüfung.");
        } finally {
            setIsLoading(false);
        }
    };

    if (loading) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }
    if (!customer) return <div>Kundendaten nicht gefunden.</div>;
    if (!contract) return <div>Vertragsdaten nicht gefunden.</div>;

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: 'lg',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                Schadensdetails
            </Typography>
            <Typography variant="h6" textAlign="center" color="primary" >
                {report?.report_number}
            </Typography>

            <Grid container spacing={2}>

                <Grid size={7}>
                    <Stack spacing={2}>

                        <ReportSummaryBox />

                        {report &&
                            <ReportInformationBox
                                reportData={report}
                            />
                        }

                        <CustomerInformationBox
                            customerData={customer}
                            customer_number={customer.customer_number}
                            details_button
                            simplified />

                        <ContractInformationBox
                            contract={contract}
                            isOffer={false}
                            router={router}
                            details_button
                            simplified
                        />
                    </Stack>
                </Grid>

                <Grid size={5}>
                    <Stack spacing={2}>

                        <ReportStatusBox />

                        {/* actions */}
                        <StaticBox title="Aktionen">
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12 }}>
                                    <RevisionsModal type={'report'} number={report_number as string} />
                                </Grid>

                                <Grid size={{ xs: 12 }}>
                                    <Tooltip title="KI Deckungsprüfung">
                                        <Button onClick={handleCoverageAssessmentClick} variant="contained" disabled={isLoading} fullWidth>
                                            <AutoAwesome sx={{ mr: 1 }} />
                                            {isLoading ? "Laden..." : "KI - Deckungsprüfung"}
                                        </Button>
                                    </Tooltip>
                                </Grid>
                            </Grid>
                        </StaticBox>

                        <CoverageAssessmentModal
                            open={open}
                            onClose={() => setOpen(false)}
                            data={data}
                            isLoading={isLoading}
                            onCreateAssessment={handleCreateAssessment}
                        />

                        <ReportTimelineBox visible />

                        {report && attachments && attachments.filter((file) => file.report_number == report.report_number).length > 0 && (
                            <ReportFilesBox
                                reportData={report}
                                attachments={attachments.filter((file) => file.report_number == report.report_number)}
                            />
                        )}

                    </Stack>
                </Grid>
                <Grid size={12}>
                    <Box display="flex" justifyContent="flex-end">
                        {/* back button */}
                        <Button
                            onClick={() => window.history.back()}
                            variant='contained'>
                            Zurück
                        </Button>
                    </Box>
                </Grid>
            </Grid>
        </Box>
    );
};

export default ReportDetails;
