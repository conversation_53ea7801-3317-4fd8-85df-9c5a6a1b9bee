"use client";

import * as React from "react";
import {
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Stack,
  Box, Chip,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import {RemainingDaysChip} from "@/components/RemainingDaysChip";
import {CustomerOffer} from "@/domain";
import {getOfferStatusChip} from "@/common";
import {useTranslations} from "next-intl";
import {calculateDaysLeft} from "@/utils/calculateDaysLeft";

interface OfferCardProps {
  offer: CustomerOffer;
  onAccept: () => void;
  onDecline: () => void;
}

export const OfferCard: React.FC<OfferCardProps> = (props) => {
  const { offer } = props

  return (
    <Card
      sx={{ borderRadius: 3, boxShadow: "0 4px 12px rgba(0,0,0,0.08)" }}
    >
      <CardContent>
        <Stack spacing={4}>
          {/* Header: Produkt / Preis links, Chips rechts */}
          <Stack direction="row" justifyContent="space-between" alignItems="flex-start" sx={theme => ({
            [theme.breakpoints.down('sm')]: {
              flexDirection: 'column-reverse',
              gap: 2
            }
          })}>
            <Info offer={offer} />
            <Chips offer={offer} />
          </Stack>

          <Buttons {...props} />
        </Stack>
      </CardContent>
    </Card>
  );
};

const Info: React.FC<{ offer: CustomerOffer }> = ({ offer }) => {
  const t = useTranslations()
  const showExtensions = offer.productExtensions && offer.productExtensions.length > 0

  return (
      <Box>
        <Typography variant="h6" fontWeight={600}>
          {offer.productName}
        </Typography>
        <Typography fontSize='15px' sx={{ mt: '10px' }}>
          {offer.risk}
        </Typography>
        {offer.premium !== undefined && (
            <Typography variant="subtitle1" color="text.secondary" fontSize='14px'>
              {`${offer.premium.amount} € / ${offer.premium.span}`}
            </Typography>
        )}
        {showExtensions && (
            <Box sx={{
                m: '10px 0',
            }}>
              <Stack direction="row" flexWrap='wrap' alignItems='center' gap={1} sx={{ mt: '5px', width: '100%' }}>
                <Typography fontSize='14px'>
                    {t('customerDashboard.offer.extensions')}
                </Typography>
                {offer.productExtensions?.map((extension, index) => (
                    <Chip key={extension + index} label={extension} size='small' />
                ))}
              </Stack>
            </Box>
        )}
      </Box>
  )
}

const Chips: React.FC<{ offer: CustomerOffer }> = ({ offer }) => {
  const t = useTranslations()
  const showRemainingDaysChip = offer.expiresAt && ['invited', 'pending', 'open'].includes(offer.status)

  return (
      <Stack direction="row" gap={1} sx={theme => ({
        [theme.breakpoints.down('sm')]: {
          flexDirection: 'row-reverse',
        }
      })}>
        {showRemainingDaysChip && <RemainingDaysChip dueDate={offer.expiresAt!} />}
        {getOfferStatusChip(offer.status, t)}
      </Stack>
  )
}

const Buttons: React.FC<OfferCardProps> = ({ offer, onAccept, onDecline }) => {
  const t = useTranslations()
  const isExpired = calculateDaysLeft(offer.expiresAt) < 0

  return (
      <Stack direction="row" flexWrap="wrap" gap={2}>
        {offer.status === 'invited' && !isExpired && (
            <Button variant="outlined">
                {t('customerDashboard.offer.button.getOffer')}
            </Button>
        )}
        {offer.status === "open" && !isExpired && (
            <>
              <Button variant="contained" color="primary" onClick={onAccept}>
                  {t('customerDashboard.offer.button.accept')}
              </Button>
              <Button variant="outlined" color="error" onClick={onDecline}>
                {t('customerDashboard.offer.button.decline')}
              </Button>
            </>
        )}
        {offer.status === 'open' && isExpired && (
          <Button variant="outlined" color="primary" onClick={() => alert('Implement me first')}>
              {t('customerDashboard.offer.button.actualizeOffer')}
          </Button>
        )}
        {offer.offerPdf && (
            <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                href={offer.offerPdf}
                target="_blank"
            >
              {t('customerDashboard.offer.button.downloadOffer')}
            </Button>
        )}
        {offer.policyPdf && (
            <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                href={offer.policyPdf}
                target="_blank"
            >
              {t('customerDashboard.offer.button.downloadPolicy')}
            </Button>
        )}
      </Stack>
  )
}
