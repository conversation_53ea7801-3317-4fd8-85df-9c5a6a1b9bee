"use client";

import { Card, CardContent, Typography, Stack, Link } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";
import {useTranslations} from "next-intl";

export const InfoCard = () => {
  const t = useTranslations()

  const phoneNumberText = t('customerDashboard.infoCard.phoneNumber')
  const phoneNumberHref = phoneNumberText.replaceAll(' ', '')

  return (
    <Card
      sx={{
        borderRadius: 3,
        boxShadow: "0 4px 12px rgba(0,0,0,0.08)",
        p: 2,
      }}
    >
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" spacing={1} alignItems="center">
            <InfoIcon color="primary" />
            <Typography variant="h6">{t('customerDashboard.infoCard.title')}</Typography>
          </Stack>

          <Typography variant="body2" color="text.secondary">
            {t('customerDashboard.infoCard.paragraph1')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('customerDashboard.infoCard.paragraph2')}
          </Typography>

          <Stack spacing={1.5} sx={{ pt: 1, borderTop: "1px solid", borderColor: "divider" }}>
            <Typography variant="subtitle2" color="primary.dark">
              {t('customerDashboard.infoCard.footerTitle')}
            </Typography>

            <Stack direction="row" spacing={1} alignItems="center">
              <PhoneIcon fontSize="small" color="primary" />
              <Link
                href={`tel:${phoneNumberHref}`}
                underline="hover"
                color="text.secondary"
              >
                {phoneNumberText}
              </Link>
            </Stack>

            <Stack direction="row" spacing={1} alignItems="center">
              <EmailIcon fontSize="small" color="primary" />
              <Link
                href={`mailto:${t('customerDashboard.infoCard.email')}`}
                underline="hover"
                color="text.secondary"
              >
                {t('customerDashboard.infoCard.email')}
              </Link>
            </Stack>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};
