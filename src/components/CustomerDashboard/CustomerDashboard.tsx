"use client";

import * as React from "react";
import {Box, CircularProgress, Stack, Typography} from "@mui/material";
import { OfferCard } from "@/components/CustomerDashboard/OfferCard";
import { InfoCard } from "@/components/CustomerDashboard/InfoCard";
import { AcceptOfferModal } from "@/components/modal/AcceptOfferModal";
import { RejectOfferModal } from "@/components/modal/RejectOfferModal";
import {CustomerOffer} from "@/domain";
import {useTranslations} from "next-intl";
import {useEffect} from "react";
import httpClient from "@/utils/HttpClient";
import {ErrorMessage} from "@/components/ErrorMessage";

export default function CustomerDashboard() {
  const t = useTranslations('customerDashboard')

  const [offers, setOffers] = React.useState<CustomerOffer[]>([]);
  const [isLoading, setIsLoading] = React.useState(false)
  const [isError, setIsError] = React.useState(false)

  const [selectedOffer, setSelectedOffer] = React.useState<CustomerOffer | null>(null);
  const [acceptModalOpen, setAcceptModalOpen] = React.useState(false);
  const [rejectModalOpen, setRejectModalOpen] = React.useState(false);

  const loadOffers = async () => {
    setIsLoading(true)

    try {
      const response = await httpClient.request('/api/customer/offers')
      setOffers(response.offers)
    } catch (error) {
      console.error(error)
      setIsError(true)
    } finally {
      setIsLoading(false)
    }
  }
  useEffect(() => {
    loadOffers()
  }, []);

  const handleAccept = (offer: CustomerOffer) => {
    setSelectedOffer(offer);
    setAcceptModalOpen(true);
  };

  const handleReject = (offer: CustomerOffer) => {
    setSelectedOffer(offer);
    setRejectModalOpen(true);
  };

  const acceptOffer = () => {
    if (selectedOffer) {
      setOffers((prev) =>
        prev.map((o) => (o.id === selectedOffer.id ? { ...o, status: "accepted" } : o))
      );
    }
    setAcceptModalOpen(false);
  };

  const rejectOffer = () => {
    if (selectedOffer) {
      setOffers((prev) =>
        prev.map((o) => (o.id === selectedOffer.id ? { ...o, status: "rejected" } : o))
      );
    }
    setRejectModalOpen(false);
  };

  const resolveMainContent = () => {
    if (isError) {
      const retry = () => {
        setIsError(false)
        loadOffers()
      }
      return (
          <Box sx={{ paddingTop: '100px' }}>
            <ErrorMessage text={t('loadingFailed')} onRetryClick={retry} />
          </Box>
      )
    }
    if (isLoading) {
      return (
          <Box sx={{ display: 'flex', justifyContent: 'center', paddingTop: '100px' }}>
            <CircularProgress />
          </Box>
      )
    }
    if (!offers.length) {
      return (
          <Box sx={{ display: 'flex', justifyContent: 'center', paddingTop: '100px' }}>
            <Typography>
              {t('noOffers')}
            </Typography>
          </Box>
      )
    }

    return (
        <Stack spacing={3}>
          {offers.map((offer) => (
              <OfferCard
                  key={offer.id}
                  offer={offer}
                  onAccept={() => handleAccept(offer)}
                  onDecline={() => handleReject(offer)}
              />
          ))}
        </Stack>
    )
  }

  return (
    <Box p={4} sx={{ minHeight: '100vh' }}>
      <Typography variant="h4" mb={3}>
        {t('greeting')}
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" mb={3}>
        {t('overview')}
      </Typography>

      {/* Flex Container für zwei Spalten */}
      <Box display="flex" flexDirection={{ xs: "column", md: "row" }} gap={3}>
        {/* Linke Spalte: Angebote */}
        <Box flex={2}>
          {resolveMainContent()}
        </Box>

        {/* Rechte Spalte: InfoCard */}
        <Box flex={1}>
          <InfoCard />
        </Box>
      </Box>

      {selectedOffer && (
        <>
          <AcceptOfferModal
            open={acceptModalOpen}
            onClose={() => setAcceptModalOpen(false)}
            onConfirm={acceptOffer}
            productName={selectedOffer.productName}
          />
          <RejectOfferModal
            open={rejectModalOpen}
            onClose={() => setRejectModalOpen(false)}
            onConfirm={rejectOffer}
            productName={selectedOffer.productName}
          />
        </>
      )}
    </Box>
  );
}
