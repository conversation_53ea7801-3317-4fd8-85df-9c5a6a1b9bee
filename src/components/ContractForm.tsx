// src/components/ContractForm.tsx
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { CustomerData, ContractData, ContractStatusData, CalculationParametersData } from '../types';
import { formatLabel } from '../utils/keyFormatter';
import { TextField, FormControlLabel, Checkbox, InputAdornment, Button, CircularProgress, Tooltip, Typography, Box } from '@mui/material';

import AnimalLiabilityInsuranceForm from './insuranceForms/AnimalLiabilityInsuranceForm'; // Tierhalterhaftpflicht
import BuildingInsuranceForm from './insuranceForms/BuildingInsuranceForm'; //Gewerbe – Gebäude-Versicherung
import BusinessInsuranceForm from './insuranceForms/BusinessInsuranceForm'; // Geschäftsversicherung
import ConstructionInsuranceForm from './insuranceForms/ConstructionInsuranceForm'; // Bauleistung
import ConstructionOwnerLiabilityForm from './insuranceForms/ConstructionOwnerLiabilityForm'; // Bauherrenhaftpflicht
import HomeAndLandownerLiabilityForm from './insuranceForms/HomeAndLandownerLiabilityForm'; // Haus- und Grundbesitzerhaftpflicht
import HouseholdInsuranceForm from './insuranceForms/HouseholdInsuranceForm'; // Hausrat
import PersonalLiabilityInsuranceForm from './insuranceForms/PersonalLiabilityInsuranceForm'; // Privathaftpflicht
import ResidentialBuildingInsuranceForm from './insuranceForms/ResidentialBuildingInsuranceForm'; // Wohngebäude
import BusinessLiabilityInsuranceForm from './insuranceForms/BusinessLiabilityInsuranceForm'; // Betriebshaftpflicht
import AccidentInsuranceForm from './insuranceForms/AccidentInsuranceForm'; // Unfallversicherung
import ShareForm from './insuranceForms/ShareForm';  // Importiere das ShareForm
import CustomerInformationBox from './box/CustomerInformationBox';
import CalculationsBox from './box/CalculationsBox';
import DefaultForm from './insuranceForms/DefaultForm';
import StaticBox from './box/StaticBox';

interface ContractFormProps {
    customer_number: string;
    contractType?: string;
    isOffer: boolean;
    edit_mode?: boolean;
    contract_number?: string;
}

const ContractForm: React.FC<ContractFormProps> = ({ customer_number, contractType = "", isOffer, edit_mode = false, contract_number }) => {
    const router = useRouter();
    const [customer, setCustomer] = useState<CustomerData | null>(null);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [calculationPreviewData, setCalculationPreviewData] = useState<any>(null);
    const [calculationsIsVisible, setCalculationsIsVisible] = useState(true);
    const [isFormDataChanged, setIsFormDataChanged] = useState(false);
    const [formData, setFormData] = useState<ContractData>({
        contract_type: contractType,
        insurance_start_date: '',
        insurance_end_date: '',
        insurance_main_due_date: '',
        payment_mode: 'jährlich',
        iban: '',
        bic: '',
        previous_insurance: '',
        previous_insurance_number: '',
        noPreviousInsurance: false,
        previous_claims: false,
        is_offer: isOffer,
        covered_risks: false,
        is_elementar: false,
        animal_data: [{
            animal_type: '',
            race: '',
            animal_name: '',
        }],
        coverage_amount: "",
        contractId: 0,
        building_type: '',
        is_permanently_occupied: false,
        insurance_sum: 0,
        object_type: '',
        tariff_group: '',
        employee_count: 0,
        business_type: '',
        living_area: 0,
        insurance_sum_1914: 0,
        is_construction_year_unknown: false,
        construction_year: 0,
        coverage_usage: 0,
        household_tech: false,
        pv_system: false,
        glass_insurance: false,
        animal_type: '',
        private_first_name: '',
        private_name: '',
        family_coverage: false,
        is_single: false,
        premium_rate: 0,
        building_sum: 0,
        customer_number: customer_number || "",
        contract_number: '',
        additional_agreements: '',
                insured_persons: [{
            first_name: '',
            last_name: '',
            birth_date: '',
            type: '',
            occupation: '',
            occupation_group: '',
            increased_benefit_clause: 0,
            disability_coverage: '',
            basic_sum: 0,
            accident_pension: 0,
            accidental_death: 0,
            daily_sickness_allowance: 0,
            hospital_daily_allowance: 0,
            transitional_benefit: 0,
            first_aid_module: false,
        }],
        share_data: [{
            share_type: 'Alpha',
            percentage: 100,
            insurance: '',
        }],
        agency_number: localStorage.getItem("agencyNumber") || "",
        commission: 0,
        contract_status: {
            create_police: true,
            send_email: process.env.NEXT_PUBLIC_MAIL_SERIVCE == "true" ? (localStorage.getItem("is_admin") == "true" ? false : true) : false,
            create_invoice: isOffer ? false : true,
            send_invoice: isOffer ? false : false,
            send_automatic_invoice: true,
            active: isOffer ? false : true,
        },
        from_offer: '',
        to_contract: '',
        is_individually_calculated: false,
        active_status: 'INACTIVE',
        risk_addresses: [{
            street: '',
            house_number: '',
            postal_code: '',
            city: '',
            unit: ''
        }]
    });
    const [calculationParametersData, setCalculationParametersData] = useState<CalculationParametersData>()

    useEffect(() => {
        const token = localStorage.getItem("jwt") || "";
        const fetchData = async () => {

            if (!customer_number) {
                return
            }

            const customerResonse = await fetch(`/api/customer/${customer_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${token}`
                },
            })
            const customerData = await customerResonse.json()
            setCustomer(customerData)


            if (edit_mode && contract_number) {
                const contractDataResponse = await fetch(`/api/contracts/${contract_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                })
                const contractData = await contractDataResponse.json()
                if (!contractData.is_individually_calculated) {
                    contractData["premie"] = undefined // reset Premie so it wont overwrite
                    contractData["premie_household_tech"] = undefined
                    contractData["premie_pv_system"] = undefined
                    contractData["premie_glass_insurance"] = undefined
                    contractData["invoice_amount"] = undefined
                }
                // set noPreviousInsurance depending on previous_insurance
                if (contractData.previous_insurance)
                    contractData["noPreviousInsurance"] = false
                else
                    contractData["noPreviousInsurance"] = true
                setFormData(contractData)
            }


            if (localStorage.getItem("is_admin") && !edit_mode) {
                const agentResponse = await fetch(`/api/agent/${customerData.agent_number}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Baerer ${token}`
                    },
                })
                const agentData = await agentResponse.json()
                setFormData((prev) => ({
                    ...prev,
                    "commission": agentData.commission,
                }));
            }
        }
        fetchData()
    }, [customer_number, edit_mode, contract_number]);

    useEffect(() => {
        if (formData.insurance_start_date != "")
            fetch(`/api/calculation/get?valid_from=${formData.insurance_start_date}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
            })
                .then(res => res.json())
                .then(data => setCalculationParametersData(data))
    }, [formData.insurance_start_date])

    useEffect(() => {
        setIsFormDataChanged(true);
    }, [formData]);

    // Implementation signature
    function handleChange(event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>): void {

        const target = event.target as
            | HTMLInputElement
            | HTMLSelectElement
            | HTMLTextAreaElement;

        const { name, value, type } = target;

        if (name === "is_bak1") {
            setFormData((prev) => ({
                ...prev,
                building_type: "BAK1"
            }));
            return;
        } else if (name === "coverage_usage") {
            setFormData((prev) => ({
                ...prev,
                coverage_usage: 100
            }));
            return;
        } else if (name === "private_building") {
            setFormData((prev) => ({
                ...prev,
                object_type: "privates Wohngebäude"
            }));
            return;
        }
        else if (name === "noPreviousInsurance" && type === "checkbox") {
            const checked = (target as HTMLInputElement).checked;
            setFormData((prev) => ({
                ...prev,
                noPreviousInsurance: checked,
                previous_insurance: checked ? "" : prev.previous_insurance,
                previous_insurance_number: checked ? "" : prev.previous_insurance_number,
            }));
            return;
        }

        let newValue: string | number | boolean = value;
        if (type === 'checkbox') {
            newValue = (event.target as HTMLInputElement).checked;
        } else if (type === 'number') {
            newValue = parseFloat(value);
        }

        const updatedFormData = {
            ...formData,
            [name]: newValue,
        };

        // TODO: seprate file -> but has to be in handleChange, because of synchronization with formData and validation
        // Consitions for HouseHoldInsuranceForm
        if (updatedFormData.living_area !== undefined && name == "living_area") {
            const calculatedInsuranceSum = updatedFormData.living_area * (calculationParametersData?.hausrat_living_area_factor ? calculationParametersData?.hausrat_living_area_factor : 0)
            if (updatedFormData.insurance_sum !== calculatedInsuranceSum) {
                updatedFormData.insurance_sum = calculatedInsuranceSum
            }
        }

        if (name === "insurance_start_date" && value) {
            const startDate = new Date(value);
            const endDate = new Date(startDate);
            if (formData.contract_type === "bauleistung" || formData.contract_type === "bauherrenhaftpflicht") {
                endDate.setFullYear(endDate.getFullYear() + 2); //add two years
            } else {
                endDate.setFullYear(endDate.getFullYear() + 1); //add one year
            }
            updatedFormData.insurance_end_date = endDate.toISOString().split('T')[0]; // ISO-Format for <input type="date">
        }
        setFormData(updatedFormData);
        validateForm(updatedFormData);
    };

    const handlePreviewCalculation = async () => {
        const token = localStorage.getItem("jwt") || "";

        if (!formData.insurance_start_date) {
            alert("Bitte ein Versicherungsbeginn-Datum angeben, bevor die Vorschau berechnet wird.");
            return;
        }

        try {
            const response = await fetch('/api/calculation/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ contractData: formData }),
            });

            if (!response.ok) {
                throw new Error('Fehler beim Abrufen der Kalkulation.');
            }

            const data = await response.json();
            setCalculationPreviewData(data);
            setIsFormDataChanged(false);
        } catch (error) {
            console.error('Fehler bei der Berechnung:', error);
            alert('Es ist ein Fehler bei der Berechnung aufgetreten.');
        }
    };


    const handleStatus = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        const status: ContractStatusData = formData.contract_status;

        if (name === "create_police") {
            status.create_police = (e.target as HTMLInputElement).checked
            if (!status.create_police) {
                status.send_invoice = false;
                status.send_email = false;
                status.create_invoice = false;
                status.send_invoice = false;
            }
        } else if (name === "send_email") {
            status.send_email = (e.target as HTMLInputElement).checked
            if (!status.send_email) {
                status.send_invoice = false;
            }
        } else if (name === "create_invoice") {
            status.create_invoice = (e.target as HTMLInputElement).checked
            if (!status.create_invoice) {
                status.send_invoice = false;
            }
        } else if (name === "send_invoice") {
            status.send_invoice = (e.target as HTMLInputElement).checked
        } else if (name === "active") {
            status.active = (e.target as HTMLInputElement).checked
        }

        setFormData((prev) => ({
            ...prev,
            'contract_status': status
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm(formData)) {
            alert("Bitte überprüfen Sie Ihre Eingaben und korrigieren Sie die markierten Fehler.");
            return;
        }

        const endpoint = edit_mode
            ? `/api/contracts/${contract_number}/update`
            : `/api/contracts/create`;

        try {
            const response = await fetch(endpoint, {
                method: edit_mode ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify({ formData, customer }),
            });

            const actionText = edit_mode ? 'aktualisiert' : 'angelegt';
            const typeText = isOffer ? 'Angebot' : 'Vertrag';
            const typePath = isOffer ? 'offer' : 'contract';

            if (response.ok) {
                const createdContract = await response.json();

                alert(`${typeText} wurde erfolgreich ${actionText}.`);

                router.push(`/customer/${createdContract.customer_number}/${typePath}/${createdContract.contract_number}`);
            } else {
                alert(`Fehler! ${typeText} konnte nicht ${actionText} werden.`);
            }
        } catch (error) {
            console.error('Die Eingaben konnten nicht übergeben werden:', error);
        }
    };

    const validateForm = (updatedData: ContractData) => {
        const newErrors: { [key: string]: string } = {};

        if (localStorage.getItem("is_admin") == "false") {
            if (updatedData.contract_type == 'wohngebaeude') {
                if (updatedData.insurance_sum! > 5000000) {
                    newErrors.insurance_sum = `Die Versicherungssumme darf maximal 5.000.000 € betragen.`;
                }
            } else if (updatedData.contract_type == 'hausrat') {
                const hausrat_living_area_factor = (calculationParametersData?.hausrat_living_area_factor ? calculationParametersData?.hausrat_living_area_factor : 0)
                if (updatedData.insurance_sum! < updatedData.living_area! * hausrat_living_area_factor) {
                    newErrors.insurance_sum = `Die Versicherungssumme muss mindestens Wohnfläche x 650 = ${updatedData.living_area! * hausrat_living_area_factor} € betragen.`;
                } else if (updatedData.insurance_sum! > 500000) {
                    newErrors.insurance_sum = "Die Versicherungssumme darf maximal 500.000 € betragen.";
                }
            }
        }

        if (updatedData.insurance_start_date == '') {
            if (updatedData.insurance_sum_1914 != null) {
                newErrors.insurance_sum_1914 = "Bitte zuerst einen Versicherungsbeginn eingeben."
            }
            if (updatedData.insurance_sum != null) {
                newErrors.insurance_sum = "Bitte zuerst einen Versicherungsbeginn eingeben."
            }
            if (updatedData.living_area != null) {
                newErrors.living_area = "Bitte zuerst einen Versicherungsbeginn eingeben."
            }
        }

        if (updatedData.glass_insurance && updatedData.invoice_amount && !updatedData.insurance_sum_1914) {
            newErrors.invoice_amount = "Bitte zuerst die Versicherungssumme 1914 eingeben."
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0; // Returns `true` if no errors
    };

    if (!customer) {
        return (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}>
                <CircularProgress />
            </div>
        );
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                {edit_mode && isOffer ? 'Angebot bearbeiten' : edit_mode && !isOffer ? 'Vertrag bearbeiten' : isOffer ? 'Angebot erstellen' : 'Vertrag erstellen'}
            </Typography>
            <form onSubmit={handleSubmit}>
                <div className='space-y-4 mx-auto relative'>

                    <CustomerInformationBox
                        customerData={customer}
                        customer_number={customer_number}
                        simplified visible
                    />

                    <StaticBox title={`${isOffer ? 'Angebotsangaben' : 'Vertragsangaben'}, ${edit_mode ? formatLabel(formData.contract_type) : formatLabel(contractType)}`}>
                        <DefaultForm formData={formData} handleChange={handleChange} />

                        {localStorage.getItem("is_admin") == "true" &&
                            <ShareForm formData={formData} setFormData={setFormData} />
                        }

                        {(edit_mode ? formData.contract_type : contractType) === 'hausrat' && (
                            <HouseholdInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} errors={errors} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'wohngebaeude' && (
                            <ResidentialBuildingInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} errors={errors} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'tierhalterhaftpflicht' && (
                            <AnimalLiabilityInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'privathaftpflicht' && (
                            <PersonalLiabilityInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'haus_und_grundbesitzerhaftpflicht' && (
                            <HomeAndLandownerLiabilityForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'bauleistung' && (
                            <ConstructionInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'bauherrenhaftpflicht' && (
                            <ConstructionOwnerLiabilityForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'geschaeftsversicherung' && (
                            <BusinessInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'gebaeudeversicherung' && (
                            <BuildingInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'betriebshaftpflicht' && (
                            <BusinessLiabilityInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}

                        {(edit_mode ? formData.contract_type : contractType) === 'unfallversicherung' && (
                            <AccidentInsuranceForm formData={formData} setFormData={setFormData} handleChange={handleChange} />
                        )}
                        {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-2'>
                            <div className='text-red-600'>Dieses Feld überschreibt die Netto Prämie!</div>
                            <div />
                            <Tooltip title="Hier können Sie die Netto Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                                <TextField
                                    name="premie"
                                    type='number'
                                    label="Prämie"
                                    value={formData.premie || ""}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    variant="outlined"
                                    slotProps={{
                                        input: {
                                            endAdornment: (
                                                <InputAdornment position="end">€</InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Tooltip>
                        </div>}
                        {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-2 gap-2'>
                            <div className='text-red-600'>Dieses Feld überschreibt die Brutto Prämie!</div>
                            <div />
                            <Tooltip title="Hier können Sie die Brutto Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                                <TextField
                                    name="invoice_amount"
                                    type='number'
                                    label="Brutto Prämie"
                                    value={formData.invoice_amount || ""}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    variant="outlined"
                                    error={!!errors.invoice_amount}
                                    helperText={errors.invoice_amount}
                                    slotProps={{
                                        input: {
                                            endAdornment: (
                                                <InputAdornment position="end">€</InputAdornment>
                                            )
                                        }
                                    }}
                                />
                            </Tooltip>
                        </div>}
                        {localStorage.getItem("is_admin") == "true" && <div className='grid grid-cols-1 gap-2 pt-4'>
                            <Tooltip title="Geben Sie weitere Zusatzvereinbarungen ein.">
                                <TextField
                                    name="additional_agreements"
                                    label="Zusatzvereinbarungen"
                                    value={formData.additional_agreements}
                                    onChange={handleChange}
                                    multiline
                                    rows={4}
                                    className="w-full p-2"
                                    variant="outlined"
                                />
                            </Tooltip>
                        </div>}
                    </StaticBox>

                    <div className='pt-4'>
                        <CalculationsBox
                            calculationPreviewData={calculationPreviewData}
                            isVisible={calculationsIsVisible}
                            setIsVisible={setCalculationsIsVisible}
                            handlePreviewCalculation={handlePreviewCalculation}
                            isFormDataChanged={isFormDataChanged}
                        />
                    </div>
                    {!edit_mode && <div className='flex flex-col space-y-4 p-5 border'>
                        {localStorage.getItem("is_admin") == "true" &&
                            <Tooltip title="Aktivieren Sie diese Option, um die Police (PDF) bei Abschluss zu erstellen.">
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="create_police"
                                            checked={formData.contract_status.create_police}
                                            onChange={handleStatus}
                                        />
                                    }
                                    label={
                                        isOffer
                                            ? "Angebot PDF bei Abschluss erstellen"
                                            : "Vertrag PDF bei Abschluss erstellen"
                                    }
                                />
                            </Tooltip>}
                        <Tooltip title="Aktivieren Sie diese Option, um die Police bei Abschluss per E-Mail an den Makler zu senden.">
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="send_email" // maybe rename it to "send_police"?
                                        checked={formData.contract_status.create_police ? formData.contract_status.send_email : false}
                                        onChange={handleStatus}
                                        disabled={process.env.NEXT_PUBLIC_MAIL_SERIVCE == "false" || !formData.contract_status.create_police || localStorage.getItem("is_admin") == "false"}
                                    />
                                }
                                label={
                                    isOffer
                                        ? "Angebot bei Abschluss an Makler senden"
                                        : "Vertrag bei Abschluss an Makler senden"
                                }
                            />
                        </Tooltip>
                        {/* {!isOffer && <Tooltip title="Aktivieren Sie diese Option, um die Rechnung bei Abschluss automatisch per E-Mail an den Kunden zu senden.">
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="send_automatic_invoice"
                                        checked={formData.contract_status.send_automatic_invoice}
                                        onChange={handleStatus}
                                        disabled={process.env.NEXT_PUBLIC_MAIL_SERIVCE == "false" || !formData.contract_status.create_police || localStorage.getItem("is_admin") == "false"}
                                    />
                                }
                                label="Rechnung bei Abschluss automatisch senden"
                            />
                        </Tooltip>} */}
                    </div>}
                    <div className='flex gap-4 justify-end'>
                        <Tooltip title="Ohne sichern zurück.">
                            <Button
                                type="button"
                                onClick={() => window.history.back()}
                                variant='contained'
                            >
                                Verwerfen
                            </Button>
                        </Tooltip>
                        <Tooltip title={edit_mode && isOffer ? 'Hier können sie die änderungen sichen und somit das Angebot aktualisieren.' : edit_mode && !isOffer ? 'Hier können sie die änderungen sichen und somit den Vertrag aktualisieren.' : isOffer ? 'Hier wird das Angebot erstellt. Sie gelangen anschließend zur Angebotsübersicht' : 'Hier wird der Vertrag abgeschlossen. Sie gelangen anschließend zur Vertragsübersicht.'}>
                            <Button type="submit"
                                variant='contained'
                                color='secondary'>
                                {edit_mode && isOffer ? 'Angebot aktualisieren' : edit_mode && !isOffer ? 'Vertrag aktualisieren' : isOffer ? 'Angebot erstellen' : 'Vertrag abschließen'}
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </form>
        </Box>
    );
};

export default ContractForm;
