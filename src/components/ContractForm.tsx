// src/components/ContractForm.tsx
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  FormControlLabel,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import {
  type Agent,
  type Contract,
  type Customer,
} from '@/generated/prisma-postgres';
import { type JsonObject } from '@/generated/prisma-postgres/runtime/library';
import { type CalculationParametersData } from '@/types';
import { apiFetch } from '@/utils/apiFetch';
import { formatLabel } from '@/utils/keyFormatter';

import CalculationsBox from './box/CalculationsBox';
import CustomerInformationBox from './box/CustomerInformationBox';
import StaticBox from './box/StaticBox';
import AccidentInsuranceForm from './insuranceForms/AccidentInsuranceForm'; // Unfallversicherung
import AnimalLiabilityInsuranceForm from './insuranceForms/AnimalLiabilityInsuranceForm'; // Tierhalterhaftpflicht
import BuildingInsuranceForm from './insuranceForms/BuildingInsuranceForm'; //Gewerbe – Gebäude-Versicherung
import BusinessInsuranceForm from './insuranceForms/BusinessInsuranceForm'; // Geschäftsversicherung
import BusinessLiabilityInsuranceForm from './insuranceForms/BusinessLiabilityInsuranceForm'; // Betriebshaftpflicht
import ConstructionInsuranceForm from './insuranceForms/ConstructionInsuranceForm'; // Bauleistung
import ConstructionOwnerLiabilityForm from './insuranceForms/ConstructionOwnerLiabilityForm'; // Bauherrenhaftpflicht
import DefaultForm from './insuranceForms/DefaultForm';
import HomeAndLandownerLiabilityForm from './insuranceForms/HomeAndLandownerLiabilityForm'; // Haus- und Grundbesitzerhaftpflicht
import HouseholdInsuranceForm from './insuranceForms/HouseholdInsuranceForm'; // Hausrat
import PersonalLiabilityInsuranceForm from './insuranceForms/PersonalLiabilityInsuranceForm'; // Privathaftpflicht
import ResidentialBuildingInsuranceForm from './insuranceForms/ResidentialBuildingInsuranceForm'; // Wohngebäude
import ShareForm from './insuranceForms/ShareForm'; // Importiere das ShareForm

interface ContractFormProps {
  contractType?: string;
  isOffer: boolean;
  editMode?: boolean;
  contractNumber?: string;
  customerNumber?: string;
}

const ContractForm: React.FC<ContractFormProps> = ({
  contractType = '',
  isOffer,
  editMode = false,
  contractNumber,
  customerNumber,
}) => {
  const session = useSession();
  const defaultContract = {
    contractType: contractType,
    insuranceStartDate: '',
    insuranceEndDate: '',
    insuranceMainDueDate: '',
    paymentMode: 'jährlich',
    iban: '',
    bic: '',
    previousInsurance: '',
    previousInsuranceNumber: '',
    previousClaims: false,
    isOffer: isOffer,
    coveredRisks: false,
    isElementar: false,
    animalData: [
      {
        animal_type: '',
        race: '',
        animal_name: '',
      },
    ],
    coverageAmount: '',
    buildingType: '',
    isPermanentlyOccupied: false,
    insuranceSum: 0,
    objectType: '',
    tariffGroup: '',
    employeeCount: 0,
    businessType: '',
    livingArea: 0,
    insuranceSum1914: 0,
    isConstructionYearUnknown: false,
    constructionYear: 0,
    coverageUsage: 0,
    householdTech: false,
    pvSystem: false,
    glassInsurance: false,
    animalType: '',
    privateFirstName: '',
    privateName: '',
    familyCoverage: false,
    isSingle: false,
    premiumRate: 0,
    buildingSum: 0,
    customerNumber: '',
    contractNumber: '',
    additionalAgreements: '',
    insuredPersons: [
      {
        first_name: '',
        last_name: '',
        birth_date: '',
        type: '',
        occupation: '',
        occupation_group: '',
        increased_benefit_clause: 0,
        disability_coverage: '',
        basic_sum: 0,
        accident_pension: 0,
        accidental_death: 0,
        daily_sickness_allowance: 0,
        hospital_daily_allowance: 0,
        transitional_benefit: 0,
        first_aid_module: false,
      },
    ],
    shareData: [
      {
        share_type: 'Alpha',
        percentage: 100,
        insurance: '',
      },
    ],
    agencyNumber: session.data?.agencyNumber || '',
    commission: 0,
    contractStatus: {
      create_police: true,
      send_email:
        process.env.NEXT_PUBLIC_MAIL_SERIVCE == 'true'
          ? session.data?.roles.includes('asevo-admin')
            ? false
            : true
          : false,
      create_invoice: isOffer ? false : true,
      send_invoice: isOffer ? false : false,
      send_automatic_invoice: true,
      active: isOffer ? false : true,
    },
    fromOffer: '',
    toContract: '',
    isIndividuallyCalculated: false,
    activeStatus: 'INACTIVE',
    riskAddresses: [
      {
        street: '',
        house_number: '',
        postal_code: '',
        city: '',
        unit: '',
      },
    ],
    id: 0,
    documentId: '',
    createdAt: new Date(),
    updatedAt: new Date(),
    riskStreet: null,
    riskHouseNumber: null,
    riskCity: null,

    premie: null,
    premieHouseholdTech: null,
    premiePvSystem: null,
    premieGlassInsurance: null,

    active: false,
    agentNumber: null,

    tax: null,
    invoiceAmount: null,

    zuersZone: null,
    individualUnit: null,
    isIndividualUnit: false,
    monumentProtection: false,
    sgk: null,

    riskLivingUnitAmount: null,
    riskCommercialUnitAmount: null,
    riskGerageAmount: null,
    commercialArea: null,
    garageArea: null,

    riskPostalCode: null,
    glassTax: null,

    firstInvoiceNet: null,
    firstInvoiceTax: null,
    firstInvoiceGross: null,
    firstInvoiceGlassNet: null,
    firstInvoiceGlassTax: null,
    firstInvoiceGlassGross: null,

    customerId: null,
    agencyId: null,
    agentId: null,
  };

  const router = useRouter();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [calculationPreviewData, setCalculationPreviewData] =
    useState<Contract>(defaultContract);
  const [calculationsIsVisible, setCalculationsIsVisible] = useState(true);
  const [isFormDataChanged, setIsFormDataChanged] = useState(false);
  const [formData, setFormData] = useState<Contract>(defaultContract);
  const [calculationParametersData, setCalculationParametersData] =
    useState<CalculationParametersData>();

  useEffect(() => {
    const fetchData = async () => {
      if (customerNumber) {
        const customerData = await apiFetch<Customer>(
          `/api/customers/${customerNumber}`,
          {
            method: 'GET',
          }
        );
        setFormData((prev) => ({
          ...prev,
          customerNumber: customerData!.customerNumber!,
          customerId: customerData!.id!,
        }));
        setCustomer(customerData);
      }

      if (editMode && contractNumber) {
        const contractData = await apiFetch<Contract>(
          `/api/contracts/${contractNumber}`,
          {
            method: 'GET',
          }
        );
        if (!contractData.isIndividuallyCalculated) {
          contractData['premie'] = null; // reset Premie so it wont overwrite
          contractData['premieHouseholdTech'] = null;
          contractData['premiePvSystem'] = null;
          contractData['premieGlassInsurance'] = null;
          contractData['invoiceAmount'] = null;
        }
        // set noPreviousInsurance depending on previousInsurance
        // if (contractData.previousInsurance)
        //     contractData["noPreviousInsurance"] = false
        // else
        //     contractData["noPreviousInsurance"] = true
        setFormData(contractData);

        if (!contractData) {
          return;
        }

        if (!customerNumber) {
          const customerData = await apiFetch<Customer>(
            `/api/customers/${contractData.customerNumber}`,
            {
              method: 'GET',
            }
          );
          setFormData((prev) => ({
            ...prev,
            customerNumber: customerData!.customerNumber!,
            customerId: customerData!.id!,
          }));
          setCustomer(customerData);
        }
      }

      if (session.data?.roles.includes('asevo-admin') && !editMode) {
        const agentData = await apiFetch<Agent>(
          `/api/agents/${session.data.agentNumber}`,
          {
            method: 'GET',
          }
        );
        setFormData((prev) => ({
          ...prev,
          commission: agentData.commission,
        }));
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editMode, contractNumber, customerNumber]);

  useEffect(() => {
    if (formData.insuranceStartDate != '')
      apiFetch(
        `/api/calculation/get?validFrom=${formData.insuranceStartDate}`,
        {
          method: 'GET',
          raw: true,
        }
      )
        .then((res) => res.json())
        .then((data) => setCalculationParametersData(data));
  }, [formData.insuranceStartDate]);

  useEffect(() => {
    setIsFormDataChanged(true);
  }, [formData]);

  // Implementation signature
  function handleChange(
    event: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ): void {
    const target = event.target as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;

    const { name, value, type } = target;

    if (name === 'isBak1') {
      setFormData((prev) => ({
        ...prev,
        buildingType: 'BAK1',
      }));
      return;
    } else if (name === 'coverageUsage') {
      setFormData((prev) => ({
        ...prev,
        coverageUsage: 100,
      }));
      return;
    } else if (name === 'private_building') {
      setFormData((prev) => ({
        ...prev,
        objectType: 'privates Wohngebäude',
      }));
      return;
    } else if (name === 'noPreviousInsurance' && type === 'checkbox') {
      const checked = (target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        noPreviousInsurance: checked,
        previousInsurance: checked ? '' : prev.previousInsurance,
        previousInsuranceNumber: checked ? '' : prev.previousInsuranceNumber,
      }));
      return;
    }

    let newValue: string | number | boolean = value;
    if (type === 'checkbox') {
      newValue = (event.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      newValue = parseFloat(value);
    }

    const updatedFormData = {
      ...formData,
      [name]: newValue,
    };

    // TODO: seprate file -> but has to be in handleChange, because of synchronization with formData and validation
    // Consitions for HouseHoldInsuranceForm
    if (
      updatedFormData.livingArea &&
      updatedFormData.livingArea !== undefined &&
      name == 'livingArea'
    ) {
      const calculatedInsuranceSum =
        updatedFormData.livingArea *
        (calculationParametersData?.hausrat_living_area_factor
          ? calculationParametersData?.hausrat_living_area_factor
          : 0);
      if (updatedFormData.insuranceSum !== calculatedInsuranceSum) {
        updatedFormData.insuranceSum = calculatedInsuranceSum;
      }
    }

    if (name === 'insuranceStartDate' && value) {
      const startDate = new Date(value);
      const endDate = new Date(startDate);
      if (
        formData.contractType === 'bauleistung' ||
        formData.contractType === 'bauherrenhaftpflicht'
      ) {
        endDate.setFullYear(endDate.getFullYear() + 2); //add two years
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1); //add one year
      }
      updatedFormData.insuranceEndDate = endDate.toISOString().split('T')[0]; // ISO-Format for <input type="date">
    }
    setFormData(updatedFormData);
    validateForm(updatedFormData);
  }

  const handlePreviewCalculation = async () => {
    if (!formData.insuranceStartDate) {
      alert(
        'Bitte ein Versicherungsbeginn-Datum angeben, bevor die Vorschau berechnet wird.'
      );
      return;
    }

    try {
      const data = await apiFetch<Contract>('/api/calculation/preview', {
        method: 'POST',
        body: JSON.stringify({ contractData: formData }),
      });

      setCalculationPreviewData(data);
      setIsFormDataChanged(false);
    } catch (error) {
      console.error('Fehler bei der Berechnung:', error);
      alert('Es ist ein Fehler bei der Berechnung aufgetreten.');
    }
  };

  const handleStatus = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name } = e.target;
    const status: JsonObject = formData.contractStatus as JsonObject;

    if (name === 'create_police') {
      status.create_police = (e.target as HTMLInputElement).checked;
      if (!status.create_police) {
        status.send_invoice = false;
        status.send_email = false;
        status.create_invoice = false;
        status.send_invoice = false;
      }
    } else if (name === 'send_email') {
      status.send_email = (e.target as HTMLInputElement).checked;
      if (!status.send_email) {
        status.send_invoice = false;
      }
    } else if (name === 'create_invoice') {
      status.create_invoice = (e.target as HTMLInputElement).checked;
      if (!status.create_invoice) {
        status.send_invoice = false;
      }
    } else if (name === 'send_invoice') {
      status.send_invoice = (e.target as HTMLInputElement).checked;
    } else if (name === 'active') {
      status.active = (e.target as HTMLInputElement).checked;
    }

    setFormData((prev) => ({
      ...prev,
      contractStatus: status,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm(formData)) {
      alert(
        'Bitte überprüfen Sie Ihre Eingaben und korrigieren Sie die markierten Fehler.'
      );
      return;
    }

    const endpoint = editMode
      ? `/api/contracts/${contractNumber}`
      : `/api/contracts/`;

    try {
      const response = await apiFetch(endpoint, {
        method: editMode ? 'PUT' : 'POST',
        raw: true,
        body: JSON.stringify({ contract: formData }),
      });

      const actionText = editMode ? 'aktualisiert' : 'angelegt';
      const typeText = isOffer ? 'Angebot' : 'Vertrag';
      const typePath = isOffer ? 'offer' : 'contract';

      if (response.ok) {
        const createdContract = await response.json();

        alert(`${typeText} wurde erfolgreich ${actionText}.`);

        router.push(`/${typePath}/${createdContract.contractNumber}`);
      } else {
        alert(`Fehler! ${typeText} konnte nicht ${actionText} werden.`);
      }
    } catch (error) {
      console.error('Die Eingaben konnten nicht übergeben werden:', error);
    }
  };

  const validateForm = (updatedData: Contract) => {
    const newErrors: { [key: string]: string } = {};

    if (!session.data?.roles.includes('asevo-admin')) {
      if (updatedData.contractType == 'wohngebaeude') {
        if (updatedData.insuranceSum! > 5000000) {
          newErrors.insuranceSum = `Die Versicherungssumme darf maximal 5.000.000 € betragen.`;
        }
      } else if (updatedData.contractType == 'hausrat') {
        const hausrat_living_area_factor =
          calculationParametersData?.hausrat_living_area_factor
            ? calculationParametersData?.hausrat_living_area_factor
            : 0;
        if (
          updatedData.insuranceSum! <
          updatedData.livingArea! * hausrat_living_area_factor
        ) {
          newErrors.insuranceSum = `Die Versicherungssumme muss mindestens Wohnfläche x 650 = ${updatedData.livingArea! * hausrat_living_area_factor} € betragen.`;
        } else if (updatedData.insuranceSum! > 500000) {
          newErrors.insuranceSum =
            'Die Versicherungssumme darf maximal 500.000 € betragen.';
        }
      }
    }

    if (updatedData.insuranceStartDate == '') {
      if (updatedData.insuranceSum1914 != null) {
        newErrors.insuranceSum1914 =
          'Bitte zuerst einen Versicherungsbeginn eingeben.';
      }
      if (updatedData.insuranceSum != null) {
        newErrors.insuranceSum =
          'Bitte zuerst einen Versicherungsbeginn eingeben.';
      }
      if (updatedData.livingArea != null) {
        newErrors.livingArea =
          'Bitte zuerst einen Versicherungsbeginn eingeben.';
      }
    }

    if (
      updatedData.glassInsurance &&
      updatedData.invoiceAmount &&
      !updatedData.insuranceSum1914
    ) {
      newErrors.invoiceAmount =
        'Bitte zuerst die Versicherungssumme 1914 eingeben.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns `true` if no errors
  };

  if (!customer) {
    return (
      <div
        style={{ display: 'flex', justifyContent: 'center', marginTop: '2rem' }}
      >
        <CircularProgress />
      </div>
    );
  }

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        {editMode && isOffer
          ? 'Angebot bearbeiten'
          : editMode && !isOffer
            ? 'Vertrag bearbeiten'
            : isOffer
              ? 'Angebot erstellen'
              : 'Vertrag erstellen'}
      </Typography>
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 mx-auto relative">
          <CustomerInformationBox
            customerData={customer}
            customerNumber={customer!.customerNumber}
            simplified
            visible
          />

          <StaticBox
            title={`${isOffer ? 'Angebotsangaben' : 'Vertragsangaben'}, ${editMode ? formatLabel(formData.contractType || '') : formatLabel(contractType)}`}
          >
            <DefaultForm formData={formData} handleChange={handleChange} />

            {session.data?.roles.includes('asevo-admin') && (
              <ShareForm formData={formData} setFormData={setFormData} />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'hausrat' && (
              <HouseholdInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
                errors={errors}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'wohngebaeude' && (
              <ResidentialBuildingInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
                errors={errors}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'tierhalterhaftpflicht' && (
              <AnimalLiabilityInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'privathaftpflicht' && (
              <PersonalLiabilityInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'haus_und_grundbesitzerhaftpflicht' && (
              <HomeAndLandownerLiabilityForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'bauleistung' && (
              <ConstructionInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'bauherrenhaftpflicht' && (
              <ConstructionOwnerLiabilityForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'geschaeftsversicherung' && (
              <BusinessInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'gebaeudeversicherung' && (
              <BuildingInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'betriebshaftpflicht' && (
              <BusinessLiabilityInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}

            {(editMode ? formData.contractType : contractType) ===
              'unfallversicherung' && (
              <AccidentInsuranceForm
                formData={formData}
                setFormData={setFormData}
                handleChange={handleChange}
              />
            )}
            {session.data?.roles.includes('asevo-admin') && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-red-600">
                  Dieses Feld überschreibt die Netto Prämie!
                </div>
                <div />
                <Tooltip title="Hier können Sie die Netto Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                  <TextField
                    name="premie"
                    type="number"
                    label="Prämie"
                    value={formData.premie || ''}
                    onChange={handleChange}
                    className="w-full p-2"
                    variant="outlined"
                    slotProps={{
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">€</InputAdornment>
                        ),
                      },
                    }}
                  />
                </Tooltip>
              </div>
            )}
            {session.data?.roles.includes('asevo-admin') && (
              <div className="grid grid-cols-2 gap-2">
                <div className="text-red-600">
                  Dieses Feld überschreibt die Brutto Prämie!
                </div>
                <div />
                <Tooltip title="Hier können Sie die Brutto Prämie überschreiben. Sobald Sie einen Wert eintragen, wird die automatische Berechnung deaktiviert.">
                  <TextField
                    name="invoiceAmount"
                    type="number"
                    label="Brutto Prämie"
                    value={formData.invoiceAmount || ''}
                    onChange={handleChange}
                    className="w-full p-2"
                    variant="outlined"
                    error={!!errors.invoiceAmount}
                    helperText={errors.invoiceAmount}
                    slotProps={{
                      input: {
                        endAdornment: (
                          <InputAdornment position="end">€</InputAdornment>
                        ),
                      },
                    }}
                  />
                </Tooltip>
              </div>
            )}
            {session.data?.roles.includes('asevo-admin') && (
              <div className="grid grid-cols-1 gap-2 pt-4">
                <Tooltip title="Geben Sie weitere Zusatzvereinbarungen ein.">
                  <TextField
                    name="additionalAgreements"
                    label="Zusatzvereinbarungen"
                    value={formData.additionalAgreements}
                    onChange={handleChange}
                    multiline
                    rows={4}
                    className="w-full p-2"
                    variant="outlined"
                  />
                </Tooltip>
              </div>
            )}
          </StaticBox>

          <div className="pt-4">
            <CalculationsBox
              calculationPreviewData={calculationPreviewData}
              isVisible={calculationsIsVisible}
              setIsVisible={setCalculationsIsVisible}
              handlePreviewCalculation={handlePreviewCalculation}
              isFormDataChanged={isFormDataChanged}
            />
          </div>
          {!editMode && (
            <div className="flex flex-col space-y-4 p-5 border">
              {session.data?.roles.includes('asevo-admin') && (
                <Tooltip title="Aktivieren Sie diese Option, um die Police (PDF) bei Abschluss zu erstellen.">
                  <FormControlLabel
                    control={
                      <Checkbox
                        name="create_police"
                        checked={
                          ((formData.contractStatus as JsonObject)
                            .create_police as boolean) || false
                        }
                        onChange={handleStatus}
                      />
                    }
                    label={
                      isOffer
                        ? 'Angebot PDF bei Abschluss erstellen'
                        : 'Vertrag PDF bei Abschluss erstellen'
                    }
                  />
                </Tooltip>
              )}
              <Tooltip title="Aktivieren Sie diese Option, um die Police bei Abschluss per E-Mail an den Makler zu senden.">
                <FormControlLabel
                  control={
                    <Checkbox
                      name="send_email" // maybe rename it to "send_police"?
                      checked={
                        ((formData.contractStatus as JsonObject)
                          .create_police as boolean)
                          ? ((formData.contractStatus as JsonObject)
                              .send_email as boolean)
                          : false
                      }
                      onChange={handleStatus}
                      disabled={
                        process.env.NEXT_PUBLIC_MAIL_SERIVCE == 'false' ||
                        !((formData.contractStatus as JsonObject)
                          .create_police as boolean) ||
                        !session.data?.roles.includes('asevo-admin')
                      }
                    />
                  }
                  label={
                    isOffer
                      ? 'Angebot bei Abschluss an Makler senden'
                      : 'Vertrag bei Abschluss an Makler senden'
                  }
                />
              </Tooltip>
              {/* {!isOffer && <Tooltip title="Aktivieren Sie diese Option, um die Rechnung bei Abschluss automatisch per E-Mail an den Kunden zu senden.">
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="send_automatic_invoice"
                                        checked={formData.contractStatus.send_automatic_invoice}
                                        onChange={handleStatus}
                                        disabled={process.env.NEXT_PUBLIC_MAIL_SERIVCE == "false" || !formData.contractStatus.create_police || localStorage.getItem("is_admin") == "false"}
                                    />
                                }
                                label="Rechnung bei Abschluss automatisch senden"
                            />
                        </Tooltip>} */}
            </div>
          )}
          <div className="flex gap-4 justify-end">
            <Tooltip title="Ohne sichern zurück.">
              <Button
                type="button"
                onClick={() => window.history.back()}
                variant="contained"
              >
                Verwerfen
              </Button>
            </Tooltip>
            <Tooltip
              title={
                editMode && isOffer
                  ? 'Hier können sie die änderungen sichen und somit das Angebot aktualisieren.'
                  : editMode && !isOffer
                    ? 'Hier können sie die änderungen sichen und somit den Vertrag aktualisieren.'
                    : isOffer
                      ? 'Hier wird das Angebot erstellt. Sie gelangen anschließend zur Angebotsübersicht'
                      : 'Hier wird der Vertrag abgeschlossen. Sie gelangen anschließend zur Vertragsübersicht.'
              }
            >
              <Button type="submit" variant="contained" color="secondary">
                {editMode && isOffer
                  ? 'Angebot aktualisieren'
                  : editMode && !isOffer
                    ? 'Vertrag aktualisieren'
                    : isOffer
                      ? 'Angebot erstellen'
                      : 'Vertrag abschließen'}
              </Button>
            </Tooltip>
          </div>
        </div>
      </form>
    </Box>
  );
};

export default ContractForm;
