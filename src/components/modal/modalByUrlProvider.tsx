'use client';
import type { Route } from 'next';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { GetModalById } from './basicModal';

export default function ModalByUrlProvider() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const pathName = usePathname();
  const router = useRouter();
  const modalId = searchParams.get('showmodal');

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    console.log('showmodal', modalId);
    setIsOpen(!!modalId);
  }, [modalId]);

  const closeModal = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete('showmodal');
    const newUrl = params.toString() ? `?${params.toString()}` : '.';
    // router.replace(newUrl, { scroll: false });
    router.replace(`${pathName}?${params.toString()}` as Route, {
      scroll: false,
    });
  };

  return (
    <>
      {modalId && (
        <GetModalById
          modalId={modalId}
          onClose={() => {
            closeModal();
          }}
        ></GetModalById>
      )}
    </>
  );
}
