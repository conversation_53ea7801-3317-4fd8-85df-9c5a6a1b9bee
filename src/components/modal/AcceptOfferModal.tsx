"use client";

import { FC } from "react";
import { Button, Typography, Stack } from "@mui/material";
import { BaseModal } from "@/components/modal/BaseModal";
import {useTranslations} from "next-intl";

type AcceptOfferModalProps = {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  productName: string;
};

export const AcceptOfferModal: FC<AcceptOfferModalProps> = ({ open, onClose, onConfirm, productName }) => {
  const t = useTranslations('customerDashboard.acceptOfferModal')

  return (
    <BaseModal open={open} onClose={onClose} title={t('title')}>
      <Typography variant="body1" align="center">
          {t.rich('question', {
              strong: () => <strong>{productName}</strong> // TODO: can we pass the substitution params to t.rich?
          })}
      </Typography>
      <Stack direction="row" spacing={2}>
        <Button variant="outlined" color="inherit" onClick={onClose}>
            {t('secondaryButton')}
        </Button>
        <Button variant="contained" color="primary" onClick={onConfirm}>
            {t('primaryButton')}
        </Button>
      </Stack>
    </BaseModal>
  );
};
