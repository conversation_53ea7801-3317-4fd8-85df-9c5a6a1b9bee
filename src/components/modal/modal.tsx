import CancelIcon from '@mui/icons-material/Cancel';
import {
  Box,
  IconButton,
  Modal,
  type SxProps,
  type Theme,
} from '@mui/material';
import { type PropsWithChildren } from 'react';

type Props = PropsWithChildren<{
  onClose: () => void;
  sx?: SxProps<Theme>;
}>;

export function KvModal(props: Props) {
  return (
    <Modal
      sx={{
        background: '#000000D9',
        maxHeight: '100%',
        display: 'flex',
      }}
      open={true}
      onClose={() => {
        props.onClose();
      }}
    >
      <Box
        sx={{
          display: 'flex',
          background: 'white',
          position: 'relative',
          width: 900,
          minWidth: '70vw',
          maxWidth: 'calc(100vw - 30px)',
          borderRadius: '7px',
          padding: '50px',
          ...props.sx,
          maxHeight: 'calc(100% - 50px)',
          margin: 'auto',
          justifyContent: 'center',
        }}
      >
        <IconButton
          sx={{
            position: 'absolute',
            right: 0,
            top: 0,
          }}
          onClick={(e) => {
            e.stopPropagation();
            props.onClose();
          }}
        >
          <CancelIcon
            sx={{
              color: 'primary.main',
              fontSize: (theme) => theme.typography.h4.fontSize,
            }}
          ></CancelIcon>
        </IconButton>
        {props.children}
      </Box>
    </Modal>
  );
}
