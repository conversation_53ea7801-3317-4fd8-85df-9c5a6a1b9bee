// src/components/modal/CoverageAssessmentModal.tsx
import { Typo<PERSON>, Chip, Box, Accordion, AccordionSummary, AccordionDetails, Button, Tooltip } from "@mui/material";
import { Check, ExpandMore, AutoAwesome } from "@mui/icons-material";
import ModalBox from "./BoxModal";

type Reference = {
    section: string;
    subsection: string;
    text: string;
};

type CoverageAssessmentData = {
    coverage_assessment: string;
    coverage_assessment_confidence: number;
    coverage_assessment_indication: string;
    fraud_assessment: string;
    fraud_assessment_confidence: number;
    fraud_assessment_indication: string;
    relevant_references: Reference[];
};

type Props = {
    open: boolean;
    onClose: () => void;
    data: CoverageAssessmentData | null;
    isLoading?: boolean;
    onCreateAssessment?: () => void;
};

const getInterpolatedColor = (confidence: number): string => {
    const clamped = Math.max(0, Math.min(1, confidence));
    let r, g, b;
    if (clamped < 0.5) {
        const ratio = clamped / 0.5;
        r = Math.round(244 + (255 - 244) * ratio);
        g = Math.round(67 + (235 - 67) * ratio);
        b = Math.round(54 + (59 - 54) * ratio);
    } else {
        const ratio = (clamped - 0.5) / 0.5;
        r = Math.round(255 + (76 - 255) * ratio);
        g = Math.round(235 + (175 - 235) * ratio);
        b = Math.round(59 + (80 - 59) * ratio);
    }
    return `rgb(${r}, ${g}, ${b})`;
};

const getColorHex = (indication: string) => {
    switch (indication) {
        case "green":
            return "#4caf50";
        case "yellow":
            return "#ff9800";
        case "red":
            return "#f44336";
        default:
            return "#9e9e9e";
    }
};

export const CoverageAssessmentModal = ({ open, onClose, data, isLoading, onCreateAssessment }: Props) => {
    if (!data) {
        return (
<ModalBox open={open} onClose={onClose} title="KI - Deckungsprüfung" maxWidth="sm">
    <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        textAlign="center"
        sx={{ mt: 4 }}
    >
        <Typography sx={{ fontSize: '16px', fontWeight: 600, mb: 3 }}>
            Noch keine Deckungsprüfung vorhanden
        </Typography>

        <Button
            onClick={onCreateAssessment}
            variant="contained"
            color="primary"
            disabled={isLoading}
        >
            <AutoAwesome sx={{ mr: 1 }} />
            {isLoading ? "Anfrage läuft..." : "KI Deckungsprüfung anfordern"}
        </Button>
    </Box>
</ModalBox>

        );
    }

    return (
        <ModalBox open={open} onClose={onClose} title="KI - Deckungsprüfung" maxWidth="md">
            {/* Deckung */}
            <Box padding="0 10px 10px">
                <Box display="flex" alignItems="center" mb={1}>
                    <Typography sx={{ fontWeight: 600, fontSize: '17px' }}>
                        Deckungseinschätzung
                    </Typography>
                    <Box
                        sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getColorHex(data.coverage_assessment_indication),
                            ml: 1,
                        }}
                    />
                    <Chip
                        icon={<Check />}
                        label={`Vertrauen: ${Math.round(data.coverage_assessment_confidence * 100)}%`}
                        size="small"
                        sx={{ ml: 2, backgroundColor: getInterpolatedColor(data.coverage_assessment_confidence), }}
                    />
                </Box>
                <Typography sx={{ marginTop: '20px' }}>
                    {data.coverage_assessment}
                </Typography>
            </Box>

            {/* Betrug */}
            <Box padding='0 10px 10px'>
                <Box display="flex" alignItems="center" mb={1}>
                    <Typography sx={{ fontWeight: 600, fontSize: '17px' }}>
                        Betrugsprüfung
                    </Typography>
                    <Box
                        sx={{
                            width: 12,
                            height: 12,
                            borderRadius: '50%',
                            backgroundColor: getColorHex(data.fraud_assessment_indication),
                            ml: 1,
                        }}
                    />
                    <Chip
                        icon={<Check />}
                        label={`Vertrauen: ${Math.round(data.fraud_assessment_confidence * 100)}%`}
                        size="small"
                        sx={{ ml: 2, backgroundColor: getInterpolatedColor(data.fraud_assessment_confidence) }}
                    />
                </Box>
                <Typography sx={{ marginTop: '20px' }} >{data.fraud_assessment}</Typography>
            </Box>

            {/* Paragraphen */}
            <Box padding='0 10px 10px'>
                <Typography gutterBottom sx={{ fontWeight: '600', fontSize: '17px', marginTop: '20px' }}>
                    Relevante Auszüge aus den Versicherungsbedingungen
                </Typography>
                {Array.isArray(data?.relevant_references) && data.relevant_references.map((ref, idx) => (
                    <Accordion key={idx} disableGutters>
                        <AccordionSummary expandIcon={<ExpandMore />}>
                            <Typography>
                                {ref.section} – {ref.subsection}
                            </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                            <Typography variant="body2">{ref.text}</Typography>
                        </AccordionDetails>
                    </Accordion>
                ))}
            </Box>
        </ModalBox >
    );
};
