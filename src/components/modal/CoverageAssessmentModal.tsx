// src/components/modal/CoverageAssessmentModal.tsx
import { AutoAwesome, Check, ExpandMore } from '@mui/icons-material';
import AutorenewOutlinedIcon from '@mui/icons-material/AutorenewOutlined';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { type MouseEventHandler } from 'react';

import { type ReportCoverageAssessment } from '@/generated/prisma-postgres';

import ModalBox from './BoxModal';

type Props = {
  open: boolean;
  onClose: () => void;
  data: ReportCoverageAssessment | null;
  isLoading?: boolean;
  createAssessment?: () => void;
};

const getInterpolatedColor = (confidence: number): string => {
  const clamped = Math.max(0, Math.min(1, confidence));
  let r, g, b;
  if (clamped < 0.5) {
    const ratio = clamped / 0.5;
    r = Math.round(244 + (255 - 244) * ratio);
    g = Math.round(67 + (235 - 67) * ratio);
    b = Math.round(54 + (59 - 54) * ratio);
  } else {
    const ratio = (clamped - 0.5) / 0.5;
    r = Math.round(255 + (76 - 255) * ratio);
    g = Math.round(235 + (175 - 235) * ratio);
    b = Math.round(59 + (80 - 59) * ratio);
  }
  return `rgb(${r}, ${g}, ${b})`;
};

const getColorHex = (indication: string) => {
  switch (indication) {
    case 'green':
      return '#4caf50';
    case 'yellow':
      return '#ff9800';
    case 'red':
      return '#f44336';
    default:
      return '#9e9e9e';
  }
};

export const CoverageAssessmentModal = ({
  open,
  onClose,
  data,
  isLoading,
  createAssessment,
}: Props) => {
  if (!data) {
    return (
      <ModalBox
        open={open}
        onClose={onClose}
        title="KI - Deckungsprüfung"
        maxWidth="sm"
      >
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          sx={{ mt: 4 }}
        >
          <Typography sx={{ fontSize: '16px', fontWeight: 600, mb: 3 }}>
            Noch keine Deckungsprüfung vorhanden
          </Typography>

          <Button
            onClick={createAssessment}
            variant="contained"
            color="primary"
            disabled={isLoading}
          >
            <AutoAwesome sx={{ mr: 1 }} />
            {isLoading ? 'Anfrage läuft...' : 'KI Deckungsprüfung anfordern'}
          </Button>
        </Box>
      </ModalBox>
    );
  }

  const handleRefreshClick: MouseEventHandler = async (event) => {
    event.stopPropagation();
    createAssessment?.();
  };

  return (
    <ModalBox
      open={open}
      onClose={onClose}
      title="KI - Deckungsprüfung"
      maxWidth="md"
    >
      {/* Deckung */}
      <Box padding="0 10px 10px">
        <Box display="flex" alignItems="center" mb={1}>
          <Typography sx={{ fontWeight: 600, fontSize: '17px' }}>
            Deckungseinschätzung
          </Typography>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor: getColorHex(data.coverageAssessmentIndication!),
              ml: 1,
            }}
          />
          <Chip
            icon={<Check />}
            label={`Vertrauen: ${Math.round(data.coverageAssessmentConfidence! * 100)}%`}
            size="small"
            sx={{
              ml: 2,
              backgroundColor: getInterpolatedColor(
                data.coverageAssessmentConfidence!
              ),
            }}
          />
        </Box>
        <Typography sx={{ marginTop: '20px' }}>
          {data.coverageAssessment}
        </Typography>
      </Box>

      {/* Betrug */}
      <Box padding="0 10px 10px">
        <Box display="flex" alignItems="center" mb={1}>
          <Typography sx={{ fontWeight: 600, fontSize: '17px' }}>
            Betrugsprüfung
          </Typography>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor: getColorHex(data.fraudAssessmentIndication!),
              ml: 1,
            }}
          />
          <Chip
            icon={<Check />}
            label={`Vertrauen: ${Math.round(data.fraudAssessmentConfidence! * 100)}%`}
            size="small"
            sx={{
              ml: 2,
              backgroundColor: getInterpolatedColor(
                data.fraudAssessmentConfidence!
              ),
            }}
          />
        </Box>
        <Typography sx={{ marginTop: '20px' }}>
          {data.fraudAssessment}
        </Typography>
      </Box>

      {/* Paragraphen */}
      <Box padding="0 10px 10px">
        <Typography
          gutterBottom
          sx={{ fontWeight: '600', fontSize: '17px', marginTop: '20px' }}
        >
          Relevante Auszüge aus den Versicherungsbedingungen
        </Typography>
        {Array.isArray(data?.relevantReferences) &&
          data.relevantReferences.map((ref, idx) => (
            <Accordion key={idx} disableGutters>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Typography>
                  {(ref as any).section} – {(ref as any).subsection}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2">{(ref as any).text}</Typography>
              </AccordionDetails>
            </Accordion>
          ))}
      </Box>

      <Tooltip title="Aktualisieren" placement="top">
        <IconButton
          onClick={handleRefreshClick}
          color="primary"
          size="small"
          sx={{ position: 'absolute', top: '27px', right: '75px' }}
          disabled={isLoading}
        >
          <AutorenewOutlinedIcon />
        </IconButton>
      </Tooltip>
      {isLoading && (
        <Typography
          sx={{
            color: 'primary',
            fontSize: '17px',
            fontStyle: 'italic',
            position: 'absolute',
            top: '32px',
            right: '115px',
          }}
        >
          Aktualisieren...
        </Typography>
      )}
    </ModalBox>
  );
};
