import type { DocumentType } from '@/modules/files/types/document-types';

export type CommunicationThreadAttachment = {
  id: number;
  name: string;
  linkedAttachmentId?: number;
  documentType?: DocumentType;
  uploadedAt?: Date;
};
export type CommunicationThread = {
  id: number;
  type: 'contract' | 'followUpRequest' | 'upload';
  insurerName?: string;
  tariffName?: string;
  message?: string;
  attachments?: CommunicationThreadAttachment[];
};

export type CommunicationModalPreset =
  | 'contractsToSign'
  | 'followUpRequests'
  | 'allDocuments';
