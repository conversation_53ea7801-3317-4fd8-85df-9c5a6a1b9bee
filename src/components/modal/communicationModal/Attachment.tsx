import DeleteIcon from '@mui/icons-material/Delete';
import DownloadIcon from '@mui/icons-material/DownloadOutlined';
import UploadIcon from '@mui/icons-material/Upload';
import { Button as Mui<PERSON>utton, Stack, Tooltip, Typography } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { type FC, type ReactElement } from 'react';

import type {
  CommunicationThread,
  CommunicationThreadAttachment as TAttachment,
} from '@/components/modal/communicationModal/domain';
import { useTRPC } from '@/trpc/client';

type AttachmentProps = TAttachment & {
  threadType: CommunicationThread['type'];
  isLinkedAttachment?: boolean;
  caseId: number;
};
export const Attachment: FC<AttachmentProps> = ({
  threadType,
  isLinkedAttachment,
  caseId,
  ...attachment
}) => {
  const t = useTranslations('communicationModal');
  const trpc = useTRPC();

  const { mutateAsync: getFileDownloadUrl } = useMutation(
    trpc.files.getFileDownloadUrl.mutationOptions({
      onSuccess: (downloadUrl) => {
        if (downloadUrl) {
          // Open file in new tab/window
          window.open(downloadUrl, '_blank');
        }
      },
      onError: (error) => {
        console.error('Download error:', error);
        alert('Failed to download file. Please try again.');
      },
    })
  );

  const handleDownload = async () => {
    await getFileDownloadUrl({
      attachmentId: attachment.id,
      caseId: caseId,
      fileName: attachment.name,
    });
  };

  const resolveAdditionalButton = () => {
    if (isLinkedAttachment) {
      return (
        <Button tooltip={t('deleteButtonTooltip')} icon={<DeleteIcon />} />
      );
    }
    if (threadType !== 'upload') {
      return (
        <Button
          tooltip={t(`uploadButtonTooltip.${threadType}`)}
          icon={<UploadIcon />}
        />
      );
    }
  };

  return (
    <Stack
      sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Typography
        sx={{
          fontSize: '17px',
          lineHeight: '18px',
          cursor: 'pointer',
          ...(isLinkedAttachment
            ? {
                color: 'var(--teal)',
                borderBottom: '1px solid var(--teal)',
                fontWeight: 600,
              }
            : { borderBottom: '1px solid black' }),
        }}
      >
        {attachment.name} test
      </Typography>

      <Stack direction="row" gap="10px">
        {resolveAdditionalButton()}
        <Button
          tooltip={t('downloadButtonTooltip')}
          icon={<DownloadIcon />}
          onClick={handleDownload}
        />
      </Stack>
    </Stack>
  );
};

type ButtonProps = {
  tooltip: string;
  icon: ReactElement;
  onClick?: () => void;
};
const Button = ({ icon, tooltip, onClick }: ButtonProps) => {
  return (
    <Tooltip placement="top" title={tooltip}>
      <MuiButton
        sx={{
          minWidth: 'unset',
          width: '35px',
          height: '35px',
        }}
        onClick={onClick}
      >
        {icon}
      </MuiButton>
    </Tooltip>
  );
};
