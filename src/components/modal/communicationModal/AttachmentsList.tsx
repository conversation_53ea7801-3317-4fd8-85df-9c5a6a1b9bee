import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { Box, Divider } from '@mui/material';
import { useTranslations } from 'next-intl';
import { type FC, Fragment } from 'react';

import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import { Attachment } from '@/components/modal/communicationModal/Attachment';
import type {
  CommunicationThread,
  CommunicationThreadAttachment,
} from '@/components/modal/communicationModal/domain';

type AttachmentsListProps = {
  attachments: CommunicationThreadAttachment[];
  threadType: CommunicationThread['type'];
  caseId: number;
};
export const AttachmentsList: FC<AttachmentsListProps> = ({
  attachments,
  threadType,
  caseId,
}) => {
  const t = useTranslations('communicationModal');

  const attachmentsMap = attachments.reduce(
    (acc, attachment) => {
      return {
        ...acc,
        [attachment.id]: attachment,
      };
    },
    {} as Record<number, CommunicationThreadAttachment>
  );

  return attachments.map((attachment, index) => {
    const isLast = index === attachments!.length - 1;
    const { linkedAttachmentId } = attachment;
    const linkedAttachment = linkedAttachmentId
      ? attachmentsMap[linkedAttachmentId]
      : null;

    if (threadType === 'upload') {
      return (
        <Fragment key={attachment.id}>
          <Attachment threadType={threadType} {...attachment} caseId={caseId} />
          {!isLast && <Divider sx={{ background: 'rgba(0,0,0,0.1)' }} />}
        </Fragment>
      );
    }

    return (
      <Box
        key={`${attachment.id} ${index}`}
        sx={{
          borderBottom: '2px solid rgba(0,0,0,0.4)',
          borderTop: '2px solid rgba(0,0,0,0.4)',
          position: 'relative',
          padding: '0 10px 0 20px',
          marginBottom: '8px',
          background: 'rgb(246,246,246)',
        }}
      >
        <Attachment threadType={threadType} {...attachment} caseId={caseId} />
        <Divider sx={{ background: 'rgba(0,0,0,0.1)' }} />
        {linkedAttachment ? (
          <Attachment
            threadType={threadType}
            {...linkedAttachment}
            caseId={caseId}
            isLinkedAttachment
          />
        ) : (
          <Box sx={{ height: '51px', display: 'flex', alignItems: 'center' }}>
            <KvcPlainTextButton
              onClick={() => console.error('Not implemented')}
              startIcon={
                <AddCircleOutlineIcon
                  sx={{
                    color: 'var(--teal)',
                    marginRight: '3px',
                    position: 'relative',
                    top: '-1px',
                  }}
                />
              }
              sx={{ fontSize: '16px', fontWeight: '400', marginLeft: '-10px' }}
            >
              {t(`uploadLinkedAttachment.${threadType}`)}
            </KvcPlainTextButton>
          </Box>
        )}
        <Bracket />
      </Box>
    );
  });
};

const Bracket = () => {
  return (
    <Box
      sx={{
        position: 'absolute',
        left: '2px',
        top: '24%',
        border: '2px solid rgba(0,0,0,0.3)',
        clipPath: `polygon(
            0 0,
            7px 0,
            7px 5px,
            5px 5px,
            5px calc(100% - 5px),
            7px calc(100% - 5px),
            7px 100%,
            0 100%
        )`,
        height: '52%',
        width: '50px',
      }}
    />
  );
};
