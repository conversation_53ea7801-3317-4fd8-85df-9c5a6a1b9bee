import type {
  CommunicationModalPreset,
  CommunicationThread,
} from '@/components/modal/communicationModal/domain';

type PresetConfig = {
  selectThreads: (all: CommunicationThread[]) => CommunicationThread[];
};
export const presets: Record<CommunicationModalPreset, PresetConfig> = {
  followUpRequests: {
    selectThreads: (all) =>
      all.filter((thread) => thread.type === 'followUpRequest'),
  },
  contractsToSign: {
    selectThreads: (all) => all.filter((thread) => thread.type === 'contract'),
  },
  allDocuments: {
    selectThreads: (all) =>
      all.filter((thread) =>
        ['followUpRequest', 'upload'].includes(thread.type)
      ),
  },
};
