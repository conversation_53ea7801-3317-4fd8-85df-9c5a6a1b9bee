import { useQuery } from '@tanstack/react-query';

import { type CommunicationModalPreset } from '@/components/modal/communicationModal/domain';
import { presets } from '@/components/modal/communicationModal/presets';
import { useTRPC } from '@/trpc/client';

export const useThreads = (
  caseId: number,
  presetId: CommunicationModalPreset,
  insurerName?: string
) => {
  const trpc = useTRPC();
  const { data, isLoading, error } = useQuery(
    trpc.messages.getCommunicationThreads.queryOptions({ caseId })
  );
  console.log(data)
  const threads = data?.threads ?? [];

  const { selectThreads } = presets[presetId];
  const filteredThreads = selectThreads(threads);

  // Filter by insurerName if provided (for individual police view)
  let finalThreads = filteredThreads;
  if (insurerName) {
    finalThreads = filteredThreads.filter(
      (thread) => thread.insurerName === insurerName
    );
  }

  return {
    threads: finalThreads,
    isLoading,
    error,
  };
};
