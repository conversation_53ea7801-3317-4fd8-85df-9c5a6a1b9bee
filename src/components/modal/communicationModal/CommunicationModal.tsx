import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import { Box, CircularProgress, Stack, Typography } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { type MouseEventHandler, useState } from 'react';

import { KvcPlainTextButton } from '@/components/buttons/plainTextButton';
import { type CommunicationModalPreset } from '@/components/modal/communicationModal/domain';
import { Thread } from '@/components/modal/communicationModal/Thread';
import { useThreads } from '@/components/modal/communicationModal/useThreads';
import { KvModal } from '@/components/modal/modal';
import { DocumentUpload } from '@/components/upload/DocumentUpload';
import { useTRPC } from '@/trpc/client';

type Props = {
  preset: CommunicationModalPreset;
  customerName: string;
  caseId: number;
  caseType: string;
  showUploadDocumentButton?: boolean;
  insurerName?: string;
  onDocumentUploaded?: () => Promise<void>;
};

export function CommunicationModal({
  preset: presetId,
  customerName,
  caseType,
  caseId,
  insurerName,
}: Props) {
  const t = useTranslations('communicationModal');
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { threads, isLoading, error } = useThreads(
    caseId,
    presetId,
    insurerName
  );
  const [showUploadModal, setShowUploadModal] = useState(false);

  const handleUploadDocumentClick: MouseEventHandler<HTMLButtonElement> = (
    e
  ) => {
    e.stopPropagation();
    setShowUploadModal(true);
  };

  const handleUploadSuccess = async () => {
    // Close the upload modal
    setShowUploadModal(false);

    // Invalidate and refetch the communication threads
    await queryClient.invalidateQueries({
      queryKey: trpc.messages.getCommunicationThreads.queryKey({ caseId }),
    });

    // Invalidate cases list to update document count
    await queryClient.invalidateQueries({
      queryKey: trpc.cases.getManyWithGroup.queryKey(),
    });

    await queryClient.invalidateQueries({
      queryKey: trpc.cases.getWithAllRelations.queryKey(),
    });
  };

  const handleCloseUploadModal = () => {
    setShowUploadModal(false);
  };

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '50vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
    >
      <Box
        sx={{
          width: '100%',
          borderBottom: '1px solid rgba(0,0,0,0.2)',
          display: 'flex',
          justifyContent: 'center',
          paddingBottom: '25px',
          color: 'var(--teal)',
        }}
      >
        <Typography sx={{ fontWeight: 'bold', fontSize: '18px' }}>
          {customerName}
        </Typography>
        <Typography sx={{ margin: '0 10px', fontSize: '18px' }}>/</Typography>
        <Typography sx={{ fontSize: '18px' }}>{caseType}</Typography>
      </Box>

      <Typography
        variant="h3"
        sx={{
          marginTop: '30px',
          color: 'var(--teal)',
          fontWeight: '600',
          fontSize: '24px',
        }}
      >
        {t(`title.${presetId}`)}
        {insurerName && (
          <Typography
            component="span"
            sx={{
              fontSize: '18px',
              fontWeight: '400',
              marginLeft: '10px',
            }}
          >
            | {insurerName}
          </Typography>
        )}
      </Typography>

      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: '30px',
            height: '50vh',
          }}
        >
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box
          sx={{
            marginTop: '30px',
            width: '75%',
            textAlign: 'center',
          }}
        >
          <Typography color="error">
            {t('error.loadingThreads', {
              defaultValue:
                'Error loading communication threads. Please try again.',
            })}
          </Typography>
        </Box>
      ) : (
        <Stack
          sx={{
            marginTop: '30px',
            width: '75%',
            height: '50vh',
            overflowY: 'auto',
            overflowX: 'hidden',
            paddingRight: '10px',
          }}
        >
          {threads.map((thread, index) => {
            return (
              <Thread
                key={thread.id}
                isLast={index === threads.length - 1}
                caseId={caseId}
                {...thread}
              />
            );
          })}
        </Stack>
      )}

      {presetId === 'allDocuments' && (
        <KvcPlainTextButton
          onClick={handleUploadDocumentClick}
          startIcon={
            <AddCircleOutlineIcon
              sx={{
                color: 'var(--teal)',
                marginRight: 1,
              }}
            />
          }
          sx={{ marginTop: '20px', fontSize: '17px' }}
        >
          {t('button.uploadDocument')}
        </KvcPlainTextButton>
      )}

      {showUploadModal && (
        <KvModal onClose={handleCloseUploadModal}>
          <DocumentUpload
            caseId={caseId}
            insurerName={insurerName}
            onUploadSuccess={handleUploadSuccess}
          />
        </KvModal>
      )}
    </Box>
  );
}
