import { <PERSON>, <PERSON><PERSON>, Divider, Stack, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { type FC } from 'react';

import { AttachmentsList } from '@/components/modal/communicationModal/AttachmentsList';
import { type CommunicationThread } from '@/components/modal/communicationModal/domain';
import { getDocumentTypeTranslationKey } from '@/modules/files/types/document-types';

type ThreadProps = CommunicationThread & {
  isLast?: boolean;
  caseId: number;
};

export const Thread: FC<ThreadProps> = ({ isLast, caseId, ...thread }) => {
  const t = useTranslations('communicationModal');
  const tGlobal = useTranslations();

  const { attachments, insurerName, tariffName, message } = thread;
  const attachmentsExist = attachments && attachments.length > 0;

  // Get document type and upload date from first attachment for upload threads
  const firstAttachment = attachments?.[0];
  const documentType = firstAttachment?.documentType;
  const uploadedAt = firstAttachment?.uploadedAt;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  return (
    <Stack
      sx={{
        padding: '20px',
        borderRadius: '8px',
        background: 'var(--gray-background)',
        width: '100%',
        ...(isLast ? {} : { marginBottom: '30px' }),
      }}
    >
      <Box
        sx={{
          display: 'flex',
          marginBottom: '15px',
        }}
      >
        <Typography sx={{ fontSize: '17px' }}>
          {t(`threadType.${thread.type}`)}
        </Typography>
        {thread.type !== 'upload' && (
          <>
            <Typography sx={{ margin: '0 6px', fontSize: '17px' }}>
              |
            </Typography>
            <Typography sx={{ fontWeight: '600', fontSize: '17px' }}>
              {insurerName}
            </Typography>
            <Typography sx={{ margin: '0 6px', fontSize: '17px' }}>
              |
            </Typography>
            <Typography sx={{ fontSize: '17px' }}>{tariffName}</Typography>
          </>
        )}
      </Box>

      {message && (
        <Typography sx={{ fontSize: '15px', margin: '-5px 0 20px' }}>
          <span
            style={{
              color: 'var(--teal)',
              fontWeight: 'bold',
              marginRight: '5px',
            }}
          >
            {t('messageFromAdminPrefix')}
          </span>
          {message}
        </Typography>
      )}

      <Divider sx={{ background: 'rgba(0,0,0,0.1)' }} />

      {thread.type === 'upload' && (documentType || uploadedAt) && (
        <Stack sx={{ marginTop: '15px', marginBottom: '10px', gap: '5px' }}>
          {documentType && (
            <Typography sx={{ fontSize: '15px', fontWeight: '600' }}>
              {tGlobal(getDocumentTypeTranslationKey(documentType))}
            </Typography>
          )}
        </Stack>
      )}

      {attachmentsExist && (
        <AttachmentsList
          threadType={thread.type}
          attachments={attachments}
          caseId={caseId}
        />
      )}

      {uploadedAt && (
        <Typography sx={{ fontSize: '14px', color: 'rgba(0,0,0,0.6)' }}>
          {formatDate(uploadedAt)}
        </Typography>
      )}

      {!attachmentsExist && message && (
        <Button
          sx={{
            marginTop: '20px',
            alignSelf: 'center',
            px: '70px',
          }}
        >
          Respond
        </Button>
      )}
    </Stack>
  );
};
