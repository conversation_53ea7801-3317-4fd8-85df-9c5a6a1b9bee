"use client";

import { FC, ReactNode } from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Typography,
  Stack,
} from "@mui/material";

type BaseModalProps = {
  open: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
};

export const BaseModal: FC<BaseModalProps> = ({ open, onClose, title, children }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          p: 2,
        },
      }}
    >
      <DialogTitle>
        <Typography variant="h5" color="primary" align="center">
          {title}
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Stack gap={3} alignItems="center" sx={{ mt: 2 }}>
          {children}
        </Stack>
      </DialogContent>
    </Dialog>
  );
};
