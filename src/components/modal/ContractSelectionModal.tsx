// src/components/modals/ContractSelectionModal.tsx
import { Button, Tooltip } from '@mui/material';
import { useRouter } from 'next/router';
import { formatLabel } from '../../utils/keyFormatter';
import ModalBox from './BoxModal';

interface ContractSelectionModalProps {
    open: boolean;
    onClose: () => void;
    customerNumber: string;
    mode: 'contract' | 'offer' | null;
}

const contractTypes = [
    'hausrat', 'wohngebaeude', 'tierhalterhaftpflicht', 'privathaftpflicht',
    'haus_und_grundbesitzerhaftpflicht', 'bauleistung', 'bauherrenhaftpflicht',
    'geschaeftsversicherung', 'gebaeudeversicherung', 'betriebshaftpflicht', 'unfallversicherung'
];

const ContractSelectionModal: React.FC<ContractSelectionModalProps> = ({ open, onClose, customerNumber, mode }) => {
    const router = useRouter();

    const handleSelection = (type: string) => {
        router.push(`/customer/${customerNumber}/${mode === "contract" ? "createContract" : "createOffer"}/${type}`);
        onClose();
    };

    return (
        <ModalBox
            open={open}
            onClose={onClose}
            title={mode === "contract" ? "Vertrag erstellen" : "Angebot erstellen"}
        >
            <div className="grid grid-cols-3 gap-4">
                {contractTypes.map(type => (
                    <Tooltip key={type} title={`${formatLabel(type)} ${mode === "contract" ? "abschließen" : "anbieten"}`}>
                        <Button
                            onClick={() => handleSelection(type)}
                            variant="contained"
                            color="secondary"
                            className="text-sm h-16 w-full py-2 px-4"
                            sx={{ textTransform: 'none' }}
                        >
                            {formatLabel(type)}
                        </Button>
                    </Tooltip>
                ))}
            </div>
        </ModalBox>
    );
};

export default ContractSelectionModal;
