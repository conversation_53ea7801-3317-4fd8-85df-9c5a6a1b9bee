// src/components/modals/RevisionModal.tsx
import { RevisionData } from "@/types";
import { formatDate } from "@/utils/dateUtils";
import { formatField } from "@/utils/keyFormatter";
import { Box, Stack, Typography, Divider, Button, Tooltip } from "@mui/material";
import { Fragment, useState } from "react";
import { History } from "@mui/icons-material";
import ModalBox from "./BoxModal";

interface RevisionsModalProps {
  type: string;
  number: string;
}

const RevisionsModal: React.FC<RevisionsModalProps> = ({ type, number }) => {
  const [revisionData, setRevisionData] = useState<RevisionData[]>();
  const [openRevisions, setOpenRevisions] = useState(false);
  const handleRevisionsClose = () => setOpenRevisions(false);

  async function handleGetRevisions() {
    const token = localStorage.getItem("jwt") || "";
    const revisionsResponse = await fetch(`/api/${type}/get/revisions/${number}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Baerer ${token}`,
      },
    });

    if (!revisionsResponse.ok) {
      throw new Error("Failed to fetch data");
    }

    const revisionData = await revisionsResponse.json();
    if (!revisionData[0]) {
      alert("Die Historie ist leer.");
      return;
    }
    setRevisionData(revisionData);
    setOpenRevisions(true);
  }

  const renderDelta = (delta: any, indentLevel = 0, parentKey = ""): JSX.Element[] => {
    const elements: JSX.Element[] = [];

    Object.entries(delta).forEach(([key, value]) => {
      if (["publishedAt", "updatedAt", "_t"].includes(key)) return;

      const isArrayDeletion = key.startsWith("_") && !isNaN(Number(key.slice(1)));
      const isNumericKey = !isNaN(Number(key));
      const indentStyle = { pl: indentLevel * 2 };

      if (isArrayDeletion && Array.isArray(value) && value.length === 3 && value[2] === 0) {
        const deletedItem = value[0];
        const index = key.slice(1);

        elements.push(
          <Box key={`deleted-${index}`} sx={{ pl: indentLevel * 2 }}>
            <Typography fontWeight="bold" color="error">
              🗑️ Eintrag {Number(index) + 1} gelöscht
            </Typography>
            <Stack spacing={1} sx={{ pl: 2 }}>
              {Object.entries(deletedItem).map(([fieldKey, fieldValue]) => (
                <Typography key={fieldKey}>
                  {formatField(fieldKey)}: {String(fieldValue) || <i>(leer)</i>}
                </Typography>
              ))}
            </Stack>
          </Box>
        );
      } else if (Array.isArray(value)) {
        if (value.length === 2) {
          if (!isNumericKey) {
            elements.push(
              <Box key={`${key}-${indentLevel}`} sx={{ pl: indentLevel * 2 }}>
                {parentKey && (
                  <Typography fontWeight="bold" color="success.main">
                    Eintrag {parentKey}
                  </Typography>
                )}
                <Typography fontWeight="bold" sx={indentStyle}>
                  {formatField(key)}:
                </Typography>
                <Stack spacing={1} sx={{ pl: indentLevel * 2 + 2 }}>
                  <Typography>Alt: {String(value[0])}</Typography>
                  <Typography>Neu: {String(value[1])}</Typography>
                </Stack>
              </Box>
            );
          } else {
            elements.push(
              <Stack key={`${key}-${indentLevel}`} spacing={1} sx={{ pl: indentLevel * 2 + 2 }}>
                <Typography>Alt: {String(value[0])}</Typography>
                <Typography>Neu: {String(value[1])}</Typography>
              </Stack>
            );
          }
        } else if (value.length === 1 && typeof value[0] === "object") {
          elements.push(
            <Box key={`added-${key}-${indentLevel}`} sx={{ pl: indentLevel * 2 }}>
              <Typography fontWeight="bold" color="success.main">
                🆕 Neuer Eintrag {Number(key) + 1}
              </Typography>
              <Stack spacing={1} sx={{ pl: 2 }}>
                {Object.entries(value[0]).map(([fieldKey, fieldValue]) => (
                  <Typography key={fieldKey}>
                    {formatField(fieldKey)}: {String(fieldValue) || <i>(leer)</i>}
                  </Typography>
                ))}
              </Stack>
            </Box>
          );
        }
      } else if (typeof value === "object" && value !== null) {
        if (!isNumericKey) {
          elements.push(
            <Typography key={key} fontWeight="bold" sx={indentStyle}>
              {formatField(key)}:
            </Typography>
          );
        }
        elements.push(...renderDelta(value, indentLevel + 1, key));
      }
    });

    return elements;
  };

  return (
    <>
      <Tooltip title={`Einen Überblick über die Historie bekommen`}>
        <Button onClick={handleGetRevisions} className="w-full" variant="contained">
          <History className="mr-2" />
          Historie anzeigen
        </Button>
      </Tooltip>

      <ModalBox open={openRevisions} onClose={handleRevisionsClose} title="Historie" maxWidth="md">
        <Box sx={{ maxHeight: "70vh", overflowY: "auto", pr: 2 }}>
          {revisionData?.map((revision, index) => (
            <Fragment key={revision.primaryId + index}>
              <Stack spacing={1} mt={2} mb={2}>
                <Typography fontWeight="bold">Änderungsdatum: {formatDate(revision.createdAt)}</Typography>
                <Typography fontWeight="bold">Maklernummer: {revision.agentId}</Typography>
                {renderDelta(revision.delta)}
              </Stack>
              <Divider />
            </Fragment>
          ))}
        </Box>
      </ModalBox>
    </>
  );
};

export default RevisionsModal;
