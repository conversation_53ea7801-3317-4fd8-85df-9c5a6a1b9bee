// src/components/modals/RevisionModal.tsx
import DownloadIcon from '@mui/icons-material/Download';
import { Box, Button, Stack, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';

import CarouselControls from '@/components/CarouselControls';
import { reportTimelineCommon } from '@/components/common';
import Markdown from '@/components/Markdown';
import {
  type ReportAttachment,
  type ReportTimelineEntry,
} from '@/generated/prisma-postgres';
import { formatDate } from '@/utils/dateUtils';
import downloadFile from '@/utils/downloadFile';
import { getToken } from '@/utils/getToken';
import { HttpClient } from '@/utils/HttpClient';

import ModalBox from './BoxModal';

interface ReportTimelineDetailsModalProps {
  reportNumber: string;
  onClose: () => void;
  entry: ReportTimelineEntry;
  goToEntry: (options: { index?: number; delta?: number }) => void;
  disablePreviousEntryButton: boolean;
  disableNextEntryButton: boolean;
}

const ReportTimelineDetailsModal: React.FC<ReportTimelineDetailsModalProps> = ({
  entry,
  onClose,
  goToEntry,
  disablePreviousEntryButton,
  disableNextEntryButton,
  reportNumber,
}) => {
  const [attachments, setAttachments] = useState<ReportAttachment[]>([]);

  useEffect(() => {
    const loadAttachments = async () => {
      try {
        const token = getToken.onClient();
        const httpClient = new HttpClient(token);

        const loadedAttachments = await httpClient.request(
          `/api/report/${reportNumber}/attachments?related_to=timeline_entry&timeline_entry_id=${entry.id}`
        );
        setAttachments(loadedAttachments);
      } catch (error) {
        console.error(error);
      }
    };

    loadAttachments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entry.id]);

  const handleAttachmentDownload = (attachment: ReportAttachment) => {
    const name = attachment.name!;
    downloadFile(
      name,
      `/api/report/${reportNumber}/attachments/${attachment.id}`,
      () => alert('Während des Downloads ist ein Fehler aufgetreten')
    );
  };
  const handleNavigation: typeof goToEntry = (options) => {
    setAttachments([]);
    goToEntry(options);
  };

  return (
    <ModalBox open={true} onClose={onClose} title="Details">
      <Box padding="0 10px 10px">
        <Stack direction="row" spacing={2} sx={{ alignItems: 'center' }}>
          <Box
            sx={(theme) => ({
              borderRadius: '50%',
              padding: '5px',
              background: theme.palette.primary.main,
            })}
          >
            {reportTimelineCommon.getAuthorIcon(entry.authorType!)}
          </Box>
          <Stack>
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography sx={{ fontWeight: '600', fontSize: '18px' }}>
                {reportTimelineCommon.getAuthorLabel(entry.authorType!)}
              </Typography>
              {reportTimelineCommon.getEntryTypeMarker(entry.entryType!)}
            </Stack>
            <Typography sx={{ fontSize: '14px', lineHeight: '1' }}>
              {formatDate(entry.timestamp!)}
            </Typography>
          </Stack>
        </Stack>
        <Typography
          sx={{ fontWeight: '600', fontSize: '17px', marginTop: '20px' }}
        >
          <Markdown>{entry.title}</Markdown>
        </Typography>
        {entry.content && (
          <Typography sx={{ marginTop: '20px' }}>
            <Markdown>{entry.content}</Markdown>
          </Typography>
        )}
        {entry.metadata && (
          <>
            <Box
              sx={(theme) => ({
                borderBottom: '1px',
                backgroundColor: theme.palette.secondary.main,
                height: '1px',
                marginTop: '20px',
              })}
            />
            <Typography sx={{ marginTop: '10px' }}>{entry.metadata}</Typography>
          </>
        )}
        {attachments.length > 0 && (
          <Stack spacing={1} sx={{ marginTop: '30px', alignItems: 'start' }}>
            {attachments.map((item) => {
              return (
                <Button
                  key={item.id}
                  onClick={() => handleAttachmentDownload(item)}
                  variant="contained"
                  size="small"
                  sx={(theme) => ({
                    gap: '10px',
                    ['&:hover']: {
                      background: theme.palette.primary.main,
                      opacity: 0.9,
                    },
                  })}
                >
                  <DownloadIcon fontSize="small" />
                  {item.name}
                </Button>
              );
            })}
          </Stack>
        )}
        <CarouselControls
          sx={{ marginTop: '30px' }}
          disablePrevious={disablePreviousEntryButton}
          disableNext={disableNextEntryButton}
          goPrevious={() => handleNavigation({ delta: -1 })}
          goNext={() => handleNavigation({ delta: 1 })}
          goMostRecent={() => handleNavigation({ index: 0 })}
        />
      </Box>
    </ModalBox>
  );
};

export default ReportTimelineDetailsModal;
