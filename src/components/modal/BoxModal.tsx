import { ReactNode } from "react";
import {Dialog, IconButton, Tooltip, Typography, Box, DialogContent, dialogClasses} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";

interface ModalBoxProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}
const ModalBox: React.FC<ModalBoxProps> = ({ open, onClose, title, children, maxWidth = 'sm' }) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth={maxWidth} fullWidth>
      <Box px={4} pt={3} display="flex" justifyContent="space-between" alignItems="center" >
        <Typography variant="h6" color="primary">
          {title}
        </Typography>
        <Tooltip title="Schließen">
          <IconButton onClick={onClose} color="primary">
            <CloseIcon />
          </IconButton>
        </Tooltip>
      </Box>
      <DialogContent>
        <Box pt={2}>
          {children}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ModalBox;
