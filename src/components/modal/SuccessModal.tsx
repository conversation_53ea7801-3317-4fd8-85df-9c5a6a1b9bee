"use client";

import { FC } from "react";
import { Typo<PERSON>, But<PERSON> } from "@mui/material";
import { useRouter } from "next/router";
import { BaseModal } from "./BaseModal";

type SuccessModalProps = {
  open: boolean;
  onClose: () => void;
  title: string;
  message: string;
  redirectPath?: string; // optional, Default "/"
};

export const SuccessModal: FC<SuccessModalProps> = ({
  open,
  onClose,
  title,
  message,
  redirectPath = "/",
}) => {
  const router = useRouter();

  const handleNextTip = () => {
    onClose();
    router.push(redirectPath);
  };

  return (
    <BaseModal open={open} onClose={onClose} title={title}>
      <Typography variant="body1" align="center">
        {message}
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={handleNextTip}
      >
        Weiteren Tipp geben
      </Button>
    </BaseModal>
  );
};
