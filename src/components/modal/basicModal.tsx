'use client';
import { type Theme } from '@emotion/react';
import LocalPoliceIcon from '@mui/icons-material/LocalPolice';
import RecommendIcon from '@mui/icons-material/Recommend';
import SellIcon from '@mui/icons-material/Sell';
import SentimentNeutralIcon from '@mui/icons-material/SentimentNeutral';
import SentimentSatisfiedAltIcon from '@mui/icons-material/SentimentSatisfiedAlt';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import { Stack, type SxProps, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';

import { intlTranslationRichHelper } from '@/utils/intlTranslationRichHelper';

import { KvModal } from './modal';

export interface BasicModalConfiguration {
  headline: string;
  text: string;
  iconId: string;
  iconColor: string;
  onClose?: () => void;
}

export interface GetModalByIdProps {
  modalId: string;
  onClose?: () => void;
}

export function GetModalById(props: GetModalByIdProps) {
  const t = useTranslations();
  if (typeof props.modalId === 'string' && props.modalId.trim().length > 0) {
    const modalConfigurationJsonPath = `modal_configurations.${props.modalId}`;
    const rawModalConfiguration = t.raw(modalConfigurationJsonPath);
    if (typeof rawModalConfiguration !== 'string') {
      if (
        typeof rawModalConfiguration.headline === 'string' &&
        typeof rawModalConfiguration.text === 'string' &&
        typeof rawModalConfiguration.iconId === 'string' &&
        typeof rawModalConfiguration.iconColor === 'string'
      ) {
        const modalConfiguration =
          rawModalConfiguration as BasicModalConfiguration;
        modalConfiguration.onClose = props.onClose;
        return (
          <BasicModal
            modalConfigurationPath={`modal_configurations.${props.modalId}`}
            onClose={props.onClose}
          ></BasicModal>
        );
      }
    }
  }
  return null;
}

interface BasicMoalProps {
  modalConfigurationPath: string;
  onClose?: () => void;
}

export function BasicModal(props: BasicMoalProps) {
  const t = useTranslations();
  return (
    <KvModal
      onClose={() => {
        props.onClose?.();
      }}
    >
      <Stack
        direction="column"
        display="flex"
        flexWrap="wrap"
        justifyContent="center"
      >
        {GetIconById(t(`${props.modalConfigurationPath}.iconId`), {
          fontSize: 180,
          color: t(`${props.modalConfigurationPath}.iconColor`),
          alignContent: 'center',
          marginLeft: 'auto',
          marginRight: 'auto',
        })}

        <Typography
          color="var(--modal-headline-color)"
          aria-labelledby="modal-title"
          variant="h5"
          component="h1"
          fontWeight={700}
          textAlign="center"
        >
          {t.rich(
            `${props.modalConfigurationPath}.headline`,
            intlTranslationRichHelper
          )}
        </Typography>

        <Typography textAlign="center">
          {t.rich(
            `${props.modalConfigurationPath}.text`,
            intlTranslationRichHelper
          )}
        </Typography>
      </Stack>
    </KvModal>
  );
}

//
export function GetIconById(iconId: string, sx?: SxProps<Theme>) {
  if (iconId === 'SentimentNeutralIcon') {
    return <SentimentNeutralIcon sx={sx} />;
  } else if (iconId === 'SentimentVeryDissatisfiedIcon') {
    return <SentimentVeryDissatisfiedIcon sx={sx} />;
  } else if (iconId === 'SentimentSatisfiedAltIcon') {
    return <SentimentSatisfiedAltIcon sx={sx} />;
  } else if (iconId === 'LocalPoliceIcon') {
    return <LocalPoliceIcon sx={sx} />;
  } else if (iconId === 'RecommendIcon') {
    return <RecommendIcon sx={sx} />;
  } else if (iconId === 'SellIcon') {
    return <SellIcon sx={sx} />;
  }
  return null;
}
