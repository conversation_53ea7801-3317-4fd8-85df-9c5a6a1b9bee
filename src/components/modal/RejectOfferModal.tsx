"use client";

import { FC } from "react";
import { Button, Typography, Stack } from "@mui/material";
import { BaseModal } from "@/components/modal/BaseModal";
import {useTranslations} from "next-intl";

type RejectOfferModalProps = {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  productName: string;
};

export const RejectOfferModal: FC<RejectOfferModalProps> = ({ open, onClose, onConfirm, productName }) => {
  const t = useTranslations('customerDashboard.rejectOfferModal')

  return (
    <BaseModal open={open} onClose={onClose} title={t('title')}>
      <Typography variant="body1" align="center">
        {t.rich('questions', {
            strong: () => <strong>{productName}</strong> // TODO: can we pass the substitution params to t.rich?
        })}
      </Typography>
      <Stack direction="row" spacing={2}>
        <Button variant="outlined" color="inherit" onClick={onClose}>
            {t('secondaryButton')}
        </Button>
        <Button variant="contained" color="error" onClick={onConfirm}>
          {t('primaryButton')}
        </Button>
      </Stack>
    </BaseModal>
  );
};
