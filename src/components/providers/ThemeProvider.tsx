'use client';

import CssBaseline from '@mui/material/CssBaseline';
import { ThemeProvider } from '@mui/material/styles';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
import type { ReactNode } from 'react';

import muiTheme from '@/utils/muiTheme';

interface MUIThemeProviderProps {
  children: ReactNode;
}

/**
 * MUI Theme Provider for Next.js App Router
 *
 * This component provides the necessary MUI theme configuration for the App Router.
 * It includes:
 * - AppRouterCacheProvider for proper SSR support with CSS layers enabled
 * - ThemeProvider with custom theme
 * - CssBaseline for consistent baseline styles
 */
export default function MUIThemeProvider({ children }: MUIThemeProviderProps) {
  return (
    <AppRouterCacheProvider>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </AppRouterCacheProvider>
  );
}
