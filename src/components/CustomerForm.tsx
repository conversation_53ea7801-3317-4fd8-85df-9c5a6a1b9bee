// src/components/CustomerForm.tsx
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

import { apiFetch } from '@/utils/apiFetch';

import StaticBox from './box/StaticBox';

interface CustomerFormProps {
  agentNumber?: string;
  agencyNumber?: string;
  editMode?: boolean;
  customerNumber?: string;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  editMode = false,
  customerNumber,
}) => {
  const session = useSession();
  const [formData, setFormData] = useState({
    salutation: '',
    namePrefix: '',
    firstName: '',
    lastName: '',
    careOf: '',
    street: '',
    houseNumber: '',
    postalCode: '',
    city: '',
    email: '',
    agentNumber: session.data?.agentNumber || '',
    agencyNumber: session.data?.agencyNumber || '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (editMode && customerNumber) {
      apiFetch(`/api/customers/${customerNumber}`, {
        method: 'GET',
        raw: true,
      })
        .then((res) => {
          if (!res.ok) throw new Error('Fehler beim Laden des Kunden');
          return res.json();
        })
        .then((data) => setFormData(data))
        .catch((err) => {
          console.error(err);
          alert('Fehler beim Laden der Kundendaten.');
        });
    }
  }, [editMode, customerNumber]);

  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      agentNumber: session.data?.agentNumber || '',
      agencyNumber: session.data?.agencyNumber || '',
    }))
  }, [session]);

  function handleChange(event: React.ChangeEvent<HTMLInputElement>): void;
  function handleChange(
    event: SelectChangeEvent<string>,
    child: React.ReactNode
  ): void;

  // Implementation signature
  function handleChange(event: any) {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }

  const validateFields = () => {
    const validators = {
      postalCode: (val: string) => /^\d{5}$/.test(val),
      email: (val: string) =>
        val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(val),
      city: (val: string) => /^[A-Za-zÄÖÜäöüß .\\-]+$/.test(val),
      street: (val: string) => /^[A-Za-zÄÖÜäöüß .\\-]+$/.test(val),
    };
    const fieldLabels: { [key: string]: string } = {
      postalCode: 'Postleitzahl (maximal 5 Zahlen)',
      email: 'E-Mail-Adresse (gültige E-Mail-Adresse)',
      houseNumber: 'Hausnummer',
      firstName: 'Vorname',
      lastName: 'Nachname',
      city: 'Ort (darf keine Zahlen enthalten)',
      street: 'Straße (darf keine Zahlen enthalten)',
    };

    const invalidFields = Object.entries(validators)
      .filter(([key, validate]) => !validate((formData as any)[key]))
      .map(([key]) => fieldLabels[key]);

    if (invalidFields.length > 0) {
      alert(
        `Bitte überprüfen Sie folgende Felder:\n\n${invalidFields.join('\n')}`
      );
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateFields()) return;

    setIsSubmitting(true);

    try {
      const method = editMode ? 'PUT' : 'POST';
      const url = editMode
        ? `/api/customers/${customerNumber}`
        : '/api/customers/';

      const response = await apiFetch(url, {
        method,
        raw: true,
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const updatedCustomer = await response.json();
        alert(
          `Der Kunde wurde erfolgreich ${editMode ? 'aktualisiert' : 'angelegt'}.`
        );
        router.push(
          `/customer/${updatedCustomer.customerNumber || customerNumber}`
        );
      } else {
        alert(
          `Fehler beim ${editMode ? 'aktualisieren' : 'anlegen'} des Kunden.`
        );
      }
    } catch (error) {
      console.error('Die Eingaben konnten nicht übergeben werden:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSetAgent = async () => {
    const agentResponse = await apiFetch(
      `/api/agents/${formData.agentNumber}`,
      {
        method: 'GET',
        raw: true,
      }
    );

    if (!agentResponse.ok) {
      alert(
        'Die eingegebene Maklernummer ist ungültig. Bitte überprüfen Sie Ihre Eingabe.'
      );
      setFormData({
        ...formData,
        agentNumber: session.data?.agentNumber || '',
        agencyNumber: session.data?.agencyNumber || '',
      });
      return;
    }

    const agentData = await agentResponse.json();

    setFormData({
      ...formData,
      agencyNumber: agentData['agencyNumber'],
    });
  };

  return (
    <Box
      sx={{
        px: { xs: 2, md: 5 },
        py: 5,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        maxWidth: '768px',
        mx: 'auto',
      }}
    >
      <Typography variant="h4" textAlign="center" color="primary">
        {editMode ? `Kunde anpassen` : 'Kunde anlegen'}
      </Typography>
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 mx-auto relative">
          {!editMode && session.data?.roles.includes('asevo-admin') && (
            <StaticBox title="Maklerzuweisung">
              <div className="grid grid-cols-2 gap-2">
                <Tooltip title="Dem Kunden zugewiesener Makler.">
                  <TextField
                    label="Maklernummer"
                    type="text"
                    name="agentNumber"
                    value={formData.agentNumber}
                    onChange={handleChange}
                    className="w-full p-2"
                    required
                  />
                </Tooltip>
                <Tooltip title="Agenturnummer des zugewiesenen Maklers. (aktualisiert sich nach Zuweisung)">
                  <TextField
                    label="Agenturnummer"
                    type="text"
                    name="agencyNumber"
                    value={formData.agencyNumber}
                    onChange={handleChange}
                    placeholder="Agenturnummer"
                    className="w-full p-2"
                    disabled
                  />
                </Tooltip>
              </div>
              <div className="grid grid-cols-2 gap-2 mt-4">
                <Tooltip title="Weist dem Kunden den Makler zu.">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSetAgent}
                  >
                    Zuweisen
                  </Button>
                </Tooltip>
              </div>
            </StaticBox>
          )}
          <StaticBox title="Kundenangaben">
            <div className="grid grid-cols-2 gap-2">
              <Tooltip title="Wählen Sie die passende Anrede für den Kunden aus. Diese wird für die persönliche Ansprache in der Korrespondenz verwendet.">
                <FormControl required variant="outlined" className="w-full p-2">
                  <InputLabel>Anrede</InputLabel>
                  <Select
                    name="salutation"
                    label="Anrede"
                    value={formData.salutation}
                    onChange={handleChange}
                    required
                  >
                    <MenuItem value="Frau">Frau</MenuItem>
                    <MenuItem value="Herr">Herr</MenuItem>
                    <MenuItem value="Frau und Herr">Frau und Herr</MenuItem>
                    <MenuItem value="Frau und Frau">Frau und Frau</MenuItem>
                    <MenuItem value="Herr und Herr">Herr und Herr</MenuItem>
                    <MenuItem value="Firma">Firma</MenuItem>
                  </Select>
                </FormControl>
              </Tooltip>
              <Tooltip title="Geben Sie den Titel des Kunden ein (falls vorhanden).">
                <TextField
                  label="Titel"
                  type="text"
                  name="namePrefix"
                  value={formData.namePrefix}
                  onChange={handleChange}
                  className="w-full p-2"
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 pt-4">
              <Tooltip title="Geben Sie den Vornamen des Kunden ein. Dieser wird für die persönliche Ansprache genutzt.">
                <TextField
                  label="Vorname"
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
              <Tooltip title="Geben Sie den Nachnamen des Kunden ein. Dieser wird zur Identifizierung und Ansprache benötigt.">
                <TextField
                  type="text"
                  label="Nachname"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
              <Tooltip title="Falls die Post an eine andere Person oder Firma weitergeleitet werden soll, geben Sie hier den Zusatz ein, z. B. 'Max Mustermann'.">
                <TextField
                  type="text"
                  label="c/o"
                  name="careOf"
                  value={formData.careOf}
                  onChange={handleChange}
                  className="w-full p-2"
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 pt-4">
              <Tooltip title="Geben Sie die Straße der Kundenadresse ein. Achten Sie darauf, den vollständigen Straßennamen korrekt zu erfassen.">
                <TextField
                  type="text"
                  label="Straße"
                  name="street"
                  value={formData.street}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
              <Tooltip title="Geben Sie die Hausnummer der Kundenadresse ein. Wenn vorhanden, können auch zusätzliche Angaben wie Stockwerk oder Gebäudeteil notiert werden.">
                <TextField
                  type="text"
                  label="Hausnummer"
                  name="houseNumber"
                  value={formData.houseNumber}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
              <Tooltip title="Geben Sie die Postleitzahl (PLZ) der Kundenadresse ein. Diese Information ist für die korrekte Zustellung der Post nötig.">
                <TextField
                  type="text"
                  name="postalCode"
                  label="Postleitzahl"
                  value={formData.postalCode}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
              <Tooltip title="Geben Sie den Ort oder die Stadt der Kundenadresse ein. Dies ist der offizielle Wohn- oder Geschäftssitz des Kunden.">
                <TextField
                  type="text"
                  label="Ort"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  className="w-full p-2"
                  required
                />
              </Tooltip>
            </div>
            <div className="grid grid-cols-2 gap-2 pt-4">
              <Tooltip title="Geben Sie die E-Mail-Adresse des Kunden ein (falls unbekannt bitte Emailadresse des Maklers angeben).">
                <TextField
                  type="email"
                  label="Emailadresse"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full p-2"
                />
              </Tooltip>
            </div>
          </StaticBox>
          <div className="flex gap-4 justify-end">
            <Tooltip title="Ohne sichern zurück.">
              <Button
                type="button"
                onClick={() => window.history.back()}
                variant="contained"
              >
                Verwerfen
              </Button>
            </Tooltip>
            <Tooltip
              title={editMode ? 'Kunden aktulaiseren' : 'Neue Kunden anlegen'}
            >
              <Button
                type="submit"
                variant="contained"
                color="secondary"
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? editMode
                    ? 'Aktualisieren...'
                    : 'Anlegen...'
                  : editMode
                    ? 'Kunde aktualisieren'
                    : 'Kunde anlegen'}
              </Button>
            </Tooltip>
          </div>
        </div>
      </form>
    </Box>
  );
};

export default CustomerForm;
