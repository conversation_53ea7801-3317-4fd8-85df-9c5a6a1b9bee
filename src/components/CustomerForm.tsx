// src/components/CustomerForm.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { Button, FormControl, TextField, InputLabel, Select, MenuItem, SelectChangeEvent, Tooltip, Typography, Box } from '@mui/material';
import StaticBox from './box/StaticBox';

interface CustomerFormProps {
    agent_number?: string;
    agency_number?: string;
    edit_mode?: boolean;
    customer_number?: string;
}

const CustomerForm: React.FC<CustomerFormProps> = ({ agent_number, agency_number, edit_mode = false, customer_number }) => {
    const [formData, setFormData] = useState({
        salutation: '',
        name_prefix: '',
        first_name: '',
        last_name: '',
        care_of: '',
        street: '',
        house_number: '',
        postal_code: '',
        city: '',
        email: '',
        agent_number: agent_number || localStorage.getItem("agent_number") || "",
        agency_number: agency_number || localStorage.getItem("agencyNumber") || ""
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const router = useRouter();

    useEffect(() => {
        if (edit_mode && customer_number) {
            fetch(`/api/customer/${customer_number}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
            })
                .then((res) => {
                    if (!res.ok) throw new Error("Fehler beim Laden des Kunden");
                    return res.json();
                })
                .then((data) => setFormData(data))
                .catch((err) => {
                    console.error(err);
                    alert("Fehler beim Laden der Kundendaten.");
                });
        }
    }, [edit_mode, customer_number]);

    function handleChange(event: React.ChangeEvent<HTMLInputElement>): void;
    function handleChange(event: SelectChangeEvent<string>, child: React.ReactNode): void;

    // Implementation signature
    function handleChange(event: any, child?: React.ReactNode) {
        const { name, value } = event.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    }

    const validateFields = () => {
        const validators = {
            postal_code: (val: string) => /^\d{5}$/.test(val),
            email: (val: string) => val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/.test(val),
            city: (val: string) => /^[A-Za-zÄÖÜäöüß .\-]+$/.test(val),
            street: (val: string) => /^[A-Za-zÄÖÜäöüß .\-]+$/.test(val),
        };
        const fieldLabels: { [key: string]: string } = {
            postal_code: 'Postleitzahl (maximal 5 Zahlen)',
            email: 'E-Mail-Adresse (gültige E-Mail-Adresse)',
            house_number: 'Hausnummer',
            first_name: 'Vorname',
            last_name: 'Nachname',
            city: 'Ort (darf keine Zahlen enthalten)',
            street: 'Straße (darf keine Zahlen enthalten)',
        };

        const invalidFields = Object.entries(validators)
            .filter(([key, validate]) => !validate((formData as any)[key]))
            .map(([key]) => fieldLabels[key]);

        if (invalidFields.length > 0) {
            alert(`Bitte überprüfen Sie folgende Felder:\n\n${invalidFields.join('\n')}`);
            return false;
        }
        return true;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateFields()) return;

        setIsSubmitting(true);

        try {
            const method = edit_mode ? 'PUT' : 'POST';
            const url = edit_mode
                ? `/api/customer/${customer_number}`
                : '/api/customer/create';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Baerer ${localStorage.getItem("jwt") || ""}`
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                const updatedCustomer = await response.json();
                alert(`Der Kunde wurde erfolgreich ${edit_mode ? 'aktualisiert' : 'angelegt'}.`);
                router.push(`/customer/${updatedCustomer.customer_number || customer_number}`);
            } else {
                alert(`Fehler beim ${edit_mode ? 'aktualisieren' : 'anlegen'} des Kunden.`);
            }
        } catch (error) {
            console.error('Die Eingaben konnten nicht übergeben werden:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleSetAgent = async () => {
        const agentResponse = await fetch(`/api/agent/${formData.agent_number}`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${localStorage.getItem("jwt") || ""}`
            }
        })

        if (!agentResponse.ok) {
            alert("Die eingegebene Maklernummer ist ungültig. Bitte überprüfen Sie Ihre Eingabe.")
            setFormData({
                ...formData,
                agent_number: localStorage.getItem("agent_number") || "",
                agency_number: localStorage.getItem("agencyNumber") || ""
            });
            return
        }

        const agentData = await agentResponse.json()

        setFormData({
            ...formData,
            agency_number: agentData["agency_number"],
        });
    }

    return (
        <Box
            sx={{
                px: { xs: 2, md: 5 },
                py: 5,
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                maxWidth: '768px',
                mx: 'auto'
            }}>
            <Typography variant="h4" textAlign="center" color="primary" >
                {edit_mode ? `Kunde anpassen` : 'Kunde anlegen'}
            </Typography>
            <form onSubmit={handleSubmit}>
                <div className='space-y-4 mx-auto relative'>
                    {!edit_mode && localStorage.getItem("is_admin") == "true" &&
                        <StaticBox title="Maklerzuweisung">
                            <div className='grid grid-cols-2 gap-2'>
                                <Tooltip title="Dem Kunden zugewiesener Makler.">
                                    <TextField
                                        label="Maklernummer"
                                        type="text"
                                        name="agent_number"
                                        value={formData.agent_number}
                                        onChange={handleChange}
                                        className="w-full p-2"
                                        required
                                    />
                                </Tooltip>
                                <Tooltip title="Agenturnummer des zugewiesenen Maklers. (aktualisiert sich nach Zuweisung)">
                                    <TextField
                                        label="Agenturnummer"
                                        type="text"
                                        name="agency_number"
                                        value={formData.agency_number}
                                        onChange={handleChange}
                                        placeholder="Agenturnummer"
                                        className="w-full p-2"
                                        disabled
                                    />
                                </Tooltip>
                            </div>
                            <div className='grid grid-cols-2 gap-2 mt-4'>
                                <Tooltip title="Weist dem Kunden den Makler zu.">
                                    <Button variant="contained" color="primary" onClick={handleSetAgent}>
                                        Zuweisen
                                    </Button>
                                </Tooltip>
                            </div>
                        </StaticBox>}
                    <StaticBox title="Kundenangaben">
                        <div className='grid grid-cols-2 gap-2'>
                            <Tooltip title="Wählen Sie die passende Anrede für den Kunden aus. Diese wird für die persönliche Ansprache in der Korrespondenz verwendet.">
                                <FormControl required variant="outlined" className="w-full p-2">
                                    <InputLabel>Anrede</InputLabel>
                                    <Select
                                        name="salutation"
                                        label="Anrede"
                                        value={formData.salutation}
                                        onChange={handleChange}
                                        required
                                    >
                                        <MenuItem value="Frau">Frau</MenuItem>
                                        <MenuItem value="Herr">Herr</MenuItem>
                                        <MenuItem value="Frau und Herr">Frau und Herr</MenuItem>
                                        <MenuItem value="Frau und Frau">Frau und Frau</MenuItem>
                                        <MenuItem value="Herr und Herr">Herr und Herr</MenuItem>
                                        <MenuItem value="Firma">Firma</MenuItem>
                                    </Select>
                                </FormControl>

                            </Tooltip>
                            <Tooltip title="Geben Sie den Titel des Kunden ein (falls vorhanden).">
                                <TextField
                                    label="Titel"
                                    type="text"
                                    name="name_prefix"
                                    value={formData.name_prefix}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 pt-4'>
                            <Tooltip title="Geben Sie den Vornamen des Kunden ein. Dieser wird für die persönliche Ansprache genutzt.">
                                <TextField
                                    label="Vorname"
                                    type="text"
                                    name="first_name"
                                    value={formData.first_name}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie den Nachnamen des Kunden ein. Dieser wird zur Identifizierung und Ansprache benötigt.">
                                <TextField
                                    type="text"
                                    label="Nachname"
                                    name="last_name"
                                    value={formData.last_name}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Falls die Post an eine andere Person oder Firma weitergeleitet werden soll, geben Sie hier den Zusatz ein, z. B. 'Max Mustermann'.">
                                <TextField
                                    type="text"
                                    label="c/o"
                                    name="care_of"
                                    value={formData.care_of}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 pt-4'>
                            <Tooltip title="Geben Sie die Straße der Kundenadresse ein. Achten Sie darauf, den vollständigen Straßennamen korrekt zu erfassen.">
                                <TextField
                                    type="text"
                                    label="Straße"
                                    name="street"
                                    value={formData.street}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Hausnummer der Kundenadresse ein. Wenn vorhanden, können auch zusätzliche Angaben wie Stockwerk oder Gebäudeteil notiert werden.">
                                <TextField
                                    type="text"
                                    label="Hausnummer"
                                    name="house_number"
                                    value={formData.house_number}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie die Postleitzahl (PLZ) der Kundenadresse ein. Diese Information ist für die korrekte Zustellung der Post nötig.">
                                <TextField
                                    type="text"
                                    name="postal_code"
                                    label="Postleitzahl"
                                    value={formData.postal_code}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                            <Tooltip title="Geben Sie den Ort oder die Stadt der Kundenadresse ein. Dies ist der offizielle Wohn- oder Geschäftssitz des Kunden.">
                                <TextField
                                    type="text"
                                    label="Ort"
                                    name="city"
                                    value={formData.city}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                    required
                                />
                            </Tooltip>
                        </div>
                        <div className='grid grid-cols-2 gap-2 pt-4'>
                            <Tooltip title="Geben Sie die E-Mail-Adresse des Kunden ein (falls unbekannt bitte Emailadresse des Maklers angeben).">
                                <TextField
                                    type="email"
                                    label="Emailadresse"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    className="w-full p-2"
                                />
                            </Tooltip>
                        </div>
                    </StaticBox>
                    <div className='flex gap-4 justify-end'>
                        <Tooltip title="Ohne sichern zurück.">
                            <Button
                                type="button"
                                onClick={() => window.history.back()}
                                variant='contained'
                            >
                                Verwerfen
                            </Button>
                        </Tooltip>
                        <Tooltip title={edit_mode ? 'Kunden aktulaiseren' : 'Neue Kunden anlegen'}>
                            <Button
                                type="submit"
                                variant='contained'
                                color='secondary'
                                disabled={isSubmitting}>
                                {isSubmitting
                                    ? (edit_mode ? 'Aktualisieren...' : 'Anlegen...')
                                    : (edit_mode ? 'Kunde aktualisieren' : 'Kunde anlegen')}
                            </Button>
                        </Tooltip>
                    </div>
                </div>
            </form>
        </Box>
    );
}

export default CustomerForm;