import { formCards, FormCard as TFormCard } from "./cards";
import {useEffect, useState} from "react";
import { FormState } from "@/inline-dependencies/FancyForm";
import { isFormField } from "@/inline-dependencies/FancyForm/domain";
import { Form } from "@/components/ReferralFlow/Form";
import { Summary, SummarySection } from "@/components/ReferralFlow/Summary";
import { useTranslations } from "next-intl";
import { Translator } from "@/domain";
import httpClient from "@/utils/HttpClient";

type CardStates = Record<string, FormState>

export const ReferralFlow = () => {
    const t = useTranslations();
    const [activeView, setActiveView] = useState<'form' | 'summary'>('form')

    const [cards] = useState<TFormCard[]>(formCards)
    const [cardStates, setCardStates] = useState<CardStates>({})

    const updateState = (cardId: string, patch: Partial<FormState>) => {
        setCardStates(prevState => ({
            ...prevState,
            [cardId]: {
                ...prevState?.[cardId],
                ...patch,
            }
        }))
    }

    useEffect(() => {
        const prefillForm = async () => {
            try {
                const { referrer } = await httpClient.request('/api/referrer/me')
                updateState('referrer', referrer)
            } catch (error) {
                console.error('[ReferralFlow] failed to prefill form:', error)
            }
        }
        prefillForm()
    }, []);


    return (
        <>
            {activeView === 'form' && (
                <Form cards={cards} cardStates={cardStates} onChange={updateState} onButtonClick={() => setActiveView('summary')} />
            )}
            {activeView === 'summary' && (
                <Summary sections={getSummarySections(cards, cardStates, t)} onBackClick={() => setActiveView('form')} />
            )}
        </>
    );
}

const getSummarySections = (definitions: TFormCard[], states: CardStates, t: Translator) => {
    return definitions.reduce<SummarySection[]>((acc, definition) => {
        if (definition.excludeFromSummary) {
            return acc
        }

        const fields = definition.items.filter(isFormField)
        const records = fields.reduce<SummarySection['records']>((acc, field) => {
            const valueInState = states[definition.id]?.[field.id]

            let value = valueInState
            if (field.element === 'dropdown') {
                const selectedOption = field.options?.find(option => option.value === valueInState)
                value = selectedOption && t(selectedOption.label)
            }

            if (value === undefined) {
                return acc
            }
            const { id, label } = field

            return [
                ...acc,
                { id, label, value }
            ]
        }, [])

        if (!records.length) {
            return acc
        }

        const section: SummarySection = {
            id: definition.id,
            title: definition.title,
            records
        }
        return [...acc, section]
    }, [])
}

