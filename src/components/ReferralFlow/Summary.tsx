import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Typo<PERSON>,
} from "@mui/material";
import { useTranslations } from "next-intl";
import { FC, useState } from "react";
import { Card } from "@/components/ReferralFlow/Card";
import httpClient from "@/utils/HttpClient";
import { SuccessModal } from "@/components/modal/SuccessModal";

export type SummarySection = {
  id: string;
  title: string;
  records: { id: string; label: string; value: string | boolean }[];
};
type Props = {
  sections: SummarySection[];
  onBackClick: () => void;
};

export const Summary: FC<Props> = ({ sections, onBackClick }) => {
  const t = useTranslations('referralFlow.summary');

  const [isSendingOffer, setIsSendingOffer] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [url, setUrl] = useState("");
  const [modalOpen, setModalOpen] = useState(false);

  const loadAgreement = async () => {
    const body = getRequestBody(sections);
    const agreementBlob = await httpClient
      .request("/api/referrer/agreement/preview", {
        method: "POST",
        body,
        raw: true,
      })
      .then((response) => response.blob());

    const localUrl = URL.createObjectURL(agreementBlob);
    setUrl(localUrl);
  };

  const sendOffer = async () => {
      setIsSendingOffer(true)

      try {
          await httpClient.request("/api/referrer/send-offer", {
              method: "POST",
              body: getRequestBody(sections),
          });
          setModalOpen(true);
      } catch (error) {
          console.error('Failed to send the client offer:', error)
          setErrorMessage(t('genericErrorMessage'))
      } finally {
          setIsSendingOffer(false)
      }
  }

  return (
    <Stack sx={{ maxWidth: "800px", margin: "0 auto" }} gap={5}>
      <Card>
        <Stack>
          <Typography variant="h2" sx={{ marginBottom: "10px" }}>
            {t("title.summary")}
          </Typography>
          <Stack gap={2}>
            {sections.map((section, index) => (
              <Section key={section.title + index} {...section} />
            ))}
          </Stack>
          <Button
            sx={{ marginTop: "50px", alignSelf: "center" }}
            onClick={onBackClick}
          >
            {t("button.backToForm")}
          </Button>
        </Stack>
      </Card>
      <Card>
        <Stack>
          <Typography variant="h2" sx={{ marginBottom: "20px" }}>
            {t("title.agreement")}
          </Typography>
          <Typography>
            {t("agreementAcceptance")}
          </Typography>
          <Typography sx={{ mt: "10px" }}>
            {t("agreementName")}
          </Typography>
          <Button
            sx={{ mt: "30px", alignSelf: "center" }}
            onClick={loadAgreement}
          >
            {t("button.loadAgreement")}
          </Button>
        </Stack>
      </Card>

      {/* Send Referral Button */}
      <Button
        sx={{ alignSelf: "center" }}
        fullWidth
        disabled={isSendingOffer}
        onClick={sendOffer}
      >
        {t("button.sendOffer")}
      </Button>

        {errorMessage && (
            <Alert severity="error">
                {errorMessage}
            </Alert>
        )}

      {/* Agreement Preview */}
      <Dialog
        open={!!url}
        onClose={() => setUrl("")}
        maxWidth="xl"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          <iframe
            src={url}
            style={{ width: "100%", height: "80vh", border: "none" }}
          />
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <SuccessModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t("modal.successTitle")}
        message={t("modal.successMessage")}
      />
    </Stack>
  );
};

const Section: FC<SummarySection> = ({ title, records }) => {
  const t = useTranslations();
  return (
    <section style={{ marginTop: "20px" }}>
      <Typography variant="h3">{t(title)}</Typography>
      <Divider
        sx={(theme) => ({
          marginTop: "10px",
          backgroundColor: theme.palette.primary.dark,
        })}
      />
      <Stack
        sx={(theme) => ({
          color: theme.palette.primary.dark,
          marginTop: "20px",
        })}
        gap={2}
      >
        {records.map(({ label, value }) => {
          const labelContent =
            typeof value === "boolean" ? t(label) : `${t(label)}:`;
          return (
            <Stack direction="row" key={label + value}>
              <Box sx={{ minWidth: "150px" }}>{labelContent}</Box>
              <Box>{value}</Box>
            </Stack>
          );
        })}
      </Stack>
    </section>
  );
};

const getRequestBody = (sections: SummarySection[]) => {
  return sections.reduce((acc, section) => {
    const { id } = section;

    const records = section.records.reduce((innerAcc, record) => {
      const { id, value } = record;
      return {
        ...innerAcc,
        [id]: value,
      };
    }, {} as any);

    return {
      ...acc,
      [id]: records,
    };
  }, [] as any);
};
