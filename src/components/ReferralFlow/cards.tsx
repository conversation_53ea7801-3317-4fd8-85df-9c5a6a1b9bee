import { FormItem, FormState } from "@/inline-dependencies/FancyForm";
import { Subtitle } from "@/components/ReferralFlow/Subtitle";
import { TermsAndConditions } from "@/components/ReferralFlow/TermsAndConditions";
import {isEmail} from "@/utils/isEmail";
import {validators} from "@/utils/validators";

export type FormCard = {
    id: string
    title: string
    items: FormItem[]
    displayIf?: (cardStates: Record<string, FormState>) => boolean | undefined
    excludeFromSummary?: boolean
}

const fieldsGroups: Record<string, FormItem[]> = {
    name: [
        {
            element: <Subtitle key='subtitle.name' translationKey='referralFlow.form.subtitle.name' />,
        },
        {
            id: 'firstName',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.firstName',
        },
        {
            id: 'lastName',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.lastName',
        },
    ],
}

const referrer: FormCard = {
    id: 'referrer',
    title: 'referralFlow.form.title.referrerInfo',
    items: [
        ...fieldsGroups.name,
        {
            element: <Subtitle key='subtitle.address' translationKey='referralFlow.form.subtitle.address' />,
        },
        {
            id: 'street',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.street',
        },
        {
            id: 'houseNumber',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.houseNumber',
        },
        {
            id: 'postalCode',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.postalCode',
        },
        {
            id: 'city',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.city',
        },
        {
            element: <Subtitle key='subtitle.contact' translationKey='referralFlow.form.subtitle.contact' />,
        },
        {
            id: 'email',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.email',
            validate: isEmail,
            validationErrorMessage: 'errorMessage.validation.email',
        },
        {
            id: 'phoneNumber',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.phoneNumber',
        },
        {
            element: <Subtitle key='subtitle.agency' translationKey='referralFlow.form.subtitle.agency' />,
        },
        {
            id: 'agencyNumber',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.agencyNumber',
        },
        {
            id: 'salesDirection',
            element: "dropdown",
            required: true,
            label: 'referralFlow.form.fieldLabel.salesDirection',
            options: [
                {
                    label: 'referralFlow.form.salesDirection.Hamburg',
                    value: 'Hamburg'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Köln',
                    value: 'Köln'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Berlin',
                    value: 'Berlin'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Leipzig',
                    value: 'Leipzig'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Frankfurt',
                    value: 'Frankfurt'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Stuttgart',
                    value: 'Stuttgart'
                },
                                {
                    label: 'referralFlow.form.salesDirection.Nürnberg',
                    value: 'Nürnberg'
                },
                                {
                    label: 'referralFlow.form.salesDirection.München',
                    value: 'München'
                },
                                                {
                    label: 'referralFlow.form.salesDirection.Sonstige',
                    value: 'Sonstige'
                }
            ]
        },
        {
            element: <Subtitle key='subtitle.bankAccount' translationKey='referralFlow.form.subtitle.bankAccount' />,
        },
        {
            id: 'iban',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.iban',
        },
    ]
}

const customer: FormCard = {
    id: 'customer',
    title: 'referralFlow.form.title.customerInfo',
    items: [
        ...fieldsGroups.name,
        {
            id: 'salutation',
            required: true,
            label: 'referralFlow.form.fieldLabel.salutation',
            element: 'dropdown',
            options: [
                { label: 'referralFlow.form.salutation.masculine', value: 'masculine' },
                { label: 'referralFlow.form.salutation.feminine', value: 'feminine' },
                { label: 'referralFlow.form.salutation.nonBinary', value: 'nonBinary' },
            ]
        },
        {
            id: 'email',
            required: true,
            label: 'referralFlow.form.fieldLabel.email',
            element: 'text',
            validate: isEmail,
            validationErrorMessage: 'errorMessage.validation.email',
        }
    ]
}

const products: FormCard = {
    id: 'products',
    title: 'referralFlow.form.title.products',
    items: [
        {
            id: 'residentialBuildingInsurance',
            element: 'checkbox',
            label: 'referralFlow.form.residentialBuildingInsurance',
        }
    ]
}

const residentialBuildingInsurance: FormCard = {
    id: 'residentialBuildingInsurance',
    title: 'referralFlow.form.residentialBuildingInsurance',
    displayIf: cardStates => cardStates.products?.residentialBuildingInsurance,
    items: [
        {
            element: <Subtitle key='subtitle.contractNumber' translationKey='referralFlow.form.subtitle.contractNumber' />,
        },
        {
            id: 'contractNumber',
            element: "text",
            required: true,
            label: 'referralFlow.form.fieldLabel.contractNumber',
            validate: validators.contractNumber,
            validationErrorMessage: 'referralFlow.form.hint.invalidContractNumber'
        },
    ],
}
const insuranceStart: FormCard = {
    id: 'insuranceStart',
    title: 'referralFlow.form.title.insuranceStart',
    excludeFromSummary: true,
    items: [
        {
            id: 'termsAndConditions',
            element: 'checkbox',
            label: 'referralFlow.form.fieldLabel.termsAndConditions',
        },
        {
            element: <TermsAndConditions key='termsAndConditionsLayout' />
        }
    ],
}

export const formCards = [
    referrer,
    customer,
    products,
    residentialBuildingInsurance,
    insuranceStart
]