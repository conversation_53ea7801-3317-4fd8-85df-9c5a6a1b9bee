import * as z from "zod";
import {But<PERSON>, Stack} from "@mui/material";
import {useTranslations} from "next-intl";
import {FormState} from "@/inline-dependencies/FancyForm";
import {FC} from "react";
import {FormCard as TFormCard} from "@/components/ReferralFlow/cards";
import {FormCard} from "@/components/ReferralFlow/FormCard";
import {ReferralFlowFormSchema} from "@/zod";

type Props = {
    cards: TFormCard[]
    cardStates: Record<string, FormState>
    onChange: (pageId: string, patch: Partial<FormState>) => void
    onButtonClick: () => void
}
export const Form: FC<Props> = ({ cards, cardStates, onChange, onButtonClick}) => {
    const t = useTranslations()

    const { success: isFormValid } = ReferralFlowFormSchema
        .extend({
            insuranceStart: z.object({
                termsAndConditions: z.boolean().refine(value => value)
            })
        })
        .safeParse(cardStates)

    return (
        <Stack sx={{ maxWidth: '800px', margin: '0 auto', gap: 5 }}>
            {cards.map(card => {
                if (card.displayIf && !card.displayIf(cardStates)) {
                    return null
                }

                return <FormCard
                    key={card.id}
                    state={cardStates[card.id] ?? {}}
                    onChange={patch => onChange(card.id, patch)}
                    {...card}
                />
            })}
            <Button disabled={!isFormValid} fullWidth sx={{ alignSelf: 'center', mt: 2, mb: 2 }} onClick={onButtonClick}>
                {t('referralFlow.form.button.next')}
            </Button>
        </Stack>
    );
}
