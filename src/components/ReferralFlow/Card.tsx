import {Box} from "@mui/material"
import {FC, ReactNode} from "react"

type Props = {
    children: ReactNode
}
export const Card: FC<Props> = ({ children }) => {
    return (
        <Box sx={{ 
            padding: '40px',
            bgcolor: "background.paper",
            borderRadius: 3,
            boxShadow: "0 4px 12px rgba(0,0,0,0.06)"
        }}>
            {children}
        </Box>
    )
}
