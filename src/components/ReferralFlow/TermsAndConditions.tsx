import {FC} from "react";
import {useTranslations} from "next-intl";
import {Typography, List, ListItem, CSSObject} from "@mui/material";

export const TermsAndConditions: FC = () => {
    const t = useTranslations()
    return (
        <List sx={(theme): CSSObject => ({
            color: theme.palette.primary.dark,
            listStyle: 'disc',
            margin: '-10px 0 0 40px',
            padding: 0,
        })}>
            {items.map(item => {
                return (
                    <ListItem key={item} sx={{ display: 'list-item', padding: '0 0 10px' }}>
                        <Typography>{t(item)}</Typography>
                    </ListItem>
                )
            })}
        </List>
    )
}

const items = [
    'referralFlow.form.termsAndConditions.1',
    'referralFlow.form.termsAndConditions.2',
    'referralFlow.form.termsAndConditions.3',
    'referralFlow.form.termsAndConditions.4',
]