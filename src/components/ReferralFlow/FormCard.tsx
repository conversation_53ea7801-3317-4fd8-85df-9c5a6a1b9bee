import {Typography} from "@mui/material";
import {useTranslations} from "next-intl";
import {FancyForm, FancyFormProps, FormItem} from "@/inline-dependencies/FancyForm";
import {FC} from "react";
import {FormCard as TFormCard} from "@/components/ReferralFlow/cards";
import {isFormField} from "@/inline-dependencies/FancyForm/domain";
import {Card} from "@/components/ReferralFlow/Card";

type Props = TFormCard & Pick<FancyFormProps, 'state' | 'onChange'>

export const FormCard: FC<Props> = ({ title, items, state, onChange }) => {
    const t = useTranslations();
    const translatedItems = items.map<FormItem>(item => {
        if (!isFormField(item)) {
            return item
        }

        let validationErrorMessage: string | undefined
        if (item.validationErrorMessage) {
            validationErrorMessage = t(item.validationErrorMessage)
        } else if (item.required) {
            validationErrorMessage = t('referralFlow.form.hint.fieldIsRequired')
        }

        return {
            ...item,
            label: t(item.label),
            tooltip: item.tooltip && t(item.tooltip),
            options: item.options?.map(option => ({
                ...option,
                label: t(option.label)
            })),
            validationErrorMessage

        }
    })

    return (
        <Card>
            <Typography variant='h2' sx={{ marginBottom: '10px' }}>{t(title)}</Typography>
            <FancyForm items={translatedItems} state={state} onChange={onChange} />
        </Card>
    );
}
