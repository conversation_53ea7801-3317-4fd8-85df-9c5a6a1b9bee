import { useFormContext } from 'react-hook-form';

import { type FormContext } from '@/types/formContext';

const useReactHookFormContext = <T extends Record<string, unknown>>() => {
  const context = useFormContext<T>() as FormContext<T>;

  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider');
  }

  return context;
};

export { useReactHookFormContext };
