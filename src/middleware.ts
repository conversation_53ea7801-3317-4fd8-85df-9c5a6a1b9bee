// src/middleware.ts
export { auth as middleware } from '@/auth';

export const config = {
    matcher: [
        '/dashboard/:path*',
        '/contracts/:path*',
        '/customers/:path*',
        '/customer/:path*',
        '/invoices/:path*',
        '/reports/:path*',
        '/agents/:path*',
        '/agencies/:path*',
        '/settings/:path*',
        '/admin_settings/:path*',
        '/info/:path*',
        '/documents/:path*',
        '/settings/:path*',
        '/admin_settings/:path*',
    ],
};
