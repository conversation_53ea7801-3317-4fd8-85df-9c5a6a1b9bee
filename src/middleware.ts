import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { identityManager } from "@/server/IdentityManager";
import {paths} from "@/paths";

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  if (pathname.startsWith("/referrer")) {
    return referrerArea(req)
  }
  if (pathname.startsWith("/customer")) {
    return customerArea(req)
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/referrer/:path*", "/customer/:path*"],
};

const referrerArea = async (req: NextRequest) => {
  const unprotectedPaths: string[] = [
    paths.referrer.unauthorized,
    paths.referrer.registration,
    paths.referrer.access,
  ]
  if (unprotectedPaths.includes(req.nextUrl.pathname)) {
    return NextResponse.next();
  }

  const unauthorized = NextResponse.redirect(new URL(paths.referrer.unauthorized, req.url));

  const session = req.cookies.get("referrerSession")?.value;
  if (!session) {
    return unauthorized
  }

  try {
    const identity = await identityManager.getReferrerOrThrow(session)
    console.log("[MIDDLEWARE] Referrer identity verified:", identity);
    return NextResponse.next();
  } catch {
    return unauthorized
  }
}

const customerArea = async (req: NextRequest) => {
  const unprotectedPaths: string[] = [
    paths.customer.unauthorized,
    paths.customer.requestAccess,
    paths.customer.access,
  ]
  if (unprotectedPaths.includes(req.nextUrl.pathname)) {
    return NextResponse.next();
  }

  const unauthorized = NextResponse.redirect(new URL(paths.customer.unauthorized, req.url));

  const session = req.cookies.get("customerSession")?.value;
  if (!session) {
    return unauthorized
  }

  try {
    const identity = await identityManager.getCustomerOrThrow(session)
    console.log("[MIDDLEWARE] Customer identity verified:", identity);
    return NextResponse.next();
  } catch (error) {
    console.error(error)
    return unauthorized
  }
}