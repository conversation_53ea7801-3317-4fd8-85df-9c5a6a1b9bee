import {TipStatus, Translator} from "@/domain";
import {Chip, ChipProps} from "@mui/material";

export const getOfferStatusLabel = (status: TipStatus, unscopedT: Translator) => {
    return unscopedT(`tipStatus.${status}`)
}

// this is not a component because MUI Tooltip has problems with ref
export const getOfferStatusChip = (status: TipStatus, unscopedT: Translator) => {
    const colorsMap: Record<TipStatus, ChipProps['color']> = {
        invited: 'default',
        pending: 'secondary',
        open: 'info',
        accepted: 'success',
        rejected: 'error',
    }
    return (
        <Chip
            label={getOfferStatusLabel(status, unscopedT)}
            color={colorsMap[status]}
            size="small"
            sx={{fontWeight: 500}}
        />
    );
}
