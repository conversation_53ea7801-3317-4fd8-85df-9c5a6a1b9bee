import {type FormItem, type FormState, isFormField} from "./domain";

// returns the IDs of fields that should be present in the form given the state
export const determineShownFields = (items: FormItem[], state: FormState) => {
    const fields = items.filter(isFormField)
    const shownFields = new Set<string>()

    for (const field of fields) {
        let isShown = true

        if (typeof field.showIf === 'string') {
            const toggleId = field.showIf
            if (!shownFields.has(toggleId) || !state[toggleId]) {
                isShown = false
            }
        }
        if (typeof field.showIf === 'object') {
            for (const [conditionFieldId, conditionValue] of Object.entries(field.showIf)) {
                if (!shownFields.has(conditionFieldId) || state[conditionFieldId] !== conditionValue) {
                    isShown = false
                }
            }
        }

        if (isShown) {
            shownFields.add(field.id)
        }
    }

    return shownFields;
}
