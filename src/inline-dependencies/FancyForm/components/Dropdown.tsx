import {FormControl, InputLabel, MenuItem, Select, SelectProps} from "@mui/material";
import {classes} from "../cssClasses";
import {initScrollLock} from "../initScrollLock";

type DropdownProps = SelectProps & {
    options: { label: string, value: any }[]
}

export const Dropdown: React.FC<DropdownProps> = ({ id, label, value, onChange, options }) => {
    const { disableScroll, enableScroll } = initScrollLock()

    return (
        <FormControl variant="outlined" className={classes.formField}>
            <InputLabel htmlFor={id}>{label}</InputLabel>
            <Select
                id={id}
                label={label}
                name={id}
                value={value}
                onChange={onChange}
                onOpen={disableScroll}
                onClose={enableScroll}
            >
                {options!.map((option) => (
                    <MenuItem
                        key={option.value}
                        className={classes.dropdownOption}
                        value={option.value}
                    >
                        {option.label}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
};
