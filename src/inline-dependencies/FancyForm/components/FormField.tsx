import {
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField,
  TextFieldProps,
  Tooltip,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
} from "@mui/material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { FC, useState } from "react";
import dayjs, { Dayjs } from "dayjs";
import { FormState, FormField as TFormField } from "../domain";
import { classes } from "../cssClasses";
import { Dropdown } from "./Dropdown";

type Props = TFormField & {
  state: FormState;
  onChange: (newValue: any) => void;
};

export const FormField: FC<Props> = (props) => {
  if (props.tooltip) {
    console.log(props)
    return (
      <Tooltip title={props.tooltip} placement="auto">
        <Content {...props} />
      </Tooltip>
    );
  }

  return <Content {...props} />;
};

const Content: FC<Props> = ({
  element,
  id,
  state,
  options,
  required,
  validate,
  validationErrorMessage,
  ...props
}) => {
  const [isValid, setIsValid] = useState(true);
  const label = required ? `${props.label} *` : props.label;

  if (element === "checkbox") {
    return (
      <FormControlLabel
        className={classes.formField}
        label={label}
        control={
          <Checkbox
            name={id}
            checked={state[id] ?? false}
            onChange={(e) => props.onChange(e.target.checked)}
          />
        }
      />
    );
  }

  if (element === "boolean-radio-group") {
    const value =
      state[id] === true ? "true" : state[id] === false ? "false" : "";
    return (
      <FormControl className={classes.formField}>
        <FormLabel component="legend" sx={{ fontSize: "0.875rem", mb: 1 }}>
          {label}
        </FormLabel>
        <RadioGroup
          row
          name={id}
          value={value}
          onChange={(e) => props.onChange(e.target.value === "true")}
        >
          <FormControlLabel value="true" control={<Radio />} label="Ja" />
          <FormControlLabel value="false" control={<Radio />} label="Nein" />
        </RadioGroup>
      </FormControl>
    );
  }

  if (element === "radio-group") {
    if (!options || options.length === 0) {
      console.error(
        `[FancyForm] Element misconfiguration: no options provided for radio-group with id=${id}`
      );
    }
    return (
      <FormControl className={classes.formField}>
        <FormLabel component="legend" sx={{ fontSize: "0.875rem", mb: 1 }}>
          {label}
        </FormLabel>
        <RadioGroup
          name={id}
          value={state[id] ?? ""}
          onChange={(e) => props.onChange(e.target.value)}
        >
          {options?.map((opt) => (
            <FormControlLabel
              key={opt.value}
              value={opt.value}
              control={<Radio />}
              label={opt.label}
            />
          ))}
        </RadioGroup>
      </FormControl>
    );
  }

  if (element === "boolean-dropdown") {
    const value = id in state ? (state[id] ? 1 : 0) : "";
    return (
      <Dropdown
        id={id}
        label={label}
        options={[
          { label: "Ja", value: 1 },
          { label: "Nein", value: 0 },
        ]}
        value={value}
        onChange={(e) => props.onChange(Boolean(e.target.value))}
      />
    );
  }

  if (element === "dropdown") {
    if (!options) {
      console.error(
        `[FancyForm] Element misconfiguration: no options provided for a dropdown with id=${id}`
      );
    }
    return (
      <Dropdown
        id={id}
        label={label}
        options={options ?? []}
        value={state[id] ?? ""}
        onChange={(e) => props.onChange(e.target.value)}
      />
    );
  }

  if (element === "datetime") {
    return (
      <DateTimePicker
        className={classes.formField}
        label={label}
        disableFuture
        value={state[id] ? dayjs(state[id]) : null}
        onChange={(newValue: Dayjs | null) =>
          props.onChange(newValue?.toISOString())
        }
      />
    );
  }

  let additionalProps: Partial<TextFieldProps> = {};
  if (element === "textarea") {
    additionalProps = { multiline: true, minRows: 3 };
  }
  if (element === "date") {
    additionalProps = {
      type: "date",
      slotProps: { inputLabel: { shrink: true } },
    };
  }
  if (element === "number") {
    additionalProps = { type: "number" };
  }
  if (element === "price") {
    additionalProps = {
      type: "number",
      slotProps: {
        input: { endAdornment: <InputAdornment position="end">€</InputAdornment> },
      },
    };
  }

  const handleChange: TextFieldProps["onChange"] = (event) => {
    const newValue = event.target.value;
    let newIsValid = true;
    if (required && newValue === "") newIsValid = false;
    if (validate && !validate(newValue)) newIsValid = false;
    setIsValid(newIsValid);
    props.onChange(newValue);
  };

  return (
    <TextField
      error={!isValid}
      helperText={isValid ? undefined : validationErrorMessage}
      className={classes.formField}
      label={label}
      name={id}
      value={state[id] ?? ""}
      onChange={handleChange}
      fullWidth={false}
      {...additionalProps}
    />
  );
};
