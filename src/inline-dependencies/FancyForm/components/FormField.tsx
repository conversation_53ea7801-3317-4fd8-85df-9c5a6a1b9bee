// src/components/forms/Input.tsx
import {
    Checkbox,
    FormControlLabel,
    InputAdornment,
    TextField,
    type TextFieldProps,
    Tooltip
} from "@mui/material";
import {DateTimePicker} from "@mui/x-date-pickers/DateTimePicker";
import dayjs, {type Dayjs} from "dayjs";
import {type FC, useState} from "react";

import {classes} from "@/inline-dependencies/FancyForm/cssClasses";
import type { AnyObject,FormField as TFormField, FormState} from "@/inline-dependencies/FancyForm/domain";
import {useDefaultFieldValue} from "@/inline-dependencies/FancyForm/hooks/useDefaultFieldValue";

import {Dropdown} from "./Dropdown";

type Props = TFormField & {
    disableDefaultValues?: boolean
    context?: AnyObject
    state: FormState
    onChange: (newValue: any) => void;
}

export const FormField: FC<Props> = (props) => {
    if (props.tooltip) {
        return (
            <Tooltip title={props.tooltip} placement='top-start'>
                <Content {...props} />
            </Tooltip>
        )
    }

    return <Content {...props} />
};

const Content: FC<Props> = ({ disableDefaultValues, context, state, onChange, ...config }) => {
    const { id, element, required, validate, validationErrorMessage } = config
    const { options, defaultValue } = resolvePropsFromContext(config, context)

    const label = required ? `${config.label} *` : config.label

    const valueWithDefault = (getValue: () => any, localDefaultValue?: any) => {
        if (id in state) {
            return getValue()
        }
        return defaultValue ?? localDefaultValue
    }

    const [isValid, setIsValid] = useState(true)
    
    useDefaultFieldValue({fieldId: id, defaultValue, setValue: onChange, disable: disableDefaultValues})

    if (element === 'checkbox') {
        const checked = valueWithDefault(() => state[id], false)

        return (
            <FormControlLabel
                className={classes.formField}
                label={label}
                control={
                    <Checkbox
                        name={id}
                        checked={checked}
                        onChange={(e) => onChange(e.target.checked)}
                    />
                }
            />
        )
    }
    if (element === 'boolean-dropdown') {
        const value = valueWithDefault(() => state[id] ? 1 : 0, '')

        return (
            <Dropdown
                id={id}
                label={label}
                options={options ?? []}
                value={value}
                onChange={(e) => onChange(Boolean(e.target.value))}
            />
        )
    }
    if (element === 'dropdown') {
        if (!options) {
            console.error(`[FancyForm] Element misconfiguration: no options provided for a dropdown with id=${id}`)
        }
        const value = valueWithDefault(() => state[id], '')

        return (
            <Dropdown
                id={id}
                label={label}
                options={options ?? []}
                value={value}
                onChange={(e) => onChange(e.target.value)}
            />
        )
    }
    if (element === 'datetime') {
        const value = valueWithDefault(() => state[id] ? dayjs(state[id]) : null)

        return (
            <DateTimePicker
                className={classes.formField}
                label={label}
                disableFuture
                value={value}
                onChange={(newValue: Dayjs | null) => onChange(newValue?.toISOString())}
            />
        )
    }

    let additionalProps: Partial<TextFieldProps> = {}
    if (element === 'textarea') {
        additionalProps = {
            multiline: true,
            minRows: 3,
        }
    }
    if (element === 'date') {
        additionalProps = {
            type: "date",
            slotProps: {
                inputLabel: {
                    shrink: true
                }
            }
        }
    }
    if (element === 'number') {
        additionalProps = {
            type: "number",
        }
    }
    if (element === 'price') {
        additionalProps = {
            type: "number",
            slotProps: {
                input: {
                    endAdornment: <InputAdornment position="end">€</InputAdornment>,
                },
            }
        }
    }

    const handleChange: TextFieldProps['onChange'] = event => {
        const newValue = event.target.value

        let newIsValid = true
        if (required && newValue === '') {
            newIsValid = false
        }
        if (validate && !validate(newValue)) {
            newIsValid = false
        }
        setIsValid(newIsValid)

        onChange(newValue)
    }

    return (
        <TextField
            error={!isValid}
            helperText={isValid ? undefined : validationErrorMessage} // TODO: show required
            className={classes.formField}
            label={label}
            name={id}
            value={valueWithDefault(() => state[id], '')}
            onChange={handleChange}
            fullWidth={false}
            {...additionalProps}
        />
    );
}

const resolvePropsFromContext = (config: TFormField, context?: AnyObject) => {
    if (!context) {
        return {}
    }

    const options = resolveDropdownOptions(config, context)
    const defaultValue = typeof config.defaultValue === 'function'
        ? config.defaultValue(context, options)
        : config.defaultValue

    return { defaultValue, options }

}
const resolveDropdownOptions = (config: TFormField, context: AnyObject) => {
    if (config.element === 'boolean-dropdown') {
        return [
            { label: 'Ja', value: 1 },
            { label: 'Nein', value: 0 },
        ]
    }
    if (config.element === 'dropdown') {
        const { options } = config
        return typeof options === 'function' ? options(context) : (options ?? [])
    }

    return []
}
