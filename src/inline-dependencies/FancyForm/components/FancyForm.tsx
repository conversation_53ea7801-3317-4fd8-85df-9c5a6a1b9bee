import {Divider, Stack} from "@mui/material";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";
import {FormState, FormItem, FormField as TFormField} from "../domain";
import {determineShownFields} from "../determineShownFields";
import {FormField} from "./FormField";
import {classes} from "../cssClasses";

export interface FancyFormProps {
    items: FormItem[];
    state: FormState;
    onChange: (patch: Partial<FormState>) => void;
}

export const FancyForm: React.FC<FancyFormProps> = ({ items, state, onChange }) => {
    const shownFields = determineShownFields(items, state)

    const renderFormField = (field: TFormField) => {
        if (!shownFields.has(field.id)) {
            return null
        }

        return (
            <FormField key={field.id} {...field} state={state} onChange={(newValue) => onChange({
                [field.id]: newValue
            })} />
        )
    }

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale='de'>
            <Stack className={classes.root} direction='row' sx={{ flexWrap: 'wrap' }}>
                {items.map((item, index) => {
                    if (item.element === 'divider') {
                        return <Divider key={`divider${index}`} />
                    }
                    if (typeof item.element === 'string') {
                        return renderFormField(item)
                    }

                    return item.element
                })}
            </Stack>
        </LocalizationProvider>
    )
};
