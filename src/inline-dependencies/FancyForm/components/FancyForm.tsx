import {Divider, Stack} from "@mui/material";
import {type SxProps} from "@mui/system";
import {AdapterDayjs} from "@mui/x-date-pickers/AdapterDayjs";
import {LocalizationProvider} from "@mui/x-date-pickers/LocalizationProvider";

import {classes} from "@/inline-dependencies/FancyForm/cssClasses";
import {determineShownFields} from "@/inline-dependencies/FancyForm/determineShownFields";
import type {
    FormField as TFormField,
    FormItem,
    FormState} from "@/inline-dependencies/FancyForm/domain";

import {FormField} from "./FormField";

export interface FancyFormProps {
    sx?: SxProps
    items: FormItem[];
    state: FormState;
    context?: Record<string, any>
    onChange: (patch: Partial<FormState>) => void;
    disableDefaultValues?: boolean
}

export const FancyForm: React.FC<FancyFormProps> = ({ disableDefaultValues, items, state, context, onChange }) => {
    const shownFields = determineShownFields(items, state)

    const renderFormField = (config: TFormField) => {
        if (!shownFields.has(config.id)) {
            return null
        }
        return (
            <FormField key={config.id} {...config} disableDefaultValues={disableDefaultValues} state={state} context={context} onChange={(newValue) => onChange({
                [config.id]: newValue
            })} />
        )
    }

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale='de'>
            <Stack className={classes.root} direction='row' sx={{ flexWrap: 'wrap' }}>
                {items.map((item, index) => {
                    if (item.element === 'divider') {
                        return <Divider key={`divider${index}`} />
                    }
                    if (typeof item.element === 'string') {
                        return renderFormField(item)
                    }

                    return item.element
                })}
            </Stack>
        </LocalizationProvider>
    )
};
