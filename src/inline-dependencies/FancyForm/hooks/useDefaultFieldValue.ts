import {useEffect} from "react";

type Params = {
    disable?: boolean
    fieldId: string
    defaultValue: any
    setValue: (value: any) => void
}
export const useDefaultFieldValue = ({ disable, defaultValue, setValue, fieldId }: Params) => {
    const itemKey = `asevo.fancyForm.${fieldId}.hasSetDefaultValue`
    useEffect(() => {
        if (disable || sessionStorage.getItem(itemKey)) {
            return
        }
        if (defaultValue !== undefined) {
            setValue(defaultValue)
            sessionStorage.setItem(itemKey, 'true')
        }
    }, [defaultValue, setValue, itemKey, disable]);
    useEffect(() => {
        if (disable) {
            return
        }
        const listener = () => {
            sessionStorage.removeItem(itemKey)
            window.removeEventListener('beforeunload', listener)
        }
        window.addEventListener('beforeunload', listener);
    }, [disable, itemKey]);
}