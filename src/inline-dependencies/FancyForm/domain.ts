import {ReactElement} from "react";

export const divider = {
    element: 'divider',
} as const

export type FormField = {
    element: 'text' | 'textarea' | 'date' | 'datetime' | 'dropdown' | 'boolean-dropdown' | 'checkbox' | 'price' | 'number' | 'radio-group'| 'boolean-radio-group'  
    label: string;
    id: string;
    tooltip?: string;
    options?: { value: string | number, label: string }[];
    showIf?: string | Record<string, any>;
    required?: boolean
    requiredFieldErrorMessage?: string
    validate?: (value: any) => boolean
    validationErrorMessage?: string
}

export type ElementMap<T> = { [key in FormField['element']]?: T }

type Layout = {
    element: ReactElement
}

export type FormItem = FormField | typeof divider | Layout

export type FormState = Record<string, any>;

export const isFormField = (formItem: FormItem): formItem is FormField => {
    return typeof formItem.element === 'string' && formItem.element !== 'divider'
}
