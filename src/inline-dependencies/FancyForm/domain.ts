import {type ReactElement} from "react";

export type FormItem<TContext extends AnyObject = AnyObject> = FormField<TContext> | typeof divider | Layout

type Layout = {
    element: ReactElement
}
export type FormField<TContext extends AnyObject = AnyObject> = {
    element: 'text' | 'textarea' | 'date' | 'datetime' | 'dropdown' | 'boolean-dropdown' | 'checkbox' | 'price' | 'number'
    label: string;
    id: string;
    tooltip?: string;
    // Only relevant for dropdowns
    options?: DropdownOptionsConfig<TContext>
    showIf?: string | AnyObject;
    defaultValue?: (context: TContext, options?: DropdownOption[]) => string | number | boolean | undefined
    required?: boolean
    requiredFieldErrorMessage?: string
    validate?: (value: any) => boolean
    validationErrorMessage?: string
}

export const divider = {
    element: 'divider',
} as const

export type DropdownOption = {
    value: string | number,
    label: string
}
export type DropdownOptionsConfig<TContext> = DropdownOption[] | ((context: TContext) => DropdownOption[])

export type FormState = AnyObject;

export const isFormField = (formItem: FormItem): formItem is FormField => {
    return typeof formItem.element === 'string' && formItem.element !== 'divider'
}

export type AnyObject = Record<string, any>
