type Result = {
    disableScroll: () => void
    enableScroll: () => void
}

// copied from https://stackoverflow.com/a/4770179/13679562 and added a bunch of `any`
export const initScrollLock = (): Result => {
    try {
        const keys: any = {37: 1, 38: 1, 39: 1, 40: 1};

        function preventDefault(e: any) {
            e.preventDefault();
        }

        function preventDefaultForScrollKeys(e: any) {
            if (keys[e.keyCode]) {
                preventDefault(e);
                return false;
            }
        }

        // modern Chrome requires { passive: false } when adding event
        let supportsPassive = false;
        try {
            window.addEventListener("test" as any, null as any, Object.defineProperty({}, 'passive', {
                get: function () { supportsPassive = true; }
            }));
        } catch {}

        const wheelOpt: any = supportsPassive ? { passive: false } : false;
        const wheelEvent: any = 'onwheel' in document.createElement('div') ? 'wheel' : 'mousewheel';

        // call this to Disable
        function disableScroll() {
            window.addEventListener('DOMMouseScroll', preventDefault, false); // older FF
            window.addEventListener(wheelEvent, preventDefault, wheelOpt); // modern desktop
            window.addEventListener('touchmove', preventDefault, wheelOpt); // mobile
            window.addEventListener('keydown', preventDefaultForScrollKeys, false);
        }

        // call this to Enable
        function enableScroll() {
            window.removeEventListener('DOMMouseScroll', preventDefault, false);
            window.removeEventListener(wheelEvent, preventDefault, wheelOpt);
            window.removeEventListener('touchmove', preventDefault, wheelOpt);
            window.removeEventListener('keydown', preventDefaultForScrollKeys, false);
        }

        return { disableScroll, enableScroll }
    } catch {
        return { disableScroll: () => {}, enableScroll: () => {} }
    }
}
