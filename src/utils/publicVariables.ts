'use server';
import 'server-only'; // <-- ensure this file cannot be imported from the client

interface PublicVariables {
  SITE_URL: string;
  STREAM_API_KEY: string;
  NEXT_PUBLIC_AUTH_KEYCLOAK_ID: string;
  NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER: string;
  AUTH_KEYCLOAK_ISSUER?: string;
}
const PUBLIC_VARIABLES = [
  'SITE_URL',
  'STREAM_API_KEY',
  'NEXT_PUBLIC_AUTH_KEYCLOAK_ID',
  'NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER',
  'AUTH_KEYCLOAK_ISSUER',
];

export const fetchPublicVariables = async () => {
  const findPublic = (key: string) => {
    return PUBLIC_VARIABLES.includes(key);
  };

  const publicEnv = Object.keys(process.env)
    .filter(findPublic)
    .reduce((acc, key) => {
      acc[key] = process.env[key];
      return acc;
    }, {} as any);

  return publicEnv as PublicVariables;
};
