import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { type FilterRow, type PaginationMeta } from '@/components/filters/types';
import { deserializeFilters, serializeFilters } from '@/components/filters/url';
import { apiFetch } from '@/utils/apiFetch';

export type SortDir = 'asc' | 'desc';
type MapDataFn<T> = (json: any) => { items: T[]; meta: PaginationMeta | null };

const normalizeQuery = (q: Record<string, any>) =>
    Object.fromEntries(
        Object.entries(q)
            .filter(([, v]) => v !== undefined && v !== null && v !== '')
            .map(([k, v]) => [k, Array.isArray(v) ? v.join(',') : String(v)])
    );

const qs = (q: Record<string, any>) =>
    new URLSearchParams(normalizeQuery(q)).toString();

export function useTableQuery<T>({
    endpoint,
    defaultSortField,
    defaultSortDirection = 'asc',
    accessToken,
    enabled = true,
    debounceMs = 300,
    mapData,
}: {
    endpoint: string;
    defaultSortField: string;
    defaultSortDirection?: SortDir;
    accessToken?: string;
    enabled?: boolean;
    debounceMs?: number;
    mapData: MapDataFn<T>;
}) {
    const router = useRouter();

    // state
    const [items, setItems] = useState<T[]>([]);
    const [meta, setMeta] = useState<PaginationMeta | null>(null);
    const [page, setPage] = useState<number>(1);
    const [limit, setLimit] = useState<number>(10);
    const [filters, setFilters] = useState<FilterRow[]>([]);
    const [sortField, setSortField] = useState<string>(defaultSortField);
    const [sortDirection, setSortDirection] = useState<SortDir>(defaultSortDirection);

    // ui
    const [initialized, setInitialized] = useState(false);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // keep mapData stable across renders
    const mapDataRef = useRef(mapData);
    useEffect(() => {
        mapDataRef.current = mapData;
    }, [mapData]);

    // --- 1) Init from URL (once) ---
    const didInitRef = useRef(false);
    useEffect(() => {
        if (!router.isReady || didInitRef.current) return;

        const q = router.query;
        const initialPage = q.page ? parseInt(String(q.page), 10) : 1;
        const initialLimit = q.limit ? parseInt(String(q.limit), 10) : 10;
        const initialSortField =
            typeof q.sortField === 'string' ? q.sortField : defaultSortField;
        const initialSortDirection: SortDir = q.sortDirection === 'desc' ? 'desc' : 'asc';

        setPage(Number.isFinite(initialPage) ? initialPage : 1);
        setLimit(Number.isFinite(initialLimit) ? initialLimit : 10);
        setSortField(initialSortField);
        setSortDirection(initialSortDirection);
        setFilters(deserializeFilters(q as Record<string, any>));
        setInitialized(true);
        didInitRef.current = true;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router.isReady]);

    // Build target query & stable key
    const targetQuery = useMemo(
        () => ({
            page,
            limit,
            sortField,
            sortDirection,
            ...serializeFilters(filters),
        }),
        [page, limit, sortField, sortDirection, filters]
    );
    const queryKey = useMemo(() => qs(targetQuery), [targetQuery]);

    // --- 2) Reflect state to URL (no fetch here) ---
    useEffect(() => {
        if (!router.isReady || !initialized || !enabled) return;

        const currentQS = qs(router.query as Record<string, any>);
        if (currentQS !== queryKey) {
            router.replace(
                { pathname: router.pathname, query: targetQuery },
                undefined,
                { shallow: true }
            );
        }
        // keep deps minimal to avoid loops on router.query identity changes
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [router.isReady, initialized, enabled, queryKey, router.pathname]);

    // --- 3) Fetch data (triggered by queryKey only) ---
    useEffect(() => {
        if (!initialized || !enabled) return;

        const controller = new AbortController();
        const timeout = setTimeout(async () => {
            setLoading(true);
            try {
                const offset = (page - 1) * limit;
                const params = {
                    limit,
                    offset,
                    sortField,
                    sortDirection,
                    ...serializeFilters(filters),
                };

                const json = await apiFetch<any>(`${endpoint}?${qs(params)}`, {
                    method: 'GET',
                    accessToken,
                    signal: controller.signal,
                });

                const { items, meta } = mapDataRef.current(json);
                setItems(items ?? []);
                setMeta(meta ?? null);
                setError(null);

                const newPageCount = meta?.pageCount;
                if (newPageCount && page > newPageCount && page !== newPageCount) {
                    setPage(newPageCount);
                }
            } catch (e: any) {
                if (e?.name === 'AbortError') return;
                console.error('Table fetch error:', e);
                setError('Fehler beim Laden der Daten');
            } finally {
                setLoading(false);
            }
        }, debounceMs);

        return () => {
            controller.abort();
            clearTimeout(timeout);
        };
        // purposely NOT depending on mapData; we read it from the ref
    }, [queryKey, initialized, enabled, endpoint, accessToken, debounceMs, page, limit, sortField, sortDirection, filters]);

    // --- helpers ---
    const handleSort = useCallback(
        (field: string) => {
            if (sortField === field) {
                setSortDirection((prev) => (prev === 'asc' ? 'desc' : 'asc'));
            } else {
                setSortField(field);
                setSortDirection('asc');
            }
        },
        [sortField]
    );

    const handleLimitChange = useCallback((event: any) => {
        const value =
            typeof event?.target?.value === 'string'
                ? parseInt(event.target.value, 10)
                : Number(event?.target?.value ?? 10);
        setLimit(value);
        setPage(1);
    }, []);

    return {
        items,
        meta,
        loading,
        error,
        initialized,
        page,
        setPage,
        limit,
        setLimit,
        filters,
        setFilters,
        sortField,
        setSortField,
        sortDirection,
        setSortDirection,
        handleSort,
        handleLimitChange,
    };
}
