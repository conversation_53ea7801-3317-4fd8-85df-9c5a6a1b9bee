// src/utils/contractClaculator.ts

import { CalculationParametersData, ContractData, AccidentInsuranceFormData } from "@/types";
import { calculateInsuranceRuntime, calculateInvoiceDays } from "./calculationUtil";

export async function calculateContract(
    contractData: ContractData,
    token: string
) {
    const data: CalculationParametersData = await getCalculationParameters(
        token,
        contractData.insurance_start_date
    );

    const invoiceDays = calculateInvoiceDays(
        contractData.insurance_start_date,
        contractData.insurance_end_date,
        contractData.insurance_main_due_date!,
        contractData.payment_mode!
    );

    switch (contractData.contract_type) {
        case "hausrat": {
            if (contractData.is_elementar) {
                contractData.premie =
                    contractData.insurance_sum! * data.hausrat_versicherte_gefahren +
                    (data.hausrat_is_elementar * contractData.insurance_sum!);
            } else {
                contractData.premie =
                    (contractData.insurance_sum! * data.hausrat_versicherte_gefahren);
            }

            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "wohngebaeude": {
            const base = data.wohngebaude_versicherte_gefahren * contractData.insurance_sum_1914!;
            const element = data.wohngebaude_is_elementar * contractData.insurance_sum_1914!;
            contractData.premie =
                data.wohngebaude_beitragsfaktor *
                (contractData.is_elementar ? base + element : base);

            if (contractData.household_tech) {
                contractData.premie_household_tech = contractData.insurance_sum_1914! * data.wohngebaude_household_tech * data.wohngebaude_beitragsfaktor;
                contractData.premie += contractData.premie_household_tech;
            }
            if (contractData.pv_system) {
                contractData.premie_pv_system = contractData.insurance_sum_1914! * data.wohngebaude_pv_system * data.wohngebaude_beitragsfaktor;
                contractData.premie += contractData.premie_pv_system;
            }
            if (contractData.glass_insurance) {
                contractData.premie_glass_insurance = contractData.insurance_sum_1914! * data.wohngebaude_glass_insurance * data.wohngebaude_beitragsfaktor;
                contractData.first_invoice_glass_net = contractData.premie_glass_insurance / 365 * invoiceDays;
                // note: we do *not* add glass to premie here; we'll handle it in the total invoice below
            }

            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "tierhalterhaftpflicht": {
            const countDog = contractData.animal_data.filter(a => a.animal_type == "Hund").length;
            const countHorse = contractData.animal_data.filter(a => a.animal_type == "Pferd").length;

            let dog = countDog * data.tierhalterhaftpflicht_dog_premie;
            let horse = countHorse * data.tierhalterhaftpflicht_horse_premie;
            if (countDog > 1) dog = data.tierhalterhaftpflicht_dog_premie + (countDog - 1) * data.tierhalterhaftpflicht_dog_premie * data.tierhalterhaftpflicht_sale_dog;
            if (countHorse > 1) horse = data.tierhalterhaftpflicht_horse_premie + (countHorse - 1) * data.tierhalterhaftpflicht_horse_premie * data.tierhalterhaftpflicht_sale_horse;

            contractData.premie = (dog + horse);
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "privathaftpflicht": {
            contractData.premie = contractData.family_coverage
                ? data.privathaftpflicht_single_premie
                : data.privathaftpflicht_family_premie;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "haus_und_grundbesitzerhaftpflicht": {
            contractData.premie =
                data.haus_und_grundbesitzerhaftpflicht_premie;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "bauleistung": {
            const grossPrem = Math.max(contractData.insurance_sum! * data.bauleistung_satz, data.bauleistung_min);

            contractData.premie = grossPrem;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "bauherrenhaftpflicht": {
            const grossPrem = Math.max(contractData.building_sum! * data.bauherrenhaftpflicht_satz, data.bauherrenhaftpflicht_min);
            contractData.premie = grossPrem;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "geschaeftsversicherung": {
            const mult = {
                Handel: data.geschaeftsversicherung_handel,
                Handwerk: data.geschaeftsversicherung_handwerk,
                Dienstleistungen: data.geschaeftsversicherung_dienstleistungen,
                Gastronomie: data.geschaeftsversicherung_gastronomie,
            }[contractData.tariff_group!];

            const busPrem = contractData.insurance_sum! * mult!;
            const elePrem = contractData.is_elementar
                ? contractData.insurance_sum! * data.geschaeftsversicherung_is_elementar
                : 0;
            contractData.premie = Math.max(busPrem + elePrem, data.geschaeftsversicherung_min);
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "gebaeudeversicherung": {
            const mult = {
                Handel: data.gebaeudeversicherung_handel,
                Handwerk: data.gebaeudeversicherung_handwerk,
                Dienstleistungen: data.gebaeudeversicherung_dienstleistungen,
                Gastronomie: data.gebaeudeversicherung_gastronomie,
            }[contractData.tariff_group!];

            const busPrem = contractData.insurance_sum! * mult!;
            const elePrem = contractData.is_elementar
                ? contractData.insurance_sum! * data.gebaeudeversicherung_is_elementar
                : 0;
            contractData.premie = Math.max(busPrem + elePrem, data.gebaeudeversicherung_min);
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }

        case "betriebshaftpflicht": {
            const mult = {
                Handel: data.betriebshaftpflicht_handel,
                Handwerk: data.betriebshaftpflicht_handwerk,
                Dienstleistungen: data.betriebshaftpflicht_dienstleistungen,
                Gastronomie: data.betriebshaftpflicht_gastronomie,
            }[contractData.tariff_group!];

            contractData.premie = mult!;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }
        case 'unfallversicherung': {
            let totalPremie = 0;

            const toNumber = (value: string | number | undefined): number => {
                const n = typeof value === "string" ? parseFloat(value) : value;
                return isNaN(n ?? NaN) ? 0 : n!;
            };

            const getFactor = (person: AccidentInsuranceFormData, field: string): number => {
                const type = person.type?.toLowerCase(); // 'kind', 'senior' or 'erwachsener'
                const group = person.occupation_group?.slice(-1).toLowerCase(); // 'a' or 'b'

                const key = type === 'kind'
                    ? `unfall_kinder_${field}`
                    : type === 'senior'
                        ? `unfall_senioren_${field}`
                        : `unfall_berufsgruppe_${group}_${field}`;

                return typeof data[key] === 'number' ? data[key] as number : 0;
            };

            contractData.insured_persons?.forEach((person, index) => {
                let personPremie = 0;
                personPremie += toNumber(person.increased_benefit_clause) * getFactor(person, 'mehrleistung');
                personPremie += toNumber(person.basic_sum) * getFactor(person, `invaliditaet_${person.disability_coverage}`);
                personPremie += toNumber(person.accident_pension) * getFactor(person, 'unfallrente');
                personPremie += toNumber(person.accidental_death) * getFactor(person, 'unfalltod');
                personPremie += toNumber(person.daily_sickness_allowance) * getFactor(person, 'krankentagegeld');
                personPremie += toNumber(person.hospital_daily_allowance) * getFactor(person, 'krankenhaustagegeld');
                personPremie += toNumber(person.transitional_benefit) * getFactor(person, 'uebergangsleistung');

                if (person.first_aid_module) {
                    personPremie += getFactor(person, 'erste_hilfe');
                }

                // 10% discount
                if (index > 0) {
                    personPremie *= 0.9;
                }

                totalPremie += personPremie;
            });

            contractData.premie = totalPremie;
            contractData.first_invoice_net = contractData.premie / 365 * invoiceDays;
            break;
        }


    }

    //
    // — now do *all* of the common invoice math in one spot —
    //
    const taxRates: Record<string, number> = {
        hausrat: data.hausrat_tax,
        wohngebaeude: data.wohngebaude_tax,
        tierhalterhaftpflicht: data.tierhalterhaftpflicht_tax,
        privathaftpflicht: data.privathaftpflicht_tax,
        haus_und_grundbesitzerhaftpflicht: data.haus_und_grundbesitzerhaftpflicht_tax,
        bauleistung: data.bauleistung_tax,
        bauherrenhaftpflicht: data.bauherrenhaftpflicht_tax,
        geschaeftsversicherung: data.geschaeftsversicherung_tax,
        gebaeudeversicherung: data.gebaeudeversicherung_tax,
        betriebshaftpflicht: data.betriebshaftpflicht_tax,
        unfallversicherung: data.unfall_tax
    };
    const rate = taxRates[contractData.contract_type];

    // first-invoice
    contractData.first_invoice_tax = contractData.first_invoice_net! * rate;
    contractData.first_invoice_gross = contractData.first_invoice_net! + contractData.first_invoice_tax;

    if (contractData.glass_insurance) {
        contractData.glass_tax = contractData.premie_glass_insurance! * data.wohngebaude_glasversicherung_tax;
        contractData.first_invoice_glass_tax = contractData.first_invoice_glass_net! * rate;
        contractData.first_invoice_glass_gross = contractData.first_invoice_glass_net! + contractData.first_invoice_glass_tax;
        contractData.first_invoice_gross += contractData.first_invoice_glass_gross;
    }

    // total invoice (add glass if present)
    contractData.tax = contractData.premie! * rate;
    contractData.invoice_amount = contractData.premie! + contractData.tax
        + (contractData.premie_glass_insurance
            ? contractData.premie_glass_insurance + (contractData.premie_glass_insurance * data.wohngebaude_glasversicherung_tax)
            : 0
        );

    return contractData;
}

export async function calculateContractWithPremie(
    contractData: ContractData,
    token: string,
    premie: number
) {
    const data: CalculationParametersData = await getCalculationParameters(
        token,
        contractData.insurance_start_date
    );

    // parse incoming premie
    contractData.premie = parseFloat(premie as unknown as string);

    // --- your existing per-type tax & invoice_amount logic ---
    switch (contractData.contract_type) {
        case 'hausrat': {
            contractData.tax = contractData.premie * data.hausrat_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'wohngebaeude': {
            contractData.glass_tax = (contractData.premie_glass_insurance ?? 0) * data.wohngebaude_glasversicherung_tax;
            contractData.tax = contractData.premie * data.wohngebaude_tax;
            contractData.invoice_amount =
                contractData.premie +
                contractData.tax +
                (contractData.premie_glass_insurance
                    ? contractData.premie_glass_insurance + contractData.glass_tax
                    : 0);
            break;
        }

        case 'tierhalterhaftpflicht': {
            contractData.tax = contractData.premie * data.tierhalterhaftpflicht_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'privathaftpflicht': {
            contractData.tax = contractData.premie * data.privathaftpflicht_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'haus_und_grundbesitzerhaftpflicht': {
            contractData.tax = contractData.premie * data.haus_und_grundbesitzerhaftpflicht_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'bauleistung': {
            contractData.tax = contractData.premie * data.bauleistung_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'bauherrenhaftpflicht': {
            contractData.tax = data.bauherrenhaftpflicht_tax * contractData.premie;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'geschaeftsversicherung': {
            contractData.tax = contractData.premie * data.geschaeftsversicherung_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'gebaeudeversicherung': {
            contractData.tax = contractData.premie * data.gebaeudeversicherung_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'betriebshaftpflicht': {
            contractData.tax = contractData.premie * data.betriebshaftpflicht_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }

        case 'unfallversicherung': {
            contractData.tax = contractData.premie * data.unfall_tax;
            contractData.invoice_amount = contractData.premie + contractData.tax;
            break;
        }
    }

    // --- now do the common "first invoice" maths once ---

    const invoiceDays = calculateInvoiceDays(
        contractData.insurance_start_date,
        contractData.insurance_end_date,
        contractData.insurance_main_due_date!,
        contractData.payment_mode!
    );

    // net amount for first invoice
    contractData.first_invoice_net =
        contractData.premie! / 365 * invoiceDays;

    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_net = contractData.premie_glass_insurance! / 365 * invoiceDays;
    }

    // pick the correct tax rate
    const taxRates: Record<string, number> = {
        hausrat: data.hausrat_tax,
        wohngebaeude: data.wohngebaude_tax,
        tierhalterhaftpflicht: data.tierhalterhaftpflicht_tax,
        privathaftpflicht: data.privathaftpflicht_tax,
        haus_und_grundbesitzerhaftpflicht: data.haus_und_grundbesitzerhaftpflicht_tax,
        bauleistung: data.bauleistung_tax,
        bauherrenhaftpflicht: data.bauherrenhaftpflicht_tax,
        geschaeftsversicherung: data.geschaeftsversicherung_tax,
        gebaeudeversicherung: data.gebaeudeversicherung_tax,
        betriebshaftpflicht: data.betriebshaftpflicht_tax,
        unfallversicherung: data.unfall_tax
    };
    const rate = taxRates[contractData.contract_type];

    // tax on that first invoice
    contractData.first_invoice_tax = contractData.first_invoice_net * rate;

    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_tax = contractData.first_invoice_glass_net! * data.wohngebaude_glasversicherung_tax;
    }

    // gross first invoice
    contractData.first_invoice_gross =
        contractData.first_invoice_net + contractData.first_invoice_tax;

    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_gross = contractData.first_invoice_glass_net! * contractData.first_invoice_glass_tax!;
    }

    return contractData;
}

export async function calculateContractWithInvoiceAmount(
    contractData: ContractData,
    token: string
) {
    const data: CalculationParametersData = await getCalculationParameters(
        token,
        contractData.insurance_start_date
    );

    // — first, invert the total invoice_amount into premie & tax —
    switch (contractData.contract_type) {
        case 'hausrat': {
            contractData.premie = contractData.invoice_amount! / (1 + data.hausrat_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'wohngebaeude': {
            if (contractData.glass_insurance) {
                if (contractData.premie_glass_insurance) {
                    contractData.premie =
                        (contractData.invoice_amount! -
                            contractData.premie_glass_insurance * (1 + data.wohngebaude_glasversicherung_tax)
                        ) / (1 + data.wohngebaude_tax);

                    contractData.tax = contractData.premie * data.wohngebaude_tax;
                    contractData.glass_tax = contractData.premie_glass_insurance * data.wohngebaude_glasversicherung_tax;
                } else {
                    // glass-premie not yet set, derive from sum_1914
                    contractData.premie_glass_insurance =
                        contractData.insurance_sum_1914! *
                        data.wohngebaude_glass_insurance *
                        data.wohngebaude_beitragsfaktor;

                    contractData.glass_tax = contractData.premie_glass_insurance * data.wohngebaude_glasversicherung_tax;

                    contractData.premie =
                        (contractData.invoice_amount! -
                            contractData.premie_glass_insurance * (1 + data.wohngebaude_glasversicherung_tax)
                        ) / (1 + data.wohngebaude_tax);

                    contractData.tax = contractData.premie * data.wohngebaude_tax;
                }
            } else {
                contractData.premie = contractData.invoice_amount! / (1 + data.wohngebaude_tax);
                contractData.tax = contractData.invoice_amount! - contractData.premie;
            }
            break;
        }

        case 'tierhalterhaftpflicht': {
            contractData.premie = contractData.invoice_amount! / (1 + data.tierhalterhaftpflicht_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'privathaftpflicht': {
            contractData.premie = contractData.invoice_amount! / (1 + data.privathaftpflicht_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'haus_und_grundbesitzerhaftpflicht': {
            contractData.premie = contractData.invoice_amount! / (1 + data.haus_und_grundbesitzerhaftpflicht_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'bauleistung': {
            contractData.premie = contractData.invoice_amount! / (1 + data.bauleistung_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'bauherrenhaftpflicht': {
            contractData.premie = contractData.invoice_amount! / (1 + data.bauherrenhaftpflicht_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'geschaeftsversicherung': {
            contractData.premie = contractData.invoice_amount! / (1 + data.geschaeftsversicherung_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'gebaeudeversicherung': {
            contractData.premie = contractData.invoice_amount! / (1 + data.gebaeudeversicherung_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'betriebshaftpflicht': {
            contractData.premie = contractData.invoice_amount! / (1 + data.betriebshaftpflicht_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }

        case 'unfallversicherung': {
            contractData.premie = contractData.invoice_amount! / (1 + data.unfall_tax);
            contractData.tax = contractData.invoice_amount! - contractData.premie;
            break;
        }
    }

    // — now compute the first‐invoice slice, with glass special case —
    const invoiceDays = calculateInvoiceDays(
        contractData.insurance_start_date,
        contractData.insurance_end_date,
        contractData.insurance_main_due_date!,
        contractData.payment_mode!
    );

    // net on first invoice
    contractData.first_invoice_net = contractData.premie! / 365 * invoiceDays;

    // glass net slice if any
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_net =
            contractData.premie_glass_insurance! / 365 * invoiceDays;
    }

    // lookup correct tax rate for this product
    const taxRates: Record<string, number> = {
        hausrat: data.hausrat_tax,
        wohngebaeude: data.wohngebaude_tax,
        tierhalterhaftpflicht: data.tierhalterhaftpflicht_tax,
        privathaftpflicht: data.privathaftpflicht_tax,
        haus_und_grundbesitzerhaftpflicht: data.haus_und_grundbesitzerhaftpflicht_tax,
        bauleistung: data.bauleistung_tax,
        bauherrenhaftpflicht: data.bauherrenhaftpflicht_tax,
        geschaeftsversicherung: data.geschaeftsversicherung_tax,
        gebaeudeversicherung: data.gebaeudeversicherung_tax,
        betriebshaftpflicht: data.betriebshaftpflicht_tax,
        unfallversicherung: data.unfall_tax
    };
    const rate = taxRates[contractData.contract_type];

    // tax on that first invoice net
    contractData.first_invoice_tax = contractData.first_invoice_net * rate;

    // glass‐tax slice
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_tax =
            contractData.first_invoice_glass_net! * data.wohngebaude_glasversicherung_tax;
    }

    // gross on first invoice
    contractData.first_invoice_gross = contractData.first_invoice_net + contractData.first_invoice_tax;

    // glass gross slice
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_gross =
            contractData.first_invoice_glass_net! + contractData.first_invoice_glass_tax!;
    }

    return contractData;
}

export async function calculateContractWithPremieAndInvoiceAmount(
    contractData: ContractData,
    token: string
) {
    const data: CalculationParametersData = await getCalculationParameters(
        token,
        contractData.insurance_start_date
    );

    // — first, derive the total tax (and glass tax) from the passed-in premie & invoice_amount —
    switch (contractData.contract_type) {
        case 'hausrat': {
            contractData.tax = contractData.invoice_amount! - contractData.premie!;
            break;
        }

        case 'wohngebaeude': {
            if (contractData.glass_insurance) {
                if (contractData.premie_glass_insurance) {
                    contractData.tax = contractData.premie! * data.wohngebaude_tax;
                    contractData.glass_tax = contractData.premie_glass_insurance * data.wohngebaude_glasversicherung_tax;
                } else {
                    // derive glass-premie if missing
                    contractData.premie_glass_insurance =
                        contractData.insurance_sum_1914! *
                        data.wohngebaude_glass_insurance *
                        data.wohngebaude_beitragsfaktor;

                    contractData.glass_tax = contractData.premie_glass_insurance * data.wohngebaude_glasversicherung_tax;
                    contractData.tax = contractData.premie! * data.wohngebaude_tax;
                }
            } else {
                contractData.tax = contractData.invoice_amount! - contractData.premie!;
            }
            break;
        }

        case 'tierhalterhaftpflicht':
        case 'privathaftpflicht':
        case 'haus_und_grundbesitzerhaftpflicht':
        case 'bauleistung':
        case 'bauherrenhaftpflicht':
        case 'geschaeftsversicherung':
        case 'gebaeudeversicherung':
        case 'betriebshaftpflicht': 
        case 'unfallversicherung': {
            // all these simply invert invoice_amount = premie + tax
            contractData.tax = contractData.invoice_amount! - contractData.premie!;
            break;
        }
    }

    // — now compute the "first invoice" slice for net, glass-net, tax & gross —
    const invoiceDays = calculateInvoiceDays(
        contractData.insurance_start_date,
        contractData.insurance_end_date,
        contractData.insurance_main_due_date!,
        contractData.payment_mode!
    );

    // net portion of the first invoice
    contractData.first_invoice_net = contractData.premie! / 365 * invoiceDays;

    // if glass coverage exists, its net slice
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_net =
            contractData.premie_glass_insurance! / 365 * invoiceDays;
    }

    // map to find the correct tax‐rate for the product
    const taxRates: Record<string, number> = {
        hausrat: data.hausrat_tax,
        wohngebaeude: data.wohngebaude_tax,
        tierhalterhaftpflicht: data.tierhalterhaftpflicht_tax,
        privathaftpflicht: data.privathaftpflicht_tax,
        haus_und_grundbesitzerhaftpflicht: data.haus_und_grundbesitzerhaftpflicht_tax,
        bauleistung: data.bauleistung_tax,
        bauherrenhaftpflicht: data.bauherrenhaftpflicht_tax,
        geschaeftsversicherung: data.geschaeftsversicherung_tax,
        gebaeudeversicherung: data.gebaeudeversicherung_tax,
        betriebshaftpflicht: data.betriebshaftpflicht_tax,
        unfallversicherung: data.unfall_tax
    };
    const rate = taxRates[contractData.contract_type];

    // tax on that first-invoice net
    contractData.first_invoice_tax = contractData.first_invoice_net * rate;

    // glass‐tax slice, if any
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_tax =
            contractData.first_invoice_glass_net! * data.wohngebaude_glasversicherung_tax;
    }

    // gross first invoice
    contractData.first_invoice_gross =
        contractData.first_invoice_net + contractData.first_invoice_tax;

    // glass gross slice
    if (contractData.glass_insurance) {
        contractData.first_invoice_glass_gross =
            contractData.first_invoice_glass_net! + contractData.first_invoice_glass_tax!;
    }

    return contractData;
}


export async function getCalculationParameters(token: string, startDate: string) {
    const data = await fetch(`${process.env.STRAPI_BASE_URL}/calculation-parameters`, {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` }
    })
    if (!data.ok) {
        throw new Error("CALCULATION_PARAMETERS not found")
    }
    const dataJson = await data.json()
    const calculationParameterList: CalculationParametersData[] = dataJson['data']

    const final_date = findClosestPastDate(calculationParameterList, startDate)

    return calculationParameterList.filter((entry) => entry.valid_from == final_date)[0]
}

function findClosestPastDate(dates: CalculationParametersData[], referenceDate: string) {
    const refDate = new Date(referenceDate);
    // Convert dates to date-objects and filter those that are earlier or the same as referenceDate
    const pastDates = dates
        .map(date => new Date(date.valid_from))
        .filter(date => date <= refDate); // now also same date
    if (pastDates.length === 0) return null;
    // Return the closest date that is the same or before referenceDate
    return pastDates.reduce((closest, date) =>
        date > closest ? date : closest
    ).toISOString().split('T')[0]; // Format YYYY-MM-DD
}

