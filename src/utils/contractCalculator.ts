// src/utils/contractClaculator.ts

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { type CalculationParameterService } from '@/server/calculationParameter/CalculationParameterService';
import { type AccidentInsuranceFormData, type AnimalData } from '@/types';

import { calculateInvoiceDays } from './calculationUtil';

export async function calculateContract(
  contractData: Contract,
  calculationParameterService: CalculationParameterService
) {
  const data: CalculationParameter =
    (await calculationParameterService.getCalculationParameters(
      contractData.insuranceStartDate || ''
    ))!;

  const invoiceDays = calculateInvoiceDays(
    contractData.insuranceStartDate!,
    contractData.insuranceEndDate!,
    contractData.insuranceMainDueDate!,
    contractData.paymentMode!
  );

  switch (contractData.contractType) {
    case 'hausrat': {
      if (contractData.isElementar) {
        contractData.premie =
          contractData.insuranceSum! * data.hausratVersicherteGefahren! +
          data.hausratIsElementar! * contractData.insuranceSum!;
      } else {
        contractData.premie =
          contractData.insuranceSum! * data.hausratVersicherteGefahren!;
      }

      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'wohngebaeude': {
      const base =
        data.wohngebaudeVersicherteGefahren! * contractData.insuranceSum1914!;
      const element =
        data.wohngebaudeIsElementar! * contractData.insuranceSum1914!;
      contractData.premie =
        data.wohngebaudeBeitragsfaktor! *
        (contractData.isElementar ? base + element : base);

      if (contractData.householdTech) {
        contractData.premieHouseholdTech =
          contractData.insuranceSum1914! *
          data.wohngebaudeHouseholdTech! *
          data.wohngebaudeBeitragsfaktor!;
        contractData.premie += contractData.premieHouseholdTech;
      }
      if (contractData.pvSystem) {
        contractData.premiePvSystem =
          contractData.insuranceSum1914! *
          data.wohngebaudePvSystem! *
          data.wohngebaudeBeitragsfaktor!;
        contractData.premie += contractData.premiePvSystem;
      }
      if (contractData.glassInsurance) {
        contractData.premieGlassInsurance =
          contractData.insuranceSum1914! *
          data.wohngebaudeGlassInsurance! *
          data.wohngebaudeBeitragsfaktor!;
        contractData.firstInvoiceGlassNet =
          (contractData.premieGlassInsurance / 365) * invoiceDays;
        // note: we do *not* add glass to premie here; we'll handle it in the total invoice below
      }

      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'tierhalterhaftpflicht': {
      const animals =
        (contractData.animalData as unknown as AnimalData[]) ?? [];
      const countDog = animals.filter((a) => a.animal_type === 'Hund').length;
      const countHorse = animals.filter((a) => a.animal_type == 'Pferd').length;

      let dog = countDog * data.tierhalterhaftpflichtDogPremie!;
      let horse = countHorse * data.tierhalterhaftpflichtHorsePremie!;
      if (countDog > 1)
        dog =
          data.tierhalterhaftpflichtDogPremie! +
          (countDog - 1) *
            data.tierhalterhaftpflichtDogPremie! *
            data.tierhalterhaftpflichtSaleDog!;
      if (countHorse > 1)
        horse =
          data.tierhalterhaftpflichtHorsePremie! +
          (countHorse - 1) *
            data.tierhalterhaftpflichtHorsePremie! *
            data.tierhalterhaftpflichtSaleHorse!;

      contractData.premie = dog + horse;
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'privathaftpflicht': {
      contractData.premie = contractData.familyCoverage
        ? data.privathaftpflichtSinglePremie
        : data.privathaftpflichtFamilyPremie;
      contractData.firstInvoiceNet = (contractData.premie! / 365) * invoiceDays;
      break;
    }

    case 'hausUndGrundbesitzerhaftpflicht': {
      contractData.premie = data.hausUndGrundbesitzerhaftpflichtPremie;
      contractData.firstInvoiceNet = (contractData.premie! / 365) * invoiceDays;
      break;
    }

    case 'bauleistung': {
      const grossPrem = Math.max(
        contractData.insuranceSum! * data.bauleistungSatz!,
        data.bauleistungMin!
      );

      contractData.premie = grossPrem;
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'bauherrenhaftpflicht': {
      const grossPrem = Math.max(
        contractData.buildingSum! * data.bauherrenhaftpflichtSatz!,
        data.bauherrenhaftpflichtMin!
      );
      contractData.premie = grossPrem;
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'geschaeftsversicherung': {
      const mult = {
        Handel: data.geschaeftsversicherungHandel,
        Handwerk: data.geschaeftsversicherungHandwerk,
        Dienstleistungen: data.geschaeftsversicherungDienstleistungen,
        Gastronomie: data.geschaeftsversicherungGastronomie,
      }[contractData.tariffGroup!];

      const busPrem = contractData.insuranceSum! * mult!;
      const elePrem = contractData.isElementar
        ? contractData.insuranceSum! * data.geschaeftsversicherungIsElementar!
        : 0;
      contractData.premie = Math.max(
        busPrem + elePrem,
        data.geschaeftsversicherungMin!
      );
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'gebaeudeversicherung': {
      const mult = {
        Handel: data.gebaeudeversicherungHandel,
        Handwerk: data.gebaeudeversicherungHandwerk,
        Dienstleistungen: data.gebaeudeversicherungDienstleistungen,
        Gastronomie: data.gebaeudeversicherungGastronomie,
      }[contractData.tariffGroup!];

      const busPrem = contractData.insuranceSum! * mult!;
      const elePrem = contractData.isElementar
        ? contractData.insuranceSum! * data.gebaeudeversicherungIsElementar!
        : 0;
      contractData.premie = Math.max(
        busPrem + elePrem,
        data.gebaeudeversicherungMin!
      );
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'betriebshaftpflicht': {
      const mult = {
        Handel: data.betriebshaftpflichtHandel,
        Handwerk: data.betriebshaftpflichtHandwerk,
        Dienstleistungen: data.betriebshaftpflichtDienstleistungen,
        Gastronomie: data.betriebshaftpflichtGastronomie,
      }[contractData.tariffGroup!];

      contractData.premie = mult!;
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }

    case 'unfallversicherung': {
      let totalPremie = 0;

      const toNumber = (value: string | number | undefined): number => {
        const n = typeof value === 'string' ? parseFloat(value) : value;
        return isNaN(n ?? NaN) ? 0 : n!;
      };

      const getFactor = (
        person: AccidentInsuranceFormData,
        field: string
      ): number => {
        const type = person.type?.toLowerCase(); // 'kind', 'senior' or 'erwachsener'
        const group = person.occupation_group?.slice(-1).toLowerCase(); // 'a' or 'b'

        const key =
          type === 'kind'
            ? `unfallKinder${field}`
            : type === 'senior'
              ? `unfallSenioren${field}`
              : `unfallBerufsgruppe${group!.toUpperCase()}${field.charAt(0).toUpperCase() + field.slice(1)}`;

        return typeof (data as any)[key] === 'number'
          ? ((data as any)[key] as number)
          : 0;
      };

      (
        contractData.insuredPersons as unknown as AccidentInsuranceFormData[]
      )?.forEach((person, index) => {
        let personPremie = 0;
        personPremie +=
          toNumber(person.increased_benefit_clause) *
          getFactor(person, 'mehrleistung');
        personPremie +=
          toNumber(person.basic_sum) *
          getFactor(person, `invaliditaet${person.disability_coverage}`);
        personPremie +=
          toNumber(person.accident_pension) * getFactor(person, 'unfallrente');
        personPremie +=
          toNumber(person.accidental_death) * getFactor(person, 'unfalltod');
        personPremie +=
          toNumber(person.daily_sickness_allowance) *
          getFactor(person, 'krankentagegeld');
        personPremie +=
          toNumber(person.hospital_daily_allowance) *
          getFactor(person, 'krankenhaustagegeld');
        personPremie +=
          toNumber(person.transitional_benefit) *
          getFactor(person, 'uebergangsleistung');

        if (person.first_aid_module) {
          personPremie += getFactor(person, 'erste_hilfe');
        }

        // 10% discount
        if (index > 0) {
          personPremie *= 0.9;
        }

        totalPremie += personPremie;
      });

      contractData.premie = totalPremie;
      contractData.firstInvoiceNet = (contractData.premie / 365) * invoiceDays;
      break;
    }
  }

  //
  // — now do *all* of the common invoice math in one spot —
  //
  const taxRates: Record<string, number> = {
    hausrat: data.hausratTax!,
    wohngebaeude: data.wohngebaudeTax!,
    tierhalterhaftpflicht: data.tierhalterhaftpflichtTax!,
    privathaftpflicht: data.privathaftpflichtTax!,
    hausUndGrundbesitzerhaftpflicht: data.hausUndGrundbesitzerhaftpflichtTax!,
    bauleistung: data.bauleistungTax!,
    bauherrenhaftpflicht: data.bauherrenhaftpflichtTax!,
    geschaeftsversicherung: data.geschaeftsversicherungTax!,
    gebaeudeversicherung: data.gebaeudeversicherungTax!,
    betriebshaftpflicht: data.betriebshaftpflichtTax!,
    unfallversicherung: data.unfallTax!,
  };
  const rate = taxRates[contractData.contractType!];

  // first-invoice
  contractData.firstInvoiceTax = contractData.firstInvoiceNet! * rate;
  contractData.firstInvoiceGross =
    contractData.firstInvoiceNet! + contractData.firstInvoiceTax;

  if (contractData.glassInsurance) {
    contractData.glassTax =
      contractData.premieGlassInsurance! * data.wohngebaudeGlasversicherungTax!;
    contractData.firstInvoiceGlassTax =
      contractData.firstInvoiceGlassNet! * rate;
    contractData.firstInvoiceGlassGross =
      contractData.firstInvoiceGlassNet! + contractData.firstInvoiceGlassTax;
    contractData.firstInvoiceGross += contractData.firstInvoiceGlassGross;
  }

  // total invoice (add glass if present)
  contractData.tax = contractData.premie! * rate;
  contractData.invoiceAmount =
    contractData.premie! +
    contractData.tax +
    (contractData.premieGlassInsurance
      ? contractData.premieGlassInsurance +
        contractData.premieGlassInsurance * data.wohngebaudeGlasversicherungTax!
      : 0);
  return contractData;
}

export async function calculateContractWithPremie(
  contractData: Contract,
  calculationParameterService: CalculationParameterService,
  premie: number
) {
  const data: CalculationParameter =
    (await calculationParameterService.getCalculationParameters(
      contractData.insuranceStartDate || ''
    ))!;

  // parse incoming premie
  contractData.premie = parseFloat(premie as unknown as string);

  switch (contractData.contractType) {
    case 'hausrat': {
      contractData.tax = contractData.premie * data.hausratTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'wohngebaeude': {
      contractData.glassTax =
        (contractData.premieGlassInsurance ?? 0) *
        data.wohngebaudeGlasversicherungTax!;
      contractData.tax = contractData.premie * data.wohngebaudeTax!;
      contractData.invoiceAmount =
        contractData.premie +
        contractData.tax +
        (contractData.premieGlassInsurance
          ? contractData.premieGlassInsurance + contractData.glassTax
          : 0);
      break;
    }

    case 'tierhalterhaftpflicht': {
      contractData.tax = contractData.premie * data.tierhalterhaftpflichtTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'privathaftpflicht': {
      contractData.tax = contractData.premie * data.privathaftpflichtTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'hausUndGrundbesitzerhaftpflicht': {
      contractData.tax =
        contractData.premie * data.hausUndGrundbesitzerhaftpflichtTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'bauleistung': {
      contractData.tax = contractData.premie * data.bauleistungTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'bauherrenhaftpflicht': {
      contractData.tax = data.bauherrenhaftpflichtTax! * contractData.premie;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'geschaeftsversicherung': {
      contractData.tax = contractData.premie * data.geschaeftsversicherungTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'gebaeudeversicherung': {
      contractData.tax = contractData.premie * data.gebaeudeversicherungTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'betriebshaftpflicht': {
      contractData.tax = contractData.premie * data.betriebshaftpflichtTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }

    case 'unfallversicherung': {
      contractData.tax = contractData.premie * data.unfallTax!;
      contractData.invoiceAmount = contractData.premie + contractData.tax;
      break;
    }
  }

  // invoice calculations
  const invoiceDays = calculateInvoiceDays(
    contractData.insuranceStartDate!,
    contractData.insuranceEndDate!,
    contractData.insuranceMainDueDate!,
    contractData.paymentMode!
  );

  contractData.firstInvoiceNet = (contractData.premie! / 365) * invoiceDays;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassNet =
      (contractData.premieGlassInsurance! / 365) * invoiceDays;
  }

  const taxRates: Record<string, number> = {
    hausrat: data.hausratTax!,
    wohngebaeude: data.wohngebaudeTax!,
    tierhalterhaftpflicht: data.tierhalterhaftpflichtTax!,
    privathaftpflicht: data.privathaftpflichtTax!,
    hausUndGrundbesitzerhaftpflicht: data.hausUndGrundbesitzerhaftpflichtTax!,
    bauleistung: data.bauleistungTax!,
    bauherrenhaftpflicht: data.bauherrenhaftpflichtTax!,
    geschaeftsversicherung: data.geschaeftsversicherungTax!,
    gebaeudeversicherung: data.gebaeudeversicherungTax!,
    betriebshaftpflicht: data.betriebshaftpflichtTax!,
    unfallversicherung: data.unfallTax!,
  };

  const rate = taxRates[contractData.contractType!];

  contractData.firstInvoiceTax = contractData.firstInvoiceNet * rate;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassTax =
      contractData.firstInvoiceGlassNet! * data.wohngebaudeGlasversicherungTax!;
  }

  contractData.firstInvoiceGross =
    contractData.firstInvoiceNet + contractData.firstInvoiceTax;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassGross =
      contractData.firstInvoiceGlassNet! * contractData.firstInvoiceGlassTax!;
  }

  return contractData;
}

export async function calculateContractWithInvoiceAmount(
  contractData: Contract,
  calculationParameterService: CalculationParameterService
) {
  const data: CalculationParameter =
    (await calculationParameterService.getCalculationParameters(
      contractData.insuranceStartDate || ''
    ))!;

  // — invert total invoiceAmount into premie & tax —
  switch (contractData.contractType) {
    case 'hausrat': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.hausratTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'wohngebaeude': {
      if (contractData.glassInsurance) {
        if (contractData.premieGlassInsurance) {
          contractData.premie =
            (contractData.invoiceAmount! -
              contractData.premieGlassInsurance *
                (1 + data.wohngebaudeGlasversicherungTax!)) /
            (1 + data.wohngebaudeTax!);

          contractData.tax = contractData.premie * data.wohngebaudeTax!;
          contractData.glassTax =
            contractData.premieGlassInsurance *
            data.wohngebaudeGlasversicherungTax!;
        } else {
          // derive glass premie from sum1914
          contractData.premieGlassInsurance =
            contractData.insuranceSum1914! *
            data.wohngebaudeGlassInsurance! *
            data.wohngebaudeBeitragsfaktor!;

          contractData.glassTax =
            contractData.premieGlassInsurance *
            data.wohngebaudeGlasversicherungTax!;

          contractData.premie =
            (contractData.invoiceAmount! -
              contractData.premieGlassInsurance *
                (1 + data.wohngebaudeGlasversicherungTax!)) /
            (1 + data.wohngebaudeTax!);

          contractData.tax = contractData.premie * data.wohngebaudeTax!;
        }
      } else {
        contractData.premie =
          contractData.invoiceAmount! / (1 + data.wohngebaudeTax!);
        contractData.tax = contractData.invoiceAmount! - contractData.premie;
      }
      break;
    }

    case 'tierhalterhaftpflicht': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.tierhalterhaftpflichtTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'privathaftpflicht': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.privathaftpflichtTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'hausUndGrundbesitzerhaftpflicht': {
      contractData.premie =
        contractData.invoiceAmount! /
        (1 + data.hausUndGrundbesitzerhaftpflichtTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'bauleistung': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.bauleistungTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'bauherrenhaftpflicht': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.bauherrenhaftpflichtTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'geschaeftsversicherung': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.geschaeftsversicherungTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'gebaeudeversicherung': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.gebaeudeversicherungTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'betriebshaftpflicht': {
      contractData.premie =
        contractData.invoiceAmount! / (1 + data.betriebshaftpflichtTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }

    case 'unfallversicherung': {
      contractData.premie = contractData.invoiceAmount! / (1 + data.unfallTax!);
      contractData.tax = contractData.invoiceAmount! - contractData.premie;
      break;
    }
  }

  // — compute first invoice slice —
  const invoiceDays = calculateInvoiceDays(
    contractData.insuranceStartDate!,
    contractData.insuranceEndDate!,
    contractData.insuranceMainDueDate!,
    contractData.paymentMode!
  );

  contractData.firstInvoiceNet = (contractData.premie! / 365) * invoiceDays;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassNet =
      (contractData.premieGlassInsurance! / 365) * invoiceDays;
  }

  // tax rate lookup
  const taxRates: Record<string, number> = {
    hausrat: data.hausratTax!,
    wohngebaeude: data.wohngebaudeTax!,
    tierhalterhaftpflicht: data.tierhalterhaftpflichtTax!,
    privathaftpflicht: data.privathaftpflichtTax!,
    hausUndGrundbesitzerhaftpflicht: data.hausUndGrundbesitzerhaftpflichtTax!,
    bauleistung: data.bauleistungTax!,
    bauherrenhaftpflicht: data.bauherrenhaftpflichtTax!,
    geschaeftsversicherung: data.geschaeftsversicherungTax!,
    gebaeudeversicherung: data.gebaeudeversicherungTax!,
    betriebshaftpflicht: data.betriebshaftpflichtTax!,
    unfallversicherung: data.unfallTax!,
  };

  const rate = taxRates[contractData.contractType!];

  contractData.firstInvoiceTax = contractData.firstInvoiceNet * rate;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassTax =
      contractData.firstInvoiceGlassNet! * data.wohngebaudeGlasversicherungTax!;
  }

  contractData.firstInvoiceGross =
    contractData.firstInvoiceNet + contractData.firstInvoiceTax;

  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassGross =
      contractData.firstInvoiceGlassNet! + contractData.firstInvoiceGlassTax!;
  }

  return contractData;
}

export async function calculateContractWithPremieAndInvoiceAmount(
  contractData: Contract,
  calculationParameterService: CalculationParameterService
) {
  const data: CalculationParameter =
    (await calculationParameterService.getCalculationParameters(
      contractData.insuranceStartDate || ''
    ))!;

  // — derive total tax (and glass tax) from passed-in premie & invoiceAmount —
  switch (contractData.contractType) {
    case 'hausrat': {
      contractData.tax = contractData.invoiceAmount! - contractData.premie!;
      break;
    }

    case 'wohngebaeude': {
      if (contractData.glassInsurance) {
        if (contractData.premieGlassInsurance) {
          contractData.tax = contractData.premie! * data.wohngebaudeTax!;
          contractData.glassTax =
            contractData.premieGlassInsurance *
            data.wohngebaudeGlasversicherungTax!;
        } else {
          // derive glass-premie if missing
          contractData.premieGlassInsurance =
            contractData.insuranceSum1914! *
            data.wohngebaudeGlassInsurance! *
            data.wohngebaudeBeitragsfaktor!;

          contractData.glassTax =
            contractData.premieGlassInsurance *
            data.wohngebaudeGlasversicherungTax!;
          contractData.tax = contractData.premie! * data.wohngebaudeTax!;
        }
      } else {
        contractData.tax = contractData.invoiceAmount! - contractData.premie!;
      }
      break;
    }

    case 'tierhalterhaftpflicht':
    case 'privathaftpflicht':
    case 'hausUndGrundbesitzerhaftpflicht':
    case 'bauleistung':
    case 'bauherrenhaftpflicht':
    case 'geschaeftsversicherung':
    case 'gebaeudeversicherung':
    case 'betriebshaftpflicht':
    case 'unfallversicherung': {
      // all these simply invert invoiceAmount = premie + tax
      contractData.tax = contractData.invoiceAmount! - contractData.premie!;
      break;
    }
  }

  // — compute "first invoice" slice for net, glass-net, tax & gross —
  const invoiceDays = calculateInvoiceDays(
    contractData.insuranceStartDate!,
    contractData.insuranceEndDate!,
    contractData.insuranceMainDueDate!,
    contractData.paymentMode!
  );

  // net portion of the first invoice
  contractData.firstInvoiceNet = (contractData.premie! / 365) * invoiceDays;

  // glass net slice
  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassNet =
      (contractData.premieGlassInsurance! / 365) * invoiceDays;
  }

  // map to find correct tax rate for product
  const taxRates: Record<string, number> = {
    hausrat: data.hausratTax!,
    wohngebaeude: data.wohngebaudeTax!,
    tierhalterhaftpflicht: data.tierhalterhaftpflichtTax!,
    privathaftpflicht: data.privathaftpflichtTax!,
    hausUndGrundbesitzerhaftpflicht: data.hausUndGrundbesitzerhaftpflichtTax!,
    bauleistung: data.bauleistungTax!,
    bauherrenhaftpflicht: data.bauherrenhaftpflichtTax!,
    geschaeftsversicherung: data.geschaeftsversicherungTax!,
    gebaeudeversicherung: data.gebaeudeversicherungTax!,
    betriebshaftpflicht: data.betriebshaftpflichtTax!,
    unfallversicherung: data.unfallTax!,
  };
  const rate = taxRates[contractData.contractType!];

  // tax on first-invoice net
  contractData.firstInvoiceTax = contractData.firstInvoiceNet * rate;

  // glass-tax slice
  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassTax =
      contractData.firstInvoiceGlassNet! * data.wohngebaudeGlasversicherungTax!;
  }

  // gross first invoice
  contractData.firstInvoiceGross =
    contractData.firstInvoiceNet + contractData.firstInvoiceTax;

  // glass gross slice
  if (contractData.glassInsurance) {
    contractData.firstInvoiceGlassGross =
      contractData.firstInvoiceGlassNet! + contractData.firstInvoiceGlassTax!;
  }

  return contractData;
}
