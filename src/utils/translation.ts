import { createTranslator } from 'next-intl';

import {
  type KvcFormFieldProps,
  ParseFormFieldsFromJson,
} from '@/components/forms/domain';

import deMessages from '../../messages/de.json';
// const messages = {
//   Home: {
//     title: 'Hello World',
//     subtitle: 'Welcome back!',
//   },
// };
// const messages = deMessages as any;

export const tNextIntl = createTranslator({
  locale: 'en',
  messages: deMessages,
});
// const t = createTranslator({ locale: 'en', deMessages });

export function makeT() {
  return createTranslator({
    locale: 'en',
    messages: deMessages,
  });
}

export function GetFormConfigurationFromIntlById(
  t: ReturnType<typeof createTranslator>,
  formId: string
): KvcFormFieldProps[] {
  const result: KvcFormFieldProps[] = [];
  try {
    if (
      typeof t !== 'undefined' &&
      t !== null &&
      typeof formId === 'string' &&
      formId.trim().length > 0
    ) {
      const rawForm = t.raw(`form_configurations.${formId}`);
      const form = ParseFormFieldsFromJson(rawForm);
      return form;
    }
  } catch (_) {
    //
  }
  return result;
}
