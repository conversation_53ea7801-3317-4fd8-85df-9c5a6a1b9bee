const downloadFileFromBlob = (fileName: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();

    link.remove()
    URL.revokeObjectURL(url);
}

const downloadFile = async (fileName: string, url: string, onError?: (error: any) => void) => {
    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem("jwt") || ""}`,
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            downloadFileFromBlob(fileName, blob);
        } else {
            onError?.(response.statusText)
        }
    } catch (error) {
        onError?.(error)
    }
}

export default downloadFile