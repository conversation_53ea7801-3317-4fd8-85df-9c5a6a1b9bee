import httpClient from "@/utils/HttpClient";

const downloadFileFromBlob = (fileName: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();

    link.remove();
    URL.revokeObjectURL(url);
};

const downloadFile = async (
    fileName: string,
    url: string,
) => {
    const response = await httpClient.request(url, {
        raw: true,
    });
    if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`)
    }

    const blob = await response.blob();
    downloadFileFromBlob(fileName, blob);
};

export default downloadFile;
