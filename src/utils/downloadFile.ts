import { apiFetch } from "@/utils/apiFetch";

const downloadFileFromBlob = (fileName: string, blob: Blob) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();

    link.remove();
    URL.revokeObjectURL(url);
};

const downloadFile = async (
    fileName: string,
    url: string,
    onError?: (error: any) => void
) => {
    try {
        const response = await apiFetch(url, {
            method: 'GET',
            raw: true,
            headers: {
                'Content-Type': 'application/json',
            },
        });
        if (response.ok) {
            const blob = await response.blob();
            downloadFileFromBlob(fileName, blob);
        } else {
            onError?.(response.statusText);
        }
    } catch (error) {
        onError?.(error);
    }
};

export default downloadFile;
