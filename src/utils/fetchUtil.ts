export async function fetchDataFromAPI(
    url: string,
    dataType: string,
    token: string,
    method: string = 'GET',
    body?: any
) {
    try {
        const response = body
            ? await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: body,
            })
            : await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

        if (!response.ok) {
            const errorDetails = await response.json();
            console.error(`${dataType} Fetch Error:`, errorDetails);
            throw new Error(
                `Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`
            );
        }

        const data = await response.json();
        return data.data?.[0] || null;
    } catch (error) {
        console.error(`Error fetching ${dataType} data:`, error);
        throw error;
    }
}

export const normalizeQuery = (q: Record<string, any>) =>
    Object.fromEntries(
        Object.entries(q)
            .filter(([, v]) => v !== undefined && v !== null && v !== '')
            .map(([k, v]) => [k, Array.isArray(v) ? v.join(',') : String(v)])
    );

export const queryString = (q: Record<string, any>) =>
    new URLSearchParams(normalizeQuery(q)).toString();
