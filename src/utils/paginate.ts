const defaultPageIndex = 1
const defaultPageSize = 10

export function paginate<T>(array: T[], pageRaw?: number, pageSizeRaw?: number) {
    if (pageRaw === undefined && pageSizeRaw === undefined) {
        return array
    }

    const page = pageRaw ?? defaultPageIndex
    const pageSize = pageSizeRaw ?? defaultPageSize

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return array.slice(startIndex, endIndex);
}
