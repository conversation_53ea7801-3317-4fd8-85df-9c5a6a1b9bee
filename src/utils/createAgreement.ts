import { PDFDocument } from 'pdf-lib';
import fs from 'fs/promises'
import path from "node:path";

type Params = {
    referrer: any
    customer: any
}
export const createAgreement = async (params: Params) => {
    const filePath = path.join(process.cwd(), "public", "Tippgebervereinbarung_20250602.pdf");
    const buffer = await fs.readFile(filePath)
    const pdf = await PDFDocument.load(buffer)

    const pages = pdf.getPages()

    drawReferrerDetails(pages[0], params)
    drawClientDetails(pages[0], params)
    drawBankAccount(pages[1], params)

    return pdf.save()
}

const drawReferrerDetails = (page: any, { referrer }: Params) => {
    const { firstName, lastName, street, houseNumber, postalCode, city} = referrer ?? {}
    const { height } = page.getSize()

    page.drawText(`Frau ${firstName} ${lastName}\n${street} ${houseNumber}\n${postalCode} ${city}`, {
        x: 90,
        y: height - 155,
        lineHeight: 15,
        size: 10,
    })
}
const drawClientDetails = (page: any, { customer }: Params) => {
    const { firstName, lastName } = customer ?? {}
    const { height } = page.getSize()

    page.drawText(`${firstName} ${lastName}`, {
        x: 242,
        y: height - 323,
        lineHeight: 15,
        size: 10,
    })
}
const drawBankAccount = (page: any, { referrer }: Params) => {
    const { firstName, lastName, iban } = referrer ?? {}
    const { height } = page.getSize()

    page.drawText(`${lastName}\n${firstName}\n${iban}`, {
        x: 145,
        y: height / 2 - 72,
        lineHeight: 13,
        size: 9,
    })
}