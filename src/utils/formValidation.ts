import { type createTranslator } from 'next-intl';

import { ParseFormFieldsFromJson } from '@/components/forms/domain';

import {
  type CaseWithAllTheRelations,
  type DataInputConfiguration,
  type DataValidationResult,
  ExtractRootIdNamesFromFormConfigurationToLabelIdArray,
  type FormValidationResult,
  type GetCaseGroupWithAllRelationsResponse,
  ValidateFormDataBasedOnFormConfiguration,
} from './helperTypes';

export function ValidateFormDataForCaseGroup(
  caseGroupData: GetCaseGroupWithAllRelationsResponse,
  dataInputConfiguration: DataInputConfiguration,
  t: ReturnType<typeof createTranslator>
): DataValidationResult | null {
  const v: DataValidationResult = {};
  caseGroupData.cases.forEach((ccase) => {
    const customerRecord: Record<string, FormValidationResult> = {};
    ccase.questionnaires.forEach((questionnaire) => {
      if (
        //   questionnaire.formId === 'citizenshipAndProfession' &&
        typeof questionnaire.formId === 'string'
      ) {
        const form = ParseFormFieldsFromJson(
          t.raw(`form_configurations.${questionnaire.formId}`)
        );
        if (form.length > 0) {
          const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
            questionnaire.answersJson,
            form,
            undefined,
            t,
            ccase
          );
          customerRecord[questionnaire.formId] = {
            bad: bad,
            required: required,
            total: required.length,
            done: required.length - bad.length,
            isComplete: bad.length === 0,
            formId: questionnaire.formId,
          };
        }
      }
    });
    dataInputConfiguration.steps.forEach((step) => {
      if (typeof customerRecord[step.step] !== 'object') {
        let stepIsCompleteBySubsteps: boolean | null = null;
        if (
          step.substep === 'formArray' &&
          typeof step.substeps === 'object' &&
          Array.isArray(step.substeps) &&
          step.substeps.length > 0
        ) {
          step.substeps.forEach((substep) => {
            if (
              stepIsCompleteBySubsteps !== false &&
              typeof substep.id === 'string' &&
              typeof customerRecord[substep.id] === 'object'
            ) {
              if (customerRecord[substep.id].isComplete === true) {
                stepIsCompleteBySubsteps = true;
              } else {
                stepIsCompleteBySubsteps = false;
              }
            }
          });
        } else if (step.substep === 'formSegmentation') {
          //
        }
        customerRecord[step.step] = {
          isComplete: stepIsCompleteBySubsteps === true,
          bad: [],
          required: [],
          total: 1,
          done: 1,
          formId: '',
        };
      }
    });
    v[ccase.customerId] = customerRecord;
  });

  return v;
}

export function LoadDataInputConfiguration(
  t: ReturnType<typeof createTranslator>
): DataInputConfiguration {
  const rawConfiguration = t.raw('data_input_configuration');
  const configuration = rawConfiguration as DataInputConfiguration;
  if (typeof configuration === 'object') {
    if (configuration?.steps?.length <= 0) {
      throw new Error('Bad configuration! ' + JSON.stringify(configuration));
    }
  }
  const runtimeConfiguration: DataInputConfiguration = {
    steps: configuration.steps.map((step, i) => {
      if (
        step.substep === 'formSegmentation' &&
        typeof step.substepform === 'string'
      ) {
        const form = ParseFormFieldsFromJson(
          t.raw(`form_configurations.${step.substepform}`)
        );
        const subSteps =
          ExtractRootIdNamesFromFormConfigurationToLabelIdArray(form);
        if (Array.isArray(subSteps) && subSteps.length > 0) {
          return {
            step: step.step,
            substep: step.substep,
            substepform: step.substepform,
            substeps: subSteps,
          };
        } else {
          return {
            step: step.step,
            substep: step.substep,
            substepform: step.substepform,
          };
        }
      } else if (
        step.substep === 'formArray' &&
        (step.substeps?.length ?? 0) > 0
      ) {
        return {
          step: step.step,
          substep: step.substep,
          substeps: step.substeps?.map((substep) => {
            if (typeof substep.label === 'string') {
              return substep;
            } else {
              return {
                id: substep.id,
                label: `form_names.${substep.id}`,
              };
            }
          }),
        };
      }
      return step;
    }),
  };
  return runtimeConfiguration;
}

export function ValidateFormDataForCase(
  cases: CaseWithAllTheRelations[],
  dataInputConfiguration: DataInputConfiguration,
  t: ReturnType<typeof createTranslator>
): DataValidationResult | null {
  const v: DataValidationResult = {};
  const completedQuestionnaireFormIds: string[] = [];
  cases.forEach((ccase) => {
    const customerRecord: Record<string, FormValidationResult> = {};
    ccase.questionnaires.forEach((questionnaire) => {
      if (typeof questionnaire.formId === 'string') {
        const form = ParseFormFieldsFromJson(
          t.raw(`form_configurations.${questionnaire.formId}`)
        );
        if (form.length > 0) {
          const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
            questionnaire.answersJson,
            form,
            undefined,
            t,
            ccase
          );
          customerRecord[questionnaire.formId] = {
            bad: bad,
            required: required,
            total: required.length,
            done: required.length - bad.length,
            isComplete: bad.length === 0,
            formId: questionnaire.formId,
          };
          if (bad.length === 0) {
            completedQuestionnaireFormIds.push(questionnaire.formId);
          }
        }
      }
    });
    const requiredForms: { step: string; formId: string }[] = [];
    dataInputConfiguration.steps.forEach((step) => {
      if (
        step.substep === 'formArray' &&
        typeof step.substeps === 'object' &&
        Array.isArray(step.substeps)
      ) {
        step.substeps.forEach((substepForm) => {
          if (typeof substepForm.id === 'string') {
            requiredForms.push({ step: step.step, formId: substepForm.id });
          }
        });
      } else if (
        step.substep === 'formSegmentation' &&
        typeof step.substepform === 'string'
      ) {
        requiredForms.push({ step: step.step, formId: step.substepform });
      } else if (typeof step.substep === 'undefined') {
        requiredForms.push({ step: step.step, formId: step.step });
      }
    });
    dataInputConfiguration.steps.forEach((step) => {
      if (typeof customerRecord[step.step] !== 'object') {
        let stepIsCompleteBySubsteps: boolean | null = null;
        if (
          step.substep === 'formArray' &&
          typeof step.substeps === 'object' &&
          Array.isArray(step.substeps) &&
          step.substeps.length > 0
        ) {
          step.substeps.forEach((substep) => {
            if (
              stepIsCompleteBySubsteps !== false &&
              typeof substep.id === 'string' &&
              typeof customerRecord[substep.id] === 'object'
            ) {
              if (customerRecord[substep.id].isComplete === true) {
                stepIsCompleteBySubsteps = true;
              } else {
                stepIsCompleteBySubsteps = false;
              }
            }
          });
        } else if (step.substep === 'formSegmentation') {
          //
        }
        customerRecord[step.step] = {
          isComplete: stepIsCompleteBySubsteps === true,
          bad: [],
          required: [],
          total: 1,
          done: 1,
          formId: '',
        };
      }
    });
    v[ccase.customerId] = customerRecord;
  });

  return v;
}

export function CaseDataInputIsComplete(
  cases: CaseWithAllTheRelations[],
  dataInputConfiguration: DataInputConfiguration,
  t: ReturnType<typeof createTranslator>
): boolean {
  const v: DataValidationResult = {};
  const completedQuestionnaireFormIds: string[] = [];
  const requiredForms: { step: string; formId: string }[] = [];
  cases.forEach((ccase) => {
    const customerRecord: Record<string, FormValidationResult> = {};
    ccase.questionnaires.forEach((questionnaire) => {
      if (typeof questionnaire.formId === 'string') {
        const form = ParseFormFieldsFromJson(
          t.raw(`form_configurations.${questionnaire.formId}`)
        );
        if (form.length > 0) {
          const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
            questionnaire.answersJson,
            form,
            undefined,
            t,
            ccase
          );
          customerRecord[questionnaire.formId] = {
            bad: bad,
            required: required,
            total: required.length,
            done: required.length - bad.length,
            isComplete: bad.length === 0,
            formId: questionnaire.formId,
          };
          if (bad.length === 0) {
            completedQuestionnaireFormIds.push(questionnaire.formId);
          }
        }
      }
    });
    dataInputConfiguration.steps.forEach((step) => {
      if (
        step.substep === 'formArray' &&
        typeof step.substeps === 'object' &&
        Array.isArray(step.substeps)
      ) {
        step.substeps.forEach((substepForm) => {
          if (typeof substepForm.id === 'string') {
            requiredForms.push({ step: step.step, formId: substepForm.id });
          }
        });
      } else if (
        step.substep === 'formSegmentation' &&
        typeof step.substepform === 'string'
      ) {
        requiredForms.push({ step: step.step, formId: step.substepform });
      } else if (typeof step.substep === 'undefined') {
        requiredForms.push({ step: step.step, formId: step.step });
      }
    });
  });

  let done = true;

  requiredForms.forEach((required) => {
    if (!completedQuestionnaireFormIds.includes(required.formId)) {
      done = false;
    }
  });

  return done;
}
