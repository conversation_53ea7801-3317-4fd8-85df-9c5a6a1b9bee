import { S3Client } from '@aws-sdk/client-s3';

const isDev = process.env.NODE_ENV?.toLowerCase().includes('prod');
export const s3bucketName = isDev
  ? process.env.IONOS_BUCKET_NAME || 'kvcare-dev-test-dev'
  : process.env.IONOS_BUCKET_NAME || 'kvcare-prod-prod';

export const s3 = new S3Client({
  region: 'eu-central-3', // MinIO ignores region but SDK requires it
  endpoint:
    process.env.IONOS_ENDPOINT_URL || 'https://s3.eu-central-3.ionoscloud.com',
  forcePathStyle: true, // 👈 required for MinIO
  credentials: {
    accessKeyId: process.env.IONOS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.IONOS_SECRET_ACCESS_KEY!,
  },
});
