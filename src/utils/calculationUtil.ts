// src/utils/calculationUtils.ts

import { type CalculationParameter } from '@/generated/prisma-postgres';

export function calculatePrice(price: number, payment_mode: string): number {
  if (payment_mode == 'Einmalzahlung') {
    return price;
  } else if (payment_mode == 'monatlich') {
    return price / 12;
  } else if (payment_mode == 'vierteljährlich') {
    return price / 4;
  } else if (payment_mode == 'halbjährlich') {
    return price / 2;
  }
  return price;
}

// function for calculate the calculation factor for contracts that runs more or less than one year
export function calculateInsuranceRuntime(
  insurance_start_date: string,
  insurance_end_date: string
) {
  const startDate = new Date(insurance_start_date);
  const endDate = new Date(insurance_end_date);
  const totalDays = Math.max(
    1,
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  return totalDays;
}

/**
 * Calculates the number of days in the **first** invoice period for an insurance policy,
 * clamped to whichever comes earliest of:
 *   • startDate + cycle-length (1/3/6/12 months depending on payment_mode)
 *   • the policy’s main due date (only if it is strictly after startDate)
 *   • the policy’s end date
 *
 * Partial days are always rounded **up**, and the result is never less than 1.
 * (This version does *not* clamp to the current date — it always returns the full first cycle,
 * except for **einmalzahlung**, which covers the whole policy in one invoice.)
 *
 * @param insurance_start_date    ISO-8601 string of the policy start
 *                                 (e.g. `"2025-07-01T00:00:00Z"`)
 * @param insurance_end_date      ISO-8601 string of the policy end
 *                                 (e.g. `"2026-06-30T23:59:59Z"`)
 * @param insurance_main_due_date ISO-8601 string of the first installment due date
 *                                 (e.g. `"2025-12-01T00:00:00Z"`)
 * @param payment_mode            `"monatlich"` | `"vierteljährlich"` |
 *                                 `"halbjährlich"` | `"jährlich"` |
 *                                 **`"einmalzahlung"`**
 *
 * @returns Number of days to invoice in the first period (always ≥ 1).
 *
 * @example
 * // One-time payment policy from 2025-06-18 to 2026-06-18:
 * calculateInvoiceDays(
 *   "2025-06-18T00:00:00Z",
 *   "2026-06-18T23:59:59Z",
 *   "2026-01-01T00:00:00Z",
 *   "einmalzahlung"
 * );
 * // → covers full policy → Math.ceil((2026-06-18 – 2025-06-18)/1 day) = 366 days
 */
export function calculateInvoiceDays(
  insurance_start_date: string,
  insurance_end_date: string,
  insurance_main_due_date: string,
  payment_mode: string // "monatlich" | "vierteljährlich" | "halbjährlich" | "jährlich" | "einmalzahlung"
): number {
  const MS_PER_DAY = 1000 * 60 * 60 * 24;
  const startDate = new Date(insurance_start_date);
  const endDate = new Date(insurance_end_date);
  const mainDue = new Date(insurance_main_due_date);

  // *** One-time payment covers the whole policy in one invoice ***
  if (payment_mode === 'einmalzahlung') {
    // clamp to at least one day even if end ≤ start
    if (endDate <= startDate) {
      return 1;
    }
    const diffDays = (endDate.getTime() - startDate.getTime()) / MS_PER_DAY;
    return Math.max(1, Math.ceil(diffDays));
  }

  // decide cycle length in months for other modes
  let monthsToAdd: number;
  switch (payment_mode) {
    case 'monatlich':
      monthsToAdd = 1;
      break;
    case 'vierteljährlich':
      monthsToAdd = 3;
      break;
    case 'halbjährlich':
      monthsToAdd = 6;
      break;
    case 'jährlich':
      monthsToAdd = 12;
      break;
    default:
      throw new Error(`Unsupported mode: ${payment_mode}`);
  }

  // raw cycle-end = start + N months
  const cycleEnd = new Date(startDate);
  cycleEnd.setMonth(cycleEnd.getMonth() + monthsToAdd);

  // only these three bounds for *first* period:
  const candidates: Date[] = [cycleEnd, endDate];
  if (mainDue > startDate) {
    candidates.push(mainDue);
  }

  // pick the earliest date
  const periodEnd = candidates.reduce(
    (min, d) => (d < min ? d : min),
    candidates[0]
  );

  // ensure ≥1 day
  if (periodEnd <= startDate) {
    return 1;
  }

  const rawDays = (periodEnd.getTime() - startDate.getTime()) / MS_PER_DAY;
  return Math.max(1, Math.ceil(rawDays));
}

export function calculateInsuranceDaysCurrentYear(
  insurance_start_date: string,
  insurance_end_date: string
): number {
  const MS_PER_DAY = 1000 * 60 * 60 * 24;

  const startDate = new Date(insurance_start_date);
  const endDate = new Date(insurance_end_date);
  const year = startDate.getFullYear();

  // Boundaries for the current calendar year
  const yearStart = new Date(year, 0, 1, 0, 0, 0, 0);
  const nextYearStart = new Date(year + 1, 0, 1, 0, 0, 0, 0);

  // Overlap window
  const periodStart = startDate > yearStart ? startDate : yearStart;
  const periodEnd = endDate < nextYearStart ? endDate : nextYearStart;

  // No overlap → 0 days
  if (periodEnd <= periodStart) {
    return 0;
  }

  // Compute days (partial days rounded up; minimum 1)
  const rawDays = (periodEnd.getTime() - periodStart.getTime()) / MS_PER_DAY;
  return Math.max(1, Math.ceil(rawDays));
}

export function calculateInvoiceDaysThisYear(
  insurance_start_date: string,
  insurance_end_date: string,
  insurance_main_due_date: string,
  payment_mode: string // "monatlich" | "vierteljährlich" | "halbjährlich" | "jährlich"
): number {
  const MS_PER_DAY = 1000 * 60 * 60 * 24;
  const startDate = new Date(insurance_start_date);
  const endDate = new Date(insurance_end_date);
  const mainDue = new Date(insurance_main_due_date);
  const now = new Date();
  const currentYear = now.getFullYear();

  // decide cycle length in months
  let monthsToAdd: number;
  switch (payment_mode) {
    case 'monatlich':
      monthsToAdd = 1;
      break;
    case 'vierteljährlich':
      monthsToAdd = 3;
      break;
    case 'halbjährlich':
      monthsToAdd = 6;
      break;
    case 'jährlich':
      monthsToAdd = 12;
      break;
    default:
      throw new Error(`Unsupported mode: ${payment_mode}`);
  }

  let totalDays = 0;
  let periodStart = startDate;
  let isFirst = true;
  const firstDue = mainDue;

  while (periodStart < endDate) {
    // calculate the raw cycle‐end
    const cycleEnd = new Date(periodStart);
    cycleEnd.setMonth(cycleEnd.getMonth() + monthsToAdd);

    // gather the possible boundaries for this invoice period
    const candidates: Date[] = [cycleEnd, endDate];
    // only clamp to the main due date on the *first* invoice, if it lies after the start
    if (isFirst && firstDue > periodStart) {
      candidates.push(firstDue);
    }

    // pick the earliest boundary
    let periodEnd = candidates[0];
    for (const d of candidates) {
      if (d < periodEnd) periodEnd = d;
    }

    // compute days, partial days rounded *up* and at least 1
    const rawMs = periodEnd.getTime() - periodStart.getTime();
    const rawDays = rawMs / MS_PER_DAY;
    const thisPeriodDays = Math.max(1, Math.ceil(rawDays));

    // determine the due date for this invoice (we invoice at periodEnd)
    const dueDate = periodEnd;

    // if its due date is in this calendar year, count its days
    if (dueDate.getFullYear() === currentYear) {
      totalDays += thisPeriodDays;
    }

    // stop if we've reached the policy end
    if (periodEnd >= endDate) break;

    // otherwise advance to next cycle
    periodStart = periodEnd;
    isFirst = false;
  }

  return totalDays;
}

export function getTax(
  calculationParameters: CalculationParameter,
  contractType: string
) {
  const labelMap: { [key: string]: number } = {
    hausrat: calculationParameters.hausratTax!,
    wohngebaeude: calculationParameters.wohngebaudeTax!,
    tierhalterhaftpflicht: calculationParameters.tierhalterhaftpflichtTax!,
    privathaftpflicht: calculationParameters.privathaftpflichtTax!,
    haus_und_grundbesitzerhaftpflicht:
      calculationParameters.hausUndGrundbesitzerhaftpflichtTax!,
    bauleistung: calculationParameters.bauleistungTax!,
    bauherrenhaftpflicht: calculationParameters.bauherrenhaftpflichtTax!,
    geschaeftsversicherung: calculationParameters.geschaeftsversicherungTax!,
    gebaeudeversicherung: calculationParameters.gebaeudeversicherungTax!,
    betriebshaftpflicht: calculationParameters.betriebshaftpflichtTax!,
    unfallversicherung: calculationParameters.unfallTax!,
  };
  return labelMap[contractType];
}
