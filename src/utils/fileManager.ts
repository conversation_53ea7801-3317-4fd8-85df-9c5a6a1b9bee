import { S3 } from 'aws-sdk';
import { Readable } from 'stream';

// Initialize the S3 client with IONOS credentials
const s3 = new S3({
  accessKeyId: process.env.IONOS_ACCESS_KEY_ID, // Stored in .env
  secretAccessKey: process.env.IONOS_SECRET_ACCESS_KEY, // Stored in .env
  endpoint: process.env.IONOS_ENDPOINT_URL, // IONOS S3 endpoint
  region: 'eu-central-3',
  s3ForcePathStyle: true, // Required for IONOS S3
});

const BUCKET_NAME = process.env.IONOS_BUCKET_NAME!;

// PDFManager class for upload and download operations
class FileManager {
  /**
   * Upload a file to the IONOS S3 bucket using its binary content.
   * @param pdfBytes - file content as Uint8Array.
   * @param objectKey - The key (name) for the file in the bucket.
   * @returns - The public URL of the uploaded file (if accessible).
   */
  async uploadFile(bytes: Uint8Array, objectKey: string): Promise<string> {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: objectKey,
        Body: Buffer.from(bytes), // Convert Uint8Array to Buffer
        ContentType: 'application/pdf',
      };
      const uploadResult = await s3.upload(params).promise();
      console.log(`File uploaded successfully: ${uploadResult.Location}`);
      return uploadResult.Location;
    } catch (error) {
      console.error('Error uploading File:', error);
      throw new Error('Failed to upload File');
    }
  }

  /**
   * Generate a signed stream to download a file from the IONOS S3 bucket.
   * @param objectKey - The key (name) of the file in the bucket.
   * @returns - A signed stream for downloading the file.
   */
  async downloadFile(objectKey: string): Promise<Readable> {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: objectKey,
      };

      const s3Stream = s3.getObject(params).createReadStream();
      return s3Stream;
    } catch (error) {
      console.error('Error generating signed stream:', error);
      throw new Error('Failed to generate download stream');
    }
  }
}

export const fileManager = new FileManager();
