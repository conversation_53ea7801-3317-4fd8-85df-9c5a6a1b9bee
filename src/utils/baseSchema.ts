import { z } from 'zod';

export const getOneInputSchema = z.object({
  caseId: z.number().int().positive(),
});

export const updateCaseDataInputProgressSchema = z.object({
  caseId: z.number().int().positive(),
  data: z.record(z.any(), z.any()).optional().nullable(),
});

export const getOneByIdOrNumber = z
  .object({
    id: z.number().int().positive().optional(),
    number: z.string().optional(),
  })
  .refine((data) => (data.id ? !data.number : !!data.number), {
    message: 'Provide id or number',
  });

export const getCaseGroupByGroupIdOrNumberOrCaseIdOrNumber = z
  .object({
    caseId: z.number().int().positive().optional(),
    caseNumber: z.string().optional(),
    caseGroupId: z.number().int().positive().optional(),
    caseGroupNumber: z.string().optional(),
  })
  .refine(
    (data) =>
      typeof data.caseId === 'number' ||
      typeof data.caseNumber === 'string' ||
      typeof data.caseGroupId === 'number' ||
      typeof data.caseGroupNumber === 'string',
    {
      message: 'Provide id or number',
    }
  );

export type GetOneInput = z.infer<typeof getOneInputSchema>;
