import { type Prisma, type Questionnaire } from '@prisma/client';
import { type createTranslator, type useTranslations } from 'next-intl';

import {
  type DatasetCondition,
  type KvcFormFieldProps,
  KvcInputFieldType,
} from '@/components/forms/domain';
import { type KvcContentProps } from '@/components/forms/FormField';
import { type GetManyWithGroup_Case } from '@/modules/case/types/case-types';

import deMessages from '../../messages/de.json';

export interface PreviousInsuranceData {
  krankenversicherung: any[];
  pflegeversicherung: any[];
  zusatzversicherung: any[];
}

export enum DataInputSteps {
  PersonalData = 'personalData',
  CitizenshipAndProfession = 'citizenshipAndProfession',
  PreviousInsurance = 'previousInsurance',
  HealthQuestions = 'healthQuestions',
  Questionnaires = 'questionnaires',
  InsurancePlanSelection = 'insurancePlanSelection',
  //   Test = 'test',
}

export interface DataInputConfiguration {
  steps: KvcInputStep[];
}

export enum DataInputSubSteps {
  PersonalData = 'personalData',
  CitizenshipAndProfession = 'citizenshipAndProfession',
  PreviousInsurance = 'previousInsurance',
  HealthQuestions = 'healthQuestions',
  Questionnaires = 'questionnaires',
  InsurancePlanSelection = 'insurancePlanSelection',
  //   Test = 'test',
}

export interface DataInputSubstep {
  id: string;
  label?: string;
}

export interface DataInputCurrent {
  step: KvcInputStep;
  substep?: DataInputSubstep;
}

export enum KvcInputSubStepType {
  formArray = 'formArray',
  formSegmentation = 'formSegmentation',
  additionalQuestionnaires = 'additionalQuestionnaires',
  none = 'none',
}
export interface KvcInputStep {
  step: string;
  substep?: KvcInputSubStepType;
  substeps?: DataInputSubstep[];
  substepform?: string;
}

export interface DataConstraint {
  fieldName: string;
  type: DataConstraintType;
}

export enum DataConstraintType {
  UniqueAmongCaseCustomers = 'UniqueAmongCaseCustomers',
}

export interface CustomerAndStepUrlParams {
  customerId: string | null;
  stepId: string | null;
}

/**
 * Constructs a string with 'firstName lastName' if those properties exist on
 * a raw object, or null if neither property exists or is empty. Used to show
 * customer name during basic data input.
 * @param obj any object
 * @returns customer name if exists or null
 */
export function GetPersonNameFromRawObject(obj: any): string | null {
  if (typeof obj === 'object') {
    const result =
      `${typeof obj.firstName === 'string' ? obj.firstName : ''} ${typeof obj.lastName === 'string' ? obj.lastName : ''}`.trim();
    if (result.length > 0) {
      return result;
    }
  }

  return null;
}

// export function ValidateFormDataBasedOnFormConfigurationSummary(
//   data: any,
//   config: FormCard
// ): string {
//   const [missingFields, totalRequiredFields] =
//     ValidateFormDataBasedOnFormConfiguration(data, config);

//   return `${totalRequiredFields.length - missingFields.length}/${totalRequiredFields.length}`;
// }

export function VerifyFieldVisibilityChain(
  data: any,
  requiredFieldIds: Set<string>,
  formFields: KvcContentProps[],
  currentField: KvcContentProps
): boolean {
  let result = false;
  if (requiredFieldIds.has(currentField.id)) {
    result = true;
  }
  // if (typeof currentField.showIf === 'undefined') {
  // }
  if (typeof currentField.showIf !== 'undefined') {
    if (typeof currentField.showIf === 'string') {
      const showifLinkedField = formFields.find(
        (f) => f.id === currentField.showIf
      );
      if (showifLinkedField !== undefined) {
        if (typeof showifLinkedField.showIf !== 'undefined') {
          result = VerifyFieldVisibilityChain(
            data,
            requiredFieldIds,
            formFields,
            showifLinkedField
          );
        }
      }
    } else if (typeof currentField.showIf === 'object') {
      let currentFieldIsRequiredBasedOnShowIf = false;
      for (const [showIfKey, showIfvalue] of Object.entries(
        currentField.showIf
      )) {
        const requiredFieldId = showIfKey;
        const linkedField = formFields.find((f) => f.id === requiredFieldId);
        if (typeof linkedField !== 'undefined') {
          if (typeof linkedField.showIf !== 'undefined') {
            const linkedFieldIsRequired = VerifyFieldVisibilityChain(
              data,
              requiredFieldIds,
              formFields,
              linkedField
            );
            if (linkedFieldIsRequired === false) {
              currentFieldIsRequiredBasedOnShowIf = false;
              break;
            } else {
              if (data[showIfKey] !== showIfvalue) {
                currentFieldIsRequiredBasedOnShowIf = false;
                break;
              } else {
                currentFieldIsRequiredBasedOnShowIf = true;
              }
            }
          } else {
            if (data[showIfKey] !== showIfvalue) {
              currentFieldIsRequiredBasedOnShowIf = false;
              break;
            } else {
              currentFieldIsRequiredBasedOnShowIf = true;
            }
          }
        }
      }
      result = currentFieldIsRequiredBasedOnShowIf;
    } else if (typeof currentField.showIfNot === 'string') {
      const showifLinkedField = formFields.find(
        (f) => f.id === currentField.showIfNot
      );
      if (showifLinkedField !== undefined) {
        if (typeof showifLinkedField.showIfNot !== 'undefined') {
          result = VerifyFieldVisibilityChain(
            data,
            requiredFieldIds,
            formFields,
            showifLinkedField
          );
        }
      }
    } else if (typeof currentField.showIfNot === 'object') {
      let currentFieldIsRequiredBasedOnShowIf = false;
      for (const [showIfKey, showIfvalue] of Object.entries(
        currentField.showIfNot
      )) {
        const requiredFieldId = showIfKey;
        const linkedField = formFields.find((f) => f.id === requiredFieldId);
        if (typeof linkedField !== 'undefined') {
          if (
            typeof linkedField.showIfNot !== 'undefined' ||
            typeof linkedField.showIf !== 'undefined'
          ) {
            const linkedFieldIsRequired = VerifyFieldVisibilityChain(
              data,
              requiredFieldIds,
              formFields,
              linkedField
            );
            if (linkedFieldIsRequired === false) {
              currentFieldIsRequiredBasedOnShowIf = false;
              break;
            } else {
              if (data[showIfKey] !== showIfvalue) {
                currentFieldIsRequiredBasedOnShowIf = false;
                break;
              } else {
                currentFieldIsRequiredBasedOnShowIf = true;
              }
            }
          } else {
            if (data[showIfKey] !== showIfvalue) {
              currentFieldIsRequiredBasedOnShowIf = false;
              break;
            } else {
              currentFieldIsRequiredBasedOnShowIf = true;
            }
          }
        }
      }
      result = currentFieldIsRequiredBasedOnShowIf;
    }
  }

  return result;
}

export function ValidateFormDataBasedOnFormConfiguration(
  obj: any,
  items: KvcFormFieldProps[],
  parentId?: string,
  t?: ReturnType<typeof createTranslator>,
  caseData?: CaseWithAllTheRelations
): [string[], string[]] {
  //   if (typeof parentId === 'string') {
  //   console.log('nvalidation 2', 'obj:', obj);
  //   }
  if (items == null || !Array.isArray(items)) {
    throw new Error('duck');
  }
  const data = obj !== null && typeof obj === 'object' ? obj : {};

  const badFieldIds: Set<string> = new Set();
  const formFields: KvcContentProps[] = [];
  const requiredFieldIds: Set<string> = new Set();
  const requiredFields: Set<KvcContentProps> = new Set();

  // if (typeof config === 'undefined' || typeof config.items === 'und')

  items.forEach((item) => {
    const formItem = item;
    if (
      typeof (formItem as any).id === 'string' &&
      typeof (formItem as any).element !== 'undefined' &&
      (formItem as any).disabled !== true
    ) {
      const formField = formItem as KvcContentProps;
      formFields.push(formField);
    }
    //  else {
    // }
  });
  // Begin by identifying required fields which have no dependencies
  // and adding them to required list directly
  for (const field of formFields) {
    //  If required unconditionally
    if (
      field.disabled !== true &&
      field.required === true &&
      typeof field.showIf === 'undefined' &&
      typeof field.showIfNot === 'undefined'
    ) {
      requiredFieldIds.add(field.id);
      requiredFields.add(field);
      if (
        field.element !== KvcInputFieldType.Database &&
        (typeof data[field.id] === 'undefined' ||
          (typeof data[field.id] === 'string' &&
            data[field.id].trim().length === 0))
      ) {
        badFieldIds.add(field.id);
      }
    }
  }
  // Now only go through fields which are not on the required list yet.
  for (const field of formFields) {
    if (field.required === true && !requiredFieldIds.has(field.id)) {
      if (typeof field.showIf === 'string') {
        const fieldIsRequired = ObjectAttributeIsAvalidString(
          data,
          field.showIf
        );
        if (fieldIsRequired) {
          requiredFieldIds.add(field.id);
          requiredFields.add(field);
          if (!ObjectAttributeIsAvalidString(data, field.id)) {
            badFieldIds.add(field.id);
          }
        }
      } else if (typeof field.showIf === 'object') {
        const currentFieldIsRequired = VerifyFieldVisibilityChain(
          data,
          requiredFieldIds,
          formFields,
          field
        );
        if (currentFieldIsRequired) {
          requiredFieldIds.add(field.id);
          requiredFields.add(field);
        }
      } else if (typeof field.showIfNot === 'string') {
        if (
          typeof data[field.showIfNot] === 'undefined' ||
          data[field.showIfNot] === null
        ) {
          requiredFieldIds.add(field.id);
          requiredFields.add(field);
        }
      } else if (typeof field.showIfNot === 'object') {
        let required = true;
        for (const [key, value] of Object.entries(field.showIfNot)) {
          if (data[key] === value) {
            required = false;
            break;
          }
        }
        if (required) {
          requiredFieldIds.add(field.id);
          requiredFields.add(field);
        }
      }
    }
  }
  //   for (const requiredFieldId of requiredFieldIds) {
  //     // When erasing date picker value it returns a null.
  //     if (
  //       typeof data[requiredFieldId] === 'undefined' ||
  //       data[requiredFieldId] === null
  //     ) {
  //       badFieldIds.add(requiredFieldId);
  //     } else if (
  //       typeof data[requiredFieldId] === 'string' &&
  //       data[requiredFieldId].trim().length === 0
  //     ) {
  //       badFieldIds.add(requiredFieldId);
  //     }
  //   }
  for (const requiredField of requiredFields) {
    const currentRequiredFieldId = requiredField.id;
    // When erasing date picker value it returns a null.
    if (
      requiredField.element === KvcInputFieldType.Database &&
      caseData != null
    ) {
      if (
        requiredField.datasetConditions != null &&
        Array.isArray(requiredField.datasetConditions) &&
        requiredField.datasetConditions.length > 0
      ) {
        const dataset = GetDatasetFromCaseData(
          caseData,
          requiredField.datasetConditions
        );

        console.debug('before dataset', dataset, badFieldIds);
        if (dataset !== null && typeof dataset === 'object') {
          if (Array.isArray(dataset) && dataset.length <= 0) {
            console.debug('bad dataset', dataset, badFieldIds);
            badFieldIds.add(currentRequiredFieldId);
          } else if (Object.entries(dataset).length <= 0) {
            console.debug('bad dataset', dataset, badFieldIds);
            badFieldIds.add(currentRequiredFieldId);
          }
        }
      }
    } else if (typeof requiredField.nestedForm === 'string') {
      if (
        typeof data[currentRequiredFieldId] === 'undefined' ||
        data[currentRequiredFieldId] === null
      ) {
        badFieldIds.add(currentRequiredFieldId);
      } else {
        const nestedFormId = requiredField.nestedForm;
        const formArray = (deMessages as any)['form_configurations'][
          nestedFormId
        ] as KvcContentProps[];
        if (!Array.isArray(formArray) || formArray.length <= 0) {
          console.warn(
            'Invalid or empty nested form configuration',
            currentRequiredFieldId,
            items
          );
        } else {
          // console.log('ffform', formArray);
          const [nestedBadFields, nestedRequiredFields] =
            ValidateFormDataBasedOnFormConfiguration(
              data[currentRequiredFieldId],
              formArray,
              nestedFormId,
              t,
              caseData
            );
          nestedBadFields.forEach((b) => badFieldIds.add(b));
          nestedRequiredFields.forEach((b) => requiredFieldIds.add(b));

          if (nestedBadFields.length > 0) {
            badFieldIds.add(currentRequiredFieldId);
          }
        }
      }
    } else if (typeof requiredField.arrayOfForm === 'string') {
      if (
        data[currentRequiredFieldId] == null ||
        !Array.isArray(data[currentRequiredFieldId]) ||
        !(data[currentRequiredFieldId]?.length > 0)
      ) {
        badFieldIds.add(currentRequiredFieldId);
      } else {
        const nestedFormId = requiredField.arrayOfForm;
        const formArray = (deMessages as any)['form_configurations'][
          nestedFormId
        ] as KvcContentProps[];
        const dataArray = data[currentRequiredFieldId];
        dataArray.forEach((dae) => {
          const [nestedBadFields, nestedRequiredFields] =
            ValidateFormDataBasedOnFormConfiguration(
              dae,
              formArray,
              nestedFormId,
              t,
              caseData
            );
          nestedBadFields.forEach((b) => badFieldIds.add(b));
          nestedRequiredFields.forEach((b) => requiredFieldIds.add(b));
        });
      }
    } else {
      if (
        typeof data[currentRequiredFieldId] === 'undefined' ||
        data[currentRequiredFieldId] === null
      ) {
        badFieldIds.add(currentRequiredFieldId);
      } else if (
        typeof data[currentRequiredFieldId] === 'string' &&
        data[currentRequiredFieldId].trim().length === 0
      ) {
        badFieldIds.add(currentRequiredFieldId);
      }
    }
  }

  //   Vaidation path can be:
  //   caseData?.potentialInsurances
  //   console.debug('result', badFieldIds);

  return [Array.from(badFieldIds), Array.from(requiredFieldIds)];
}

function ObjectAttributeIsAvalidString(obj: any, name: string): boolean {
  if (typeof obj === 'object') {
    const value = obj[name];
    if (typeof value === 'string' && value.trim().length > 0) {
      return true;
    }
  }
  return false;
}

export function ExtractRootIdNamesFromFormConfigurationToStringArray(
  config: any
): string[] | null {
  console.log('ExtractRootIdNamesFromFormConfigurationToStringArray', config);
  const result: string[] = [];
  if (typeof config === 'object' && Array.isArray(config)) {
    config.forEach((field: any) => {
      if (typeof field === 'object' && typeof field.label === 'string') {
        result.push(field.label);
      }
    });
    if (result.length > 0) {
      return result;
    }
  }
  return null;
}

export function ExtractRootIdNamesFromFormConfigurationToLabelIdArray(
  config: any
): DataInputSubstep[] | null {
  const result: DataInputSubstep[] = [];
  if (typeof config === 'object' && Array.isArray(config)) {
    config.forEach((field: any) => {
      if (typeof field === 'object' && typeof field.id === 'string') {
        result.push({ label: field.label, id: field.id });
      }
    });
    if (result.length > 0) {
      return result;
    }
  }
  return null;
}

export type NextIntlTranslationHookType = ReturnType<typeof useTranslations>;

export function FindQuestionnaireForFormId(
  formId: string,
  questionnaires: Questionnaire[]
): Questionnaire | null {
  return questionnaires.find((it) => it.formId === formId) || null;
}

export function GetNextCaseCustomer(
  caseGroup: GetCaseGroupWithAllRelationsResponse,
  id: any
): CustomerStandalone | undefined {
  // TODO: fix
  const index = caseGroup.customers.findIndex((item) => item.customerId === id);
  if (index !== -1 && index < caseGroup.customers.length - 1) {
    return caseGroup.customers[index + 1];
  }
  return undefined;
}

export function PatchQuestionnaireForValidation(
  caseData: CaseWithAllTheRelations,
  caseCustomerId: number,
  questionnaireId: number,
  formId: string,
  newData: any
): CaseWithAllTheRelations {
  if (
    typeof caseData === 'object' &&
    typeof caseCustomerId === 'number' &&
    typeof newData === 'object'
  ) {
    return {
      ...caseData,
      questionnaires: caseData.questionnaires.map((q) =>
        q.questionnaireId === questionnaireId && q.formId === formId
          ? {
              ...q,
              answersJson: newData,
            }
          : q
      ),
    };
  }
  return caseData;
}

export function PatchCustomerForValidation(
  caseData: CaseWithAllTheRelations,
  caseCustomerId: number,
  newData: any
): CaseWithAllTheRelations {
  if (
    typeof caseData === 'object' &&
    typeof caseCustomerId === 'number' &&
    typeof newData === 'object'
  ) {
    return {
      ...caseData,
      customer: newData,
    };
  }
  return caseData;
}

export interface FormValidationResult {
  bad: string[];
  required: string[];
  total: number;
  done: number;
  isComplete: boolean;
  formId: string;
}

export type DataValidationResult = Record<
  string,
  Record<string, FormValidationResult>
>;

export function GetFormConfigurationById(
  formId: string
): KvcContentProps[] | null {
  if (typeof formId === 'string') {
    const formArray = (deMessages as any)['form_configurations'][
      formId
    ] as KvcContentProps[];
    if (
      typeof formArray === 'object' &&
      formArray !== null &&
      Array.isArray(formArray) &&
      formArray.length > 0
    ) {
      return formArray;
    }
  }
  return null;
}

export function VerifyThatPotentialInsuranceExistsForCustomer() {
  //
}

// TODO: use prisma enum if not broken
export enum PotentialInsuranceCreatedBy {
  Ai = 'ai',
  Consultant = 'user',
  KvcareTeam = 'admin',
}

export function GetDatasetFromCaseData(
  caseData: CaseWithAllTheRelations,
  datasetConditions: DatasetCondition[]
): any {
  if (
    typeof caseData === 'object' &&
    typeof datasetConditions === 'object' &&
    Array.isArray(datasetConditions) &&
    datasetConditions.length > 0
  ) {
    const condition = datasetConditions[0];
    if (
      typeof condition === 'object' &&
      typeof condition.dataset === 'string'
    ) {
      if (
        condition.dataset === 'potentialInsurance' &&
        typeof caseData.potentialInsurances === 'object'
      ) {
        const dataset = caseData.potentialInsurances as any;
        if (condition.condition === 'hasObjectWithKv') {
          if (Array.isArray(dataset) && dataset.length > 0) {
            const result = (dataset as any[]).filter(
              (it) =>
                typeof it === 'object' && it[condition.key] === condition.value
            );
            return result;
          } else if (dataset[condition.key] === condition.value) {
            return dataset;
          }
        }
      }
    }
  }
  return undefined;
}

// type CaseWithRelations = Prisma.PromiseReturnType<typeof prisma.case.findFirst>;

export type CaseGroupWithRelations = Prisma.CaseGroupGetPayload<{
  include: {
    memberships: {
      include: {
        case: {
          include: {
            customer: true;
            questionnaires: true;
            riskPreRequests: true;
            attachments: true;
            caseGroupMembership: true;
            applications: true;
            messages: true;
            potentialInsurances: true;
          };
        };
      };
    };
  };
}>;

export type CaseWithAllTheRelations = Prisma.CaseGetPayload<{
  include: {
    customer: true;
    questionnaires: true;
    riskPreRequests: true;
    attachments: true;
    caseGroupMembership: true;
    applications: true;
    messages: true;
    potentialInsurances: true;
  };
}>;

export type CaseGroupWithMembershipCustomerAttachments =
  Prisma.CaseGroupGetPayload<{
    include: {
      memberships: {
        include: { case: true };
      };
      cases: {
        include: {
          customer: true;
          attachments: true;
        };
      };
    };
  }>;

export type CaseWithCustomersAndAttachments = Prisma.CaseGetPayload<{
  include: {
    customer: true;
    attachments: true;
    questionnaires: true;
  };
}>;

export type CustomerWithRelations = Prisma.CustomerGetPayload<{
  include: {
    cases: true;
  };
}>;

export type CustomerStandalone = Prisma.CustomerGetPayload<{
  include: {
    cases: false;
  };
}>;

export type GetCaseGroupWithAllRelationsResponse = {
  cases: CaseWithAllTheRelations[];
  customers: Prisma.CustomerGetPayload<{ include: { cases: false } }>[];
};

export type PotentialInsuranceFromCaseGroup =
  GetCaseGroupWithAllRelationsResponse['cases'][number]['potentialInsurances'][number];

export type CaseFromGetCaseGroupWithAllRelationsResponse =
  GetCaseGroupWithAllRelationsResponse['cases'][number];

export type CaseDataInputProgress = {
  maxStep?: string;
  maxSubstep?: string;
};

export type CaseDataInputVisitedStep = {
  step: string;
  substeps: string[];
};

export type CaseDataInputVisitedSteps = {
  visitedSteps: string[];
};

export function GetNewMaxStepOrNull(
  config: DataInputConfiguration,
  current: DataInputCurrent,
  currentProgress?: CaseDataInputProgress
): CaseDataInputProgress | null {
  if (
    typeof currentProgress !== 'object' ||
    currentProgress === null ||
    typeof currentProgress.maxStep !== 'string' ||
    typeof currentProgress.maxSubstep !== 'string'
  ) {
    return {
      maxStep: current.step.step,
      maxSubstep: current.substep?.id,
    };
  }

  const currentStepIndex = config.steps.findIndex(
    (it) => it.step === current.step.step
  );
  const currentStep = config.steps[currentStepIndex];

  const maxStepIndex = config.steps.findIndex(
    (it) => it.step === currentProgress.maxStep
  );
  const maxStep = config.steps[maxStepIndex];

  return null;
}

export enum CaseGroupRelationType {
  paying = 'paying',
  paying_and_insured_primary = 'paying_and_insured_primary',
  insured_primary = 'insured_primary',
  child = 'child',
  spouse = 'spouse',
}

const CaseGroupRelationTypeOrder: Record<string, number> = {
  [CaseGroupRelationType.paying]: 5,
  [CaseGroupRelationType.paying_and_insured_primary]: 4,
  [CaseGroupRelationType.insured_primary]: 3,
  [CaseGroupRelationType.spouse]: 2,
  [CaseGroupRelationType.child]: 1,
};

// export function DataInputConfigurationToMap(config: DataInputConfiguration) {}

export interface CaseNavbarInfo {
  firstName: string;
  lastName: string;
  caseNumber: string;
  caseType: string;
}

export const CaseGroupRelationTypeComparator = (
  a: GetManyWithGroup_Case,
  b: GetManyWithGroup_Case
): number => {
  try {
    return (
      (CaseGroupRelationTypeOrder[b.caseGroupMembership?.[0]?.relationType] ||
        0) -
      (CaseGroupRelationTypeOrder[a.caseGroupMembership?.[0]?.relationType] ||
        0)
    );
  } catch (_) {
    return 0;
  }
};

export function RemoveObjectFiedldsByList(obj: any, fields: string[]): any {
  if (typeof obj === 'object' && obj !== null && fields.length > 0) {
    fields.forEach((field) => {
      delete obj[field];
    });
    return obj;
  }
  return obj;
}
