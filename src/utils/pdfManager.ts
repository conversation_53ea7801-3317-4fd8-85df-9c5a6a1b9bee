import {
    PDFDocument,
    type PDFFont,
    type PDFPage,
    rgb,
    StandardFonts,
} from 'pdf-lib';

import {
    type Agent,
    type CalculationParameter,
    type Contract,
    type Customer,
    type Invoice,
} from '@/generated/prisma-postgres';
import { type JsonArray } from '@/generated/prisma-postgres/runtime/library';

import { calculateFutureDate } from './dateUtils';
import { fileManager } from './fileManager';
import { type InvoicePositionData } from './invoice/types';
import { formatInvoiceType, formatLabel } from './keyFormatter';
import { formatUrl } from './pdfPicker';
import createAccidentInsurancePdfData from './pdfTemplates/accidentInsurancePdfTemplate';
import createAnimalLiabilityPdfData from './pdfTemplates/animalLiabilityInsurancePdfTemplate';
import createBuildingPdfData from './pdfTemplates/buildingInsurancePdfTemplate';
import createBusinessPdfData from './pdfTemplates/businessInsurancePdfTemplate';
import createBusinessLiabilityPdfData from './pdfTemplates/businessLiabilityInsurancePdfTemplate';
import createConstructionPdfData from './pdfTemplates/constructionInsurancePdfTemplate';
import createConstructionOwnerPdfData from './pdfTemplates/constructionOwnerLiabilityPdfTemplate';
import createHomePdfData from './pdfTemplates/homeAndLandownerLiabilityPdfTemplate';
import createHouseholdPdfData from './pdfTemplates/householdInsurancePdfTemplate';
import createPersonalLiabilityPdfData from './pdfTemplates/personalLiabilityInsurancePdfTemplate';
import createResidentialBuildingPdfData from './pdfTemplates/residentialBuildingInsurancePdfTemplate';

export async function appendPages(
    pdfDoc: PDFDocument,
    pdfPath: string,
    count: number,
    pagesToCopy?: number[]
): Promise<void> {
    let loadedPdfDoc: PDFDocument;
    try {
        const pdfStream = await fileManager.downloadFile(`assets/${pdfPath}`);
        const chunks: Uint8Array[] = [];
        for await (const chunk of pdfStream) {
            chunks.push(chunk);
        }
        const pdfBytes = Buffer.concat(chunks);

        loadedPdfDoc = await PDFDocument.load(pdfBytes);
    } catch (err: any) {
        const msg = err.message ?? '';
        console.debug(msg)
        console.warn(`appendPages: ${pdfPath} not found, skipping`);
        return;
    }

    const totalPages = loadedPdfDoc.getPageCount();
    const indices =
        pagesToCopy ?? Array.from({ length: totalPages }, (_, i) => i);

    for (let i = 0; i < count; i++) {
        const pages = await pdfDoc.copyPages(loadedPdfDoc, indices);
        for (const p of pages) {
            pdfDoc.addPage(p);
        }
    }
}

export async function createContractPDF(
    contract: Contract,
    customer: Customer,
    user: Agent,
    calculationParameter: CalculationParameter
) {
    try {
        const isoString = contract.updatedAt;
        const date = new Date(isoString);
        const formattedDate = `${date.getDate().toString().padStart(2, '0')}.${(
            date.getMonth() + 1
        )
            .toString()
            .padStart(2, '0')}.${date.getFullYear()}`;

        const pdfBytesStreamCover = await fileManager.downloadFile(
            'assets/police_cover.pdf'
        );
        const chunksCover: Uint8Array[] = [];
        for await (const chunk of pdfBytesStreamCover) {
            chunksCover.push(chunk);
        }
        const coverPdfBytes = Buffer.concat(chunksCover);
        const pdfDoc = await PDFDocument.load(coverPdfBytes);

        function getBlankPagesFor(contractLike: Contract): number {
            switch (contractLike.contractType) {
                case 'hausrat':
                case 'wohngebaeude':
                case 'tierhalterhaftpflicht':
                case 'privathaftpflicht':
                case 'hausUndGrundbesitzerhaftpflicht':
                case 'bauleistung':
                case 'bauherrenhaftpflicht':
                case 'geschaeftsversicherung':
                case 'gebaeudeversicherung':
                case 'betriebshaftpflicht':
                    // all of these always get exactly 2 extra blank pages
                    return 2;

                case 'unfallversicherung': {
                    // special case: number of insured persons + 1
                    const count = (contractLike.insuredPersons as JsonArray)?.length ?? 0;
                    return count + 1;
                }

                default:
                    console.warn(`Unknown contractType: ${contractLike.contractType}`);
                    return 1;
            }
        }

        // append blank pages
        const numberBlankPages = getBlankPagesFor(contract);
        await appendPages(pdfDoc, 'police_page_blank.pdf', numberBlankPages);
        // append declaration for this contract type
        await appendPages(pdfDoc, `${contract.contractType}.pdf`, 1);
        // append general conditions
        await appendPages(pdfDoc, 'general_insurance_conditions.pdf', 1);

        // fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaFontItalic = await pdfDoc.embedFont(
            StandardFonts.HelveticaOblique
        );
        const helveticaFontBoldItalic = await pdfDoc.embedFont(
            StandardFonts.HelveticaOblique
        );
        const helveticaBoldFont = await pdfDoc.embedFont(
            StandardFonts.HelveticaBold
        );

        const titleFontSize = 13;
        const textFontSize = 11;

        const pages = pdfDoc.getPages();

        // draw number of pages
        for (const [index, page] of pages.entries()) {
            page.drawText(`[${index + 1}/${pages.length}]`, {
                x: page.getWidth() - 30,
                y: 10,
                size: 10,
                font: helveticaFont,
            });
        }

        // Draw all environment banners.
        for (const page of pages) {
            const bannerX = page.getWidth() - 136;
            const bannerY = page.getHeight() - 50;
            drawEnvironmentBanner(page, helveticaBoldFont, bannerX, bannerY);
        }

        // PAGE 1
        let page = pages[0];
        let yPosition = 720;

        function setPage(pageNumber: number) {
            page = pages[pageNumber];
            yPosition = 720;
        }

        const addText = (value: any, bold: boolean = true, italic = false) => {
            if (value) {
                page.drawText(`${value}`, {
                    x: 72,
                    y: yPosition,
                    size: textFontSize,
                    font: bold
                        ? italic
                            ? helveticaFontBoldItalic
                            : helveticaBoldFont
                        : italic
                            ? helveticaFontItalic
                            : helveticaFont,
                });
                yPosition -= 20;
            }
        };

        const addSubPoint = (key: any, value: any) => {
            if (key && value) {
                page.drawText(`   •   `, {
                    x: 72,
                    y: yPosition,
                    size: textFontSize,
                    font: helveticaFont,
                });
                page.drawText(`${key}: `, {
                    x: 72 + helveticaBoldFont.widthOfTextAtSize(`   •   `, textFontSize),
                    y: yPosition,
                    size: textFontSize,
                    font: helveticaBoldFont,
                });
                page.drawText(`${value}`, {
                    x:
                        72 +
                        helveticaBoldFont.widthOfTextAtSize(
                            `   •   ${key}: `,
                            textFontSize
                        ),
                    y: yPosition,
                    size: textFontSize,
                    font: helveticaFont,
                });
                yPosition -= 20;
            }
        };

        const addLine = () => {
            page.drawLine({
                start: { x: 72, y: yPosition },
                end: { x: 550, y: yPosition },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            yPosition -= 20;
        };

        // customer address
        page.drawText(customer.salutation!, {
            x: 72,
            y: 720,
            size: textFontSize,
            font: helveticaFont,
        });

        if (customer.namePrefix) {
            page.drawText(
                `${customer.namePrefix} ${customer.firstName} ${customer.lastName}`,
                {
                    x: 72,
                    y: 705,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
        } else {
            page.drawText(`${customer.firstName} ${customer.lastName}`, {
                x: 72,
                y: 705,
                size: textFontSize,
                font: helveticaFont,
            });
        }

        if (customer.careOf) {
            page.drawText(`c/o ${customer.careOf}`, {
                x: 72,
                y: 690,
                size: textFontSize,
                font: helveticaFont,
            });
            page.drawText(`${customer.street} ${customer.houseNumber}`, {
                x: 72,
                y: 675,
                size: textFontSize,
                font: helveticaFont,
            });
            page.drawText(`${customer.postalCode} ${customer.city}`, {
                x: 72,
                y: 660,
                size: textFontSize,
                font: helveticaFont,
            });
        } else {
            page.drawText(`${customer.street} ${customer.houseNumber}`, {
                x: 72,
                y: 690,
                size: textFontSize,
                font: helveticaFont,
            });
            page.drawText(`${customer.postalCode} ${customer.city}`, {
                x: 72,
                y: 675,
                size: textFontSize,
                font: helveticaFont,
            });
        }

        // document date
        page.drawText(`Hamburg, ${formattedDate}`, {
            x: 400,
            y: 720,
            size: textFontSize,
            font: helveticaFont,
        });

        // document title
        page.drawText(
            `${formatLabel(contract.contractType!)} – ${contract.isOffer ? 'Angebot' : 'Versicherungspolice'} Nr. ${contract.contractNumber}`,
            { x: 72, y: 580, size: titleFontSize, font: helveticaFont }
        );
        page.drawText(`${contract.isOffer ? 'Angebot' : 'Versicherungspolice'}`, {
            x: 110,
            y: 480,
            size: 30,
            font: helveticaFont,
        });

        // agent address
        page.drawText(`Ihr Makler:`, {
            x: 72,
            y: 320,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(`${user.companyName}`, {
            x: 72,
            y: 305,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(`${user.street} ${user.houseNumber}`, {
            x: 72,
            y: 290,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(`${user.postalCode} ${user.city}`, {
            x: 72,
            y: 275,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(`${user.telephoneNumber}`, {
            x: 72,
            y: 260,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(`${user.url}`, {
            x: 72,
            y: 245,
            size: textFontSize,
            font: helveticaFont,
        });

        // PAGE 2
        page = pages[1];
        yPosition = 720;

        addText(`Sparte:`);
        addText(`   •   ${formatLabel(contract.contractType!)}`, false);
        addLine();

        addText(`Versicherungszeitraum:`);
        const startDate = new Date(contract.insuranceStartDate!);
        addSubPoint(
            `Versicherungsbeginn`,
            ` ${startDate.getDate().toString().padStart(2, '0')}.${(
                startDate.getMonth() + 1
            )
                .toString()
                .padStart(2, '0')}.${startDate.getFullYear()}`
        );
        const endDate = new Date(contract.insuranceEndDate!);
        addSubPoint(
            `Versicherungsablauf`,
            `  ${endDate.getDate().toString().padStart(2, '0')}.${(
                endDate.getMonth() + 1
            )
                .toString()
                .padStart(2, '0')}.${endDate.getFullYear()}`
        );
        addLine();

        // product-specific declarations
        if (contract.contractType === 'unfallversicherung') {
            await createAccidentInsurancePdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'tierhalterhaftpflicht') {
            await createAnimalLiabilityPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'gebaeudeversicherung') {
            await createBuildingPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'geschaeftsversicherung') {
            await createBusinessPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'betriebshaftpflicht') {
            await createBusinessLiabilityPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'bauleistung') {
            await createConstructionPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'bauherrenhaftpflicht') {
            await createConstructionOwnerPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'hausUndGrundbesitzerhaftpflicht') {
            await createHomePdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'hausrat') {
            await createHouseholdPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'privathaftpflicht') {
            await createPersonalLiabilityPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }
        if (contract.contractType === 'wohngebaeude') {
            await createResidentialBuildingPdfData(
                contract,
                addText,
                addSubPoint,
                addLine,
                setPage,
                calculationParameter
            );
        }

        if (!contract.isOffer) {
            addText(`Zahlung:`);
            if (contract.iban === '' && contract.bic === '') {
                addText(
                    `Bitte überweisen Sie den fälligen Betrag auf folgendes Konto:`,
                    false
                );
                addSubPoint(`IBAN`, `DE06 2005 0550 1506 4610 43`);
                addSubPoint(`Verwendungszweck`, ` ${contract.contractNumber}`);
            } else {
                addSubPoint(`IBAN`, `${contract.iban}`);
                addSubPoint(`BIC`, `${contract.bic}`);
                addSubPoint(`Verwendungszweck`, ` ${contract.contractNumber}`);
                addText(
                    `Der Betrag wird automatisch von dem genannten Konto abgebucht.`,
                    false,
                    true
                );
            }
            addLine();
        }

        addText(
            `SHB Allgemeine Versicherung VVaG, Johannes-Albers-Allee 2, 53639 Königswinter`,
            false
        );
        addText(
            `Wir bescheinigen, dass der Umsatz gemäß §4 Nr. 10a UStG steuerfrei ist. Steuernummer: 43/702/02422`,
            false
        );

        const pdfBytes = await pdfDoc.save();
        return pdfBytes;
    } catch (error) {
        console.error('Error creating PDF:', error);
        throw new Error('PDF creation failed');
    }
}

export async function createInvoicePDF(invoiceData: Invoice, preview = false) {
    try {
        const dueDateObj = new Date(invoiceData.dueDate!);
        const currentDateObj = new Date();
        const formattedCurrentDate = `${currentDateObj.getDate().toString().padStart(2, '0')}.${(
            currentDateObj.getMonth() + 1
        )
            .toString()
            .padStart(2, '0')}.${currentDateObj.getFullYear()}`;

        // download template
        const pdfBytesStream = await fileManager.downloadFile(
            'assets/' + formatUrl('invoice')
        );
        const chunks: Uint8Array[] = [];
        for await (const chunk of pdfBytesStream) {
            chunks.push(chunk);
        }
        const existingPdfBytes = Buffer.concat(chunks);

        const pdfDoc = await PDFDocument.load(existingPdfBytes);

        // fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaFontItalic = await pdfDoc.embedFont(
            StandardFonts.HelveticaOblique
        );
        const helveticaFontBoldItalic = await pdfDoc.embedFont(
            StandardFonts.HelveticaOblique
        );
        const helveticaBoldFont = await pdfDoc.embedFont(
            StandardFonts.HelveticaBold
        );

        const textFontSize = 11;

        let pages = pdfDoc.getPages();

        // Draw all environment banners.
        for (const page of pages) {
            const bannerX = page.getWidth() - 136;
            const bannerY = page.getHeight() - 50;
            drawEnvironmentBanner(page, helveticaBoldFont, bannerX, bannerY);
            drawPreviewBanner(page, helveticaBoldFont, bannerX, bannerY, preview);
        }

        // PAGE 1
        let page = pages[0];
        let yPosition = 720;

        const addText = (value: any, bold: boolean = false, italic = false) => {
            if (value) {
                page.drawText(`${value}`, {
                    x: 72,
                    y: yPosition,
                    size: textFontSize,
                    font: bold
                        ? italic
                            ? helveticaFontBoldItalic
                            : helveticaBoldFont
                        : italic
                            ? helveticaFontItalic
                            : helveticaFont,
                });
                yPosition -= 15;
            }
        };

        const addTextWithKey = (key: any, value: any) => {
            if (key && value) {
                page.drawText(`${key}: `, {
                    x: 72,
                    y: yPosition,
                    size: textFontSize,
                    font: helveticaBoldFont,
                });
                page.drawText(`${value}`, {
                    x: 72 + helveticaBoldFont.widthOfTextAtSize(`${key}: `, textFontSize),
                    y: yPosition,
                    size: textFontSize,
                    font: helveticaFont,
                });
                yPosition -= 15;
            }
        };

        function drawRightAlignedText(
            text: string,
            font: PDFFont,
            size: number,
            rightEdgeX: number,
            y: number,
            page: PDFPage
        ) {
            const textWidth = font.widthOfTextAtSize(text, size);
            const x = rightEdgeX - textWidth;

            page.drawText(text, {
                x,
                y,
                size,
                font,
            });
        }

        function drawNumberOfPages() {
            for (const [index, page] of pages.entries()) {
                page.drawText(`[${index + 1}/${pages.length}]`, {
                    x: page.getWidth() - 30,
                    y: 10,
                    size: 10,
                    font: helveticaFont,
                });
            }
        }

        async function drawPositions(
            rows: {
                sparte: string;
                net: number;
                taxName: string;
                taxAmount: number;
            }[],
            startY: number
        ) {
            let y = startY;
            const textSize = 11;

            const FINAL_RIGHT_EDGE = 550;

            page.drawText('Sparten: Deckungsumfang siehe Deklaration', {
                x: 72,
                y,
                size: textFontSize,
                font: helveticaBoldFont,
            });
            drawRightAlignedText(
                'Erhebungsbeitrag',
                helveticaBoldFont,
                textSize,
                FINAL_RIGHT_EDGE,
                y,
                page
            );

            y -= 5;

            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;

            let netto = 0;
            let pageIndex = 0;
            for (const row of rows) {
                page.drawText(row.sparte, {
                    x: 72,
                    y,
                    size: textSize,
                    font: helveticaFont,
                });

                drawRightAlignedText(
                    `${(Math.round(row.net * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`,
                    helveticaFont,
                    textSize,
                    FINAL_RIGHT_EDGE,
                    y,
                    page
                );
                netto += row.net;
                y -= 15;
                if (y <= 110) {
                    const templatePdfDoc = await PDFDocument.load(existingPdfBytes);
                    const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0]);
                    pdfDoc.addPage(copiedPages[0]);
                    pages = pdfDoc.getPages();
                    pageIndex += 1;
                    page = pages[pageIndex];
                    y = 630;
                }
            }
            y += 10;
            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;

            page.drawText('Gesamtnettobeitrag:', {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            drawRightAlignedText(
                `${(Math.round(netto * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`,
                helveticaFont,
                textSize,
                FINAL_RIGHT_EDGE,
                y,
                page
            );

            y -= 25;
            if (invoiceData.totalNet! > 0)
                page.drawText(
                    `Vom ${dueDateObj.getDate().toString().padStart(2, '0')}.${(
                        dueDateObj.getMonth() + 1
                    )
                        .toString()
                        .padStart(
                            2,
                            '0'
                        )}.${dueDateObj.getFullYear()} bis ${calculateFutureDate(
                            invoiceData.dueDate!,
                            invoiceData.paymentMode!,
                            invoiceData.insuranceEndDate!
                        )} sind zu zahlen:`,
                    {
                        x: 72,
                        y,
                        size: textSize,
                        font: helveticaBoldFont,
                    }
                );

            y -= 15;
            page.drawText('Beitrag', {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            drawRightAlignedText(
                `${(Math.round(netto * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`,
                helveticaFont,
                textSize,
                FINAL_RIGHT_EDGE,
                y,
                page
            );
            y -= 15;

            y -= 15;
            for (const row of rows) {
                page.drawText(`${row.taxName.replace('', '')}`, {
                    x: 72,
                    y,
                    size: textSize,
                    font: helveticaFont,
                });
                drawRightAlignedText(
                    `${(Math.round(row.taxAmount * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`,
                    helveticaFont,
                    textSize,
                    FINAL_RIGHT_EDGE,
                    y,
                    page
                );
                y -= 15;
                if (y <= 110) {
                    const templatePdfDoc = await PDFDocument.load(existingPdfBytes);
                    const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0]);
                    pdfDoc.addPage(copiedPages[0]);
                    pages = pdfDoc.getPages();
                    pageIndex += 1;
                    page = pages[pageIndex];
                    y = 630;
                }
            }
            y += 10;
            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;
            page.drawText(invoiceData.totalNet! > 0 ? `Rechnungsbetrag` : 'Betrag', {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            drawRightAlignedText(
                `${(Math.round(invoiceData.totalGross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`,
                helveticaFont,
                textSize,
                FINAL_RIGHT_EDGE,
                y,
                page
            );
            return y;
        }

        // Customer Address
        if (invoiceData.namePrefix) {
            page.drawText(
                `${invoiceData.namePrefix} ${invoiceData.firstName} ${invoiceData.lastName}`,
                {
                    x: 72,
                    y: 660,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
        } else {
            page.drawText(`${invoiceData.firstName} ${invoiceData.lastName}`, {
                x: 72,
                y: 660,
                size: textFontSize,
                font: helveticaFont,
            });
        }

        if (invoiceData.billingCareOf) {
            page.drawText(`c/o ${invoiceData.billingCareOf}`, {
                x: 72,
                y: 645,
                size: textFontSize,
                font: helveticaFont,
            });
            page.drawText(
                `${invoiceData.billingStreet} ${invoiceData.billingHouseNumber}`,
                {
                    x: 72,
                    y: 630,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
            page.drawText(
                `${invoiceData.billingPostalCode} ${invoiceData.billingCity}`,
                {
                    x: 72,
                    y: 615,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
        } else {
            page.drawText(
                `${invoiceData.billingStreet} ${invoiceData.billingHouseNumber}`,
                {
                    x: 72,
                    y: 645,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
            page.drawText(
                `${invoiceData.billingPostalCode} ${invoiceData.billingCity}`,
                {
                    x: 72,
                    y: 630,
                    size: textFontSize,
                    font: helveticaFont,
                }
            );
        }

        // Agent Address
        const xAddressPosition = 385;
        page.drawText(`Vermittler:`, {
            x: xAddressPosition,
            y: 660,
            size: textFontSize,
            font: helveticaBoldFont,
        });
        page.drawText(`Nummer: ${invoiceData.agentNumber}`, {
            x: xAddressPosition,
            y: 645,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(invoiceData.agentCompanyName!, {
            x: xAddressPosition,
            y: 630,
            size: textFontSize,
            font: helveticaFont,
        });
        page.drawText(
            `${invoiceData.agentStreet} ${invoiceData.agentHouseNumber}`,
            {
                x: xAddressPosition,
                y: 615,
                size: textFontSize,
                font: helveticaFont,
            }
        );
        page.drawText(`${invoiceData.agentPostalCode} ${invoiceData.agentCity}`, {
            x: xAddressPosition,
            y: 600,
            size: textFontSize,
            font: helveticaFont,
        });

        // Date
        page.drawText(`Hamburg, ${formattedCurrentDate}`, {
            x: xAddressPosition,
            y: 570,
            size: textFontSize,
            font: helveticaFont,
        });

        // begin
        yPosition = 540;
        if (invoiceData.namePrefix) {
            addTextWithKey(
                'Versicherungsnehmer',
                `${invoiceData.namePrefix} ${invoiceData.firstName} ${invoiceData.lastName}`
            );
        } else {
            addTextWithKey(
                'Versicherungsnehmer',
                `${invoiceData.firstName} ${invoiceData.lastName}`
            );
        }
        addTextWithKey('Kundennummer', `${invoiceData.customerNumber}`);
        addTextWithKey('Versicherungsschein', `${invoiceData.contractNumber}`);

        yPosition = 470;
        addText(
            formatInvoiceType(invoiceData.type!) +
            (invoiceData.subject == null || invoiceData.subject === ''
                ? ''
                : `: ${invoiceData.subject}`),
            true
        );

        yPosition = 400;

        const positionData: {
            sparte: string;
            net: number;
            taxName: string;
            taxAmount: number;
        }[] = [];
        for (const position of invoiceData.positions as unknown as InvoicePositionData[]) {
            positionData.push({
                sparte: position.name,
                net: position.net,
                taxName: `Versicherungssteuer ${position.tax * 100}% auf ${position.net} EUR netto`,
                taxAmount: position.tax_amount!,
            });
        }
        yPosition = (await drawPositions(positionData, 430)) - 50;

        if (yPosition <= 198) {
            const oldIndex = pages.length;
            const templatePdfDoc = await PDFDocument.load(existingPdfBytes);
            const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0]);
            pdfDoc.addPage(copiedPages[0]);
            pages = pdfDoc.getPages();
            page = pages[oldIndex];
            yPosition = 630;
        }

        if (invoiceData.iban === '' && invoiceData.bic === '') {
            if (invoiceData.totalNet! > 0) {
                addText('Wir bitten Sie, den fälligen Rechnungsbetrag zu begleichen.');
                addTextWithKey('IBAN', 'DE06 2005 0550 1506 4610 43');
                addTextWithKey('BIC', 'HASPDEHHXXX');
            } else {
                addText('Wir werden Ihnen den Betrag baldmöglichst überweisen.');
            }
            addTextWithKey('Verwendungszweck', invoiceData.contractNumber);
        } else {
            if (invoiceData.totalNet! > 0)
                addText(
                    `Der Betrag wird automatisch von dem genannten Konto abgebucht.`
                );
            else
                addText(
                    'Wir werden Ihnen, den Rechnungsbetrag baldmöglichst überweisen.'
                );
            addTextWithKey('IBAN', `${invoiceData.iban}`);
            addTextWithKey('BIC', `${invoiceData.bic}`);
            addTextWithKey('Verwendungszweck', invoiceData.contractNumber);
        }

        yPosition = yPosition - 40;
        page.drawText(
            'SHB Allgemeine Versicherung VVaG, Johannes-Albers-Allee 2, 53639 Königswinter',
            {
                x: 72,
                y: yPosition,
                size: 9,
                font: helveticaFont,
            }
        );
        page.drawText(
            'Wir bescheinigen, dass der Umsatz gemäß §4 Nr. 10a UStG steuerfrei ist. Steuernummer: 43/702/02422',
            {
                x: 72,
                y: yPosition - 15,
                size: 9,
                font: helveticaFont,
            }
        );

        drawNumberOfPages();

        const pdfBytes = await pdfDoc.save();
        return pdfBytes;
    } catch (error) {
        console.error('Error creating PDF:', error);
        throw new Error('PDF creation failed');
    }
}

interface EnvironmentBannerOptions {
    envText?: string;
    width?: number;
    height?: number;
    fontSize?: number;
}

/**
 * Draws a debug banner on the specified PDF page.
 */
export function drawEnvironmentBanner(
    page: PDFPage,
    font: PDFFont,
    x: number,
    y: number,
    options: EnvironmentBannerOptions = {}
): void {
    const {
        envText = process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev'
            ? 'TEST'
            : process.env.NEXT_PUBLIC_ENVIRONMENT === 'stage'
                ? 'STAGE'
                : '',
        width = 80,
        height = 20,
        fontSize = 12,
    } = options;

    if (!envText) return;

    page.drawRectangle({
        x,
        y,
        width,
        height,
        color: rgb(1, 0, 0),
    });

    const textWidth = font.widthOfTextAtSize(envText, fontSize);
    const textX = x + (width - textWidth) / 2;
    const textY = y + (height - fontSize) / 2 + 1.5;

    page.drawText(envText, {
        x: textX,
        y: textY,
        size: fontSize,
        font,
        color: rgb(1, 1, 1),
    });
}

export function drawPreviewBanner(
    page: PDFPage,
    font: PDFFont,
    x: number,
    y: number,
    preview: boolean,
    options: EnvironmentBannerOptions = {}
): void {
    const { width = 80, height = 20, fontSize = 12 } = options;

    if (!preview) return;

    page.drawRectangle({
        x,
        y,
        width,
        height,
        color: rgb(1, 0, 0),
    });

    const textWidth = font.widthOfTextAtSize('Vorschau', fontSize);
    const textX = x + (width - textWidth) / 2;
    const textY = y + (height - fontSize) / 2 + 1.5;

    page.drawText('Vorschau', {
        x: textX,
        y: textY,
        size: fontSize,
        font,
        color: rgb(1, 1, 1),
    });
}
