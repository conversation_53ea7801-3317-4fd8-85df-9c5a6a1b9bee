import { PDFDocument, PDFFont, PDFPage, StandardFonts, drawText, rgb } from 'pdf-lib';
import { formatInvoiceType, formatLabel } from './keyFormatter';
import { formatUrl } from './pdfPicker';
import createAccidentInsurancePdfData from './pdfTemplates/accidentInsurancePdfTemplate';
import createAnimalLiabilityPdfData from './pdfTemplates/animalLiabilityInsurancePdfTemplate';
import createBuildingPdfData from './pdfTemplates/buildingInsurancePdfTemplate';
import createBusinessPdfData from './pdfTemplates/businessInsurancePdfTemplate';
import createBusinessLiabilityPdfData from './pdfTemplates/businessLiabilityInsurancePdfTemplate';
import createConstructionPdfData from './pdfTemplates/constructionInsurancePdfTemplate';
import createConstructionOwnerPdfData from './pdfTemplates/constructionOwnerLiabilityPdfTemplate';
import createHomePdfData from './pdfTemplates/homeAndLandownerLiabilityPdfTemplate';
import createHouseholdPdfData from './pdfTemplates/householdInsurancePdfTemplate';
import createPersonalLiabilityPdfData from './pdfTemplates/personalLiabilityInsurancePdfTemplate';
import createResidentialBuildingPdfData from './pdfTemplates/residentialBuildingInsurancePdfTemplate';
import { fileManager } from './fileManager';
import { calculateFutureDate } from './dateUtils';
import { AgentData, CalculationParametersData, ContractData } from '@/types';
import { InvoiceData } from "./invoice/types";

/**
 * Appends pages from an external PDF into the given `pdfDoc`.
 *
 * Downloads a PDF from your asset store, loads it, and copies its pages
 * into `pdfDoc` a specified number of times.  If the PDF can’t be found,
 * the function logs a warning and returns without modifying `pdfDoc`.
 *
 * @param pdfDoc        The target PDFDocument to which pages will be added.
 * @param pdfPath       Path (relative to your assets bucket) of the PDF to append.
 * @param count         Number of times to replicate the selected pages.
 * @param pagesToCopy   Optional array of zero-based page indices to copy.
 *                       If omitted, _all_ pages in the source PDF are copied.
 *
 * @returns A promise that resolves once pages are appended (or skipped if not found).
 *
 * @example
 * // Append the entire 3-page PDF twice:
 * await appendPages(mainDoc, "police_form.pdf", 2);
 *
 * @example
 * // Append only pages 0 and 2 of the blank template once:
 * await appendPages(mainDoc, "police_page_blank.pdf", 1, [0, 2]);
 *
 * @throws If download or PDF parsing fails for reasons _other_ than “not found”,
 *         the error is propagated up.
 */
export async function appendPages(
    pdfDoc: PDFDocument,
    pdfPath: string,
    count: number,
    pagesToCopy?: number[]
): Promise<void> {
    let loadedPdfDoc: PDFDocument;
    try {
        const pdfStream = await fileManager.downloadFile(`assets/${pdfPath}`);
        const chunks: Uint8Array[] = [];
        for await (const chunk of pdfStream) {
            chunks.push(chunk);
        }
        const pdfBytes = Buffer.concat(chunks);

        loadedPdfDoc = await PDFDocument.load(pdfBytes);

    } catch (err: any) {
        // adjust these checks to match whatever error your fileManager throws
        const msg = err.message ?? '';
        console.warn(`appendPages: ${pdfPath} not found, skipping`);
        return;
    }

    // determine which pages to copy
    const totalPages = loadedPdfDoc.getPageCount();
    const indices =
        pagesToCopy ??
        Array.from({ length: totalPages }, (_, i) => i);

    // copy each of those pages `count` times
    for (let i = 0; i < count; i++) {
        const pages = await pdfDoc.copyPages(loadedPdfDoc, indices);
        for (const p of pages) {
            pdfDoc.addPage(p);
        }
    }
}



export async function createContractPDF(contract: any, customer: any, user: any, token: string) {
    try {
        const isoString = contract.updatedAt;
        const date = new Date(isoString);
        const formattedDate = `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;

        const pdfBytesStreamCover = await fileManager.downloadFile("assets/police_cover.pdf");
        const chunksCover: Uint8Array[] = [];
        for await (const chunk of pdfBytesStreamCover) {
            chunksCover.push(chunk);
        }
        const coverPdfBytes = Buffer.concat(chunksCover);
        const pdfDoc = await PDFDocument.load(coverPdfBytes);

        function getBlankPagesFor(contract: ContractData): number {
            switch (contract.contract_type) {
                case "hausrat":
                case "wohngebaeude":
                case "tierhalterhaftpflicht":
                case "privathaftpflicht":
                case "haus_und_grundbesitzerhaftpflicht":
                case "bauleistung":
                case "bauherrenhaftpflicht":
                case "geschaeftsversicherung":
                case "gebaeudeversicherung":
                case "betriebshaftpflicht":
                    // all of these always get exactly 1 blank page
                    return 2;

                case "unfallversicherung":
                    // special case: number of insured persons + 1
                    const count = contract.insured_persons?.length ?? 0;
                    return count + 1;

                default:
                    // fallback if an unknown contract_type sneaks through
                    console.warn(`Unknown contract_type: ${contract.contract_type}`);
                    return 1;
            }
        }

        // appand blank pages
        const number_blank_pages = getBlankPagesFor(contract);
        await appendPages(pdfDoc, "police_page_blank.pdf", number_blank_pages);
        // append declarations
        await appendPages(pdfDoc, `${contract.contract_type}.pdf`, 1)
        // appand general conditions
        await appendPages(pdfDoc, 'general_insurance_conditions.pdf', 1)

        // fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaFontItalic = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);
        const helveticaFontBoldItalic = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);
        const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

        // text size
        const titleFontSize = 13;
        const textFontSize = 11;

        // pages
        const pages = pdfDoc.getPages()

        // draw number of pages
        for (const [index, page] of pages.entries()) {
            page.drawText(`[${index + 1}/${pages.length}]`, {
                x: page.getWidth() - 30,
                y: 10,
                size: 10,
                font: helveticaFont
            });
        }

        // Draw all environment banners.
        for (const page of pages) {
            const bannerX = page.getWidth() - 136;
            const bannerY = page.getHeight() - 50;
            drawEnvironmentBanner(page, helveticaBoldFont, bannerX, bannerY);
        }

        // PAGE 1
        let page = pages[0]
        let yPosition = 720;

        function setPage(pageNumber: number) {
            page = pages[pageNumber];
            yPosition = 720
        }

        const addText = (value: any, bold: boolean = true, italic = false) => {
            if (value) {
                page.drawText(`${value}`, { x: 72, y: yPosition, size: textFontSize, font: bold ? italic ? helveticaFontBoldItalic : helveticaBoldFont : italic ? helveticaFontItalic : helveticaFont, });
                yPosition -= 20;
            }
        };

        const addSubPoint = (key: any, value: any) => {
            if (key && value) {
                page.drawText(`   •   `, { x: 72, y: yPosition, size: textFontSize, font: helveticaFont, })
                page.drawText(`${key}: `, { x: 72 + helveticaBoldFont.widthOfTextAtSize(`   •   `, textFontSize), y: yPosition, size: textFontSize, font: helveticaBoldFont, });
                page.drawText(`${value}`, { x: 72 + helveticaBoldFont.widthOfTextAtSize(`   •   ${key}: `, textFontSize), y: yPosition, size: textFontSize, font: helveticaFont, });
                yPosition -= 20;
            }
        }

        const addLine = () => {
            page.drawLine({ start: { x: 72, y: yPosition }, end: { x: 550, y: yPosition }, thickness: 1, color: rgb(0.9, 0.9, 0.9) });
            yPosition -= 20;
        };

        // cusomer adress
        page.drawText(customer.salutation, { x: 72, y: 720, size: textFontSize, font: helveticaFont, });

        if (customer.name_prefix) {
            page.drawText(`${customer.name_prefix} ${customer.first_name} ${customer.last_name}`, { x: 72, y: 705, size: textFontSize, font: helveticaFont, });
        } else {
            page.drawText(`${customer.first_name} ${customer.last_name}`, { x: 72, y: 705, size: textFontSize, font: helveticaFont, });
        }

        if (customer.care_of) {
            page.drawText(`c/o ${customer.care_of}`, { x: 72, y: 690, size: textFontSize, font: helveticaFont, });
            page.drawText(`${customer.street} ${customer.house_number}`, { x: 72, y: 675, size: textFontSize, font: helveticaFont, });
            page.drawText(`${customer.postal_code} ${customer.city}`, { x: 72, y: 660, size: textFontSize, font: helveticaFont, });
        } else {
            page.drawText(`${customer.street} ${customer.house_number}`, { x: 72, y: 690, size: textFontSize, font: helveticaFont, });
            page.drawText(`${customer.postal_code} ${customer.city}`, { x: 72, y: 675, size: textFontSize, font: helveticaFont, });
        }

        // document date
        page.drawText(`Hamburg, ${formattedDate}`, { x: 400, y: 720, size: textFontSize, font: helveticaFont, });

        // document title
        page.drawText(`${formatLabel(contract.contract_type)} – ${contract.is_offer ? "Angebot" : "Versicherungspolice"} Nr. ${contract.contract_number}`, { x: 72, y: 580, size: titleFontSize, font: helveticaFont });
        page.drawText(`${contract.is_offer ? "Angebot" : "Versicherungspolice"}`, { x: 110, y: 480, size: 30, font: helveticaFont });

        // agent adress
        page.drawText(`Ihr Makler:`, { x: 72, y: 320, size: textFontSize, font: helveticaFont, });
        page.drawText(`${user.company_name}`, { x: 72, y: 305, size: textFontSize, font: helveticaFont, });
        page.drawText(`${user.street} ${user.house_number}`, { x: 72, y: 290, size: textFontSize, font: helveticaFont, });
        page.drawText(`${user.postal_code} ${user.city}`, { x: 72, y: 275, size: textFontSize, font: helveticaFont, });
        page.drawText(`${user.telephone_number}`, { x: 72, y: 260, size: textFontSize, font: helveticaFont, });
        page.drawText(`${user.url}`, { x: 72, y: 245, size: textFontSize, font: helveticaFont, });


        // PAGE 2
        page = pages[1]
        yPosition = 720

        addText(`Sparte:`);
        addText(`   •   ${formatLabel(contract.contract_type)}`, false);
        addLine();

        addText(`Versicherungszeitraum:`);
        const startDate = new Date(contract.insurance_start_date);
        addSubPoint(`Versicherungsbeginn`, ` ${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()}`);
        const endDate = new Date(contract.insurance_end_date);
        addSubPoint(`Versicherungsablauf`, `  ${endDate.getDate().toString().padStart(2, '0')}.${(endDate.getMonth() + 1).toString().padStart(2, '0')}.${endDate.getFullYear()}`);
        addLine();

        // accident insurance
        if (contract.contract_type === 'unfallversicherung') {
            await createAccidentInsurancePdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // animal liability insurance
        if (contract.contract_type === 'tierhalterhaftpflicht') {
            await createAnimalLiabilityPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // building insurance
        if (contract.contract_type === 'gebaeudeversicherung') {
            await createBuildingPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // business insurance
        if (contract.contract_type === 'geschaeftsversicherung') {
            await createBusinessPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // business liability insurance
        if (contract.contract_type === 'betriebshaftpflicht') {
            await createBusinessLiabilityPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // construction insurance
        if (contract.contract_type === 'bauleistung') {
            await createConstructionPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // construction owner liability insurance
        if (contract.contract_type === 'bauherrenhaftpflicht') {
            await createConstructionOwnerPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // home and landowner liability insurance
        if (contract.contract_type === 'haus_und_grundbesitzerhaftpflicht') {
            await createHomePdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // household insurance
        if (contract.contract_type === 'hausrat') {
            await createHouseholdPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // personal liability insurance
        if (contract.contract_type === 'privathaftpflicht') {
            await createPersonalLiabilityPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        // residential building insurance
        if (contract.contract_type === 'wohngebaeude') {
            await createResidentialBuildingPdfData(contract, addText, addSubPoint, addLine, setPage, token);
        }

        if (!contract.is_offer) {
            addText(`Zahlung:`);
            if (contract.iban === "" && contract.bic === "") {
                addText(`Bitte überweisen Sie den fälligen Betrag auf folgendes Konto:`, false);
                addSubPoint(`IBAN`, `DE06 2005 0550 1506 4610 43`);
                addSubPoint(`Verwendungszweck`, ` ${contract.contract_number}`);
            } else {
                addSubPoint(`IBAN`, `${contract.iban}`);
                addSubPoint(`BIC`, `${contract.bic}`);
                addSubPoint(`Verwendungszweck`, ` ${contract.contract_number}`);
                addText(`Der Betrag wird automatisch von dem genannten Konto abgebucht.`, false, true);
            }
            addLine();
        }

        addText(`SHB Allgemeine Versicherung VVaG, Johannes-Albers-Allee 2, 53639 Königswinter`, false);
        addText(`Wir bescheinigen, dass der Umsatz gemäß §4 Nr. 10a UStG steuerfrei ist. Steuernummer: 43/702/02422`, false);

        const pdfBytes = await pdfDoc.save();
        return pdfBytes;
    } catch (error) {
        console.error('Error creating PDF:', error);
        throw new Error('PDF creation failed');
    }
}

export async function createInvoicePDF(invoiceData: InvoiceData, preview = false) {
    try {
        const due_date_obj = new Date(invoiceData.due_date)
        const current_date_obj = new Date()
        const formattedDate = `${due_date_obj.getDate().toString().padStart(2, '0')}.${(due_date_obj.getMonth() + 1).toString().padStart(2, '0')}.${due_date_obj.getFullYear()}`;
        const formattedCurrentDate = `${current_date_obj.getDate().toString().padStart(2, '0')}.${(current_date_obj.getMonth() + 1).toString().padStart(2, '0')}.${current_date_obj.getFullYear()}`;

        // download template
        const pdfBytesStream = await fileManager.downloadFile("assets/" + formatUrl("invoice"))
        const chunks: Uint8Array[] = [];
        for await (const chunk of pdfBytesStream) {
            chunks.push(chunk);
        }
        const existingPdfBytes = Buffer.concat(chunks);

        const pdfDoc = await PDFDocument.load(existingPdfBytes)

        // fonts
        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
        const helveticaFontItalic = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);
        const helveticaFontBoldItalic = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);
        const helveticaBoldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

        // text size
        const titleFontSize = 13;
        const textFontSize = 11;

        // pages
        let pages = pdfDoc.getPages()

        // Draw all environment banners.
        for (const page of pages) {
            const bannerX = page.getWidth() - 136;
            const bannerY = page.getHeight() - 50;
            drawEnvironmentBanner(page, helveticaBoldFont, bannerX, bannerY);
            drawPreviewBanner(page, helveticaBoldFont, bannerX, bannerY, preview);
        }

        // PAGE 1
        let page = pages[0]
        let yPosition = 720;

        function setPage(pageNumber: number) {
            page = pages[pageNumber];
            yPosition = 720
        }

        const addText = (value: any, bold: boolean = false, italic = false) => {
            if (value) {
                page.drawText(`${value}`, { x: 72, y: yPosition, size: textFontSize, font: bold ? italic ? helveticaFontBoldItalic : helveticaBoldFont : italic ? helveticaFontItalic : helveticaFont, });
                yPosition -= 15;
            }
        };

        const addSubPoint = (key: any, value: any) => {
            if (key && value) {
                page.drawText(`   •   `, { x: 72, y: yPosition, size: textFontSize, font: helveticaFont, })
                page.drawText(`${key}: `, { x: 72 + helveticaBoldFont.widthOfTextAtSize(`   •   `, textFontSize), y: yPosition, size: textFontSize, font: helveticaBoldFont, });
                page.drawText(`${value}`, { x: 72 + helveticaBoldFont.widthOfTextAtSize(`   •   ${key}: `, textFontSize), y: yPosition, size: textFontSize, font: helveticaFont, });
                yPosition -= 22;
            }
        }

        const addTextWithKey = (key: any, value: any) => {
            if (key && value) {
                page.drawText(`${key}: `, { x: 72, y: yPosition, size: textFontSize, font: helveticaBoldFont, });
                page.drawText(`${value}`, { x: 72 + helveticaBoldFont.widthOfTextAtSize(`${key}: `, textFontSize), y: yPosition, size: textFontSize, font: helveticaFont, });
                yPosition -= 15;
            }
        }

        const addLine = () => {
            page.drawLine({ start: { x: 72, y: yPosition }, end: { x: 550, y: yPosition }, thickness: 1, color: rgb(0.9, 0.9, 0.9) });
            yPosition -= 22;
        };

        function drawRightAlignedText(
            text: string,
            font: PDFFont,
            size: number,
            rightEdgeX: number,
            y: number,
            page: PDFPage
        ) {
            // Measure how wide the text is
            const textWidth = font.widthOfTextAtSize(text, size);
            // Subtract the width from the right edge to get the X coordinate
            const x = rightEdgeX - textWidth;

            page.drawText(text, {
                x,
                y,
                size,
                font,
            });
        }

        function drawNumberOfPages() {
            for (const [index, page] of pages.entries()) {
                page.drawText(`[${index + 1}/${pages.length}]`, {
                    x: page.getWidth() - 30,
                    y: 10,
                    size: 10,
                    font: helveticaFont
                });
            }

        }

        // Example of a table-drawing function
        async function drawPositions(
            data: { sparte: string; net: number, tax_name: string, tax_amount: number }[],
            startY: number
        ) {
            let y = startY;
            const textSize = 11;

            // Define right edges of the "annual" and "final" columns
            const ANNUAL_RIGHT_EDGE = 430;
            const FINAL_RIGHT_EDGE = 550;

            // Header
            page.drawText('Sparten: Deckungsumfang siehe Deklaration', {
                x: 72,
                y,
                size: textFontSize,
                font: helveticaBoldFont,
            });
            // drawRightAlignedText('Jahresbeitrag', helveticaBoldFont, textSize, ANNUAL_RIGHT_EDGE, y);
            drawRightAlignedText('Erhebungsbeitrag', helveticaBoldFont, textSize, FINAL_RIGHT_EDGE, y, page);

            y -= 5;

            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;

            // Table data rows
            let netto = 0
            let pageIndex = 0
            for (const row of data) {
                // Left-aligned "sparten" in the first column
                page.drawText(row.sparte, {
                    x: 72,
                    y,
                    size: textSize,
                    font: helveticaFont,
                });

                // Right-aligned text for final columns
                drawRightAlignedText(`${(Math.round(row.net * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, FINAL_RIGHT_EDGE, y, page);
                netto += row.net
                y -= 15;
                if (y <= 110) {
                    const templatePdfDoc = await PDFDocument.load(existingPdfBytes)
                    const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0])
                    pdfDoc.addPage(copiedPages[0])
                    pages = pdfDoc.getPages()
                    pageIndex += 1
                    page = pages[pageIndex]
                    y = 630
                }
            }
            y += 10;
            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;

            page.drawText("Gesamtnettobeitrag:", {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            // drawRightAlignedText(`${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, ANNUAL_RIGHT_EDGE, y);
            drawRightAlignedText(`${(Math.round(netto * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, FINAL_RIGHT_EDGE, y, page);

            y -= 25
            if (invoiceData.total_net > 0)
                page.drawText(`Vom ${due_date_obj.getDate().toString().padStart(2, '0')}.${(due_date_obj.getMonth() + 1).toString().padStart(2, '0')}.${due_date_obj.getFullYear()} bis ${calculateFutureDate(invoiceData.due_date, invoiceData.payment_mode, invoiceData.insurance_end_date)} sind zu zahlen:`, {
                    x: 72,
                    y,
                    size: textSize,
                    font: helveticaBoldFont,
                });
            y -= 15
            page.drawText(`Beitrag`, {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            drawRightAlignedText(`${(Math.round(netto * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, FINAL_RIGHT_EDGE, y, page);
            y -= 15
            // page.drawText(`Ratenzuschlag`, {
            //     x: 72,
            //     y,
            //     size: textSize,
            //     font: helveticaFont,
            // });
            // drawRightAlignedText("0,00 EUR", helveticaFont, textSize, FINAL_RIGHT_EDGE, y);
            y -= 15
            for (const row of data) {
                page.drawText(`${row.tax_name.replace("", "")}`, {
                    x: 72,
                    y,
                    size: textSize,
                    font: helveticaFont,
                });
                drawRightAlignedText(`${(Math.round(row.tax_amount * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, FINAL_RIGHT_EDGE, y, page);
                y -= 15;
                if (y <= 110) {
                    const templatePdfDoc = await PDFDocument.load(existingPdfBytes)
                    const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0])
                    pdfDoc.addPage(copiedPages[0])
                    pages = pdfDoc.getPages()
                    pageIndex += 1
                    page = pages[pageIndex]
                    y = 630
                }
            }
            y += 10;
            page.drawLine({
                start: { x: 72, y },
                end: { x: 550, y },
                thickness: 1,
                color: rgb(0.9, 0.9, 0.9),
            });
            y -= 12;
            page.drawText(invoiceData.total_net > 0 ? `Rechnungsbetrag` : 'Betrag', {
                x: 72,
                y,
                size: textSize,
                font: helveticaFont,
            });
            drawRightAlignedText(`${(Math.round(invoiceData.total_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`, helveticaFont, textSize, FINAL_RIGHT_EDGE, y, page);
            return y
        }


        // Customer Address
        if (invoiceData.name_prefix) {
            page.drawText(`${invoiceData.name_prefix} ${invoiceData.first_name} ${invoiceData.last_name}`, { x: 72, y: 660, size: textFontSize, font: helveticaFont });
        } else {
            page.drawText(`${invoiceData.first_name} ${invoiceData.last_name}`, { x: 72, y: 660, size: textFontSize, font: helveticaFont });
        }

        if (invoiceData.billing_care_of) {
            page.drawText(`c/o ${invoiceData.billing_care_of}`, { x: 72, y: 645, size: textFontSize, font: helveticaFont, });
            page.drawText(`${invoiceData.billing_street} ${invoiceData.billing_house_number}`, { x: 72, y: 630, size: textFontSize, font: helveticaFont });
            page.drawText(`${invoiceData.billing_postal_code} ${invoiceData.billing_city}`, { x: 72, y: 615, size: textFontSize, font: helveticaFont });
        } else {
            page.drawText(`${invoiceData.billing_street} ${invoiceData.billing_house_number}`, { x: 72, y: 645, size: textFontSize, font: helveticaFont });
            page.drawText(`${invoiceData.billing_postal_code} ${invoiceData.billing_city}`, { x: 72, y: 630, size: textFontSize, font: helveticaFont });
        }

        // Agent Address
        const xAddressPosition = 385
        page.drawText(`Vermittler:`, { x: xAddressPosition, y: 660, size: textFontSize, font: helveticaBoldFont });
        page.drawText(`Nummer: ${invoiceData.agent_number}`, { x: xAddressPosition, y: 645, size: textFontSize, font: helveticaFont });
        page.drawText(invoiceData.agent_company_name, { x: xAddressPosition, y: 630, size: textFontSize, font: helveticaFont });
        page.drawText(`${invoiceData.agent_street} ${invoiceData.agent_house_number}`, { x: xAddressPosition, y: 615, size: textFontSize, font: helveticaFont });
        page.drawText(`${invoiceData.agent_postal_code} ${invoiceData.agent_city}`, { x: xAddressPosition, y: 600, size: textFontSize, font: helveticaFont });

        // Date
        page.drawText(`Hamburg, ${formattedCurrentDate}`, { x: xAddressPosition, y: 570, size: textFontSize, font: helveticaFont, });

        // begin
        yPosition = 540
        if (invoiceData.name_prefix) {
            addTextWithKey('Versicherungsnehmer', `${invoiceData.name_prefix} ${invoiceData.first_name} ${invoiceData.last_name}`)
        } else {
            addTextWithKey('Versicherungsnehmer', `${invoiceData.first_name} ${invoiceData.last_name}`)
        }
        addTextWithKey('Kundennummer', `${invoiceData.customer_number}`)
        addTextWithKey('Versicherungsschein', `${invoiceData.contract_number}`)

        yPosition = 470
        addText(formatInvoiceType(invoiceData.type) + ((invoiceData.subject == undefined || invoiceData.subject == '') ? '' : (': ' + invoiceData.subject)), true)

        yPosition = 400
        // if (contract.glass_insurance) {
        //     const tax_value = (getTax(calculationParameters, contract.contract_type) * 100).toLocaleString('de', { minimumFractionDigits: 0, maximumFractionDigits: 4 })
        //     const tax_value_glass = (getTax(calculationParameters, "wohngebaeude_glass") * 100).toLocaleString('de', { minimumFractionDigits: 0, maximumFractionDigits: 4 })
        //     const netto_value = (Math.round(calculatePrice(contract.premie!, contract.payment_mode) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        //     const netto_value_glass = (Math.round(calculatePrice(contract.premie_glass_insurance!, contract.payment_mode) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        //     drawPositions([{ sparte: formatLabel(contract.contract_type), final: calculatePrice(contract.premie!, contract.payment_mode), tax_name: `Versicherungssteuer ${tax_value}% auf ${netto_value} EUR netto`, final_tax: calculatePrice(contract.tax!, contract.payment_mode) },
        //     { sparte: "Gebäudeglasversicherung", final: calculatePrice(contract.premie_glass_insurance!, contract.payment_mode), tax_name: `Versicherungssteuer ${tax_value_glass}% auf ${netto_value_glass} EUR netto`, final_tax: calculatePrice(contract.glass_tax!, contract.payment_mode) }], 400)
        // }
        // else {
        //     const tax_value = (getTax(calculationParameters, contract.contract_type) * 100).toLocaleString('de', { minimumFractionDigits: 0, maximumFractionDigits: 4 })
        //     const netto_value = (Math.round(calculatePrice(contract.premie!, contract.payment_mode) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        //     drawPositions([{ sparte: formatLabel(contract.contract_type), final: calculatePrice(contract.premie!, contract.payment_mode), tax_name: `Versicherungssteuer ${tax_value}% auf ${netto_value} EUR netto`, final_tax: calculatePrice(contract.tax!, contract.payment_mode) }], 400)
        // }
        const positionData = []
        for (const position of invoiceData.positions) {
            positionData.push({ sparte: position.name, net: position.net, tax_name: `Versicherungssteuer ${position.tax * 100}% auf ${position.net} EUR netto`, tax_amount: position.tax_amount })
        }
        yPosition = await drawPositions(positionData, 430) - 50

        if (yPosition <= 198) {
            const oldIndex = pages.length
            const templatePdfDoc = await PDFDocument.load(existingPdfBytes)
            const copiedPages = await pdfDoc.copyPages(templatePdfDoc, [0])
            pdfDoc.addPage(copiedPages[0])
            pages = pdfDoc.getPages()
            page = pages[oldIndex]
            yPosition = 630
        }

        //yPosition = 200
        if (invoiceData.iban === "" && invoiceData.bic === "") {
            if (invoiceData.total_net > 0) {
                addText("Wir bitten Sie, den fälligen Rechnungsbetrag zu begleichen.")
                addTextWithKey('IBAN', 'DE06 2005 0550 1506 4610 43')
                addTextWithKey('BIC', 'HASPDEHHXXX')
            }
            else
                addText("Wir werden Ihnen den Betrag baldmöglichst überweisen.")
            addTextWithKey('Verwendungszweck', invoiceData.contract_number)
        } else {
            if (invoiceData.total_net > 0)
                addText(`Der Betrag wird automatisch von dem genannten Konto abgebucht.`)
            else
                addText("Wir werden Ihnen, den Rechnungsbetrag baldmöglichst überweisen.")
            addTextWithKey('IBAN', `${invoiceData.iban}`)
            addTextWithKey('BIC', `${invoiceData.bic}`)
            addTextWithKey('Verwendungszweck', invoiceData.contract_number)
        }

        yPosition = yPosition - 40
        page.drawText("SHB Allgemeine Versicherung VVaG, Johannes-Albers-Allee 2, 53639 Königswinter", {
            x: 72,
            y: yPosition,
            size: 9,
            font: helveticaFont,
        });
        page.drawText("Wir bescheinigen, dass der Umsatz gemäß §4 Nr. 10a UStG steuerfrei ist. Steuernummer: 43/702/02422", {
            x: 72,
            y: yPosition - 15,
            size: 9,
            font: helveticaFont,
        });

        drawNumberOfPages()

        const pdfBytes = await pdfDoc.save();
        return pdfBytes;
    } catch (error) {
        console.error('Error creating PDF:', error);
        throw new Error('PDF creation failed');
    }
}

function getTax(calculationParameters: CalculationParametersData, contract_type: string) {
    const labelMap: { [key: string]: number } = {
        hausrat: calculationParameters.hausrat_tax,
        wohngebaeude: calculationParameters.wohngebaude_tax,
        wohngebaeude_glass: calculationParameters.wohngebaude_glasversicherung_tax,
        tierhalterhaftpflicht: calculationParameters.tierhalterhaftpflicht_tax,
        privathaftpflicht: calculationParameters.privathaftpflicht_tax,
        haus_und_grundbesitzerhaftpflicht: calculationParameters.haus_und_grundbesitzerhaftpflicht_tax,
        bauleistung: calculationParameters.bauleistung_tax,
        bauherrenhaftpflicht: calculationParameters.bauherrenhaftpflicht_tax,
        geschaeftsversicherung: calculationParameters.gebaeudeversicherung_tax,
        gebaeudeversicherung: calculationParameters.gebaeudeversicherung_tax,
        betriebshaftpflicht: calculationParameters.betriebshaftpflicht_tax,
        unfallversicherung: 0,
    };
    return labelMap[contract_type]
}

interface EnvironmentBannerOptions {
    envText?: string;
    width?: number;
    height?: number;
    fontSize?: number;
}

/**
 * Draws a debug banner on the specified PDF page.
 *
 * @param page - The pdf-lib page object to draw on.
 * @param font - The embedded font to use for the banner text.
 * @param x - The x coordinate where the banner should be drawn.
 * @param y - The y coordinate where the banner should be drawn.
 * @param options - Optional parameters for customization.
 */
export function drawEnvironmentBanner(
    page: PDFPage,
    font: PDFFont,
    x: number,
    y: number,
    options: EnvironmentBannerOptions = {}
): void {
    const {
        envText =
        process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev'
            ? 'TEST'
            : process.env.NEXT_PUBLIC_ENVIRONMENT === 'stage'
                ? 'STAGE'
                : '',
        width = 80,
        height = 20,
        fontSize = 12,
    } = options;

    // Only draw the banner if envText is set.
    if (!envText) return;

    // Draw the banner background as a red rectangle.
    page.drawRectangle({
        x,
        y,
        width,
        height,
        color: rgb(1, 0, 0), // red
    });

    // Calculate the position to center the text in the banner.
    const textWidth = font.widthOfTextAtSize(envText, fontSize);
    const textX = x + (width - textWidth) / 2;
    const textY = y + (height - fontSize) / 2 + 1.5;

    // Draw the banner text in white.
    page.drawText(envText, {
        x: textX,
        y: textY,
        size: fontSize,
        font,
        color: rgb(1, 1, 1), // white
    });
}

export function drawPreviewBanner(
    page: PDFPage,
    font: PDFFont,
    x: number,
    y: number,
    preview: boolean,
    options: EnvironmentBannerOptions = {}
): void {
    const {
        width = 80,
        height = 20,
        fontSize = 12,
    } = options;

    // Only draw the banner if envText is set.
    if (!preview) return;

    // Draw the banner background as a red rectangle.
    page.drawRectangle({
        x,
        y,
        width,
        height,
        color: rgb(1, 0, 0), // red
    });

    // Calculate the position to center the text in the banner.
    const textWidth = font.widthOfTextAtSize('Vorschau', fontSize);
    const textX = x + (width - textWidth) / 2;
    const textY = y + (height - fontSize) / 2 + 1.5;

    // Draw the banner text in white.
    page.drawText('Vorschau', {
        x: textX,
        y: textY,
        size: fontSize,
        font,
        color: rgb(1, 1, 1), // white
    });
}
