/**
 * Converts a camelCase or PascalCase field name to a human-readable format
 *
 * @example
 * humanizeFieldName('firstName') // 'First Name'
 * humanizeFieldName('outpatientDiagnoses') // 'Outpatient Diagnoses'
 * humanizeFieldName('BMI') // 'BMI'
 */
export const humanizeFieldName = (field: string | number): string => {
  const str = field.toString();

  return str
    .split(/(?=[A-Z])/) // Split on capital letters
    .join(' ')
    .replace(/^./, (char) => char.toUpperCase()); // Capitalize first letter
};
