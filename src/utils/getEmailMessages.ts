import {getTranslations} from "@/server/translations";

export function getEmailMessages(locale: string, group: string) {
  const translations: Record<string, any> = getTranslations(locale)
  const base = translations.email;

  const groupMessages = base?.[group];
  const footerMessages = base?.footer;

  if (!groupMessages) {
    throw new Error(
      `No translations found for locale: ${locale} and group ${group}`
    );
  }

  if (!footerMessages) {
    throw new Error(
      `No footer translations found for locale: ${locale}`
    );
  }

  // footer wird hinten angehängt, Keys aus group überschreiben ggf. footer
  return {
    ...footerMessages,
    ...groupMessages,
  };
}
