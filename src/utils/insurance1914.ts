export type BuildingSelection = {
  roofType: string;
  hasBasement: boolean;
  floorsFactor: number;
};

export type SurchargeSelection = {
  [key: string]: boolean;
};

export type AdditionalAreas = {
  livingArea: number;
  basementLiving: number;
  garageSpaces: number;
  carportSpaces: number;
  undergroundSpaces: number;
};

export type SpecialFeature = {
  description: string;
  newBuildValueEUR: number;
};

export type Outbuilding = {
  usage: string;
  wallType: string;
  roofType: string;
  squareMeters: number;
  newBuildValueEUR: number;
};

export const FACTORS = {
  garage: 700,
  carport: 350,
  underground: 1000,
  basementLiving: 20,
  // Step1 floors factors and Step2 surcharges should also come from DB later
};

export const BAUPREIS_INDEX_2025 = 21.9;

export function calculateInsurance1914(
  building: BuildingSelection,
  surcharges: Record<string, number>, // selected surcharges with their numeric values
  additional: AdditionalAreas,
  special: SpecialFeature[],
  outbuildings: Outbuilding[]
) {
  // sum from step 1 (floorsFactor) and step 2 (all additions)
  const combinedFactor =
    building.floorsFactor +
    Object.values(surcharges).reduce((a, b) => a + b, 0);

  // 2this sum is multiplied with livingArea
  const base1914 = additional.livingArea * combinedFactor;

  // add additional areas
  const additionalValue =
    additional.basementLiving * FACTORS.basementLiving +
    additional.garageSpaces * FACTORS.garage +
    additional.carportSpaces * FACTORS.carport +
    additional.undergroundSpaces * FACTORS.underground;

  // specialValue = special components in euro divided by Baupreisindex
  const specialValue = special.reduce(
    (sum, s) => sum + s.newBuildValueEUR / BAUPREIS_INDEX_2025,
    0
  );

  // outbuildingValue = outbuildings in euro divided by Baupreisindex
  const outbuildingValue = outbuildings.reduce(
    (sum, o) => sum + o.newBuildValueEUR / BAUPREIS_INDEX_2025,
    0
  );

  // total 1914
  const versicherungssumme1914 =
    base1914 + additionalValue + specialValue + outbuildingValue;

  // total 2025
  const buildingValue2025 = versicherungssumme1914 * BAUPREIS_INDEX_2025;

  return {
    base1914,
    additionalValue,
    specialValue,
    outbuildingValue,
    versicherungssumme1914,
    buildingValue2025,
  };
}
