export type Building = {
  roofType: 'flat' | 'unfinished_attic' | 'finished_attic';
  hasBasement: boolean;
  floorsNumber: number;
};

const surchargeAmounts = {
  naturalStoneCopperRoof: 4,
  stoneCeramicCladding: 5,
  stuccoWood: 6,
  premiumFlooring: 4,
  metalWoodWindows: 4,
  premiumDoors: 3,
  premiumSanitary: 6,
  heatingRenewableSystems: 6,
  premiumKitchen: 4,
} as const

type Surcharge = keyof typeof surchargeAmounts

export const FACTORS = {
  garage: 700,
  carport: 350,
  underground: 1000,
  basementLiving: 20,
  // Step1 floors factors and Step2 surcharges should also come from DB later
};

export const BAUPREIS_INDEX_2025 = 21.9;

type Params = {
    building: Building
    surcharges: Surcharge[]
    livingArea: number // m², w/o the basement
    basementLivingArea: number // the area used for living/hobbies
    parkingSpaces: {
      garage: number
      carport: number
      underground: number
    }
    specialFeaturesPrice: number // Neubauwert
    outbuildingPrice: number // Neubauwert
}

export function calculateInsurance1914({
    building,
    surcharges,
    livingArea,
    basementLivingArea,
    parkingSpaces,
    specialFeaturesPrice,
    outbuildingPrice,
}: Params) {
  const
  // sum from step 1 (floorsFactor) and step 2 (all additions)
  const combinedFactor =
    building.floorsFactor +
    Object.values(surchargeItems).reduce((a, b) => a + b, 0);

  // 2this sum is multiplied with livingArea
  const base1914 = additional.livingArea * combinedFactor;

  // add additional areas
  const additionalValue =
    additional.basementLiving * FACTORS.basementLiving +
    additional.garageSpaces * FACTORS.garage +
    additional.carportSpaces * FACTORS.carport +
    additional.undergroundSpaces * FACTORS.underground;

  // specialValue = special components in euro divided by Baupreisindex
  const specialValue = special.reduce(
    (sum, s) => sum + s.newBuildValueEUR / BAUPREIS_INDEX_2025,
    0
  );

  // outbuildingValue = outbuildings in euro divided by Baupreisindex
  const outbuildingValue = outbuildings.reduce(
    (sum, o) => sum + o.newBuildValueEUR / BAUPREIS_INDEX_2025,
    0
  );

  // total 1914
  const versicherungssumme1914 =
    base1914 + additionalValue + specialValue + outbuildingValue;

  // total 2025
  const buildingValue2025 = versicherungssumme1914 * BAUPREIS_INDEX_2025;

  return {
    base1914,
    additionalValue,
    specialValue,
    outbuildingValue,
    versicherungssumme1914,
    buildingValue2025,
  };
}

const resolveBaseFactor = ({ roofType, hasBasement, floorsNumber }: Building) => {

}

const floorLists: Record<string, { label: string; factor: number }[]> = {
  // Flachdach
  flat_true: [
    { label: "1-2 Geschosse", factor: 190 },
    { label: "3-4 Geschosse", factor: 150 },
    { label: "5 Geschosse", factor: 135 },
    { label: "6-7 Geschosse", factor: 130 },
  ],
  flat_false: [
    { label: "1 Geschoss", factor: 160 },
    { label: "2 Geschosse", factor: 160 },
    { label: "3-7 Geschosse", factor: 135 },
  ],

  // Dachgeschoss nicht ausgebaut
  attic_plain_true: [
    { label: "1 Geschoss", factor: 190 },
    { label: "2 Geschosse", factor: 165 },
    { label: "3-4 Geschosse", factor: 150 },
    { label: "5-7 Geschosse", factor: 130 },
  ],
  attic_plain_false: [
    { label: "1 Geschoss", factor: 160 },
    { label: "2 Geschosse", factor: 140 },
    { label: "3-7 Geschosse", factor: 135 },
  ],

  // Dachgeschoss ausgebaut
  attic_built_true: [
    { label: "1 Geschoss", factor: 165 },
    { label: "2 Geschosse", factor: 150 },
    { label: "3 Geschosse", factor: 140 },
    { label: "4 Geschosse", factor: 135 },
    { label: "5-7 Geschosse", factor: 130 },
  ],
  attic_built_false: [
    { label: "1 Geschoss", factor: 140 },
    { label: "2 Geschosse", factor: 130 },
    { label: "3-7 Geschosse", factor: 125 },
  ],
};
