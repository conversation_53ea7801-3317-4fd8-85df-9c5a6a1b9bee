[{"id": 1, "code": -1, "name": "Kind", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 2, "code": 482, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 3, "code": 935, "name": "<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 4, "code": 761, "name": "Abgeordnete", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 5, "code": 522, "name": "Abpack-, <PERSON><PERSON><PERSON><PERSON>llmaschineneinsteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 6, "code": 755, "name": "Absatzfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 7, "code": 676, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Handel)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 8, "code": 935, "name": "Abwasserentsorger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 9, "code": 32, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 10, "code": 32, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 11, "code": 883, "name": "Agrarwissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 12, "code": 482, "name": "Akustikmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 13, "code": 864, "name": "<PERSON><PERSON>pf<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 14, "code": 864, "name": "Altentherapeuten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 15, "code": 672, "name": "Ambulante <PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 16, "code": 811, "name": "Amtsanwälte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 17, "code": 352, "name": "Änderungsnäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 18, "code": 351, "name": "Änderungsschneider", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 19, "code": 780, "name": "Angestellte, kaufmännisch", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 20, "code": 252, "name": "Anlagenmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 21, "code": 937, "name": "Anlagenreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 22, "code": 511, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 23, "code": 315, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 24, "code": 775, "name": "Anwendungssoftwareentwickler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 25, "code": 685, "name": "Apothekenhelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 26, "code": 844, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 27, "code": 273, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 28, "code": 24, "name": "Aquarienwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 29, "code": 997, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 30, "code": 868, "name": "Arbeitsberater und -vermittler (BA)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 31, "code": 983, "name": "Arbeitssuchende", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 32, "code": 778, "name": "Arbeitsvorbereiter, Na<PERSON>bereiter  (EDV)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 33, "code": 609, "name": "Architekten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 34, "code": 823, "name": "Archivare", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 35, "code": 838, "name": "Artisten", "gefahrengruppe": "Gefahrengruppe-N"}, {"id": 36, "code": 841, "name": "Ärzte (Humanmedizin)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 37, "code": 856, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 38, "code": 482, "name": "As<PERSON><PERSON><PERSON>uer (nicht Straßenbau)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 39, "code": 612, "name": "Astronomen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 40, "code": 612, "name": "Atomphysiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 41, "code": 626, "name": "Atomtechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 42, "code": 792, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 43, "code": 304, "name": "Aug<PERSON>pt<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 44, "code": 652, "name": "<PERSON><PERSON><PERSON><PERSON>, Ausbildung<PERSON><PERSON>, -meister", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 45, "code": 695, "name": "Außendienstmitarbeiter (Versicherung)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 46, "code": 671, "name": "Außenhandelskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 47, "code": 544, "name": "Autokranführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 48, "code": 550, "name": "Automateneinrichter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 49, "code": 300, "name": "Automatenmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 50, "code": 662, "name": "Autoverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 51, "code": 936, "name": "<PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 52, "code": 391, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 53, "code": 391, "name": "Bäckereimaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 54, "code": 391, "name": "Backwarenhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 55, "code": 661, "name": "Backwarenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 56, "code": 795, "name": "Bademeister", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 57, "code": 792, "name": "Badewärter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 58, "code": 545, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 59, "code": 274, "name": "Bahnbetriebsschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 60, "code": 307, "name": "Bandagist", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 61, "code": 531, "name": "Bandarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 62, "code": 549, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 63, "code": 651, "name": "Bandmeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 64, "code": 691, "name": "Bankfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 65, "code": 915, "name": "Barmixer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 66, "code": 803, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 67, "code": 11, "name": "Bauern", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 68, "code": 623, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 69, "code": 485, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 70, "code": 472, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 71, "code": 741, "name": "Bauhofverwalter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 72, "code": 603, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 73, "code": 261, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 74, "code": 803, "name": "Baukontrolleure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 75, "code": 544, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 76, "code": 546, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 77, "code": 51, "name": "Baumschulengärtner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 78, "code": 481, "name": "Bauplattenverleger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 79, "code": 254, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 80, "code": 692, "name": "Bausparkassenfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 81, "code": 623, "name": "Baustellentechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 82, "code": 633, "name": "Baustoffprüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 83, "code": 623, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 84, "code": 501, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 85, "code": 642, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 86, "code": 487, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 87, "code": 912, "name": "Bedienungshilfskräfte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 88, "code": 252, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 89, "code": 937, "name": "Behälterreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 90, "code": 411, "name": "Beiköche", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 91, "code": 512, "name": "Beizer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 92, "code": 351, "name": "Bekleidungsschneider", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 93, "code": 627, "name": "Bekleidungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 94, "code": 352, "name": "Bekleidungsteilenäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 95, "code": 835, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 96, "code": 605, "name": "Berg<PERSON>uing<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 97, "code": 625, "name": "Bergbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 98, "code": 71, "name": "Bergleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 99, "code": 72, "name": "Bergmaschinentechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 100, "code": 71, "name": "Bergtechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 101, "code": 624, "name": "Bergvermessungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 102, "code": 868, "name": "Berufsberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 103, "code": 802, "name": "Berufsfeuerwehrleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 104, "code": 714, "name": "Berufskraftfahrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 105, "code": 838, "name": "Berufssportler", "gefahrengruppe": "Gefahrengruppe-N"}, {"id": 106, "code": 295, "name": "Besteckmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 107, "code": 442, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 108, "code": 442, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -mon<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 109, "code": 442, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 110, "code": 546, "name": "Betoniermaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 111, "code": 112, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 112, "code": 461, "name": "Betons<PERSON>ßenbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 113, "code": 112, "name": "Betonwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 114, "code": 750, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 115, "code": 750, "name": "Betriebsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 116, "code": 756, "name": "Betriebsorganisatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 117, "code": 753, "name": "Betriebsprüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 118, "code": 763, "name": "Betriebsräte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 119, "code": 274, "name": "Betriebsschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 120, "code": 651, "name": "Betriebsstellenleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 121, "code": 881, "name": "Betriebswirte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 122, "code": 852, "name": "Bewegungstherapeuten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 123, "code": 823, "name": "Bibliothekare", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 124, "code": 485, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 125, "code": 833, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 126, "code": 835, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -techniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 127, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 128, "code": 724, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 129, "code": 631, "name": "Biologielaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 130, "code": 309, "name": "Biologiemodell<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 131, "code": 631, "name": "Biologisch-technische Sonderfachkräfte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 132, "code": 287, "name": "<PERSON><PERSON><PERSON>karosseriebauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 133, "code": 211, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 134, "code": 261, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 135, "code": 211, "name": "Blechpresser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 136, "code": 265, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 137, "code": 211, "name": "Blechs<PERSON>zer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 138, "code": 621, "name": "Blechverarbeitungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 139, "code": 211, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 140, "code": 261, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 141, "code": 702, "name": "Bodenstewards", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 142, "code": 224, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 143, "code": 549, "name": "Bohrmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 144, "code": 625, "name": "Bohrtechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 145, "code": 224, "name": "Bohrwerkdreher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 146, "code": 506, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 147, "code": 723, "name": "Bootsleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 148, "code": 735, "name": "Bordfunker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 149, "code": 704, "name": "B<PERSON>rsenmakler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 150, "code": 506, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 151, "code": 802, "name": "Brandschutzfachleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 152, "code": 121, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -t<PERSON><PERSON>, -g<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 153, "code": 121, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 154, "code": 421, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 155, "code": 627, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 156, "code": 423, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 157, "code": 627, "name": "Brennereitechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 158, "code": 241, "name": "Brennschneider", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 159, "code": 135, "name": "Brillenoptikschleifer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 160, "code": 544, "name": "Brückenkranführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 161, "code": 716, "name": "Brückenwärter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 162, "code": 466, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 163, "code": 178, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 164, "code": 772, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 165, "code": 674, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 166, "code": 300, "name": "<PERSON><PERSON><PERSON>enmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 167, "code": 915, "name": "Büfettiers, Büfettgehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 168, "code": 661, "name": "Büfettverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 169, "code": 931, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 170, "code": 835, "name": "Bühnenbildner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 171, "code": 832, "name": "Bühnenleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 172, "code": 839, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 173, "code": 549, "name": "Bühnenmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 174, "code": 501, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 175, "code": 761, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 176, "code": 780, "name": "Büroangestellte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 177, "code": 780, "name": "Bürofachkräfte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 178, "code": 780, "name": "Bürogehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 179, "code": 784, "name": "Bürohilfskräfte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 180, "code": 317, "name": "Büroinformationselektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 181, "code": 300, "name": "Büromaschinenmechaniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 182, "code": 788, "name": "<PERSON><PERSON><PERSON><PERSON>bearbeiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 183, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 184, "code": 642, "name": "CAD-<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 185, "code": 641, "name": "CAD-<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 186, "code": 911, "name": "<PERSON><PERSON>-Manager", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 187, "code": 789, "name": "Chefsekretäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 188, "code": 141, "name": "Chemiebetriebswerker, Chemiefacharbeite", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 189, "code": 142, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 190, "code": 141, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 191, "code": 633, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 192, "code": 142, "name": "Chemielaborwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 193, "code": 611, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 194, "code": 931, "name": "Chemischreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 195, "code": 626, "name": "Chemotechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 196, "code": 295, "name": "Chi<PERSON><PERSON>iemechanike<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 197, "code": 831, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 198, "code": 550, "name": "CNC-Maschineneinrichter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 199, "code": 756, "name": "Controller", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 200, "code": 705, "name": "Croupiers", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 201, "code": 835, "name": "Cutter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 202, "code": 488, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 203, "code": 541, "name": "Dampfkraftwerkmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 204, "code": 541, "name": "Dampfmaschinenmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 205, "code": 813, "name": "Datenschutzbeauftragte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 206, "code": 783, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 207, "code": 774, "name": "Datenverarbeitungsfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 208, "code": 779, "name": "Datenverarbeitungskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 209, "code": 23, "name": "Deckstationswärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 210, "code": 465, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 211, "code": 836, "name": "Dekorateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 212, "code": 839, "name": "Dekorationenmaler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 213, "code": 887, "name": "Demographen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 214, "code": 609, "name": "Denkmalspfleger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 215, "code": 834, "name": "Designer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 216, "code": 805, "name": "Desinfektoren", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 217, "code": 423, "name": "Destillateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 218, "code": 791, "name": "Detektive", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 219, "code": 891, "name": "Diakone in der Seelsorge", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 220, "code": 101, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 221, "code": 855, "name": "Diätassistenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 222, "code": 411, "name": "Diätköche", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 223, "code": 881, "name": "Diplom-Betriebswirte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 224, "code": 61, "name": "Diplom-Forstwirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 225, "code": 881, "name": "Diplom-Kaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 226, "code": 32, "name": "Diplom-Landwirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 227, "code": 881, "name": "Diplom-Ökonomen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 228, "code": 881, "name": "Diplom-Volkswirte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 229, "code": 751, "name": "Direktionsassistenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 230, "code": 831, "name": "Dirigenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 231, "code": 780, "name": "Disponenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 232, "code": 823, "name": "Dokumentare", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 233, "code": 822, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 234, "code": 212, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 235, "code": 212, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -verar<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 236, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 237, "code": 212, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 238, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 239, "code": 185, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 240, "code": 221, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 241, "code": 544, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 242, "code": 838, "name": "Dressmen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 243, "code": 675, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 244, "code": 174, "name": "<PERSON>ucker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 245, "code": 179, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 246, "code": 174, "name": "Druckfachwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 247, "code": 173, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 248, "code": 627, "name": "Drucktechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 249, "code": 172, "name": "Druckvorlagenhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 250, "code": 777, "name": "DV-Berater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 251, "code": 776, "name": "DV-Koordinatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 252, "code": 774, "name": "DV-Leiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 253, "code": 776, "name": "DV-Organisatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 254, "code": 779, "name": "DV-<PERSON><PERSON><PERSON><PERSON>, DV-Controller", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 255, "code": 777, "name": "DV-Vertriebsfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 256, "code": 302, "name": "Edelmetallschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 257, "code": 101, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -boh<PERSON>, -<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 258, "code": 632, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 259, "code": 677, "name": "Einkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 260, "code": 677, "name": "Einkaufsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 261, "code": 836, "name": "Einrichtungsberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 262, "code": 442, "name": "Einschaler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 263, "code": 672, "name": "Einzelhandelskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 264, "code": 712, "name": "Eisenbahnbetriebsaufseher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 265, "code": 442, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -flechter, -verleger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 266, "code": 511, "name": "Eisenentroster und - anstreicher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 267, "code": 255, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 268, "code": 310, "name": "Elektriker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 269, "code": 311, "name": "Elektroanlageninstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 270, "code": 321, "name": "Elektrogeräte-, Elektroteilemontierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 271, "code": 316, "name": "Elektro<PERSON><PERSON><PERSON><PERSON>, -mechan<PERSON><PERSON>, -pr<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 272, "code": 602, "name": "Elektroingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 273, "code": 310, "name": "Elektroinstallateure, -monteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 274, "code": 313, "name": "<PERSON>ek<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -mon<PERSON><PERSON>, -wickle", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 275, "code": 541, "name": "Elektromaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 276, "code": 316, "name": "Elektromechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 277, "code": 313, "name": "Elek<PERSON><PERSON><PERSON><PERSON>uer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 278, "code": 316, "name": "Elektroniker (Elektrogerätebau)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 279, "code": 316, "name": "Elektroprüfer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 280, "code": 241, "name": "Elektroschweißer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 281, "code": 622, "name": "Elektrotechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 282, "code": 235, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 283, "code": 235, "name": "Emailschriftenmaler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 284, "code": 521, "name": "Endkontrolleure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 285, "code": 311, "name": "Energieanlagenelektroniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 286, "code": 316, "name": "Energiegeräteelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 287, "code": 541, "name": "Energiemaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 288, "code": 853, "name": "Entbindungspfleger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 289, "code": 229, "name": "<PERSON><PERSON><PERSON><PERSON> (Metall)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 290, "code": 935, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 291, "code": 641, "name": "<PERSON>twu<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 292, "code": 80, "name": "Erdengewinner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 293, "code": 625, "name": "Erdgastechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 294, "code": 80, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Erdgasgewinner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 295, "code": 625, "name": "Erdöltechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 296, "code": 855, "name": "Ernährungsberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 297, "code": 921, "name": "Ernährungswirtschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 298, "code": 883, "name": "Ernährungswissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 299, "code": 191, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 300, "code": 863, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 301, "code": 886, "name": "Erziehungspsychologen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 302, "code": 885, "name": "Erziehungswissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 303, "code": 486, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 304, "code": 671, "name": "Exportkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 305, "code": 871, "name": "Fachhochschullehrer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 306, "code": 873, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 307, "code": 662, "name": "Fachverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 308, "code": 713, "name": "Fahrbetriebsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 309, "code": 878, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 310, "code": 724, "name": "Fährleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 311, "code": 742, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 312, "code": 287, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 313, "code": 621, "name": "Fahrzeugbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 314, "code": 485, "name": "F<PERSON>rzeugglaser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 315, "code": 512, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 316, "code": 492, "name": "Fahrzeugpolsterer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 317, "code": 936, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -p<PERSON>ger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 318, "code": 374, "name": "Fahrzeugsattler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 319, "code": 506, "name": "Fahrzeugstellmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 320, "code": 771, "name": "Fakturisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 321, "code": 865, "name": "Familien<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 322, "code": 626, "name": "Farbentechnik<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 323, "code": 931, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 324, "code": 441, "name": "Fassadenmaurer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 325, "code": 934, "name": "Fassadenreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 326, "code": 212, "name": "<PERSON>er<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 327, "code": 229, "name": "Feiler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 328, "code": 265, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 329, "code": 316, "name": "Feingeräteelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 330, "code": 374, "name": "Fe<PERSON>lederwarenhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 331, "code": 300, "name": "Feinmechaniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 332, "code": 135, "name": "Feinoptiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 333, "code": 374, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 334, "code": 331, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 335, "code": 374, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Portefeullier)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 336, "code": 378, "name": "<PERSON><PERSON><PERSON><PERSON>, -verar<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 337, "code": 485, "name": "<PERSON><PERSON>(bau)glaser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 338, "code": 312, "name": "Fernmeldeanlagenelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 339, "code": 312, "name": "Fernmeldeelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 340, "code": 312, "name": "Fernmeldeinstallateure, -mechaniker, -mon", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 341, "code": 622, "name": "Fernmeldetechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 342, "code": 821, "name": "Fernsehsprecher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 343, "code": 315, "name": "Fernsehtechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 344, "code": 435, "name": "Fertiggerichtekonservierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 345, "code": 606, "name": "Fertigungsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 346, "code": 627, "name": "Fertigungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 347, "code": 441, "name": "Feuerungsbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 348, "code": 802, "name": "Feuerwehrleute (Berufsfeuerwehr)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 349, "code": 676, "name": "Filialleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 350, "code": 835, "name": "Filmbildner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 351, "code": 634, "name": "Filmlaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 352, "code": 349, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 353, "code": 757, "name": "Finanzberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 354, "code": 771, "name": "Finanzfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 355, "code": 704, "name": "Finanzmakler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 356, "code": 751, "name": "Finanzwesenleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 357, "code": 23, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 358, "code": 435, "name": "Fischverarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 359, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 360, "code": 488, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 361, "code": 174, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 362, "code": 131, "name": "Flachglasmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 363, "code": 294, "name": "Flachgraveure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 364, "code": 937, "name": "Flaschenreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 365, "code": 805, "name": "<PERSON><PERSON><PERSON>beschauer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 366, "code": 401, "name": "<PERSON><PERSON><PERSON><PERSON>, Fleischwarenhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 367, "code": 661, "name": "Fleischwarenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 368, "code": 483, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 369, "code": 531, "name": "Fließbandarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 370, "code": 549, "name": "Fließbandmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 371, "code": 53, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 372, "code": 912, "name": "Flugbegleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 373, "code": 283, "name": "<PERSON>lug<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 374, "code": 283, "name": "Fluggerätmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 375, "code": 726, "name": "Flugingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 376, "code": 879, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 377, "code": 726, "name": "Flugsicherungsbedienstete", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 378, "code": 283, "name": "Flugtriebwerkmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 379, "code": 726, "name": "Flugzeugabfertiger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 380, "code": 621, "name": "Flugzeugbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 381, "code": 726, "name": "Flugzeugführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 382, "code": 726, "name": "Flugzeugprüfer(im Wartungsdienst)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 383, "code": 624, "name": "Flurbereinigungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 384, "code": 549, "name": "Fördermaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 385, "code": 201, "name": "Former", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 386, "code": 834, "name": "Formgestalter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 387, "code": 201, "name": "Formgießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 388, "code": 112, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 389, "code": 880, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 390, "code": 61, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 391, "code": 61, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 392, "code": 61, "name": "Forst<PERSON><PERSON><PERSON>, Forstwirte (nicht Waldfacharbeiter)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 393, "code": 837, "name": "Fotograf", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 394, "code": 634, "name": "Fotolaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 395, "code": 838, "name": "Fotomodelle", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 396, "code": 171, "name": "Fotosetzer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 397, "code": 222, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 398, "code": 879, "name": "Freizeitlehrer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 399, "code": 702, "name": "Fremdenführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 400, "code": 782, "name": "Fremdsprachenkorrespondenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 401, "code": 789, "name": "Fremdsprachensekretäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 402, "code": 471, "name": "Friedhofsarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 403, "code": 51, "name": "Friedhofsgärtner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 404, "code": 901, "name": "Friseure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 405, "code": 713, "name": "Fuhrparkleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 406, "code": 715, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 407, "code": 735, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 408, "code": 622, "name": "Funktechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 409, "code": 505, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 410, "code": 181, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 411, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -lehrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 412, "code": 902, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 413, "code": 234, "name": "Galvaniseure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 414, "code": 626, "name": "Galvanotechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 415, "code": 358, "name": "Gardinennäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 416, "code": 51, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 417, "code": 52, "name": "Gartenarchitekten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 418, "code": 52, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -berater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 419, "code": 52, "name": "Gartenbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 420, "code": 52, "name": "Gartenverwalter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 421, "code": 51, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 422, "code": 267, "name": "Gasinstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 423, "code": 794, "name": "Gasthofgehilfen", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 424, "code": 911, "name": "Gastronomen", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 425, "code": 911, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -leiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 426, "code": 914, "name": "Gaststättenkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 427, "code": 911, "name": "Gaststättenpächter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 428, "code": 911, "name": "Gastwirt", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 429, "code": 934, "name": "Gebäudereiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 430, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,-wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 431, "code": 882, "name": "Geisteswissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 432, "code": 891, "name": "Geistliche", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 433, "code": 706, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 434, "code": 472, "name": "Gemeindea<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 435, "code": 894, "name": "Gemeindehelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 436, "code": 853, "name": "Gemeindeschwestern, -pfleger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 437, "code": 11, "name": "G<PERSON>üsebauern", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 438, "code": 435, "name": "Gemüsekonservierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 439, "code": 661, "name": "Gemüseverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 440, "code": 661, "name": "Genußmittelverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 441, "code": 883, "name": "Geowissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 442, "code": 794, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 443, "code": 371, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 444, "code": 814, "name": "Gerichtsvollzieher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 445, "code": 443, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 446, "code": 873, "name": "Gesamtschullehrer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 447, "code": 875, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 448, "code": 751, "name": "Geschäftsbereichsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 449, "code": 750, "name": "Geschäftsführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 450, "code": 194, "name": "Gesenkschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 451, "code": 608, "name": "Gesundheitsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 452, "code": 629, "name": "Gesundheitstechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 453, "code": 423, "name": "Getränkehersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 454, "code": 633, "name": "Getränkelaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 455, "code": 937, "name": "Getränkeleitungsreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 456, "code": 273, "name": "Getriebeschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 457, "code": 803, "name": "Gewerbeaufseher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 458, "code": 763, "name": "Gewerkschaftsfunktionäre, -sekretäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 459, "code": 191, "name": "Gießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 460, "code": 605, "name": "Gießereiingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 461, "code": 625, "name": "Gießereitechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 462, "code": 201, "name": "Gießereiwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 463, "code": 481, "name": "Gipser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 464, "code": 121, "name": "Gipsformengießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 465, "code": 135, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 466, "code": 135, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 467, "code": 131, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 468, "code": 131, "name": "Glasbläser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 469, "code": 485, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 470, "code": 135, "name": "Glasgraveure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 471, "code": 131, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -app<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 472, "code": 514, "name": "Glas<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 473, "code": 131, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 474, "code": 934, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 475, "code": 135, "name": "<PERSON><PERSON><PERSON>, -sch<PERSON><PERSON>, -boh<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 476, "code": 135, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 477, "code": 627, "name": "Glastechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 478, "code": 131, "name": "Glaswerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 479, "code": 463, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 480, "code": 463, "name": "<PERSON><PERSON><PERSON><PERSON>up<PERSON><PERSON>, -meister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 481, "code": 201, "name": "Glockengießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 482, "code": 302, "name": "Goldschmiede", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 483, "code": 834, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 484, "code": 294, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 485, "code": 801, "name": "Grenzschutzbedienstete", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 486, "code": 671, "name": "Großhandelskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 487, "code": 72, "name": "G<PERSON>benschloss<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 488, "code": 873, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 489, "code": 11, "name": "Grünlandwirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 490, "code": 145, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -ve<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 491, "code": 344, "name": "Gummistrumpfstricker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 492, "code": 145, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 493, "code": 300, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 494, "code": 201, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 495, "code": 521, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 496, "code": 715, "name": "Güterkraftverkehrunternehmer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 497, "code": 872, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 498, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 499, "code": 901, "name": "Haarstylist", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 500, "code": 724, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 501, "code": 724, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 502, "code": 194, "name": "Hammerschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 503, "code": 522, "name": "Handelsfachpacker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 504, "code": 671, "name": "Handelskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 505, "code": 687, "name": "Handelsvertreter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 506, "code": 201, "name": "Handformer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 507, "code": 689, "name": "Handlungsreisende", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 508, "code": 376, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 509, "code": 171, "name": "<PERSON><PERSON>zer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 510, "code": 344, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 511, "code": 341, "name": "Handweber", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 512, "code": 873, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 513, "code": 923, "name": "Hausangestellte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 514, "code": 921, "name": "Hausdamen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 515, "code": 794, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 516, "code": 989, "name": "<PERSON><PERSON><PERSON><PERSON>, -m<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 517, "code": 883, "name": "Haushaltswissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 518, "code": 796, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 519, "code": 865, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 520, "code": 937, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 521, "code": 662, "name": "Hausratverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 522, "code": 796, "name": "Hauswarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 523, "code": 921, "name": "Hauswirtschafterinnen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 524, "code": 923, "name": "Hauswirtschaftliche Helfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 525, "code": 921, "name": "Hauswirtschaftsberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 526, "code": 853, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 527, "code": 866, "name": "He<PERSON><PERSON>hungspfleg<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 528, "code": 862, "name": "Heilpädagogen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 529, "code": 851, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 530, "code": 549, "name": "Heizanlagenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 531, "code": 549, "name": "He<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 532, "code": 268, "name": "Heizungsbauer, -installateure, -monteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 533, "code": 838, "name": "Hell<PERSON>her", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 534, "code": 531, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 535, "code": 229, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 536, "code": 440, "name": "<PERSON><PERSON><PERSON><PERSON>achar<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 537, "code": 440, "name": "Hochbaupoliere, Hochbaumeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 538, "code": 440, "name": "Hochbauvorarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 539, "code": 440, "name": "Hochbauwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 540, "code": 174, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 541, "code": 264, "name": "Hochdruckrohrschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 542, "code": 622, "name": "Hochfrequenztechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 543, "code": 191, "name": "Hochofenwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 544, "code": 871, "name": "Hochschuldozenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 545, "code": 871, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 546, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 547, "code": 131, "name": "Hohlglaswarenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 548, "code": 487, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 549, "code": 181, "name": "Holzbearbeitungsmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 550, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 551, "code": 62, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 552, "code": 487, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -mon<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 553, "code": 181, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 554, "code": 506, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 555, "code": 181, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 556, "code": 506, "name": "Holzkonstruktionsbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 557, "code": 512, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 558, "code": 505, "name": "Holzmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 559, "code": 512, "name": "Holzoberflächenveredler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 560, "code": 181, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -s<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 561, "code": 185, "name": "Holzspielwarenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 562, "code": 627, "name": "Holztechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 563, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ma<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 564, "code": 315, "name": "Hörgeräteakustiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 565, "code": 915, "name": "Hotelangestellte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 566, "code": 794, "name": "Hoteldiener", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 567, "code": 911, "name": "Hotelgeschäftsführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 568, "code": 911, "name": "Hoteliers", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 569, "code": 914, "name": "Hotelkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 570, "code": 915, "name": "Hotelportiers", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 571, "code": 256, "name": "Hufschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 572, "code": 24, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 573, "code": 354, "name": "<PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 574, "code": 191, "name": "<PERSON><PERSON><PERSON>facharb<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 575, "code": 605, "name": "Hütteningenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 576, "code": 625, "name": "Hüttentechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 577, "code": 23, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 578, "code": 704, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -ka<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 579, "code": 316, "name": "Industrieelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 580, "code": 785, "name": "Industriekaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 581, "code": 270, "name": "Industriemechaniker (Maschinenbau)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 582, "code": 651, "name": "Industriemeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 583, "code": 774, "name": "Informatiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 584, "code": 317, "name": "Informationselektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 585, "code": 600, "name": "Ingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 586, "code": 836, "name": "Innenarchitekten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 587, "code": 491, "name": "Innendekorateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 588, "code": 269, "name": "Installateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 589, "code": 482, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 590, "code": 61, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 591, "code": 259, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 592, "code": 821, "name": "Journalisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 593, "code": 869, "name": "Jugendherbergsväter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 594, "code": 813, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 595, "code": 632, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 596, "code": 813, "name": "Justitiare", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 597, "code": 302, "name": "Juwelengoldschmiede", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 598, "code": 311, "name": "Kabelmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 599, "code": 212, "name": "Kabelwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 600, "code": 923, "name": "Kabinenstewards", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 601, "code": 484, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 602, "code": 423, "name": "Ka<PERSON>ebereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 603, "code": 423, "name": "Kakaobereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 604, "code": 771, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 605, "code": 80, "name": "Kalkwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 606, "code": 482, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 607, "code": 266, "name": "Kältemechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 608, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 609, "code": 837, "name": "Kameraleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 610, "code": 484, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 611, "code": 466, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 612, "code": 441, "name": "Kanalmaurer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 613, "code": 911, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -leiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 614, "code": 661, "name": "Kantinenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 615, "code": 721, "name": "<PERSON><PERSON><PERSON><PERSON> (Schiffahrt)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 616, "code": 621, "name": "Karosseriebautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 617, "code": 706, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -kontroll<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 618, "code": 642, "name": "Kartographen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 619, "code": 161, "name": "Kartonagenmaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 620, "code": 773, "name": "Kassenfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 621, "code": 773, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 622, "code": 624, "name": "Katastertechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 623, "code": 670, "name": "Ka<PERSON>le<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 624, "code": 780, "name": "Kaufmännische Angestellte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 625, "code": 627, "name": "Kautschuktechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 626, "code": 912, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 627, "code": 121, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 628, "code": 627, "name": "Keramiktechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 629, "code": 514, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 630, "code": 121, "name": "Ke<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 631, "code": 201, "name": "Kernmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 632, "code": 612, "name": "Kernphysik<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 633, "code": 626, "name": "Kerntechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 634, "code": 252, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 635, "code": 252, "name": "Kesselsch<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 636, "code": 549, "name": "Kesselwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 637, "code": 80, "name": "Kiesge<PERSON>ner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 638, "code": 863, "name": "Kindergartenleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 639, "code": 863, "name": "Kindergärtnerinnen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 640, "code": 923, "name": "Kindermädchen (nicht Erzieherin)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 641, "code": 867, "name": "Kinderpflegerinnen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 642, "code": 662, "name": "Kioskverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 643, "code": 305, "name": "Klavierstimmer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 644, "code": 482, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 645, "code": 838, "name": "Kleindarsteller", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 646, "code": 261, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 647, "code": 549, "name": "Klimaanlagenmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 648, "code": 549, "name": "Klimaanlagenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 649, "code": 349, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 650, "code": 411, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 651, "code": 317, "name": "Kommunikationselektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 652, "code": 838, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 653, "code": 831, "name": "Komponisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 654, "code": 549, "name": "Kompressormaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 655, "code": 392, "name": "Konditoren", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 656, "code": 661, "name": "Konditorwarenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 657, "code": 393, "name": "Konfektmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 658, "code": 255, "name": "Konstruktionsmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 659, "code": 641, "name": "Konstruktionszeichner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 660, "code": 780, "name": "Ko<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 661, "code": 401, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 662, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 663, "code": 185, "name": "Korkwarenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 664, "code": 171, "name": "Korrektoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 665, "code": 902, "name": "Kosmetikberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 666, "code": 902, "name": "Kosmetikerinnen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 667, "code": 633, "name": "Kosmetiklaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 668, "code": 835, "name": "Kostümbildner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 669, "code": 318, "name": "Kraftfahrzeugelektriker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 670, "code": 673, "name": "Kraftfahrzeugkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 671, "code": 281, "name": "Kraftfahrzeugs<PERSON><PERSON><PERSON>, -mechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 672, "code": 662, "name": "Kraftfahrzeugverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 673, "code": 549, "name": "Kraftmaschinenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 674, "code": 255, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 675, "code": 544, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 676, "code": 852, "name": "Krankengymnasten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 677, "code": 854, "name": "Krankenpflegehelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 678, "code": 853, "name": "Krankenschwestern, -pfleger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 679, "code": 53, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 680, "code": 354, "name": "Krawatt<PERSON>n<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 681, "code": 411, "name": "Küchenhilfen", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 682, "code": 266, "name": "Kühlanlageninstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 683, "code": 741, "name": "Kühlhausverwalter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 684, "code": 465, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 685, "code": 751, "name": "Kundendienstleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 686, "code": 131, "name": "Kunstaugenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 687, "code": 309, "name": "Kunstgewerbler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 688, "code": 485, "name": "<PERSON><PERSON>g<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 689, "code": 875, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 690, "code": 833, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 691, "code": 705, "name": "Künstlervermittler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 692, "code": 833, "name": "<PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 693, "code": 256, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 694, "code": 256, "name": "Kunstschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 695, "code": 153, "name": "Ku<PERSON>stoffbearbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 696, "code": 152, "name": "<PERSON><PERSON><PERSON><PERSON>-Form<PERSON>ber,- spritzer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 697, "code": 506, "name": "Kunststoffkonstruktionsbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 698, "code": 153, "name": "<PERSON><PERSON>stoffschloss<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 699, "code": 153, "name": "Kunststoffschweißer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 700, "code": 627, "name": "Kunststofftechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 701, "code": 150, "name": "Kunststoffverarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 702, "code": 153, "name": "Kunststoffwarenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 703, "code": 501, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 704, "code": 252, "name": "Kupferschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 705, "code": 378, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 706, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 707, "code": 792, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 708, "code": 894, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 709, "code": 714, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 710, "code": 612, "name": "Kybernetiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 711, "code": 633, "name": "Laboranten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 712, "code": 511, "name": "<PERSON><PERSON><PERSON>  (Ausbau)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 713, "code": 633, "name": "Lacklaboranten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 714, "code": 626, "name": "Lacktechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 715, "code": 744, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 716, "code": 741, "name": "Lagerverwalter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 717, "code": 13, "name": "Landarbeitsaufseher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 718, "code": 13, "name": "Landarbeitskräfte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 719, "code": 13, "name": "Landmaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 720, "code": 282, "name": "Landmaschinenmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 721, "code": 761, "name": "Landräte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 722, "code": 52, "name": "Landschaftsarchitekten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 723, "code": 51, "name": "Landschaftsgärtner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 724, "code": 11, "name": "Landwirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 725, "code": 32, "name": "Landwirtschaftliche Berater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 726, "code": 631, "name": "Landwirtschaftlich-technische Sonderfachk", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 727, "code": 834, "name": "Layouter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 728, "code": 705, "name": "Leasing-Kaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 729, "code": 633, "name": "Lebensmittellaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 730, "code": 627, "name": "Lebensmitteltechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 731, "code": 376, "name": "Lederbekleidungshersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 732, "code": 371, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 733, "code": 376, "name": "<PERSON><PERSON><PERSON><PERSON>, -stanzer, -stepper, -verar<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 734, "code": 627, "name": "Ledertechnik<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 735, "code": 376, "name": "Lederware<PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 736, "code": 870, "name": "Lehramtsanwärter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 737, "code": 870, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 738, "code": 874, "name": "Lehrer an berufsbildenden Schulen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 739, "code": 805, "name": "Leichenbestatter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 740, "code": 506, "name": "Leichtflugzeugbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 741, "code": 311, "name": "Leitungsmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 742, "code": 821, "name": "Lektoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 743, "code": 839, "name": "Lichtreklamehersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 744, "code": 171, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 745, "code": 423, "name": "Lim<PERSON>denber<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 746, "code": 349, "name": "<PERSON><PERSON><PERSON>flechter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 747, "code": 756, "name": "Logis<PERSON>ker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 748, "code": 273, "name": "Lokomotivschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 749, "code": 245, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 750, "code": 721, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 751, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 752, "code": 484, "name": "Luftheizungsbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 753, "code": 549, "name": "Lüftungsanlagenmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 754, "code": 268, "name": "Lüftungsinstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 755, "code": 549, "name": "Lüftungswärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 756, "code": 701, "name": "Luftverkehrskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 757, "code": 741, "name": "Magaziner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 758, "code": 704, "name": "Makler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 759, "code": 511, "name": "<PERSON><PERSON>  (Ausbau)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 760, "code": 421, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 761, "code": 750, "name": "Manager", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 762, "code": 931, "name": "Mangler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 763, "code": 838, "name": "Mannequins", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 764, "code": 435, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 765, "code": 757, "name": "Marketingberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 766, "code": 755, "name": "Marketingfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 767, "code": 887, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 768, "code": 344, "name": "Maschenwarenfertiger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 769, "code": 601, "name": "Maschinenbauingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 770, "code": 273, "name": "Maschinenbaumechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 771, "code": 651, "name": "Maschinenbaumeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 772, "code": 621, "name": "Maschinenbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 773, "code": 550, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 774, "code": 201, "name": "Maschinenformer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 775, "code": 549, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 776, "code": 131, "name": "Maschinenhohlglaswarenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 777, "code": 937, "name": "Maschinenreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 778, "code": 273, "name": "Ma<PERSON><PERSON>sch<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 779, "code": 782, "name": "Maschinenschreiber", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 780, "code": 171, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 781, "code": 359, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 782, "code": 549, "name": "Maschinenwarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 783, "code": 540, "name": "Maschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 784, "code": 835, "name": "Maskenbildner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 785, "code": 852, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 786, "code": 372, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 787, "code": 612, "name": "Mathematiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 788, "code": 492, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 789, "code": 292, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 790, "code": 723, "name": "<PERSON><PERSON><PERSON>  (Küsten-, Seeschiffahrt)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 791, "code": 724, "name": "Mat<PERSON>n (Binnenschiffahrt)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 792, "code": 441, "name": "Maurer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 793, "code": 441, "name": "Ma<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 794, "code": 852, "name": "Medizinische Bademeister", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 795, "code": 857, "name": "Medizinisch-technische Assistenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 796, "code": 23, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 797, "code": 287, "name": "Messerschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 798, "code": 300, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 799, "code": 622, "name": "Meßtechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 800, "code": 292, "name": "Meßwerkzeugbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 801, "code": 323, "name": "Metallar<PERSON>ter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 802, "code": 287, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 803, "code": 234, "name": "Metallbeizer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 804, "code": 213, "name": "<PERSON><PERSON><PERSON><PERSON>, -dr<PERSON><PERSON>, -<PERSON>r<PERSON><PERSON>, -sch<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 805, "code": 305, "name": "Metallblasinstrumentenmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 806, "code": 234, "name": "Metallf<PERSON>rber", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 807, "code": 295, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 808, "code": 295, "name": "Metallfeinwerkzeugbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 809, "code": 283, "name": "Metallflugzeugbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 810, "code": 201, "name": "<PERSON><PERSON><PERSON><PERSON>, -gie<PERSON>r", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 811, "code": 276, "name": "Metallgewebemacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 812, "code": 245, "name": "Metallkleber", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 813, "code": 235, "name": "Metalloberflächenveredler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 814, "code": 235, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -s<PERSON><PERSON><PERSON>, -s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 815, "code": 231, "name": "Metallpolierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 816, "code": 633, "name": "Metallprüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 817, "code": 229, "name": "Metallsäger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 818, "code": 225, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 819, "code": 233, "name": "Metallvergüter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 820, "code": 323, "name": "Metallwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 821, "code": 194, "name": "Metallzieher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 822, "code": 883, "name": "Meteorologen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 823, "code": 401, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 824, "code": 431, "name": "Milch(produkte)bereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 825, "code": 631, "name": "Milchwirtschaftliche Laboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 826, "code": 80, "name": "<PERSON><PERSON>au<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 827, "code": 423, "name": "Mineralwasserbereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 828, "code": 761, "name": "Minister", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 829, "code": 673, "name": "Möbelkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 830, "code": 512, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 831, "code": 743, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 832, "code": 492, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 833, "code": 501, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 834, "code": 743, "name": "Möbeltransporteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 835, "code": 662, "name": "Möbelverk<PERSON><PERSON><PERSON>, -kauf<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 836, "code": 676, "name": "Modedirektricen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 837, "code": 502, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 838, "code": 834, "name": "Modelleure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 839, "code": 502, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 840, "code": 351, "name": "Modellschneider", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 841, "code": 502, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 842, "code": 354, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 843, "code": 431, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 844, "code": 431, "name": "Molkereifachleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 845, "code": 501, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 846, "code": 269, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 847, "code": 483, "name": "<PERSON><PERSON>kle<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 848, "code": 273, "name": "Motorenschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 849, "code": 273, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 850, "code": 435, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 851, "code": 823, "name": "Museumsfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 852, "code": 674, "name": "Musikalienh<PERSON>nd<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 853, "code": 831, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 854, "code": 305, "name": "Musikinstrumentenbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 855, "code": 875, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 856, "code": 341, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 857, "code": 354, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 858, "code": 316, "name": "Nachrichtengerätemechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 859, "code": 622, "name": "Nachrichtentechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 860, "code": 212, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 861, "code": 352, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 862, "code": 300, "name": "Nähmaschinenmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 863, "code": 435, "name": "Nährmittelhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 864, "code": 423, "name": "Nahrungs- und Genußmittelkoster", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 865, "code": 627, "name": "Nahrungsmitteltechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 866, "code": 661, "name": "Nahrungsmittelverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 867, "code": 883, "name": "Naturwissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 868, "code": 550, "name": "NC-Maschineneinrichter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 869, "code": 349, "name": "<PERSON>z<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 870, "code": 245, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 871, "code": 813, "name": "Notare", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 872, "code": 786, "name": "Notargehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 873, "code": 352, "name": "Oberbekleidungsnäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 874, "code": 351, "name": "Oberbekleidungsschneider", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 875, "code": 11, "name": "Obstbauern", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 876, "code": 435, "name": "Obstkonservierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 877, "code": 661, "name": "Obstverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 878, "code": 714, "name": "Omnibusfahrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 879, "code": 715, "name": "Omnibusverkehrsunternehmer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 880, "code": 778, "name": "Operatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 881, "code": 300, "name": "Optikgerätemechaniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 882, "code": 894, "name": "Ordensangehörige", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 883, "code": 792, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 884, "code": 756, "name": "Organisatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 885, "code": 307, "name": "Orthopädiemechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 886, "code": 372, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 887, "code": 885, "name": "Pädagogen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 888, "code": 873, "name": "Pädagogische Assistenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 889, "code": 161, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 890, "code": 627, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 891, "code": 164, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 892, "code": 164, "name": "<PERSON><PERSON><PERSON><PERSON>nh<PERSON>tel<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 893, "code": 491, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 894, "code": 763, "name": "Parteifunktionäre, -sekret<PERSON>re", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 895, "code": 813, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -ingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 896, "code": 392, "name": "Patissiers", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 897, "code": 378, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 898, "code": 378, "name": "Pelzwerker, -n<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 899, "code": 998, "name": "Pensionäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 900, "code": 757, "name": "Personalberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 901, "code": 751, "name": "Personalleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 902, "code": 763, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 903, "code": 879, "name": "Personaltrainer (nicht EDV)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 904, "code": 791, "name": "Personenschutzfachkräfte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 905, "code": 901, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 906, "code": 891, "name": "<PERSON><PERSON><PERSON>, Pastor", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 907, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,-wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 908, "code": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 909, "code": 11, "name": "Pflanzenzüchter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 910, "code": 461, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 911, "code": 793, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 912, "code": 689, "name": "Pharmaberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 913, "code": 141, "name": "Pharmakan<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 914, "code": 633, "name": "Pharmalaboranten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 915, "code": 858, "name": "Pharmazeutisch-technische Assistenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 916, "code": 782, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 917, "code": 612, "name": "<PERSON>ys<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 918, "code": 632, "name": "Physiklaboranten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 919, "code": 626, "name": "Physikotechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 920, "code": 852, "name": "Physiotherapeuten (nicht Ärzte)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 921, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 922, "code": 839, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 923, "code": 545, "name": "Planierraupenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 924, "code": 483, "name": "Plattenleg<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 925, "code": 931, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 926, "code": 792, "name": "Platzanweiser", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 927, "code": 794, "name": "Platzwarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 928, "code": 361, "name": "Plisseebrenner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 929, "code": 801, "name": "Polizeibedienstete", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 930, "code": 492, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 931, "code": 544, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 932, "code": 121, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 933, "code": 514, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 934, "code": 732, "name": "Postbeamte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 935, "code": 731, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 936, "code": 732, "name": "Posthelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 937, "code": 732, "name": "Postzusteller", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 938, "code": 294, "name": "Prägewalzengraveure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 939, "code": 309, "name": "Präparatoren", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 940, "code": 761, "name": "Präsidenten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 941, "code": 546, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 942, "code": 891, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 943, "code": 751, "name": "Produktionsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 944, "code": 276, "name": "Produktionstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 945, "code": 755, "name": "Produktmanager", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 946, "code": 835, "name": "<PERSON><PERSON><PERSON><PERSON>, Produktionsleiter (Film)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 947, "code": 775, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 948, "code": 316, "name": "Prozeßleiterelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 949, "code": 316, "name": "Prüffeldelektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 950, "code": 622, "name": "Prüffeldmeßtechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 951, "code": 311, "name": "Prüffeldmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 952, "code": 886, "name": "Psychologen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 953, "code": 821, "name": "Publizisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 954, "code": 549, "name": "Pumpenmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 955, "code": 549, "name": "Pumpenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 956, "code": 309, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 957, "code": 224, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 958, "code": 673, "name": "Radiofachkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 959, "code": 315, "name": "Radiotechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 960, "code": 466, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 961, "code": 546, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 962, "code": 712, "name": "Rangierpersonal", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 963, "code": 491, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 964, "code": 836, "name": "Raumgestalter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 965, "code": 934, "name": "<PERSON><PERSON><PERSON><PERSON>, -re<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 966, "code": 609, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 967, "code": 873, "name": "Realschullehrer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 968, "code": 12, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 969, "code": 778, "name": "Rechenzentrumsfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 970, "code": 771, "name": "Re<PERSON><PERSON>ngspr<PERSON>fer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 971, "code": 751, "name": "Rechnungswesenleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 972, "code": 813, "name": "Rechtsanwälte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 973, "code": 786, "name": "Rechtsanwaltsgehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 974, "code": 813, "name": "Rechtsbeistände", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 975, "code": 812, "name": "Rechtspfleger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 976, "code": 821, "name": "Redakteure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 977, "code": 701, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 978, "code": 488, "name": "<PERSON>et(dach)decker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 979, "code": 607, "name": "REFA-Ingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 980, "code": 628, "name": "REFA-Techniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 981, "code": 675, "name": "Reformhauskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 982, "code": 622, "name": "Regeltechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 983, "code": 609, "name": "Regionalplaner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 984, "code": 832, "name": "Regisseure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 985, "code": 145, "name": "<PERSON><PERSON>nma<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 986, "code": 702, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 987, "code": 702, "name": "Reiseverkehrskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 988, "code": 876, "name": "Reit- und Fahrlehrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 989, "code": 870, "name": "Rektoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 990, "code": 874, "name": "Rektoren an berufsbildenden Schulen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 991, "code": 873, "name": "Rektoren an Grund-, Haupt-, Real- und Son", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 992, "code": 872, "name": "Rektoren an Gymnasien", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 993, "code": 998, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 994, "code": 274, "name": "Reparaturschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 995, "code": 689, "name": "Repräsentanten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 996, "code": 176, "name": "Reprographen", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 997, "code": 172, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 998, "code": 912, "name": "Restaurantfachleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 999, "code": 833, "name": "Restauratoren (Gemälde, Skulpturen)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1000, "code": 61, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1001, "code": 811, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1002, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,-wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1003, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1004, "code": 264, "name": "Rohrinstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1005, "code": 264, "name": "Rohrleitungsbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1006, "code": 264, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1007, "code": 937, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1008, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1009, "code": 259, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1010, "code": 821, "name": "Rundfunksprecher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1011, "code": 11, "name": "Saatzüchter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1012, "code": 181, "name": "Sägewerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1013, "code": 80, "name": "Salzbe-,verarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1014, "code": 80, "name": "Sandge<PERSON>ner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1015, "code": 832, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1016, "code": 854, "name": "Sanitäter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1017, "code": 374, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1018, "code": 867, "name": "Säuglingspflegerinnen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1019, "code": 292, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1020, "code": 466, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1021, "code": 460, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1022, "code": 805, "name": "Schädlingsbekämpfer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1023, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1024, "code": 311, "name": "Schaltanlagenmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1025, "code": 549, "name": "Schaltanlagenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1026, "code": 832, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1027, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1028, "code": 295, "name": "Scherenmonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1029, "code": 651, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1030, "code": 488, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1031, "code": 711, "name": "Sc<PERSON>enenfahrzeugführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1032, "code": 506, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1033, "code": 621, "name": "Schiffbautechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1034, "code": 936, "name": "Schiffs(wand)reiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1035, "code": 723, "name": "Schiffsbetriebsmeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1036, "code": 274, "name": "Schiffsbetriebsschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1037, "code": 721, "name": "Schiffsbetriebstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1038, "code": 724, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1039, "code": 735, "name": "Schiffsfunker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1040, "code": 721, "name": "Schiffsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1041, "code": 411, "name": "Schiffsköche", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1042, "code": 704, "name": "Schiffsmakler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1043, "code": 721, "name": "Schiffsmaschinenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1044, "code": 721, "name": "Schiffsmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1045, "code": 723, "name": "Schiffsmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1046, "code": 721, "name": "Schiffsoffiziere", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1047, "code": 912, "name": "Schiffsstewards", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1048, "code": 506, "name": "Schiffszimmerer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1049, "code": 839, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1050, "code": 839, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1051, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1052, "code": 358, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1053, "code": 401, "name": "Schlachter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1054, "code": 923, "name": "Schlafwagenschaffner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1055, "code": 145, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1056, "code": 225, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1057, "code": 465, "name": "Schleusenwärter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1058, "code": 254, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1059, "code": 191, "name": "Schmelzofenwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1060, "code": 287, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1061, "code": 302, "name": "<PERSON><PERSON><PERSON><PERSON>fasser", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1062, "code": 349, "name": "Schmucktextilienhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1063, "code": 673, "name": "Schmuckwarenkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1064, "code": 662, "name": "Schmuckwarenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1065, "code": 351, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1066, "code": 292, "name": "Schnittwerkzeugmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1067, "code": 185, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1068, "code": 393, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1069, "code": 441, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1070, "code": 804, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -meister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1071, "code": 201, "name": "Schriftgießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1072, "code": 171, "name": "Schriftsetzer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1073, "code": 821, "name": "Schriftsteller", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1074, "code": 373, "name": "Schuhfabrikarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1075, "code": 373, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON>, -stepper", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1076, "code": 372, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1077, "code": 373, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1078, "code": 999, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1079, "code": 870, "name": "Sc<PERSON><PERSON>iter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1080, "code": 874, "name": "Schulleiter an berufsbildenden Schulen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1081, "code": 873, "name": "Schulleiter an Grund-, Haupt-, Real- und S", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1082, "code": 872, "name": "Schulleiter an Gymnasien", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1083, "code": 886, "name": "Schulpsychologen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1084, "code": 461, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1085, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,-wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1086, "code": 241, "name": "Sch<PERSON><PERSON>r", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1087, "code": 795, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1088, "code": 795, "name": "Schwimmeistergehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1089, "code": 544, "name": "Schwimmkranmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1090, "code": 521, "name": "Seegüterkontrolleure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1091, "code": 891, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1092, "code": 358, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1093, "code": 549, "name": "Seilbahnmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1094, "code": 332, "name": "Seiler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1095, "code": 789, "name": "Sekretäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1096, "code": 805, "name": "Sektionsgehilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1097, "code": 761, "name": "Senatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1098, "code": 791, "name": "Sicherheitsbedienstete", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1099, "code": 608, "name": "Sicherheitsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1100, "code": 803, "name": "Sicherheitskontrolleure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1101, "code": 629, "name": "Sicherheitstechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1102, "code": 175, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1103, "code": 302, "name": "Silberschmiede", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1104, "code": 741, "name": "Siloverwalter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1105, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1106, "code": 775, "name": "<PERSON>entwick<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1107, "code": 801, "name": "Soldaten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1108, "code": 714, "name": "Sonderfahrzeugführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1109, "code": 873, "name": "<PERSON><PERSON>chu<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1110, "code": 523, "name": "Sortiermaschinenbediener", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1111, "code": 861, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1112, "code": 869, "name": "Sozialberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1113, "code": 861, "name": "Sozialpädagoge", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1114, "code": 884, "name": "Sozialwissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1115, "code": 691, "name": "Sparkassenfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1116, "code": 701, "name": "Spediteure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1117, "code": 701, "name": "Speditionskaufleute, -sach<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1118, "code": 393, "name": "Speiseeishersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1119, "code": 435, "name": "S<PERSON>isefettherstel<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1120, "code": 308, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1121, "code": 792, "name": "Spielhallena<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1122, "code": 309, "name": "S<PERSON>ltiermacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1123, "code": 331, "name": "Spinner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1124, "code": 331, "name": "Spinnfaservorbereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1125, "code": 506, "name": "Sportger<PERSON><PERSON>auer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1126, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1127, "code": 884, "name": "Sportwissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1128, "code": 862, "name": "Sprachheilpädagogen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1129, "code": 856, "name": "Sprechstundenhelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1130, "code": 460, "name": "Sprengmeister", "gefahrengruppe": "Gefahrengruppe-N"}, {"id": 1131, "code": 460, "name": "Sprengmeisterhelfer", "gefahrengruppe": "Gefahrengruppe-N"}, {"id": 1132, "code": 212, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1133, "code": 332, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1134, "code": 811, "name": "Staatsanwälte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1135, "code": 609, "name": "Stadtplaner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1136, "code": 761, "name": "Stadträte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1137, "code": 255, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1138, "code": 442, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1139, "code": 293, "name": "Stahlformenbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1140, "code": 294, "name": "Stahlgraveure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1141, "code": 265, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1142, "code": 194, "name": "Stahlschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1143, "code": 164, "name": "Stahlstichpräger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1144, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1145, "code": 211, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1146, "code": 292, "name": "Stanzwerkzeugmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1147, "code": 603, "name": "<PERSON>ati<PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1148, "code": 838, "name": "Statisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1149, "code": 887, "name": "Statistiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1150, "code": 743, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1151, "code": 625, "name": "Steiger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1152, "code": 101, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1153, "code": 80, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1154, "code": 101, "name": "<PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1155, "code": 101, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1156, "code": 461, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1157, "code": 627, "name": "Steintechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1158, "code": 506, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1159, "code": 712, "name": "Stellwerkspersonal", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1160, "code": 173, "name": "Stempelmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1161, "code": 782, "name": "<PERSON><PERSON><PERSON><PERSON>, -typisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1162, "code": 782, "name": "Stenokontoristen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1163, "code": 753, "name": "Steuerberater, -bevollmächtigte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1164, "code": 754, "name": "Steuerfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1165, "code": 753, "name": "<PERSON><PERSON>erpr<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1166, "code": 359, "name": "<PERSON>er", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1167, "code": 461, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1168, "code": 461, "name": "Straßen<PERSON>uhelf<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1169, "code": 546, "name": "St<PERSON><PERSON><PERSON>baumaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1170, "code": 461, "name": "Straßenbaupoliere, -meister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1171, "code": 716, "name": "Straßenmeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1172, "code": 461, "name": "Straßenpflasterer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1173, "code": 461, "name": "Straßenunterhaltungsarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1174, "code": 281, "name": "Straßenwachtfahrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1175, "code": 716, "name": "Straßenwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1176, "code": 716, "name": "Streckenwarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1177, "code": 185, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1178, "code": 354, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1179, "code": 354, "name": "Strumpfnäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1180, "code": 999, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1181, "code": 481, "name": "Stukkateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1182, "code": 421, "name": "Sudmeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1183, "code": 423, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1184, "code": 813, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1185, "code": 776, "name": "Systemanalytiker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1186, "code": 775, "name": "Systemsoftwareentwickler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1187, "code": 424, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1188, "code": 424, "name": "Tabakfermentierer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1189, "code": 424, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1190, "code": 869, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -v<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1191, "code": 686, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1192, "code": 686, "name": "Tankwarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1193, "code": 832, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1194, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1195, "code": 175, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1196, "code": 491, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1197, "code": 803, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-N"}, {"id": 1198, "code": 705, "name": "Taxatoren", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1199, "code": 714, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1200, "code": 715, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1201, "code": 620, "name": "Techniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1202, "code": 835, "name": "Technische Bühnen-, Studioleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1203, "code": 785, "name": "Technische Kaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1204, "code": 608, "name": "Technische Sachverständige", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1205, "code": 721, "name": "Technische Schiffsoffiziere", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1206, "code": 641, "name": "Technische Zeichner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1207, "code": 423, "name": "Teebereiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1208, "code": 735, "name": "Telefonisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1209, "code": 735, "name": "Telegraphisten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1210, "code": 312, "name": "Telekommunikationselektroniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1211, "code": 876, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1212, "code": 341, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1213, "code": 24, "name": "Terrarienwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1214, "code": 112, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1215, "code": 486, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1216, "code": 175, "name": "Textildrucker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1217, "code": 673, "name": "Textilkaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1218, "code": 633, "name": "Textillaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1219, "code": 341, "name": "Textilmas<PERSON>enführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1220, "code": 341, "name": "Textilmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1221, "code": 834, "name": "Textilmustergestalter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1222, "code": 358, "name": "Textilnäher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1223, "code": 931, "name": "Textilreiniger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1224, "code": 359, "name": "Textils<PERSON><PERSON>ckmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1225, "code": 341, "name": "Textilstopfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1226, "code": 627, "name": "Textiltechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1227, "code": 359, "name": "Textilverarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1228, "code": 361, "name": "<PERSON>il<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1229, "code": 349, "name": "Textilverflechter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1230, "code": 662, "name": "Textilverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1231, "code": 782, "name": "Textverarbeitungsfachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1232, "code": 549, "name": "Theatermaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1233, "code": 859, "name": "Therapeuten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1234, "code": 460, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1235, "code": 546, "name": "<PERSON><PERSON><PERSON>aschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1236, "code": 460, "name": "Tiefbaupoliere", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1237, "code": 460, "name": "Tiefbauvorar<PERSON>ter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1238, "code": 460, "name": "Tiefbauwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1239, "code": 80, "name": "Tiefbohrfacharbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1240, "code": 174, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1241, "code": 843, "name": "Tierärzte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1242, "code": 856, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1243, "code": 805, "name": "Tierkörperverwerter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1244, "code": 24, "name": "<PERSON><PERSON><PERSON><PERSON> (nicht Artisten)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1245, "code": 24, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1246, "code": 23, "name": "<PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1247, "code": 32, "name": "Tierzuchtberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1248, "code": 501, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1249, "code": 835, "name": "<PERSON><PERSON><PERSON><PERSON>, -techniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1250, "code": 121, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1251, "code": 80, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1252, "code": 313, "name": "Transformatorenbauer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1253, "code": 744, "name": "Transportarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1254, "code": 742, "name": "Transport<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1255, "code": 487, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Holz)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1256, "code": 265, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1257, "code": 757, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1258, "code": 711, "name": "Triebfahrzeugführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1259, "code": 482, "name": "Trockenbaumonteure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1260, "code": 511, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1261, "code": 273, "name": "Turbinenschlosser", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1262, "code": 822, "name": "<PERSON>bers<PERSON>zer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1263, "code": 308, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -reparateure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1264, "code": 308, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1265, "code": 803, "name": "Umweltschutzbeauftragte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1266, "code": 608, "name": "Umweltschutzingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1267, "code": 629, "name": "Umweltschutztechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1268, "code": 883, "name": "Umweltwissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1269, "code": 233, "name": "Universalhärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1270, "code": 757, "name": "Unternehmensberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1271, "code": 750, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1272, "code": 763, "name": "Verbandsfunktionäre, -sekretäre", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1273, "code": 763, "name": "Verbandsleiter, -geschäftsführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1274, "code": 921, "name": "Verbraucherberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1275, "code": 611, "name": "Verfahrensingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1276, "code": 191, "name": "Verfahrensmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1277, "code": 441, "name": "Verfuger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1278, "code": 512, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1279, "code": 660, "name": "Verkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1280, "code": 687, "name": "Verkaufs-, Vertriebsbeauftragte (nicht EDV", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1281, "code": 660, "name": "Verkaufsberater, -aufsichten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1282, "code": 663, "name": "Verkaufsfahrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1283, "code": 703, "name": "Verkaufsförderer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1284, "code": 660, "name": "Verkaufshilfen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1285, "code": 751, "name": "Verkaufsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1286, "code": 678, "name": "Verkaufssachbearbeiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1287, "code": 878, "name": "Verkehrserzieher", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1288, "code": 702, "name": "Verkehrsfachleute (Personen-,Fremdenver", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1289, "code": 701, "name": "Verkehrskaufleute (Güterverkehr)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1290, "code": 702, "name": "Verkehrslotsen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1291, "code": 683, "name": "Verlagskaufleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1292, "code": 683, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1293, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1294, "code": 604, "name": "Vermessungsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1295, "code": 624, "name": "Vermessungstechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1296, "code": 642, "name": "Vermessungszeichner", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1297, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1298, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1299, "code": 162, "name": "Verpackungsmittelhersteller", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1300, "code": 162, "name": "Verpackungsmittelmaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1301, "code": 162, "name": "Verpackungsmittelmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1302, "code": 481, "name": "Verputzer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1303, "code": 522, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1304, "code": 695, "name": "Versicherungsfachleute (nicht gesetzl. Vers", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1305, "code": 704, "name": "Versicherungsmakler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1306, "code": 695, "name": "Versicherungssachbearbeiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1307, "code": 621, "name": "Versorgungstechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1308, "code": 705, "name": "Versteigerer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1309, "code": 689, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1310, "code": 757, "name": "Vertriebsberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1311, "code": 751, "name": "Vertriebsleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1312, "code": 678, "name": "Vertriebssachbearbeiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1313, "code": 755, "name": "Vertriebswegefachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1314, "code": 176, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1315, "code": 31, "name": "Verwalter in der Land- und Tierwirtschaft", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1316, "code": 765, "name": "Verwaltungsfachleute (gehobener Dienst)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1317, "code": 764, "name": "Verwaltungsfachleute (höherer Dienst)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1318, "code": 787, "name": "Verwaltungsfachleute (mittlerer Dienst)", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1319, "code": 857, "name": "Veterinärmedizinisch-technische Assistente", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1320, "code": 837, "name": "Videografen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1321, "code": 634, "name": "Videolaboranten", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1322, "code": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1323, "code": 902, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1324, "code": 814, "name": "Vollstreckungsbedienstete", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1325, "code": 814, "name": "Vollzugsbedienstete", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1326, "code": 331, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1327, "code": 145, "name": "Vulkaniseure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1328, "code": 791, "name": "Wachbedienstete", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1329, "code": 792, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1330, "code": 287, "name": "Wagenschmiede", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1331, "code": 506, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1332, "code": 838, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1333, "code": 62, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1334, "code": 62, "name": "Waldfacha<PERSON>eiter (Forstwirte)", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1335, "code": 605, "name": "Walzwerksingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1336, "code": 522, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, -<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1337, "code": 522, "name": "Warenaufmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1338, "code": 744, "name": "Warenausträger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1339, "code": 521, "name": "Wareneingangs-, -ausgangskontrolleure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1340, "code": 522, "name": "Warenkennzeichner", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1341, "code": 512, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, -maler", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1342, "code": 521, "name": "Warenprüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1343, "code": 523, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1344, "code": 482, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1345, "code": 194, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1346, "code": 353, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1347, "code": 931, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1348, "code": 353, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ider, Wäscheschneider", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1349, "code": 465, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1350, "code": 267, "name": "Wasserinstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1351, "code": 541, "name": "Wasserkraftwerkmaschinisten", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1352, "code": 876, "name": "Wassersportlehrer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1353, "code": 465, "name": "Wasserstraßenverkehrswarte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1354, "code": 549, "name": "Wasserwärter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1355, "code": 341, "name": "<PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1356, "code": 341, "name": "<PERSON>vorber<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1357, "code": 32, "name": "Weinbauberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1358, "code": 13, "name": "Weinbergsarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1359, "code": 423, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1360, "code": 703, "name": "Werbeberater", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1361, "code": 703, "name": "Werbefachleute", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1362, "code": 309, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1363, "code": 703, "name": "Werbeleiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1364, "code": 839, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1365, "code": 255, "name": "Werftarbeiter", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1366, "code": 802, "name": "Werkfeuerwehrleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1367, "code": 629, "name": "Werkführer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1368, "code": 874, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1369, "code": 651, "name": "Werkmeister", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1370, "code": 791, "name": "Werkschutzfachkräfte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1371, "code": 874, "name": "Werkstattlehrer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1372, "code": 608, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1373, "code": 632, "name": "Werkstoffpüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1374, "code": 626, "name": "Werkstofftechniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1375, "code": 292, "name": "Werkzeugmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1376, "code": 705, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1377, "code": 61, "name": "Wildheger", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1378, "code": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,-wirte", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1379, "code": 12, "name": "<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1380, "code": 607, "name": "Wirtschaftsingenieure", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1381, "code": 753, "name": "Wirtschaftsprüfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1382, "code": 628, "name": "Wirtschaftstechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1383, "code": 881, "name": "Wirtschaftswissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1384, "code": 880, "name": "Wissenschaftler", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1385, "code": 880, "name": "Wissenschaftliche Mitarbeiter", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1386, "code": 661, "name": "Wurstwarenverkäufer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1387, "code": 706, "name": "<PERSON><PERSON>hler<PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1388, "code": 300, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1389, "code": 842, "name": "Zahnärzte", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1390, "code": 856, "name": "Zahnarzthelfer", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1391, "code": 303, "name": "Zahntechniker", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1392, "code": 802, "name": "Zechenfeuerwehrleute", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1393, "code": 875, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1394, "code": 834, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1395, "code": 744, "name": "Zeitungsausträger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1396, "code": 161, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1397, "code": 80, "name": "Zementwerker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1398, "code": 268, "name": "Zentralheizungsinstallateure", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1399, "code": 220, "name": "Zerspanungsmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1400, "code": 488, "name": "Ziegeldecker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1401, "code": 292, "name": "Ziehwerkzeugmacher", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1402, "code": 487, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1403, "code": 923, "name": "Zimmermädchen", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1404, "code": 201, "name": "Zinngießer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1405, "code": 294, "name": "<PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1406, "code": 994, "name": "Zivildienstleistende", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1407, "code": 393, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1408, "code": 712, "name": "Zugabfertiger", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1409, "code": 712, "name": "Zugbegleitpersonal", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1410, "code": 714, "name": "Zugmaschinenführer", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1411, "code": 351, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-A"}, {"id": 1412, "code": 281, "name": "Zweiradmechaniker", "gefahrengruppe": "Gefahrengruppe-B"}, {"id": 1413, "code": 332, "name": "<PERSON><PERSON><PERSON>", "gefahrengruppe": "Gefahrengruppe-B"}]