import crypto from "crypto";
import { v4 as uuidv4 } from 'uuid';

const contractMap: { [key: string]: number } = {
    hausrat: 10,
    wohnge<PERSON><PERSON><PERSON>: 11,
    tierhalterhaftpflicht: 12,
    privathaftpflicht: 13,
    haus_und_grundbesitzerhaftpflicht: 14,
    bauleistung: 15,
    bau<PERSON>renhaftpflicht: 16,
    geschaeftsversicherung: 17,
    gebaeudeversicherung: 18,
    betriebshaftpflicht: 19,
    unfallversicherung: 20,
};

const BLACKLIST = new Set(["CIF"]);

/**
 * Generate a secure customer number with a salted input.
 * The result is in the format: 2 letters followed by 6 numbers.
 * 
 * @param input - The unique input (e.g., email, username).
 * @returns A customer number in the format "XXX123456".
 */
export function generateCustomerNumber(inputStr: string): string {
    if (!inputStr) {
        throw new Error("input is required.");
    }

    // Generate a UUID for added randomness
    const uniqueSalt = uuidv4().replace(/-/g, "");  // Generates a 32-character hex string without dashes

    // Combine input with the current timestamp and unique salt
    const currentTime = Date.now();  // Milliseconds since epoch
    const saltedInput = `${inputStr}${currentTime}${process.env.NUMBER_GENERATOR_SALT}${uniqueSalt}`;

    // Create SHA-256 hash of the salted input
    const hashDigest = crypto.createHash('sha256').update(saltedInput).digest('hex');

    // Map the UUID to A-Z letters (covering the full alphabet instead of just A-F)
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const uuidBasedLetters = uniqueSalt
        .slice(0, 6)
        .split('')
        .map(char => alphabet[parseInt(char, 16) % 26])
        .join('');

    // Extract numeric values from the hash
    let hashNumericOnly = hashDigest.replace(/\D/g, "");  // Remove non-numeric characters
    if (hashNumericOnly.length < 6) {
        hashNumericOnly += Array.from({ length: 6 - hashNumericOnly.length }, () =>
            Math.floor(Math.random() * 10).toString()
        ).join('');  // Ensure at least 6 digits
    }
    const numbers = hashNumericOnly.slice(0, 6);  // Pick the first 6 digits

    // Combine letters and numbers to form the customer number
    return `${uuidBasedLetters.slice(0, 3)}${numbers}`;
}

/**
 * Generate a secure contract number with a salted input.
 * The result is in the format: 3 letters - contractMapNumber - yyyy + 8 numbers
 * 
 * e.g., "ASF-10-20240123"
 * 
 * @param contract_type - The type of contract, mapped to a number (contractMap).
 * @param inputStr - The unique input (e.g., email, username).
 * @returns A contract number, like "ABC-10-20240123".
 */
export function generateContractNumber(
  contract_type: string,
  inputStr: string
): string {
  if (!inputStr) {
    throw new Error(" input is required.");
  }

  // We'll regenerate the uniqueSalt until the 3-letter prefix is not blacklisted.
  let prefix: string;
  let uniqueSalt: string;

  do {
    // Generate a UUID for added randomness
    uniqueSalt = uuidv4().replace(/-/g, ""); // 32-char hex string

    // Create a 6-letter sequence from the uniqueSalt
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const uuidBasedLetters = uniqueSalt
      .slice(0, 6)
      .split('')
      .map(char => alphabet[parseInt(char, 16) % 26])
      .join('');

    // Grab the first 3 as our prefix
    prefix = uuidBasedLetters.slice(0, 3);
  } while (BLACKLIST.has(prefix)); 
  // If `prefix` is in the blacklist, generate a new `uniqueSalt` and try again.

  // Combine input with the salt, current timestamp, and UUID for uniqueness
  const currentTime = Date.now();  // in milliseconds
  const saltedInput = `${inputStr}${currentTime}${process.env.NUMBER_GENERATOR_SALT}${uniqueSalt}`;

  const year = new Date().getFullYear();

  // Map contract type to a number
  const contractTypeNumber = contractMap[contract_type] || "00";  // fallback

  // Generate SHA-256 hash of the salted input
  const hashDigest = crypto
    .createHash("sha256")
    .update(saltedInput)
    .digest("hex");

  // Extract numeric values from the hash (fallback to random digits if needed)
  let hashNumericOnly = hashDigest.replace(/\D/g, ""); // remove non-numeric
  if (hashNumericOnly.length < 8) {
    hashNumericOnly += Array.from({ length: 8 - hashNumericOnly.length }, () =>
      Math.floor(Math.random() * 10).toString()
    ).join(""); 
  }

  const numbers = Array.from({ length: 8 }, () =>
    hashNumericOnly[Math.floor(Math.random() * hashNumericOnly.length)]
  ).join("");

  // Final contract number
  return `${prefix}-${contractTypeNumber}-${year}${numbers}`;
}

/**
 * Generate a random report number.
 * The result is in the format: 2 letters followed by 6 numbers.
 * 
 * Example: ASF10-20240123-123456
 * 
 * @param customer_number - customer number
 * @returns A report number in the format "<cumstomer-number>-123456".
 */
export function generateReportNumber(customer_number: string): string {

    const random = Math.round(Math.random() * 1000000)
    
    // Combine the customer number and random number to form the report number
    return `${customer_number}-${random}`;
}

/**
 * Generate a secure agent number with a salted input.
 * The result is in the format: 2 letters followed by 4 numbers.
 * 
 * ASF-0123
 * 
 * @param input - The unique input (e.g., email, username).
 * @returns A agent number in the format "XXX-XXXX".
 */
export function generateAgentNumber(inputStr: string): string {
    if (!inputStr) {
        throw new Error("Input is required.");
    }

    // Generate a UUID for added randomness
    const uniqueSalt = uuidv4().replace(/-/g, ""); // Generates a 32-character hex string without dashes

    // Combine input with the current timestamp and unique salt
    const currentTime = Date.now(); // Milliseconds since epoch
    const saltedInput = `${inputStr}${currentTime}${process.env.NUMBER_GENERATOR_SALT}${uniqueSalt}`;

    // Create SHA-256 hash of the salted input
    const hashDigest = crypto.createHash('sha256').update(saltedInput).digest('hex');

    // Map the UUID to A-Z letters
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const uuidBasedLetters = uniqueSalt
        .slice(0, 6)
        .split('')
        .map((char) => alphabet[parseInt(char, 16) % 26])
        .join('');

    // Extract numeric values from the hash
    let hashNumericOnly = hashDigest.replace(/\D/g, ""); // Remove non-numeric characters
    if (hashNumericOnly.length < 4) {
        hashNumericOnly += Array.from({ length: 4 - hashNumericOnly.length }, () =>
            Math.floor(Math.random() * 10).toString()
        ).join(''); // Ensure at least 4 digits
    }
    const numbers = hashNumericOnly.slice(-4); // Take the last 4 digits

    // Combine letters and numbers to form the agent number
    return `${uuidBasedLetters.slice(0, 3)}-${numbers}`;
}

/**
 * Generate a secure agency number with a salted input.
 * By default, the result is in the format: 4 letters + "-" + 4 numbers, e.g., "ASF-0123".
 *
 * @param input - The unique input (e.g., email, username).
 * @returns A string such as "ABC-0123".
 */
export function generateAgencyNumber(input: string): string {
    if (!input) {
      throw new Error("Input is required.");
    }
  
    let prefix: string;
    let uniqueSalt: string;
  
    // Create a 4-letter prefix that isn’t blacklisted.
    do {
      // Generate a UUID and remove hyphens => 32-char hex string.
      uniqueSalt = uuidv4().replace(/-/g, "");
  
      // Convert first 6 hex chars to letters (A-Z).
      const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      const uuidBasedLetters = uniqueSalt
        .slice(0, 6)
        .split("")
        .map((char) => alphabet[parseInt(char, 16) % 26])
        .join("");
  
      // Take the first 4 letters as our prefix.
      prefix = uuidBasedLetters.slice(0, 4);
    } while (BLACKLIST.has(prefix)); // If blacklisted, regenerate.
  
    // Build a salted input using time, the environment salt, the uniqueSalt, etc.
    const time = Date.now();
    const environmentSalt = process.env.NUMBER_GENERATOR_SALT || "";
    const saltedInput = `${input}${time}${environmentSalt}${uniqueSalt}`;
  
    // Hash the salted input.
    const hashDigest = crypto
      .createHash("sha256")
      .update(saltedInput)
      .digest("hex");
  
    // Extract numeric values from the hash. If fewer than 4 exist, pad with random digits.
    let hashNumericOnly = hashDigest.replace(/\D/g, ""); // remove all non-numeric
    if (hashNumericOnly.length < 4) {
      hashNumericOnly += Array.from({ length: 4 - hashNumericOnly.length }, () =>
        Math.floor(Math.random() * 10).toString()
      ).join("");
    }
  
    // Pick 4 digits from the (expanded) numeric pool randomly.
    const numbers = Array.from({ length: 4 }, () =>
      hashNumericOnly[Math.floor(Math.random() * hashNumericOnly.length)]
    ).join("");
  
    // Return the final agency number, e.g. "ABC-0123".
    return `${prefix}-${numbers}`;
  }