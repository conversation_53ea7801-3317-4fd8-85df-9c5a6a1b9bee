import {
    AuthorType, EntityMapper, InsuranceCondition, ReportAttachment,
    ReportPrecheck, ReportPrecheckStatus,
    ReportPrecheckTemplate,
    ReportSummary,
    TimelineEntry,
    TimelineEntryType
} from "@/types";

export const timelineEntryMapper = {
    toDomain(apiValue: any): TimelineEntry {
        return {
            author_id: apiValue.author_id,
            report_number: apiValue.report_number,
            timestamp: apiValue.timestamp,
            related_entries: apiValue.related_entries,
            id: apiValue.documentId,
            entry_type: entryTypeMapper.toDomain(apiValue.entry_type),
            author_type: authorTypeMapper.toDomain(apiValue.author_type),
            title: richTextMapper.toDomain(apiValue.title || ''),
            content: apiValue.content ? richTextMapper.toDomain(apiValue.content) : undefined,
            metadata: apiValue.metadata ? richTextMapper.toDomain(apiValue.metadata) : undefined,
        }
    },
    toStorage(domainValue: TimelineEntry): any {
        return {
            author_id: domainValue.author_id,
            report_number: domainValue.report_number,
            timestamp: domainValue.timestamp,
            related_entries: domainValue.related_entries,
            entry_type: entryTypeMapper.toStorage(domainValue.entry_type),
            author_type: authorTypeMapper.toStorage(domainValue.author_type),
            title: richTextMapper.toStorage(domainValue.title),
            content: richTextMapper.toStorage(domainValue.content || ''),
            metadata: richTextMapper.toStorage(domainValue.metadata || ''),
        }
    },
}

export const reportSummaryMapper = {
    toDomain(apiValue: any): ReportSummary {
        return {
            id: apiValue.documentId,
            report_number: apiValue.report_number,
            author_type: authorTypeMapper.toDomain(apiValue.author_type),
            text: richTextMapper.toDomain(apiValue.text),
            timestamp: apiValue.timestamp,
        }
    },
    toStorage(domainValue: ReportSummary): any {
        return {
            report_number: domainValue.report_number,
            author_type: authorTypeMapper.toStorage(domainValue.author_type),
            text: richTextMapper.toStorage(domainValue.text),
            timestamp: domainValue.timestamp,
        }
    },
}

export const reportPrecheckMapper = {
    toDomain(apiValue: any): ReportPrecheck {
        return {
            id: apiValue.documentId,
            report_number: apiValue.report_number,
            author_type: authorTypeMapper.toDomain(apiValue.author_type),
            timestamp: apiValue.timestamp,
            status: reportPrecheckStatusMapper.toDomain(apiValue.precheck_status),
            tooltip: apiValue.tooltip,
            title: apiValue.title,
            index: apiValue.index,
            agent_number: apiValue.agent_number,
        }
    },
    toStorage({ id, author_type, status, ...domainFields }: ReportPrecheck): any {
        return {
            author_type: authorTypeMapper.toStorage(author_type),
            precheck_status: reportPrecheckStatusMapper.toStorage(status),
            ...domainFields
        }
    },
}

export const reportPrecheckTemplateMapper = {
    toDomain(apiValue: any): ReportPrecheckTemplate {
        return {
            id: apiValue.documentId,
            title: apiValue.title,
            index: apiValue.index,
            tooltip: apiValue.tooltip,
            insurance_type: apiValue.insurance_type,
        }
    },
    toStorage(domainValue: ReportPrecheckTemplate): any {
        return {
            title: domainValue.title,
            index: domainValue.index,
            tooltip: domainValue.tooltip,
            insurance_type: domainValue.insurance_type,
        }
    },
}

export const richTextMapper = {
    toDomain(apiValue: string): string {
        return apiValue.replaceAll('<br>', '\n\n')
    },
    toStorage(domainValue: string): string {
        return domainValue.replaceAll('\n\n', '<br>')
    },
}

export const reportPrecheckStatusMapper = {
    toDomain(apiValue: string): ReportPrecheckStatus {
        const domainValue: any = ReportPrecheckStatus[apiValue as any]
        if (domainValue === undefined) {
            throw new Error(`reportPrecheckStatusMapper.toDomain: ${apiValue} is not supported`);
        }

        return domainValue
    },
    toStorage(domainValue: ReportPrecheckStatus): string {
        const [apiValue] = Object.entries(ReportPrecheckStatus).find(
            ([key, value]) => value === domainValue
        )!

        return apiValue
    }
}

export const entryTypeMapper = {
    toDomain(apiValue: string): TimelineEntryType {
        const domainValue: any = TimelineEntryType[apiValue as any]
        if (domainValue === undefined) {
            throw new Error(`entryTypeMapper.toDomain: ${apiValue} is not supported`);
        }

        return domainValue
    },
    toStorage(domainValue: TimelineEntryType): string {
        const [apiValue] = Object.entries(TimelineEntryType).find(
            ([_, value]) => value === domainValue
        )!
        return apiValue
    }
}

export const authorTypeMapper = {
    toDomain(apiValue: string): AuthorType {
        const domainValue: any = AuthorType[apiValue as any]
        if (domainValue === undefined) {
            throw new Error(`authorTypeMapper.toDomain: ${apiValue} is not supported`);
        }

        return domainValue
    },
    toStorage(domainValue: AuthorType): string {
        const [apiValue] = Object.entries(AuthorType).find(
            ([key, value]) => value === domainValue
        )!
        return apiValue
    }
}

export const reportAttachmentMapper: EntityMapper<ReportAttachment> = {
    toDomain(apiValue) {
        return {
            id: apiValue.documentId,
            name: apiValue.name,
            bucket_path: apiValue.bucket_path,
            related_to: apiValue.related_to,
            related_entity_id: apiValue.related_entity_id,
        }
    },
    toStorage({ id, ...apiValue }) {
        return apiValue
    },
}

export const insuranceConditionMapper: EntityMapper<InsuranceCondition> = {
    toDomain({ file_name, documentId, product_code, product_name, risk_carrier_code, risk_carrier_name, valid_from, valid_to, bucket_path, relevant_contract_types }) {
        return {
            id: documentId,
            relevant_contract_types: JSON.parse(relevant_contract_types),
            file_name,
            product_code,
            product_name,
            risk_carrier_code,
            risk_carrier_name,
            valid_from,
            valid_to,
            bucket_path
        }
    },
    toStorage({ id, relevant_contract_types, ...apiValue }) {
        return {
            ...apiValue,
            relevant_contract_types: JSON.stringify(relevant_contract_types),
        }
    },
}
