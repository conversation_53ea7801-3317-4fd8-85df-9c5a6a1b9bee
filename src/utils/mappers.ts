import { type ReportSummary } from '@/generated/prisma-postgres';
import { type JsonObject } from '@/generated/prisma-postgres/runtime/library';

export const reportSummaryMapper = {
  toDomain(apiValue: any): JsonObject {
    return {
      id: apiValue.documentId,
      reportNumber: apiValue.reportNumber,
      authorType: apiValue.authorType,
      text: richTextMapper.toDomain(apiValue.text),
      timestamp: apiValue.timestamp,
    };
  },
  toStorage(domainValue: ReportSummary): any {
    return {
      reportNumber: domainValue.reportNumber,
      authorType: domainValue.authorType,
      text: richTextMapper.toStorage(domainValue.text!),
      timestamp: domainValue.timestamp,
    };
  },
};

export const richTextMapper = {
  toDomain(apiValue: string): string {
    return apiValue.replaceAll('<br>', '\n\n');
  },
  toStorage(domainValue: string): string {
    return domainValue.replaceAll('\n\n', '<br>');
  },
};
