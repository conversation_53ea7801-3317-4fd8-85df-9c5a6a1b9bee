export function ObfuscateString(
  text: string,
  obfuscationOn: boolean = true
): string {
  if (obfuscationOn && typeof text === 'string' && text.trim().length > 0) {
    return text.replace(
      /[A-Za-z]+/g,
      (match) => match[0] + 'x'.repeat(match.length - 1) // keep first, replace rest
    );
  }
  return text;
}

export function ObfuscateNumber(
  text: string,
  obfuscationOn: boolean = true
): string {
  const keepLast = 3;
  if (obfuscationOn && typeof text === 'string' && text.trim().length > 0) {
    return text.replace(/\d+/g, (match) => {
      if (match.length <= keepLast) return match;
      const maskedPart = 'x'.repeat(match.length - keepLast);
      const visiblePart = match.slice(-keepLast);
      return maskedPart + visiblePart;
    });
  }
  return text;
}

export function ObfuscateDDMMYYYY(
  text: string,
  obfuscationOn: boolean = true
): string {
  if (obfuscationOn && typeof text === 'string' && text.trim().length > 0) {
    return text.replace(/\d+/g, (match) => {
      if (match.length <= 2) {
        return 'x'.repeat(match.length);
      }
      return match;
    });
  }
  return text;
}

export function formatDateToDDMMYYYY(date: Date): string {
  const day: string = String(date.getDate()).padStart(2, '0');
  const month: string = String(date.getMonth() + 1).padStart(2, '0');
  const year: number = date.getFullYear();

  return `${day}.${month}.${year}`;
}
