// util/parseMultipart.ts
import formidable, {
  type Fields,
  type File as FormidableFile,
  type Files,
} from 'formidable';

export type ParsedForm = {
  fields: Record<string, any>;
  files: FormidableFile[];
};

export function parseMultipart(req: any): Promise<ParsedForm> {
  return new Promise((resolve, reject) => {
    const form = formidable({
      multiples: true,
      keepExtensions: true,
      // choose an appropriate temp dir (Linux containers often use /tmp)
      uploadDir: '/tmp',
      maxFileSize: 50 * 1024 * 1024, // 50MB, tune as needed
    });

    form.parse(req, (err, fields: Fields, files: Files) => {
      if (err) return reject(err);

      // flatten fields (formidable gives string|string[])
      const flatFields: Record<string, any> = {};
      for (const [k, v] of Object.entries(fields)) {
        const val = Array.isArray(v) ? v[0] : v;
        // attempt to JSON.parse any stringified objects/arrays
        try {
          flatFields[k] = typeof val === 'string' ? JSON.parse(val) : val;
        } catch {
          flatFields[k] = val;
        }
      }

      // collect all files (your client uses key "file")
      const fileListRaw = files.file ?? [];
      const fileList = (
        Array.isArray(fileListRaw) ? fileListRaw : [fileListRaw]
      ).filter(Boolean) as FormidableFile[];

      resolve({ fields: flatFields, files: fileList });
    });
  });
}
