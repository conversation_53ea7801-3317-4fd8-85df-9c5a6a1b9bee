// src/utils/dateUtils.ts

export const calculateFutureDate = (
  dateString: string,
  interval: string,
  endDateString: string
): string => {

  if (!["Einmalzahlung", "monatlich", "viertel<PERSON><PERSON><PERSON><PERSON>", "halb<PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>"].includes(interval)) {
    throw new Error("Bad format, interval has to be 'Einmalzahlung', 'monatlich', 'viertelj<PERSON>hrlich', 'halbj<PERSON>hrlich' or 'j<PERSON>hrlich'");
  }

  const [year, month, day] = dateString.split("-").map(Number);
  const initialDate = new Date(year, month - 1, day); // month - 1, because JS months are zero-based

  if (isNaN(initialDate.getTime())) {
    throw new Error("Bad format, date is not of form YYYY-MM-DD");
  }

  const monthsToAdd = interval === "Einmalzahlung" ? 24 :
    interval === "monatlich" ? 1 :
      interval === "vierteljährlich" ? 3 :
        interval === "halbj<PERSON>hrlich" ? 6 : 12;

  const targetMonth = initialDate.getMonth() + monthsToAdd;
  const targetYear = initialDate.getFullYear() + Math.floor(targetMonth / 12);
  const adjustedMonth = targetMonth % 12;

  let tentativeDate = new Date(targetYear, adjustedMonth, initialDate.getDate());

  // if calculated date is not valid (month end issue), correct it
  if (tentativeDate.getMonth() !== adjustedMonth) {
    tentativeDate.setDate(1); // skip to the first of the next month
    tentativeDate.setMonth(adjustedMonth + 1);
  }

  const endDate = new Date(endDateString);
  if (tentativeDate > endDate)
    tentativeDate = endDate

  // format: TT.MM.JJJJ
  const formattedDate = `${tentativeDate.getDate().toString().padStart(2, '0')}.${(tentativeDate.getMonth() + 1)
    .toString()
    .padStart(2, '0')}.${tentativeDate.getFullYear()}`;

  return formattedDate;
};

export const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  };
  return new Date(dateString).toLocaleString('de-DE', options);
};

/**
 * Return a UTC date string in YYYY-MM-DD format.
 * @param {Date|string|number} value – Anything the Date constructor accepts.
 *                                    Defaults to “now” if omitted.
 * @returns {string} e.g. "2025-04-08"
 */
export function toUTCDateString(
  value: Date | string | number = Date.now()
): string {
  const d = new Date(value);

  // Helper: always two digits
  const pad2 = (n: number): string => String(n).padStart(2, '0');

  return `${d.getUTCFullYear()}-${pad2(d.getUTCMonth() + 1)}-${pad2(d.getUTCDate())}`;
}