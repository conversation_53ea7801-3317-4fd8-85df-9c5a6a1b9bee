// src/utils/pdfTemplates/residentialBuildingInsurancePdfTemplate.ts
// Wohngebäude

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { type RiskAddressData } from '@/types';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { calculateFutureDate } from '@/utils/dateUtils';

export default async function createResidentialBuildingPdfData(
  contract: Contract,
  addText: (value: string, bold?: boolean) => void,
  addSubPoint: (key: string, value: string) => void,
  addLine: () => void,
  setPage: (pageNumber: number) => void,
  calculationParameter: CalculationParameter
) {
  const startDate = new Date(contract.insuranceStartDate!);

  addText(`Tarif:`);
  addText(`ALPHA WOHNGEBÄUDE EXPERT`, false);
  addText(`(<PERSON><PERSON>, Le<PERSON>swasser, Sturm/Hagel)`, false);
  if (
    contract.isElementar ||
    contract.householdTech ||
    contract.pvSystem ||
    contract.glassInsurance
  ) {
    addText(`Zusatz:`);
  }
  if (contract.isElementar)
    addText(`   •   Elementarschadenversicherung`, false);
  if (contract.householdTech)
    addText(`   •   Haustechnik EXPERT Zusatzbaustein`, false);
  if (contract.pvSystem)
    addText(`   •   Photovoltaik EXPERT Zusatzbaustein`, false);
  if (contract.glassInsurance)
    addText(`   •   Gebäudeglasversicherung `, false);
  addLine();

  addText(`Risikoangaben:`);
  (contract.riskAddresses as unknown as RiskAddressData[]).forEach(
    (risk_address, index) => {
      addSubPoint(
        `Risikoort${(contract.riskAddresses as unknown as RiskAddressData[]).length > 1 ? ' ' + (index + 1) : ''}`,
        `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`
      );
    }
  );
  addSubPoint(`Risikobezeichnung`, ` ${contract.objectType}`);
  addSubPoint(`Bauartklasse`, ` 1`);
  addSubPoint(
    `Baujahr`,
    `${contract.isConstructionYearUnknown ? 'unbekannt' : `${contract.constructionYear}`}`
  );
  addLine();

  if (contract.additionalAgreements) {
    addText(`Zusatzvereinbarungen:`);
    addText(`${contract.additionalAgreements}`, false);
  }

  // PAGE 3
  setPage(2);

  addText(`Versicherungssumme:`);
  addSubPoint(
    `Versicherungssumme (1914)`,
    ` ${contract.insuranceSum1914} Mark`
  );
  addSubPoint(
    `Baukostenindex (${new Date(calculationParameter.validFrom!).getFullYear()})`,
    ` ${calculationParameter.wohngebaudeSummenfaktor!.toString().replace('.', ',')}`
  );
  addSubPoint(
    `Versicherungssumme (${startDate.getFullYear()})`,
    ` ${contract.insuranceSum} EUR`
  );

  addText(`Jahresbeitrag`);
  if (!contract.isIndividuallyCalculated) {
    let beitrag = calculationParameter.wohngebaudeVersicherteGefahren!;
    if (contract.isElementar)
      beitrag += calculationParameter.wohngebaudeIsElementar!;
    if (contract.glassInsurance)
      beitrag += calculationParameter.wohngebaudeGlassInsurance!;
    if (contract.householdTech)
      beitrag += calculationParameter.wohngebaudeHouseholdTech!;
    if (contract.pvSystem) beitrag += calculationParameter.wohngebaudePvSystem!;
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).replace('.', ',')} ‰`
    );
  }
  if (!contract.isIndividuallyCalculated) {
    addSubPoint(
      `Beitragsfaktor`,
      ` ${calculationParameter.wohngebaudeBeitragsfaktor!.toString().replace('.', ',')}`
    );
  }
  if (contract.glassInsurance) {
    const nettoValue = Math.round(contract.premie! * 100) / 100;
    const nettoValueGlass =
      Math.round(contract.premieGlassInsurance! * 100) / 100;
    const nettoTotal = nettoValue + nettoValueGlass;
    addSubPoint(
      `Netto-Beitrag`,
      ` ${nettoTotal.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
    );
  } else {
    addSubPoint(
      `Netto-Beitrag`,
      ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })} EUR`
    );
  }
  addLine();

  addText(`Beitrag ${startDate.getFullYear()}`);
  if (!contract.isIndividuallyCalculated) {
    let beitrag = calculationParameter.wohngebaudeVersicherteGefahren!;
    if (contract.isElementar)
      beitrag += calculationParameter.wohngebaudeIsElementar!;
    if (contract.glassInsurance)
      beitrag += calculationParameter.wohngebaudeGlassInsurance!;
    if (contract.householdTech)
      beitrag += calculationParameter.wohngebaudeHouseholdTech!;
    if (contract.pvSystem) beitrag += calculationParameter.wohngebaudePvSystem!;
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).replace('.', ',')} ‰`
    );
  }
  if (!contract.isIndividuallyCalculated) {
    addSubPoint(
      `Beitragsfaktor`,
      ` ${calculationParameter.wohngebaudeBeitragsfaktor!.toString().replace('.', ',')}`
    );
  }
  if (contract.glassInsurance) {
    const nettoValue =
      Math.round(
        (calculateInsuranceDaysCurrentYear(
          contract.insuranceStartDate!,
          contract.insuranceEndDate!
        ) /
          365) *
          contract.premie! *
          100
      ) / 100;
    const nettoValueGlass =
      Math.round(
        (calculateInsuranceDaysCurrentYear(
          contract.insuranceStartDate!,
          contract.insuranceEndDate!
        ) /
          365) *
          contract.premieGlassInsurance! *
          100
      ) / 100;
    const nettoTotal = nettoValue + nettoValueGlass;
    addSubPoint(
      `Netto-Beitrag`,
      ` ${nettoTotal.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
    );
  } else {
    addSubPoint(
      `Netto-Beitrag`,
      ` ${(
        Math.round(
          (calculateInsuranceDaysCurrentYear(
            contract.insuranceStartDate!,
            contract.insuranceEndDate!
          ) /
            365) *
            contract.premie! *
            100
        ) / 100
      ).toLocaleString('de', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })} EUR`
    );
  }
  addSubPoint(`Zahlungsweise`, ` ${contract.paymentMode}`);
  addLine();

  addText(
    `Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(
      startDate.getMonth() + 1
    )
      .toString()
      .padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(
      contract.insuranceStartDate!,
      contract.paymentMode!,
      contract.insuranceEndDate!
    )})`
  );
  if (contract.glassInsurance) {
    const nettoValue = Math.round((contract.firstInvoiceNet || 0) * 100) / 100;
    const nettoValueGlass =
      Math.round((contract.firstInvoiceGlassNet || 0) * 100) / 100;
    const nettoTotal = nettoValue + nettoValueGlass;
    addSubPoint(
      `Netto-Beitrag`,
      ` ${nettoTotal.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
    );
    addSubPoint(
      `Versicherungssteuer ${(calculationParameter.wohngebaudeTax! * 100)
        .toFixed(2)
        .replace('.', ',')} % auf ${nettoValue.toLocaleString('de', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })} EUR netto`,
      ` ${(Math.round(contract.firstInvoiceTax! * 100) / 100).toLocaleString(
        'de',
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }
      )} EUR`
    );
    addSubPoint(
      `Versicherungssteuer ${(
        calculationParameter.wohngebaudeGlasversicherungTax! * 100
      )
        .toFixed(2)
        .replace('.', ',')} % auf ${nettoValueGlass.toLocaleString('de', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })} EUR netto`,
      ` ${(
        Math.round(contract.firstInvoiceGlassTax! * 100) / 100
      ).toLocaleString('de', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })} EUR`
    );
    addSubPoint(
      `Rechnungsbetrag`,
      ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString(
        'de',
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }
      )} EUR`
    );
  } else {
    addSubPoint(
      `Netto-Beitrag`,
      ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString(
        'de',
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }
      )} EUR`
    );
    addSubPoint(
      `Versicherungssteuer (${(calculationParameter.wohngebaudeTax! * 100).toFixed(2).replace('.', ',')} %)`,
      ` ${(Math.round(contract.firstInvoiceTax! * 100) / 100).toLocaleString(
        'de',
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }
      )} EUR`
    );
    addSubPoint(
      `Rechnungsbetrag`,
      ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString(
        'de',
        {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }
      )} EUR`
    );
  }
  addLine();

  addText(`Vertragsgrundlagen:`);
  addText(
    `   •   Allgemeine Wohngebäude Versicherungsbedingungen (VGB 2022)`,
    false
  );
  addText(`   •   Deckungskonzept ALPHA WOHNGEBÄUDE EXPERT`, false);
  addLine();
}
