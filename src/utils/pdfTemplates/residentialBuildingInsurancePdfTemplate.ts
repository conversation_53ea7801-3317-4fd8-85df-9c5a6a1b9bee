// src/utils/pdfTemplates/residentialBuildingInsurancePdfTemplate.ts
// Wohngebäude

import { ContractData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createResidentialBuildingPdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key: string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)

    addText(`Tarif:`);
    addText(`ALPHA WOHNGEBÄUDE EXPERT`, false);
    addText(`(<PERSON><PERSON>, <PERSON>was<PERSON>, Sturm/<PERSON>gel)`, false);
    if (contract.is_elementar || contract.household_tech || contract.pv_system || contract.glass_insurance) {
        addText(`Zusatz:`);
    }
    if (contract.is_elementar) { addText(`   •   Elementarschadenversicherung`, false); }
    if (contract.household_tech) { addText(`   •   Haustechnik EXPERT Zusatzbaustein`, false); }
    if (contract.pv_system) { addText(`   •   Photovoltaik EXPERT Zusatzbaustein`, false); }
    if (contract.glass_insurance) { addText(`   •   Gebäudeglasversicherung `, false); }
    addLine();

    addText(`Risikoangaben:`);
    contract.risk_addresses.forEach((risk_address, index) => {
        addSubPoint(`Risikoort${contract.risk_addresses.length > 1 ? (" " + (index + 1)) : ""}`, `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`);
    })
    addSubPoint(`Risikobezeichnung`, ` ${contract.object_type}`)
    addSubPoint(`Bauartklasse`, ` 1`)
    addSubPoint(`Baujahr`, `${contract.is_construction_year_unknown ? "unbekannt" : `${contract.construction_year}`}`);
    addLine();

    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        addText(`${contract.additional_agreements}`, false);
    }

    // PAGE 3
    setPage(2)

    addText(`Versicherungssumme:`);
    addSubPoint(`Versicherungssumme (1914)`, ` ${contract.insurance_sum_1914} Mark`);
    addSubPoint(`Baukostenindex (${new Date(calculationParameters.valid_from).getFullYear()})`, ` ${calculationParameters.wohngebaude_summenfaktor.toString().replace(".", ",")}`);
    addSubPoint(`Versicherungssumme (${startDate.getFullYear()})`, ` ${contract.insurance_sum} EUR`);

    addText(`Jahresbeitrag`);
    if (!contract.is_individually_calculated) {
        let beitrag = calculationParameters.wohngebaude_versicherte_gefahren;
        if (contract.is_elementar) { beitrag += calculationParameters.wohngebaude_is_elementar; }
        if (contract.glass_insurance) { beitrag += calculationParameters.wohngebaude_glass_insurance; }
        if (contract.household_tech) { beitrag += calculationParameters.wohngebaude_household_tech; }
        if (contract.pv_system) { beitrag += calculationParameters.wohngebaude_pv_system; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    if (!contract.is_individually_calculated) { addSubPoint(`Beitragsfaktor`, ` ${calculationParameters.wohngebaude_beitragsfaktor.toString().replace(".", ",")}`); }
    if (contract.glass_insurance) {
        const netto_value = Math.round(contract.premie! * 100) / 100;
        const netto_value_glass = Math.round(contract.premie_glass_insurance! * 100) / 100;
        const netto_total = netto_value + netto_value_glass;
        addSubPoint(`Netto-Beitrag`, ` ${(netto_total).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    } else {
        addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    }
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    if (!contract.is_individually_calculated) {
        let beitrag = calculationParameters.wohngebaude_versicherte_gefahren;
        if (contract.is_elementar) { beitrag += calculationParameters.wohngebaude_is_elementar; }
        if (contract.glass_insurance) { beitrag += calculationParameters.wohngebaude_glass_insurance; }
        if (contract.household_tech) { beitrag += calculationParameters.wohngebaude_household_tech; }
        if (contract.pv_system) { beitrag += calculationParameters.wohngebaude_pv_system; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    if (!contract.is_individually_calculated) { addSubPoint(`Beitragsfaktor`, ` ${calculationParameters.wohngebaude_beitragsfaktor.toString().replace(".", ",")}`); }
    if (contract.glass_insurance) {
        const netto_value = Math.round(calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) / 365 * contract.premie! * 100) / 100;
        const netto_value_glass = Math.round(calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) / 365 * contract.premie_glass_insurance! * 100) / 100;
        const netto_total = netto_value + netto_value_glass;
        addSubPoint(`Netto-Beitrag`, ` ${(netto_total).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    } else {
        addSubPoint(`Netto-Beitrag`, ` ${(Math.round(calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) / 365 * contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    }
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();


    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date)})`);
    if (contract.glass_insurance) {
        const netto_value = Math.round((contract.first_invoice_net || 0) * 100) / 100;
        const netto_value_glass = Math.round((contract.first_invoice_glass_net || 0) * 100) / 100;
        const netto_total = netto_value + netto_value_glass;
        addSubPoint(`Netto-Beitrag`, ` ${(netto_total).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
        addSubPoint(`Versicherungssteuer ${(calculationParameters.wohngebaude_tax * 100).toFixed(2).toString().replace(".", ",")} % auf ${netto_value.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR netto`, ` ${(Math.round(contract.first_invoice_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
        addSubPoint(`Versicherungssteuer ${(calculationParameters.wohngebaude_glasversicherung_tax * 100).toFixed(2).toString().replace(".", ",")} % auf ${netto_value_glass.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR netto`, ` ${(Math.round(contract.first_invoice_glass_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
        addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    } else {
        addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
        addSubPoint(`Versicherungssteuer (${(calculationParameters.wohngebaude_tax * 100).toFixed(2).toString().replace(".", ",")} %)`, ` ${(Math.round(contract.first_invoice_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
        addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    }
    addLine();


    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Wohngebäude Versicherungsbedingungen (VGB 2022)`, false);
    addText(`   •   Deckungskonzept ALPHA WOHNGEBÄUDE EXPERT`, false);
    addLine();
}