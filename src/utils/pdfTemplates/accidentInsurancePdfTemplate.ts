// src/utils/pdfTemplates/accidentInsurancePdfTemplate.ts
// Unfallversicherung

//AccidentInsuranceForm

import { ContractData, AccidentInsuranceFormData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createAccidentInsurancePdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key: string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)
    let pageNum = 1;

    addText(`Tarif:`);
    addText(`Unfallversicherung EXPERT`, false);
    addLine();
    addText(`Risikoangaben:`);

    const formatValue = (value: any): string => {
        const number = Number(value);

        if (isNaN(number) || value === '' || value === null || value === undefined || value === false) {
            return '0 EUR';
        }

        return `${number} EUR`;
    };

    const generateInsuredPersonFields = (insuredPersonData: AccidentInsuranceFormData[]) => {
        insuredPersonData.forEach((person, index) => {
            addText(`Person ${index + 1}`);
            addSubPoint('Vorname', person.first_name);
            addSubPoint('Nachname', person.last_name);
            addSubPoint('Geburtstag', person.birth_date);
            if (person.type) addSubPoint('Typ', person.type);
            if (person.occupation_group) addSubPoint('Berufsgruppe', person.occupation_group);
            if (person.occupation) addSubPoint('Beruf', person.occupation);
            addSubPoint('Mehrleistungsklausel', formatValue(person.increased_benefit_clause));
            addSubPoint('Invalidität', `${person.disability_coverage} %`);
            addSubPoint('Grundsumme Invalidität', formatValue(person.basic_sum));
            addSubPoint('Unfallrente', formatValue(person.accident_pension));
            addSubPoint('Unfalltod', formatValue(person.accidental_death));
            addSubPoint('Krankentagegeld', formatValue(person.daily_sickness_allowance));
            addSubPoint('Krankenhaustagegeld mit Genesungsgeld', formatValue(person.hospital_daily_allowance));
            addSubPoint('Übergangsleistung', formatValue(person.transitional_benefit));
            if (typeof person.first_aid_module === 'boolean') addSubPoint('Erste-Hilfe-Baustein', person.first_aid_module ? 'Ja' : 'Nein');
            pageNum += 1;
            setPage(pageNum)
        });
    };
    generateInsuredPersonFields(contract.insured_persons);

    // New Page
    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        contract.additional_agreements.split(/\r?\n/).forEach((line) => {
            addText(`  ${line}`, false);
        })
    }

    addText(`Jahresbeitrag`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();

    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date)})`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Versicherungssteuer (${(calculationParameters.tierhalterhaftpflicht_tax * 100).toFixed(2).toString().replace(".", ",")} %)`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Unfallversicherung Versicherungsbedingungen`, false);
    addText(`   •   Deckungskonzept ALPHA UNFALLVERSICHERUNG EXPERT`, false);
    addLine();
}