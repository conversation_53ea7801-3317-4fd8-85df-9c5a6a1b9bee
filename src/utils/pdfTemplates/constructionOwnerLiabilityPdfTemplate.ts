// src/utils/pdfTemplates/constructionOwnerLiabilityPdfTemplate.ts
// Bauherrenhaftpflicht

import { ContractData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createConstructionOwnerPdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key: string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)

    addText(`Tarif:`);
    addText(`ALPHA BAUHERRENHAFTPFLICHT EXPERT`, false);
    addLine();

    addText(`Risikoangaben:`);
    contract.risk_addresses.forEach((risk_address, index) => {
        addSubPoint(`Risikoort${contract.risk_addresses.length > 1 ? (" " + (index + 1)) : ""}`, `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`);
    })
    addSubPoint(`Risikobezeichnung`, `${contract.object_type}`)
    addSubPoint(`Bauartklasse`, ` 1`)
    addLine();

    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        addText(`${contract.additional_agreements}`, false);
    }

    // PAGE 3
    setPage(2)

    addText(`Deckungssumme:`);
    addSubPoint(`Personen-, Sach-, Vermögensschäden`, ` EUR 60.000.000`);
    addSubPoint(`Bausumme`, ` ${contract.building_sum!.toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Jahresbeitrag`);
    if (!contract.is_individually_calculated) { addSubPoint(`Beitragssatz`, ` 0,25 ‰`);}
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    if (!contract.is_individually_calculated) { addSubPoint(`Beitragssatz`, ` 0,25 ‰`);}
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();

    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date)})`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Versicherungssteuer (${(calculationParameters.bauherrenhaftpflicht_tax * 100).toFixed(2).toString().replace(".", ",")} %)`, ` ${(Math.round(contract.first_invoice_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Haftpflicht Versicherungsbedingungen (AHB 2016)`, false);
    addText(`   •   Deckungskonzept ALPHA BAUHERRENHAFTPFLICHT EXPERT`, false);
    addLine();
}