// src/utils/pdfTemplates/animalLiabilityInsurancePdfTemplate.ts
// Tierhalterhaftpflicht

import { ContractData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculateInvoiceDaysThisYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createAnimalLiabilityPdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key:string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)

    addText(`Tarif:`);
    addText(`ALPHA TIERHALTERHAFTPFLICHT EXPERT`, false);
    addLine();

    addText(`Risikoangaben:`);
    const generateAnimalFields = (animalData: { animal_type: string; animal_name: string; race?: string;}[]) => {
        animalData.forEach(animal => {
            addSubPoint('Tierart', animal.animal_type);
            addSubPoint('Name', animal.animal_name);

            if (animal.animal_type === "Hund" && animal.race) {
                addSubPoint('Rasse', animal.race);
            }
        });
    };
    generateAnimalFields(contract.animal_data);
    addLine();

    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        addText(`${contract.additional_agreements}`, false);
    }

    // PAGE 3
    setPage(2)

    addText(`Deckungssumme:`);
    addSubPoint('Personen-, Sach-, Vermögensschäden', ` EUR ${parseInt(contract.coverage_amount).toLocaleString('de' ,{minimumFractionDigits:0, maximumFractionDigits:2})}`);
    addLine();

    addText(`Jahresbeitrag`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();

    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date )})`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addSubPoint(`Versicherungssteuer (${(calculationParameters.tierhalterhaftpflicht_tax * 100).toFixed(2).toString().replace(".", ",")} %)`,  ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addLine();

    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Haftpflicht Versicherungsbedingungen (AHB 2016)`, false);
    addText(`   •   Deckungskonzept ALPHA TIERHALTERHAFTPFLICHT EXPERT`, false);
    addLine();
}