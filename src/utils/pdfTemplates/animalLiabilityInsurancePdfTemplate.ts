// src/utils/pdfTemplates/animalLiabilityInsurancePdfTemplate.ts
// Tierhalterhaftpflicht

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { calculateFutureDate } from '@/utils/dateUtils';

export default async function createAnimalLiabilityPdfData(
  contract: Contract,
  addText: (value: string, bold?: boolean) => void,
  addSubPoint: (key: string, value: string) => void,
  addLine: () => void,
  setPage: (pageNumber: number) => void,
  calculationParameter: CalculationParameter
) {
  const startDate = new Date(contract.insuranceStartDate!);

  addText(`Tarif:`);
  addText(`ALPHA TIERHALTERHAFTPFLICHT EXPERT`, false);
  addLine();

  addText(`Risikoangaben:`);
  const generateAnimalFields = (
    animalData: { animal_type: string; animal_name: string; race?: string }[]
  ) => {
    animalData.forEach((animal) => {
      addSubPoint('Tierart', animal.animal_type);
      addSubPoint('Name', animal.animal_name);

      if (animal.animal_type === 'Hund' && animal.race) {
        addSubPoint('Rasse', animal.race);
      }
    });
  };
  generateAnimalFields(contract.animalData as any);
  addLine();

  if (contract.additionalAgreements) {
    addText(`Zusatzvereinbarungen:`);
    addText(`${contract.additionalAgreements}`, false);
  }

  // PAGE 3
  setPage(2);

  addText(`Deckungssumme:`);
  addSubPoint(
    'Personen-, Sach-, Vermögensschäden',
    ` EUR ${parseInt(contract.coverageAmount!).toLocaleString('de', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}`
  );
  addLine();

  addText(`Jahresbeitrag`);
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Beitrag ${startDate.getFullYear()}`);
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round((contract.premie! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(`Zahlungsweise`, ` ${contract.paymentMode}`);
  addLine();

  addText(
    `Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insuranceStartDate!, contract.paymentMode!, contract.insuranceEndDate!)})`
  );
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Versicherungssteuer (${(calculationParameter.tierhalterhaftpflichtTax! * 100).toFixed(2).toString().replace('.', ',')} %)`,
    ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Rechnungsbetrag`,
    ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Vertragsgrundlagen:`);
  addText(
    `   •   Allgemeine Haftpflicht Versicherungsbedingungen (AHB 2016)`,
    false
  );
  addText(`   •   Deckungskonzept ALPHA TIERHALTERHAFTPFLICHT EXPERT`, false);
  addLine();
}
