// src/utils/pdfTemplates/householdInsurancePdfTemplate.ts
// Hausrat

import { ContractData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createHouseholdPdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key: string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)

    addText(`Tarif:`);
    addText(`ALPHA HAUSRATVERSICHERUNG EXPERT`, false);
    addText(`(<PERSON><PERSON>, ED/V, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ur<PERSON>/<PERSON>)`, false);
    if (contract.is_elementar) {
        addText(`Zusatz:`);
        addText(`   •   Elementarschadenversicherung`, false);
    }
    addLine();

    addText(`Risikoangaben:`);
    contract.risk_addresses.forEach((risk_address, index) => {
        addSubPoint(`Risikoort${contract.risk_addresses.length > 1 ? (" " + (index + 1)) : ""}`, `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`);
    })
    addSubPoint(`Risikobezeichnung`, `${contract.object_type}`);
    addSubPoint(`Bauartklasse`, ` 1`)
    addSubPoint(`Wohnfläche`, ` ${contract.living_area} m²`);
    addLine();

    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        addText(`${contract.additional_agreements}`, false);
    }

    // PAGE 3
    setPage(2)

    addText(`Versicherungssumme:`);
    addSubPoint(`Versicherungssumme zum Neuwert`, ` ${contract.insurance_sum} EUR`);
    addLine();

    addText(`Versicherte Gefahren:`);
    addText(`Gemäß Alpha Hausratversicherung Expert`, false);
    addText(`Zusätzliche Einschlüsse : ${contract.is_elementar ? "Elementar ( Selbstbeteiligung EUR 1.000 )" : "keine"}`, false);
    addLine();

    addText(`Jahresbeitrag`);
    if (!contract.is_individually_calculated) {
        let beitrag = calculationParameters.hausrat_versicherte_gefahren;
        if (contract.is_elementar) { beitrag += calculationParameters.hausrat_is_elementar; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    if (!contract.is_individually_calculated) {
        let beitrag = calculationParameters.hausrat_versicherte_gefahren;
        if (contract.is_elementar) { beitrag += calculationParameters.hausrat_is_elementar; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();

    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date)})`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Versicherungssteuer (${(calculationParameters.hausrat_tax * 100).toFixed(2).toString().replace(".", ",")} %)`, ` ${(Math.round(contract.first_invoice_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Hausrat Versicherungsbedingungen (VHB 2022)`, false);
    addText(`   •   Deckungskonzept ALPHA HAUSRAT EXPERT`, false);
    addLine();
}