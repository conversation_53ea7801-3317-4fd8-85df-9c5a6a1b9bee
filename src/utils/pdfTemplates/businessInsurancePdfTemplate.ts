// src/utils/pdfTemplates/businessInsurancePdfTemplate.ts
// Geschäftsversicherung

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { type RiskAddressData } from '@/types';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { calculateFutureDate } from '@/utils/dateUtils';

export default async function createBusinessPdfData(
  contract: Contract,
  addText: (value: string, bold?: boolean) => void,
  addSubPoint: (key: string, value: string) => void,
  addLine: () => void,
  setPage: (pageNumber: number) => void,
  calculationParameter: CalculationParameter
) {
  const startDate = new Date(contract.insuranceStartDate!);

  addText(`Tarif:`);
  addText(`ALPHA GESCHÄFTSVERSICHERUNG EXPERT`, false);
  addText(`(<PERSON>uer, ED/V, Leitungswasser, Sturm/Hagel)`, false);
  if (contract.isElementar!) {
    addText(`Zusatz:`);
    addText(`   •   Elementarschadenversicherung`, false);
  }
  addLine();

  addText(`Risikoangaben:`);
  (contract.riskAddresses as unknown as RiskAddressData[])!.forEach(
    (risk_address, index) => {
      addSubPoint(
        `Risikoort${(contract.riskAddresses as unknown as RiskAddressData[])!.length > 1 ? ' ' + (index + 1) : ''}`,
        `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`
      );
    }
  );
  addSubPoint(`Betriebsart`, `${contract.businessType}`);
  addLine();

  if (contract.additionalAgreements) {
    addText(`Zusatzvereinbarungen:`);
    addText(`${contract.additionalAgreements}`, false);
  }

  // PAGE 3
  setPage(2);

  addText(`Versicherungssumme:`);
  addSubPoint(`Versicherungssumme`, ` ${contract.insuranceSum} EUR`);
  addLine();

  addText(`Versicherte Gefahren:`);
  addText(`Gemäß Alpha Geschäftsversicherung Expert`, false);
  addText(
    `Zusätzliche Einschlüsse : ${contract.isElementar! ? 'Elementar ( Selbstbeteiligung EUR 2.000 )' : 'keine'}`,
    false
  );
  addLine();

  addText(`Jahresbeitrag`);
  if (!contract.isIndividuallyCalculated!) {
    let beitrag = 0;
    const group = contract.tariffGroup!;
    if (contract.isElementar!) {
      beitrag += calculationParameter.geschaeftsversicherungIsElementar!;
    }
    if (group === 'Handel') {
      beitrag += calculationParameter.geschaeftsversicherungHandel!;
    }
    if (group === 'Handwerk') {
      beitrag += calculationParameter.geschaeftsversicherungHandwerk!;
    }
    if (group === 'Dienstleistungen') {
      beitrag += calculationParameter.geschaeftsversicherungDienstleistungen!;
    }
    if (group === 'Gastronomie') {
      beitrag += calculationParameter.geschaeftsversicherungGastronomie!;
    }
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).toString().replace('.', ',')} ‰`
    );
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Beitrag ${startDate.getFullYear()}`);
  if (!contract.isIndividuallyCalculated!) {
    let beitrag = 0;
    const group = contract.tariffGroup!;
    if (contract.isElementar!) {
      beitrag += calculationParameter.geschaeftsversicherungIsElementar!;
    }
    if (group === 'Handel') {
      beitrag += calculationParameter.geschaeftsversicherungHandel!;
    }
    if (group === 'Handwerk') {
      beitrag += beitrag +=
        calculationParameter.geschaeftsversicherungHandwerk!;
    }
    if (group === 'Dienstleistungen') {
      beitrag += calculationParameter.geschaeftsversicherungDienstleistungen!;
    }
    if (group === 'Gastronomie') {
      beitrag += calculationParameter.geschaeftsversicherungGastronomie!;
    }
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).toString().replace('.', ',')} ‰`
    );
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round((contract.premie! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(`Zahlungsweise`, ` ${contract.paymentMode!}`);
  addLine();

  addText(
    `Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insuranceStartDate!, contract.paymentMode!, contract.insuranceEndDate!)})`
  );
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Versicherungssteuer (${(calculationParameter.geschaeftsversicherungTax! * 100).toFixed(2).toString().replace('.', ',')} %)`,
    ` ${(Math.round(contract.firstInvoiceTax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Rechnungsbetrag`,
    ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Vertragsgrundlagen:`);
  addText(
    `   •   Allgemeine Bedingungen für die Feuerversicherung ( AFB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Leitungswasserversicherung ( AWB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Sturmversicherung ( AStB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Einbruchdiebstahlversicherung ( AERB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Glasversicherung ( AGlB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Betriebsunterbrechung ( ZKBU 2010 )`,
    false
  );
  addText(`   •   Deckungskonzept ALPHA GESCHÄFTSVERSICHERUNG EXPERT`, false);
  addLine();
}
