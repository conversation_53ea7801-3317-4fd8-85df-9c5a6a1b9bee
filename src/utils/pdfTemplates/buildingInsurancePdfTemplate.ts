// src/utils/pdfTemplates/buildingInsurancePdfTemplate.ts
// Gewerbe – Gebäude-Versicherung

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { type JsonArray } from '@/generated/prisma-postgres/runtime/library';
import { type RiskAddressData } from '@/types';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { calculateFutureDate } from '@/utils/dateUtils';

export default async function createBuildingPdfData(
  contract: Contract,
  addText: (value: string, bold?: boolean) => void,
  addSubPoint: (key: string, value: string) => void,
  addLine: () => void,
  setPage: (pageNumber: number) => void,
  calculationParameter: CalculationParameter
) {
  const startDate = new Date(contract.insuranceStartDate!);

  addText(`Tarif:`);
  addText(`ALPHA GEWERBLICHE GEBÄUDE EXPERT`, false);
  addText(`(<PERSON><PERSON>, <PERSON><PERSON>ungswasser, Sturm/<PERSON>gel)`, false);
  if (contract.isElementar!) {
    addText(`Zusatz:`);
    addText(`   •   Elementarschadenversicherung`, false);
  }
  addLine();

  addText(`Risikoangaben:`);
  (contract.riskAddresses as unknown as RiskAddressData[])!.forEach(
    (risk_address, index) => {
      addSubPoint(
        `Risikoort${(contract.riskAddresses as JsonArray)!.length > 1 ? ' ' + (index + 1) : ''}`,
        `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`
      );
    }
  );
  addSubPoint(`Betriebsart`, `${contract.businessType}`);
  addSubPoint(`Bauartklasse`, ` 1`);
  addSubPoint(
    `Baujahr`,
    `${contract.isConstructionYearUnknown ? 'unbekannt' : `${contract.constructionYear}`}`
  );

  if (contract.additionalAgreements) {
    addText(`Zusatzvereinbarungen:`);
    addText(`${contract.additionalAgreements}`);
  }

  // PAGE 3
  setPage(2);

  addText(`Versicherungssumme:`);
  addSubPoint(
    `Versicherungssumme zum Neuwert (${startDate.getFullYear()})`,
    ` ${contract.insuranceSum} EUR`
  );
  addLine();

  addText(`Versicherte Gefahren:`);
  addText('Gemäß Alpha gewerbliche Gebäude Expert', false);
  addText(
    `Zusätzliche Einschlüsse: ${contract.isElementar! ? 'Elementar' : 'keine'}`,
    false
  );
  addLine();

  addText(`Jahresbeitrag`);
  addSubPoint(`Tarifgruppe`, `${contract.buildingType}`);
  if (!contract.isIndividuallyCalculated) {
    let beitrag = 0;
    const group = contract.tariffGroup;
    if (contract.isElementar!) {
      beitrag += calculationParameter.gebaeudeversicherungIsElementar!;
    }
    if (group === 'Handel') {
      beitrag += calculationParameter.gebaeudeversicherungHandel!;
    }
    if (group === 'Handwerk') {
      beitrag += calculationParameter.gebaeudeversicherungHandwerk!;
    }
    if (group === 'Dienstleistungen') {
      beitrag += calculationParameter.gebaeudeversicherungDienstleistungen!;
    }
    if (group === 'Gastronomie') {
      beitrag += calculationParameter.gebaeudeversicherungGastronomie!;
    }
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).toString().replace('.', ',')} ‰`
    );
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Beitrag ${startDate.getFullYear()}`);
  addSubPoint(`Tarifgruppe`, `${contract.buildingType}`);
  if (!contract.isIndividuallyCalculated) {
    let beitrag = 0;
    const group = contract.tariffGroup;
    if (contract.isElementar!) {
      beitrag += calculationParameter.gebaeudeversicherungIsElementar!;
    }
    if (group === 'Handel') {
      beitrag += calculationParameter.gebaeudeversicherungHandel!;
    }
    if (group === 'Handwerk') {
      beitrag += calculationParameter.gebaeudeversicherungHandwerk!;
    }
    if (group === 'Dienstleistungen') {
      beitrag += calculationParameter.gebaeudeversicherungDienstleistungen!;
    }
    if (group === 'Gastronomie') {
      beitrag += calculationParameter.gebaeudeversicherungGastronomie!;
    }
    addSubPoint(
      `Beitragssatz`,
      ` ${(beitrag * 1000).toFixed(2).toString().replace('.', ',')} ‰`
    );
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round((contract.premie! / 365) * calculateInsuranceDaysCurrentYear(contract.insuranceStartDate!, contract.insuranceEndDate!) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(`Zahlungsweise`, ` ${contract.paymentMode}`);
  addLine();

  addText(
    `Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insuranceStartDate!, contract.paymentMode!, contract.insuranceEndDate!)})`
  );
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Versicherungssteuer (${(calculationParameter.gebaeudeversicherungTax! * 100).toFixed(2).toString().replace('.', ',')} %)`,
    ` ${(Math.round(contract.firstInvoiceTax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addSubPoint(
    `Rechnungsbetrag`,
    ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`
  );
  addLine();

  addText(`Vertragsgrundlagen:`);
  addText(
    `   •   Allgemeine Bedingungen für die Feuerversicherung ( AFB2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Leitungswasserversicherung ( AWB 2010 )`,
    false
  );
  addText(
    `   •   Allgemeine Bedingungen für die Sturmversicherung ( AStB 2010 )`,
    false
  );
  addText(`   •   Deckungskonzept ALPHA GEWERBE GEBÄUDE EXPERT`, false);
  addLine();
}
