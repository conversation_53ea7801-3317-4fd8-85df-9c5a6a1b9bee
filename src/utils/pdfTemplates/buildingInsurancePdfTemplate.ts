// src/utils/pdfTemplates/buildingInsurancePdfTemplate.ts
// Gewerbe – Gebäude-Versicherung

import { ContractData } from "@/types";
import { calculateFutureDate } from "@/utils/dateUtils";
import { calculateInsuranceDaysCurrentYear, calculatePrice } from "../calculationUtil";
import { getCalculationParameters } from "../contractCalculator";

export default async function createBuildingPdfData(contract: ContractData, addText: (value: string, bold?: boolean) => void, addSubPoint: (key: string, value: string) => void, addLine: () => void, setPage: (pageNumber: number) => void, token: string) {

    const startDate = new Date(contract.insurance_start_date);
    const calculationParameters = await getCalculationParameters(token, contract.insurance_start_date)

    addText(`Tarif:`);
    addText(`ALPHA GEWERBLICHE GEBÄUDE EXPERT`, false);
    addText(`(<PERSON><PERSON>, <PERSON><PERSON>was<PERSON>, Sturm/<PERSON>)`, false);
    if (contract.is_elementar) {
        addText(`Zusatz:`);
        addText(`   •   Elementarschadenversicherung`, false);
    }
    addLine();

    addText(`Risikoangaben:`);
    contract.risk_addresses.forEach((risk_address, index) => {
        addSubPoint(`Risikoort${contract.risk_addresses.length > 1 ? (" " + (index + 1)) : ""}`, `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`);
    })
    addSubPoint(`Betriebsart`, `${contract.business_type}`)
    addSubPoint(`Bauartklasse`, ` 1`)
    addSubPoint(`Baujahr`, `${contract.is_construction_year_unknown ? "unbekannt" : `${contract.construction_year}`}`);


    if (contract.additional_agreements) {
        addText(`Zusatzvereinbarungen:`);
        addText(`${contract.additional_agreements}`);
    }

    // PAGE 3
    setPage(2)

    addText(`Versicherungssumme:`);
    addSubPoint(`Versicherungssumme zum Neuwert (${startDate.getFullYear()})`, ` ${contract.insurance_sum} EUR`);
    addLine();

    addText(`Versicherte Gefahren:`);
    addText('Gemäß Alpha gewerbliche Gebäude Expert', false)
    addText(`Zusätzliche Einschlüsse: ${contract.is_elementar ? "Elementar" : "keine"}`, false);
    addLine();

    addText(`Jahresbeitrag`);
    addSubPoint(`Tarifgruppe`, `${contract.building_type}`);
    if (!contract.is_individually_calculated) {
        let beitrag = 0;
        const group = contract.tariff_group;
        if (contract.is_elementar) { beitrag += calculationParameters.gebaeudeversicherung_is_elementar; }
        if (group === "Handel") { beitrag += calculationParameters.gebaeudeversicherung_handel; }
        if (group === "Handwerk") { beitrag += calculationParameters.gebaeudeversicherung_handwerk; }
        if (group === "Dienstleistungen") { beitrag += calculationParameters.gebaeudeversicherung_dienstleistungen; }
        if (group === "Gastronomie") { beitrag += calculationParameters.gebaeudeversicherung_gastronomie; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de' ,{minimumFractionDigits:2, maximumFractionDigits:2})} EUR`);
    addLine();

    addText(`Beitrag ${startDate.getFullYear()}`);
    addSubPoint(`Tarifgruppe`, `${contract.building_type}`);
    if (!contract.is_individually_calculated) {
        let beitrag = 0;
        const group = contract.tariff_group;
        if (contract.is_elementar) { beitrag += calculationParameters.gebaeudeversicherung_is_elementar; }
        if (group === "Handel") { beitrag += calculationParameters.gebaeudeversicherung_handel; }
        if (group === "Handwerk") { beitrag += calculationParameters.gebaeudeversicherung_handwerk; }
        if (group === "Dienstleistungen") { beitrag += calculationParameters.gebaeudeversicherung_dienstleistungen; }
        if (group === "Gastronomie") { beitrag += calculationParameters.gebaeudeversicherung_gastronomie; }
        addSubPoint(`Beitragssatz`, ` ${(beitrag * 1000).toFixed(2).toString().replace(".", ",")} ‰`);
    }
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.premie! / 365 * calculateInsuranceDaysCurrentYear(contract.insurance_start_date, contract.insurance_end_date) * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Zahlungsweise`, ` ${contract.payment_mode}`);
    addLine();

    addText(`Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(startDate.getMonth() + 1).toString().padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(contract.insurance_start_date, contract.payment_mode, contract.insurance_end_date)})`);
    addSubPoint(`Netto-Beitrag`, ` ${(Math.round(contract.first_invoice_net! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Versicherungssteuer (${(calculationParameters.gebaeudeversicherung_tax * 100).toFixed(2).toString().replace(".", ",")} %)`, ` ${(Math.round(contract.first_invoice_tax! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addSubPoint(`Rechnungsbetrag`, ` ${(Math.round(contract.first_invoice_gross! * 100) / 100).toLocaleString('de', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} EUR`);
    addLine();

    addText(`Vertragsgrundlagen:`);
    addText(`   •   Allgemeine Bedingungen für die Feuerversicherung ( AFB2010 )`, false);
    addText(`   •   Allgemeine Bedingungen für die Leitungswasserversicherung ( AWB 2010 )`, false);
    addText(`   •   Allgemeine Bedingungen für die Sturmversicherung ( AStB 2010 )`, false);
    addText(`   •   Deckungskonzept ALPHA GEWERBE GEBÄUDE EXPERT`, false);
    addLine();
}