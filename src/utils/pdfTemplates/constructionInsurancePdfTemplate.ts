// src/utils/pdfTemplates/constructionInsurancePdfTemplate.ts
// Bauleistung

import {
  type CalculationParameter,
  type Contract,
} from '@/generated/prisma-postgres';
import { type RiskAddressData } from '@/types';
import { calculateInsuranceDaysCurrentYear } from '@/utils/calculationUtil';
import { calculateFutureDate } from '@/utils/dateUtils';

export default async function createConstructionPdfData(
  contract: Contract,
  addText: (value: string, bold?: boolean) => void,
  addSubPoint: (key: string, value: string) => void,
  addLine: () => void,
  setPage: (pageNumber: number) => void,
  calculationParameter: CalculationParameter
) {
  const startDate = new Date(contract.insuranceStartDate!);

  addText(`Tarif:`);
  addText(`ALPHA BAULEISTUNG EXPERT`, false);
  addLine();

  addText(`Risikoangaben:`);
  (contract.riskAddresses as unknown as RiskAddressData[]).forEach(
    (risk_address, index) => {
      addSubPoint(
        `Risikoort${(contract.riskAddresses as unknown as RiskAddressData[]).length > 1 ? ' ' + (index + 1) : ''}`,
        `${risk_address.street} ${risk_address.house_number}, ${risk_address.postal_code} ${risk_address.city}${risk_address.unit?.trim() ? ', ' + risk_address.unit.trim() : ''}`
      );
    }
  );
  addSubPoint(`Risikobezeichnung`, `${contract.objectType}`);
  addSubPoint(`Bauartklasse`, ` 1`);
  addLine();

  if (contract.additionalAgreements) {
    addText(`Zusatzvereinbarungen:`);
    addText(`${contract.additionalAgreements}`, false);
  }

  // PAGE 3
  setPage(2);

  addText(`Versicherungssumme:`);
  addSubPoint(
    `Bausumme`,
    ` ${contract.insuranceSum!.toLocaleString('de', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })} EUR`
  );
  addLine();

  addText(`Jahresbeitrag`);
  if (!contract.isIndividuallyCalculated) {
    addSubPoint(`Beitragssatz`, ` 0,8 ‰`);
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.premie! * 100) / 100).toLocaleString('de', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })} EUR`
  );
  addLine();

  addText(`Beitrag ${startDate.getFullYear()}`);
  if (!contract.isIndividuallyCalculated) {
    addSubPoint(`Beitragssatz`, ` 0,8 ‰`);
  }
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(
      Math.round(
        (contract.premie! / 365) *
          calculateInsuranceDaysCurrentYear(
            contract.insuranceStartDate!,
            contract.insuranceEndDate!
          ) *
          100
      ) / 100
    ).toLocaleString('de', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })} EUR`
  );
  addSubPoint(`Zahlungsweise`, ` ${contract.paymentMode}`);
  addLine();

  addText(
    `Zu zahlender Betrag (${startDate.getDate().toString().padStart(2, '0')}.${(
      startDate.getMonth() + 1
    )
      .toString()
      .padStart(2, '0')}.${startDate.getFullYear()} - ${calculateFutureDate(
      contract.insuranceStartDate!,
      contract.paymentMode!,
      contract.insuranceEndDate!
    )})`
  );
  addSubPoint(
    `Netto-Beitrag`,
    ` ${(Math.round(contract.firstInvoiceNet! * 100) / 100).toLocaleString(
      'de',
      {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }
    )} EUR`
  );
  addSubPoint(
    `Versicherungssteuer (${(calculationParameter.bauleistungTax! * 100)
      .toFixed(2)
      .toString()
      .replace('.', ',')} %)`,
    ` ${(Math.round(contract.firstInvoiceTax! * 100) / 100).toLocaleString(
      'de',
      {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }
    )} EUR`
  );
  addSubPoint(
    `Rechnungsbetrag`,
    ` ${(Math.round(contract.firstInvoiceGross! * 100) / 100).toLocaleString(
      'de',
      {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }
    )} EUR`
  );
  addLine();

  addText(`Vertragsgrundlagen:`);
  addText(
    `   •   Allgemeine Bauleistung Versicherungsbedingungen (ABBL 2018)`,
    false
  );
  addText(`   •   Deckungskonzept ALPHA BAULEISTUNG EXPERT`, false);
  addLine();
}
