
export interface InvoiceData {
    documentId: string;
    type: InvoiceType;
    contract_number: string;
    version_number: number;
    invoice_detail_status: InvoiceDetailStatusData;
    invoice_status: InvoiceStatusType;
    agent_status: InvoiceAgentStatusType;
    insurance_status: InvoiceInsuranceStatusType;
    customer_status: InvoiceCustomerStatusType; 
    invoice_number: string;
    due_date: string;
    total_net: number;
    total_gross: number;
    positions: InvoicePositionData[];
    billing_street: string;
    billing_house_number: string;
    billing_city: string;
    billing_postal_code: string;
    billing_care_of: string;
    first_name: string;
    last_name: string;
    name_prefix: string;
    payment_mode: string;
    insurance_start_date: string;
    insurance_end_date: string;
    iban: string;
    bic: string;
    agent_street: string;
    agent_house_number: string;
    agent_city: string;
    agent_postal_code: string;
    agent_company_name: string;
    agent_number: string;
    customer_number: string;
    subject: string;
    updatedAt: string;
    createdAt: string;
    automatically_generated: boolean;
    agency_number: string;
}

export interface InvoicePositionData {
    net: number;
    tax: number;
    name: string;
    tax_amount: number;
}

export interface InvoiceDetailStatusData {
    send_date: string;
    overdue_date: string;
    dunning_date: string;
    partially_paid_date: string;
    partially_payment_amount: number;
    payment_date: string;
    refund_date: string;
    cancel_date: string;
    fail_date: string;
    proccessing_date: string;
}

export interface InvoiceSequenceData {
    contract_number: string;
    sequence: number;
    documentId: string;
}

export enum InvoiceStatusType {
    UNBOOKED,// 0
    BOOKED,// 1
    COMPLETED// 2
}

export enum InvoiceCustomerStatusType {
    OPEN,// 1
    REMINDER_1,// 2
    REMINDER_2,// 3
    DEMAND,// 4
    PAID,// 5
    CANCELED,// 6
    REFUNDED// 7
}

export enum InvoiceAgentStatusType {
    OPEN,// 1
    REMINDER_1,// 2
    REMINDER_2,// 3
    DEMAND,// 4
    PAID,// 5
    CANCELED,// 6
    REFUNDED// 7
}

export enum InvoiceInsuranceStatusType {
    OPEN,// 1
    PAIED//2
}

export enum InvoiceType {
    FRIST_INVOICE,
    ADDENDUM,
    BILL,
    CREDIT
}
