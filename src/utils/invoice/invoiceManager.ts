import { CalculationParametersData, ContractData } from "@/types";
import { InvoiceData, InvoiceSequenceData, InvoiceDetailStatusData, InvoicePositionData } from "./types";
import { calculateInvoiceDays, calculatePrice, getTax } from "../calculationUtil";
import { formatLabel } from "../keyFormatter";
import { createInvoicePDF } from "../pdfManager";
import { dbWriter } from "../dbWriter";

class InvoiceManager {

    async getInvoice(token: string, invoice_number: string) {
        const invoiceData: InvoiceData = (await this.fetchDataFromAPI(
            `${process.env.STRAPI_BASE_URL}/invoices?filters[invoice_number][$eq]=${invoice_number}`,
            'invoices',
            token
        )).data[0];
        return invoiceData;
    }

    async createEntry(token: string, invoiceData: InvoiceData, invoiceNumber?: string): Promise<InvoiceData> {

        let sequence = await invoiceManager.getSequence(
            invoiceData.contract_number,
            process.env.STRAPI_API_TOKEN!,
        );

        if (!sequence) {
            await invoiceManager.createSequence(process.env.STRAPI_API_TOKEN!, invoiceData.contract_number);
            sequence = await invoiceManager.getSequence(invoiceData.contract_number, process.env.STRAPI_API_TOKEN!);
        }

        if (invoiceNumber == undefined) {
            invoiceNumber = invoiceManager.generateInvoiceNumber(
                invoiceData.contract_number,
                sequence.sequence,
            );
        }



        const newInvoiceData = await this.fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoices`,
            'invoices',
            token,
            'POST',
            JSON.stringify({
                data: {
                    'contract_number': invoiceData.contract_number,
                    'version_number': 0,
                    'invoice_detail_status': invoiceData.invoice_detail_status,
                    'invoice_status': invoiceData.invoice_status,
                    'agent_status': invoiceData.agent_status,
                    'customer_status': invoiceData.customer_status,
                    'insurance_status': invoiceData.insurance_status,
                    'invoice_number': invoiceNumber,
                    'due_date': invoiceData.due_date,
                    'total_net': invoiceData.total_net,
                    'total_gross': invoiceData.total_gross,
                    'positions': invoiceData.positions,
                    'billing_street': invoiceData.billing_street,
                    'billing_house_number': invoiceData.billing_house_number,
                    'billing_city': invoiceData.billing_city,
                    'billing_postal_code': invoiceData.billing_postal_code,
                    'billing_care_of': invoiceData.billing_care_of,
                    'first_name': invoiceData.first_name,
                    'last_name': invoiceData.last_name,
                    'name_prefix': invoiceData.name_prefix,
                    'payment_mode': invoiceData.payment_mode,
                    'insurance_start_date': invoiceData.insurance_start_date,
                    'insurance_end_date': invoiceData.insurance_end_date,
                    'iban': invoiceData.iban,
                    'bic': invoiceData.bic,
                    'agent_street': invoiceData.agent_street,
                    'agent_house_number': invoiceData.agent_house_number,
                    'agent_city': invoiceData.agent_city,
                    'agent_postal_code': invoiceData.agent_postal_code,
                    'agent_company_name': invoiceData.agent_company_name,
                    'agent_number': invoiceData.agent_number,
                    'customer_number': invoiceData.customer_number,
                    'type': invoiceData.type,
                    'subject': invoiceData.subject,
                    'automatically_generated': invoiceData.automatically_generated,
                    'agency_number': invoiceData.agency_number
                }
            })
        );

        await invoiceManager.updateSequenceInDB(
            token,
            sequence,
            invoiceData.contract_number,
        );

        return newInvoiceData.data
    }

    async createSequence(token: string, contractNumber: string) {
        await this.fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoice-sequences`,
            'invoice-sequence',
            token,
            'POST',
            JSON.stringify({
                data: {
                    'contract_number': contractNumber,
                    'sequence': 0,
                }
            })
        );
    }

    async updateSequenceInDB(token: string, sequenceData: InvoiceSequenceData, contractNumber: string) {
        const newSequenceNumber = Number(sequenceData.sequence) + 1
        await this.fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoice-sequences/${sequenceData.documentId}`,
            'invoice-sequences',
            token,
            'PUT',
            JSON.stringify({
                data: {
                    'contract_number': contractNumber,
                    'sequence': newSequenceNumber
                }
            })
        );
    }

    generatePositions(insurance_start_date: string, contractData: ContractData, calculationParameters: CalculationParametersData) {

        const taxRates: Record<string, number> = {
            hausrat: calculationParameters.hausrat_tax,
            wohngebaeude: calculationParameters.wohngebaude_tax,
            tierhalterhaftpflicht: calculationParameters.tierhalterhaftpflicht_tax,
            privathaftpflicht: calculationParameters.privathaftpflicht_tax,
            haus_und_grundbesitzerhaftpflicht: calculationParameters.haus_und_grundbesitzerhaftpflicht_tax,
            bauleistung: calculationParameters.bauleistung_tax,
            bauherrenhaftpflicht: calculationParameters.bauherrenhaftpflicht_tax,
            geschaeftsversicherung: calculationParameters.geschaeftsversicherung_tax,
            gebaeudeversicherung: calculationParameters.gebaeudeversicherung_tax,
            betriebshaftpflicht: calculationParameters.betriebshaftpflicht_tax,
            unfallversicherung: calculationParameters.unfall_tax
        };
        const rate = taxRates[contractData.contract_type];

        const invoiceDays = calculateInvoiceDays(
            insurance_start_date,
            contractData.insurance_end_date,
            contractData.insurance_main_due_date!,
            contractData.payment_mode!
        );

        const invoice_net = contractData.premie! / 365 * invoiceDays;
        const invoice_tax = invoice_net * rate;
        let invoice_gross = invoice_net + invoice_tax;

        let invoice_glass_net = 0;
        let invoice_glass_tax = 0;
        let invoice_glass_gross = 0;

        if (contractData.glass_insurance) {
            invoice_glass_net = contractData.premie_glass_insurance! / 365 * invoiceDays
            invoice_glass_tax = invoice_glass_net * rate;
            invoice_glass_gross = invoice_glass_net + invoice_glass_tax;
            invoice_gross += invoice_glass_gross;
        }


        let positions: InvoicePositionData[] = [{ "name": formatLabel(contractData.contract_type), "net": parseFloat(calculatePrice(contractData.premie!, contractData.payment_mode).toFixed(2)), "tax": parseFloat(getTax(calculationParameters, contractData.contract_type).toFixed(2)), "tax_amount": parseFloat(calculatePrice(contractData.tax!, contractData.payment_mode).toFixed(2)) }];
        if (contractData.glass_insurance)
            positions = [
                { "name": formatLabel(contractData.contract_type), "net": parseFloat(calculatePrice(contractData.premie!, contractData.payment_mode).toFixed(2)), "tax": parseFloat(getTax(calculationParameters, contractData.contract_type).toFixed(2)), "tax_amount": parseFloat(calculatePrice(contractData.tax!, contractData.payment_mode).toFixed(2)) },
                { "name": "Gebäudeglasversicherung", "net": parseFloat(calculatePrice(contractData.premie_glass_insurance!, contractData.payment_mode).toFixed(2)), "tax": parseFloat(getTax(calculationParameters, "wohngebaeude_glass").toFixed(2)), "tax_amount": parseFloat(calculatePrice(contractData.glass_tax!, contractData.payment_mode).toFixed(2)) }
            ];
        return positions;
    }

    generatePositionsFirstInvoice(contractData: ContractData, calculationParameters: CalculationParametersData) {
        let positions: InvoicePositionData[] = [{ "name": formatLabel(contractData.contract_type), "net": parseFloat(contractData.first_invoice_net!.toFixed(2)), "tax": parseFloat(getTax(calculationParameters, contractData.contract_type).toFixed(2)), "tax_amount": parseFloat(contractData.first_invoice_tax!.toFixed(2)) }];
        if (contractData.glass_insurance)
            positions = [
                { "name": formatLabel(contractData.contract_type), "net": parseFloat(contractData.first_invoice_net!.toFixed(2)), "tax": getTax(calculationParameters, contractData.contract_type), "tax_amount": parseFloat(contractData.first_invoice_tax!.toFixed(2)) },
                { "name": "Gebäudeglasversicherung", "net": parseFloat(contractData.first_invoice_glass_net!.toFixed(2)), "tax": getTax(calculationParameters, "wohngebaeude_glass"), "tax_amount": parseFloat(contractData.first_invoice_glass_tax!.toFixed(2)) }
            ];
        return positions;
    }

    async getSequence(contract_number: string, token: string): Promise<InvoiceSequenceData> {
        const sequenceResponse = await this.fetchDataFromAPI(`${process.env.STRAPI_BASE_URL}/invoice-sequences?filters[contract_number][$eq][0]=${contract_number}`, 'invoice-sequence', token, 'GET');
        const sequenceData: InvoiceSequenceData = sequenceResponse.data[0];
        return sequenceData;
    }

    async generatePDFBytes(invoiceData: InvoiceData, preview?: boolean) {
        return await createInvoicePDF(invoiceData, preview);
    }

    generateInvoiceNumber(contractNumber: string, sequenceNumber: number) {
        let newSequenceNumber = 1;
        if (sequenceNumber)
            newSequenceNumber = Number(sequenceNumber) + 1
        return `${contractNumber}-${newSequenceNumber}`;
    }

    async updateInvoice(token: string, documentId: string, invoiceData: InvoiceData): Promise<InvoiceData> {
        return dbWriter.updateDocument(token, "invoices", documentId, invoiceData, "invoice", 'invoice_number');
    }


    async fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
        try {
            const response = body ? await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: body
            }) :
                await fetch(url, {
                    method: method,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

            if (!response.ok) {
                const errorDetails = await response.json();
                console.error(`${dataType} Fetch Error:`, errorDetails);
                throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
            }

            const data = await response.json();
            return data || null;
        } catch (error) {
            console.error(`Error fetching ${dataType} data:`, error);
            throw error;
        }
    }
}

export const invoiceManager = new InvoiceManager();