import {
    type CalculationParameter,
    type Contract,
    type Invoice,
    type InvoiceSequence,
    type PrismaClient,
} from '@/generated/prisma-postgres';
import { InvoiceService } from '@/server/invoice/InvoiceService';
import { InvoiceSequenceService } from '@/server/sequence/InvoiceSequenceService';
import {
    calculateInvoiceDays,
    calculatePrice,
    getTax,
} from '@/utils/calculationUtil';
import { formatLabel } from '@/utils/keyFormatter';
import { createInvoicePDF } from '@/utils/pdfManager';

import { type InvoiceData, type InvoicePositionData } from './types';

class InvoiceManager {
    async getInvoice(token: string, invoice_number: string) {
        const invoiceData: InvoiceData = (
            await this.fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/invoices?filters[invoice_number][$eq]=${invoice_number}`,
                'invoices',
                token
            )
        ).data[0];
        return invoiceData;
    }

    async createEntry(
        db: PrismaClient,
        invoiceData: Invoice,
        invoiceNumber?: string
    ): Promise<Invoice> {
        let sequence = await invoiceManager.getSequence(
            invoiceData.contractNumber!,
            db
        );

        if (invoiceNumber == undefined) {
            invoiceNumber = invoiceManager.generateInvoiceNumber(
                invoiceData.contractNumber!,
                sequence ? sequence.sequence! : 0
            );
        }

        const invoiceService = new InvoiceService(db);
        const newInvoice = invoiceService.create({
            contractNumber: invoiceData.contractNumber,
            versionNumber: 0,
            invoiceDetailStatus: invoiceData.invoiceDetailStatus as any,
            invoiceStatus: invoiceData.invoiceStatus,
            agentStatus: invoiceData.agentStatus,
            customerStatus: invoiceData.customerStatus,
            insuranceStatus: invoiceData.insuranceStatus,
            invoiceNumber: invoiceNumber,
            dueDate: invoiceData.dueDate,
            totalNet: invoiceData.totalNet,
            totalGross: invoiceData.totalGross,
            positions: invoiceData.positions as any,
            billingStreet: invoiceData.billingStreet,
            billingHouseNumber: invoiceData.billingHouseNumber,
            billingCity: invoiceData.billingCity,
            billingPostalCode: invoiceData.billingPostalCode,
            billingCareOf: invoiceData.billingCareOf,
            firstName: invoiceData.firstName,
            lastName: invoiceData.lastName,
            namePrefix: invoiceData.namePrefix,
            paymentMode: invoiceData.paymentMode,
            insuranceStartDate: invoiceData.insuranceStartDate,
            insuranceEndDate: invoiceData.insuranceEndDate,
            iban: invoiceData.iban,
            bic: invoiceData.bic,
            agentStreet: invoiceData.agentStreet,
            agentHouseNumber: invoiceData.agentHouseNumber,
            agentCity: invoiceData.agentCity,
            agentPostalCode: invoiceData.agentPostalCode,
            agentCompanyName: invoiceData.agentCompanyName,
            agentNumber: invoiceData.agentNumber,
            customerNumber: invoiceData.customerNumber,
            type: invoiceData.type,
            subject: invoiceData.subject,
            automaticallyGenerated: invoiceData.automaticallyGenerated,
            agencyNumber: invoiceData.agencyNumber,
            agency: { connect: { agencyNumber: invoiceData.agencyNumber! } },
            agent: { connect: { agentNumber: invoiceData.agentNumber! } },
            customer: { connect: { customerNumber: invoiceData.customerNumber! } },
            contract: { connect: { contractNumber: invoiceData.contractNumber! } },
        });

        if (!sequence) {
            sequence = await invoiceManager.createSequence(
                db,
                invoiceData.contractNumber!,
                invoiceData.contractId!
            );
        } else {
            await invoiceManager.updateSequenceInDB(
                db,
                sequence!,
                sequence.documentId!
            );
        }

        return newInvoice;
    }

    async createSequence(
        db: PrismaClient,
        contractNumber: string,
        contractId: number
    ) {
        const sequenceService = new InvoiceSequenceService(db);
        return await sequenceService.create({
            contractNumber: contractNumber,
            sequence: 1,
            contract: { connect: { id: contractId } },
        })!;
    }

    async updateSequenceInDB(
        db: PrismaClient,
        sequenceData: InvoiceSequence,
        documentId: string
    ) {
        const invoiceSequenceService = new InvoiceSequenceService(db);
        invoiceSequenceService.update(
            { documentId: documentId },
            { sequence: sequenceData.sequence }
        );
    }

    generatePositions(
        insuranceStartDate: string,
        contractData: Contract,
        calculationParameters: CalculationParameter
    ) {
        const taxRates: Record<string, number> = {
            hausrat: calculationParameters.hausratTax!,
            wohngebaeude: calculationParameters.wohngebaudeTax!,
            tierhalterhaftpflicht: calculationParameters.tierhalterhaftpflichtTax!,
            privathaftpflicht: calculationParameters.privathaftpflichtTax!,
            haus_und_grundbesitzerhaftpflicht:
                calculationParameters.hausUndGrundbesitzerhaftpflichtTax!,
            bauleistung: calculationParameters.bauleistungTax!,
            bauherrenhaftpflicht: calculationParameters.bauherrenhaftpflichtTax!,
            geschaeftsversicherung: calculationParameters.geschaeftsversicherungTax!,
            gebaeudeversicherung: calculationParameters.gebaeudeversicherungTax!,
            betriebshaftpflicht: calculationParameters.betriebshaftpflichtTax!,
            unfallversicherung: calculationParameters.unfallTax!,
        };
        const rate = taxRates[contractData.contractType!];

        const invoiceDays = calculateInvoiceDays(
            insuranceStartDate,
            contractData.insuranceEndDate!,
            contractData.insuranceMainDueDate!,
            contractData.paymentMode!
        );

        const invoice_net = (contractData.premie! / 365) * invoiceDays;
        const invoice_tax = invoice_net * rate;
        let invoice_gross = invoice_net + invoice_tax;

        let invoice_glass_net = 0;
        let invoice_glass_tax = 0;
        let invoice_glass_gross = 0;

        if (contractData.glassInsurance) {
            invoice_glass_net =
                (contractData.premieGlassInsurance! / 365) * invoiceDays;
            invoice_glass_tax = invoice_glass_net * rate;
            invoice_glass_gross = invoice_glass_net + invoice_glass_tax;
            invoice_gross += invoice_glass_gross;
            console.log(invoice_gross)
        }

        let positions: InvoicePositionData[] = [
            {
                name: formatLabel(contractData.contractType!),
                net: parseFloat(
                    calculatePrice(
                        contractData.premie!,
                        contractData.paymentMode!
                    ).toFixed(2)
                ),
                tax: parseFloat(
                    getTax(calculationParameters, contractData.contractType!).toFixed(2)
                ),
                tax_amount: parseFloat(
                    calculatePrice(contractData.tax!, contractData.paymentMode!).toFixed(
                        2
                    )
                ),
            },
        ];
        if (contractData.glassInsurance)
            positions = [
                {
                    name: formatLabel(contractData.contractType!),
                    net: parseFloat(
                        calculatePrice(
                            contractData.premie!,
                            contractData.paymentMode!
                        ).toFixed(2)
                    ),
                    tax: parseFloat(
                        getTax(calculationParameters, contractData.contractType!).toFixed(2)
                    ),
                    tax_amount: parseFloat(
                        calculatePrice(
                            contractData.tax!,
                            contractData.paymentMode!
                        ).toFixed(2)
                    ),
                },
                {
                    name: 'Gebäudeglasversicherung',
                    net: parseFloat(
                        calculatePrice(
                            contractData.premieGlassInsurance!,
                            contractData.paymentMode!
                        ).toFixed(2)
                    ),
                    tax: parseFloat(
                        getTax(calculationParameters, 'wohngebaeude_glass').toFixed(2)
                    ),
                    tax_amount: parseFloat(
                        calculatePrice(
                            contractData.glassTax!,
                            contractData.paymentMode!
                        ).toFixed(2)
                    ),
                },
            ];
        return positions;
    }

    generatePositionsFirstInvoice(
        contractData: Contract,
        calculationParameters: CalculationParameter
    ) {
        let positions: InvoicePositionData[] = [
            {
                name: formatLabel(contractData.contractType!),
                net: parseFloat(contractData.firstInvoiceNet!.toFixed(2)),
                tax: parseFloat(
                    getTax(calculationParameters, contractData.contractType!).toFixed(2)
                ),
                tax_amount: parseFloat(contractData.firstInvoiceTax!.toFixed(2)),
            },
        ];
        if (contractData.glassInsurance)
            positions = [
                {
                    name: formatLabel(contractData.contractType!),
                    net: parseFloat(contractData.firstInvoiceNet!.toFixed(2)),
                    tax: getTax(calculationParameters, contractData.contractType!),
                    tax_amount: parseFloat(contractData.firstInvoiceTax!.toFixed(2)),
                },
                {
                    name: 'Gebäudeglasversicherung',
                    net: parseFloat(contractData.firstInvoiceGlassNet!.toFixed(2)),
                    tax: getTax(calculationParameters, 'wohngebaeude_glass'),
                    tax_amount: parseFloat(contractData.firstInvoiceGlassTax!.toFixed(2)),
                },
            ];
        return positions;
    }

    async getSequence(contractNumber: string, db: PrismaClient) {
        const sequenceService = new InvoiceSequenceService(db);
        const sequenceData = sequenceService.getSequence({ contractNumber });
        return sequenceData;
    }

    async generatePDFBytes(invoiceData: Invoice, preview?: boolean) {
        return await createInvoicePDF(invoiceData, preview);
    }

    generateInvoiceNumber(contractNumber: string, sequenceNumber: number) {
        let newSequenceNumber = 1;
        if (sequenceNumber) newSequenceNumber = Number(sequenceNumber) + 1;
        return `${contractNumber}-${newSequenceNumber}`;
    }

    async updateInvoice(
        db: PrismaClient,
        documentId: string,
        invoiceData: any
    ): Promise<Invoice> {
        const invoiceService = new InvoiceService(db);
        return invoiceService.update({ documentId }, invoiceData);
    }

    async fetchDataFromAPI(
        url: string,
        dataType: string,
        token: string,
        method: string = 'GET',
        body?: any
    ) {
        try {
            const response = body
                ? await fetch(url, {
                    method: method,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                    body: body,
                })
                : await fetch(url, {
                    method: method,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

            if (!response.ok) {
                const errorDetails = await response.json();
                console.error(`${dataType} Fetch Error:`, errorDetails);
                throw new Error(
                    `Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`
                );
            }

            const data = await response.json();
            return data || null;
        } catch (error) {
            console.error(`Error fetching ${dataType} data:`, error);
            throw error;
        }
    }
}

export const invoiceManager = new InvoiceManager();
