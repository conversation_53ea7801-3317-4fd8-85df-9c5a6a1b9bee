/**
 * Converts a Node.js stream to base64
 * @param stream - The Node.js readable stream
 * @returns Promise<string> - The base64 encoded string
 */
export async function streamToBase64(
  stream: NodeJS.ReadableStream
): Promise<string> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];

    stream.on('data', (chunk: Buffer) => {
      chunks.push(chunk);
    });

    stream.on('end', () => {
      const buffer = Buffer.concat(chunks);
      const base64String = buffer.toString('base64');
      resolve(base64String);
    });

    stream.on('error', (error) => {
      reject(error);
    });
  });
}
