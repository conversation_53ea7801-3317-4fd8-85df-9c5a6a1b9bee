export function formatEuro(value: number): string {
  // format number to decimal comma and thousand dot
  return value.toLocaleString("de-DE", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

export const formatDate = (stringified: string) => {
  const intl = new Intl.DateTimeFormat("de-DE", {
    day: "2-digit",
    month: "2-digit",
    year: 'numeric',
  })
  return intl.format(new Date(stringified))
}