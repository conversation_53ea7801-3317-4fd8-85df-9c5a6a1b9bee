import { getSession } from "next-auth/react";


// --- apiFetch overloads ---
export function apiFetch<T>(
    url: string,
    opts?: RequestInit & { accessToken?: string }
): Promise<T>;

export function apiFetch(
    url: string,
    opts: RequestInit & { accessToken?: string; raw: true }
): Promise<Response>;

// --- single implementation ---
export async function apiFetch<T>(
    url: string,
    opts: RequestInit & { accessToken?: string; raw?: boolean } = {}
): Promise<T | Response> {
    const { headers, raw, ...rest } = opts;
    let { accessToken } = opts;

    if (!accessToken) {
        const session = await getSession();
        accessToken = session?.accessToken;
    }

    const res = await fetch(url, {
        ...rest,
        headers: {
            ...(headers || {}),
            ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
            ...(rest.body && !(rest.body instanceof FormData)
                ? { "Content-Type": "application/json" }
                : {}),
        },
    });

    if (!res.ok) throw new Error(await res.text());
    return raw ? res : (res.json() as Promise<T>);
}
