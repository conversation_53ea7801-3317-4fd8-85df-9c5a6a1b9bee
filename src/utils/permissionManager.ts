class PermissionManager {
  async getAuthUser(token: string) {
    const user = await fetch(
      `${process.env.STRAPI_BASE_URL}/users/me?populate=agency`,
      {
        method: 'GET',
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    if (!user.ok) {
      return undefined;
    }
    const userJson = await user.json();
    userJson['agentNumber'] = userJson['agent_number'];
    return userJson;
  }

  async isAdmin(token: string): Promise<boolean> {
    const authUser = await this.getAuthUser(token);
    return authUser['agency']['is_admin'] == true;
  }

  isUserAdmin(authUser: any): boolean {
    return authUser['agency']['is_admin'] == true;
  }

  async hasContractPermissions(contract_number: string, token: string) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    // Fetch contract data
    const contract = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'contract',
        token
      )
    )?.[0];

    if (!contract) {
      return false;
    }

    return contract_number == contract.contract_number;
  }

  async hasCustomerPermissions(customer_number: string, token: string) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    // Fetch customer data
    const customer = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/customers?filters[customer_number][$eq][0]=${customer_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'customer',
        token
      )
    )?.[0];

    if (!customer) {
      return false;
    }

    return customer_number == customer.customer_number;
  }

  async hasReportPermissions(report_number: string, token: string) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    // Fetch report data
    const report = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/reports?filters[report_number][$eq][0]=${report_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'report',
        token
      )
    )?.[0];

    if (!report) {
      return false;
    }

    return report_number == report.report_number;
  }

  async hasAgentPermissions(agent_number: string, token: string) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    // Fetch agent data
    const agent = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/users?filters[agent_number][$eq][0]=${agent_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'agent',
        token
      )
    )?.[0];

    if (!agent) {
      return false;
    }

    return agent_number == agent.agent_number;
  }

  async hasInvoicePermissions(documentId: string, token: string) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    const invoice = await fetchDataFromAPI(
      // TODO: potential bug
      `${process.env.STRAPI_BASE_URL}/invoices/${documentId}`,
      'contract',
      token
    );

    // Fetch contract data
    const contract = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${invoice.contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'contract',
        token
      )
    )?.[0];

    if (!contract) {
      return false;
    } else {
      return true;
    }
  }

  async hasInvoicePermissionsInvoiceNumber(
    invoice_number: string,
    token: string
  ) {
    const authUser = await this.getAuthUser(token);
    if (this.isUserAdmin(authUser)) return true;

    const invoice = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/invoices/?filters[invoice_number][$eq][0]=${invoice_number}`,
        'contract',
        token
      )
    )?.[0];

    // Fetch contract data
    const contract = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${invoice.contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'contract',
        token
      )
    )?.[0];

    if (!contract) {
      return false;
    } else {
      return true;
    }
  }

  async hasUserContractPermissions(
    authUser: any,
    contract_number: string,
    token: string
  ) {
    if (this.isUserAdmin(authUser)) return true;

    // Fetch contract data
    const contract = (
      await fetchDataFromAPI(
        `${process.env.STRAPI_BASE_URL}/contracts?filters[contract_number][$eq][0]=${contract_number}&filters[agency_number][$eq][1]=${authUser['agency_number']}`,
        'contract',
        token
      )
    )?.[0];

    if (!contract) {
      return false;
    }

    return contract_number == contract.contract_number;
  }
}

export const permissionManager = new PermissionManager();

async function fetchDataFromAPI(url: string, dataType: string, token: string) {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorDetails = await response.json();
      console.error(`${dataType} Fetch Error:`, errorDetails);
      throw new Error(
        `Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`
      );
    }

    const data = await response.json();
    return data.data || null;
  } catch (error) {
    console.error(`Error fetching ${dataType} data:`, error);
    throw error;
  }
}
