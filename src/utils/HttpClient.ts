type RequestConfig = Omit<RequestInit, 'body'> & {
    body?: any,
    baseUrl?: string;
    raw?: boolean
}

class HttpClient {
    constructor() {}

    request<T = any>(url: string, config?: Omit<RequestConfig, 'raw'>): Promise<T>
    request(url: string, config: RequestConfig & { raw: true }): Promise<Response>

    async request<T = any>(url: string, config: RequestConfig = {}): Promise<T | Response> {
        const finalUrl = `${config.baseUrl ?? ''}${url}`
        const finalConfig = {
            ...config,
            body: config.body ? JSON.stringify(config.body) : undefined,
            headers: {
                'Content-Type': 'application/json',
                ...config.headers,
            },
        }

        const response = await fetch(finalUrl, finalConfig);
        if (!response.ok) {
            return this.handleErrorResponse(response)
        }

        return config.raw ? response : response.json();
    }

    private async handleErrorResponse(response: Response): Promise<never> {
        const text = await response.text()
        let json: any

        try {
            json = JSON.parse(text)
        } catch {
            throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`)
        }

        const error = new Error(`Failed to fetch: ${text}`)
        const {errorCode} = json

        if (errorCode) {
            Object.assign(error, { code: errorCode })
        }
        throw error
    }
}

const httpClient = new HttpClient()

export default httpClient