type RequestConfig = Omit<RequestInit, 'body'> & {
  body?: any;
  baseUrl?: string;
};

export class HttpClient {
  constructor(private readonly token: string) {}

  async request(url: string, config: RequestConfig = {}) {
    const finalUrl = `${config.baseUrl ?? ''}${url}`;
    const finalConfig = {
      ...config,
      body: config.body ? JSON.stringify(config.body) : undefined,
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...config.headers,
      },
    };

    const response = await fetch(finalUrl, finalConfig);
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${await response.text()}`);
    }

    return response.json();
  }

  async strapiRequest(url: string, config: RequestConfig = {}) {
    const finalConfig = {
      ...config,
      body: config.body ? { data: config.body } : undefined,
      baseUrl: process.env.STRAPI_BASE_URL,
    };
    const json = await this.request(url, finalConfig);

    return json.data;
  }
}
