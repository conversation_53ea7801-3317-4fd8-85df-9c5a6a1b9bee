import { Prisma } from '@prisma/client';

export interface FrontendError {
  code: string;
  field: string;
}

export function PrismaErrorToFrontendError(
  prismaError:
    | Prisma.PrismaClientKnownRequestError
    | Prisma.PrismaClientValidationError
): FrontendError | null {
  if (typeof prismaError !== 'undefined') {
    if (prismaError instanceof Prisma.PrismaClientKnownRequestError) {
      if (
        typeof prismaError.code === 'string' &&
        Array.isArray(prismaError?.meta?.target) &&
        prismaError?.meta?.target.length > 0
      ) {
        return {
          code: prismaError.code,
          field: prismaError?.meta?.target[0],
        };
      }
    } else if (prismaError instanceof Prisma.PrismaClientValidationError) {
      return {
        code: 'validation',
        field: '',
      };
    }
  }
  return null;
}

export function GetTextForFrontendError(t: any, error: FrontendError): string {
  try {
    if (typeof error.code === 'string' && error.code.trim().length > 0) {
      if (typeof error.field === 'string' && error.field.trim().length > 0) {
        return `${t(`errors.prisma.${error.code}.${error.field}`)}`;
      } else {
        return `${t(`errors.prisma.${error.code}`)}`;
      }
    }
  } catch (_) {
    console.error('GetTextForFrontendError', error);
  }
  return 'Error!';
}
