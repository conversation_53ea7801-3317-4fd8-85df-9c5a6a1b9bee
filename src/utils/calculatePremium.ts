export interface PremiumInput {
  livingAreaMain: number;
  deductible: "keine" | "150 €";
  constructionYear: number;
  paymentInterval:
    | "jährlich"
    | "halbjährlich"
    | "vierteljährlich"
    | "monatlich";
  zuersZone: 1 | 2 | 3 | 4;
  stormZone: 1 | 2;
  waterZone: 1 | 2 | 3 | 4;
  coverageWater?: boolean;
  coverageStorm?: boolean;
  extendedPipeCoverage?: boolean;
  unnamedRisks?: boolean;
  glassInsurance?: boolean;
  maxPowerKWp?: number;
}

export interface PremiumResult {
  basePremium: number;
  totalPremium: number;
  details: Record<string, number | string>;
}

// Values
const ZONE_FACTORS: Record<number, Record<number, number>> = {
  1: {
    // Storm-Zone 1
    1: 1.461,
    2: 1.6262,
    3: 2.0038,
    4: 2.3793,
  },
  2: {
    // Storm-Zone 2
    1: 1.6301,
    2: 1.8818,
    3: 2.2535,
    4: 2.633,
  },
};

const ZUERS_ZONE_FACTORS: Record<number, number | null> = {
  1: 0.2897,
  2: 0.3354,
  3: 5.445,
  4: null, // nicht zeichnbar
};

const ADDONS = {
  ableitungsrohre: 0.1839,
  unbenannteGefahren: 0.1126,
  photovoltaik: 0.2346,
  glas150: 79.3,
  glas250: 112.48,
};

const DEDUCTIBLE_DISCOUNT = 0.25;
const MIN_PREMIUM = 100;
const PAYMENT_SURCHARGE = {
  jährlich: 0,
  halbjährlich: 0.03,
  vierteljährlich: 0.05,
  monatlich: 0.05,
};

// New building Discount depending on age
const getNewBuildingDiscount = (buildingAge: number): number => {
  if (buildingAge <= 3) return 0.5;
  if (buildingAge <= 6) return 0.45;
  if (buildingAge <= 10) return 0.4;
  if (buildingAge <= 18) return 0.2;
  if (buildingAge <= 19) return 0.15;
  if (buildingAge <= 20) return 0.1;
  return 0;
};

// Main function
export function calculatePremium(input: PremiumInput): PremiumResult {
  const currentYear = new Date().getFullYear();
  const buildingAge = currentYear - input.constructionYear;

  // 1. Base premium (z. B. 1,2 € pro m² as Placeholder)
  const baseRate = 1.2;
  const basePremium = input.livingAreaMain * baseRate;

  // 2. Zone Factor
  const zoneFactor = ZONE_FACTORS[input.stormZone][input.waterZone];
  let premium = basePremium * zoneFactor;
  
  const zuersFactor = ZUERS_ZONE_FACTORS[input.zuersZone];
  if (zuersFactor === null) {
    throw new Error(
      `Baustein Elementar kann in Zone ${input.zuersZone} nicht gezeichnet werden.`
    );
  }
  premium *= zuersFactor;

  // 3. Addons
  if (input.extendedPipeCoverage) premium += premium * ADDONS.ableitungsrohre;
  if (input.unnamedRisks) premium += premium * ADDONS.unbenannteGefahren;
  if (input.maxPowerKWp && input.maxPowerKWp > 0)
    premium += premium * ADDONS.photovoltaik;
  if (input.glassInsurance) {
    premium +=
      input.livingAreaMain <= 150
        ? ADDONS.glas150
        : input.livingAreaMain <= 250
          ? ADDONS.glas250
          : ADDONS.glas250 + 40; // larger 250 m² = Surcharge
  }

  // 4. Disount New Building
  const newBuildingDiscount = getNewBuildingDiscount(buildingAge);
  premium *= 1 - newBuildingDiscount;

  // 5. Disount deductible
  if (input.deductible === "150 €") premium *= 1 - DEDUCTIBLE_DISCOUNT;

  // 6. Surcharge because of paymentInterval
  premium *= 1 + PAYMENT_SURCHARGE[input.paymentInterval];

  // 7. Minimum Premium
  premium = Math.max(premium, MIN_PREMIUM);

  return {
    basePremium: Math.round(basePremium * 100) / 100,
    totalPremium: Math.round(premium * 100) / 100,
    details: {
      zoneFactor,
      newBuildingDiscount,
      deductible: input.deductible,
      zuersZone: input.zuersZone,
    },
  };
}
