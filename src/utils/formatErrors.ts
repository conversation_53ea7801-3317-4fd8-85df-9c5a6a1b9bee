import type { FieldErrors } from 'react-hook-form';

import { humanizeFieldName } from './humanizeFieldName';

export type ErrorMessage = {
  field: string;
  label: string;
  message: string | undefined;
  category?: string;
  index?: number;
};

type ErrorValue = {
  message?: string;
  type?: string;
  ref?: unknown;
} & Record<string, unknown>;

/**
 * Formats react-hook-form errors into a structured array of error messages
 * Handles nested objects, arrays, and complex field structures
 *
 * @param errors - FieldErrors object from react-hook-form
 * @param translations - Optional translation function for field labels
 * @returns Array of formatted error messages with field, label, message, category, and index
 *
 * @example
 * const errors = {
 *   firstName: { message: 'Required' },
 *   outpatientDiagnoses: [
 *     { message: 'Required' },
 *     { message: 'Too short' }
 *   ]
 * };
 *
 * const formatted = formatErrors(errors);
 * // [
 * //   { field: 'firstName', label: 'First Name', message: 'Required' },
 * //   { field: 'outpatientDiagnoses[0]', label: 'Outpatient Diagnoses', message: 'Required', category: 'Outpatient Diagnoses', index: 0 },
 * //   { field: 'outpatientDiagnoses[1]', label: 'Outpatient Diagnoses', message: 'Too short', category: 'Outpatient Diagnoses', index: 1 }
 * // ]
 */
export const formatErrors = <T extends Record<string, unknown>>(
  errors: FieldErrors<T>,
  translations?: (key: string) => string
): ErrorMessage[] => {
  const formattedErrors: ErrorMessage[] = [];

  const processErrors = (
    obj: FieldErrors<T> | ErrorValue | Array<ErrorValue>,
    parentField = '',
    parentLabel = ''
  ): void => {
    if (!obj || typeof obj !== 'object') {
      return;
    }

    Object.entries(obj).forEach(([key, value]) => {
      if (!key || value === undefined) {
        return;
      }

      const currentField = parentField ? `${parentField}.${key}` : key;
      const isArrayField = currentField.includes('[');
      const arrayMatch = currentField.match(/\[(\d+)\]/);
      const arrayIndex = arrayMatch ? parseInt(arrayMatch[1]) : undefined;

      // Extract category name from array fields (e.g., 'outpatientDiagnoses[0]' -> 'outpatientDiagnoses')
      const categoryName = currentField.split('[')[0];
      const categoryLabel = translations
        ? translations(categoryName)
        : humanizeFieldName(categoryName);

      if (Array.isArray(value)) {
        // Handle array of errors
        value.forEach((item, index) => {
          if (item) {
            processErrors(item, `${currentField}[${index}]`, categoryLabel);
          }
        });
      } else if (value && typeof value === 'object') {
        const errorValue = value as ErrorValue;
        if (errorValue.message) {
          // This is a leaf error node
          formattedErrors.push({
            field: currentField,
            label: translations ? translations(key) : humanizeFieldName(key),
            message: errorValue.message,
            category: isArrayField ? categoryLabel : undefined,
            index: arrayIndex,
          });
        } else {
          // Continue traversing nested objects
          processErrors(errorValue, currentField, parentLabel);
        }
      }
    });
  };

  try {
    processErrors(errors);
  } catch (error) {
    console.error('Error processing form errors:', error);
  }

  // Sort errors: first by category, then by index
  return formattedErrors.sort((a, b) => {
    if (a.category && b.category) {
      if (a.category !== b.category) {
        return a.category.localeCompare(b.category);
      }
      return (a.index || 0) - (b.index || 0);
    }
    if (a.category) return 1;
    if (b.category) return -1;
    return 0;
  });
};
