import { type useTranslations } from 'next-intl';

import { type KvcFormFieldProps } from '@/components/forms/domain';

type TranslatorObject = ReturnType<typeof useTranslations>;

export const JsonUtils = {
  ProcessValuesArraysPathsAppendToObjectAsKeyValue(
    valuesArraysPaths: any,
    targetObject: any,
    t: TranslatorObject
  ) {
    if (
      valuesArraysPaths &&
      Array.isArray(valuesArraysPaths) &&
      valuesArraysPaths.length > 0 &&
      typeof targetObject === 'object'
    ) {
      valuesArraysPaths.forEach((path: string) => {
        if (typeof path === 'string' && path.trim().length > 0) {
          const rawStringArray = t.raw(path);
          if (Array.isArray(rawStringArray)) {
            rawStringArray.forEach((optionString: string) => {
              if (
                typeof optionString === 'string' &&
                optionString.trim().length > 0
              ) {
                targetObject[optionString] = optionString;
              }
            });
          }
        }
      });
    }
  },

  ProcessValuesArraysPathsAppendToStringArray(
    valuesArraysPaths: any,
    t: TranslatorObject
  ): string[] {
    const result: string[] = [];
    if (
      valuesArraysPaths &&
      Array.isArray(valuesArraysPaths) &&
      valuesArraysPaths.length > 0
    ) {
      valuesArraysPaths.forEach((path: string) => {
        if (typeof path === 'string' && path.trim().length > 0) {
          const rawStringArray = t.raw(path);
          if (Array.isArray(rawStringArray)) {
            rawStringArray.forEach((optionString: string) => {
              if (
                typeof optionString === 'string' &&
                optionString.trim().length > 0
              ) {
                result.push(optionString);
              }
            });
          }
        }
      });
    }
    return result;
  },
};

export function GetListOfInvisibleFields(
  data: any,
  configs: KvcFormFieldProps[]
): string[] {
  const result: string[] = [];
  if (
    typeof configs === 'object' &&
    Array.isArray(configs) &&
    typeof data === 'object' &&
    Object.keys(data).length > 0
  ) {
    configs.forEach((config) => {
      if (typeof config.id === 'string') {
        const isVisible = FieldIsVisible(data, config);
        if (isVisible === false) {
          result.push(config.id);
        }
      }
    });
  }
  return result;
}

export function FieldIsVisible(data: any, config: KvcFormFieldProps): boolean {
  let result = true;
  if (typeof config === 'object' && typeof data === 'object') {
    if (config.disabled === true) {
      return false;
    }
    if (typeof config.showIf === 'string') {
      if (typeof data[config.showIf] === 'undefined') {
        result = false;
      }
    } else if (typeof config.showIf === 'object') {
      for (const [key, value] of Object.entries(config.showIf)) {
        if (data[key] !== value) {
          result = false;
          break;
        }
      }
    }

    if (typeof config.showIfNot === 'string') {
      if (typeof data[config.showIfNot] !== 'undefined') {
        result = false;
      }
    } else if (typeof config.showIfNot === 'object') {
      for (const [key, value] of Object.entries(config.showIfNot)) {
        if (data[key] === value) {
          result = false;
          break;
        }
      }
    }
  }

  return result;
}
