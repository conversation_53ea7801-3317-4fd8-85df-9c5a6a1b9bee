// src/utils/pdfPicker.ts

// path map
const labelMap: { [key: string]: string } = {
  hausrat: 'household_insurance.pdf',
  wohngebaeude: 'residential_building_insurance.pdf',
  tierhalterhaftpflicht: 'animal_liability_insurance.pdf',
  privathaftpflicht: 'personal_liability_insurance.pdf',
  haus_und_grundbesitzerhaftpflicht: 'home_and_landowner_liability.pdf',
  bauleistung: 'construction_insurance.pdf',
  bauherrenhaftpflicht: 'construction_owner_liability.pdf',
  geschaeftsversicherung: 'business_insurance.pdf',
  gebaeudeversicherung: 'building_insurance.pdf',
  betriebshaftpflicht: 'business_liability_insurance.pdf',
  unfallversicherung: 'accident_insurance.pdf', //TODO CHANGE PDF FILE, THIS ONE IS ONLY FOR TESTING
  invoice: 'invoice_alpha.pdf',
};

// function to change `contract_type` into a url
export const formatUrl = (contractType: string): string => {
  return (
    labelMap[contractType] ||
    contractType.charAt(0).toUpperCase() + contractType.slice(1)
  );
};
