import nodemailer, { SendMailOptions } from "nodemailer";

let transporter: nodemailer.Transporter;

// reusable transporter (created only once)
function getTransporter() {
  if (!transporter) {
    transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,          
      port: Number(process.env.SMTP_PORT),  
      secure: false, 
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }
  return transporter;
}

export async function sendEmail(options: SendMailOptions) {
  const transporter = getTransporter();

  const info = await transporter.sendMail({
    from: `"Tippgeber Portal" <${process.env.MAIL_FROM}>`,
    ...options,
  });

  if (process.env.NODE_ENV !== "production") {
    console.log("📧 Mail Preview URL: %s", nodemailer.getTestMessageUrl(info));
  }

  return info;
}
