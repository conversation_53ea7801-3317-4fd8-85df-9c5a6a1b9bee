import { ContractData, CustomerData, ReportData } from '@/types';
import nodemailer, { Transporter } from 'nodemailer';
import { Readable } from 'stream';
import { formatLabel, formatLetterSalutation } from './keyFormatter';
import { fileManager } from './fileManager';
import path from 'path';
import fs from 'fs';
import { formatDate } from './dateUtils';

// MailManager class for sending emails
class MailManager {
  private transporter: Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_SERVER,
      port: process.env.SMTP_PORT,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
      },
    });
  }

  /**
   * Sends an email using the transporter.
   * @param to - Recipient email address(es).
   * @param subject - Email subject.
   * @param html - Email content in HTML format.
   * @param cc - Optional carbon copy (CC).
   * @param attachments - Optional attachments (Stream).
   */
  async sendMail(
    to: string | string[],
    subject: string,
    html: string,
    cc?: string | string[],
    attachments?: { filename: string; content: Buffer | Readable | string }[]
  ): Promise<void> {

    if (process.env.NEXT_PUBLIC_MAIL_SERIVCE == "false") {
      console.log("Mail service deaktivated!")
      return
    }

    if (process.env.DEV_MODE == "true") {
      to = process.env.DEV_MAIL_TO!;
      cc = "";
    }

    try {
      const mailOptions = {
        from: process.env.SMTP_USER,
        to,
        cc,
        subject,
        html,
        attachments: attachments?.map((attachment) => ({
          filename: attachment.filename + ".pdf",
          content: attachment.content, // This can be a stream, buffer, or string
          contentType: 'application/pdf' // TODO: Hardcoded contentType - should be changed
        })),
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log(`Email sent: ${info.messageId}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Sends a damage report email
   */
  async sendReport({
    subject,
    customer,
    reportData,
    contract_number
  }: {
    subject: string;
    customer: CustomerData;
    reportData: ReportData;
    contract_number: string;
  }) {
    const templatePath = path.join(process.cwd(), 'public', 'mailTemplates', 'reportTemplate.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    // Replace the placeholders
    html = html.replaceAll(/{{customer_name}}/g, customer.first_name + " " + customer.last_name);
    html = html.replaceAll(/{{report_number}}/g, reportData.report_number);
    html = html.replaceAll(/{{customer_number}}/g, customer.customer_number);
    html = html.replaceAll(/{{damage_date}}/g, formatDate(reportData.createdAt).split(",")[0]);
    html = html.replaceAll(/{{details}}/g, reportData.text);
    html = html.replaceAll(/{{contract_number}}/g, contract_number);

    try {
      await this.sendMail(process.env.MAIL_SEND_TO_SUPPORT, subject, html);
    } catch (error) {
      console.error('Error sending report email:', error);
      throw new Error('Failed to send report email');
    }
  }

  /**
   * Sends a damage special email (example "Unfallversicherung")
   */
  async sendSpecial({
    subject,
    contract_number,
    customer_data,
    text
  }: {
    subject: string;
    text: string;
    contract_number: string;
    customer_data: CustomerData;
  }) {
    const html = `
    <!DOCTYPE html>
    <html lang="de">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
           }
          .container {
            width: 90%;
            margin-left: auto;
            margin-right: auto;
            max-width: 600px;
            padding: 20px;
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 0.375rem;
          }
          .header {
            color: #2D5BAA;
            padding: 20px;
            text-align: center;
            font-size: 24px;
          }
          .content {
            margin: 20px 0;
          }
          .field {
            margin-bottom: 10px;
          }
          .label {
            display: block;
            margin-bottom: 5px;
          }
          .value {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }
          .center {
            display: block;
            margin-left: auto;
            margin-right: auto;
          }
        </style>
      </head>
      <body>
      <div class=content>
        <div class="header">
          Anfrage Unfallversicherung
        </div>
        <div class="field">
          Kundennummer: ${customer_data.customer_number}
        </div>
        <div class="field">
          Vorgeschlagene Angebotsnummer: ${contract_number}
        </div>
        <div class="field">
          Anrede: ${customer_data.salutation}
        </div>
        <div class="field">
          Vorname: ${customer_data.first_name}
        </div>
        <div class="field">
          Nachname: ${customer_data.last_name}
        </div>
        <div class="field">
          c/o: ${customer_data.care_of}
        </div>
        <div class="field">
          Straße: ${customer_data.street}
        </div>
        <div class="field">
          Hausnummer: ${customer_data.house_number}
        </div>
        <div class="field">
          Postleitzahl: ${customer_data.postal_code}
        </div>
        <div class="field">
          Ort: ${customer_data.city}
        </div>
        <div class="field">
          E-Mail: ${customer_data.email}
        </div>
        <div class="field">
          Anfrage: ${text}
        </div>
    </div>
</body>
</html>
      `;

    try {
      await this.sendMail(process.env.MAIL_SEND_TO_SUPPORT, subject, html);
    } catch (error) {
      console.error('Error sending report email:', error);
      throw new Error('Failed to send report email');
    }
  }


  /**
   * Sends an email with contract details
   */
  async sendPolice(
    customer: CustomerData,
    contractData: ContractData,
    agentEmail: string
  ) {
    const templatePath = path.join(process.cwd(), 'public', 'mailTemplates', 'contractTemplate.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    // Replace the placeholders
    html = html.replaceAll(/{{starting_date}}/g, formatDate(contractData.insurance_start_date).split(",")[0]);
    html = html.replaceAll(/{{end_date}}/g, formatDate(contractData.insurance_end_date).split(",")[0]);
    html = html.replaceAll(/{{contract_type}}/g, formatLabel(contractData.contract_type).split(",")[0]);
    html = html.replaceAll(/{{contract_number}}/g, contractData.contract_number);

    // Replace letter salutation
    html = html.replaceAll(/{{letter_salutation}}/g, `${formatLetterSalutation(customer.salutation)} ${customer.last_name}`);

    const pdfStream = await fileManager.downloadFile(contractData.contract_number);
    const subject = formatLabel(contractData.contract_type);
    await this.sendMail(agentEmail, "Vertrag " + subject, html, undefined, [
      {
        filename: `${formatLabel(contractData.contract_type)} - ${contractData.contract_number}`,
        content: pdfStream,
      },
    ]);
  }

  /**
   * Sends an offer email
   */
  async sendOffer(
    customer: CustomerData,
    contractData: ContractData,
    agentEmail: string
  ) {
    const templatePath = path.join(process.cwd(), 'public', 'mailTemplates', 'offerTemplate.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    // Replace the placeholders
    html = html.replaceAll(/{{starting_date}}/g, formatDate(contractData.insurance_start_date).split(",")[0]);
    html = html.replaceAll(/{{end_date}}/g, formatDate(contractData.insurance_end_date).split(",")[0]);
    html = html.replaceAll(/{{contract_type}}/g, formatLabel(contractData.contract_type).split(",")[0]);
    html = html.replaceAll(/{{contract_number}}/g, contractData.contract_number);

    // Replace letter salutation
    html = html.replaceAll(/{{letter_salutation}}/g, `${formatLetterSalutation(customer.salutation)} ${customer.last_name}`);

    const pdfStream = await fileManager.downloadFile(contractData.contract_number);
    const subject = formatLabel(contractData.contract_type);
    await this.sendMail(agentEmail, "Angebot " + subject, html, undefined, [
      {
        filename: `${formatLabel(contractData.contract_type)} - ${contractData.contract_number}`,
        content: pdfStream,
      },
    ]);
  }

  /**
   * Sends an invoice email
   */
  async sendInvoice( // TODO: change from email address to finance.
    customer: CustomerData,
    contractData: ContractData,
    invoice_number: string,
    agentEmail: string
  ) {
    const due_date = invoice_number?.slice(-10).slice(0, -2)
    const templatePath = path.join(process.cwd(), 'public', 'mailTemplates', 'invoicetemplate.html');
    let html = fs.readFileSync(templatePath, 'utf8');
    const formattedDueDate = due_date?.split("-").reverse().join(".");

    // Replace the placeholders
    html = html.replaceAll(/{{starting_date}}/g, formatDate(contractData.insurance_start_date).split(",")[0]);
    html = html.replaceAll(/{{end_date}}/g, formatDate(contractData.insurance_end_date).split(",")[0]);
    html = html.replaceAll(/{{contract_type}}/g, formatLabel(contractData.contract_type).split(",")[0]);
    html = html.replaceAll(/{{invoice_number}}/g, invoice_number);
    html = html.replaceAll(/{{due_date}}/g, formattedDueDate);

    // Replace letter salutation
    html = html.replaceAll(/{{letter_salutation}}/g, `${formatLetterSalutation(customer.salutation)} ${customer.last_name}`);

    const pdfStream = await fileManager.downloadFile(invoice_number);
    const subject =  `Rechnung ${formatLabel(contractData.contract_type)} vom ${formattedDueDate}`;
    await this.sendMail(agentEmail, subject, html, undefined,[
      {
        filename: `Rechnung - ${invoice_number}`,
        content: pdfStream,
      },
    ]);
  }

  /**
   * Sends an email using sendMail.
   */
  async sendRegistrationMail(
    agent_number: string,
    agency_number: string,
    agent_data: any
  ) {
    const html = `
    <!DOCTYPE html>
    <html lang="de">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
           }
          .container {
            width: 90%;
            margin-left: auto;
            margin-right: auto;
            max-width: 600px;
            padding: 20px;
            border: 1px solid #ddd;
            background-color: #fff;
            border-radius: 0.375rem;
          }
          .header {
            color: #2D5BAA;
            padding: 20px;
            text-align: center;
            font-size: 24px;
          }
          .content {
            margin: 20px 0;
          }
          .field {
            margin-bottom: 10px;
          }
          .label {
            display: block;
            margin-bottom: 5px;
          }
          .value {
            padding: 10px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }
          .center {
            display: block;
            margin-left: auto;
            margin-right: auto;
          }
        </style>
      </head>
      <body>
      <div class=content>
        <div class="header">
          Eingehende Registrierung
        </div>
        <div class="field">
          Agenturnummer: ${agency_number}
        </div>
        <div class="field">
          Maklernummer: ${agent_number}
        </div>
        <div class="field">
          Anrede: ${agent_data.salutation}
        </div>
        <div class="field">
          Vorname: ${agent_data.first_name}
        </div>
        <div class="field">
          Nachname: ${agent_data.last_name}
        </div>
        <div class="field">
          Straße: ${agent_data.street}
        </div>
        <div class="field">
          Hausnummer: ${agent_data.house_number}
        </div>
        <div class="field">
          Postleitzahl: ${agent_data.postal_code}
        </div>
        <div class="field">
          Ort: ${agent_data.city}
        </div>
        <div class="field">
          E-Mail: ${agent_data.email}
        </div>
        <div class="field">
          Telefonnummer: ${agent_data.telephone_number}
        </div>
        <div class="field">
          URL: ${agent_data.url}
        </div>
    </div>
</body>
</html>
    `
    const subject = "Registration " + agent_number
    this.sendMail(process.env.MAIL_SEND_TO_SUPPORT, subject, html)
  }
}

// Export a singleton instance of MailManager
export const mailManager = new MailManager();
