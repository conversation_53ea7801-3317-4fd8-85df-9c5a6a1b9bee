// lib/mongo.ts  –  connection helper (Next.js style)
import { MongoClient } from 'mongodb'
const uri = process.env.MONGODB_URI!
let mongoDBClientPromise: Promise<MongoClient>

if (process.env.NODE_ENV === 'development') {
  // keeps one socket alive during hot-reloads
  // @ts-expect-error type not known
  global._mongoClientPromise ??= new MongoClient(uri).connect()
  // @ts-expect-error type not known
  mongoDBClientPromise = global._mongoClientPromise
} else {
    mongoDBClientPromise = new MongoClient(uri).connect()
}
export default mongoDBClientPromise
