// src/utils/mongoDB/mongoDBFunctions.ts
// import { Prisma } from "@/generated/prisma-mongo";

import { type Prisma } from '@prisma/client';
import { diff } from 'jsondiffpatch';

import { type PolicyContext } from '@/server/infra/policy';

export type RevisionModel = 'agent' | 'contract' | 'customer' | 'invoice';

export async function writeRevision({
  model,
  before,
  after,
  ctx,
}: {
  model: RevisionModel;
  before: any;
  after: any;
  ctx: PolicyContext;
}) {
  if (!before && !after) return;

  const rawDelta = diff(before, after);
  if (!rawDelta) return;

  const deltaJson = JSON.parse(
    JSON.stringify(rawDelta)
  ) as Prisma.InputJsonValue;
  const agentId = ctx.agentNumber ?? 'unknown';

  // switch (
  //   model.toLowerCase() as RevisionModel
  //   // case "agent": {
  //   //   const primaryId = (after ?? before)?.agentNumber as string;
  //   //   await prismaMongo.agentRevision.create({ data: { primaryId, agentId, delta: deltaJson } });
  //   //   return;
  //   // }
  //   // case "contract": {
  //   //   const primaryId = (after ?? before)?.contractNumber as string;
  //   //   await prismaMongo.contractRevision.create({ data: { primaryId, agentId, delta: deltaJson } });
  //   //   return;
  //   // }
  //   // case "customer": {
  //   //   const primaryId = (after ?? before)?.customerNumber as string;
  //   //   await prismaMongo.customerRevision.create({ data: { primaryId, agentId, delta: deltaJson } });
  //   //   return;
  //   // }
  //   // case "invoice": {
  //   //   const primaryId = (after ?? before)?.invoiceNumber as string;
  //   //   await prismaMongo.invoiceRevision.create({ data: { primaryId, agentId, delta: deltaJson } });
  //   //   return;
  //   // }
  // ) {
  // }
}

export async function getRevisions(model: RevisionModel, primaryId: string) {
  // switch (model) {
  //   case "agent":
  //     return prismaMongo.agentRevision.findMany({ where: { primaryId }, orderBy: { createdAt: "desc" } });
  //   case "contract":
  //     return prismaMongo.contractRevision.findMany({ where: { primaryId }, orderBy: { createdAt: "desc" } });
  //   case "customer":
  //     return prismaMongo.customerRevision.findMany({ where: { primaryId }, orderBy: { createdAt: "desc" } });
  //   case "invoice":
  //     return prismaMongo.invoiceRevision.findMany({ where: { primaryId }, orderBy: { createdAt: "desc" } });
  // }
}
