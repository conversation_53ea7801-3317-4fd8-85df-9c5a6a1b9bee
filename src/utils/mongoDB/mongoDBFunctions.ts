// src/utils/mongoDB/mongoDBFunctions.ts
import mongoDBClientPromise from './mongoDBManager';
import { diff } from 'jsondiffpatch'

export async function saveRevision({
    primaryId,              // primary key in Postgres
    agentId,                // who did it
    before,
    after,
    collectionName
}: {
    primaryId: string; agentId: string;
    before: unknown; after: unknown; collectionName: string;
}) {
    const delta = diff(before, after)
    if (!delta) return

    const client = await mongoDBClientPromise
    const result = await client.db('revisions').collection(collectionName).insertOne({
        primaryId, agentId,
        delta,
        createdAt: new Date(),
    })

    if (result.acknowledged != true) {
        console.log('Error creating change log')
    }
}

export async function getRevisions(
    primaryId: string,
    collectionName: string
) {

    const client = await mongoDBClientPromise
    const revisions = await client
        .db('revisions')
        .collection(collectionName)
        .find({ primaryId })
        .sort({ createdAt: -1 })
        .toArray()
    return revisions
}
