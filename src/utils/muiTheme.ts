'use client';

import { createTheme } from '@mui/material/styles';

const variables = {
  columnGap: '20px',
};

const colorTeal = '#2faa97';
const colorTealDark = '#34746a';
const colorTealLight = '#c6f3e4';

const muiTheme = createTheme({
  palette: {
    primary: {
      main: colorTeal, // teal
      dark: colorTealDark, // teal-dark
      light: colorTealLight, // teal-light
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#9e9e9e', // gray-light
      contrastText: '#171717',
    },
    error: {
      main: '#ff7979', // error-red
    },
    background: {
      default: '#ffffff', // background
      paper: '#f4f4f4', // card-background
    },
    text: {
      primary: '#171717', // foreground
      secondary: '#9e9e9e', // gray-light
    },
    separator: {
      dark: '#7B7B7B',
      light: '#D3D3D3',
      main: '#D3D3D3',
    },
    colors: {
      lightGray: '#CECECE',
      warningYellow: '#FFE55F',
    },
    buttons: {
      primary: {
        hover: '#053C34',
      },
    },
  },
  typography: {
    fontFamily: 'Inter, "Open Sans", Arial',
    fontSize: 16,
    button: {
      fontSize: 16,
      textTransform: 'none',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        //  Raw CSS overrides go here if necessary.
      },
    },

    MuiButton: {
      styleOverrides: {
        root: {
          backgroundColor: 'var(--teal-dark)',
          color: 'white',
          '&:hover': {
            backgroundColor: '#053C34',
          },
          padding: '8px 16px',
        },
      },
    },

    MuiListItem: {
      styleOverrides: {
        root: {
          listStyle: 'none',
          width: 'auto',
          height: 'auto',
          padding: 0,
        },
      },
    },

    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: 'none !important',
          '--Paper-shadow': 'none',
          borderRadius: 7,
          '&:hover': {
            boxShadow: 'none !important',
          },
          padding: 24,
          display: 'flex',
          flexDirection: 'column',
          background: 'var(--card-background)',

          '&.disabled': {
            background: 'none',
            border: 'solid 2px var(--card-background)',
          },
        },
      },
    },

    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 57,
          height: 29,
          padding: 0,
          display: 'flex',
        },

        switchBase: {
          padding: 2,
          '&.Mui-checked': {
            transform: 'translateX(28px)',
            '& + .MuiSwitch-track': {
              opacity: 1,
              backgroundColor: 'white',
            },
          },
          '&.Mui-checked .MuiSwitch-thumb': {
            backgroundColor: 'var(--teal-dark)',
          },
        },

        thumb: {
          backgroundColor: 'var(--toggle-off)',
          boxShadow: '0 2px 4px 0 rgb(0 35 11 / 20%)',
          width: 25,
          height: 25,
          borderRadius: 25 / 2,
        },

        track: {
          borderRadius: 29 / 2,
          opacity: 1,
          backgroundColor: 'white',
          border: 'solid 1px var(--toggle-border)',
          boxSizing: 'border-box',
        },
      },
    },

    // MuiTextField: {
    //     styleOverrides: {
    //         root: {
    //             marginTop: 10,
    //             marginBottom: 10,
    //         }
    //     }
    // },

    MuiSelect: {
      styleOverrides: {
        select: {
          textAlign: 'left',
        },
      },
    },

    MuiFormControl: {
      styleOverrides: {
        root: {
          // marginTop: 10,
          // marginBottom: 10,
          '&.FancyForm__formField': {
            // background: 'red',
            display: 'flex',
            flexWrap: 'wrap',
            minWidth: 250,
            maxWidth: '100%',
            flex: '0 1 calc(50% - 20px)',
            // gap: '0 20px',
            // flex: 0,
            // background: 'red',
            '&.formW100': {
              flex: '1 1 100%',
              marginRight: '20px',
            },
          },
        },
      },
      // variants: [
      //     {
      //         props: props => props.className?.includes('FancyForm__formField') ?? false,
      //         style: ({ theme }: any): CSSObject => ({
      //         flexBasis: `calc((100% - ${variables.columnGap}) / 2)`,
      //         margin: 0,
      //         '& .MuiFormLabel-root:not(.MuiFormLabel-filled):not(.Mui-focused)': {
      //             top: '-4px',
      //         },
      //         '& .MuiFormLabel-root.Mui-focused': {
      //             color: 'black',
      //         },
      //         }),
      //     },
      // ],
    },

    MuiStack: {
      styleOverrides: {
        root: {
          '&.FancyForm__root': {
            // background: 'green',
            display: 'flex',
            // flexWrap: 'wrap',
            // minWidth: '50%',
            // gap: '0 20px',
            gap: '20px',
          },
          '&.formW100': {
            flex: '1 1 100%',
            marginRight: '20px',
          },
          variants: [
            {
              props: { variant: 'form' },
              style: {
                display: 'flex',
                flexWrap: 'wrap',
                gap: '0 20px',
                // gap: 20,
                // spacing: 20,
                '& > div': {
                  flex: 1,
                  minWidth: 300,
                  marginTop: '20px',
                  // marginBottom: '20px',
                  // gap: '20px',
                  // background: 'red',
                },
              },
            },
          ],
        },
      },
    },

    MuiAutocomplete: {
      styleOverrides: {
        root: {
          '&.FancyForm__formField': {
            display: 'flex',
            flexWrap: 'wrap',
            minWidth: 100,
            maxWidth: '100%',
            flex: '0 1 calc(50% - 20px)',

            '&.formW100': {
              flex: '1 1 100%',
              marginRight: '20px',
            },
          },
        },
      },
    },

    MuiDivider: {
      styleOverrides: {
        root: {
          backgroundColor: 'var(--teal)',
        },
      },
    },

    MuiRadio: {
      styleOverrides: {
        root: ({ theme }) => ({
          '&.Mui-checked': {
            color: theme.palette.primary.main,
          },
          '&.Mui-checked.Mui-disabled': {
            color: 'gray',
          },
          '&.Mui-disabled': {
            cursor: 'not-allowed',
            pointerEvents: 'auto',
          },
        }),
      },
    },

    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          //   '& input:invalid': {
          //     borderColor: 'red', // for browsers that show border
          //     // border: 'solid 2px red',
          //     boxShadow: '0 0 0 2px rgba(255,0,0,0.3)',
          //   },
          //   '& input:invalid:focus': {
          //     outline: 'none',
          //     boxShadow: '0 0 0 2px rgba(255,0,0,0.6)',
          //   },
          '& input:user-invalid::placeholder': {
            color: '#9c27b0', // your custom color
            opacity: 1, // keep full opacity
          },
          // Target the fieldset when the input is invalid
          '& input:user-invalid + fieldset': {
            borderColor: 'red',
            borderWidth: 2,
          },
          // Optional: keep red on focus too
          '& input:user-invalid:focus + fieldset': {
            borderColor: 'red',
            borderWidth: 2,
          },
        },
        input: {
          '&:user-invalid::placeholder': {
            color: 'red',
            opacity: 1, // ensure full color
          },
        },
      },
    },

    MuiInputLabel: {
      styleOverrides: {
        root: {
          // Target the label when the sibling input is invalid
          '&.Mui-focused': {
            transition: 'color 0.2s ease',
          },
          // This works for outlined variant
          '&:has(+ .MuiOutlinedInput-root input:user-invalid)': {
            color: 'red',
          },
        },
      },
    },
  },
});

export default muiTheme;
