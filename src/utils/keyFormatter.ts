// src/utils/keyFormatter.ts

import { InvoiceAgentStatusType, InvoiceCustomerStatusType, InvoiceInsuranceStatusType, InvoiceStatusType, InvoiceType } from "./invoice/types";

// human readable map
const labelMap: { [key: string]: string } = {
    hausrat: "Hausrat-Versicherung",
    wohngebaeude: "Wohngebäude-Versicherung",
    tierhalterhaftpflicht: "Tierhalter-Haftpflicht",
    privathaftpflicht: "Privathaftpflicht",
    haus_und_grundbesitzerhaftpflicht: "Haus- und Grundbesitzerhaftpflicht",
    bauleistung: "Bauleistung-Versicherung",
    bauherrenhaftpflicht: "Bauherrenhaftpflicht",
    geschaeftsversicherung: "Geschäfts-Versicherung",
    gebaeudeversicherung: "Gewerbe – Gebäude-Versicherung",
    betriebshaftpflicht: "Betriebshaftpflicht",
    unfallversicherung: "Unfallversicherung",
    true: "Ja",
    false: "Nein",
    ACTIVE: "Aktiv",
    INACTIVE: "Inaktiv",
    DELETED: "Gelöscht",
    CANCELED: "Storniert"
};

// function to change `contract_type` into a label
export const formatLabel = (contractType: string): string => {
    return labelMap[contractType] || contractType.charAt(0).toUpperCase() + contractType.slice(1);
};


// human readable map
const columnTitleMap: { [key: string]: string } = {
    contract_number: "Vertragsnummer",
    customer_number: "Kundennummer",
    agency_number: "Agenturnummer",
    agent_number: "Maklernummer",
    contract_type: "Vertragsart",
    invoice_number: "Rechnungsnummer",
    first_name: "Vorname",
    last_name: "Nachname",
    street: "Straße",
    postal_code: "Postleitzahl",
    city: "Ort",
    show_original_offer: "Zeige verwendete Angebote",
    name_prefix: "Titel",
    billing_house_number: "Rechnungsadresse",
    active_status: "Status",
    external_report_number: "Externe Schadennummer",
    company_name: "Agenturname",
    animal_data: "Tierdaten",
    animal_name: "Tiername",
    animal_type: "Tiertyp",
    race: "Rasse"
};

// function to change a column title into a readable label
export const formatField = (columnTitleType: string): string => {
    return columnTitleMap[columnTitleType] || columnTitleType.charAt(0).toUpperCase() + columnTitleType.slice(1);
};

// human readable map
const letterSalutationMap: { [key: string]: string } = {
    "Frau": "Sehr geehrte Frau",
    "Herr": "Sehr geehrter Herr",
    "Frau und Herr": "Sehr geehrte Frau und Herr",
    "Frau und Frau": "Sehr geehrte Damen",
    "Herr und Herr": "Sehr geehrte Herren",
    "Firma": "Sehr geehrte Damen und Herren der Firma"
};

export const formatLetterSalutation = (columnTitleType: string): string => {
    return letterSalutationMap[columnTitleType];
};

const baseLabels = {
    [InvoiceStatusType.UNBOOKED]: 'Ungebucht',
    [InvoiceStatusType.BOOKED]: 'Gebucht',
    [InvoiceStatusType.COMPLETED]: 'Abgeschlossen'
} as const satisfies Record<InvoiceStatusType, string>;

const customerLabels = {
    [InvoiceCustomerStatusType.OPEN]: 'Offen',
    [InvoiceCustomerStatusType.REMINDER_1]: '1. Mahnung',
    [InvoiceCustomerStatusType.REMINDER_2]: '2. Mahnung',
    [InvoiceCustomerStatusType.DEMAND]: 'Letzte Mahnung',
    [InvoiceCustomerStatusType.PAID]: 'Bezahlt',
    [InvoiceCustomerStatusType.CANCELED]: 'Storniert',
    [InvoiceCustomerStatusType.REFUNDED]: 'Erstattet'
} as const satisfies Record<InvoiceCustomerStatusType, string>;

const agentLabels = {
    [InvoiceAgentStatusType.OPEN]: 'Offen',
    [InvoiceAgentStatusType.REMINDER_1]: '1. Mahnung',
    [InvoiceAgentStatusType.REMINDER_2]: '2. Mahnung',
    [InvoiceAgentStatusType.DEMAND]: 'Letzte Mahnung',
    [InvoiceAgentStatusType.PAID]: 'Bezahlt',
    [InvoiceAgentStatusType.CANCELED]: 'Storniert',
    [InvoiceAgentStatusType.REFUNDED]: 'Erstattet'
} as const satisfies Record<InvoiceAgentStatusType, string>;

const insuranceLabels = {
    [InvoiceInsuranceStatusType.OPEN]: 'Offen',
    [InvoiceInsuranceStatusType.PAIED]: 'Bezahlt'
} as const satisfies Record<InvoiceInsuranceStatusType, string>;


export function formatInvoiceStatus(status: InvoiceStatusType): string {
        return baseLabels[status]
}

export function formatInvoiceAgentStatus(status: InvoiceAgentStatusType): string {
    return agentLabels[status]
}

export function formatInvoiceInsuranceStatus(status: InvoiceInsuranceStatusType): string {
    return insuranceLabels[status]
}

export function formatInvoiceCustomerStatus(status: InvoiceCustomerStatusType): string {
    return customerLabels[status]
}

const invoiceTypeLabels = {
    [InvoiceType.FRIST_INVOICE]: 'Erstprämie',
    [InvoiceType.ADDENDUM]: 'Nachtrag',
    [InvoiceType.BILL]: 'Rechnung',
    [InvoiceType.CREDIT]: 'Gutschrift',
} as const satisfies Record<InvoiceType, string>;

export function formatInvoiceType(type: InvoiceType): string {
    return invoiceTypeLabels[type]
}

const reportMap: { [key: string]: string } = {
  // AndereVersicherungForm
  "diff_insurance":                            "Bestehen anderweitige Versicherungen für die vom Schaden betroffenen Sachen?",
  "company_name":                              "Name der Gesellschaft",
  "company_location":                          "Ort der Gesellschaft",
  "company_file_number":                       "Aktenzeichen der Gesellschaft",

  // AngabenForm
  "person_fault":                              "Wer hat den Schaden verschuldet?",
  "person_fault_name":                         "Name",
  "person_fault_address":                      "Adresse",
  "is_damage_same_kind":                       "Wurden Sie bereits von Schäden gleicher Art betroffen?",
  "person_fault_date":                         "Wann",
  "person_fault_damage_amount":                "Schadenhöhe",
  "damage_amount":                             "Wie hoch schätzen Sie den Schaden?",
  "new_value":                                 "Wie hoch schätzen Sie den Neuwert der gesamten versicherten Sachen?",
  "is_entitled_input_tax_deduction":           "Sind Sie vorsteuerabzugsberechtigt (§15 UstG)?",

  // BankangabenForm
  "bank_company":                              "Bankinstitut",
  "bank_iban":                                 "IBAN",
  "bank_bic":                                  "BIC",

  // EigentuemerForm
  "owner":                                     "Wer ist Eigentümer der betroffenen Sachen?",
  "owner_name":                                "Name",
  "owner_address":                             "Adresse",

  // SchadenaufstellungForm
  "damage_date":                               "Schadendatum",
  "damage_location":                           "Schadensort",
  "iban":                                      "IBAN",
  "external_report_number":                    "Schadensnummer der Versicherung",
  "coverd_risk":                               "Versicherte Gefahr",
  "text":                                      "Beschreibung",

  // SchadenOrtForm
  "damage_date_time":                          "Wann ereignete sich der Schaden?",
  "knowledge_damage_date_time":                "Wann erhielten Sie von dem Schaden Kenntnis?",
  "damage_location_room":                      "In welchem Raum ereignete sich der Schaden?",
  "building_type_usage":                       "Gebäudeart und -nutzung",
  "building_owner":                            "Wer ist Eigentümer des Gebäudes oder der Wohnung?",
  "building_owner_name":                       "Name",
  "building_owner_address":                    "Adresse",
  "date_damage_reported":                      "Wann wurde der Schaden gemeldet?",
  "date_damage_reported_person":               "Wem wurde der Schaden gemeldet?",
  "date_damage_reported_police":               "Wann wurde der Polizei Anzeige erstattet?",
  "police_diary_nr":                           "Polizei Tagebuch-Nr.",
  "police_address":                            "Dienststelle/Adresse",

  // SchadenshergangForm
  "course_of_damage":                          "Bitte schildern Sie den Schadenhergang so ausführlich, dass ein möglichst genaues Bild entsteht:",
  "mitigation_measures":                       "Welche Schadenminderungsmaßnahmen wurden ergriffen?",

  // ZusatzfragenForm
  "is_burglary_damage":                        "Einbruchdiebstahl",
  "forced_open":                               "Wurden Behältnisse gewaltsam geöffnet?",
  "forced_open_description":                   "Beschreibung",
  "location_keys":                             "Wo befanden sich die Schlüssel?",
  "burglary_characteristics":                  "Sind Einbruchsmerkmale sichtbar?",
  "burglary_characteristics_description":      "Beschreibung",
  "used_keys":                                 "Welche Mittel haben die Diebe zum Öffnen angewendet?",
  "is_tap_water_damage":                       "Leitungswasserschäden",
  "tap_water_damage_location":                 "An welcher Anlage ist der Schaden entstanden?",
  "tap_water_person":                          "Wer bewohnt die vom Schaden betroffene Wohnung?",
  "tap_water_person_name":                     "Name",
  "is_tap_insured":                            "Hat dieser eine Leitungswasser-Versicherung?",
  "part_building_own_expense":                 "Haben Sie als Mieter Gebäudeteile auf eigene Rechnung eingebracht?",
  "part_building_own_expense_description":     "Welche Gebäudeteile?",
  "part_building_own_expense_amount":          "Wert",
  "is_fe_lw_st":                               "Schäden an Fußböden durch Feuer, Leitungswasser oder Sturm",
  "person_floor_creation":                     "Wer hat den Belag angeschafft?",
  "floor_attachment":                          "Wie ist der Fußboden verlegt?",
  "below_floor_covering":                      "Was befindet sich unter dem Fußbodenbelag?",
  "below_floor_covering_other":                "Anderes",
  "is_glas_damage":                            "Glasschäden",
  "glas_inserted_before_damage":               "War die Scheibe vor dem Schadenereignis fertig eingesetzt?",
  "glas_imperfactions":                        "Sind Mängel an der Umrahmung vorhanden und ist hierauf der Schaden zurückzuführen?",
  "glas_imperfactions_decription":             "Beschreibung",
  "repair_order_booked":                       "Haben Sie den Reparaturauftrag bereits erteilt?",
  "repair_order_company":                      "Firma",
  "glas_compensation_person":                  "Die Entschädigung soll gezahlt werden an",
};


export const formatReportLabel = (key: string): string => {
    return reportMap[key] || key.charAt(0).toUpperCase() + key.slice(1);
};
