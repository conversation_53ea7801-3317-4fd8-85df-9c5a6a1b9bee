import { AgentData, ContractData, CustomerData, ReportData } from "@/types";
import { InvoiceData } from "./invoice/types";
import { saveRevision } from "./mongoDB/mongoDBFunctions";
import { permissionManager } from "./permissionManager";

class DBWriter {

    private allowedFields = {
        invoice: [
            'version_number',
            'invoice_detail_status',
            'invoice_status',
            'agent_status',
            'customer_status',
            'insurance_status',
            'due_date',
            'total_net',
            'total_gross',
            'positions',
            'billing_street',
            'billing_house_number',
            'billing_city',
            'billing_postal_code',
            'first_name',
            'last_name',
            'name_prefix',
            'subject'
        ] as (keyof InvoiceData)[],

        contract: [
            'contract_type',
            'insurance_start_date',
            'insurance_end_date',
            'payment_mode',
            'iban',
            'bic',
            'previous_insurance',
            'previous_insurance_number',
            'previous_claims',
            'is_offer',
            'risk_addresses',
            'building_type',
            'is_permanently_occupied',
            'insurance_sum',
            'covered_risks',
            'is_elementar',
            'coverage_amount',
            'object_type',
            'tariff_group',
            'employee_count',
            'business_type',
            'living_area',
            'insurance_sum_1914',
            'is_construction_year_unknown',
            'construction_year',
            'coverage_usage',
            'household_tech',
            'pv_system',
            'glass_insurance',
            'animal_type',
            'animal_data',
            'private_first_name',
            'private_name',
            'family_coverage',
            'is_single',
            'premium_rate',
            'building_sum',
            'premie',
            'premie_household_tech',
            'premie_pv_system',
            'premie_glass_insurance',
            'additional_agreements',
            'agent_number',
            'tax',
            'invoice_amount',
            'zuers_zone',
            'share_data',
            'is_individual_unit',
            'individual_unit',
            'commission',
            'contract_status',
            'from_offer',
            'is_individually_calculated',
            'glass_tax',
            'active_status',
            'employee_count',
            'risk_addresses',
            'insured_persons',
            'insurance_main_due_date',
            'first_invoice_net',
            'first_invoice_tax',
            'first_invoice_gross',
            'first_invoice_glass_net',
            'first_invoice_glass_tax',
            'first_invoice_glass_gross'
        ] as (keyof ContractData)[],

        customer: [
            'salutation',
            'name_prefix',
            'first_name',
            'last_name',
            'care_of',
            'street',
            'house_number',
            'postal_code',
            'city',
            'email'
        ] as (keyof CustomerData)[],

        report: [
            'text',
            'damage_date',
            'damage_location',
            'coverd_risk',
            'iban',
            'external_report_number',
            'data_raw'
        ] as (keyof ReportData)[],

        agent: [
            "username",
            "company_name",
            "city",
            "street",
            "house_number",
            "postal_code",
            "email",
            "url",
            "telephone_number",
            "commission"
        ] as (keyof AgentData)[],
    };

    private filterAllowedFields<T extends Record<string, any>>(data: T, allowedFields: (keyof T)[]): Partial<T> {
        const filtered: Partial<T> = {};
        for (const key of allowedFields) {
            if (key in data) {
                filtered[key] = data[key];
            }
        }
        return filtered;
    }

    async updateDocument<T extends Record<string, any>>(
        token: string,
        endpoint: string,
        documentId: string,
        dataToUpdate: T,
        type: keyof DBWriter["allowedFields"],
        idFieldName: keyof T
    ): Promise<T> {
        const beforeData: T = await this.getDocument<T>(token, endpoint, type, idFieldName as string, dataToUpdate[idFieldName]);

        const allowedFields = this.allowedFields[type] as (keyof T)[];
        const filteredData = this.filterAllowedFields(dataToUpdate, allowedFields);
        let jsonData: any = { data: filteredData }

        if (idFieldName == 'id') {
            jsonData = filteredData
        }

        const url = idFieldName == 'id' ? `${process.env.STRAPI_BASE_URL}/${endpoint}/${dataToUpdate[idFieldName]}` : `${process.env.STRAPI_BASE_URL}/${endpoint}/${documentId}`;
        let updateResponse = (await this.fetchDataFromAPI(
            url,
            type,
            token,
            'PUT',
            JSON.stringify(jsonData)
        ));

        let pId = dataToUpdate[idFieldName]

        if (idFieldName == 'id') {
            pId = dataToUpdate['agent_number']
        } else {
            updateResponse = updateResponse.data
        }

        const authUser = await permissionManager.getAuthUser(token);

        await saveRevision({
            primaryId: pId,
            agentId: authUser["agent_number"],
            before: beforeData,
            after: updateResponse,
            collectionName: `${type}_revision`
        });

        return updateResponse;
    }

    async getDocument<T>(token: string, endpoint: string, type: string, idFieldNam: string, number: string): Promise<T> {
        let documentData;
        if (idFieldNam == 'id') {
            documentData = await this.fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/${endpoint}/${number}`,
                type,
                token
            );
            return documentData as T;
        } else {
            documentData = await this.fetchDataFromAPI(
                `${process.env.STRAPI_BASE_URL}/${endpoint}?filters[${idFieldNam}][$eq]=${number}`,
                type,
                token
            );
            return documentData.data[0] as T;
        }
    }

    async fetchDataFromAPI(url: string, dataType: string, token: string, method: string = "GET", body?: any) {
        try {
            const response = body ? await fetch(url, {
                method: method,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: body
            }) :
                await fetch(url, {
                    method: method,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

            if (!response.ok) {
                const errorDetails = await response.json();
                console.error(`${dataType} Fetch Error:`, errorDetails);
                throw new Error(`Failed to fetch ${dataType}: ${errorDetails.error?.message || response.statusText}`);
            }

            const data = await response.json();
            return data || null;
        } catch (error) {
            console.error(`Error fetching ${dataType} data:`, error);
            throw error;
        }
    }

}


export const dbWriter = new DBWriter();