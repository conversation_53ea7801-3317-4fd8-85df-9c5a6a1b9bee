// src/types/liability-insurances.ts

export interface CustomerData {
    id: number;
    customer_id: number;                //nur für die nutzung in Verträgen
    salutation: string;                 //Anrede
    name_prefix: string;                //Titel
    first_name: string;                 //Vorname
    last_name: string;                  //Nachname
    care_of: string;                    //c/o
    street: string;                     //Straße
    house_number: string;               //Hausnummer
    postal_code: string;                //Postleitzahl
    city: string;                       //Stadt
    email: string;                      //Emailadresse
    customer_number: string;            //generierte Kundennummer
    agent_number: string;               //Maklernnummer
    documentId: string;                 //Datenbank ID 
}

export interface AgentData {
    id: number;
    documentId: string;
    username: string;
    street: string;                     //Straße
    house_number: string;               //Hausnummer
    postal_code: string;                //Postleitzahl
    city: string;                       //Stadt
    email: string;                      //Emailadresse
    agency_number: string;              //generierte Agenturnummer
    agent_number: string;               //generierte Maklernnummer
    url: string;                        //URL
    telephone_number: string;           //Telefonnummer
    company_name: string;               //Agenturname
    commission: number;                 //Courtage
}

export interface ContractData {

    contractId: number;                 //nur für die nutzung in der Vertragsliste
    active_status: string;              //status of the contract ACTIVE/INACTIVE/DELETED

    //used by all
    customer_number: string;            //zugehörige Kundennummer
    contract_number: string;            //generiert bei Vertragserstellung
    contract_type: string;              //Vertragstyp
    insurance_start_date: string;       //Versicherungsbeginn
    insurance_end_date: string;         //Versicherungsablauf
    insurance_main_due_date: string;    //Hauptfälligkeit
    payment_mode: string;               //Zahlungsweise
    iban: string;                       //IBAN
    bic: string;                        //BIC
    previous_insurance: string;         //Vorversicherer
    previous_insurance_number?: string; //Nr. Vorversicherer
    noPreviousInsurance: boolean;       //Vorversicherer?
    previous_claims: boolean;           //Vorschäden
    is_offer: boolean;                  //Angebot?
    additional_agreements: string;      //Zusatzvereinbarungen
    share_data: ShareData[];            //Feld für die Anteile
    agency_number: string;              //Agenturnummer
    commission: number;                 //Courtage
    contract_status: ContractStatusData;//Versicherungsstatus
    from_offer: string;                 //Ursprungsangebot
    to_contract: string;                //Zielvertrag
    updatedAt?: string;                  //Aktualisierungsdatum
    agent_number?: string                //Maklernnummer
    is_individually_calculated?: boolean //Individuelle Berechnung

    //Used by multiple
    risk_addresses: RiskAddressData[];     //Risikoaddressen
    building_type?: string;             //Bauart
    is_permanently_occupied?: boolean;  //ständig bewohnt?
    insurance_sum?: number;             //Versicherungssumme
    covered_risks: boolean;             //Versicherte Gefahren
    is_elementar: boolean;              //Elementar?
    coverage_amount: string;            //Deckungssumme
    object_type?: string;               //Objektart
    tariff_group?: string;              //Tarifgruppe
    zuers_zone?: string;                //Zürs Zone

    // Hausrat
    living_area?: number;               //Wohnfläche

    //Wohngebäude
    insurance_sum_1914?: number;            //Versicherungssumme 1914
    construction_year?: number;             //Baujahr
    is_construction_year_unknown: boolean;  //Baujahr bekannt?  
    coverage_usage?: number;                //100% Wohnzwecke?
    household_tech?: boolean;               //Haustechnik EXPERT
    pv_system?: boolean;                    //Photovoltaik EXPERT
    glass_insurance?: boolean;              //Glasversicherung
    is_individual_unit?: boolean;           //Individuelle Wohneinheit?
    individual_unit?: string;                //Lage der Wohneinheit

    //Tierhaltung
    animal_type?: string;               //Tierart
    animal_data: AnimalData[];          //Rasse und Name

    //Privathaftpflicht
    private_first_name?: string;        //Vorname (der versicherten Person)
    private_name?: string;              //Nachame (Der Familie oder Versicherten Person)
    family_coverage?: boolean;          //Familien-Deckung?
    is_single?: boolean;                //Singel?

    //Haus- und Grundbesitzerhaftpflicht

    //Bauleistung
    premium_rate?: number;              //Prämiensatz

    //Bauherrenhaftpflicht
    building_sum?: number;              //Bausumme

    //Geschäftversicherung

    //Betriebshaftpflicht
    business_type?: string;             //Betriebsart
    employee_count: number;             //Mitarbeiteranzahl

    //Gebäudeversicherung

    //Unfallversicherung
    insured_persons: AccidentInsuranceFormData[]; //Versicherte Person (Objekt)

    //Calculation results
    premie?: number;                    //Netto-Prämie gesamt
    first_invoice_net?: number;         //Erstprämie Netto
    first_invoice_tax?: number;         //Erstprämie Steuer
    first_invoice_gross?: number;       //Erstprämie Brutto
    first_invoice_glass_net?: number;   //Erstprämie Glas Netto
    first_invoice_glass_tax?: number;   //Erstprämie Glas Steuer
    first_invoice_glass_gross?: number; //Erstprämie Glas Brutto
    premie_household_tech?: number;     //Netto-Prämie Haustechnik Exclusiv
    premie_pv_system?: number;          //Netto-Prämie Photovoltaik Exclusiv
    premie_glass_insurance?: number;    //Netto-Prämie Glasversicherung
    tax?: number;                       //Prämie zuzüglich Versicherungssteuer
    invoice_amount?: number;            //Rechnungsbetrag
    glass_tax?: number;                 //Glasvericherungs Prämie zuzüglich Steuer

    documentId?: string                 //UID
}

export type AnimalData = {
    animal_type: string;                //Tierart
    race: string;                       //Rasse
    animal_name: string;                //Name
};

export interface AgencyData {
    agency_name: string,                //Agenturname
    agency_number: string,              //Agenturnummer
    users_permissions_users: object[],  //Relation zu Benutzern
    is_admin: boolean                   //Admin Berechtigungen
}

export interface CalculationParametersData {
    valid_from: string;                                 //Jahreswert ab dem die Parameter gültig sind

    hausrat_versicherte_gefahren: number;               //Multiplikator versicherten Gefahren, Hausratversicherung
    hausrat_is_elementar: number;                       //Multiplikator Elementarschäden, Hausratversicherung
    hausrat_tax: number;                                //Steueranteil (Prozentsatz), Hausratversicherung
    hausrat_living_area_factor: number;                 //Faktor zur Berechnung der Kosten der Wohnfläche

    wohngebaude_beitragsfaktor: number;                 //Faktor Berechnung Prämie, Wohngebäudeversicherung
    wohngebaude_versicherte_gefahren: number;           //Multiplikator versicherte Gefahren (z. B. Feuer, Sturm), Wohngebäudeversicherung
    wohngebaude_is_elementar: number;                   //Multiplikator Elementarschäden, Wohngebäudeversicherung
    wohngebaude_household_tech: number;                 //Faktor Absicherung von Haustechnik, Wohngebäudeversicherung
    wohngebaude_pv_system: number;                      //Faktor Absicherung Photovoltaik-Anlage
    wohngebaude_glass_insurance: number;                //Faktor Glasversicherung, Wohngebäudeversicherung
    wohngebaude_tax: number;                            //Steueranteil (Prozentsatz), Wohngebäudeversicherung
    wohngebaude_glasversicherung_tax: number;           //Steueranteil für Glasversicherung
    wohngebaude_summenfaktor: number;                   //Faktor zur berechnung der aktuellen Versicherungssumme

    tierhalterhaftpflicht_sale_dog: number;             //Rabattfaktor für zusätzliche Hunde, Tierhalterhaftpflicht
    tierhalterhaftpflicht_sale_horse: number;           //Rabattfaktor für zusätzliche Pferde, Tierhalterhaftpflicht
    tierhalterhaftpflicht_dog_premie: number;           //Prämie pro Hund, Tierhalterhaftpflicht
    tierhalterhaftpflicht_horse_premie: number;         //Prämie pro Pferd, Tierhalterhaftpflicht
    tierhalterhaftpflicht_tax: number;                  //Steueranteil (Prozentsatz), Tierhalterhaftpflichtversicherung

    privathaftpflicht_family_premie: number;            //Prämie für Familien, Privathaftpflichtversicherung
    privathaftpflicht_single_premie: number;            //Prämie für Einzelpersonen, Privathaftpflichtversicherung
    privathaftpflicht_tax: number;                      //Steueranteil (Prozentsatz), Privathaftpflichtversicherung

    haus_und_grundbesitzerhaftpflicht_premie: number;   //Prämie, Haus- und Grundbesitzerhaftpflichtversicherung
    haus_und_grundbesitzerhaftpflicht_tax: number;      //Steueranteil (Prozentsatz), Haus- und Grundbesitzerhaftpflichtversicherung

    bauleistung_satz: number;                           //Prämiensatz pro Versicherungssumme, Bauleistungsversicherung
    bauleistung_min: number;                            //Mindestprämie, Bauleistungsversicherung
    bauleistung_tax: number;                            //Steueranteil (Prozentsatz), Bauleistungsversicherung

    bauherrenhaftpflicht_satz: number;                  //Prämiensatz pro Versicherungssumme, Bauherrenhaftpflicht
    bauherrenhaftpflicht_min: number;                   //Mindestprämie, Bauherrenhaftpflicht
    bauherrenhaftpflicht_tax: number;                   //Steueranteil (Prozentsatz), Bauherrenhaftpflichtversicherung

    geschaeftsversicherung_is_elementar: number;        //Multiplikator Elementarschäden, Geschäftsversicherung
    geschaeftsversicherung_min: number;                 //Mindestprämie, Geschäftsversicherung
    geschaeftsversicherung_handel: number;              //Multiplikator für Handelsunternehmen, Geschäftsversicherung
    geschaeftsversicherung_handwerk: number;            //Multiplikator für Handwerksunternehmen, Geschäftsversicherung
    geschaeftsversicherung_dienstleistungen: number;    //Multiplikator für Dienstleistungsunternehmen, Geschäftsversicherung
    geschaeftsversicherung_gastronomie: number;         //Multiplikator für Gastronomieunternehmen, Geschäftsversicherung
    geschaeftsversicherung_tax: number;                 //Steueranteil (Prozentsatz), Geschäftsversicherung

    gebaeudeversicherung_is_elementar: number;          //Multiplikator für Elementarschäden, Gebäudeversicherung
    gebaeudeversicherung_min: number;                   //Mindestprämie in der Gebäudeversicherung
    gebaeudeversicherung_handel: number;                //Multiplikator für Handelsunternehmen, Gebäudeversicherung
    gebaeudeversicherung_handwerk: number;              //Multiplikator für Handwerksunternehmen, Gebäudeversicherung
    gebaeudeversicherung_dienstleistungen: number;      //Multiplikator für Dienstleistungsunternehmen, Gebäudeversicherung
    gebaeudeversicherung_gastronomie: number;           //Multiplikator für Gastronomieunternehmen, Gebäudeversicherung
    gebaeudeversicherung_tax: number;                   //Steueranteil (Prozentsatz), Gebäudeversicherung

    betriebshaftpflicht_handel: number;                 //Beitrag für Handelsunternehmen, Betriebshaftpflicht
    betriebshaftpflicht_handwerk: number;               //Beitrag für Handwerksunternehmen, Betriebshaftpflicht
    betriebshaftpflicht_dienstleistungen: number;       //Beitrag für Dienstleistungsunternehmen, Betriebshaftpflicht
    betriebshaftpflicht_gastronomie: number;            //Beitrag für Gastronomieunternehmen, Betriebshaftpflicht
    betriebshaftpflicht_tax: number;                    //Steueranteil (Prozentsatz), Betriebshaftpflichtversicherung

    unfall_berufsgruppe_a_mehrleistung: number;         //Multiplikator für Mehrleistung in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_mehrleistung: number;         //Multiplikator für Mehrleistung in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_mehrleistung: number;                 //Multiplikator für Mehrleistung für Kinder, Unfallversicherung
    unfall_senioren_mehrleistung: number;               //Multiplikator für Mehrleistung für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_invaliditaet_225: number;     //Multiplikator für Invalidität 225% in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_invaliditaet_225: number;     //Multiplikator für Invalidität 225% in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_invaliditaet_225: number;             //Multiplikator für Invalidität 225% für Kinder, Unfallversicherung
    unfall_senioren_invaliditaet_225: number;           //Multiplikator für Invalidität 225% für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_invaliditaet_350: number;     //Multiplikator für Invalidität 350% in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_invaliditaet_350: number;     //Multiplikator für Invalidität 350% in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_invaliditaet_350: number;             //Multiplikator für Invalidität 350% für Kinder, Unfallversicherung
    unfall_senioren_invaliditaet_350: number;           //Multiplikator für Invalidität 350% für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_invaliditaet_500: number;     //Multiplikator für Invalidität 500% in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_invaliditaet_500: number;     //Multiplikator für Invalidität 500% in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_invaliditaet_500: number;             //Multiplikator für Invalidität 500% für Kinder, Unfallversicherung
    unfall_senioren_invaliditaet_500: number;           //Multiplikator für Invalidität 500% für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_unfallrente: number;          //Multiplikator für Unfallrente in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_unfallrente: number;          //Multiplikator für Unfallrente in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_unfallrente: number;                  //Multiplikator für Unfallrente für Kinder, Unfallversicherung
    unfall_senioren_unfallrente: number;                //Multiplikator für Unfallrente für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_unfalltod: number;            //Multiplikator für Unfalltod in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_unfalltod: number;            //Multiplikator für Unfalltod in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_unfalltod: number;                    //Multiplikator für Unfalltod für Kinder, Unfallversicherung
    unfall_senioren_unfalltod: number;                  //Multiplikator für Unfalltod für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_krankentagegeld: number;      //Multiplikator für Krankentagegeld in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_krankentagegeld: number;      //Multiplikator für Krankentagegeld in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_krankentagegeld: number;              //Multiplikator für Krankentagegeld für Kinder, Unfallversicherung
    unfall_senioren_krankentagegeld: number;            //Multiplikator für Krankentagegeld für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_krankenhaustagegeld: number;  //Multiplikator für Krankenhaustagegeld mit Genesungsgeld in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_krankenhaustagegeld: number;  //Multiplikator für Krankenhaustagegeld mit Genesungsgeld in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_krankenhaustagegeld: number;          //Multiplikator für Krankenhaustagegeld mit Genesungsgeld für Kinder, Unfallversicherung
    unfall_senioren_krankenhaustagegeld: number;        //Multiplikator für Krankenhaustagegeld mit Genesungsgeld für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_uebergangsleistung: number;   //Multiplikator für Übergangsleistung in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_uebergangsleistung: number;   //Multiplikator für Übergangsleistung in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_uebergangsleistung: number;           //Multiplikator für Übergangsleistung für Kinder, Unfallversicherung
    unfall_senioren_uebergangsleistung: number;         //Multiplikator für Übergangsleistung für Senioren, Unfallversicherung
    unfall_berufsgruppe_a_erste_hilfe: number;          //Prämie für Erste-Hilfe-Baustein in der Berufsgruppe A, Unfallversicherung
    unfall_berufsgruppe_b_erste_hilfe: number;          //Prämie für Erste-Hilfe-Baustein in der Berufsgruppe B, Unfallversicherung
    unfall_kinder_erste_hilfe: number;                  //Prämie für Erste-Hilfe-Baustein für Kinder, Unfallversicherung
    unfall_senioren_erste_hilfe: number;                //Prämie für Erste-Hilfe-Baustein für Senioren, Unfallversicherung
    unfall_tax: number;

    [key: string]: number | string;

}

export interface AttachmentData {
    contract_number: string;            //Vertragsnummer
    bucket_path: string;                //Dateipfad
    createdAt: string;                  //Erstellungdatum
    updatedAt: string;                  //Änderungsdatum
    type: string;                       //File Type
    report_number: string               //Schadensnummer
    documentId: string                  //unique ID
}

export type ReportData = {
    documentId: string;
    contract_number: string;            //Vertragsnummer
    customer_number: string;            //Kundennummer
    text: string;                       //Meldung
    report_number: string;              //Schadensnummer
    external_report_number: string;     //Externe Schadensnummer
    createdAt: string;                  //Erstellungdatum
    damage_date: string                 //Schadensdatum
    damage_location: string             //Schadensort
    iban: string                        //IBAN für die Begleichung des Schadens
    coverd_risk: string                 //Versicherte Gefahr
    data_raw: any
};

export interface ShareData {
    share_type: string;                 //Anteilhaber (Assekuranz/Makler)
    percentage: number;                 //Prozentsatz des Anteils
    insurance?: string;                 //Versicherer
}

export interface ContractStatusData {
    send_email: boolean;                //senden von E-Mail
    send_invoice: boolean;              //senden von Rechnung
    send_automatic_invoice: boolean;    //automatisches senden von Rechnungen
    create_police: boolean;             //erstellen von Police/Angebot
    create_invoice: boolean;            //erstellen von Rechnung
    active: boolean;                    //aktiver Vertrag
}

export interface RiskAddressData {
    street: string;
    house_number: string;
    postal_code: string;
    city: string;
    unit: string;
}

export interface RevisionData {
    primaryId: string,
    agentId: string,
    delta: object,
    createdAt: string
}

export interface AccidentInsuranceFormData {
    first_name: string;                 //Vorname
    last_name: string;                  //Nachname
    birth_date: string;                 //Geburtstag
    type?: string;
    occupation_group?: string;          //Berufsgruppe
    occupation?: string;                //Beruf   
    increased_benefit_clause?: number;  //Mehrleistungsklausel
    disability_coverage?: string;       //Invalidität
    basic_sum?: number;                 //Grundsumme Invalidität
    accident_pension?: number;          //Unfallrente
    accidental_death?: number;          //Unfalltod
    daily_sickness_allowance?: number;  //Krankentagegeld
    hospital_daily_allowance?: number;  //Krankenhaustagegeld mit Genesungsgeld
    transitional_benefit?: number;      //Übergangsleistung
    first_aid_module?: boolean;         //Erste-Hilfe-Baustein
}

export interface AgencyStatisticsData {
    openAmount: number,
    paiedAmount: number,
    openInvoices: number,
    paiedInvoices: number
}

export enum ReportPrecheckStatus {
    EMPTY = 0,
    CHECKED = 1,
    DECLINED = 2,
}
export interface ReportPrecheck {
    id: string
    index: number
    title: string
    status: ReportPrecheckStatus
    timestamp: string
    tooltip: string
    agent_number: string
    author_type: AuthorType
    report_number: string
}
export interface ReportPrecheckTemplate {
    id: string
    insurance_type: string
    title: string
    index: number
    tooltip: string
}

export enum AuthorType {
    AGENT,
    CUSTOMER,
    AI,
}

export enum TimelineEntryType {
    COMMENT,
    DOCUMENTS,
    ACTION,
    SUMMARY,
}
export interface TimelineEntry {
    id: string
    entry_type: TimelineEntryType
    report_number: string
    author_type: AuthorType
    author_id: string // agent_number, customer_number or AI Marker
    timestamp: string
    title: string
    content?: string
    metadata?: string
    related_entries?: { id: string }[]
}

export interface ReportAttachment {
    id: string
    name: string
    bucket_path: string
    related_to: 'report' | 'timeline_entry'
    related_entity_id: string
}

export interface ReportSummary {
    id: string
    report_number: string
    author_type: AuthorType
    text: string
    timestamp: string
}

export interface JobInitResponse {
    job_id: string
    status: string
}

export interface AnalyzeTimelineEntryRequestFile {
    name: string,
    url: string // TODO: remove after removal on AI services side
    base64: string
}
export interface AnalyzeTimelineEntryRequest {
    entry: TimelineEntry & {
        files: AnalyzeTimelineEntryRequestFile[]
    }
    expectedDocumentTypes: string[]
}
export interface TimelineEntryAnalysisJobResponse {
    status: string
    aiSummary: string
    aiCategory: string
    aiConfidence: number
    aiFraudScore: number
}

export interface CreateReportSummaryRequest {
    prechecks: ReportPrecheck[]
    timeline: TimelineEntry[]
    contractSummary: any // TODO: remove after removing validation on AI side
}
export interface ReportSummaryJobResponse {
    status: string
    timelineSummary: string | null
}

export type User = Record<string, any>

export interface EntityMapper<TDomain, TStorage = any> {
    toDomain(storageEntity: TStorage): TDomain;
    toStorage(domainEntity: TDomain): TStorage;
}

export type ContractType =
    | 'hausrat'
    | 'wohngebaeude'
    | 'tierhalterhaftpflicht'
    | 'privathaftpflicht'
    | 'haus_und_grundbesitzerhaftpflicht'
    | 'bauleistung'
    | 'bauherrenhaftpflicht'
    | 'geschaeftsversicherung'
    | 'gebaeudeversicherung'
    | 'betriebshaftpflicht'
    | 'unfallversicherung'

export type InsuranceCondition = {
    id: string
    file_name: string
    bucket_path: string
    relevant_contract_types: ContractType[], // string in Strapi
    product_code: string,
    product_name: string,
    risk_carrier_code: string,
    risk_carrier_name: string,
    valid_from: string,
    valid_to: string,
}

export type OffsetPagination = {
    start?: number
    limit?: number
}
