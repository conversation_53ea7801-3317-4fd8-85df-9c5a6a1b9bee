// types/next-auth.d.ts
import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
    interface Session extends DefaultSession {
        accessToken?: string;
        roles: string[];
        agentNumber?: string;
        agencyNumber?: string;
    }
}

declare module 'next-auth/jwt' {
    interface JWT {
        access_token?: string;
        refresh_token?: string;
        expires_at?: number;
        roles?: string[];
        agentNumber?: string;
        agencyNumber?: string;
    }
}
