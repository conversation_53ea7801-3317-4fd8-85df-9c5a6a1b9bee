// theme.d.ts
import "@mui/material/styles";

declare module "@mui/material/styles" {
  interface Palette {
    separator: {
      light: string;
      main: string;
      dark: string;
    };
    colors: {
        lightGray: string;
        warningYellow: string;
    };
    buttons: {
        primary: {
            hover: string;
        }
    }
  }
  interface PaletteOptions {
    separator?: {
      light?: string;
      main?: string;
      dark?: string;
    };
    colors?: {
        lightGray?: string;
        warningYellow?: string;
    };
    buttons?: {
        primary?: {
            hover?: string;
        }
    };
  }
}
