#refeo
A modern application built with Next.js, MUI and Prisma.

## 🚀 Tech Stack

### Core Framework

- **[Next.js 15.4.6](https://nextjs.org)** - React framework with App Router
- **[React 19.1.0](https://react.dev)** - UI library
- **[TypeScript 5](https://www.typescriptlang.org)** - Type safety

### Backend & API

- **[Prisma 6.14.0](https://prisma.io)** - Database ORM
- **[PostgreSQL](https://postgresql.org)** - Primary database

### UI & Styling

- **[Material-UI 7.3.1](https://mui.com)** - Component library

### Authentication & Internationalization

- **[next-intl 4.3.5](https://next-intl-docs.vercel.app)** - Internationalization

### Development Tools

- **[ESLint 9](https://eslint.org)** - Code linting
- **[Turbopack](https://turbo.build/pack)** - Fast bundler for development

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd kv-care
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .example_env .env.local
   ```

4. **Set up the database**

```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# Seed the database (optional)
npm run seed
```

5. **Start the development server**

   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

6. **Internationalization Setup**

   The application uses next-intl for internationalization. To work with locales:

   ```bash
   # Locale files are located in /messages
   # Currently supports German (de.json)
   # Add new locale files as needed
   ```

   Configure your preferred locale in the application settings or browser preferences.

## Cookie Consent Integration

### Files
- `src/components/CookieConsent.tsx` — banner + modal for cookie consent (Material UI + next-intl).
- Translations: `locales/<lang>/common.json` — keys under `cookieConsent.*`.

### Localization
- The component uses `useTranslations("common")`.
- To add new languages or edit texts, update `locales/<lang>/common.json`.
- Example keys are provided in `locales/de/common.json`.

### Cookie Categories
Currently implemented categories:
- `essential` (always enabled, required for login/session cookies like `referrerSession`)
- `functional`
- `analytics`
- `marketing`

To add new categories:
1. Extend the `ConsentCategories` type in `CookieConsent.tsx`.
2. Add a new toggle in the Dialog UI.
3. Add translation keys in all locales.
4. Update persistence logic if needed.

### Usage in Frontend
Check consent before loading non-essential scripts or setting cookies:

```ts
import { isCategoryAllowed } from "@/components/CookieConsent";

if (isCategoryAllowed("analytics")) {
  // Load analytics script here
}
```

## 📧 Email Templates with react-email

This project uses [react-email](https://react.email/) to build and preview transactional emails (e.g., customer invitations, referrer login links, offer notifications).  
The templates are modular, translatable, and can be tested both locally in development and in production.

---

### 🚀 Local Development & Preview

To test email templates locally, a dedicated preview route is available in Next.js.

1. **Start the local server**
```bash
npm run dev
```
or
```bash
yarn dev
```

2. **Open the preview route**
In your browser, navigate to:
```bash
http://localhost:3000/email/template?locale=de&message=referrerLogin
```
- **locale: language code (e.g., de)**
- **message: email group key (e.g., referrerLogin, customerInvitation, customerOfferNotification, etc.)**

### 📌 Developer Notes

- **Add a new template**

- **Add translations to common.json**

- **Optionally add a renderer in src/emails/renderEmailTemplate.tsx**

- **Preview via the /email/template route**

- **Update an existing template**
Changes are immediately visible at http://localhost:3000/email/template?...

All emails are responsive and share a centralized layout.