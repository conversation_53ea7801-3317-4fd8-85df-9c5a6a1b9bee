# KV Care

A modern application built with Next.js App Router, tRPC, MUI and Prisma.

## 🚀 Tech Stack

### Core Framework

- **[Next.js 15.4.6](https://nextjs.org)** - React framework with App Router
- **[React 19.1.0](https://react.dev)** - UI library
- **[TypeScript 5](https://www.typescriptlang.org)** - Type safety

### Backend & API

- **[tRPC 11.5.0](https://trpc.io)** - End-to-end typesafe APIs
- **[Prisma 6.14.0](https://prisma.io)** - Database ORM
- **[PostgreSQL](https://postgresql.org)** - Primary database
- **[Zod 4.1.4](https://zod.dev)** - Schema validation

### State Management & Data Fetching

- **[TanStack Query 5.85.5](https://tanstack.com/query)** - Server state management
- **[Zustand 5.0.7](https://zustand-demo.pmnd.rs)** - Client state management

### UI & Styling

- **[Material-UI 7.3.1](https://mui.com)** - Component library
- **[Emotion](https://emotion.sh)** - CSS-in-JS styling
- **[Sass 1.90.0](https://sass-lang.com)** - CSS preprocessor

### Authentication & Internationalization

- **[Keycloak 26.2.0](https://keycloak.org)** - Authentication and authorization
- **[next-intl 4.3.5](https://next-intl-docs.vercel.app)** - Internationalization

### Development Tools

- **[ESLint 9](https://eslint.org)** - Code linting
- **[Prettier 3.2.5](https://prettier.io)** - Code formatting
- **[Turbopack](https://turbo.build/pack)** - Fast bundler for development

## 🏗️ Architecture

### App Router Structure

The application uses Next.js App Router with route groups for organized layouts:

```
src/app/
├── (auth)/          # Authentication routes
│   └── login/
├── (dashboard)/     # Main application routes
│   ├── cases/
│   ├── customers/
│   ├── overview/
│   └── start/
├── api/
│   └── trpc/        # tRPC API endpoints
└── layout.tsx       # Root layout
```

### Modular Architecture

Features are organized into self-contained modules:

```
src/modules/
└── case/
    ├── server/      # tRPC routers and server logic
    ├── types/       # TypeScript types and Zod schemas
    ├── utils/       # Helper functions
    ├── hooks/       # Custom React hooks
    └── ui/          # React components and views
        ├── components/
        └── views/
```

### Data Layer

- **Database**: PostgreSQL with Prisma ORM
- **API Layer**: tRPC for type-safe client-server communication
- **State Management**: TanStack Query for server state, Zustand for client state
- **Validation**: Zod schemas for runtime type checking

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd kv-care
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .example_env .env.local
   ```

   Configure your environment variables including:
   - `DATABASE_URL` - PostgreSQL connection string
   - `SITE_URL` - Important for tRPC configuration
   - `STREAM_API_KEY` - Stream Chat API key
   - `STREAM_API_SECRET` - Stream Chat API secret
   - Other required environment variables as specified in `.example_env`

4. **Set up the database**

   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run database migrations
   npx prisma db push

   # Seed the database (optional)
   npm run seed
   ```

5. **Start the development server**

   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

6. **Internationalization Setup**

   The application uses next-intl for internationalization. To work with locales:

   ```bash
   # Locale files are located in /messages
   # Currently supports German (de.json)
   # Add new locale files as needed
   ```

   Configure your preferred locale in the application settings or browser preferences.

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run ts:check` - Run TypeScript type checking
- `npm run lint:format` - Format and lint code
- `npm run seed` - Seed the database with initial data

## 🗄️ Database Schema

The application uses PostgreSQL with the following main entities:

- **Customer** - Customer information and contact details
- **Case** - Case management with status tracking
- **User** - User accounts and authentication
- **Message** - Communication within cases
- **Attachment** - File attachments for cases

## 🔧 Development

### Code Organization

- **Components**: Reusable UI components in `/src/components`
- **Modules**: Self-contained feature packages in `/src/modules` - each module contains ALL related functionality for that feature including tRPC routes/routers, React components, custom hooks, views/pages, TypeScript types and schemas, and any other feature-specific code. This modular architecture ensures better organization and maintainability.
- **Utils**: Shared utilities in `/src/utils`
- **Styles**: Global styles in `/src/styles`
- **Theme**: Material-UI theme configuration in `/src/utils/muiTheme.ts` for consistent styling across the application

### Type Safety

- All API endpoints are fully type-safe using tRPC
- Database queries are type-safe with Prisma
- Runtime validation with Zod schemas
- Strict TypeScript configuration

### Code Quality

- ESLint configuration with Next.js and TypeScript rules
- Prettier for consistent code formatting
- Import sorting and unused import removal
- Path aliases configured (`@/*` → `./src/*`)

## 🌐 Internationalization

The application supports multiple languages using next-intl:

- German (`de`) - Primary language
- Extensible for additional languages

## 🔐 Authentication

Authentication is handled through Keycloak integration, providing:

- Secure user authentication
- Role-based access control
- Session management

## 📦 Deployment

Deployment will be handled via Bitbucket (implementation details to be added later).
