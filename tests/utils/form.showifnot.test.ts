// src/utils/math.test.ts
import { beforeEach } from 'node:test';

import { describe, expect, test } from '@jest/globals';
import { createTranslator } from 'next-intl';

import deMessages from '../../messages/de.json';
import {
  type KvcFormFieldProps,
  KvcInputFieldType,
} from '../../src/components/forms/domain';
import {
  GetFormConfigurationById,
  ValidateFormDataBasedOnFormConfiguration,
} from '../../src/utils/helperTypes';
import { FieldIsVisible } from '../../src/utils/json';

const tNextIntl = createTranslator({
  locale: 'de',
  messages: deMessages,
});

describe('math utils', () => {
  test('isEven() should return true for even numbers', () => {
    expect(2).toBe(2);
  });
});

describe('showIf object', () => {
  let result: Record<string, boolean> = {};
  let data = {};
  let expected: Record<string, boolean> = {};

  beforeEach(() => {
    result = {};
    data = {};
    expected = {};
  });

  test('messages', () => {
    expect(deMessages === null).toBeFalsy();
    expect(deMessages.greeting).toBe('hellow');
  });

  test('intl', () => {
    expect(tNextIntl === null).toBeFalsy();
    expect(typeof tNextIntl === 'undefined').toBeFalsy();
    expect(tNextIntl('greeting')).toBe('hellow');
  });

  test('1', () => {
    const form = GetFormConfigurationById('vorversicherungKrankenversicherung');
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);

    form?.forEach((field) => {
      const fieldIsVisible = FieldIsVisible({}, field);
      //   console.log('isVisible', field.id, fieldIsVisible);
      expect(fieldIsVisible).toBe(true);
    });
  });

  test('test form is not empty', () => {
    const form = testForm;
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);
  });

  test('showIfNot', () => {
    const testField = {
      id: 'dog',
      element: KvcInputFieldType.Text,
      showIfNot: 'cat',
    };
    expect(FieldIsVisible({}, testField)).toBe(true);
    expect(FieldIsVisible({ cat: 'meow' }, testField)).toBe(false);
  });

  test('showIf', () => {
    const testField = {
      id: 'dog',
      element: KvcInputFieldType.Text,
      showIf: 'cat',
    };
    expect(FieldIsVisible({}, testField)).toBe(false);
    expect(FieldIsVisible({ cat: 'meow' }, testField)).toBe(true);
  });

  test('animals', () => {
    data = {};
    expected = {
      dog: true,
      cat: true,
      fish: true,
      puppy: false,
      mouse: true,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      //   console.log(
      //     'expected',
      //     field.id,
      //     fieldIsVisible,
      //     'to be',
      //     expected[field.id]
      //   );
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });

  test('animals2', () => {
    data = {
      dog: 'woof',
    };
    expected = {
      dog: true,
      cat: true,
      fish: true,
      puppy: true,
      mouse: true,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });

  test('animals3', () => {
    data = {
      cat: 'meow',
    };
    expected = {
      dog: false,
      cat: true,
      fish: true,
      puppy: false,
      mouse: false,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });

  test('animals4', () => {
    data = {
      cat: 'purr',
    };
    expected = {
      dog: true,
      cat: true,
      fish: true,
      puppy: false,
      mouse: true,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });

    const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
      data,
      testForm,
      undefined,
      tNextIntl
    );

    expect(required.length).toBe(4);
    expect(bad.length).toBe(3);
    expect(bad.includes('dog')).toBeTruthy();
    expect(bad.includes('fish')).toBeTruthy();
    expect(bad.includes('mouse')).toBeTruthy();
  });

  test('ifnot', () => {
    data = {
      cat: 'meow',
    };
    expected = {
      dog: false,
      cat: true,
      fish: true,
      puppy: false,
      mouse: false,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });

    const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
      data,
      testForm,
      undefined,
      tNextIntl
    );

    // expect(required.length).toBe(4);
    // expect(bad.length).toBe(3);
    // expect(bad.includes('dog')).toBeTruthy();
    // expect(bad.includes('fish')).toBeTruthy();
    // expect(bad.includes('mouse')).toBeTruthy();
  });
  //
});

const testForm: KvcFormFieldProps[] = [
  {
    id: 'dog',
    element: KvcInputFieldType.Text,
    showIfNot: { cat: 'meow' },
    required: true,
  },
  {
    id: 'cat',
    element: KvcInputFieldType.Text,
    required: true,
  },
  {
    id: 'fish',
    element: KvcInputFieldType.Text,
    required: true,
  },
  {
    id: 'puppy',
    element: KvcInputFieldType.Text,
    showIf: { dog: 'woof' },
    required: true,
  },
  {
    id: 'mouse',
    element: KvcInputFieldType.Text,
    showIfNot: { cat: 'meow' },
    required: true,
  },
];
