// src/utils/math.test.ts
import { beforeEach } from 'node:test';

import { describe, expect, test } from '@jest/globals';

import {
  type KvcFormFieldProps,
  KvcInputFieldType,
} from '../../src/components/forms/domain';
import { GetFormConfigurationById } from '../../src/utils/helperTypes';
import { FieldIsVisible } from '../../src/utils/json';

describe('math utils', () => {
  test('isEven() should return true for even numbers', () => {
    expect(2).toBe(2);
  });
});

describe('showIf', () => {
  let result: Record<string, boolean> = {};
  let data = {};
  let expected: Record<string, boolean> = {};

  beforeEach(() => {
    result = {};
    data = {};
    expected = {};
  });

  test('1', () => {
    const form = GetFormConfigurationById('vorversicherungKrankenversicherung');
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);

    form?.forEach((field) => {
      const fieldIsVisible = FieldIsVisible({}, field);
      //   console.log('isVisible', field.id, fieldIsVisible);
      expect(fieldIsVisible).toBe(true);
    });
  });

  test('test form is not empty', () => {
    const form = testForm;
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);
  });

  test('showIfNot', () => {
    const testField = {
      id: 'dog',
      element: KvcInputFieldType.Text,
      showIfNot: 'cat',
    };
    expect(FieldIsVisible({}, testField)).toBe(true);
    expect(FieldIsVisible({ cat: 'meow' }, testField)).toBe(false);
  });

  test('showIf', () => {
    const testField = {
      id: 'dog',
      element: KvcInputFieldType.Text,
      showIf: 'cat',
    };
    expect(FieldIsVisible({}, testField)).toBe(false);
    expect(FieldIsVisible({ cat: 'meow' }, testField)).toBe(true);
  });

  test('animals', () => {
    data = {};
    expected = {
      dog: true,
      cat: true,
      fish: true,
      puppy: false,
      mouse: true,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      //   console.log(
      //     'expected',
      //     field.id,
      //     fieldIsVisible,
      //     'to be',
      //     expected[field.id]
      //   );
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });

  test('animals2', () => {
    data = {
      dog: 'woof',
    };
    expected = {
      dog: true,
      cat: true,
      fish: true,
      puppy: true,
      mouse: true,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });

  test('animals3', () => {
    data = {
      cat: 'meow',
    };
    expected = {
      dog: false,
      cat: true,
      fish: true,
      puppy: false,
      mouse: false,
    };
    testForm.forEach((field) => {
      const fieldIsVisible = FieldIsVisible(data, field);
      result[field.id] = fieldIsVisible;
      expect(fieldIsVisible).toBe(expected[field.id]);
    });
  });
});

const testForm: KvcFormFieldProps[] = [
  {
    id: 'dog',
    element: KvcInputFieldType.Text,
    showIfNot: 'cat',
  },
  {
    id: 'cat',
    element: KvcInputFieldType.Text,
  },
  {
    id: 'fish',
    element: KvcInputFieldType.Text,
  },
  {
    id: 'puppy',
    element: KvcInputFieldType.Text,
    showIf: 'dog',
  },
  {
    id: 'mouse',
    element: KvcInputFieldType.Text,
    showIfNot: 'cat',
  },
];
