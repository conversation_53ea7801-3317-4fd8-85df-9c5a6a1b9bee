// src/utils/math.test.ts
import { beforeEach } from 'node:test';

import { describe, expect, test } from '@jest/globals';

import deMessages from '../../messages/de.json';
import {
  type KvcFormFieldProps,
  KvcInputFieldType,
} from '../../src/components/forms/domain';
import {
  GetFormConfigurationById,
  ValidateFormDataBasedOnFormConfiguration,
} from '../../src/utils/helperTypes';
import { FieldIsVisible } from '../../src/utils/json';
import {
  GetFormConfigurationFromIntlById,
  makeT,
} from '../../src/utils/translation';

// const tNextIntl = createTranslator({
//   locale: 'de',
//   messages: deMessages,
// });

describe('Previous Insurance', () => {
  const t = makeT();
  const testForm: KvcFormFieldProps[] = GetFormConfigurationFromIntlById(
    t,
    'previousInsurance'
  );

  let result: Record<string, boolean> = {};
  let data = {};
  let expected: Record<string, boolean> = {};

  beforeEach(() => {
    result = {};
    data = {};
    expected = {};
  });

  test('messages', () => {
    expect(deMessages === null).toBeFalsy();
    expect(deMessages.greeting).toBe('hellow');
  });

  test('intl', () => {
    expect(t === null).toBeFalsy();
    expect(typeof t === 'undefined').toBeFalsy();
    expect(t('greeting')).toBe('hellow');
  });

  test('1', () => {
    const form = GetFormConfigurationById('vorversicherungKrankenversicherung');
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);

    form?.forEach((field) => {
      const fieldIsVisible = FieldIsVisible({}, field);
      //   console.log('isVisible', field.id, fieldIsVisible);
      expect(fieldIsVisible).toBe(true);
    });
  });

  test('test form is not empty', () => {
    const form = testForm;
    expect(form === null).toBe(false);
    expect(form?.length).toBeGreaterThan(0);
  });

  //   test('showIf', () => {
  //     const testField = {
  //       id: 'dog',
  //       element: KvcInputFieldType.Text,
  //       showIf: 'cat',
  //     };
  //     expect(FieldIsVisible({}, testField)).toBe(false);
  //     expect(FieldIsVisible({ cat: 'meow' }, testField)).toBe(true);
  //   });

  test('Empty data', () => {
    data = {};
    expected = {
      vorversicherungKrankenversicherung: true,
      vorversicherungPflegeversicherung: true,
      vorversicherungZusatzversicherung: true,
    };
    testForm.forEach((field) => {
      if (field.element === KvcInputFieldType.ArrayOf) {
        const fieldIsVisible = FieldIsVisible(data, field);
        result[field.id] = fieldIsVisible;
        //   console.log(
        //     'expected',
        //     field.id,
        //     fieldIsVisible,
        //     'to be',
        //     expected[field.id]
        //   );
        expect(fieldIsVisible).toBe(expected[field.id]);
      }
    });

    const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
      data,
      testForm,
      undefined,
      t
    );
    expect(bad.length).toBe(1);
    expect(bad[0]).toBe('vorversicherungKrankenversicherung');
    expect(required.length).toBe(1);
    expect(required[0]).toBe('vorversicherungKrankenversicherung');
  });

  test('No previous insurance 1', () => {
    data = {
      vorversicherungKrankenversicherung: [
        {
          nameProvider: 'Keine',
        },
      ],
    };
    expected = {
      vorversicherungKrankenversicherung: true,
      vorversicherungPflegeversicherung: true,
      vorversicherungZusatzversicherung: true,
    };
    testForm.forEach((field) => {
      if (field.element === KvcInputFieldType.ArrayOf) {
        const fieldIsVisible = FieldIsVisible(data, field);
        result[field.id] = fieldIsVisible;
        //   console.log(
        //     'expected',
        //     field.id,
        //     fieldIsVisible,
        //     'to be',
        //     expected[field.id]
        //   );
        expect(fieldIsVisible).toBe(expected[field.id]);
      }
    });

    const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
      data,
      testForm,
      undefined,
      t
    );
    // expect(bad).toBe([]);
    expect(bad.length).toBe(0);
    // expect(bad[0]).toBe('vorversicherungKrankenversicherung');
    expect(required.length).toBe(2);
    expect(required[0]).toBe('vorversicherungKrankenversicherung');
    expect(required[1]).toBe('nameProvider');
  });

  test('Previous health insurance', () => {
    const testForm: KvcFormFieldProps[] = GetFormConfigurationFromIntlById(
      t,
      'vorversicherungKrankenversicherung'
    );
    data = {
      nameProvider: 'Keine',
    };
    expected = {
      nameProvider: true,
      beginYearMonth: false,
      endYearMonth: false,
      outstanding_contributions: false,
    };
    testForm.forEach((field) => {
      if (field.element === KvcInputFieldType.ArrayOf) {
        const fieldIsVisible = FieldIsVisible(data, field);
        result[field.id] = fieldIsVisible;
        //   console.log(
        //     'expected',
        //     field.id,
        //     fieldIsVisible,
        //     'to be',
        //     expected[field.id]
        //   );
        expect(fieldIsVisible).toBe(expected[field.id]);
      }
    });

    const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
      data,
      testForm,
      undefined,
      t
    );
    // expect(bad).toBe([]);
    expect(bad.length).toBe(0);
    // expect(bad[0]).toBe('vorversicherungKrankenversicherung');
    expect(required.length).toBe(1);
    expect(required[0]).toBe('nameProvider');
  });

  //   test('animals2', () => {
  //     data = {
  //       dog: 'woof',
  //     };
  //     expected = {
  //       dog: true,
  //       cat: true,
  //       fish: true,
  //       puppy: true,
  //       mouse: true,
  //     };
  //     testForm.forEach((field) => {
  //       const fieldIsVisible = FieldIsVisible(data, field);
  //       result[field.id] = fieldIsVisible;
  //       expect(fieldIsVisible).toBe(expected[field.id]);
  //     });
  //   });

  //   test('animals3', () => {
  //     data = {
  //       cat: 'meow',
  //     };
  //     expected = {
  //       dog: false,
  //       cat: true,
  //       fish: true,
  //       puppy: false,
  //       mouse: false,
  //     };
  //     testForm.forEach((field) => {
  //       const fieldIsVisible = FieldIsVisible(data, field);
  //       result[field.id] = fieldIsVisible;
  //       expect(fieldIsVisible).toBe(expected[field.id]);
  //     });
  //   });

  //   test('animals4', () => {
  //     data = {
  //       cat: 'purr',
  //     };
  //     expected = {
  //       dog: true,
  //       cat: true,
  //       fish: true,
  //       puppy: false,
  //       mouse: true,
  //     };
  //     testForm.forEach((field) => {
  //       const fieldIsVisible = FieldIsVisible(data, field);
  //       result[field.id] = fieldIsVisible;
  //       expect(fieldIsVisible).toBe(expected[field.id]);
  //     });

  //     const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
  //       data,
  //       testForm,
  //       undefined,
  //       t
  //     );

  //     expect(required.length).toBe(4);
  //     expect(bad.length).toBe(3);
  //     expect(bad.includes('dog')).toBeTruthy();
  //     expect(bad.includes('fish')).toBeTruthy();
  //     expect(bad.includes('mouse')).toBeTruthy();
  //   });

  //   test('ifnot', () => {
  //     data = {
  //       cat: 'meow',
  //     };
  //     expected = {
  //       dog: false,
  //       cat: true,
  //       fish: true,
  //       puppy: false,
  //       mouse: false,
  //     };
  //     testForm.forEach((field) => {
  //       const fieldIsVisible = FieldIsVisible(data, field);
  //       result[field.id] = fieldIsVisible;
  //       expect(fieldIsVisible).toBe(expected[field.id]);
  //     });

  //     const [bad, required] = ValidateFormDataBasedOnFormConfiguration(
  //       data,
  //       testForm,
  //       undefined,
  //       t
  //     );

  //     // expect(required.length).toBe(4);
  //     // expect(bad.length).toBe(3);
  //     // expect(bad.includes('dog')).toBeTruthy();
  //     // expect(bad.includes('fish')).toBeTruthy();
  //     // expect(bad.includes('mouse')).toBeTruthy();
  //   });
  //
});
