# Updated: 29.07.2025
# MGIS Pipeline Helm chart based Deployment 1.0
# Author: <PERSON><PERSON><PERSON><PERSON>
# Created: July. 2025
image: atlassian/default-image:3

definitions:
  services:
    docker:
      memory: 3072 # Memory in MB -  allocate 3GB (3072MB) of memory to docker service
  image: &mgis-image
    name: atlassian/default-image
  steps:
    - step: &build-and-push-container
        name: Build and Push
        script:
          # Login and push to the new common registry
          - echo $REGISTRY_PASSWORD | docker login $REGISTRY_URL -u $REGISTRY_USER --password-stdin
          - IMAGE_NAME="$REGISTRY_URL/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT::7}"
          - docker build . --file docker/develop/Dockerfile --tag ${IMAGE_NAME}
          - docker push ${IMAGE_NAME}
        services:
          - docker
        caches:
          - docker

    - step: &helm-deploy
        name: He<PERSON> Deploy
        image: alpine/helm:3.14.0
        script:
          - apk add --no-cache git curl yq bash
          # Restore kubeconfig from env variable
          - mkdir -p ~/.kube
          - echo "$KUBE_CONFIG" | base64 -d > ~/.kube/config
          - chmod 600 ~/.kube/config
          # Clone the private Helm chart repo
          - git clone --depth 1 https://x-token-auth:$<EMAIL>/mv-services/asevo-helm-template.git /chart
          # Detect environment and select values file
          - export IMAGE_TAG="${BITBUCKET_COMMIT::7}"
          - export VALUES_FILE="helm-values/${BITBUCKET_DEPLOYMENT_ENVIRONMENT:-dev}/values.yaml"
          # Patch values yaml file
          - yq e ".image.tag = \"$IMAGE_TAG\"" -i "$VALUES_FILE"
          - yq e ".imagePullSecret.username = \"$REGISTRY_USER\"" -i "$VALUES_FILE"
          - yq e ".imagePullSecret.password = \"$REGISTRY_PASSWORD\"" -i "$VALUES_FILE"
          - export ENV_PREFIX=$(echo "${BITBUCKET_DEPLOYMENT_ENVIRONMENT:-dev}" | tr '[:lower:]' '[:upper:]')
          - |
            SECRET_FIELDS=(
              SMTP_PASSWORD
              STRAPI_API_TOKEN
              CALCULATION_PARAMETERS_ID
              IONOS_ACCESS_KEY_ID
              IONOS_SECRET_ACCESS_KEY
              NUMBER_GENERATOR_SALT
              CORONJOB_API_KEY
            )

            for key in "${SECRET_FIELDS[@]}"; do
              varname="${ENV_PREFIX}_${key}"
              val=$(eval echo \$$varname)
              echo "Checking: $varname → $val"
              if [ -n "$val" ]; then
                yq e ".secret.data.${key} = \"$val\"" -i "$VALUES_FILE"
              fi
            done

          # Extract release metadata
          - export RELEASE_NAME=$(yq e '.app.name' "$VALUES_FILE")
          - export NAMESPACE=$(yq e '.app.namespace' "$VALUES_FILE")
          - cat $VALUES_FILE
          # Deploy with Helm
          - helm upgrade --install "$RELEASE_NAME" /chart --namespace "$NAMESPACE" -f "$VALUES_FILE"
        services:
          - docker
        caches:
          - docker

pipelines:
  default:
    - step:
        name: Build from Branch
        script:
          - docker build . --file docker/develop/Dockerfile
        services:
          - docker
        caches:
          - docker
  branches:
    develop:
      - step:
          <<: *build-and-push-container
          trigger: automatic
      - step:
          <<: *helm-deploy
          deployment: dev
    # staging:
    #   - step:
    #       <<: *build-and-push-container
    #       trigger: automatic
    #   - step:
    #       <<: *helm-deploy
    #       deployment: stg
    # main:
    #   - step:
    #       <<: *build-and-push-container
    #       trigger: automatic
    #   - step:
    #       <<: *helm-deploy
    #       deployment: prd
