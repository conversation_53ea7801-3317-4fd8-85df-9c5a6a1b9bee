// Prisma Client output for MongoDB
generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma-mongo"
}

datasource mongo {
  provider = "mongodb"
  url      = env("MONGODB_URI") // e.g. mongodb+srv://user:pw@cluster/revisions
}


model AgentRevision {
  id             String   @id @default(auto()) @map("_id") @mongo.ObjectId
  primaryId      String
  agentId        String
  delta          Json
  createdAt      DateTime  @default(now())

  @@map("agent_revision")
}

model ContractRevision {
  id             String   @id @default(auto()) @map("_id") @mongo.ObjectId
  primaryId      String
  agentId        String
  delta          Json
  createdAt      DateTime  @default(now())

  @@map("contract_revision")
}

model CustomerRevision {
  id             String   @id @default(auto()) @map("_id") @mongo.ObjectId
  primaryId      String
  agentId        String
  delta          Json
  createdAt      DateTime  @default(now())

  @@map("customer_revision")
}

model InvoiceRevision {
  id             String   @id @default(auto()) @map("_id") @mongo.ObjectId
  primaryId      String
  agentId        String
  delta          Json
  createdAt      DateTime  @default(now())

  @@map("invoice_revision")
}

model ReportRevision {
  id             String   @id @default(auto()) @map("_id") @mongo.ObjectId
  primaryId      String
  agentId        String
  delta          Json
  createdAt      DateTime  @default(now())

  @@map("report_revision")
}
