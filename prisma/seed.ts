import {PrismaClient} from '@prisma/client';
import Chance from 'chance'

const prisma = new PrismaClient();
const chance = new Chance()

const MONTH = 30 * 24 * 60 * 60 * 1000

const createReferrer = (overrides?: Record<string, any>) => {
    const [firstName, lastName] = chance.name().split(' ')

    return {
      email: chance.email(),
      firstName,
      lastName,
      street: chance.street(),
      houseNumber: String(chance.natural({ max: 300 })),
      postalCode: chance.zip(),
      city: chance.city(),
      phoneNumber: chance.phone(),
      agencyNumber: chance.string({ length: 8, casing: 'upper', alpha: true, numeric: true }),
      salesDirection: chance.pickone([
        'Hamburg',
        'Köln',
        'Berlin',
        'Leipzig',
        'Frankfurt',
        'Stuttgart',
        'Nürnberg',
        'München',
        'Sonstige',
      ]),
      iban: `DE${chance.string({ length: 16, numeric: true })}`,
      ...overrides,
    }

}

async function main() {
  const referrers = chance.n(createReferrer, 2)
  const products = chance.n(() => ({
    name: chance.word({ syllables: 4, capitalize: true }),
    description: chance.sentence(),
  }), 5)
  const customers = chance.n(() => {
    const [firstName, lastName] = chance.name().split(' ')

    return {
      salutation: chance.pickone(['Herr', 'Frau', 'Divers']),
      firstName,
      lastName,
      street: '',
      houseNumber: '',
      postalCode: '',
      city: '',
      phoneNumber: '',
      email: chance.email(),
    }
  }, 2)

  await Promise.all(
      referrers.map(data => prisma.referrer.create({
        data
      }))
  )
  await prisma.referrer.create({
    data: createReferrer({
      email: process.env.ADMIN_EMAIL,
      isAdmin: true,
    })
  })

  await Promise.all(
      products.map(data => prisma.product.create({
        data
      }))
  )
  await prisma.product.update({
    where: { id: 1 },
    data: { name: 'Wohngebäudeversicherung' }
  })

  await Promise.all(
      customers.map(data => prisma.customer.create({
        data
      }))
  )

  for (let i = 0; i < 30; i++) {
    const status = chance.pickone(['invited', 'pending', 'open', 'accepted', 'rejected'])

    const productExtensions = chance.bool() ? chance.n(
        () => chance.word({ syllables: 4, capitalize: true }),
        chance.integer({ min: 0, max: 5 })
    ) : undefined
    const offer = ['open', 'accepted', 'rejected'].includes(status) ? {
      pdf: '/path/to/offer.pdf',
      shareWithReferrer: chance.bool(),
      premium: chance.natural({ min: 10, max: 100 }),
      productExtensions,
      expiresAt: new Date(Date.now() + chance.integer({ min: -MONTH, max: MONTH }) + MONTH / 2)
    } : undefined

    const policy = status === 'accepted' ? {
      pdf: '/path/to/policy.pdf',
      premium: offer!.premium,
      commission: chance.natural({ min: 100, max: 1000 }),
      issuedAt: chance.date(),
    } : undefined

    await prisma.tip.upsert({
      where: { id: i+1 },
      create: {
        agreementPdf: '',
        status,
        commission: chance.natural({ min: 100, max: 1000 }),
        risk: chance.address(),
        referrer: {
          connect: {
            id: chance.natural({ min: 1, max: referrers.length })
          }
        },
        product: {
          connect: {
            id: chance.natural({ min: 1, max: products.length })
          }
        },
        customer: {
          connect: {
            id: chance.natural({ min: 1, max: customers.length })
          }
        },
        previousInsurance: {
          create: {
            contractNumber: 'AS-1234567890',
            consentGiven: true,
          }
        },
        offer: offer ? {
          create: offer,
        } : undefined,
        policy: policy ? {
          create: policy
        } : undefined
      },
      update: {}
    })
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
