import { CaseStatus, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting comprehensive seed data creation...');

  // ===== CUSTOMERS =====
  console.log('\n👥 Creating customers...');

  // Customer from mock case 1 - <PERSON>-<PERSON>
  const customer1 = await prisma.customer.upsert({
    where: { customerId: 1 },
    create: {
      customerId: 1,
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON>-Bichler',
      dateOfBirth: new Date('1980-05-15T00:00:00Z'),
      gender: 'male',
      email: '<EMAIL>',
      phoneNumber: '+49 123 456789',
      street: 'Hauptstraße',
      houseNumber: '123',
      postalCode: '10115',
      city: 'Berlin',
      country: 'Deutschland',
      salutation: 'Herr',
      status: 'Active',
    },
    update: {},
  });

  // Customer from mock case 2 - <PERSON>
  const customer2 = await prisma.customer.upsert({
    where: { customerId: 2 },
    create: {
      customerId: 2,
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      dateOfBirth: new Date('1985-03-20T00:00:00Z'),
      gender: 'female',
      email: '<EMAIL>',
      phoneNumber: '+49 987 654321',
      street: 'Berliner Straße',
      houseNumber: '45',
      postalCode: '20095',
      city: 'Hamburg',
      country: 'Deutschland',
      salutation: 'Frau',
      status: 'Active',
    },
    update: {},
  });

  // Customer from mock case 3 - Thomas Mueller
  const customer3 = await prisma.customer.upsert({
    where: { customerId: 3 },
    create: {
      customerId: 3,
      firstName: 'Thomas',
      lastName: 'Mueller',
      dateOfBirth: new Date('1975-11-10T00:00:00Z'),
      gender: 'male',
      email: '<EMAIL>',
      phoneNumber: '+49 555 123456',
      street: 'Münchener Straße',
      houseNumber: '78',
      postalCode: '80331',
      city: 'München',
      country: 'Deutschland',
      salutation: 'Herr',
      academicTitle: 'Dr.',
      status: 'Active',
    },
    update: {},
  });

  // Customer from mock case 4 - Maria Weber
  const customer4 = await prisma.customer.upsert({
    where: { customerId: 4 },
    create: {
      customerId: 4,
      firstName: 'Maria',
      lastName: 'Weber',
      dateOfBirth: new Date('1990-07-25T00:00:00Z'),
      gender: 'female',
      email: '<EMAIL>',
      phoneNumber: '+49 777 987654',
      street: 'Kölner Ring',
      houseNumber: '12',
      postalCode: '50667',
      city: 'Köln',
      country: 'Deutschland',
      salutation: 'Frau',
      status: 'Active',
    },
    update: {},
  });

  // ===== USERS =====
  console.log('\n👤 Creating users...');

  // Admin user
  const userAdmin1 = await prisma.user.upsert({
    where: { username: 'admin1' },
    create: {
      role: 'admin',
      email: '<EMAIL>',
      username: 'admin1',
      firstName: 'Thomas',
      lastName: 'Administrator',
      phoneNumber: '+49 30 55512345',
      street: 'Unter den Linden',
      houseNumber: '77',
      postalCode: '10117',
      city: 'Berlin',
      country: 'Germany',
      termsAndConditionsAccepted: true,
    },
    update: {},
  });

  // Agent 1 - with real keycloakId from mock data
  const userAgent1 = await prisma.user.upsert({
    where: { username: 'agent1' },
    create: {
      keycloakId: '40f19f67-fd01-48ad-950d-b200593e6d7d', // Real keycloakId from mock
      role: 'agent',
      email: '<EMAIL>',
      username: 'agent1',
      firstName: 'Agent',
      lastName: 'One',
      termsAndConditionsAccepted: false,
    },
    update: {},
  });

  // Agent 2 - with real keycloakId from mock data
  const userAgent2 = await prisma.user.upsert({
    where: { username: 'agent2' },
    create: {
      keycloakId: '5578cb75-7ecf-4f56-8a0f-8c77d26f6f61', // Real keycloakId from mock
      role: 'agent',
      email: '<EMAIL>',
      username: 'agent2',
      firstName: 'Agent',
      lastName: 'Two',
      termsAndConditionsAccepted: false,
    },
    update: {},
  });

  // Frank Assekurichter - from mock cases (assigned user)
  const userFrank = await prisma.user.upsert({
    where: { username: 'frank.assekurichter' },
    create: {
      keycloakId: 'user-1', // From mock data
      role: 'agent',
      email: '<EMAIL>',
      username: 'frank.assekurichter',
      firstName: 'Frank',
      lastName: 'Assekurichter',
      termsAndConditionsAccepted: true,
    },
    update: {},
  });

  // ===== CASES =====
  console.log('\n📋 Creating cases...');

  // Case 1 - JGRD-8265 (Risiko-Voranfrage) - Manfred Lehmann-Bichler
  const case1 = await prisma.case.upsert({
    where: { caseNumber: 'JGRD-8265' },
    create: {
      caseNumber: 'JGRD-8265',
      customerId: customer1.customerId,
      assignedUserId: userFrank.userId,
      caseType: 'Risiko-Voranfrage',
      status: CaseStatus.RiskInquiryRunning,
      createdAt: new Date('2024-01-15T00:00:00Z'),
    },
    update: {},
  });

  // Case 2 - ABCD-1234 (Antrag) - Anna Schmidt
  const case2 = await prisma.case.upsert({
    where: { caseNumber: 'ABCD-1234' },
    create: {
      caseNumber: 'ABCD-1234',
      customerId: customer2.customerId,
      assignedUserId: userFrank.userId,
      caseType: 'Antrag',
      status: CaseStatus.Applied,
      createdAt: new Date('2024-01-10T00:00:00Z'),
    },
    update: {},
  });

  // Case 3 - EFGH-5678 (Schnellcheck) - Thomas Mueller
  const case3 = await prisma.case.upsert({
    where: { caseNumber: 'EFGH-5678' },
    create: {
      caseNumber: 'EFGH-5678',
      customerId: customer3.customerId,
      assignedUserId: null, // No assigned user in mock data
      caseType: 'Schnellcheck',
      status: CaseStatus.Rejected,
      createdAt: new Date('2024-01-08T00:00:00Z'),
    },
    update: {},
  });

  // Case 4 - IJKL-9012 (Risiko-Voranfrage) - Maria Weber
  const case4 = await prisma.case.upsert({
    where: { caseNumber: 'IJKL-9012' },
    create: {
      caseNumber: 'IJKL-9012',
      customerId: customer4.customerId,
      assignedUserId: userFrank.userId,
      caseType: 'Risiko-Voranfrage',
      status: CaseStatus.Processing,
      createdAt: new Date('2024-01-05T00:00:00Z'),
    },
    update: {},
  });

  // ===== RISK PRE-REQUESTS =====
  console.log('\n🔍 Creating risk pre-requests...');

  // Case 1 (JGRD-8265) - Multiple risk pre-requests for comprehensive testing
  const riskPreRequest1 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 1 },
    create: {
      riskPreRequestId: 1,
      caseId: case1.caseId,
      insurerName: 'Nürnberger Versicherung',
      tariffName: 'Basic Plus',
      monthlyPremium: 533.0,
      additionalFee: 28.38,
      status: 'Ready',
      agentSelect: true,
      medicalHistory:
        'Comprehensive life insurance coverage - standard health questionnaire completed',
      remarks:
        'Recommended option for customer - approved after risk assessment',
      submittedAt: new Date('2024-01-16T10:30:00Z'),
    },
    update: {},
  });

  const riskPreRequest2 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 2 },
    create: {
      riskPreRequestId: 2,
      caseId: case1.caseId,
      insurerName: 'Gothaer',
      tariffName: 'PKV XL',
      monthlyPremium: null,
      additionalFee: null,
      status: 'Pending',
      agentSelect: true,
      medicalHistory:
        'Premium health insurance - detailed medical questionnaire submitted',
      remarks: 'Pending risk assessment - awaiting medical review',
      submittedAt: new Date('2024-01-17T14:15:00Z'),
    },
    update: {},
  });

  const riskPreRequest3 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 3 },
    create: {
      riskPreRequestId: 3,
      caseId: case1.caseId,
      insurerName: 'Axa',
      tariffName: 'Advanced L',
      monthlyPremium: 566.0,
      additionalFee: 45.0,
      status: 'Ready',
      agentSelect: false,
      medicalHistory:
        'Advanced coverage plan - standard health assessment completed',
      remarks: 'Good value option - approved with standard terms',
      submittedAt: new Date('2024-01-18T09:45:00Z'),
    },
    update: {},
  });

  const riskPreRequest4 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 4 },
    create: {
      riskPreRequestId: 4,
      caseId: case1.caseId,
      insurerName: 'Signal Iduna',
      tariffName: 'Schutz Advanced',
      monthlyPremium: null,
      additionalFee: null,
      status: 'InputRequired',
      agentSelect: false,
      medicalHistory:
        'Advanced protection plan - comprehensive medical history provided',
      remarks:
        '1 processing request pending - additional medical documentation requested',
      submittedAt: new Date('2024-01-19T11:20:00Z'),
    },
    update: {},
  });

  const riskPreRequest5 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 5 },
    create: {
      riskPreRequestId: 5,
      caseId: case1.caseId,
      insurerName: 'Allianz',
      tariffName: 'A45+',
      monthlyPremium: null,
      additionalFee: null,
      status: 'InputRequired',
      agentSelect: true,
      medicalHistory:
        'Premium coverage with additional benefits - extensive health questionnaire completed',
      remarks:
        '2 processing requests pending - underwriting review in progress',
      submittedAt: new Date('2024-01-20T16:00:00Z'),
    },
    update: {},
  });

  const riskPreRequest6 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 6 },
    create: {
      riskPreRequestId: 6,
      caseId: case1.caseId,
      insurerName: 'ARAG',
      tariffName: 'PKV Schutz',
      monthlyPremium: null,
      additionalFee: null,
      status: 'Denied',
      agentSelect: false,
      medicalHistory:
        'Basic protection plan - standard medical questionnaire submitted',
      remarks: 'Application rejected due to pre-existing medical conditions',
      submittedAt: new Date('2024-01-21T13:30:00Z'),
    },
    update: {},
  });

  // Case 2 (ABCD-1234) - Single risk pre-request for different case type
  const riskPreRequest7 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 7 },
    create: {
      riskPreRequestId: 7,
      caseId: case2.caseId,
      insurerName: 'Generali',
      tariffName: 'Premium Care',
      monthlyPremium: 450.0,
      additionalFee: 25.0,
      status: 'Ready',
      agentSelect: true,
      medicalHistory: 'Comprehensive health insurance - clean medical history',
      remarks: 'Excellent coverage option - approved with preferred rates',
      submittedAt: new Date('2024-01-11T10:00:00Z'),
    },
    update: {},
  });

  // Case 3 (EFGH-5678) - Schnellcheck with rejected risk pre-requests
  const riskPreRequest8 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 8 },
    create: {
      riskPreRequestId: 8,
      caseId: case3.caseId,
      insurerName: 'DKV',
      tariffName: 'Basis Schutz',
      monthlyPremium: null,
      additionalFee: null,
      status: 'Denied',
      agentSelect: false,
      medicalHistory:
        'Basic health insurance coverage - pre-existing conditions disclosed',
      remarks:
        'Application rejected due to pre-existing conditions and age factors',
      submittedAt: new Date('2024-01-09T14:30:00Z'),
    },
    update: {},
  });

  const riskPreRequest9 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 9 },
    create: {
      riskPreRequestId: 9,
      caseId: case3.caseId,
      insurerName: 'Barmenia',
      tariffName: 'Mehr Sicher',
      monthlyPremium: null,
      additionalFee: null,
      status: 'Denied',
      agentSelect: false,
      medicalHistory:
        'Enhanced protection plan - detailed medical history provided',
      remarks:
        'Declined after comprehensive risk assessment - high risk profile',
      submittedAt: new Date('2024-01-10T11:15:00Z'),
    },
    update: {},
  });

  // Case 4 (IJKL-9012) - Completed Risiko-Voranfrage with successful risk pre-requests
  const riskPreRequest10 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 10 },
    create: {
      riskPreRequestId: 10,
      caseId: case4.caseId,
      insurerName: 'Debeka',
      tariffName: 'BKK Premium',
      monthlyPremium: 389.5,
      additionalFee: 15.75,
      status: 'Ready',
      agentSelect: true,
      medicalHistory:
        'Comprehensive health insurance with dental coverage - excellent health profile',
      remarks:
        'Excellent coverage option - recommended, approved with best rates',
      submittedAt: new Date('2024-01-06T09:00:00Z'),
    },
    update: {},
  });

  const riskPreRequest11 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 11 },
    create: {
      riskPreRequestId: 11,
      caseId: case4.caseId,
      insurerName: 'HUK-Coburg',
      tariffName: 'Gesundheit Plus',
      monthlyPremium: 425.0,
      additionalFee: null,
      status: 'Ready',
      agentSelect: false,
      medicalHistory:
        'Premium health insurance with additional benefits - standard health assessment',
      remarks: 'Good alternative option - approved with standard terms',
      submittedAt: new Date('2024-01-07T15:30:00Z'),
    },
    update: {},
  });

  const riskPreRequest12 = await prisma.riskPreRequest.upsert({
    where: { riskPreRequestId: 12 },
    create: {
      riskPreRequestId: 12,
      caseId: case4.caseId,
      insurerName: 'Continentale',
      tariffName: 'CEKA-Z',
      monthlyPremium: null,
      additionalFee: null,
      status: 'Pending',
      agentSelect: true,
      medicalHistory:
        'Comprehensive dental and health coverage - medical questionnaire under review',
      remarks: 'Risk assessment in progress - awaiting underwriter decision',
      submittedAt: new Date('2024-01-08T12:45:00Z'),
    },
    update: {},
  });

  // ===== APPLICATIONS =====
  console.log('\n📄 Creating applications...');

  // Applications for ready status insurances
  const application1 = await prisma.application.upsert({
    where: { applicationId: 1 },
    create: {
      applicationId: 1,
      caseId: case1.caseId,
      status: 'Approved',
      planType: 'Basic Plus',
      premiumAmount: 533.0,
      remarks: 'Application approved for Nürnberger Versicherung',
    },
    update: {},
  });

  const application2 = await prisma.application.upsert({
    where: { applicationId: 2 },
    create: {
      applicationId: 2,
      caseId: case1.caseId,
      status: 'Approved',
      planType: 'Advanced L',
      premiumAmount: 566.0,
      remarks: 'Application approved for Axa',
    },
    update: {},
  });

  const application3 = await prisma.application.upsert({
    where: { applicationId: 3 },
    create: {
      applicationId: 3,
      caseId: case2.caseId,
      status: 'Approved',
      planType: 'Premium Care',
      premiumAmount: 450.0,
      remarks: 'Application approved for Generali',
    },
    update: {},
  });

  const application4 = await prisma.application.upsert({
    where: { applicationId: 4 },
    create: {
      applicationId: 4,
      caseId: case1.caseId,
      status: 'Rejected',
      planType: 'PKV Schutz',
      premiumAmount: null,
      remarks: 'Application rejected by ARAG due to risk factors',
    },
    update: {},
  });

  // Applications for Case 4 (IJKL-9012) - Completed case
  const application5 = await prisma.application.upsert({
    where: { applicationId: 5 },
    create: {
      applicationId: 5,
      caseId: case4.caseId,
      status: 'Approved',
      planType: 'BKK Premium',
      premiumAmount: 389.5,
      remarks: 'Application approved for Debeka - excellent coverage',
    },
    update: {},
  });

  const application6 = await prisma.application.upsert({
    where: { applicationId: 6 },
    create: {
      applicationId: 6,
      caseId: case4.caseId,
      status: 'Approved',
      planType: 'Gesundheit Plus',
      premiumAmount: 425.0,
      remarks: 'Application approved for HUK-Coburg - good alternative',
    },
    update: {},
  });

  // ===== CASE CUSTOMER RELATIONSHIPS =====
  console.log('\n👥 Creating case-customer relationships...');

  // Create CaseCustomer relationships for proper data structure
  //   await prisma.caseCustomer.upsert({
  //     where: { caseCustomerId: 1 },
  //     create: {
  //       caseCustomerId: 1,
  //       caseId: case1.caseId,
  //       customerId: customer1.customerId,
  //       relationType: 'paying_and_insured_primary',
  //     },
  //     update: {},
  //   });

  //   await prisma.caseCustomer.upsert({
  //     where: { caseCustomerId: 2 },
  //     create: {
  //       caseCustomerId: 2,
  //       caseId: case2.caseId,
  //       customerId: customer2.customerId,
  //       relationType: 'paying_and_insured_primary',
  //     },
  //     update: {},
  //   });

  //   await prisma.caseCustomer.upsert({
  //     where: { caseCustomerId: 3 },
  //     create: {
  //       caseCustomerId: 3,
  //       caseId: case3.caseId,
  //       customerId: customer3.customerId,
  //       relationType: 'paying_and_insured_primary',
  //     },
  //     update: {},
  //   });

  //   await prisma.caseCustomer.upsert({
  //     where: { caseCustomerId: 4 },
  //     create: {
  //       caseCustomerId: 4,
  //       caseId: case4.caseId,
  //       customerId: customer4.customerId,
  //       relationType: 'paying_and_insured_primary',
  //     },
  //     update: {},
  //   });

  console.log('\n🎉 Comprehensive seed data created successfully!');
  console.log('\n� SUMMARY:');
  console.log('✅ 4 Customers created');
  console.log('✅ 4 Users created (1 admin, 3 agents)');
  console.log('✅ 4 Cases created with different types and statuses');
  console.log('✅ 12 Risk pre-requests created with various statuses');
  console.log('✅ 6 Applications created (5 approved, 1 rejected)');
  console.log('✅ 4 Case-customer relationships created');

  console.log('\n� CUSTOMERS:');
  console.log('1. Manfred Lehmann-Bichler (Berlin) - Case: JGRD-8265');
  console.log('2. Anna Schmidt (Hamburg) - Case: ABCD-1234');
  console.log('3. Thomas Mueller (München) - Case: EFGH-5678');
  console.log('4. Maria Weber (Köln) - Case: IJKL-9012');

  console.log('\n📋 CASES:');
  console.log(
    '1. JGRD-8265 (Risiko-Voranfrage) - 6 risk pre-requests, multiple statuses'
  );
  console.log('2. ABCD-1234 (Antrag) - 1 risk pre-request, approved status');
  console.log('3. EFGH-5678 (Schnellcheck) - 2 risk pre-requests, rejected');
  console.log(
    '4. IJKL-9012 (Risiko-Voranfrage) - 3 risk pre-requests, completed'
  );

  console.log('\n👤 USERS:');
  console.log('Admin: admin1 (Thomas Administrator)');
  console.log(
    'Agent 1: agent1 (Agent One) - keycloakId: 40f19f67-fd01-48ad-950d-b200593e6d7d'
  );
  console.log(
    'Agent 2: agent2 (Agent Two) - keycloakId: 5578cb75-7ecf-4f56-8a0f-8c77d26f6f61'
  );
  console.log(
    'Agent 3: frank.assekurichter (Frank Assekurichter) - keycloakId: user-1'
  );

  console.log('\n🏢 INSURANCE COMPANIES:');
  console.log(
    'Nürnberger Versicherung, Gothaer, Axa, Signal Iduna, Allianz, ARAG, Generali'
  );

  console.log('\n🔐 LOGIN CREDENTIALS:');
  console.log('Admin: admin1 / Test123!');
  console.log('Agent 1: agent1 / Test123!');
  console.log('Agent 2: agent2 / Test123!');
  console.log('Agent 3: frank.assekurichter / Test123!');

  console.log('\n🎯 TESTING SCENARIOS:');
  console.log('• Dashboard statistics with various case statuses');
  console.log('• Individual case view with multiple risk pre-requests');
  console.log(
    '• Different risk pre-request statuses (Approved, Pending, Processing, Rejected)'
  );
  console.log('• Agent selection indicators (agentSelect: true/false)');
  console.log('• Premium and additional fee calculations');
  console.log('• Case assignments to different agents');
  console.log('• Risk pre-request attachments functionality');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
