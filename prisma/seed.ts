import { PrismaClient } from '../src/generated/prisma-postgres/index.js';

const prisma = new PrismaClient();

async function main() {
  // 1️⃣ Create a Theme first
  const theme = await prisma.theme.create({
    data: {
      documentId: 'ykghwlnfyrmifzczcyb13yg9',
      themeString:
        '{"palette":{"primary":{"main":"#003C59"},"secondary":{"main":"#9DD2C3"}},"typography":{"fontFamily":"Inter, Arial, sans-serif"},"components":{"MuiTooltip":{"defaultProps":{"placement":"top","disableInteractive":true}},"MuiButton":{"styleOverrides":{"root":{"color":"white","fontSize":"0.875rem","padding":"0.5rem 1rem","transition":"background-color 0.3s ease","borderRadius":0,"boxShadow":"none"},"containedPrimary":{"backgroundColor":"{{palette.primary.main}}","&:hover":{"backgroundColor":"{{palette.secondary.main}}","boxShadow":"none"}},"containedSecondary":{"backgroundColor":"{{palette.secondary.main}}","&:hover":{"backgroundColor":"{{palette.primary.main}}","boxShadow":"none"}}}},"MuiIconButton":{"styleOverrides":{"root":{"color":"{{palette.primary.main}}","&:hover":{"backgroundColor":"{{palette.primary.main}}","color":"white","boxShadow":"none"}}}},"MuiMenuItem":{"styleOverrides":{"root":{"backgroundColor":"{{palette.primary.main}}","color":"white","&:hover":{"backgroundColor":"{{palette.secondary.main}}","color":"white","boxShadow":"none"},"&.Mui-selected":{"backgroundColor":"{{palette.secondary.main}}","color":"{{palette.secondary.contrastText}}"},"&.Mui-selected:hover":{"backgroundColor":"{{palette.secondary.main}}"}}}},"MuiTextField":{"styleOverrides":{"root":{"borderRadius":0,"boxShadow":"none"}}},"MuiCard":{"styleOverrides":{"root":{"borderRadius":0,"boxShadow":"none"}}},"MuiPaper":{"styleOverrides":{"root":{"boxShadow":"none"}}},"MuiTimelineDot":{"styleOverrides":{"root":{"backgroundColor":"{{palette.primary.main}}"}}}}}',
      companyName: 'Alpha Insurance',
    },
  });

  // 2️⃣ Create an Agency
  const agency = await prisma.agency.create({
    data: {
      agencyName: 'MV-Services',
      agencyNumber: '0000-ZZZ',
      isAdmin: true,
    },
  });

  // 3️⃣ Create an Agent connected to the Agency
  const agent = await prisma.agent.create({
    data: {
      username: 'Max Mustermann',
      email: '<EMAIL>',
      agencyNumber: '0000-ZZZ',
      agentNumber: 'ZZZ-0000',
      street: 'Beethoven Straße',
      houseNumber: '42',
      city: 'Frankfurt',
      companyName: 'testCompany',
      postalCode: '60325',
      commission: 0,
      confirmed: true,
      blocked: false,
      agency: {
        connect: { id: agency.id },
      },
    },
  });

  console.log('Seed complete ✅');
  console.log({ theme, agency, agent });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
