/*
  Warnings:

  - You are about to drop the column `additionalFee` on the `PotentialInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `agentSelect` on the `PotentialInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `PotentialInsurance` table. All the data in the column will be lost.
  - The `status` column on the `RiskPreRequest` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `insurerName` to the `RiskPreRequest` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tariffName` to the `RiskPreRequest` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."PotentialInsuranceStatus" AS ENUM ('Pending', 'InProgress', 'InputRequired', 'Denied', 'Ready', 'Sent');

-- AlterTable
ALTER TABLE "public"."Attachment" ADD COLUMN     "riskPreRequestId" INTEGER;

-- AlterTable
ALTER TABLE "public"."PotentialInsurance" DROP COLUMN "additionalFee",
DROP COLUMN "agentSelect",
DROP COLUMN "status";

-- AlterTable
ALTER TABLE "public"."RiskPreRequest" ADD COLUMN     "additionalFee" DECIMAL(10,2),
ADD COLUMN     "agentSelect" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "insurerName" TEXT NOT NULL,
ADD COLUMN     "monthlyPremium" DECIMAL(10,2),
ADD COLUMN     "tariffName" TEXT NOT NULL,
DROP COLUMN "status",
ADD COLUMN     "status" "public"."PotentialInsuranceStatus" NOT NULL DEFAULT 'Pending';

-- AddForeignKey
ALTER TABLE "public"."Attachment" ADD CONSTRAINT "Attachment_riskPreRequestId_fkey" FOREIGN KEY ("riskPreRequestId") REFERENCES "public"."RiskPreRequest"("riskPreRequestId") ON DELETE SET NULL ON UPDATE CASCADE;
