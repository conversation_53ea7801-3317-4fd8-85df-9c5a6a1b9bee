/*
  Warnings:

  - A unique constraint covering the columns `[caseGroupNumber]` on the table `CaseGroup` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "public"."Case" ADD COLUMN     "caseGroupCaseGroupId" INTEGER;

-- AlterTable
ALTER TABLE "public"."CaseGroup" ADD COLUMN     "caseGroupNumber" TEXT NOT NULL DEFAULT ((upper(substr(md5((random())::text), 1, 3)) || '-'::text) || substr(md5((random())::text), 1, 3));

-- CreateIndex
CREATE UNIQUE INDEX "CaseGroup_caseGroupNumber_key" ON "public"."CaseGroup"("caseGroupNumber");

-- AddForeignKey
ALTER TABLE "public"."Case" ADD CONSTRAINT "Case_caseGroupCaseGroupId_fkey" FOREIGN KEY ("caseGroupCaseGroupId") REFERENCES "public"."CaseGroup"("caseGroupId") ON DELETE SET NULL ON UPDATE CASCADE;
