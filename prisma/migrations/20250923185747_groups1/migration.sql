-- AlterTable
ALTER TABLE "public"."Questionnaire" ADD COLUMN     "caseCaseId" INTEGER;

-- CreateTable
CREATE TABLE "public"."CaseGroup" (
    "caseGroupId" SERIAL NOT NULL,
    "groupType" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CaseGroup_pkey" PRIMARY KEY ("caseGroupId")
);

-- CreateTable
CREATE TABLE "public"."CaseGroupMembership" (
    "caseGroupMembershipId" SERIAL NOT NULL,
    "caseGroupId" INTEGER NOT NULL,
    "caseId" INTEGER NOT NULL,
    "relationType" TEXT NOT NULL,

    CONSTRAINT "CaseGroupMembership_pkey" PRIMARY KEY ("caseGroupMembershipId")
);

-- AddForeignKey
ALTER TABLE "public"."Questionnaire" ADD CONSTRAINT "Questionnaire_caseCaseId_fkey" FOREIGN KEY ("caseCaseId") REFERENCES "public"."Case"("caseId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CaseGroupMembership" ADD CONSTRAINT "CaseGroupMembership_caseGroupId_fkey" FOREIGN KEY ("caseGroupId") REFERENCES "public"."CaseGroup"("caseGroupId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CaseGroupMembership" ADD CONSTRAINT "CaseGroupMembership_caseId_fkey" FOREIGN KEY ("caseId") REFERENCES "public"."Case"("caseId") ON DELETE RESTRICT ON UPDATE CASCADE;
