-- CreateEnum
CREATE TYPE "public"."ReportAttachmentRelatedTo" AS ENUM ('REPORT', 'TIMELINE_ENTRY');

-- CreateEnum
CREATE TYPE "public"."PrecheckStatus" AS ENUM ('EMPTY', 'CHECKED', 'DECLINED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "public"."PrecheckAuthorType" AS ENUM ('AGENT', 'AI');

-- Create<PERSON>num
CREATE TYPE "public"."ReportSummaryAuthorType" AS ENUM ('AGENT', 'AI');

-- CreateEnum
CREATE TYPE "public"."ReportTimelineEntryType" AS ENUM ('COMMENT', 'DOCUMENTS', 'ACTION', 'SUMMARY');

-- CreateEnum
CREATE TYPE "public"."ReportTimelineAuthorType" AS ENUM ('AGENT', 'CUSTOMER', 'AI');

-- CreateTable
CREATE TABLE "public"."Agent" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "confirmed" BOOLEAN NOT NULL DEFAULT false,
    "blocked" BOOLEAN NOT NULL DEFAULT false,
    "roleId" INTEGER,
    "agencyId" INTEGER,
    "agencyNumber" TEXT,
    "agentNumber" TEXT,
    "street" TEXT,
    "houseNumber" TEXT,
    "city" TEXT,
    "companyName" TEXT,
    "url" TEXT,
    "telephoneNumber" TEXT,
    "commission" DOUBLE PRECISION,
    "postalCode" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Role" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Agency" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "agencyName" TEXT NOT NULL,
    "agencyNumber" TEXT NOT NULL,
    "isAdmin" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agency_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Customer" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "salutation" TEXT,
    "namePrefix" TEXT,
    "firstName" TEXT,
    "lastName" TEXT,
    "street" TEXT,
    "houseNumber" TEXT,
    "city" TEXT,
    "email" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "customerNumber" TEXT NOT NULL,
    "careOf" TEXT,
    "agentNumber" TEXT,
    "postalCode" TEXT,
    "agencyId" INTEGER,
    "agencyNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Customer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."CalculationParameter" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "hausratVersicherteGefahren" DOUBLE PRECISION,
    "hausratIsElementar" DOUBLE PRECISION,
    "hausratTax" DOUBLE PRECISION,
    "hausratLivingAreaFactor" DOUBLE PRECISION,
    "wohngebaudeBeitragsfaktor" DOUBLE PRECISION,
    "wohngebaudeVersicherteGefahren" DOUBLE PRECISION,
    "wohngebaudeIsElementar" DOUBLE PRECISION,
    "wohngebaudeHouseholdTech" DOUBLE PRECISION,
    "wohngebaudePvSystem" DOUBLE PRECISION,
    "wohngebaudeGlassInsurance" DOUBLE PRECISION,
    "wohngebaudeTax" DOUBLE PRECISION,
    "wohngebaudeSummenfaktor" DOUBLE PRECISION,
    "wohngebaudeGlasversicherungTax" DOUBLE PRECISION,
    "tierhalterhaftpflichtSaleDog" DOUBLE PRECISION,
    "tierhalterhaftpflichtSaleHorse" DOUBLE PRECISION,
    "tierhalterhaftpflichtDogPremie" DOUBLE PRECISION,
    "tierhalterhaftpflichtHorsePremie" DOUBLE PRECISION,
    "tierhalterhaftpflichtTax" DOUBLE PRECISION,
    "privathaftpflichtFamilyPremie" DOUBLE PRECISION,
    "privathaftpflichtSinglePremie" DOUBLE PRECISION,
    "privathaftpflichtTax" DOUBLE PRECISION,
    "hausUndGrundbesitzerhaftpflichtPremie" DOUBLE PRECISION,
    "hausUndGrundbesitzerhaftpflichtTax" DOUBLE PRECISION,
    "bauleistungSatz" DOUBLE PRECISION,
    "bauleistungMin" DOUBLE PRECISION,
    "bauleistungTax" DOUBLE PRECISION,
    "bauherrenhaftpflichtSatz" DOUBLE PRECISION,
    "bauherrenhaftpflichtMin" DOUBLE PRECISION,
    "bauherrenhaftpflichtTax" DOUBLE PRECISION,
    "geschaeftsversicherungIsElementar" DOUBLE PRECISION,
    "geschaeftsversicherungMin" DOUBLE PRECISION,
    "geschaeftsversicherungHandel" DOUBLE PRECISION,
    "geschaeftsversicherungHandwerk" DOUBLE PRECISION,
    "geschaeftsversicherungDienstleistungen" DOUBLE PRECISION,
    "geschaeftsversicherungGastronomie" DOUBLE PRECISION,
    "geschaeftsversicherungTax" DOUBLE PRECISION,
    "gebaeudeversicherungIsElementar" DOUBLE PRECISION,
    "gebaeudeversicherungMin" DOUBLE PRECISION,
    "gebaeudeversicherungHandel" DOUBLE PRECISION,
    "gebaeudeversicherungHandwerk" DOUBLE PRECISION,
    "gebaeudeversicherungDienstleistungen" DOUBLE PRECISION,
    "gebaeudeversicherungGastronomie" DOUBLE PRECISION,
    "gebaeudeversicherungTax" DOUBLE PRECISION,
    "betriebshaftpflichtHandel" DOUBLE PRECISION,
    "betriebshaftpflichtHandwerk" DOUBLE PRECISION,
    "betriebshaftpflichtDienstleistungen" DOUBLE PRECISION,
    "betriebshaftpflichtGastronomie" DOUBLE PRECISION,
    "betriebshaftpflichtTax" DOUBLE PRECISION,
    "unfallBerufsgruppeAMehrleistung" DOUBLE PRECISION,
    "unfallBerufsgruppeBMehrleistung" DOUBLE PRECISION,
    "unfallKinderMehrleistung" DOUBLE PRECISION,
    "unfallSeniorenMehrleistung" DOUBLE PRECISION,
    "unfallBerufsgruppeAInvaliditaet225" DOUBLE PRECISION,
    "unfallBerufsgruppeBInvaliditaet225" DOUBLE PRECISION,
    "unfallKinderInvaliditaet225" DOUBLE PRECISION,
    "unfallSeniorenInvaliditaet225" DOUBLE PRECISION,
    "unfallBerufsgruppeAInvaliditaet350" DOUBLE PRECISION,
    "unfallBerufsgruppeBInvaliditaet350" DOUBLE PRECISION,
    "unfallKinderInvaliditaet350" DOUBLE PRECISION,
    "unfallSeniorenInvaliditaet350" DOUBLE PRECISION,
    "unfallBerufsgruppeAInvaliditaet500" DOUBLE PRECISION,
    "unfallBerufsgruppeBInvaliditaet500" DOUBLE PRECISION,
    "unfallKinderInvaliditaet500" DOUBLE PRECISION,
    "unfallSeniorenInvaliditaet500" DOUBLE PRECISION,
    "unfallBerufsgruppeAUnfallrente" DOUBLE PRECISION,
    "unfallBerufsgruppeBUnfallrente" DOUBLE PRECISION,
    "unfallKinderUnfallrente" DOUBLE PRECISION,
    "unfallSeniorenUnfallrente" DOUBLE PRECISION,
    "unfallBerufsgruppeAUnfalltod" DOUBLE PRECISION,
    "unfallBerufsgruppeBUnfalltod" DOUBLE PRECISION,
    "unfallKinderUnfalltod" DOUBLE PRECISION,
    "unfallSeniorenUnfalltod" DOUBLE PRECISION,
    "unfallBerufsgruppeAKrankentagegeld" DOUBLE PRECISION,
    "unfallBerufsgruppeBKrankentagegeld" DOUBLE PRECISION,
    "unfallKinderKrankentagegeld" DOUBLE PRECISION,
    "unfallSeniorenKrankentagegeld" DOUBLE PRECISION,
    "unfallBerufsgruppeAKrankenhaustagegeld" DOUBLE PRECISION,
    "unfallBerufsgruppeBKrankenhaustagegeld" DOUBLE PRECISION,
    "unfallKinderKrankenhaustagegeld" DOUBLE PRECISION,
    "unfallSeniorenKrankenhaustagegeld" DOUBLE PRECISION,
    "unfallBerufsgruppeAUebergangsleistung" DOUBLE PRECISION,
    "unfallBerufsgruppeBUebergangsleistung" DOUBLE PRECISION,
    "unfallKinderUebergangsleistung" DOUBLE PRECISION,
    "unfallSeniorenUebergangsleistung" DOUBLE PRECISION,
    "unfallBerufsgruppeAErsteHilfe" DOUBLE PRECISION,
    "unfallBerufsgruppeBErsteHilfe" DOUBLE PRECISION,
    "unfallKinderErsteHilfe" DOUBLE PRECISION,
    "unfallSeniorenErsteHilfe" DOUBLE PRECISION,
    "unfallTax" DOUBLE PRECISION,
    "validFrom" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CalculationParameter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Attachment" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "contractNumber" TEXT,
    "bucketPath" TEXT,
    "agentNumber" TEXT,
    "type" TEXT,
    "reportNumber" TEXT,
    "agentId" INTEGER,
    "contractId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Attachment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Contract" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "contractType" TEXT,
    "insuranceStartDate" TEXT,
    "insuranceEndDate" TEXT,
    "paymentMode" TEXT,
    "iban" TEXT,
    "bic" TEXT,
    "previousInsurance" TEXT,
    "isOffer" BOOLEAN NOT NULL DEFAULT true,
    "riskStreet" TEXT,
    "riskHouseNumber" TEXT,
    "riskCity" TEXT,
    "buildingType" TEXT,
    "isPermanentlyOccupied" BOOLEAN NOT NULL DEFAULT false,
    "insuranceSum" DOUBLE PRECISION,
    "isElementar" BOOLEAN NOT NULL DEFAULT false,
    "coverageAmount" TEXT,
    "objectType" TEXT,
    "tariffGroup" TEXT,
    "livingArea" DOUBLE PRECISION,
    "insuranceSum1914" DOUBLE PRECISION,
    "constructionYear" INTEGER,
    "coverageUsage" DOUBLE PRECISION,
    "householdTech" BOOLEAN NOT NULL DEFAULT false,
    "pvSystem" BOOLEAN NOT NULL DEFAULT false,
    "glassInsurance" BOOLEAN NOT NULL DEFAULT false,
    "animalType" TEXT,
    "privateFirstName" TEXT,
    "privateName" TEXT,
    "familyCoverage" BOOLEAN NOT NULL DEFAULT false,
    "isSingle" BOOLEAN NOT NULL DEFAULT false,
    "premiumRate" DOUBLE PRECISION,
    "buildingSum" DOUBLE PRECISION,
    "premie" DOUBLE PRECISION,
    "premieHouseholdTech" DOUBLE PRECISION,
    "premiePvSystem" DOUBLE PRECISION,
    "premieGlassInsurance" DOUBLE PRECISION,
    "animalData" JSONB,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "contractNumber" TEXT NOT NULL,
    "customerNumber" TEXT,
    "agencyNumber" TEXT,
    "agentNumber" TEXT,
    "additionalAgreements" TEXT,
    "coveredRisks" BOOLEAN NOT NULL DEFAULT false,
    "businessType" TEXT,
    "tax" DOUBLE PRECISION,
    "invoiceAmount" DOUBLE PRECISION,
    "previousClaims" BOOLEAN NOT NULL DEFAULT false,
    "zuersZone" TEXT,
    "shareData" JSONB,
    "individualUnit" TEXT,
    "isIndividualUnit" BOOLEAN NOT NULL DEFAULT false,
    "commission" DOUBLE PRECISION,
    "contractStatus" JSONB,
    "fromOffer" TEXT,
    "toContract" TEXT,
    "isConstructionYearUnknown" BOOLEAN NOT NULL DEFAULT false,
    "monumentProtection" BOOLEAN NOT NULL DEFAULT false,
    "sgk" DOUBLE PRECISION,
    "riskLivingUnitAmount" DOUBLE PRECISION,
    "riskCommercialUnitAmount" DOUBLE PRECISION,
    "riskGerageAmount" DOUBLE PRECISION,
    "commercialArea" DOUBLE PRECISION,
    "garageArea" DOUBLE PRECISION,
    "previousInsuranceNumber" TEXT,
    "riskPostalCode" TEXT,
    "isIndividuallyCalculated" BOOLEAN NOT NULL DEFAULT false,
    "glassTax" DOUBLE PRECISION,
    "activeStatus" TEXT,
    "employeeCount" INTEGER,
    "riskAddresses" JSONB,
    "insuredPersons" JSONB,
    "insuranceMainDueDate" TEXT,
    "firstInvoiceNet" DOUBLE PRECISION,
    "firstInvoiceTax" DOUBLE PRECISION,
    "firstInvoiceGross" DOUBLE PRECISION,
    "firstInvoiceGlassNet" DOUBLE PRECISION,
    "firstInvoiceGlassTax" DOUBLE PRECISION,
    "firstInvoiceGlassGross" DOUBLE PRECISION,
    "customerId" INTEGER,
    "agencyId" INTEGER,
    "agentId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Contract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."InsuranceCondition" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "productCode" TEXT,
    "productName" TEXT,
    "riskCarrierCode" TEXT,
    "riskCarrierName" TEXT,
    "validFrom" TEXT,
    "validTo" TEXT,
    "bucketPath" TEXT,
    "relevantContractTypes" TEXT,
    "fileName" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InsuranceCondition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Invoice" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "versionNumber" DOUBLE PRECISION,
    "invoiceDetailStatus" JSONB,
    "dueDate" TEXT,
    "contractNumber" TEXT,
    "totalNet" DOUBLE PRECISION,
    "totalGross" DOUBLE PRECISION,
    "positions" JSONB,
    "billingStreet" TEXT,
    "billingHouseNumber" TEXT,
    "billingCity" TEXT,
    "billingPostalCode" TEXT,
    "firstName" TEXT,
    "lastName" TEXT,
    "namePrefix" TEXT,
    "invoiceNumber" TEXT NOT NULL,
    "insuranceStartDate" TEXT,
    "insuranceEndDate" TEXT,
    "iban" TEXT,
    "bic" TEXT,
    "billingCareOf" TEXT,
    "agentCompanyName" TEXT,
    "agentStreet" TEXT,
    "agentHouseNumber" TEXT,
    "agentPostalCode" TEXT,
    "agentCity" TEXT,
    "paymentMode" TEXT,
    "agentNumber" TEXT,
    "customerNumber" TEXT,
    "type" INTEGER,
    "invoiceStatus" INTEGER,
    "agentStatus" INTEGER,
    "customerStatus" INTEGER,
    "insuranceStatus" INTEGER,
    "subject" TEXT,
    "automaticallyGenerated" BOOLEAN NOT NULL DEFAULT false,
    "agencyNumber" TEXT,
    "contractId" INTEGER,
    "customerId" INTEGER,
    "agencyId" INTEGER,
    "agentId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Invoice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."BankTransaction" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "bookingDay" TEXT,
    "valuta" TEXT,
    "bankSortCode" TEXT,
    "accountNumber" TEXT,
    "name" TEXT,
    "bookingInformation" TEXT,
    "purposeOfUse" TEXT,
    "amount" DOUBLE PRECISION,
    "currency" TEXT,
    "bankStatementNumber" TEXT,
    "orderReference" TEXT,
    "client" TEXT,
    "invoiceNumber" TEXT,
    "info" TEXT,
    "account" TEXT,
    "invoiceId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BankTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."InvoiceTransaction" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "invoiceNumber" TEXT,
    "contractNumber" TEXT,
    "difference" DOUBLE PRECISION,
    "type" TEXT,
    "invoiceId" INTEGER,
    "contractId" INTEGER,
    "bankTransactionId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvoiceTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."InvoiceSequence" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "contractNumber" TEXT,
    "sequence" DOUBLE PRECISION,
    "contractId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InvoiceSequence_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Report" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "text" TEXT,
    "customerNumber" TEXT,
    "contractNumber" TEXT,
    "reportNumber" TEXT NOT NULL,
    "damageDate" TEXT,
    "damageLocation" TEXT,
    "iban" TEXT,
    "coveredRisk" TEXT,
    "agencyNumber" TEXT,
    "externalReportNumber" TEXT,
    "dataRaw" JSONB,
    "customerId" INTEGER,
    "contractId" INTEGER,
    "agencyId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportAttachment" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "bucketPath" TEXT,
    "relatedTo" "public"."ReportAttachmentRelatedTo",
    "relatedEntityId" TEXT,
    "name" TEXT,
    "reportId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportAttachment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportCoverageAssessment" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "coverageAssessment" TEXT,
    "coverageAssessmentConfidence" DOUBLE PRECISION,
    "coverageAssessmentIndication" TEXT,
    "fraudAssessment" TEXT,
    "fraudAssessmentConfidence" DOUBLE PRECISION,
    "fraudAssessmentIndication" TEXT,
    "relevantReferences" JSONB,
    "reportNumber" TEXT,
    "reportId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportCoverageAssessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportPrecheck" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "anotherId" TEXT,
    "index" DOUBLE PRECISION,
    "title" TEXT,
    "precheckStatus" "public"."PrecheckStatus",
    "timestamp" TEXT,
    "tooltip" TEXT,
    "agentNumber" TEXT,
    "authorType" "public"."PrecheckAuthorType",
    "reportNumber" TEXT,
    "reportId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportPrecheck_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportPrecheckTemplate" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "anotherId" TEXT,
    "insuranceType" TEXT,
    "title" TEXT,
    "index" DOUBLE PRECISION,
    "tooltip" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportPrecheckTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportSummary" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "reportNumber" TEXT,
    "authorType" "public"."ReportSummaryAuthorType",
    "text" TEXT,
    "timestamp" TIMESTAMP(3),
    "reportId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportSummary_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."ReportTimelineEntry" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "entryType" "public"."ReportTimelineEntryType",
    "reportNumber" TEXT,
    "authorType" "public"."ReportTimelineAuthorType",
    "authorId" TEXT,
    "timestamp" TEXT,
    "title" TEXT,
    "content" TEXT,
    "files" JSONB,
    "metadata" TEXT,
    "relatedEntries" JSONB,
    "reportId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportTimelineEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Theme" (
    "id" SERIAL NOT NULL,
    "documentId" TEXT NOT NULL,
    "themeString" TEXT,
    "companyName" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Theme_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Agent_documentId_key" ON "public"."Agent"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Agent_email_key" ON "public"."Agent"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Agent_agentNumber_key" ON "public"."Agent"("agentNumber");

-- CreateIndex
CREATE UNIQUE INDEX "Agency_documentId_key" ON "public"."Agency"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Agency_agencyNumber_key" ON "public"."Agency"("agencyNumber");

-- CreateIndex
CREATE UNIQUE INDEX "Customer_documentId_key" ON "public"."Customer"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Customer_customerNumber_key" ON "public"."Customer"("customerNumber");

-- CreateIndex
CREATE UNIQUE INDEX "CalculationParameter_documentId_key" ON "public"."CalculationParameter"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Attachment_documentId_key" ON "public"."Attachment"("documentId");

-- CreateIndex
CREATE INDEX "Attachment_contractNumber_idx" ON "public"."Attachment"("contractNumber");

-- CreateIndex
CREATE INDEX "Attachment_reportNumber_idx" ON "public"."Attachment"("reportNumber");

-- CreateIndex
CREATE INDEX "Attachment_agentId_idx" ON "public"."Attachment"("agentId");

-- CreateIndex
CREATE INDEX "Attachment_contractId_idx" ON "public"."Attachment"("contractId");

-- CreateIndex
CREATE UNIQUE INDEX "Contract_documentId_key" ON "public"."Contract"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Contract_contractNumber_key" ON "public"."Contract"("contractNumber");

-- CreateIndex
CREATE INDEX "Contract_contractNumber_idx" ON "public"."Contract"("contractNumber");

-- CreateIndex
CREATE INDEX "Contract_customerId_idx" ON "public"."Contract"("customerId");

-- CreateIndex
CREATE INDEX "Contract_agencyId_idx" ON "public"."Contract"("agencyId");

-- CreateIndex
CREATE INDEX "Contract_agentId_idx" ON "public"."Contract"("agentId");

-- CreateIndex
CREATE UNIQUE INDEX "InsuranceCondition_documentId_key" ON "public"."InsuranceCondition"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Invoice_documentId_key" ON "public"."Invoice"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Invoice_invoiceNumber_key" ON "public"."Invoice"("invoiceNumber");

-- CreateIndex
CREATE INDEX "Invoice_invoiceNumber_idx" ON "public"."Invoice"("invoiceNumber");

-- CreateIndex
CREATE INDEX "Invoice_contractNumber_idx" ON "public"."Invoice"("contractNumber");

-- CreateIndex
CREATE INDEX "Invoice_contractId_idx" ON "public"."Invoice"("contractId");

-- CreateIndex
CREATE INDEX "Invoice_customerId_idx" ON "public"."Invoice"("customerId");

-- CreateIndex
CREATE INDEX "Invoice_agencyId_idx" ON "public"."Invoice"("agencyId");

-- CreateIndex
CREATE INDEX "Invoice_agentId_idx" ON "public"."Invoice"("agentId");

-- CreateIndex
CREATE UNIQUE INDEX "BankTransaction_documentId_key" ON "public"."BankTransaction"("documentId");

-- CreateIndex
CREATE INDEX "BankTransaction_invoiceNumber_idx" ON "public"."BankTransaction"("invoiceNumber");

-- CreateIndex
CREATE INDEX "BankTransaction_bankStatementNumber_idx" ON "public"."BankTransaction"("bankStatementNumber");

-- CreateIndex
CREATE INDEX "BankTransaction_orderReference_idx" ON "public"."BankTransaction"("orderReference");

-- CreateIndex
CREATE INDEX "BankTransaction_invoiceId_idx" ON "public"."BankTransaction"("invoiceId");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceTransaction_documentId_key" ON "public"."InvoiceTransaction"("documentId");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_invoiceNumber_idx" ON "public"."InvoiceTransaction"("invoiceNumber");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_contractNumber_idx" ON "public"."InvoiceTransaction"("contractNumber");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_invoiceId_idx" ON "public"."InvoiceTransaction"("invoiceId");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_contractId_idx" ON "public"."InvoiceTransaction"("contractId");

-- CreateIndex
CREATE INDEX "InvoiceTransaction_bankTransactionId_idx" ON "public"."InvoiceTransaction"("bankTransactionId");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceTransaction_invoiceId_bankTransactionId_key" ON "public"."InvoiceTransaction"("invoiceId", "bankTransactionId");

-- CreateIndex
CREATE UNIQUE INDEX "InvoiceSequence_documentId_key" ON "public"."InvoiceSequence"("documentId");

-- CreateIndex
CREATE INDEX "InvoiceSequence_contractNumber_idx" ON "public"."InvoiceSequence"("contractNumber");

-- CreateIndex
CREATE INDEX "InvoiceSequence_contractId_idx" ON "public"."InvoiceSequence"("contractId");

-- CreateIndex
CREATE UNIQUE INDEX "Report_documentId_key" ON "public"."Report"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "Report_reportNumber_key" ON "public"."Report"("reportNumber");

-- CreateIndex
CREATE INDEX "Report_reportNumber_idx" ON "public"."Report"("reportNumber");

-- CreateIndex
CREATE INDEX "Report_customerNumber_idx" ON "public"."Report"("customerNumber");

-- CreateIndex
CREATE INDEX "Report_contractNumber_idx" ON "public"."Report"("contractNumber");

-- CreateIndex
CREATE INDEX "Report_agencyNumber_idx" ON "public"."Report"("agencyNumber");

-- CreateIndex
CREATE INDEX "Report_customerId_idx" ON "public"."Report"("customerId");

-- CreateIndex
CREATE INDEX "Report_contractId_idx" ON "public"."Report"("contractId");

-- CreateIndex
CREATE INDEX "Report_agencyId_idx" ON "public"."Report"("agencyId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportAttachment_documentId_key" ON "public"."ReportAttachment"("documentId");

-- CreateIndex
CREATE INDEX "ReportAttachment_relatedEntityId_idx" ON "public"."ReportAttachment"("relatedEntityId");

-- CreateIndex
CREATE INDEX "ReportAttachment_reportId_idx" ON "public"."ReportAttachment"("reportId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportCoverageAssessment_documentId_key" ON "public"."ReportCoverageAssessment"("documentId");

-- CreateIndex
CREATE INDEX "ReportCoverageAssessment_reportNumber_idx" ON "public"."ReportCoverageAssessment"("reportNumber");

-- CreateIndex
CREATE INDEX "ReportCoverageAssessment_reportId_idx" ON "public"."ReportCoverageAssessment"("reportId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportPrecheck_documentId_key" ON "public"."ReportPrecheck"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportPrecheck_anotherId_key" ON "public"."ReportPrecheck"("anotherId");

-- CreateIndex
CREATE INDEX "ReportPrecheck_reportNumber_idx" ON "public"."ReportPrecheck"("reportNumber");

-- CreateIndex
CREATE INDEX "ReportPrecheck_agentNumber_idx" ON "public"."ReportPrecheck"("agentNumber");

-- CreateIndex
CREATE INDEX "ReportPrecheck_reportId_idx" ON "public"."ReportPrecheck"("reportId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportPrecheckTemplate_documentId_key" ON "public"."ReportPrecheckTemplate"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportPrecheckTemplate_anotherId_key" ON "public"."ReportPrecheckTemplate"("anotherId");

-- CreateIndex
CREATE INDEX "ReportPrecheckTemplate_insuranceType_idx" ON "public"."ReportPrecheckTemplate"("insuranceType");

-- CreateIndex
CREATE UNIQUE INDEX "ReportSummary_documentId_key" ON "public"."ReportSummary"("documentId");

-- CreateIndex
CREATE INDEX "ReportSummary_reportNumber_idx" ON "public"."ReportSummary"("reportNumber");

-- CreateIndex
CREATE INDEX "ReportSummary_reportId_idx" ON "public"."ReportSummary"("reportId");

-- CreateIndex
CREATE UNIQUE INDEX "ReportTimelineEntry_documentId_key" ON "public"."ReportTimelineEntry"("documentId");

-- CreateIndex
CREATE INDEX "ReportTimelineEntry_reportNumber_idx" ON "public"."ReportTimelineEntry"("reportNumber");

-- CreateIndex
CREATE INDEX "ReportTimelineEntry_authorId_idx" ON "public"."ReportTimelineEntry"("authorId");

-- CreateIndex
CREATE INDEX "ReportTimelineEntry_reportId_idx" ON "public"."ReportTimelineEntry"("reportId");

-- CreateIndex
CREATE UNIQUE INDEX "Theme_documentId_key" ON "public"."Theme"("documentId");

-- CreateIndex
CREATE INDEX "Theme_companyName_idx" ON "public"."Theme"("companyName");

-- AddForeignKey
ALTER TABLE "public"."Agent" ADD CONSTRAINT "Agent_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "public"."Role"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Agent" ADD CONSTRAINT "Agent_agencyId_fkey" FOREIGN KEY ("agencyId") REFERENCES "public"."Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Customer" ADD CONSTRAINT "Customer_agencyId_fkey" FOREIGN KEY ("agencyId") REFERENCES "public"."Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Attachment" ADD CONSTRAINT "Attachment_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "public"."Agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Attachment" ADD CONSTRAINT "Attachment_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Contract" ADD CONSTRAINT "Contract_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "public"."Customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Contract" ADD CONSTRAINT "Contract_agencyId_fkey" FOREIGN KEY ("agencyId") REFERENCES "public"."Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Contract" ADD CONSTRAINT "Contract_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "public"."Agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Invoice" ADD CONSTRAINT "Invoice_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Invoice" ADD CONSTRAINT "Invoice_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "public"."Customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Invoice" ADD CONSTRAINT "Invoice_agencyId_fkey" FOREIGN KEY ("agencyId") REFERENCES "public"."Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Invoice" ADD CONSTRAINT "Invoice_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "public"."Agent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."BankTransaction" ADD CONSTRAINT "BankTransaction_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "public"."Invoice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvoiceTransaction" ADD CONSTRAINT "InvoiceTransaction_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "public"."Invoice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvoiceTransaction" ADD CONSTRAINT "InvoiceTransaction_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvoiceTransaction" ADD CONSTRAINT "InvoiceTransaction_bankTransactionId_fkey" FOREIGN KEY ("bankTransactionId") REFERENCES "public"."BankTransaction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvoiceSequence" ADD CONSTRAINT "InvoiceSequence_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Report" ADD CONSTRAINT "Report_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "public"."Customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Report" ADD CONSTRAINT "Report_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "public"."Contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Report" ADD CONSTRAINT "Report_agencyId_fkey" FOREIGN KEY ("agencyId") REFERENCES "public"."Agency"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ReportAttachment" ADD CONSTRAINT "ReportAttachment_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."Report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ReportCoverageAssessment" ADD CONSTRAINT "ReportCoverageAssessment_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."Report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ReportPrecheck" ADD CONSTRAINT "ReportPrecheck_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."Report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ReportSummary" ADD CONSTRAINT "ReportSummary_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."Report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."ReportTimelineEntry" ADD CONSTRAINT "ReportTimelineEntry_reportId_fkey" FOREIGN KEY ("reportId") REFERENCES "public"."Report"("id") ON DELETE SET NULL ON UPDATE CASCADE;
