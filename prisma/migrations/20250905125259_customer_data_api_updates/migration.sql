-- CreateEnum
CREATE TYPE "public"."RelationType" AS ENUM ('paying', 'paying_and_insured_primary', 'insured_primary', 'child', 'spouse');

-- DropIndex
DROP INDEX "public"."Customer_email_key";

-- AlterTable
ALTER TABLE "public"."Customer" ALTER COLUMN "email" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."Questionnaire" (
    "questionnaireId" SERIAL NOT NULL,
    "caseCustomerId" INTEGER NOT NULL,
    "formId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'Pending',
    "answersJson" JSONB,

    CONSTRAINT "Questionnaire_pkey" PRIMARY KEY ("questionnaireId")
);

-- CreateTable
CREATE TABLE "public"."CaseCustomer" (
    "caseCustomerId" SERIAL NOT NULL,
    "caseId" INTEGER NOT NULL,
    "customerId" INTEGER NOT NULL,
    "relationType" "public"."RelationType",
    "email" TEXT,

    CONSTRAINT "CaseCustomer_pkey" PRIMARY KEY ("caseCustomerId")
);

-- CreateIndex
CREATE UNIQUE INDEX "CaseCustomer_caseId_customerId_key" ON "public"."CaseCustomer"("caseId", "customerId");

-- AddForeignKey
ALTER TABLE "public"."Questionnaire" ADD CONSTRAINT "Questionnaire_caseCustomerId_fkey" FOREIGN KEY ("caseCustomerId") REFERENCES "public"."CaseCustomer"("caseCustomerId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CaseCustomer" ADD CONSTRAINT "CaseCustomer_caseId_fkey" FOREIGN KEY ("caseId") REFERENCES "public"."Case"("caseId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."CaseCustomer" ADD CONSTRAINT "CaseCustomer_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "public"."Customer"("customerId") ON DELETE RESTRICT ON UPDATE CASCADE;
