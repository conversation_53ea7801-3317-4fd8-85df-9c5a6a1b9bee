/*
  Warnings:

  - The `status` column on the `Case` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "public"."CaseStatus" AS ENUM ('DataEntry', 'RiskInquiryRunning', 'ApplicationReady', 'Processing', 'Applied', 'Rejected');

-- AlterTable
ALTER TABLE "public"."Case" DROP COLUMN "status",
ADD COLUMN     "status" "public"."CaseStatus" NOT NULL DEFAULT 'DataEntry';
