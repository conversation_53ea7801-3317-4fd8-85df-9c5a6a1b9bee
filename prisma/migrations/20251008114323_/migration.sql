-- AlterTable
ALTER TABLE "public"."Attachment" ADD COLUMN     "applicationId" INTEGER;

-- AlterTable
ALTER TABLE "public"."Message" ADD COLUMN     "applicationId" INTEGER,
ADD COLUMN     "riskPreRequestId" INTEGER;

-- AddForeignKey
ALTER TABLE "public"."Message" ADD CONSTRAINT "Message_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "public"."Application"("applicationId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Message" ADD CONSTRAINT "Message_riskPreRequestId_fkey" FOREIGN KEY ("riskPreRequestId") REFERENCES "public"."RiskPreRequest"("riskPreRequestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "public"."Attachment" ADD CONSTRAINT "Attachment_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "public"."Application"("applicationId") ON DELETE SET NULL ON UPDATE CASCADE;
