/*
  Warnings:

  - You are about to drop the column `offerPdfPath` on the `Offer` table. All the data in the column will be lost.
  - You are about to drop the column `policyPdfPath` on the `Policy` table. All the data in the column will be lost.
  - You are about to drop the column `city` on the `PreviousInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `houseNumber` on the `PreviousInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `postalCode` on the `PreviousInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `street` on the `PreviousInsurance` table. All the data in the column will be lost.
  - You are about to drop the column `agreementPdfPath` on the `Tip` table. All the data in the column will be lost.
  - You are about to drop the column `totalCommission` on the `Tip` table. All the data in the column will be lost.
  - Added the required column `salutation` to the `Customer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expiresAt` to the `Offer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `pdf` to the `Offer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `premium` to the `Offer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `commission` to the `Policy` table without a default value. This is not possible if the table is not empty.
  - Added the required column `pdf` to the `Policy` table without a default value. This is not possible if the table is not empty.
  - Added the required column `premium` to the `Policy` table without a default value. This is not possible if the table is not empty.
  - Made the column `firstName` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `lastName` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `street` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `houseNumber` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `postalCode` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `city` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `phoneNumber` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `agencyNumber` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `salesDirection` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Made the column `iban` on table `Referrer` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `agreementPdf` to the `Tip` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."Customer" ADD COLUMN     "salutation" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."Offer" DROP COLUMN "offerPdfPath",
ADD COLUMN     "expiresAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "pdf" TEXT NOT NULL,
ADD COLUMN     "premium" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "productExtensions" TEXT[];

-- AlterTable
ALTER TABLE "public"."Policy" DROP COLUMN "policyPdfPath",
ADD COLUMN     "commission" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "pdf" TEXT NOT NULL,
ADD COLUMN     "premium" DOUBLE PRECISION NOT NULL;

-- AlterTable
ALTER TABLE "public"."PreviousInsurance" DROP COLUMN "city",
DROP COLUMN "houseNumber",
DROP COLUMN "postalCode",
DROP COLUMN "street";

-- AlterTable
ALTER TABLE "public"."Referrer" ADD COLUMN     "isAdmin" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN "firstName" SET NOT NULL,
ALTER COLUMN "lastName" SET NOT NULL,
ALTER COLUMN "street" SET NOT NULL,
ALTER COLUMN "houseNumber" SET NOT NULL,
ALTER COLUMN "postalCode" SET NOT NULL,
ALTER COLUMN "city" SET NOT NULL,
ALTER COLUMN "phoneNumber" SET NOT NULL,
ALTER COLUMN "agencyNumber" SET NOT NULL,
ALTER COLUMN "salesDirection" SET NOT NULL,
ALTER COLUMN "iban" SET NOT NULL;

-- AlterTable
ALTER TABLE "public"."Tip" DROP COLUMN "agreementPdfPath",
DROP COLUMN "totalCommission",
ADD COLUMN     "agreementPdf" TEXT NOT NULL,
ADD COLUMN     "commission" DOUBLE PRECISION,
ADD COLUMN     "risk" TEXT;

-- CreateTable
CREATE TABLE "public"."CustomerAccessToken" (
    "id" SERIAL NOT NULL,
    "customerId" INTEGER NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "usedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CustomerAccessToken_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CustomerAccessToken_token_key" ON "public"."CustomerAccessToken"("token");

-- AddForeignKey
ALTER TABLE "public"."CustomerAccessToken" ADD CONSTRAINT "CustomerAccessToken_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "public"."Customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
