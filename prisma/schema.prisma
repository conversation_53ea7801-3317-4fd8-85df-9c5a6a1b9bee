generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // oder "mysql" / "sqlite" je nach DB
  url      = env("DATABASE_URL")
}

model Referrer {
  id             Int      @id @default(autoincrement())
  isAd<PERSON>  @default(false)
  email          String   @unique
  firstName      String
  lastName       String
  street         String
  houseNumber    String
  postalCode     String
  city           String
  phoneNumber    String
  agencyNumber   String
  salesDirection String
  iban           String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  tips         Tip[]
  accessTokens ReferrerAccessToken[]
}

model ReferrerAccessToken {
  id         Int       @id @default(autoincrement())
  referrer   Referrer  @relation(fields: [referrerId], references: [id])
  referrerId Int
  token      String    @unique
  expiresAt  DateTime
  usedAt     DateTime?
  createdAt  DateTime  @default(now())
}

model CustomerAccessToken {
  id         Int       @id @default(autoincrement())
  customer   Customer  @relation(fields: [customerId], references: [id])
  customerId Int
  token      String    @unique
  expiresAt  DateTime
  usedAt     DateTime?
  createdAt  DateTime  @default(now())
}

model Product {
  id          Int      @id @default(autoincrement())
  name        String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  tips Tip[]
}

model Customer {
  id          Int      @id @default(autoincrement())
  salutation  String
  firstName   String
  lastName    String
  street      String
  houseNumber String
  postalCode  String
  city        String
  phoneNumber String
  email       String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  tips         Tip[]
  accessTokens CustomerAccessToken[]
}

model PreviousInsurance {
  id             Int      @id @default(autoincrement())
  contractNumber String
  consentGiven   Boolean
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  tip Tip?
}

model Tip {
  id                  Int               @id @default(autoincrement())
  referrer            Referrer          @relation(fields: [referrerId], references: [id])
  referrerId          Int
  product             Product           @relation(fields: [productId], references: [id])
  productId           Int
  customer            Customer          @relation(fields: [customerId], references: [id])
  customerId          Int
  previousInsurance   PreviousInsurance @relation(fields: [previousInsuranceId], references: [id])
  previousInsuranceId Int               @unique // <-- FIX

  agreementPdf String // S3 path
  status       String
  commission   Float?
  risk         String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  invitations Invitation[]
  offer       Offer?
  policy      Policy?
  documents   Document[]
}

model Invitation {
  id              Int       @id @default(autoincrement())
  tip             Tip       @relation(fields: [tipId], references: [id])
  tipId           Int
  invitationToken String
  sentAt          DateTime?
  acceptedAt      DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model Offer {
  id                Int       @id @default(autoincrement())
  tip               Tip       @relation(fields: [tipId], references: [id])
  tipId             Int       @unique
  pdf               String
  shareWithReferrer Boolean
  premium           Float
  productExtensions String[]
  requestedAt       DateTime?
  receivedAt        DateTime?
  expiresAt         DateTime
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

model Policy {
  id         Int      @id @default(autoincrement())
  tip        Tip      @relation(fields: [tipId], references: [id])
  tipId      Int      @unique
  pdf        String
  premium    Float
  commission Float
  issuedAt   DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Document {
  id           Int      @id @default(autoincrement())
  tip          Tip      @relation(fields: [tipId], references: [id])
  tipId        Int
  documentType String
  filePath     String
  createdAt    DateTime @default(now())
}

generator types {
  provider        = "prisma-types-generator"
  output          = "../src/generated/types"
  createDtoPrefix = "Create"
  updateDtoPrefix = "Update"
  dtoSuffix       = "Dto"
  entityPrefix    = "" // optional
  entitySuffix    = "Entity" // optional
}
