generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

model Customer {
    customerId    Int      @id @default(autoincrement())
    firstName     String
    lastName      String
    dateOfBirth   DateTime
    gender        String?
    email         String?
    phoneNumber   String?
    street        String?
    houseNumber   String?
    postalCode    String?
    city          String?
    country       String?  @default("Germany")
    salutation    String?
    academicTitle String?
    agencyName    String?
    status        String   @default("Active") // Active, Invited, Draft
    createdAt     DateTime @default(now())
    cases         Case[]

    // caseCustomer  CaseCustomer[]
}

model User {
    userId                     Int                  @id @default(autoincrement())
    keycloakId                 String?              @unique // Keycloak user ID (sub claim)
    username                   String               @unique
    role                       String
    firstName                  String?
    lastName                   String?
    email                      String               @unique
    phoneNumber                String?
    street                     String?
    houseNumber                String?
    postalCode                 String?
    city                       String?
    country                    String?
    createdAt                  DateTime             @default(now())
    lastLogin                  DateTime?
    salutation                 String?
    academicTitle              String?
    agencyName                 String?
    termsAndConditionsAccepted Boolean              @default(false)
    attachments                Attachment[]         @relation("UploadedAttachments")
    assignedCases              Case[]               @relation("AssignedCases")
    caseUsers                  CaseUser[]
    receivedMessages           Message[]            @relation("ReceivedMessages")
    sentMessages               Message[]            @relation("SentMessages")
    PotentialInsurance         PotentialInsurance[]
}

model Case {
    caseId               Int                   @id @default(autoincrement())
    caseNumber           String                @unique @default(dbgenerated("((upper(substr(md5((random())::text), 1, 3)) || '-'::text) || substr(md5((random())::text), 1, 3))"))
    customerId           Int
    assignedUserId       Int?
    caseType             String
    createdAt            DateTime              @default(now())
    dataInputProgress    Json?
    status               CaseStatus            @default(DataEntry)
    applications         Application[]
    attachments          Attachment[]
    assignedUser         User?                 @relation("AssignedCases", fields: [assignedUserId], references: [userId])
    customer             Customer              @relation(fields: [customerId], references: [customerId])
    caseUsers            CaseUser[]
    questionnaires       Questionnaire[]
    quickChecks          QuickCheck[]
    riskPreRequests      RiskPreRequest[]
    messages             Message[]
    potentialInsurances  PotentialInsurance[]
    caseGroupMembership  CaseGroupMembership[]
    CaseGroup            CaseGroup?            @relation(fields: [caseGroupCaseGroupId], references: [caseGroupId])
    caseGroupCaseGroupId Int?
}

model Questionnaire {
    questionnaireId Int      @id @default(autoincrement())
    formId          String
    createdAt       DateTime @default(now())
    type            String
    status          String   @default("Pending")
    answersJson     Json?
    Case            Case?    @relation(fields: [caseCaseId], references: [caseId])
    caseCaseId      Int?
    // caseCustomerId  Int
    // caseCustomer    CaseCustomer @relation(fields: [caseCustomerId], references: [caseCustomerId])
}

model CaseGroup {
    caseGroupId     Int      @id @default(autoincrement())
    assignedUserId  Int?
    groupType       String
    description     String
    createdAt       DateTime @default(now())
    caseGroupNumber String   @unique @default(dbgenerated("((upper(substr(md5((random())::text), 1, 3)) || '-'::text) || substr(md5((random())::text), 1, 3))"))

    memberships CaseGroupMembership[]
    cases       Case[]
}

model CaseGroupMembership {
    caseGroupMembershipId Int    @id @default(autoincrement())
    caseGroupId           Int
    caseId                Int
    relationType          String

    caseGroup CaseGroup @relation(fields: [caseGroupId], references: [caseGroupId])
    case      Case      @relation(fields: [caseId], references: [caseId])
}

// model CaseCustomer {
//     caseCustomerId Int             @id @default(autoincrement())
//     caseId         Int
//     customerId     Int
//     relationType   RelationType?
//     email          String?
//     case           Case            @relation(fields: [caseId], references: [caseId])
//     customer       Customer        @relation(fields: [customerId], references: [customerId])
//     questionnaires Questionnaire[]

//     @@unique([caseId, customerId])
// }

model CaseUser {
    caseUserId Int     @id @default(autoincrement())
    caseId     Int
    userId     Int
    roleInCase String?
    case       Case    @relation(fields: [caseId], references: [caseId])
    user       User    @relation(fields: [userId], references: [userId])
}

model QuickCheck {
    quickCheckId Int      @id @default(autoincrement())
    caseId       Int
    checkDate    DateTime @default(now())
    result       String?
    notes        String?
    case         Case     @relation(fields: [caseId], references: [caseId])
}

// Case status enum based on business workflow from flowchart
enum CaseStatus {
    // Initial phase
    DataEntry // Dateneingabe - entering customer data

    // Risk inquiry phase
    RiskInquiryRunning // Risiko-Voranfrage läuft - risk pre-inquiry submitted

    // Application phase
    ApplicationReady // Antrag bereit - application ready to submit
    Processing // Nachbearbeitung - requires additional processing/data

    // Final states
    Applied // Beantragt - application submitted successfully
    Rejected // Abgelehnt - application rejected

    // Legacy compatibility (can be removed later)
    Open // Legacy: offen
}

// Antrag läuft: application in progress,  Nachbearbeitung: input required, Abgelehnt: application denied,   Antrag bereit: application ready, Antrag eingereicht: application sent

enum PotentialInsuranceStatus {
    Pending

    InProgress
    InputRequired
    Denied
    Ready
    Sent
}

model RiskPreRequest {
    riskPreRequestId Int                      @id @default(autoincrement())
    caseId           Int
    submittedAt      DateTime                 @default(now())
    status           PotentialInsuranceStatus @default(Pending)
    insurerName      String
    tariffName       String
    monthlyPremium   Decimal?                 @db.Decimal(10, 2)
    additionalFee    Decimal?                 @db.Decimal(10, 2)
    agentSelect      Boolean                  @default(false)

    medicalHistory String?
    remarks        String?
    attachments    Attachment[]
    case           Case         @relation(fields: [caseId], references: [caseId])
    Message        Message[]
}

model Application {
    applicationId     Int          @id @default(autoincrement())
    caseId            Int
    submittedAt       DateTime     @default(now())
    status            String       @default("Pending")
    planType          String?
    premiumAmount     Decimal?     @db.Decimal(10, 2)
    coverageStartDate DateTime?
    coverageEndDate   DateTime?
    remarks           String?
    attachments       Attachment[]
    case              Case         @relation(fields: [caseId], references: [caseId])
    Message           Message[]
}

model Message {
    messageId        Int             @id @default(autoincrement())
    caseId           Int
    senderId         Int
    recipientId      Int
    riskPreRequestId Int?
    applicationId    Int?
    sentAt           DateTime        @default(now())
    subject          String?
    body             String?
    isRead           Boolean         @default(false)
    attachments      Attachment[]
    application      Application?    @relation(fields: [applicationId], references: [applicationId])
    riskPreRequest   RiskPreRequest? @relation(fields: [riskPreRequestId], references: [riskPreRequestId])
    case             Case            @relation(fields: [caseId], references: [caseId])
    recipient        User            @relation("ReceivedMessages", fields: [recipientId], references: [userId])
    sender           User            @relation("SentMessages", fields: [senderId], references: [userId])
}

model Attachment {
    attachmentId       Int             @id @default(autoincrement())
    caseId             Int?
    messageId          Int?
    riskPreRequestId   Int?
    applicationId      Int?
    linkedAttachmentId Int?
    status             String?
    fileName           String
    filePath           String
    uploadedAt         DateTime        @default(now())
    uploadedBy         Int
    documentType       DocumentType?
    case               Case?           @relation(fields: [caseId], references: [caseId])
    message            Message?        @relation(fields: [messageId], references: [messageId])
    riskPreRequest     RiskPreRequest? @relation(fields: [riskPreRequestId], references: [riskPreRequestId])
    uploadedByUser     User            @relation("UploadedAttachments", fields: [uploadedBy], references: [userId])
    application        Application?    @relation(fields: [applicationId], references: [applicationId])
}

enum DocumentType {
    examination_booklet
    medical_report
    lab_report
    hospital_report
    vaccination_card
    application_form
    authorization_consent
    rehab_report
    medication_plan
    medical_certificate
    psychological_report
    surgery_report
    followup_report
    termination_confirmation
    advisory_protocol
    health_self_disclosure
    risk_pre_request
    insurance_policy
    modified_document
    additional_information
    other_document
}

model PotentialInsurance {
    potentialInsuranceId Int      @id @default(autoincrement())
    caseId               Int
    userId               Int?
    insurerName          String
    productName          String
    monthlyPremium       Decimal? @db.Decimal(10, 2)

    coverageDetails   String?
    notes             String?
    status            String?
    createdByUserType PotentialInsuranceCreatedBy @default(user)
    case              Case                        @relation(fields: [caseId], references: [caseId])
    user              User?                       @relation(fields: [userId], references: [userId])
}

enum PotentialInsuranceCreatedBy {
    user
    admin
    ai
}

enum RelationType {
    paying
    paying_and_insured_primary
    insured_primary
    child
    spouse
}
