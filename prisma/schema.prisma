datasource db {
  provider = "postgresql" // oder "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Customer {
  customerId    Int      @id @default(autoincrement())
  firstName     String
  lastName      String
  dateOfBirth   DateTime
  gender        String?
  email         String   @unique
  phoneNumber   String?
  street        String?
  houseNumber   String?
  postalCode    String?
  city          String?
  country       String?  @default("Germany")
  createdAt     DateTime @default(now())

  cases         Case[]
}

model User {
  userId        Int      @id @default(autoincrement())
  username      String   @unique
  passwordHash  String
  role          String
  firstName     String?
  lastName      String?
  email         String   @unique
  phoneNumber   String?
  street        String?
  houseNumber   String?
  postalCode    String?
  city          String?
  country       String?
  createdAt     DateTime @default(now())
  lastLogin     DateTime?

  assignedCases Case[]   @relation("AssignedCases")
  sentMessages  Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")
  attachments   Attachment[] @relation("UploadedAttachments")
  caseUsers     CaseUser[]
}

model Case {
  caseId         Int       @id @default(autoincrement())
  caseNumber     String    @unique @default(dbgenerated("upper(substr(md5(random()::text), 1, 3)) || '-' || substr(md5(random()::text), 1, 3)"))
  customerId     Int
  assignedUserId Int?
  caseType       String
  createdAt      DateTime  @default(now())
  status         String    @default("Open")

  customer       Customer  @relation(fields: [customerId], references: [customerId])
  assignedUser   User?     @relation("AssignedCases", fields: [assignedUserId], references: [userId])

  caseUsers      CaseUser[]
  quickChecks    QuickCheck[]
  riskPreRequests RiskPreRequest[]
  applications   Application[]
  messages       Message[]
  attachments    Attachment[]
  potentialInsurances PotentialInsurance[]
}

model CaseUser {
  caseUserId Int    @id @default(autoincrement())
  caseId     Int
  userId     Int
  roleInCase String?

  case       Case   @relation(fields: [caseId], references: [caseId])
  user       User   @relation(fields: [userId], references: [userId])
}

model QuickCheck {
  quickCheckId Int      @id @default(autoincrement())
  caseId       Int
  checkDate    DateTime @default(now())
  result       String?
  notes        String?

  case         Case     @relation(fields: [caseId], references: [caseId])
}

model RiskPreRequest {
  riskPreRequestId Int      @id @default(autoincrement())
  caseId           Int
  submittedAt      DateTime @default(now())
  status           String   @default("Pending")
  medicalHistory   String?
  remarks          String?

  case             Case     @relation(fields: [caseId], references: [caseId])
}

model Application {
  applicationId     Int      @id @default(autoincrement())
  caseId            Int
  submittedAt       DateTime @default(now())
  status            String   @default("Pending")
  planType          String?
  premiumAmount     Decimal? @db.Decimal(10, 2)
  coverageStartDate DateTime?
  coverageEndDate   DateTime?
  remarks           String?

  case              Case     @relation(fields: [caseId], references: [caseId])
}

model Message {
  messageId   Int      @id @default(autoincrement())
  caseId      Int
  senderId    Int
  recipientId Int
  sentAt      DateTime @default(now())
  subject     String?
  body        String?
  isRead      Boolean  @default(false)

  case        Case     @relation(fields: [caseId], references: [caseId])
  sender      User     @relation("SentMessages", fields: [senderId], references: [userId])
  recipient   User     @relation("ReceivedMessages", fields: [recipientId], references: [userId])
  attachments Attachment[]
}

model Attachment {
  attachmentId Int      @id @default(autoincrement())
  caseId       Int?
  messageId    Int?
  fileName     String
  filePath     String
  uploadedAt   DateTime @default(now())
  uploadedBy   Int

  case         Case?    @relation(fields: [caseId], references: [caseId])
  message      Message? @relation(fields: [messageId], references: [messageId])
  uploadedByUser User   @relation("UploadedAttachments", fields: [uploadedBy], references: [userId])
}

model PotentialInsurance {
  potentialInsuranceId Int      @id @default(autoincrement())
  caseId               Int
  insurerName          String
  productName          String
  monthlyPremium       Decimal? @db.Decimal(10, 2)
  coverageDetails      String?
  notes                String?

  case                 Case     @relation(fields: [caseId], references: [caseId])
}
