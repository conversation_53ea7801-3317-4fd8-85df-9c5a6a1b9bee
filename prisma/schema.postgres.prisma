// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma-postgres"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URI")
}

model Agent {
  id               Int       @id @default(autoincrement())
  /// Stable UUID you can expose publicly
  documentId       String    @unique @default(cuid())

  username         String
  email            String    @unique
  confirmed        <PERSON>olean   @default(false)
  blocked          <PERSON>olean   @default(false)

  // Relations
  roleId           Int?
  role             Role?     @relation(fields: [roleId], references: [id], onDelete: SetNull)

  agencyId         Int?
  agency           Agency?   @relation(fields: [agencyId], references: [id], onDelete: SetNull)

  attachments      Attachment[]
  contracts        Contract[]
  invoices         Invoice[]

  // Extra profile fields
  agencyNumber     String?
  agentNumber      String?  @unique
  street           String?
  houseNumber      String?
  city             String?
  companyName      String?
  url              String?
  telephoneNumber  String?
  commission       Float?
  postalCode       String?

  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model Role {
  id    Int    @id @default(autoincrement())
  name  String
  users Agent[]
}

model Agency {
  id           Int        @id @default(autoincrement())
  documentId   String     @unique @default(cuid())

  agencyName   String
  agencyNumber String     @unique
  isAdmin      Boolean    @default(false)

  // Relations
  users        Agent[]
  customers    Customer[]
  contracts    Contract[]
  invoices     Invoice[]
  reports      Report[]

  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
}

model Customer {
  id             Int      @id @default(autoincrement())
  documentId     String   @unique @default(cuid())

  salutation     String?
  namePrefix     String?
  firstName      String?
  lastName       String?
  street         String?
  houseNumber    String?
  city           String?
  email          String?
  active         Boolean  @default(false)
  customerNumber String   @unique

  careOf         String?
  agentNumber    String?
  postalCode     String?

  // Relation to Agency (N:1)
  agencyId       Int?
  agencyNumber   String?
  agency         Agency?  @relation(fields: [agencyId], references: [id], onDelete: SetNull)

  contracts      Contract[]
  invoices       Invoice[]
  reports        Report[]

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model CalculationParameter {
  id                                    Int     @id @default(autoincrement())
  documentId                             String  @unique @default(cuid())

  // Hausrat
  hausratVersicherteGefahren             Float?
  hausratIsElementar                     Float?
  hausratTax                             Float?
  hausratLivingAreaFactor                Float?

  // Wohngebäude
  wohngebaudeBeitragsfaktor              Float?
  wohngebaudeVersicherteGefahren         Float?
  wohngebaudeIsElementar                 Float?
  wohngebaudeHouseholdTech               Float?
  wohngebaudePvSystem                    Float?
  wohngebaudeGlassInsurance              Float?
  wohngebaudeTax                         Float?
  wohngebaudeSummenfaktor                Float?
  wohngebaudeGlasversicherungTax         Float?

  // Tierhalterhaftpflicht
  tierhalterhaftpflichtSaleDog           Float?
  tierhalterhaftpflichtSaleHorse         Float?
  tierhalterhaftpflichtDogPremie         Float?
  tierhalterhaftpflichtHorsePremie       Float?
  tierhalterhaftpflichtTax               Float?

  // Privathaftpflicht
  privathaftpflichtFamilyPremie          Float?
  privathaftpflichtSinglePremie          Float?
  privathaftpflichtTax                   Float?

  // Haus- und Grundbesitzerhaftpflicht
  hausUndGrundbesitzerhaftpflichtPremie  Float?
  hausUndGrundbesitzerhaftpflichtTax     Float?

  // Bauleistung
  bauleistungSatz                        Float?
  bauleistungMin                         Float?
  bauleistungTax                         Float?

  // Bauherrenhaftpflicht
  bauherrenhaftpflichtSatz               Float?
  bauherrenhaftpflichtMin                Float?
  bauherrenhaftpflichtTax                Float?

  // Geschäftsversicherung
  geschaeftsversicherungIsElementar      Float?
  geschaeftsversicherungMin              Float?
  geschaeftsversicherungHandel           Float?
  geschaeftsversicherungHandwerk         Float?
  geschaeftsversicherungDienstleistungen Float?
  geschaeftsversicherungGastronomie      Float?
  geschaeftsversicherungTax              Float?

  // Gebäudeversicherung (gewerblich)
  gebaeudeversicherungIsElementar        Float?
  gebaeudeversicherungMin                Float?
  gebaeudeversicherungHandel             Float?
  gebaeudeversicherungHandwerk           Float?
  gebaeudeversicherungDienstleistungen   Float?
  gebaeudeversicherungGastronomie        Float?
  gebaeudeversicherungTax                Float?

  // Betriebshaftpflicht
  betriebshaftpflichtHandel              Float?
  betriebshaftpflichtHandwerk            Float?
  betriebshaftpflichtDienstleistungen    Float?
  betriebshaftpflichtGastronomie         Float?
  betriebshaftpflichtTax                 Float?

  // Unfall – Mehrleistungen
  unfallBerufsgruppeAMehrleistung        Float?
  unfallBerufsgruppeBMehrleistung        Float?
  unfallKinderMehrleistung               Float?
  unfallSeniorenMehrleistung             Float?

  // Unfall – Invalidität 225%
  unfallBerufsgruppeAInvaliditaet225     Float?
  unfallBerufsgruppeBInvaliditaet225     Float?
  unfallKinderInvaliditaet225            Float?
  unfallSeniorenInvaliditaet225          Float?

  // Unfall – Invalidität 350%
  unfallBerufsgruppeAInvaliditaet350     Float?
  unfallBerufsgruppeBInvaliditaet350     Float?
  unfallKinderInvaliditaet350            Float?
  unfallSeniorenInvaliditaet350          Float?

  // Unfall – Invalidität 500%
  unfallBerufsgruppeAInvaliditaet500     Float?
  unfallBerufsgruppeBInvaliditaet500     Float?
  unfallKinderInvaliditaet500            Float?
  unfallSeniorenInvaliditaet500          Float?

  // Unfall – Rente
  unfallBerufsgruppeAUnfallrente         Float?
  unfallBerufsgruppeBUnfallrente         Float?
  unfallKinderUnfallrente                Float?
  unfallSeniorenUnfallrente              Float?

  // Unfall – Unfalltod
  unfallBerufsgruppeAUnfalltod           Float?
  unfallBerufsgruppeBUnfalltod           Float?
  unfallKinderUnfalltod                  Float?
  unfallSeniorenUnfalltod                Float?

  // Unfall – Krankentagegeld
  unfallBerufsgruppeAKrankentagegeld     Float?
  unfallBerufsgruppeBKrankentagegeld     Float?
  unfallKinderKrankentagegeld            Float?
  unfallSeniorenKrankentagegeld          Float?

  // Unfall – Krankenhaustagegeld
  unfallBerufsgruppeAKrankenhaustagegeld Float?
  unfallBerufsgruppeBKrankenhaustagegeld Float?
  unfallKinderKrankenhaustagegeld        Float?
  unfallSeniorenKrankenhaustagegeld      Float?

  // Unfall – Übergangsleistung
  unfallBerufsgruppeAUebergangsleistung  Float?
  unfallBerufsgruppeBUebergangsleistung  Float?
  unfallKinderUebergangsleistung         Float?
  unfallSeniorenUebergangsleistung       Float?

  // Unfall – Erste Hilfe
  unfallBerufsgruppeAErsteHilfe          Float?
  unfallBerufsgruppeBErsteHilfe          Float?
  unfallKinderErsteHilfe                 Float?
  unfallSeniorenErsteHilfe               Float?

  // Unfall – Steuer
  unfallTax                              Float?

  // Misc
  validFrom                               String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Attachment {
  id             Int       @id @default(autoincrement())
  documentId     String    @unique @default(cuid())

  contractNumber String?
  bucketPath     String?
  agentNumber    String?
  type           String?
  reportNumber   String?

  // Relations
  agentId        Int?
  agent          Agent?     @relation(fields: [agentId], references: [id], onDelete: SetNull)

  contractId     Int?
  contract       Contract? @relation(fields: [contractId], references: [id], onDelete: SetNull)

  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@index([contractNumber])
  @@index([reportNumber])
  @@index([agentId])
  @@index([contractId])
}

model Contract {
  id                           Int       @id @default(autoincrement())
  documentId                   String    @unique @default(cuid())

  contractType                 String?
  insuranceStartDate           String?
  insuranceEndDate             String?
  paymentMode                  String?
  iban                         String?
  bic                          String?
  previousInsurance            String?
  isOffer                      Boolean   @default(true)
  riskStreet                   String?
  riskHouseNumber              String?
  riskCity                     String?
  buildingType                 String?
  isPermanentlyOccupied        Boolean   @default(false)
  insuranceSum                 Float?
  isElementar                  Boolean   @default(false)
  coverageAmount               String?
  objectType                   String?
  tariffGroup                  String?
  livingArea                   Float?
  insuranceSum1914             Float?
  constructionYear             Int?
  coverageUsage                Float?
  householdTech                Boolean   @default(false)
  pvSystem                     Boolean   @default(false)
  glassInsurance               Boolean   @default(false)
  animalType                   String?
  privateFirstName             String?
  privateName                  String?
  familyCoverage               Boolean   @default(false)
  isSingle                     Boolean   @default(false)
  premiumRate                  Float?
  buildingSum                  Float?
  premie                       Float?
  premieHouseholdTech          Float?
  premiePvSystem               Float?
  premieGlassInsurance         Float?
  animalData                   Json?
  active                       Boolean   @default(false)
  contractNumber               String    @unique
  customerNumber               String?
  agencyNumber                 String?
  agentNumber                  String?
  additionalAgreements         String?
  coveredRisks                 Boolean   @default(false)
  businessType                 String?
  tax                          Float?
  invoiceAmount                Float?
  previousClaims               Boolean   @default(false)
  zuersZone                    String?
  shareData                    Json?
  individualUnit               String?
  isIndividualUnit             Boolean   @default(false)
  commission                   Float?
  contractStatus               Json?
  fromOffer                    String?
  toContract                   String?
  isConstructionYearUnknown    Boolean   @default(false)
  monumentProtection           Boolean   @default(false)
  sgk                          Float?
  riskLivingUnitAmount         Float?
  riskCommercialUnitAmount     Float?
  riskGerageAmount             Float?
  commercialArea               Float?
  garageArea                   Float?
  previousInsuranceNumber      String?
  riskPostalCode               String?
  isIndividuallyCalculated     Boolean   @default(false)
  glassTax                     Float?
  activeStatus                 String?
  employeeCount                Int?
  riskAddresses                Json?
  insuredPersons               Json?
  insuranceMainDueDate         String?
  firstInvoiceNet              Float?
  firstInvoiceTax              Float?
  firstInvoiceGross            Float?
  firstInvoiceGlassNet         Float?
  firstInvoiceGlassTax         Float?
  firstInvoiceGlassGross       Float?

  // Relations
  customerId                   Int?
  customer                     Customer?   @relation(fields: [customerId], references: [id], onDelete: SetNull)

  agencyId                     Int?
  agency                       Agency?     @relation(fields: [agencyId], references: [id], onDelete: SetNull)

  agentId                      Int?
  agent                        Agent?       @relation(fields: [agentId], references: [id], onDelete: SetNull)

  attachments                  Attachment[]
  invoices                     Invoice[]
  invoiceTransactions          InvoiceTransaction[]
  invoiceSequences             InvoiceSequence[]
  reports                      Report[]

  createdAt                    DateTime    @default(now())
  updatedAt                    DateTime    @updatedAt

  @@index([contractNumber])
  @@index([customerId])
  @@index([agencyId])
  @@index([agentId])
}

model InsuranceCondition {
  id                     Int      @id @default(autoincrement())
  documentId             String   @unique @default(cuid())

  productCode            String?
  productName            String?
  riskCarrierCode        String?
  riskCarrierName        String?
  validFrom              String?
  validTo                String?
  bucketPath             String?
  relevantContractTypes  String?
  fileName               String?

  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
}

model Invoice {
  id                     Int       @id @default(autoincrement())
  documentId             String    @unique @default(cuid())

  versionNumber          Float?
  invoiceDetailStatus    Json?
  dueDate                String?
  contractNumber         String?
  totalNet               Float?
  totalGross             Float?
  positions              Json?
  billingStreet          String?
  billingHouseNumber     String?
  billingCity            String?
  billingPostalCode      String?
  firstName              String?
  lastName               String?
  namePrefix             String?
  invoiceNumber          String    @unique
  insuranceStartDate     String?
  insuranceEndDate       String?
  iban                   String?
  bic                    String?
  billingCareOf          String?
  agentCompanyName       String?
  agentStreet            String?
  agentHouseNumber       String?
  agentPostalCode        String?
  agentCity              String?
  paymentMode            String?
  agentNumber            String?
  customerNumber         String?
  type                   Int?
  invoiceStatus          Int?
  agentStatus            Int?
  customerStatus         Int?
  insuranceStatus        Int?
  subject                String?
  automaticallyGenerated Boolean   @default(false)
  agencyNumber           String?

  // Relations
  contractId             Int?
  contract               Contract?  @relation(fields: [contractId], references: [id], onDelete: SetNull)

  customerId             Int?
  customer               Customer?  @relation(fields: [customerId], references: [id], onDelete: SetNull)

  agencyId               Int?
  agency                 Agency?    @relation(fields: [agencyId], references: [id], onDelete: SetNull)

  agentId                Int?
  agent                  Agent?      @relation(fields: [agentId], references: [id], onDelete: SetNull)

  bankTransactions       BankTransaction[]
  invoiceTransactions    InvoiceTransaction[]

  createdAt              DateTime   @default(now())
  updatedAt              DateTime   @updatedAt

  @@index([invoiceNumber])
  @@index([contractNumber])
  @@index([contractId])
  @@index([customerId])
  @@index([agencyId])
  @@index([agentId])
}

model BankTransaction {
  id                   Int      @id @default(autoincrement())
  documentId           String   @unique @default(cuid())

  bookingDay           String?
  valuta               String?
  bankSortCode         String?
  accountNumber        String?
  name                 String?
  bookingInformation   String?
  purposeOfUse         String?
  amount               Float?
  currency             String?
  bankStatementNumber  String?
  orderReference       String?
  client               String?
  invoiceNumber        String?
  info                 String?
  account              String?

  // Optional link to an Invoice
  invoiceId            Int?
  invoice              Invoice? @relation(fields: [invoiceId], references: [id], onDelete: SetNull)

  invoiceTransactions  InvoiceTransaction[]

  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@index([invoiceNumber])
  @@index([bankStatementNumber])
  @@index([orderReference])
  @@index([invoiceId])
}

model InvoiceTransaction {
  id                 Int               @id @default(autoincrement())
  documentId         String            @unique @default(cuid())

  // From table
  invoiceNumber      String?
  contractNumber     String?
  difference         Float?
  type               String?

  // Relations
  invoiceId          Int?
  invoice            Invoice?          @relation(fields: [invoiceId], references: [id], onDelete: SetNull)

  contractId         Int?
  contract           Contract?         @relation(fields: [contractId], references: [id], onDelete: SetNull)

  bankTransactionId  Int?
  bankTransaction    BankTransaction?  @relation(fields: [bankTransactionId], references: [id], onDelete: SetNull)

  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt

  @@unique([invoiceId, bankTransactionId])
  @@index([invoiceNumber])
  @@index([contractNumber])
  @@index([invoiceId])
  @@index([contractId])
  @@index([bankTransactionId])
}

model InvoiceSequence {
  id             Int       @id @default(autoincrement())
  documentId     String    @unique @default(cuid())

  contractNumber String?
  sequence       Float?

  // Optional relation to Contract
  contractId     Int?
  contract       Contract? @relation(fields: [contractId], references: [id], onDelete: SetNull)

  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@index([contractNumber])
  @@index([contractId])
}

model Report {
  id                    Int       @id @default(autoincrement())
  documentId            String    @unique @default(cuid())

  text                  String?
  customerNumber        String?
  contractNumber        String?
  reportNumber          String    @unique
  damageDate            String?
  damageLocation        String?
  iban                  String?
  coveredRisk           String?
  agencyNumber          String?
  externalReportNumber  String?
  dataRaw               Json?

  // Optional relations by IDs (robust links)
  customerId            Int?
  customer              Customer?  @relation(fields: [customerId], references: [id], onDelete: SetNull)

  contractId            Int?
  contract              Contract?  @relation(fields: [contractId], references: [id], onDelete: SetNull)

  agencyId              Int?
  agency                Agency?    @relation(fields: [agencyId], references: [id], onDelete: SetNull)

  attachments           ReportAttachment[]
  coverageAssessments   ReportCoverageAssessment[]
  prechecks             ReportPrecheck[]
  summaries             ReportSummary[]
  timelineEntries       ReportTimelineEntry[]

  createdAt             DateTime   @default(now())
  updatedAt             DateTime   @updatedAt

  @@index([reportNumber])
  @@index([customerNumber])
  @@index([contractNumber])
  @@index([agencyNumber])
  @@index([customerId])
  @@index([contractId])
  @@index([agencyId])
}

enum ReportAttachmentRelatedTo {
  REPORT
  TIMELINE_ENTRY
}

model ReportAttachment {
  id              Int       @id @default(autoincrement())
  documentId      String    @unique @default(cuid())

  bucketPath      String?
  relatedTo       ReportAttachmentRelatedTo?
  relatedEntityId String?
  name            String?

  // Optional direct link to Report (robust FK)
  reportId        Int?
  report          Report?   @relation(fields: [reportId], references: [id], onDelete: SetNull)

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([relatedEntityId])
  @@index([reportId])
}

model ReportCoverageAssessment {
  id                             Int       @id @default(autoincrement())
  documentId                     String    @unique @default(cuid())

  coverageAssessment             String?
  coverageAssessmentConfidence   Float?
  coverageAssessmentIndication   String?
  fraudAssessment                String?
  fraudAssessmentConfidence      Float?
  fraudAssessmentIndication      String?
  relevantReferences             Json?
  reportNumber                   String?

  // Relation to Report
  reportId                       Int?
  report                         Report?   @relation(fields: [reportId], references: [id], onDelete: SetNull)

  createdAt                      DateTime  @default(now())
  updatedAt                      DateTime  @updatedAt

  @@index([reportNumber])
  @@index([reportId])
}

enum PrecheckStatus {
  EMPTY
  CHECKED
  DECLINED
}

enum PrecheckAuthorType {
  AGENT
  AI
}

model ReportPrecheck {
  id             Int                 @id @default(autoincrement())
  documentId     String              @unique @default(cuid())

  anotherId      String?             @unique
  index          Float?
  title          String?
  precheckStatus PrecheckStatus?
  timestamp      String?
  tooltip        String?
  agentNumber    String?
  authorType     PrecheckAuthorType?
  reportNumber   String?

  // Relation to Report (optional)
  reportId       Int?
  report         Report?             @relation(fields: [reportId], references: [id], onDelete: SetNull)

  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt

  @@index([reportNumber])
  @@index([agentNumber])
  @@index([reportId])
}

model ReportPrecheckTemplate {
  id             Int      @id @default(autoincrement())
  documentId     String   @unique @default(cuid())

  anotherId      String?  @unique
  insuranceType  String?
  title          String?
  index          Float?
  tooltip        String?

  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([insuranceType])
}

enum ReportSummaryAuthorType {
  AGENT
  AI
}

model ReportSummary {
  id           Int                      @id @default(autoincrement())
  documentId   String                   @unique @default(cuid())

  reportNumber String?
  authorType   ReportSummaryAuthorType?
  text         String?
  timestamp    DateTime?

  // Relation to Report
  reportId     Int?
  report       Report?                  @relation(fields: [reportId], references: [id], onDelete: SetNull)

  createdAt    DateTime                  @default(now())
  updatedAt    DateTime                  @updatedAt

  @@index([reportNumber])
  @@index([reportId])
}

enum ReportTimelineEntryType {
  COMMENT
  DOCUMENTS
  ACTION
  SUMMARY
}

enum ReportTimelineAuthorType {
  AGENT
  CUSTOMER
  AI
}

model ReportTimelineEntry {
  id             Int                      @id @default(autoincrement())
  documentId     String                   @unique @default(cuid())

  entryType      ReportTimelineEntryType?
  reportNumber   String?
  authorType     ReportTimelineAuthorType?
  authorId       String?
  timestamp      String?
  title          String?
  content        String?
  files          Json?
  metadata       String?
  relatedEntries Json?

  // Relation to Report (optional robust FK)
  reportId       Int?
  report         Report?                  @relation(fields: [reportId], references: [id], onDelete: SetNull)

  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt

  @@index([reportNumber])
  @@index([authorId])
  @@index([reportId])
}

model Theme {
  id           Int      @id @default(autoincrement())
  documentId   String   @unique @default(cuid())

  themeString  String?
  companyName  String?

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([companyName])
}
