import nextJest from 'next/jest.js';
const createJestConfig = nextJest({ dir: './' });

const customJestConfig = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  transformIgnorePatterns: [
    //  This actually gets overwritten by createJestConfig
    //  so we splice it in again below.
    'node_modules/(?!(next-intl|use-intl)/)', // 👈 transpile these ESM deps
  ],
};

const result = async () => {
  const base = await createJestConfig(customJestConfig)();
  return {
    ...base,
    transform: {
      ...base.transform,
      '^.+\\.(t|j)sx?$': [
        'babel-jest',
        { configFile: './babel.jest.config.js' }, // Jest-only Babel config
      ],
    },
    transformIgnorePatterns: [
      'node_modules/(?!(next-intl|use-intl)/)', // 👈 transpile these ESM deps
    ],
  };
};

export default result;
