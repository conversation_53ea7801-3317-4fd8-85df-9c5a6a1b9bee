DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
MONGO_URI="mongodb+srv://admin:<EMAIL>/kv_care?retryWrites=true&w=majority"

SITE_URL="http://localhost:3000"

STREAM_API_KEY=83qsf2nsjkde
STREAM_API_SECRET=tm6tu8ncryrnxaygc2nbwxgt4eekcvc2c3x6usj77spnfv27n7hqkqgrkn64h3g9

SESSION_SECRET="kv-care-super-secret-session-key-32-chars-minimum-length-required"
AUTH_SECRET="eo3uk3JIhWSlLqI3ECJ74z25Qk9SEv5dgdUL6Qb1Eg8=" # Added by `npx auth`. Read more: https://cli.authjs.dev
AUTH_URL=http://localhost:3000
AUTH_KEYCLOAK_ID=kv-care-web-dev
AUTH_KEYCLOAK_SECRET=4o46RrjOiQH02PMMg4Ma81bl99k0IC7N
AUTH_KEYCLOAK_ISSUER=https://login.dev.kvcare.de/realms/dev

NEXT_PUBLIC_AUTH_URL=http://localhost:3000
NEXT_PUBLIC_AUTH_KEYCLOAK_ID=kv-care-web-dev
NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER=https://login.dev.kvcare.de/realms/dev

# IONOS_ENDPOINT_URL
# IONOS_BUCKET_NAME
# IONOS_ACCESS_KEY_ID
# IONOS_SECRET_ACCESS_KEY
