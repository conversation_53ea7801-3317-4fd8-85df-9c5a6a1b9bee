{"name": "refeo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:drop": "npx prisma db push --force-reset", "prisma:reseed": "npm run prisma:drop && ts-node prisma/seed.ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@json2csv/plainjs": "^7.0.6", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@mui/x-date-pickers": "^8.10.0", "@prisma/client": "^6.14.0", "@react-email/components": "^0.5.1", "@react-email/render": "^1.2.1", "aws-sdk": "^2.1692.0", "dayjs": "^1.11.13", "jose": "^6.0.13", "jsonwebtoken": "^9.0.2", "keycloak-js": "^26.2.0", "next": "^15.5.2", "next-intl": "^4.3.4", "nodemailer": "^7.0.5", "pdf-lib": "^1.17.1", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^4.1.12"}, "devDependencies": {"@eslint/eslintrc": "^3", "@mainamiru/prisma-types-generator": "^1.1.14", "@types/chance": "^1.1.7", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^7.0.0", "@types/react": "^19", "@types/react-dom": "^19", "chance": "^1.1.13", "eslint": "^9", "eslint-config-next": "15.4.6", "prisma": "^6.14.0", "ts-node": "^10.9.2", "typescript": "^5"}}