{"name": "alpha", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@json2csv/plainjs": "^7.0.6", "@mui/icons-material": "^6.2.1", "@mui/lab": "^6.0.0-beta.32", "@mui/material": "^6.2.1", "@mui/x-date-pickers": "^8.7.0", "@strapi/client": "^1.4.0", "@types/formidable": "^3.4.5", "@types/luxon": "^3.6.2", "aws-sdk": "^2.1692.0", "dayjs": "^1.11.13", "formidable": "^3.5.2", "image-to-pdf": "^3.0.2", "jsondiffpatch": "^0.7.3", "luxon": "^3.6.1", "mongodb": "^6.16.0", "next": "^15.3.4", "nodemailer": "^6.10.0", "p-limit": "^6.2.0", "pdf-lib": "^1.17.1", "qs": "^6.14.0", "react": "^18", "react-dom": "^18", "react-markdown": "^10.1.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.14.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}