{"name": "kv-care", "version": "0.1.0", "private": true, "type": "module", "scripts": {"prepare": "husky", "prebuild": "npx prisma generate && npx prisma generate --schema ./prisma-mongo-db/schema.prisma", "predev": "npx prisma generate && npx prisma generate --schema ./prisma-mongo-db/schema.prisma", "dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "typegen": "next typegen", "ts:check": "tsc --noEmit", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\" \"*.{js,jsx,ts,tsx,mjs,cjs}\"", "lint:fix": "eslint \"src/**/*.{js,jsx,ts,tsx}\" \"*.{js,jsx,ts,tsx,mjs,cjs}\" --fix", "format:check": "prettier --check .", "format:write": "prettier --write .", "lint:format": "npm run format:write && npm run lint:fix", "seed": "ts-node prisma/seed.ts", "db:generate": "npx prisma generate && npx prisma generate --schema ./prisma-mongo-db/schema.prisma", "db:migrate": "npx prisma migrate dev", "db:migrate:mongo": "npx prisma db push --schema ./prisma-mongo-db/schema.prisma", "db:migrate:all": "npm run db:generate && npm run db:migrate && npm run db:migrate:mongo", "db:deploy": "npm run db:generate && npx prisma migrate deploy && npm run db:migrate:mongo", "db:studio": "npx prisma studio --port 5555", "db:studio:mongo": "npx prisma studio --schema ./prisma-mongo-db/schema.prisma --port 5556", "test": "jest", "test:watch": "npx jest --watch"}, "dependencies": {"@aws-sdk/client-s3": "3.896.0", "@aws-sdk/s3-request-presigner": "3.896.0", "@emotion/cache": "^11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@hookform/resolvers": "5.2.2", "@mui/icons-material": "7.3.2", "@mui/material": "7.3.2", "@mui/material-nextjs": "7.3.2", "@mui/x-date-pickers": "8.12.0", "@prisma/client": "6.16.2", "@t3-oss/env-nextjs": "^0.13.8", "@tanstack/react-query": "5.90.2", "@tanstack/react-query-devtools": "5.90.2", "@trpc/client": "11.6.0", "@trpc/server": "11.6.0", "@trpc/tanstack-react-query": "11.6.0", "@types/nodemailer": "7.0.1", "bcrypt": "6.0.0", "date-fns": "^4.1.0", "dayjs": "1.11.18", "fast-deep-equal": "3.1.3", "i18n-iso-countries": "^7.14.0", "iron-session": "8.0.4", "jsondiffpatch": "0.7.3", "jwt-decode": "4.0.0", "keycloak-js": "26.2.0", "lodash.debounce": "4.0.8", "next": "15.5.4", "next-auth": "5.0.0-beta.29", "next-intl": "4.3.9", "nuqs": "2.6.0", "prisma-type-generator": "1.13.0", "react": "19.2.0", "react-dom": "19.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "7.62.0", "react-number-format": "5.4.4", "server-only": "^0.0.1", "stream-chat": "9.19.1", "stream-chat-react": "13.6.5", "superjson": "^2.2.2", "use-deep-compare-effect": "1.8.1", "usehooks-ts": "^3.1.1", "zod": "4.1.11", "zustand": "5.0.8"}, "devDependencies": {"@babel/core": "7.28.4", "@eslint/eslintrc": "3", "@testing-library/jest-dom": "6.9.1", "@testing-library/react": "16.3.0", "@types/bcrypt": "6.0.0", "@types/lodash.debounce": "4.0.9", "@types/node": "24.5.2", "@types/react": "19.2.2", "@types/react-dom": "19.2.1", "babel-jest": "30.2.0", "eslint": "9.35.0", "eslint-config-next": "15.5.4", "eslint-config-prettier": "10.1.8", "eslint-plugin-no-relative-import-paths": "1.6.1", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-unused-imports": "4.2.0", "husky": "^9.1.7", "jest": "30.2.0", "jest-environment-jsdom": "30.2.0", "prettier": "3.6.2", "prisma": "6.16.2", "ts-node": "10.9.2", "tsx": "4.20.6", "typescript": "5"}}