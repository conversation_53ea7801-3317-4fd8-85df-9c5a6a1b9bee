{"name": "kv-care", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@prisma/client": "^6.13.0", "@types/nodemailer": "^6.4.17", "keycloak-js": "^26.2.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "prisma": "^6.13.0", "typescript": "^5"}}