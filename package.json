{"name": "alpha", "version": "0.1.0", "private": true, "type": "module", "scripts": {"prepare": "husky", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate:pg": "prisma generate --schema=prisma/schema.postgres.prisma", "prisma:generate:mongo": "prisma generate --schema=prisma/schema.mongo.prisma", "prisma:generate": "npm run prisma:generate:pg && npm run prisma:generate:mongo", "prisma:push": "prisma db push --schema ./prisma/schema.postgres.prisma && prisma db push --schema ./prisma/schema.mongo.prisma", "predev": "npm run prisma:generate", "ts:check": "tsc --noEmit", "lint:fix": "next lint --fix", "format:check": "prettier --check .", "format:write": "prettier --write .", "lint:format": "npm run format:write && npm run lint:fix", "db:generate": "npm run prisma:generate:pg && npm run prisma:generate:mongo", "db:migrate:mongo": "prisma db push --schema ./prisma/schema.mongo.prisma", "db:deploy": "npm run db:generate && prisma migrate deploy --schema=./prisma/schema.postgres.prisma && npm run db:migrate:mongo", "seed": "ts-node prisma/seed.ts"}, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@heroicons/react": "2.2.0", "@json2csv/plainjs": "7.0.6", "@mui/icons-material": "7.3.2", "@mui/lab": "^7.0.0-beta", "@mui/material": "7.3.2", "@mui/material-nextjs": "7.3.2", "@mui/x-date-pickers": "8.11.3", "@prisma/client": "6.16.2", "@strapi/client": "1.4.0", "@types/formidable": "3.4.5", "@types/luxon": "3.6.2", "aws-sdk": "2.1692.0", "dayjs": "1.11.13", "formidable": "3.5.4", "image-to-pdf": "3.0.2", "jsondiffpatch": "0.7.3", "luxon": "3.6.1", "mongodb": "6.16.0", "next": "15.5.3", "next-auth": "5.0.0-beta.29", "nodemailer": "6.10.0", "p-limit": "6.2.0", "pdf-lib": "1.17.1", "qs": "6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "10.1.0", "sqlite": "5.1.1", "sqlite3": "5.1.7", "uuid": "11.1.0"}, "devDependencies": {"@eslint/eslintrc": "3", "@types/node": "20", "@types/nodemailer": "6.4.17", "@types/qs": "6.14.0", "@types/react": "19", "@types/react-dom": "19", "@types/sqlite3": "3.1.11", "@types/uuid": "10.0.0", "autoprefixer": "10.4.20", "eslint": "9", "eslint-config-next": "15.4.6", "eslint-config-prettier": "10.1.8", "eslint-plugin-no-relative-import-paths": "1.6.1", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-unused-imports": "4.1.4", "husky": "9.1.7", "postcss": "8.4.47", "prettier": "3.2.5", "prisma": "6.14.0", "sass": "1.90.0", "tailwindcss": "3.4.14", "ts-node": "10.9.2", "typescript": "5"}}