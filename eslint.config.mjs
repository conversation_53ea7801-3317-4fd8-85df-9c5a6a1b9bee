import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
  recommendedConfig: js.configs.recommended,
});

const eslintConfig = [
  js.configs.recommended,
  ...compat.config({
    extends: ['next/core-web-vitals', 'next/typescript', 'prettier'],
    plugins: [
      'simple-import-sort',
      'unused-imports',
      'no-relative-import-paths',
    ],

    rules: {
      // Simple Import Sort Plugin pravila
      'simple-import-sort/imports': 'warn',
      'simple-import-sort/exports': 'warn',

      // Unused Imports Plugin pravila
      'no-unused-vars': 'off', // Isključuje<PERSON> osnovni rule
      'unused-imports/no-unused-imports': 'warn',
      'unused-imports/no-unused-vars': [
        'warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],

      'react/no-unescaped-entities': 'off',
      'no-prototype-builtins': 'off',
      '@next/next/no-img-element': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',

      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/consistent-type-imports': [
        'warn',
        {
          prefer: 'type-imports',
          fixStyle: 'inline-type-imports',
        },
      ],

      // Import path rules - prefer alias imports over relative imports
      // BUT exclude shared-fe submodule which should use relative imports only
      'no-relative-import-paths/no-relative-import-paths': [
        'warn',
        {
          allowSameFolder: true,
          rootDir: 'src',
          prefix: '@',
        },
      ],

      'no-empty': 'off',
    },
  }),
  // Override for shared-fe submodule - disable no-relative-import-paths rule
  {
    files: ['src/shared-fe/**/*.ts', 'src/shared-fe/**/*.tsx'],
    rules: {
      'no-relative-import-paths/no-relative-import-paths': 'off',
      // Enforce relative imports in shared-fe
      'no-restricted-imports': [
        'error',
        {
          patterns: [
            {
              group: ['@/*', '~/*', '~/shared-fe/*'],
              message:
                'Use relative imports (./path or ../path) instead of path aliases in shared-fe submodule.',
            },
          ],
        },
      ],
    },
  },
  {
    ignores: [
      'node_modules/**',
      '.next/**',
      'build/**',
      '**/*.d.ts',
      'src/generated/**',
    ],
  },
];

export default eslintConfig;
